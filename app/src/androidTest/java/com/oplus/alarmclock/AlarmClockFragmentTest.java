/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AlarmClockFragmentTest
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/6/17
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/17, ********, create
 ************************************************************/
package com.oplus.alarmclock;

import android.content.Context;
import android.os.Bundle;
import android.os.Looper;
import android.os.SystemClock;
import android.text.format.Time;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiDevice;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.proxy.AlarmListAdapterProxy;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.ClockUiUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;
import com.oplus.alarmclock.utils.UiDeviceUtils;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.ArrayList;

public class AlarmClockFragmentTest {

    private static Context sContext;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
        ScreenUtils.screenOn(sContext);
    }

    @After
    public void tearDown() {
        ClockTestUtils.deleteAllAlarm();
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景化用例
     * 编号:Clock_013_0136 新建闹钟时返回弹框的功能检查
     * 步骤:
     * 1.时钟---闹钟---“+”，点击back键，查看弹框内容
     * 期望结果:
     * 在时钟8.0之前：1.弹框有三个选项“保存闹钟”、“不保存闹钟”和“取消”
     * 在时钟8.0之后：1.添加闹钟的页面消失
     */
    @Ignore
    @Test
    public void should_show_success_when_getSetExitDialog_with_press_back() {
        try {
            AlarmClock alarmClock = mMainRule.launchActivity(null);
            final AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    alarmClockFragment.openModelView(false);
                }
            });
            SystemClock.sleep(TestConstant.TIME_1000);
//            AlarmModelView alarmModelView = (AlarmModelView) ReflectUtils.reflect(AlarmClock.class, "mAlarmModelView", alarmClock);
//            Assert.assertTrue("The add alarm interface is showing.", alarmModelView.isDrawerOpened());

            UiDevice uiDevice = UiDeviceUtils.getUiDevice();
            uiDevice.pressBack();
            SystemClock.sleep(TestConstant.TIME_1000);

            if (!AppUtils.isOverEightVersion(sContext)) { // 8.0之前的版本
                String save = alarmClock.getResources().getString(R.string.save_alarm);
                String unsave = alarmClock.getResources().getString(R.string.unsave);
                String cancel = alarmClock.getResources().getString(R.string.cancel);

                boolean hasSave = uiDevice.hasObject(By.text(save));
                boolean hasUnsave = uiDevice.hasObject(By.text(unsave));
                boolean hasCancel = uiDevice.hasObject(By.text(cancel));
                Assert.assertTrue(hasSave && hasUnsave && hasCancel);
            } else { // 8.0之后的版本
//                Assert.assertFalse("The add alarm interface is dismissed.", alarmModelView.isDrawerOpened());
            }
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景化用例
     * 编号:Clock_013_0137
     * 步骤:
     * 1.时钟---闹钟---“+”，调节时间选择框到1分钟后，点击“保存”，查看提醒情况以及闹钟列表显示情况
     * 期望结果:
     * 1.新建时钟界面上方会显示“距离下一次响铃还有1分钟”；点击“保存”后，
     * 会回到闹钟列表页，该闹钟会显示“约1分钟后响铃”
     */
    @Test
    public void should_show_right_when_openModelView_with_from_AlarmClockFragment_addView() {
        try {
            AlarmClock alarmClock = mMainRule.launchActivity(null);
            final AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            AlarmListAdapterProxy listAdapterProxy = hookAlarmClockFragmentAdapter(alarmClockFragment);

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    alarmClockFragment.openModelView(false);
                }
            });
            SystemClock.sleep(TestConstant.TIME_1000);

//            final AddAlarmFragment addAlarmFragment = ClockUiUtils.getAddAlarmFragment(alarmClock);
//            COUITimeLimitPicker oplusTimePicker = ClockUiUtils.getAddAlarmFragmentTimePicker(addAlarmFragment);
//            Assert.assertNotNull(oplusTimePicker);
//
//            Time time = ClockTestUtils.getTimeOfNextMinute();
//            oplusTimePicker.setCurrentHour(time.hour);
//            oplusTimePicker.setCurrentMinute(time.minute);
//            SystemClock.sleep(TestConstant.TIME_500);
//
//            String[] stringArray = alarmClock.getResources().getStringArray(R.array.alarm_set_next);
//            Field mAlarmLeftTimeF = AddAlarmFragment.class.getDeclaredField("mAlarmLeftTime");
//            mAlarmLeftTimeF.setAccessible(true);
//            TextView mAlarmLeftTime = (TextView) mAlarmLeftTimeF.get(addAlarmFragment);

//            Assert.assertNotNull(mAlarmLeftTime);
//            if (stringArray[0].equals(mAlarmLeftTime.getText().toString())) {
//                time = ClockTestUtils.getTimeOfNextMinute();
//                oplusTimePicker.setCurrentHour(time.hour);
//                oplusTimePicker.setCurrentMinute(time.minute);
//                SystemClock.sleep(TestConstant.TIME_500);
//            }
//            Assert.assertEquals(stringArray[0], mAlarmLeftTime.getText().toString());

//            alarmClock.runOnUiThread(new Runnable() {
//                @Override
//                public void run() {
//                    try {
//                        ClockUiUtils.addAlarmFragmentSaveNewAlarm(addAlarmFragment);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }
//            });
            SystemClock.sleep(TestConstant.TIME_1000);

            View itemView = listAdapterProxy.getItemView(0);
            Assert.assertNotNull("Please make sure you has reflected successfuly", itemView);

            TextView alarmLabel = (TextView) itemView.findViewById(R.id.daysOfWeek_label);
            String label = alarmLabel.getText().toString();
            Assert.assertTrue(label.contains("约 1分钟后响铃"));
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景化用例
     * 编号:Clock_013_0165
     * 删除闹钟（7.2）,已有10个闹钟存在
     * 步骤:
     * 1.时钟---闹钟，长按任一闹钟进入编辑页，点击“删除”，二次确认面板再选择删除，查看删除情况
     * 2.在步骤1的基础上，点击“全选”，点击“删除”，二次确认面板再选择删除，查看删除情况
     * 期望结果:
     * 1.选择的闹钟可以被删除，页面显示不会出现异常
     * 2.闹钟可以被全部删除，页面显示不会出现异常
     */
    @Test
    public void should_delete_success_when_deleteSelectedAlarms_with_one_or_all_alarm() {
        try {
            final AlarmClock alarmClock = mMainRule.launchActivity(null);
            // create 10 alarm clocks
            createAlarmData();

            final AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            COUIRecyclerView listView = (COUIRecyclerView) alarmClockFragment.getBlurView();
            AlarmListAdapter listAdapter = (AlarmListAdapter) listView.getAdapter();
            Assert.assertNotNull(listAdapter);

            AlarmListAdapter.OnItemLongClickListener longClickListener = (AlarmListAdapter.OnItemLongClickListener) ClockUiUtils.reflectField(AlarmListAdapter.class,
                    "mOnItemLongClickListener", listAdapter);
            Assert.assertNotNull(longClickListener);

            doDeleteOne(alarmClock, longClickListener, alarmClockFragment, 0);
            Assert.assertEquals(9, listAdapter.getList().size());
            // delete all alarm
            doDeleteAll(alarmClock, alarmClockFragment);
            Assert.assertEquals(0, listAdapter.getList().size());
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }


    /**
     * 场景化用例
     * 编辑页重打开闹钟的生效情况（7.2）
     * 步骤:
     * 列表页有两个闹钟，一个1分钟后响，一个2分钟后响
     * 1.时钟---闹钟，关闭1分钟后和2分钟后响的闹钟，查看关闭是否成功
     * 2.等待1分钟，查看到点闹钟是否会响起
     * 3.打开2分钟后响的闹钟的开关，等待2分钟后，查看到点闹钟是否会响起
     * 期望结果:
     * 1.可以关闭，1分钟后响和2分钟后响的闹钟开关灰化
     * 2.闹钟不会响起
     * 3.该闹钟开关不灰化，闹钟会响起
     */
    @Test
    public void should_success_when_asyncSetAlarmEnable_with_close_one_min_alarm_and_open_two_min_alarm() {
        if (Looper.myLooper() == null) {
            Looper.prepare();
        }
        Time time = new Time();
        time.setToNow();
        ClockTestUtils.addAlarm(time.hour, time.minute + 1);
        ClockTestUtils.addAlarm(time.hour, time.minute + 2);

        try {
            AlarmClock alarmClock = mMainRule.launchActivity(null);
            AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            COUIRecyclerView listView = ClockUiUtils.getAlarmClockFragmentListView(alarmClockFragment);
            RecyclerView.LayoutManager layoutManager = listView.getLayoutManager();
            final AlarmListAdapter adapter = (AlarmListAdapter) listView.getAdapter();
            Assert.assertNotNull(listView);
            Assert.assertNotNull(layoutManager);
            Assert.assertNotNull(adapter);

            long start = System.currentTimeMillis();
            while (adapter.getItemCount() != 2) {
                if (System.currentTimeMillis() - start > TestConstant.TIME_2000) {
                    break;
                }
            }
            if (AppUtils.isOverEightVersion(sContext)) {
                Assert.assertEquals(2, adapter.getItemCount() - 1); // 8.0上getItemCount方法加了1（列表最后加了一行空数据）
            } else {
                Assert.assertEquals(2, adapter.getItemCount());
            }

            setAlarmEnable(alarmClockFragment, adapter.getList().get(0), false, getCompoundButton(layoutManager, 0), 0);
            setAlarmEnable(alarmClockFragment, adapter.getList().get(1), false, getCompoundButton(layoutManager, 1), 1);
            SystemClock.sleep(TestConstant.TIME_1000);
            Assert.assertFalse(adapter.getList().get(0).isEnabled());
            Assert.assertFalse(adapter.getList().get(1).isEnabled());

            setAlarmEnable(alarmClockFragment, adapter.getList().get(1), true, getCompoundButton(layoutManager, 1), 1);
            SystemClock.sleep(TestConstant.TIME_1000);
            Assert.assertTrue(adapter.getList().get(1).isEnabled());
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }

    private CompoundButton getCompoundButton(RecyclerView.LayoutManager layoutManager, int pos) {
        View itemView = layoutManager.findViewByPosition(pos);
        Assert.assertNotNull(itemView);
        return (CompoundButton) itemView.findViewById(R.id.alarm_switch);
    }

    private void setAlarmEnable(AlarmClockFragment fragment, Alarm alarm, boolean enable,
                                CompoundButton button, int position) {
        try {
            Method asyncSetAlarmEnabled = fragment.getClass().getDeclaredMethod("asyncSetAlarmEnabled",
                    Alarm.class, boolean.class, CompoundButton.class, int.class);
            asyncSetAlarmEnabled.setAccessible(true);
            asyncSetAlarmEnabled.invoke(fragment, alarm, enable, button, position);
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }

    private void doDeleteAll(AlarmClock alarmClock, final AlarmClockFragment alarmClockFragment) {
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                invokeDoQuickSelect(alarmClockFragment);
            }
        });
        SystemClock.sleep(TestConstant.TIME_500);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                invokeDeleteSelectedAlarms(alarmClockFragment);
                alarmClockFragment.clearEdit();
            }
        });
        SystemClock.sleep(TestConstant.TIME_2000);
    }

    private void invokeDoQuickSelect(AlarmClockFragment alarmClockFragment) {
        try {
            Method doQuickSelect = AlarmClockFragment.class.getDeclaredMethod("doQuickSelect");
            doQuickSelect.setAccessible(true);
            doQuickSelect.invoke(alarmClockFragment);
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }

    private void doDeleteOne(AlarmClock alarmClock,
                             final AlarmListAdapter.OnItemLongClickListener longClickListener,
                             final AlarmClockFragment alarmClockFragment, final int index) {
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                longClickListener.onItemLongClick(index);
            }
        });
        SystemClock.sleep(TestConstant.TIME_500);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                invokeDeleteSelectedAlarms(alarmClockFragment);
                alarmClockFragment.clearEdit();
            }
        });
        SystemClock.sleep(TestConstant.TIME_500);
    }

    private void createAlarmData() {
        Time time = new Time();
        time.setToNow();
        ArrayList<Integer> days = new ArrayList<>();
        days.add(1);
        Bundle data = new Bundle();
        data.putInt("android.intent.extra.alarm.HOUR", getTimeHour(time));
        data.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        data.putInt("android.intent.extra.alarm.DELETE_AFTER_USE", 0);
        data.putInt("android.intent.extra.alarm.workday_switch", 0);
        for (int i = 0; i < 10; i++) {
            data.putInt("android.intent.extra.alarm.MINUTES", i);
            data.putString("label", "闹钟" + i);
            ClockTestUtils.addAlarm(data);
        }
    }

    private int getTimeHour(Time time) {
        if (time.hour >= 23) {
            return 0;
        }
        return time.hour + 1;
    }

    private AlarmListAdapterProxy hookAlarmClockFragmentAdapter(AlarmClockFragment alarmClockFragment) throws Exception {
        AlarmListAdapter adapter;
        try {
            adapter = ClockUiUtils.getAlarmClockFragmentAdapter(alarmClockFragment);
        } catch (Exception e) {
            throw new Exception("Hook AlarmListAdapter failed." + e.toString());
        }
        AlarmListAdapterProxy adapterProxy = new AlarmListAdapterProxy(alarmClockFragment.getActivity(), adapter);
        ClockUiUtils.replaceAlarmListAdapter(alarmClockFragment, adapterProxy);
        return adapterProxy;
    }

    private void invokeDeleteSelectedAlarms(AlarmClockFragment alarmClockFragment) {
        try {
            Method deleteSelectedAlarms = AlarmClockFragment.class.getDeclaredMethod("deleteSelectedAlarms");
            deleteSelectedAlarms.setAccessible(true);
            deleteSelectedAlarms.invoke(alarmClockFragment);
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }
}
