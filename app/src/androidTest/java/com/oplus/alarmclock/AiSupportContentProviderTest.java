/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AiSupportContentProviderTest
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/6/16
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/16, ********, create
 ************************************************************/
package com.oplus.alarmclock;

import android.content.Context;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.format.Time;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.ClockUiUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;

import java.util.ArrayList;

public class AiSupportContentProviderTest {
    private static final int DAYS_OF_WEEK = 7;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);
    private static Context sContext;

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
    }

    @After
    public void tearDown() {
        ClockTestUtils.deleteAllAlarm();
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    @Ignore
    @Test
    public void should_alert_success_when_add_alarm_with_screenOff() {
        Time time = ClockTestUtils.getTimeOfNextMinute();
        Bundle bundle = ClockTestUtils.addAlarm(time.hour, time.minute);
        Assert.assertNotNull(bundle);
        Assert.assertEquals(1, bundle.getInt("result", -1));

        ScreenUtils.waitAlarmAlert(sContext, false);

        Assert.assertNotNull(CurrentAlarmScheduleHolder.getAlarmSchedule());
    }

    @Test
    public void should_alert_success_when_add_alarm_with_screenOn() {
        Time time = ClockTestUtils.getTimeOfNextMinute();
        Bundle bundle = ClockTestUtils.addAlarm(time.hour, time.minute);
        Assert.assertNotNull(bundle);
        Assert.assertEquals(1, bundle.getInt("result", -1));
        ScreenUtils.waitAlarmAlert(sContext, true);

        Assert.assertNotNull(CurrentAlarmScheduleHolder.getAlarmSchedule());
    }

    /**
     * 场景化用例
     * 编号:Clock_013_0135 核查重复闹钟自然日响铃
     * 步骤:
     * 1.时钟---闹钟---“+”---“自定义重复日期”，勾选“每周一-每周日”，
     * 设置闹钟时间为14:00，点击“保存”，查看是否保存成功
     * 期望结果:
     * 1.可以保存成功
     */
    @Test
    public void should_save_success_when_add_alarm_with_list_view() {
        int hour = 14;
        int minutes = 0;
        String label = "test";
        ScreenUtils.screenOn(sContext);
        try {
            AlarmClock alarmClock = mMainRule.launchActivity(null);
            SystemClock.sleep(TestConstant.TIME_2000);
            AlarmClockFragment mAlarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            Assert.assertNotNull(mAlarmClockFragment);

            COUIRecyclerView listView = (COUIRecyclerView) mAlarmClockFragment.getBlurView();
            AlarmListAdapter listAdapter = (AlarmListAdapter) listView.getAdapter();
            Assert.assertNotNull(listAdapter);

            int itemCount = listAdapter.getItemCount();
            int alarmsCount = AlarmUtils.getAlarmsCount(alarmClock);
            ArrayList<Integer> days = new ArrayList<>();
            for (int i = 1; i <= DAYS_OF_WEEK; i++) {
                days.add(i);
            }
            Bundle data = new Bundle();
            data.putInt("android.intent.extra.alarm.HOUR", hour);
            data.putInt("android.intent.extra.alarm.MINUTES", minutes);
            data.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
            data.putString("label", label);
            data.putInt("android.intent.extra.alarm.DELETE_AFTER_USE", 0);
            data.putInt("android.intent.extra.alarm.workday_switch", 0);

            Bundle bundle = ClockTestUtils.addAlarm(data);

            Assert.assertEquals(1, bundle.getInt("result", -1));
            Assert.assertEquals("When insert a data successfuly, alarmsCount should add one.",
                    AlarmUtils.getAlarmsCount(alarmClock), alarmsCount + 1);
            if (itemCount == 0) {
                Assert.assertEquals("When insert a data successfuly, listView should add one item.",
                        listAdapter.getItemCount(), itemCount + 2);
            } else {
                Assert.assertEquals("When insert a data successfuly, listView should add one item.",
                        listAdapter.getItemCount(), itemCount + 1);
            }

            if (listAdapter.getList().size() == 1) {
                Alarm alarm = listAdapter.getList().get(0);
                Assert.assertEquals(label, alarm.getLabel());
                Assert.assertEquals(hour, alarm.getHour());
                Assert.assertEquals(minutes, alarm.getMinutes());
            }
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }
}
