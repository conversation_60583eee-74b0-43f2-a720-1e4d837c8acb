/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AlarmAlertTest
 * Version Number : 1.0
 * Description    :
 * Author         : W9002382
 * Date           : 2020/10/16
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/10/16, W9002382, create
 ************************************************************/
package com.oplus.alarmclock;

import android.app.Activity;
import android.content.Context;
import android.database.Cursor;
import android.os.ConditionVariable;
import android.os.SystemClock;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.UiObject2;

import com.coui.appcompat.panel.COUIBottomSheetDialogFragment;
import com.coui.appcompat.searchview.COUISearchViewAnimate;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.AddCityActivity;
import com.oplus.alarmclock.globalclock.AddCityFragment;
import com.oplus.alarmclock.globalclock.AddCityPanelFragment;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.OnItemClickListener;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;
import com.oplus.alarmclock.utils.AppFeatureUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.proxy.ActivityLifecycleCallbacksImpl;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockUiUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;
import com.oplus.alarmclock.utils.UiDeviceUtils;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.lang.ref.WeakReference;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import com.coui.appcompat.toolbar.COUIToolbar;

public class WorldClockTest {

    private static Context sContext;
    private WeakReference<Activity> mActivityWeakReference;
    private ConditionVariable mVariable = new ConditionVariable();

    private ActivityLifecycleCallbacksImpl sCallback = new ActivityLifecycleCallbacksImpl() {
        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            if (activity instanceof AddCityActivity) {
                if (mVariable != null) {
                    mActivityWeakReference = new WeakReference<>(activity);
                    mVariable.open();
                }
            }
        }
    };

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ScreenUtils.screenOn(sContext);
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景自动化
     * 不同方式添加世界时钟（8.0） -- Clock_014_0067
     * 步骤：
     * 1.时钟---时钟---“+”，在输入框中输入“纽约”，点击搜索结果，查看时钟界面是否有新增此世界时钟
     * 期望结果：
     * 1.新增成功，在时钟界面有纽约世界时钟
     */
    @Test
    public void should_success_when_addWorldClock_with_NewYork() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

        try {
            WorldClockViewFragment worldClockViewFragment = ClockUiUtils.gotoWorldClockViewFragment(alarmClock);
            if (worldClockViewFragment.getCurrentCitiesCount() != 0) {
                deleteAllWorldClock(alarmClock, worldClockViewFragment);
            }
            COUIFloatingButton floatingBtn = (COUIFloatingButton) ClockUiUtils.reflectField(AlarmClock.class, "mFloatingBtn", alarmClock);
            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    worldClockViewFragment.onFABClicked();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_5000);
            SystemClock.sleep(TestConstant.TIME_2000); // wait for enter addCityActivity

            AddCityActivity addCityActivity = (AddCityActivity) mActivityWeakReference.get();
            COUIBottomSheetDialogFragment colorBottomSheetDialogFragment =
                    (COUIBottomSheetDialogFragment) ClockUiUtils.reflectField(AddCityActivity.class,
                            "mColorBottomSheetDialogFragment", addCityActivity);
            AddCityPanelFragment addCityPanelFragment = (AddCityPanelFragment) ClockUiUtils.reflectField(COUIBottomSheetDialogFragment.class, "mCurrentPanelFragment", colorBottomSheetDialogFragment);
            AddCityFragment addCityFragment = (AddCityFragment) ClockUiUtils.reflectField(AddCityPanelFragment.class, "mAddCityFragment", addCityPanelFragment);

            COUISearchViewAnimate globalSearchView = (COUISearchViewAnimate) ClockUiUtils.reflectField(AddCityFragment.class, "mGlobalSearchView", addCityFragment);
            View.OnClickListener searchOnClickListener = (View.OnClickListener) ClockUiUtils.reflectField(COUISearchViewAnimate.class, "mOnClickListener", globalSearchView);
            LinearLayout mHintViewLayout = (LinearLayout) ClockUiUtils.reflectField(COUISearchViewAnimate.class, "mHintViewLayout", globalSearchView);
            Method doQueryMethod = AddCityFragment.class.getDeclaredMethod("doQuery", String.class);
            doQueryMethod.setAccessible(true);
            COUIRecyclerView cityList = (COUIRecyclerView) ClockUiUtils.reflectField(AddCityFragment.class, "mCityList", addCityFragment);
            RecyclerView.ViewHolder viewHolder = cityList.findViewHolderForAdapterPosition(0);

            addCityActivity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    searchOnClickListener.onClick(mHintViewLayout); // click search

                    try {
                        doQueryMethod.invoke(addCityFragment, "纽约");
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                        Assert.fail(e.toString());
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                        Assert.fail(e.toString());
                    }
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_5000);
            SystemClock.sleep(TestConstant.TIME_2000); // wait for ui

            skipSouGou(); // 跳过可能弹出的搜狗输入法声明框
            String NewYorkName = "纽约-纽约州 (美国)";
            Assert.assertNotNull("query NewYork success", UiDeviceUtils.findObjectByText(NewYorkName));

            AddGlobalCityListAdapter adapter = (AddGlobalCityListAdapter) cityList.getAdapter();
            OnItemClickListener onItemClickListener = (OnItemClickListener) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mOnItemClickListener", adapter);
            Cursor cursor = (Cursor) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mCursor", adapter);
            addCityActivity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onItemClickListener.onItemClock(0, cursor); // 点击第一个条目
                }
            });

            SystemClock.sleep(TestConstant.TIME_2000); // wait for ui

            CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
            List<City> cities = cityListAdapter.getList();
            Assert.assertNotNull("cities should be not null", cities);
            Assert.assertEquals("the size of cities should be correct", 1, cities.size());
            Assert.assertEquals("WorldClockViewFragment has NewYork city", "纽约，纽约州，美国", cities.get(0).getName()); // 纽约，纽约州，美国
            UiDeviceUtils.pressBack();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景自动化
     * 不同方式添加世界时钟（8.0） -- Clock_014_0067
     * 步骤：
     * 1.时钟---时钟---“+”，点击右边的索引“T”，点击“台北-台湾 (中国)”(外销显示“台北”)，查看时钟界面是否有新增此世界时钟
     * 期望结果：
     * 1.新增成功，在时钟界面有台北的世界时钟
     */
    @Test
    public void should_success_when_addWorldClock_with_TaiBei() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

        try {
            WorldClockViewFragment worldClockViewFragment = ClockUiUtils.gotoWorldClockViewFragment(alarmClock);
            if (worldClockViewFragment.getCurrentCitiesCount() != 0) {
                deleteAllWorldClock(alarmClock, worldClockViewFragment);
            }
            COUIFloatingButton floatingBtn = (COUIFloatingButton) ClockUiUtils.reflectField(AlarmClock.class, "mFloatingBtn", alarmClock);
            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    worldClockViewFragment.onFABClicked();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_5000);
            SystemClock.sleep(TestConstant.TIME_2000); // wait for enter addCityActivity

            AddCityActivity addCityActivity = (AddCityActivity) mActivityWeakReference.get();
            COUIBottomSheetDialogFragment colorBottomSheetDialogFragment =
                    (COUIBottomSheetDialogFragment) ClockUiUtils.reflectField(AddCityActivity.class,
                            "mColorBottomSheetDialogFragment", addCityActivity);
            AddCityPanelFragment addCityPanelFragment = (AddCityPanelFragment) ClockUiUtils.reflectField(COUIBottomSheetDialogFragment.class, "mCurrentPanelFragment", colorBottomSheetDialogFragment);
            AddCityFragment addCityFragment = (AddCityFragment) ClockUiUtils.reflectField(AddCityPanelFragment.class, "mAddCityFragment", addCityPanelFragment);

            COUISearchViewAnimate globalSearchView = (COUISearchViewAnimate) ClockUiUtils.reflectField(AddCityFragment.class, "mGlobalSearchView", addCityFragment);
            View.OnClickListener searchOnClickListener = (View.OnClickListener) ClockUiUtils.reflectField(COUISearchViewAnimate.class, "mOnClickListener", globalSearchView);
            LinearLayout mHintViewLayout = (LinearLayout) ClockUiUtils.reflectField(COUISearchViewAnimate.class, "mHintViewLayout", globalSearchView);
            Method doQueryMethod = AddCityFragment.class.getDeclaredMethod("doQuery", String.class);
            doQueryMethod.setAccessible(true);
            COUIRecyclerView cityList = (COUIRecyclerView) ClockUiUtils.reflectField(AddCityFragment.class, "mCityList", addCityFragment);
            RecyclerView.ViewHolder viewHolder = cityList.findViewHolderForAdapterPosition(0);

            addCityActivity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    searchOnClickListener.onClick(mHintViewLayout); // click search
                    try {
                        doQueryMethod.invoke(addCityFragment, "T");
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                        Assert.fail(e.toString());
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                        Assert.fail(e.toString());
                    }
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_5000);
            SystemClock.sleep(TestConstant.TIME_2000); // wait for ui
            skipSouGou(); // 跳过可能弹出的搜狗输入法声明框

            String taiBeiName = "台北-台湾 (中国)";
            if (AppFeatureUtils.isExpVersion(sContext)) {
                taiBeiName = "台北";
            }
            Assert.assertNotNull("query TaiBei success", UiDeviceUtils.findObjectByText(taiBeiName));

            AddGlobalCityListAdapter adapter = (AddGlobalCityListAdapter) cityList.getAdapter();
            OnItemClickListener onItemClickListener = (OnItemClickListener) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mOnItemClickListener", adapter);
            Cursor cursor = (Cursor) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mCursor", adapter);
            addCityActivity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onItemClickListener.onItemClock(0, cursor); // 点击第一个条目
                }
            });

            SystemClock.sleep(TestConstant.TIME_2000); // wait for ui

            CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
            List<City> cities = cityListAdapter.getList();
            Assert.assertNotNull("cities should be not null", cities);
            Assert.assertEquals("the size of cities should be correct", 1, cities.size());
            String taiBeiCityName = "台北，台湾，中国";
            if (AppFeatureUtils.isExpVersion(sContext)) {
                taiBeiCityName = "台北";
            }
            Assert.assertEquals("WorldClockViewFragment has NewYork city", taiBeiCityName, cities.get(0).getName()); // 台北，台湾，中国
            UiDeviceUtils.pressBack();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景自动化
     * 删除世界时钟（8.0） -- Clock_014_0068
     * 步骤：
     * 1.时钟---时钟，添加4个世界时钟，长按任意世界时钟进入编辑页，点击“删除”，查看删除情况
     * 期望结果：
     * 1.选中的世界时钟被删除
     */
    @Test
    public void should_success_when_deleteWorldClock_with_deleteOne() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

        try {
            WorldClockViewFragment worldClockViewFragment = ClockUiUtils.gotoWorldClockViewFragment(alarmClock);
            if (worldClockViewFragment.getCurrentCitiesCount() != 0) {
                deleteAllWorldClock(alarmClock, worldClockViewFragment);
                SystemClock.sleep(TestConstant.TIME_1000);
            }
            for (int i = 0; i < 4; i++) { // 添加四个世界时钟
                addOneClock(alarmClock, worldClockViewFragment, i);
                SystemClock.sleep(TestConstant.TIME_1000);
            }
            CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
            List<City> cities = cityListAdapter.getList();
            Assert.assertNotNull("cities should be not null", cities);
            Assert.assertEquals("the size of cities should be correct", 4, cities.size());

            deleteOneWorldClock(alarmClock, worldClockViewFragment);
            SystemClock.sleep(TestConstant.TIME_2000); //wait for ui

            Assert.assertEquals("the size of cities should be correct", 3, cities.size());
            UiDeviceUtils.pressBack();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景自动化
     * 删除世界时钟（8.0） -- Clock_014_0068
     * 步骤：
     * 1.时钟---时钟，添加4个世界时钟，长按任意世界时钟进入编辑页，点击“全选”，点击“删除”，查看删除情况
     * 期望结果：
     * 1.所有的世界时钟均被删除
     */
    @Test
    public void should_success_when_deleteWorldClock_with_deleteAll() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

        try {
            WorldClockViewFragment worldClockViewFragment = ClockUiUtils.gotoWorldClockViewFragment(alarmClock);
            if (worldClockViewFragment.getCurrentCitiesCount() != 0) {
                deleteAllWorldClock(alarmClock, worldClockViewFragment);
                SystemClock.sleep(TestConstant.TIME_1000);
            }
            for (int i = 0; i < 4; i++) { // 添加四个世界时钟
                addOneClock(alarmClock, worldClockViewFragment, i);
                SystemClock.sleep(TestConstant.TIME_1000);
            }
            CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
            List<City> cities = cityListAdapter.getList();
            Assert.assertNotNull("cities should be not null", cities);
            Assert.assertEquals("the size of cities should be correct", 4, cities.size());

            deleteAllWorldClock(alarmClock, worldClockViewFragment);
            SystemClock.sleep(TestConstant.TIME_2000); //wait for ui

            Assert.assertEquals("the size of cities should be correct", 0, cities.size());
            UiDeviceUtils.pressBack();
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }


    private void skipSouGou() { // 跳过可能弹出的搜狗输入法声明
        UiObject2 agreeButton = UiDeviceUtils.findObjectByText("同意并使用");
        if (agreeButton != null) {
            agreeButton.click();
        }
    }

    private void addOneClock(AlarmClock alarmClock, WorldClockViewFragment worldClockViewFragment, int position) throws Exception {
        COUIFloatingButton floatingBtn = (COUIFloatingButton) ClockUiUtils.reflectField(AlarmClock.class, "mFloatingBtn", alarmClock);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                worldClockViewFragment.onFABClicked();
            }
        });
        mVariable.close();
        mVariable.block(TestConstant.TIME_5000);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for enter addCityActivity

        AddCityActivity addCityActivity = (AddCityActivity) mActivityWeakReference.get();
        COUIBottomSheetDialogFragment colorBottomSheetDialogFragment =
                (COUIBottomSheetDialogFragment) ClockUiUtils.reflectField(AddCityActivity.class,
                        "mColorBottomSheetDialogFragment", addCityActivity);
        AddCityPanelFragment addCityPanelFragment = (AddCityPanelFragment) ClockUiUtils.reflectField(COUIBottomSheetDialogFragment.class, "mCurrentPanelFragment", colorBottomSheetDialogFragment);
        AddCityFragment addCityFragment = (AddCityFragment) ClockUiUtils.reflectField(AddCityPanelFragment.class, "mAddCityFragment", addCityPanelFragment);
        COUIRecyclerView cityList = (COUIRecyclerView) ClockUiUtils.reflectField(AddCityFragment.class, "mCityList", addCityFragment);

        AddGlobalCityListAdapter adapter = (AddGlobalCityListAdapter) cityList.getAdapter();
        OnItemClickListener onItemClickListener = (OnItemClickListener) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mOnItemClickListener", adapter);
        Cursor cursor = (Cursor) ClockUiUtils.reflectField(AddGlobalCityListAdapter.class, "mCursor", adapter);
        addCityActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                onItemClickListener.onItemClock(position, cursor);
            }
        });
    }

    private void deleteAllWorldClock(AlarmClock alarmClock, WorldClockViewFragment worldClockViewFragment) throws Exception {
        CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
        View.OnLongClickListener longClickListener = (View.OnLongClickListener) ClockUiUtils.reflectField(CityListAdapter.class, "mItemLongClickListener", cityListAdapter);
        COUIRecyclerView cityListRecyclerView = (COUIRecyclerView) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mCityList", worldClockViewFragment);
        RecyclerView.ViewHolder viewHolder = cityListRecyclerView.findViewHolderForAdapterPosition(0);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                longClickListener.onLongClick(viewHolder.itemView);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (worldClockViewFragment.getCurrentCitiesCount() > 1) { // 全选
                    worldClockViewFragment.onEditSelectedAll();
                }
                worldClockViewFragment.delete();
                //Exit edit
                worldClockViewFragment.clearEdit();
            }
        });
    }

    private void deleteOneWorldClock(AlarmClock alarmClock, WorldClockViewFragment worldClockViewFragment) throws Exception {
        CityListAdapter cityListAdapter = (CityListAdapter) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mListAdapter", worldClockViewFragment);
        View.OnLongClickListener longClickListener = (View.OnLongClickListener) ClockUiUtils.reflectField(CityListAdapter.class, "mItemLongClickListener", cityListAdapter);
        COUIRecyclerView cityListRecyclerView = (COUIRecyclerView) ClockUiUtils.reflectField(WorldClockViewFragment.class, "mCityList", worldClockViewFragment);
        RecyclerView.ViewHolder viewHolder = cityListRecyclerView.findViewHolderForAdapterPosition(0);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                longClickListener.onLongClick(viewHolder.itemView);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);
        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                worldClockViewFragment.delete();
                //Exit edit
                worldClockViewFragment.clearEdit();
            }
        });
    }
}