/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AlarmAlertTest
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/7/31
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/7/31, ********, create
 ************************************************************/
package com.oplus.alarmclock;

import static org.mockito.Mockito.spy;

import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.coui.appcompat.preference.COUIJumpPreference;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.oplus.alarmclock.alarmclock.AlarmSettingFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.AsyncRingtonePlayer;
import com.oplus.alarmclock.alert.AlarmAlertFullScreen;
import com.oplus.alarmclock.timer.TimerAlert;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.List;

public class AlarmAlertTest {

    private static Context sContext;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
        ScreenUtils.screenOn(sContext);
    }

    @After
    public void tearDown() {
        ClockTestUtils.deleteAllAlarm();
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景自动化
     * 锁屏上划停止闹钟响铃（7.2）
     * 步骤：
     * 1.设置一个1分钟后响铃的闹钟（“稍后提醒”开关打开），锁屏，等待到点闹钟响起，在锁屏界面上划，查看闹钟是否被停止
     * 2.时钟---闹钟，查看该闹钟是否被关闭
     * 3.设置一个1分钟后响铃的闹钟（“稍后提醒”开关关闭），锁屏，等待到点闹钟响起，在锁屏界面上划，查看闹钟是否被停止
     * 4.时钟---闹钟，查看该闹钟是否被关闭
     * 期望结果：
     * 1.到点闹钟响起，锁屏界面出现闹钟响铃动画，显示闹钟的标签、“点击稍后提醒”和“上滑停止闹钟”的提示，闹钟停止响铃
     * 2.该闹钟开关显示关闭
     * 3.到点闹钟响起，锁屏界面出现闹钟响铃动画，显示闹钟的标签、以及“上滑停止闹钟”的提示，闹钟停止响铃
     * 4.该闹钟开关显示关闭
     * 实验室机器锁屏后会自动亮屏，导致用例失败。先ignore
     */
    @Ignore
    @Test
    public void should_success_when_onSlideEnd_with_swipe_up_the_lock_screen() {
        for (int i = 0; i < 2; i++) {
            boolean isSnooze = i == 0;
            ClockTestUtils.createAndWaitFullScreenAlert("测试" + i, isSnooze, true);
        }

        List<Alarm> alarms = ClockTestUtils.getAlarmClockListData(mMainRule);
        Assert.assertNotNull(alarms);
        Assert.assertEquals(2, alarms.size());
        for (Alarm alarm : alarms) {
            Assert.assertFalse(alarm.isEnabled());
        }
    }

    /**
     * 场景自动化
     * 锁屏下点击“稍后提醒”延迟闹钟响铃（7.2）
     * 步骤：
     * 1.设置一个1分钟后响铃的闹钟（“稍后提醒”开关打开），锁屏，等待到点闹钟响起，点击“点击稍后提醒”，查看是否有闹钟通知弹出
     * 2.等待5分钟后，闹钟是否响起
     * 期望结果：
     * 1.锁屏界面会有闹钟通知，显示5分钟后再提醒
     * 2.到点闹钟会再次响起
     * 实验室机器锁屏后会自动亮屏，导致用例失败。先ignore
     */
    @Ignore
    @Test
    public void should_success_when_snooze_with_click_snooze_view() {
        String title = "测试" + System.currentTimeMillis();
        AlarmAlertFullScreen alarmAlertFullScreen =
                ClockTestUtils.createAndWaitFullScreenAlert(title, true, false);

        clickSnoozeView(alarmAlertFullScreen);

        long powerTime = System.currentTimeMillis();
        long afterAlertTime = powerTime + TestConstant.MINUTE_5;

        SystemClock.sleep(TestConstant.TIME_1000);

        Notification notification = ClockTestUtils.getNotificationByTitle(title);
        Assert.assertNotNull(notification);
        String extraText = notification.extras.getString(Notification.EXTRA_TEXT);

        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(afterAlertTime);
        String contentText = sContext.getString(
                R.string.alarm_notify_snooze_text, Formatter.formatTime(sContext, c));

        Assert.assertEquals(contentText, extraText);

        List<Alarm> alarms = ClockTestUtils.getAlarmClockListData(mMainRule);
        Assert.assertNotNull(alarms);

        Alarm alarm = ClockTestUtils.getAlarmByTitle(alarms, title);
        Assert.assertNotNull(alarm);
        Assert.assertTrue(alarm.isEnabled());
        long nextTime = AlarmUtils.getAlarmNextTime(alarm, null);
        Assert.assertTrue("NextAlertTime:" + nextTime + ", afterAlertTime:" + afterAlertTime,
                Math.abs(nextTime - afterAlertTime) <= TestConstant.SECOND_10);
    }

    private void clickSnoozeView(AlarmAlertFullScreen activity) {
        try {
            Method snooze = activity.getClass().getSuperclass().getDeclaredMethod("snooze");
            snooze.setAccessible(true);
            snooze.invoke(activity);
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }


    /**
     * 场景自动化
     * 锁屏下点击“稍后提醒”延迟闹钟响铃
     * 步骤：
     * 1.设置一个1分钟后响铃的闹钟（“稍后提醒”开关打开），锁屏，等待到点闹钟响起，点击页面缩小音量大小(可多次点击) 之后 点击“点击稍后提醒”，查看是否有闹钟通知弹出
     * 2.等待5分钟后，闹钟是否响起
     * 期望结果：
     * 1.锁屏界面会有闹钟通知，显示5分钟后再提醒
     * 2.到点闹钟会再次响起，闹钟音量恢复原来大小
     * 实验室机器锁屏后会自动亮屏，导致用例失败。先ignore
     */
    @Ignore
    @Test
    public void should_success_when_snooze_after_click_fullscreen() {
        String title = "测试" + System.currentTimeMillis();
        AlarmAlertFullScreen alarmAlertFullScreen =
                ClockTestUtils.createAndWaitFullScreenAlert(title, true, false);
        for (int i = 0; i < 2; i++) {
            clickFullScreen(alarmAlertFullScreen);
        }

        clickSnoozeView(alarmAlertFullScreen);

        long powerTime = System.currentTimeMillis();
        long afterAlertTime = powerTime + TestConstant.MINUTE_5;

        SystemClock.sleep(TestConstant.TIME_1000);

        Notification notification = ClockTestUtils.getNotificationByTitle(title);
        Assert.assertNotNull(notification);
        String extraText = notification.extras.getString(Notification.EXTRA_TEXT);

        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(afterAlertTime);
        String contentText = sContext.getString(
                R.string.alarm_notify_snooze_text, Formatter.formatTime(sContext, c));

        Assert.assertEquals(contentText, extraText);

        List<Alarm> alarms = ClockTestUtils.getAlarmClockListData(mMainRule);
        Assert.assertNotNull(alarms);

        Alarm alarm = ClockTestUtils.getAlarmByTitle(alarms, title);
        Assert.assertNotNull(alarm);
        Assert.assertTrue(alarm.isEnabled());
        long nextTime = AlarmUtils.getAlarmNextTime(alarm, null);
        Assert.assertTrue("NextAlertTime:" + nextTime + ", afterAlertTime:" + afterAlertTime,
                Math.abs(nextTime - afterAlertTime) <= TestConstant.SECOND_10);
    }

    /**
     * 设置闹钟音量
     * @param activity
     */
    private void clickFullScreen(AlarmAlertFullScreen activity) {
        try {
            final Context spyContext = spy(activity);
            final AsyncRingtonePlayer player = new AsyncRingtonePlayer(spyContext);
            player.setVolumeReduceByTime(activity);
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
    }
}
