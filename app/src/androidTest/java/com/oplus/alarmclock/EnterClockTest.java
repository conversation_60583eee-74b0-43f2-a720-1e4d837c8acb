/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AlarmAlertTest
 * Version Number : 1.0
 * Description    :
 * Author         : W9002382
 * Date           : 2020/10/12
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/10/12, W9002382, create
 ************************************************************/
package com.oplus.alarmclock;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.ConditionVariable;
import android.os.SystemClock;

import androidx.annotation.NonNull;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.UiObject2;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.alarmclock.AlarmSettingActivity;
import com.oplus.alarmclock.proxy.ActivityLifecycleCallbacksImpl;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;
import com.oplus.alarmclock.utils.UiDeviceUtils;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.lang.ref.WeakReference;
import java.util.List;

public class EnterClockTest {

    private static Context sContext;
    private WeakReference<Activity> mActivityWeakReference;
    private ConditionVariable mVariable = new ConditionVariable();

    private ActivityLifecycleCallbacksImpl sCallback = new ActivityLifecycleCallbacksImpl() {
        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            if (activity instanceof AlarmSettingActivity) {
                if (mVariable != null) {
                    mActivityWeakReference = new WeakReference<>(activity);
                    mVariable.open();
                }
            }
        }
    };

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
        ScreenUtils.screenOn(sContext);
    }

    @After
    public void tearDown() {
        ClockTestUtils.deleteAllAlarm();
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景自动化
     * 桌面图标进入时钟（8.0） -- Clock_014_0061
     * 步骤：
     * 1.点击桌面时钟图标进入时钟，查看是否能正常进入
     * 期望结果：
     * 1.能够正常进入时钟
     */
    @Test
    public void should_success_when_enterClock() {
        UiDeviceUtils.pressHome();
        UiObject2 clockUiObject = null;
        for (int i = 0; i < 10; i++) {
            clockUiObject = UiDeviceUtils.findObjectByText("时钟");
            if (clockUiObject == null) {
                clockUiObject = UiDeviceUtils.findObjectByText("Clock");
            }
            if (clockUiObject != null) {
                break;
            }
            UiDeviceUtils.swipeLeft(); // 持续在桌面找时钟应用
        }
        Assert.assertNotNull("Clock should be installed", clockUiObject);
        clockUiObject.click();
        SystemClock.sleep(TestConstant.TIME_1000);
        ActivityManager am = (ActivityManager) sContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> tasks = am.getRunningTasks(1);
        String topActivityName = tasks.get(0).topActivity.getClassName();
        Assert.assertEquals("Clock should be open success.", "com.oplus.alarmclock.AlarmClock", topActivityName);
    }
}
