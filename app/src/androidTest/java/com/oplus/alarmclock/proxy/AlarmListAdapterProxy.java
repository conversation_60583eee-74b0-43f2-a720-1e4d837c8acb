/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.proxy.AlarmListAdapterProxy
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/6/18
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/18, W9002519, create
 ************************************************************/
package com.oplus.alarmclock.proxy;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.utils.FoldScreenUtils;

import java.util.HashMap;
import java.util.Map;

public class AlarmListAdapterProxy extends AlarmListAdapter {
    private AlarmListAdapter mAdapter;
    private Map<Integer, RecyclerView.ViewHolder> mMap;

    public AlarmListAdapterProxy(Context context, AlarmListAdapter adapter) {
        super(context, FoldScreenUtils.UiMode.NORMAL);
        mAdapter = adapter;
        mMap = new HashMap<>();
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        super.onBindViewHolder(viewHolder, position);
        mMap.put(position, viewHolder);
    }

    public View getItemView(int position) {
        RecyclerView.ViewHolder viewHolder = mMap.get(position);
        return viewHolder != null ? viewHolder.itemView : null;
    }
}
