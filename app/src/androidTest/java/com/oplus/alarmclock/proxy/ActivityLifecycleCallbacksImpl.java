/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.proxy.ActivityLifecycleCallbacksImpl
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/7/30
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/7/30, ********, create
 ************************************************************/
package com.oplus.alarmclock.proxy;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public abstract class ActivityLifecycleCallbacksImpl implements Application.ActivityLifecycleCallbacks {
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {

    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }
}
