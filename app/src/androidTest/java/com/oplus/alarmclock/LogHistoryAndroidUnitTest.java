/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-10, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock;

import android.os.Parcel;

import androidx.test.filters.SmallTest;
import androidx.test.internal.runner.junit4.AndroidJUnit4ClassRunner;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static com.google.common.truth.Truth.assertThat;

// @RunWith is required only if you use a mix of JUnit3 and JUnit4.
//@RunWith(AndroidJUnit4.class)
@RunWith(AndroidJUnit4ClassRunner.class)
//@RunWith(RobolectricTestRunner.class)
@SmallTest
public class LogHistoryAndroidUnitTest {

    public static final String TEST_STRING = "hello android test";
    public static final int TEST_INT = 1234;
    private LogHistory mLogHistory;

    @Before
    public void createLogHistory() {
        mLogHistory = new LogHistory(TEST_INT, TEST_STRING);
    }

    @Test
    public void logHistory_ParcelableWriteRead() {
        // Write the data.
        Parcel parcel = Parcel.obtain();
        mLogHistory.writeToParcel(parcel, mLogHistory.describeContents());

        // After you're done with writing, you need to reset the parcel for reading.
        parcel.setDataPosition(0);

        // Read the data.
        LogHistory createdFromParcel = LogHistory.CREATOR.createFromParcel(parcel);

        // Verify that the received data is correct.
        assertThat(createdFromParcel.value1).isEqualTo(TEST_INT);
        assertThat(createdFromParcel.value2).isEqualTo(TEST_STRING);
    }
}