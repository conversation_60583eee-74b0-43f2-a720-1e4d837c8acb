/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.AlarmAlertTest
 * Version Number : 1.0
 * Description    :
 * Author         : W9002382
 * Date           : 2020/10/23
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/10/23, W9002382, create
 ************************************************************/
package com.oplus.alarmclock;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.ConditionVariable;
import android.os.SystemClock;

import androidx.annotation.NonNull;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.coui.appcompat.button.COUIButton;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.stopwatch.OplusStopWatch;
import com.oplus.alarmclock.stopwatch.StopWatchFragment;
import com.oplus.alarmclock.stopwatch.StopWatchRecordAdapter;
import com.oplus.alarmclock.view.LocalColorRecyclerView;
import com.oplus.alarmclock.proxy.ActivityLifecycleCallbacksImpl;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockUiUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;
import com.oplus.alarmclock.utils.UiDeviceUtils;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.lang.ref.WeakReference;

import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

public class StopWatchTest {

    private static final int STATUS_0 = 0;
    private static final int STATUS_1 = 1;
    private static final int STATUS_2 = 2;
    private static Context sContext;
    private WeakReference<Activity> mActivityWeakReference;
    private ConditionVariable mVariable = new ConditionVariable();

    private ActivityLifecycleCallbacksImpl sCallback = new ActivityLifecycleCallbacksImpl() {
        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            if (activity instanceof AlarmClock) {
                if (mVariable != null) {
                    mActivityWeakReference = new WeakReference<>(activity);
                    mVariable.open();
                }
            }
        }
    };

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ScreenUtils.screenOn(sContext);
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景自动化
     * 秒表基本功能检查（8.0） -- Clock_014_0074
     * 步骤：
     * 1.时钟---秒表，点击“启动”，查看是否可以正常启动计时
     * 2.点击“计次”，查看是否会生成计次记录
     * 3.点击“暂停”，查看秒表是否会暂停
     * 4.点击“启动”，查看秒表是否按照上次进度继续计时
     * 5.点击“暂停”后再点击“复位”，查看秒表会不会归零
     * 期望结果：
     * 1.可以正常计时
     * 2.会生成计次记录
     * 3.秒表会暂停计时
     * 4.秒表会继续从步骤3停止的时间点开始继续计时
     * 5.秒表归零并且计次记录清空
     */
    @Test
    public void should_success_when_startStopWatch() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);

        try {
            StopWatchFragment stopWatchFragment = ClockUiUtils.gotoStopWatchFragment(alarmClock);
            OplusStopWatch myWatch = (OplusStopWatch) ClockUiUtils.reflectField(StopWatchFragment.class, "mMyWatch", stopWatchFragment);
            COUIButton buttonStart = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonStart", stopWatchFragment);
            COUIButton buttonCancel = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCancel", stopWatchFragment);
            COUIButton buttonCount = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCount", stopWatchFragment);
            LocalColorRecyclerView listView = (LocalColorRecyclerView) ClockUiUtils.reflectField(StopWatchFragment.class, "mListView", stopWatchFragment);
            StopWatchRecordAdapter adapter = (StopWatchRecordAdapter) listView.getAdapter();

            int status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            if (status != STATUS_0) {
                int finalStatus = status;
                alarmClock.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (finalStatus == STATUS_1) {
                            stopWatchFragment.onClick(buttonCancel);
                            SystemClock.sleep(TestConstant.TIME_500);
                        }
                        stopWatchFragment.onClick(buttonCount); // 复位
                    }
                });
            }
            SystemClock.sleep(TestConstant.TIME_1000);

            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_0, status);
            Assert.assertEquals("The text on the button should displayed correctly", "开始", buttonStart.getText());
            Assert.assertEquals("Start time is 0", 0L, myWatch.getElapseTime());
            Assert.assertEquals("The number of counts is 0", 0, adapter.getItemCount()); // 计次个数

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonStart); // 开始
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_2000);
            long oldElapseTime = myWatch.getElapseTime();
            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_1, status);
            Assert.assertTrue("The timing should correct", myWatch.getElapseTime() >= 1500L);
            Assert.assertEquals("The text on the button should displayed correctly", "暂停", buttonCancel.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "计次", buttonCount.getText());
            SystemClock.sleep(TestConstant.TIME_200);
            Assert.assertNotEquals("Stopwatch is running", oldElapseTime, myWatch.getElapseTime());
            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonCount); // 计次
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);
            oldElapseTime = myWatch.getElapseTime();
            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_1, status);
            Assert.assertTrue("The timing should correct", myWatch.getElapseTime() > 2000L);
            Assert.assertEquals("The text on the button should displayed correctly", "暂停", buttonCancel.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "计次", buttonCount.getText());
            Assert.assertEquals("The number of counts is 0", 1, adapter.getItemCount()); // 计次个数
            SystemClock.sleep(TestConstant.TIME_200);
            Assert.assertNotEquals("Stopwatch is running", oldElapseTime, myWatch.getElapseTime());

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonCancel); // 暂停
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);
            oldElapseTime = myWatch.getElapseTime();
            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_2, status);
            Assert.assertTrue("The timing should correct", myWatch.getElapseTime() > 2000L);
            Assert.assertEquals("The text on the button should displayed correctly", "继续", buttonCancel.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "复位", buttonCount.getText());
            Assert.assertEquals("The number of counts is 0", 1, adapter.getItemCount()); // 计次个数
            Assert.assertEquals("Stopwatch was paused", oldElapseTime, myWatch.getElapseTime());

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonCancel); // 继续
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);
            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_1, status);
            Assert.assertTrue("The timing should correct", myWatch.getElapseTime() > oldElapseTime);
            Assert.assertEquals("The text on the button should displayed correctly", "暂停", buttonCancel.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "计次", buttonCount.getText());

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonCancel); // 暂停
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonCount); // 复位
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);
            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_0, status);
            Assert.assertEquals("The text on the button should displayed correctly", "开始", buttonStart.getText());
            Assert.assertEquals("Start time is 0", 0L, myWatch.getElapseTime());
            Assert.assertEquals("The number of counts is 0", 0, adapter.getItemCount()); // 计次个数

            SystemClock.sleep(TestConstant.TIME_2000);

        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }

    /**
     * 场景自动化
     * 秒表后台运行（8.0） -- Clock_014_0075
     * 步骤：
     * 1.时钟---秒表，点击“启动”，back键退到桌面，等待1分钟，再进入时钟---秒表，查看秒表是否有在后台进行
     * 2.home键退到桌面，等待1分钟，再进入时钟---秒表，查看秒表是否有在后台进行
     * 期望结果：
     * 1.秒表有在后台进行
     * 2.秒表有在后台进行
     * （比如在back/home键退出时，秒表已经到达2:11，在桌面等待1分钟再进入，则秒表应该是3:11）
     */
    @Test
    public void should_success_when_enterBackground() {
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        AlarmClock alarmClock = mMainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

        try {
            StopWatchFragment stopWatchFragment = ClockUiUtils.gotoStopWatchFragment(alarmClock);
            OplusStopWatch myWatch = (OplusStopWatch) ClockUiUtils.reflectField(StopWatchFragment.class, "mMyWatch", stopWatchFragment);
            COUIButton buttonStart = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonStart", stopWatchFragment);
            COUIButton buttonCancel = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCancel", stopWatchFragment);
            COUIButton buttonCount = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCount", stopWatchFragment);
            LocalColorRecyclerView listView = (LocalColorRecyclerView) ClockUiUtils.reflectField(StopWatchFragment.class, "mListView", stopWatchFragment);
            StopWatchRecordAdapter adapter = (StopWatchRecordAdapter) listView.getAdapter();

            int status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            if (status != STATUS_0) {
                int finalStatus = status;
                alarmClock.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (finalStatus == STATUS_1) {
                            stopWatchFragment.onClick(buttonCancel);
                            SystemClock.sleep(TestConstant.TIME_500);
                        }
                        stopWatchFragment.onClick(buttonCount); // 复位
                    }
                });
            }
            SystemClock.sleep(TestConstant.TIME_1000);

            status = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragment);
            Assert.assertEquals("Status should be correct", STATUS_0, status);

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragment.onClick(buttonStart); // 开始
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
            SystemClock.sleep(TestConstant.TIME_1000);
            UiDeviceUtils.pressBack(); // 按返回键退出时钟
            SystemClock.sleep(TestConstant.WAIT_MINUTE);

            alarmClock = mMainRule.launchActivity(null);
            SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity

            StopWatchFragment stopWatchFragmentTwo = ClockUiUtils.gotoStopWatchFragment(alarmClock);
            OplusStopWatch myWatchTwo = (OplusStopWatch) ClockUiUtils.reflectField(StopWatchFragment.class, "mMyWatch", stopWatchFragmentTwo);
            COUIButton buttonStartTwo = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonStart", stopWatchFragmentTwo);
            COUIButton buttonCancelTwo = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCancel", stopWatchFragmentTwo);
            COUIButton buttonCountTwo = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCount", stopWatchFragmentTwo);

            int statusTwo = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragmentTwo);
            long oldElapseTimeTwo = myWatchTwo.getElapseTime();

            Assert.assertEquals("Status should be correct", STATUS_1, statusTwo);
            Assert.assertTrue("The timing should correct", myWatchTwo.getElapseTime() >= TestConstant.WAIT_MINUTE);
            Assert.assertEquals("The text on the button should displayed correctly", "暂停", buttonCancelTwo.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "计次", buttonCountTwo.getText());
            SystemClock.sleep(TestConstant.TIME_200);
            Assert.assertNotEquals("Stopwatch is running", oldElapseTimeTwo, myWatchTwo.getElapseTime());

            UiDeviceUtils.pressHome(); // 按home键回到桌面
            SystemClock.sleep(TestConstant.WAIT_MINUTE);


            Intent intent = new Intent(sContext, AlarmClock.class);
            intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
            sContext.startActivity(intent);

            SystemClock.sleep(TestConstant.TIME_2000); // wait for launch activity
            alarmClock = (AlarmClock) mActivityWeakReference.get();

            StopWatchFragment stopWatchFragmentThree = ClockUiUtils.gotoStopWatchFragment(alarmClock);
            OplusStopWatch myWatchThree = (OplusStopWatch) ClockUiUtils.reflectField(StopWatchFragment.class, "mMyWatch", stopWatchFragmentThree);
            COUIButton buttonStartThree = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonStart", stopWatchFragmentThree);
            COUIButton buttonCancelThree = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCancel", stopWatchFragmentThree);
            COUIButton buttonCountThree = (COUIButton) ClockUiUtils.reflectField(StopWatchFragment.class, "mButtonCount", stopWatchFragmentThree);

            int statusThree = (int) ClockUiUtils.reflectField(StopWatchFragment.class, "mStatus", stopWatchFragmentThree);
            long oldElapseTimeThree = myWatchThree.getElapseTime();

            Assert.assertEquals("Status should be correct", STATUS_1, statusThree);
            Assert.assertTrue("The timing should correct", myWatchThree.getElapseTime() >= TestConstant.WAIT_MINUTE * 2);
            Assert.assertEquals("The text on the button should displayed correctly", "暂停", buttonCancelThree.getText());
            Assert.assertEquals("The text on the button should displayed correctly", "计次", buttonCountThree.getText());
            SystemClock.sleep(TestConstant.TIME_200);
            Assert.assertNotEquals("Stopwatch is running", oldElapseTimeThree, myWatchThree.getElapseTime());

            alarmClock.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    stopWatchFragmentThree.onClick(buttonCancelThree);
                    SystemClock.sleep(TestConstant.TIME_500);
                    stopWatchFragmentThree.onClick(buttonCountThree); // 复位
                    mVariable.open();
                }
            });
            mVariable.close();
            mVariable.block(TestConstant.TIME_2000);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        }
    }
}