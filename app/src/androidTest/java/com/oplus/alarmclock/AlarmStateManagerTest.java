/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.SnoozeTest
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/7/30
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/7/30, W9002519, create
 ************************************************************/
package com.oplus.alarmclock;

import android.app.Notification;
import android.content.Context;
import android.os.SystemClock;

import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.ScreenUtils;
import com.oplus.alarmclock.utils.TestConstant;
import com.oplus.alarmclock.utils.UiDeviceUtils;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;

import java.util.Calendar;
import java.util.List;
@Ignore("编译不过，暂时注释")
public class AlarmStateManagerTest {
    private static final String VOLUME_DOWN = "input keyevent 25";
    private static final String POWER_EVENT = "input keyevent 26";

    private static Context sContext;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
        ScreenUtils.screenOn(sContext);
    }

    @After
    public void tearDown() {
        ClockTestUtils.deleteAllAlarm();
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 场景自动化
     * 锁屏下按电源键延迟闹钟响铃（7.2）
     * 步骤：
     * 1.设置一个1分钟后响铃的闹钟（“稍后提醒”开关打开），锁屏，等待到点闹钟响起，按电源键，亮屏查看是否有闹钟通知弹出
     * 2.等待5分钟后，闹钟是否响起
     * 期望结果：
     * 1.锁屏界面会有闹钟通知，显示5分钟后再提醒
     * 2.到点闹钟会再次响起
     * 实验室机器锁屏后会自动亮屏，导致用例失败。先ignore
     */
    @Ignore
    @Test
    public void should_notify_when_setSnoozeState_with_power_key_event() {
        String title = "测试" + System.currentTimeMillis();
        ClockTestUtils.createAndWaitFullScreenAlert(title, true, false);
        Assert.assertTrue(UiDeviceUtils.isScreenOn());

        UiDeviceUtils.executeShellCommand(POWER_EVENT);

        long powerTime = System.currentTimeMillis();
        long afterAlertTime = powerTime + TestConstant.MINUTE_5;
        SystemClock.sleep(TestConstant.TIME_2000);

        Notification notification = ClockTestUtils.getNotificationByTitle(title);
        Assert.assertNotNull(notification);
        String extraText = notification.extras.getString(Notification.EXTRA_TEXT);

        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(afterAlertTime);
        String contentText = sContext.getString(
                R.string.alarm_notify_snooze_text, Formatter.formatTime(sContext, c));
        Assert.assertEquals(contentText, extraText);

        List<Alarm> alarms = ClockTestUtils.getAlarmClockListData(mMainRule);
        Assert.assertNotNull(alarms);

        Alarm alarm = ClockTestUtils.getAlarmByTitle(alarms, title);
        Assert.assertNotNull(alarm);
        Assert.assertTrue(alarm.isEnabled());
        long nextTime = AlarmUtils.getAlarmNextTime(alarm, null);
        Assert.assertTrue("NextAlertTime:" + nextTime + ", afterAlertTime:" + afterAlertTime,
                Math.abs(nextTime - afterAlertTime) <= TestConstant.SECOND_10);
    }

    /**
     * 场景自动化
     * 锁屏下按音量下键延迟闹钟响铃（7.2）
     * 步骤：
     * 1.设置一个1分钟后响铃的闹钟（“稍后提醒”开关打开），锁屏，等待到点闹钟响起，按音量下键，查看是否有闹钟通知弹出
     * 2.等待5分钟后，闹钟是否响起
     * 期望结果：
     * 1.锁屏界面会有闹钟通知，显示5分钟后再提醒
     * 2.到点闹钟会再次响起
     * 实验室机器锁屏后会自动亮屏，导致用例失败。先ignore
     */
    @Ignore
    @Test
    public void should_notify_when_setSnoozeState_with_volume_key_event() {
        String title = "测试" + System.currentTimeMillis();
        ClockTestUtils.createAndWaitFullScreenAlert(title, true, false);
        Assert.assertTrue(UiDeviceUtils.isScreenOn());

        UiDeviceUtils.executeShellCommand(VOLUME_DOWN);

        long powerTime = System.currentTimeMillis();
        long afterAlertTime = powerTime + TestConstant.MINUTE_5;
        SystemClock.sleep(TestConstant.TIME_2000);

        Notification notification = ClockTestUtils.getNotificationByTitle(title);
        Assert.assertNotNull(notification);

        String extraText = notification.extras.getString(Notification.EXTRA_TEXT);
        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(afterAlertTime);
        String contentText = sContext.getString(
                R.string.alarm_notify_snooze_text, Formatter.formatTime(sContext, c));
        Assert.assertEquals(contentText, extraText);

        List<Alarm> alarms = ClockTestUtils.getAlarmClockListData(mMainRule);
        Assert.assertNotNull(alarms);

        Alarm alarm = ClockTestUtils.getAlarmByTitle(alarms, title);
        Assert.assertNotNull(alarm);
        Assert.assertTrue(alarm.isEnabled());
        long nextTime = AlarmUtils.getAlarmNextTime(alarm, null);
        Assert.assertTrue("NextAlertTime:" + nextTime + ", afterAlertTime:" + afterAlertTime,
                Math.abs(nextTime - afterAlertTime) <= TestConstant.SECOND_10);
    }
}
