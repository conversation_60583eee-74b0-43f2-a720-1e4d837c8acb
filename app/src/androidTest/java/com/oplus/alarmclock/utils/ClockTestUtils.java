/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.utils.ClockTestUtils
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/6/16
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/16, ********, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationManager;
import android.content.ContentProviderClient;
import android.content.Context;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.ConditionVariable;
import android.os.SystemClock;
import android.service.notification.StatusBarNotification;
import android.text.format.Time;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.coui.appcompat.picker.COUITimeLimitPicker;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AddAlarmFragment;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.alert.AlarmAlertFullScreen;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.proxy.ActivityLifecycleCallbacksImpl;

import org.jetbrains.annotations.NotNull;
import org.junit.Assert;

import java.lang.ref.WeakReference;
import java.util.List;

public class ClockTestUtils {

    private static final String ADD_ALARM_URI = "content://com.oplus.alarmclock.ai";
    private static final String ADD_ALARM_INTERFACE = "add_alarm";
    private static final String DEL_ALARM_INTERFACE = "delete_alarm";
    private static final String DEL_ALL_ALARM_INTERFACE = "del_all_alarms";
    private static final String START_TIMER = "start_timer";
    private static final String CHECK_TIMER = "check_timer";
    private static final String CANCEL_TIMER = "cancel_timer";

    private static ConditionVariable sVariable = new ConditionVariable();
    private static WeakReference<Activity> sActivityWeakReference;

    private static ActivityLifecycleCallbacksImpl sCallback = new ActivityLifecycleCallbacksImpl() {
        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            if (activity instanceof AlarmAlertFullScreen) {
                if (sVariable != null) {
                    sActivityWeakReference = new WeakReference<>(activity);
                    sVariable.open();
                }
            }
        }
    };

    public static Bundle addAlarm(int hour, int minutes) {
        Bundle data = new Bundle();
        data.putInt("android.intent.extra.alarm.HOUR", hour);
        data.putInt("android.intent.extra.alarm.MINUTES", minutes);
        return handleData(data, ADD_ALARM_INTERFACE);
    }

    public static Bundle addAlarm(Bundle data) {
        return handleData(data, ADD_ALARM_INTERFACE);
    }

    public static Bundle deleteAllAlarm() {
        return handleData(null, DEL_ALL_ALARM_INTERFACE);
    }

    public static boolean hasRunningTimer() {
        Bundle bundle = handleData(null, CHECK_TIMER);
        int result = bundle.getInt("result", 0);
        if (result != -5 && result != 1) {
            Assert.fail("error result 0.");
        }
        return result == -5;
    }

    public static Bundle cancelTimer() {
        return handleData(null, CANCEL_TIMER);
    }

    private static Bundle handleData(Bundle data, String method) {
        Uri uri = Uri.parse(ADD_ALARM_URI);
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        ContentProviderClient client = context.getContentResolver().acquireUnstableContentProviderClient(uri);
        Assert.assertNotNull("ContentProviderClient is null.", client);

        try {
            return client.call(method, null, data);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail(e.toString());
        } finally {
            client.close();
        }
        return null;
    }

    public static String getTopActivityName() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        Assert.assertNotNull("ActivityManager should not be null.", manager);
        List localList = manager.getRunningTasks(1);
        ActivityManager.RunningTaskInfo localRunningTaskInfo = (ActivityManager.RunningTaskInfo) localList.get(0);
        return localRunningTaskInfo.topActivity.getClassName();
    }

    @NotNull
    public static Time getTimeOfNextMinute() {
        Time time = new Time();
        time.setToNow();
        if (time.minute + 1 >= 60) {
            time.hour += 1;
            time.minute = 0;
            if (time.hour == 24) {
                time.hour = 0;
            }
        } else {
            time.minute += 1;
        }
        return time;
    }

    public static Time getTimeOfNextMinute(int minutes) {
        Time time = new Time();
        time.setToNow();
        if (time.minute + minutes >= 60) {
            time.hour += 1;
            time.minute = time.minute + minutes - 60;
            if (time.hour == 24) {
                time.hour = 0;
            }
        } else {
            time.minute += minutes;
        }
        return time;
    }

    public static void createNextMinuteAlarmByUi(AlarmClock alarmClock, boolean isSnoozeOn, boolean needScreenOn) throws Exception {
        final AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);

        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                alarmClockFragment.openModelView(false);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);

//        final AddAlarmFragment addAlarmFragment = ClockUiUtils.getAddAlarmFragment(alarmClock);
//        Alarm alarm = ClockUiUtils.getAddAlarmFragmentAlarm(addAlarmFragment);
//        // rebuild alarm
//        int snoonzeItem = isSnoozeOn ? ClockConstant.SNOOZE_SWITCH_ON_5_MIN : ClockConstant.SNOOZE_SWITCH_OFF;
//        alarm.setSnoonzeItem(snoonzeItem);
//        ClockUiUtils.setAlarm(addAlarmFragment, alarm);
//
//        COUITimeLimitPicker timePicker = ClockUiUtils.getAddAlarmFragmentTimePicker(addAlarmFragment);
//        Assert.assertNotNull(timePicker);
//
//        Time time = ClockTestUtils.getTimeOfNextMinute();
//        timePicker.setCurrentHour(time.hour);
//        timePicker.setCurrentMinute(time.minute);
        SystemClock.sleep(TestConstant.TIME_200);

//        alarmClock.runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    ClockUiUtils.addAlarmFragmentSaveNewAlarm(addAlarmFragment);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        });
        ScreenUtils.waitAlarmAlert(alarmClock, needScreenOn);
    }

    public static boolean addNewAlarm(int hour, int minute, String label, boolean isSnooze) {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        Alarm alarm = new Alarm();
        alarm.setHour(hour);
        alarm.setMinutes(minute);
        alarm.setLabel(label);
        alarm.setSnoonzeItem(isSnooze ? 1 : 0);
        alarm.setTime(System.currentTimeMillis());
        alarm.setSilent(false);
        alarm.setEnabled(true);
        alarm.setAlert(RingtoneManager.getActualDefaultRingtoneUri(context, RingtoneManager.TYPE_ALARM));
        return AlarmUtils.addNewAlarm(context, alarm, true) > 0;
    }

    public static AlarmAlertFullScreen createAndWaitFullScreenAlert(String label, boolean isSnooze, boolean slide) {
        sActivityWeakReference = null;
        AlarmClockApplication application = AlarmClockApplication.getInstance();
        application.registerActivityLifecycleCallbacks(sCallback);
        Time time = ClockTestUtils.getTimeOfNextMinute();
        boolean success = ClockTestUtils.addNewAlarm(time.hour, time.minute, label, isSnooze);
        Assert.assertTrue("addNewAlarm return false.", success);
        ScreenUtils.screenOff();
        sVariable.close();
        sVariable.block(TestConstant.WAIT_MINUTE);

        SystemClock.sleep(TestConstant.TIME_2000);
        if (slide) {
            UiDeviceUtils.swipeUp();
        }
        application.unregisterActivityLifecycleCallbacks(sCallback);
        if (sActivityWeakReference == null) {
            return null;
        } else {
            return (AlarmAlertFullScreen) sActivityWeakReference.get();
        }
    }

    public static Notification getNotificationByTitle(String title) {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        NotificationManager nm = (NotificationManager) context.getSystemService(
                Context.NOTIFICATION_SERVICE);
        StatusBarNotification[] activeNotifications = nm.getActiveNotifications();
        Notification notification = null;
        for (StatusBarNotification activeNotification : activeNotifications) {
            Notification n = activeNotification.getNotification();
            String extraTitle = n.extras.getString(Notification.EXTRA_TITLE);
            if (title.equals(extraTitle)) {
                notification = n;
                break;
            }
        }
        return notification;
    }

    public static List<Alarm> getAlarmClockListData(ActivityTestRule<AlarmClock> mainRule) {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        ScreenUtils.screenOn(context);
        AlarmClock alarmClock = mainRule.launchActivity(null);
        SystemClock.sleep(TestConstant.TIME_2000);
        AppUtils.skipGuidePage(context);
        AlarmListAdapter adapter = null;
        try {
            AlarmClockFragment alarmClockFragment = ClockUiUtils.getAlarmClockFragment(alarmClock);
            COUIRecyclerView listView = (COUIRecyclerView) alarmClockFragment.getBlurView();
            adapter = (AlarmListAdapter) listView.getAdapter();
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
        Assert.assertNotNull(adapter);
        return adapter.getList();
    }

    public static Alarm getAlarmByTitle(List<Alarm> alarms, String title) {
        for (Alarm alarm : alarms) {
            if (alarm.getLabel().equals(title)) {
                return alarm;
            }
        }
        return null;
    }
}
