/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.utils.ReflectUtils
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/9/11
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/9/11, W9002519, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import org.junit.Assert;

import java.lang.reflect.Field;

public class ReflectUtils {
    public static Object reflect(Class clazz, String name, Object obj) {
        Field declaredField;
        try {
            declaredField = clazz.getDeclaredField(name);
            declaredField.setAccessible(true);
            return declaredField.get(obj);
        } catch (Exception e) {
            Assert.fail("Reflect " + name + " failed." + e.toString());
        }
        return null;
    }
}
