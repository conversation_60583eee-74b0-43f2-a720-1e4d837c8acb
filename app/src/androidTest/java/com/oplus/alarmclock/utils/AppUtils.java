/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.utils.AppUtils
 * Version Number : 1.0
 * Description    :
 * Author         : W9002382
 * Date           : 2020/11/12
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/11/12, W9002382, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.SystemClock;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;

public class AppUtils {
    public static int getVersionCode(Context context) {
        PackageManager manager = context.getPackageManager();
        int code = 0;
        try {
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            code = info.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return code;
    }

    public static boolean isOverEightVersion(Context context) {
        int versionCode = getVersionCode(context);
        return String.valueOf(versionCode).charAt(0) >= '8';
    }

    public static void skipGuidePage(Context context) {
        if (PrivacyPolicyAlert.isFirstEntry(context)) {
            UiDeviceUtils.executeShellCommand("am start -n com.oplus.alarmclock/com.oplus.alarmclock.AlarmClock");
            SystemClock.sleep(TestConstant.TIME_2000);
            UiDeviceUtils.clickByText(context.getString(R.string.color_clock_agree_and_use));
            SystemClock.sleep(TestConstant.TIME_1000);
            UiDeviceUtils.pressBack();
        }
    }
}
