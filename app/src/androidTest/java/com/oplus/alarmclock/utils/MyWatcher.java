/************************************************************
 * Copyright 2010-2019 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       :  MyWatcher.java
 * Version Number : 1.0
 * Description    : android test
 * Author         : yuhang 80264452
 * Date           : 2019-12-19
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-12-19, yuhang, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.util.Log;

import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiObject;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.UiSelector;
import androidx.test.uiautomator.UiWatcher;

public class MyWatcher implements UiWatcher {
    private static final String TAG = MyWatcher.class.getSimpleName();
    private final UiDevice mDevice;

    public MyWatcher(UiDevice device) {
        mDevice = device;
    }

    @Override
    public boolean checkForCondition() {
        if (mDevice.hasObject(By.text("同意"))) {
            UiObject uiObject1 = mDevice.findObject(new UiSelector().text("同意"));
            try {
                uiObject1.click();
                Log.i(TAG, "点击同意成功");
            } catch (UiObjectNotFoundException e) {
                Log.e(TAG, e.toString());
            }
            return true;
        }
        if (mDevice.hasObject(By.text("同意并继续"))) {
            UiObject uiObject1 = mDevice.findObject(new UiSelector().text("同意并继续"));
            try {
                uiObject1.click();
                Log.i(TAG, "点击同意并继续成功");
            } catch (UiObjectNotFoundException e) {
                Log.e(TAG, e.toString());
            }
            return true;
        }
        if (mDevice.hasObject(By.text("确定"))) {
            UiObject uiObject1 = mDevice.findObject(new UiSelector().text("确定"));
            try {
                uiObject1.click();
                Log.i(TAG, "点击确定成功");
            } catch (UiObjectNotFoundException e) {
                Log.e(TAG, e.toString());
            }
            return true;
        }
        if (mDevice.hasObject(By.text("跳过"))) {
            UiObject uiObject1 = mDevice.findObject(new UiSelector().text("跳过"));
            try {
                uiObject1.click();
                Log.i(TAG, "点击跳过成功");
            } catch (UiObjectNotFoundException e) {
                Log.e(TAG, e.toString());
            }
            return true;
        }
        return false;
    }
}
