/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.android.calendar.oplus.specialauto.UiDeviceUtils
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/6/16
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/16, W9002519, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.app.KeyguardManager;
import android.content.Context;
import android.os.RemoteException;
import android.os.SystemClock;
import android.util.Log;

import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.Until;

import com.oplus.autotest.olt.testlib.utils.LogUtils;

import org.junit.Assert;

import java.io.IOException;
import java.util.List;

public class UiDeviceUtils {
    private static final String TAG = UiDeviceUtils.class.getSimpleName();
    private static final int TIMEOUT = 3 * 1000;
    private static UiDevice sInstance = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());

    public static UiDevice getUiDevice() {
        return sInstance;
    }

    public static void screenWakeUpAndUnlock(Context context) throws Exception {
        boolean isScreenOn;
        try {
            isScreenOn = sInstance.isScreenOn();
            if (!isScreenOn) {
                sInstance.wakeUp();
            }
        } catch (Exception e) {
            throw new Exception("Fialed to get isScreenOn." + e.getMessage());
        }
        KeyguardManager keyguardManager =
                (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
        if (keyguardManager == null) {
            throw new Exception("keyguardManager is null.");
        }
        boolean locked = keyguardManager.isKeyguardLocked();
        if (locked) {
            try {
                UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
                        .executeShellCommand("input keyevent 82");
            } catch (Exception e) {
                throw new Exception("Failed to execute shell command : input keyevent 82");
            }
        }
    }

    public static void screenLock() throws Exception {
        long start = System.currentTimeMillis();
        try {
            if (sInstance.isScreenOn()) {
                UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
                        .executeShellCommand("input keyevent 26");
                while (sInstance.isScreenOn() && System.currentTimeMillis() - start < TestConstant.SECOND_10) {
                    SystemClock.sleep(TestConstant.TIME_200);
                }
                Assert.assertFalse("Make screen off failed", sInstance.isScreenOn());
            }
        } catch (Exception e) {
            throw new Exception("Fialed to get isScreenOn." + e.getMessage());
        }
    }

    public static boolean isScreenOn() {
        try {
            return sInstance.isScreenOn();
        } catch (Exception e) {
            Log.d(TAG, e.toString());
            return false;
        }
    }

    public static boolean swipe(int startX, int startY, int endX, int endY, int steps) {
        return sInstance.swipe(startX, startY, endX, endY, steps);
    }

    public static void swipeUp() {
        int w = sInstance.getDisplayWidth();
        int h = sInstance.getDisplayHeight();
        swipe(w / 2, h * 4 / 5, w / 2, h / 5, 10);
        SystemClock.sleep(TestConstant.TIME_2000);
    }

    public static void executeShellCommand(String cmd) {
        try {
            UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
                    .executeShellCommand(cmd);
            SystemClock.sleep(TestConstant.TIME_1000);
        } catch (IOException e) {
            Assert.fail(e.toString());
        }
    }

    public static boolean pressBack() {
        return sInstance.pressBack();
    }

    public static boolean pressHome() {
        return sInstance.pressHome();
    }

    public static boolean pressRecentApps() {
        try {
            return sInstance.pressRecentApps();
        } catch (Exception e) {
            Assert.fail(e.toString());
        }
        return false;
    }

    public static void click(int x, int y) {
        sInstance.click(x, y);
    }

    /**
     * 功能：获取对象
     */
    private static UiObject2 findObjectByText(String text, int timeout) {
        return sInstance.wait(Until.findObject(By.text(text)), timeout);
    }

    private static UiObject2 findObjectByText(Class clazz, String text, int timeout) {
        String expectClazzName = clazz.getCanonicalName();
        List<UiObject2> uiObject2List = sInstance.wait(Until.findObjects(By.text(text)), timeout);
        if (uiObject2List == null) {
            return null;
        }
        for (UiObject2 uiObject2 : uiObject2List) {
            if (expectClazzName.equals(uiObject2.getClassName())) {
                return uiObject2;
            }
        }
        return null;
    }

    public static UiObject2 findObjectByText(String text) {
        return findObjectByText(text, TIMEOUT);
    }

    public static UiObject2 findObjectByText(Class clazz, String text) {
        return findObjectByText(clazz, text, TIMEOUT);
    }

    private static boolean clickByText(String text, int timeout) {
        UiObject2 object = findObjectByText(text, timeout);
        return click(object, text);
    }

    public static boolean clickByText(String text) {
        return clickByText(text, TIMEOUT);
    }

    /**
     * 点击
     */
    private static boolean click(UiObject2 object, String msg) {
        boolean result = false;
        if (!isNull(object, msg)) {
            LogUtils.logDebug(TAG, "点击：" + msg);
            object.click();
            result = true;
        }
        LogUtils.logError(TAG, msg + "点击是否成功：" + result);
        return result;
    }

    private static boolean isNull(UiObject2 uiObject, String msg) {
        boolean result = false;
        if (uiObject == null) {
            Log.i(TAG, "找不到" + msg + "结点");
            result = true;
        }
        return result;
    }

    public static boolean isScreenOnAndUnlock(Context context) {
        boolean isScreenOn;
        try {
            isScreenOn = sInstance.isScreenOn();
        } catch (RemoteException e) {
            return false;
        }
        KeyguardManager keyguardManager =
                (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
        if (keyguardManager == null) {
            return false;
        }
        return isScreenOn && keyguardManager.isKeyguardLocked();
    }

    public static void swipeDownToOpenNotificationBar() {
        int w = sInstance.getDisplayWidth();
        int h = sInstance.getDisplayHeight();
        swipe(w / 2, 0, w / 2, h / 2, 10);
        SystemClock.sleep(TestConstant.TIME_2000);
    }

    public static void swipeLeft() {
        int w = sInstance.getDisplayWidth();
        int h = sInstance.getDisplayHeight();
        swipe(w / 2, h / 2, 0, h / 2, 10);
    }
}
