/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.utils.ClockUiUtils
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/6/17
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/17, W9002519, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.app.Activity;
import android.os.SystemClock;
import android.view.View;
import android.widget.Button;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.alarmclock.AddAlarmFragment;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.alert.AlarmFloatingWindowManager;
import com.oplus.alarmclock.alert.AlarmFloatingWindowView;
import com.oplus.alarmclock.alert.AlarmService;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;
import com.oplus.alarmclock.stopwatch.StopWatchFragment;
import com.oplus.alarmclock.timer.OplusTimerFragment;
import com.oplus.alarmclock.timer.TimerFloatingViewService;
import com.oplus.alarmclock.timer.ui.OplusCountdownTimeView;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.alarmclock.view.LocalViewPager;
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;
import com.coui.appcompat.picker.COUITimeLimitPicker;
import com.oplus.alarmclock.proxy.AlarmListAdapterProxy;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class ClockUiUtils {
    /**
     * Gou should make AlarmClock was shown.
     */
    public static AlarmClockFragment getAlarmClockFragment(AlarmClock activity) throws Exception {
        return (AlarmClockFragment) reflectField(AlarmClock.class, "mAlarmClockFragment", activity);
    }

    /**
     * Get list adapter of AlarmClockFragment.
     */
    public static AlarmListAdapter getAlarmClockFragmentAdapter(AlarmClockFragment fragment) throws Exception {
        return (AlarmListAdapter) reflectField(AlarmClockFragment.class, "mListAdapter", fragment);
    }

    /**
     * Get list view of AlarmClockFragment.
     */
    public static COUIRecyclerView getAlarmClockFragmentListView(AlarmClockFragment fragment) throws Exception {
        return (COUIRecyclerView) reflectField(AlarmClockFragment.class, "mAlarmsList", fragment);
    }

    /**
     * Get list view of AlarmClockFragment.
     */
    public static WorldClockViewFragment getWorldClockViewFragment(AlarmClock activity) throws Exception {
        return (WorldClockViewFragment) reflectField(AlarmClock.class, "mWorldClockFragment", activity);
    }

    /**
     * Get float window viewa when larm alerting with screen is on.
     */
    public static AlarmFloatingWindowView getFloatWindowView() throws Exception {
        AlarmFloatingWindowManager floatWindowManager =
                (AlarmFloatingWindowManager) reflectField(AlarmService.class, "sAlarmFloatingWindowManager", null);
        if (floatWindowManager == null) {
            throw new Exception("When alarm alert is showing, sAlarmFloatingWindowManager should't be null.");
        }
        return (AlarmFloatingWindowView) reflectField(AlarmFloatingWindowManager.class, "mAlarmFloatingWindowView", floatWindowManager);
    }

    public static AlarmFloatingWindowView getTimerFloatWindowView() throws Exception {
        AlarmFloatingWindowManager floatWindowManager =
                (AlarmFloatingWindowManager) reflectField(TimerFloatingViewService.class, "sAlarmFloatingWindowManager", null);
        if (floatWindowManager == null) {
            throw new Exception("When timer alert is showing, sAlarmFloatingWindowManager should't be null.");
        }
        return (AlarmFloatingWindowView) reflectField(AlarmFloatingWindowManager.class, "mAlarmFloatingWindowView", floatWindowManager);
    }

    /**
     * Imitate user opration which click button of save when showing AddAlarmFragment.
     */
    public static void addAlarmFragmentSaveNewAlarm(AddAlarmFragment fragment) throws Exception {
        Method asyncSaveResultM;
        try {
            asyncSaveResultM = AddAlarmFragment.class.getDeclaredMethod("asyncSaveResult");
        } catch (NoSuchMethodException e) {
            throw new Exception("Reflect asyncSaveResult() failed." + e.toString());
        }
        asyncSaveResultM.setAccessible(true);
        asyncSaveResultM.invoke(fragment);

        Method closeAlarmSetSetPageM;
        try {
            closeAlarmSetSetPageM = AddAlarmFragment.class.getDeclaredMethod("closeAlarmSetSetPage");
        } catch (NoSuchMethodException e) {
            throw new Exception("Reflect closeAlarmSetSetPage() failed." + e.toString());
        }
        closeAlarmSetSetPageM.setAccessible(true);
        closeAlarmSetSetPageM.invoke(fragment);
    }

    /**
     * Replace mListAdapter of AlarmClockFragment with AlarmListAdapterProxy so that the tester
     * can easily obtain itemView.
     */
    public static void replaceAlarmListAdapter(final AlarmClockFragment alarmClockFragment,
                                               final AlarmListAdapterProxy adapterProxy) throws Exception {
        Field mListAdapterF;
        try {
            mListAdapterF = AlarmClockFragment.class.getDeclaredField("mListAdapter");
        } catch (NoSuchFieldException e) {
            throw new Exception("Reflect mListAdapter failed." + e.toString());
        }
        mListAdapterF.setAccessible(true);
        mListAdapterF.set(alarmClockFragment, (AlarmListAdapter) adapterProxy);

        alarmClockFragment.getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    COUIRecyclerView listView = getAlarmClockFragmentListView(alarmClockFragment);
                    listView.setAdapter((AlarmListAdapter) adapterProxy);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

    }

//    /**
//     * Get the Fragment popped up by the "+" function in the AlarmClock.
//     */
//    public static AddAlarmFragment getAddAlarmFragment(AlarmClock alarmClock) throws Exception {
//        AlarmModelView mAlarmModelView =
//                (AlarmModelView) reflectField(AlarmClock.class, "mAlarmModelView", alarmClock);
//
//        Field mFragmentF;
//        try {
//            mFragmentF = AlarmModelView.class.getDeclaredField("mAddAlarmFragment");
//        } catch (NoSuchFieldException e) {
//            throw new Exception("Reflect mFragment failed." + e.toString());
//        }
//        mFragmentF.setAccessible(true);
//        Fragment mFragment = (Fragment) mFragmentF.get(mAlarmModelView);
//        if (!(mFragment instanceof AddAlarmFragment)) {
//            throw new Exception("Current mFragment is not instanceof AddAlarmFragment.");
//        }
//        return (AddAlarmFragment) mFragment;
//    }

    /**
     * Get the TimePicker of AddAlarmFragment for setting alarm time.
     */
    public static COUITimeLimitPicker getAddAlarmFragmentTimePicker(AddAlarmFragment fragment) throws Exception {
        return (COUITimeLimitPicker) reflectField(AddAlarmFragment.class, "mOplusTimePicker", fragment);
    }

    public static Alarm getAddAlarmFragmentAlarm(AddAlarmFragment fragment) throws Exception {
        return (Alarm) reflectField(AddAlarmFragment.class, "mAlarm", fragment);
    }

    public static void setAlarm(AddAlarmFragment fragment, Alarm alarm) throws Exception {
        Field mAlarmF;
        try {
            mAlarmF = AddAlarmFragment.class.getDeclaredField("mAlarm");
        } catch (NoSuchFieldException e) {
            throw new Exception("Reflect mAlarm failed." + e.toString());
        }
        mAlarmF.setAccessible(true);
        mAlarmF.set(fragment, alarm);
    }

    public static View getRootViewOfFloatWindowView(AlarmFloatingWindowView windowView) throws Exception {
        return (View) reflectField(AlarmFloatingWindowView.class, "mWindowView", windowView);
    }

    public static ViewPager2 getColorViewPager(AlarmClock alarmClock) throws Exception {
        return (ViewPager2) reflectField(AlarmClock.class, "mColorViewPager", alarmClock);
    }

    public static Object reflectField(@NotNull Class clazz, @NotNull String name, Object obj) throws Exception {
        Field declaredField;
        try {
            declaredField = clazz.getDeclaredField(name);
        } catch (NoSuchFieldException e) {
            throw new Exception("Reflect " + name + " failed." + e.toString());
        }
        declaredField.setAccessible(true);
        return declaredField.get(obj);
    }

    public static OplusTimerFragment gotoOplusTimerFragment(AlarmClock alarmClock) {
        final ViewPager2 colorViewPager =
                (ViewPager2) ReflectUtils.reflect(AlarmClock.class, "mColorViewPager", alarmClock);

        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                colorViewPager.setCurrentItem(AlarmClock.TAB_INDEX_OPLUSTIME);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);

        BaseFragment fragment = (BaseFragment) ReflectUtils.reflect(AlarmClock.class, "mCurrentFragment", alarmClock);
        return (OplusTimerFragment) fragment;
    }

    public static WorldClockViewFragment gotoWorldClockViewFragment(AlarmClock alarmClock) {
        final ViewPager2 colorViewPager =
                (ViewPager2) ReflectUtils.reflect(AlarmClock.class, "mColorViewPager", alarmClock);

        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                colorViewPager.setCurrentItem(AlarmClock.TAB_INDEX_GLOBALCITY);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);

        BaseFragment fragment = (BaseFragment) ReflectUtils.reflect(AlarmClock.class, "mWorldClockFragment", alarmClock);
        return (WorldClockViewFragment) fragment;
    }

    public static StopWatchFragment gotoStopWatchFragment(AlarmClock alarmClock) {
        final ViewPager2 colorViewPager =
                (ViewPager2) ReflectUtils.reflect(AlarmClock.class, "mColorViewPager", alarmClock);

        alarmClock.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                colorViewPager.setCurrentItem(AlarmClock.TAB_INDEX_STOPWATCH);
            }
        });
        SystemClock.sleep(TestConstant.TIME_1000);

        BaseFragment fragment = (BaseFragment) ReflectUtils.reflect(AlarmClock.class, "mStopWatchFragment", alarmClock);
        return (StopWatchFragment) fragment;
    }

    public static TimerController getTimerController(OplusTimerFragment fragment) {
        return (TimerController) ReflectUtils.reflect(OplusTimerFragment.class, "mTimerController", fragment);
    }

    public static OplusTimePickerCustomClock getTimerPicker(TimerController controller) {
        return (OplusTimePickerCustomClock) ReflectUtils.reflect(TimerController.class, "mOplusTimerPicker", controller);
    }

    public static OplusCountdownTimeView getCountdownTime(TimerController controller) {
        return (OplusCountdownTimeView) ReflectUtils.reflect(TimerController.class, "mCountdownTime", controller);
    }

    public static Button getBtCancel(TimerController controller) {
        return (Button) ReflectUtils.reflect(TimerController.class, "mButtonCancel", controller);
    }

    public static Button getBtStop(TimerController controller) {
        return (Button) ReflectUtils.reflect(TimerController.class, "mButtonPause", controller);
    }

    public static Button getBtStart(TimerController controller) {
        return (Button) ReflectUtils.reflect(TimerController.class, "mButtonStart", controller);
    }

    public static void clickView(Activity activity, final View view) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                view.performClick();
            }
        });
    }

    public static void setCountdownTime(Activity activity, final OplusTimePickerCustomClock timerPicker,
                                        final int h, final int m, final int s) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                timerPicker.setCurrentHour(h);
                timerPicker.setCurrentMinute(m);
                timerPicker.setCurrentSecond(s);
            }
        });
        SystemClock.sleep(TestConstant.TIME_500);
    }
}
