/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.utils.InitTestUtils
 * Version Number : 1.0
 * Description    :
 * Author         : W9002519
 * Date           : 2020/6/19
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/6/19, W9002519, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.SystemClock;

import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiObject2;

import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.R;

public class InitTestUtils {

    private static boolean sIsIniting = false;
    private static String[] sPermissionsOpts = {"始终允许", "允许", "仅在使用此应用时允许", "使用时允许", "同意"};

    public static void ignoreMorningAlarm() {
        final Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        boolean morningClockStatus = MorningAlarmClock.ifSupportMorningBroadcast(context);
        if (morningClockStatus) {
            PrefUtils.putBoolean(context, MorningAlarmClock.MORNING_PREFERENCE, MorningAlarmClock.MORNING_IS_SHOWN, true);
        }
        if (!sIsIniting) {
            sIsIniting = true;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    handleRuntimePermission(context);
                }
            }).start();
            new Thread(new Runnable() {
                @Override
                public void run() {
                    handlePermissionDialog();
                }
            }).start();
        }
    }

    private static void handlePermissionDialog() {
        UiDevice uiDevice = UiDeviceUtils.getUiDevice();
        long start = System.currentTimeMillis();
        while (System.currentTimeMillis() - start < TestConstant.WAIT_MINUTE) {
            SystemClock.sleep(TestConstant.TIME_200);
            for (String str : sPermissionsOpts) {
                UiObject2 object = uiDevice.findObject(By.text(str));
                if (object != null) {
                    object.click();
                }
            }
        }
        sIsIniting = false;
    }

    private static void handleRuntimePermission(Context context) {
        UiDevice uiDevice = UiDeviceUtils.getUiDevice();
        String ok = context.getResources().getString(R.string.privacy_policy_ok);
        boolean flag = true;
        long start = System.currentTimeMillis();
        while (flag && System.currentTimeMillis() - start < TestConstant.WAIT_MINUTE) {
            SystemClock.sleep(TestConstant.TIME_200);
            UiObject2 object = uiDevice.findObject(By.text(ok));
            if (object != null) {
                object.click();
                flag = false;
            }
        }
    }
}
