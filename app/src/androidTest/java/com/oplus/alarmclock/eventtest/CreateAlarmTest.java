/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.eventtest.CreateAlarmTest
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2020/7/27
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/7/27, ********, create
 ************************************************************/
package com.oplus.alarmclock.eventtest;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;

import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.autotest.olt.testlib.annotations.CaseId;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
public class CreateAlarmTest {

    private static Context sContext;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
    }

    @After
    public void tearDown() {

    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 新建0时00分闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置0时00分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01907")
    @CasePrioritization("C")
    public void clock_api_0001() {
        int hour = 0;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建0时59分闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置0时59分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01908")
    @CasePrioritization("C")
    public void clock_api_0002() {
        int hour = 0;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01909")
    @CasePrioritization("C")
    public void clock_api_0003() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建24时00分闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置24时00分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建失败，add_alarm接口result返回-1
     */
    @Test
    @CaseId("AT_APP_Clock_01910")
    @CasePrioritization("C")
    public void clock_api_0004() {
        int hour = 24;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal -1", -1, result.getInt("result"));
    }

    /**
     * 新建23时60分闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时60分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建失败，add_alarm接口result返回-1
     */
    @Test
    @CaseId("AT_APP_Clock_01911")
    @CasePrioritization("C")
    public void clock_api_0005() {
        int hour = 23;
        int minutes = 60;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal -1", -1, result.getInt("result"));
    }

    /**
     * 新建12.34时10分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置12.34时10分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为23:10
     */
    @Test
    @CaseId("AT_APP_Clock_01912")
    @CasePrioritization("C")
    public void clock_api_0006() {
        double hour = 12.34;
        int minutes = 10;
        String label = "";
        Bundle args = new Bundle();
        args.putDouble("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建九时20分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置九时20分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为23:20
     */
    @Test
    @CaseId("AT_APP_Clock_01913")
    @CasePrioritization("C")
    public void clock_api_0007() {
        String hour = "九";
        int minutes = 20;
        String label = "";
        Bundle args = new Bundle();
        args.putString("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建null时30分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置null时30分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为23:30
     */
    @Test
    @CaseId("AT_APP_Clock_01914")
    @CasePrioritization("C")
    public void clock_api_0008() {
        int minutes = 30;
        String label = "";
        Bundle args = new Bundle();
        args.putSerializable("android.intent.extra.alarm.HOUR", null);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建12时40.1分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置12时40.1分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为12:00
     */
    @Test
    @CaseId("AT_APP_Clock_01915")
    @CasePrioritization("C")
    public void clock_api_0009() {
        int hour = 12;
        double minutes = 40.1;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putDouble("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建13时五十分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置13时五十分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为13:00
     */
    @Test
    @CaseId("AT_APP_Clock_01916")
    @CasePrioritization("C")
    public void clock_api_0010() {
        int hour = 13;
        String minutes = "五十";
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putString("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建14时null分的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置14时null分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 5、创建成功，闹钟时间为14:00
     */
    @Test
    @CaseId("AT_APP_Clock_01917")
    @CasePrioritization("C")
    public void clock_api_0011() {
        int hour = 14;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putSerializable("android.intent.extra.alarm.MINUTES", null);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建闹钟时间合法，名称为长度为1的中文字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为1的中文字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01918")
    @CasePrioritization("C")
    public void clock_api_0012() {
        int hour = 8;
        int minutes = 0;
        String label = "一";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为1的数字字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为1的中文字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01919")
    @CasePrioritization("C")
    public void clock_api_0013() {
        int hour = 8;
        int minutes = 0;
        String label = "1";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为1的特殊字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为1的特殊字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01920")
    @CasePrioritization("C")
    public void clock_api_0014() {
        int hour = 8;
        int minutes = 0;
        String label = "@";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为5的中文字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为5的中文字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01921")
    @CasePrioritization("C")
    public void clock_api_0015() {
        int hour = 8;
        int minutes = 0;
        String label = "一二三四五";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为5的数字字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为5的数字字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01922")
    @CasePrioritization("C")
    public void clock_api_0016() {
        int hour = 8;
        int minutes = 0;
        String label = "12345";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为5的特殊字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为5的特殊字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01923")
    @CasePrioritization("C")
    public void clock_api_0017() {
        int hour = 8;
        int minutes = 0;
        String label = "@@##@";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为40的中文字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01924")
    @CasePrioritization("C")
    public void clock_api_0018() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为40的数字字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为40的数字字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01925")
    @CasePrioritization("C")
    public void clock_api_0019() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "1";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为40的特殊字符（包括乱码等）
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为40的特殊字符（包括乱码等）
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01926")
    @CasePrioritization("C")
    public void clock_api_0020() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 20; i++) {
            label += "@毺";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为41的中文字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为41的中文字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建失败，add_alarm接口result返回-1；
     */
    @Test
    @CaseId("AT_APP_Clock_01927")
    @CasePrioritization("C")
    public void clock_api_0021() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 41; i++) {
            label += "一";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为41的数字字符
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为41的数字字符
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建失败，add_alarm接口result返回-1；
     */
    @Test
    @CaseId("AT_APP_Clock_01928")
    @CasePrioritization("C")
    public void clock_api_0022() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 41; i++) {
            label += "1";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，名称为长度为41的特殊字符（包括乱码等）
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为41的特殊字符（包括乱码等）
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建失败，add_alarm接口result返回-1；
     */
    @Test
    @CaseId("AT_APP_Clock_01929")
    @CasePrioritization("C")
    public void clock_api_0023() {
        int hour = 8;
        int minutes = 0;
        String label = "@";
        for (int i = 0; i < 20; i++) {
            label += "@毺";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，铃声为无
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置8时00分
     * 2、响一次
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：铃声为无
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01930")
    @CasePrioritization("C")
    public void clock_api_0024() {
        int hour = 8;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", android.provider.AlarmClock.VALUE_RINGTONE_SILENT);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，铃声为“繁星”
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置18时00分
     * 2、每周一重复
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：铃声为“繁星”
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01931")
    @CasePrioritization("C")
    public void clock_api_0025() {
        int hour = 18;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/113?title=ringtone_014&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，铃声天气铃声
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置18时00分
     * 2、每周一重复
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：铃声为无
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01932")
    @CasePrioritization("C")
    public void clock_api_0026() {
        int hour = 18;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "dynamic_weather_alert");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，铃声为本地铃声-音乐
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置18时00分
     * 2、每周一重复
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：本地铃声-音乐，
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致；
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01933")
    @CasePrioritization("C")
    public void clock_api_0034() {
        int hour = 18;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        Uri uri = getAudioContentUri(sContext, new File("/storage/emulated/0/Music/song/本地音乐.mp3"));
        args.putString("android.intent.extra.alarm.RINGTONE", uri.toString());
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建闹钟时间合法，铃声为本地铃声-录音
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置18时00分
     * 2、每周一重复
     * 3、闹钟名称为长度为40的中文字符
     * 4、铃声与振动为：本地铃声-录音
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01934")
    @CasePrioritization("C")
    public void clock_api_0035() {
        int hour = 18;
        int minutes = 0;
        String label = "";
        for (int i = 0; i < 40; i++) {
            label += "一";
        }
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        Uri uri = getAudioContentUri(sContext, new File("/storage/emulated/0/Music/Recordings/Standard Recordings/录音测试.mp3"));
        args.putString("android.intent.extra.alarm.RINGTONE", uri.toString());
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建0时01分周一闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置0时01分
     * 2、自定义周三（Mon）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01935")
    @CasePrioritization("C")
    public void clock_api_0039() {
        int hour = 0;
        int minutes = 1;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(4);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分周日闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、自定义周日（Sun）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01936")
    @CasePrioritization("C")
    public void clock_api_0040() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(1);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分周八闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、自定义周八（EIGHT)
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建成功，add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01937")
    @CasePrioritization("C")
    public void clock_api_0041() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(9);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建0时01分周一、周一闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置0时01分
     * 2、自定义周一（Mon），周一（mon）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建成功，add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01938")
    @CasePrioritization("C")
    public void clock_api_0042() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        days.add(2);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分周一、周三闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、自定义周一（Mon），周三（Wed）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01939")
    @CasePrioritization("C")
    public void clock_api_0043() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        days.add(4);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分周一到周日闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、自定义周一到周日（Mon，Tue，Wed，Thu，Fri，Sat，Sun）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01940")
    @CasePrioritization("C")
    public void clock_api_0044() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        for (int i = 1; i <= 7; i++) {
            days.add(i);
        }
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 新建23时59分周几为null闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置23时59分
     * 2、重复周期周几为null
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 闹钟创建成功，add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01941")
    @CasePrioritization("C")
    public void clock_api_0045() {
        int hour = 23;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
    }

    /**
     * 新建20时00分工作日（法定工作日）的闹钟
     * [When]手机中不存在闹钟
     * [Given]
     * 1、设置20时00分
     * 2、重复周期为工作日（法定工作日）
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致，add_alarm接口result返回1；，
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01942")
    @CasePrioritization("C")
    public void clock_api_0047() {
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 已有2047个闹钟，再新建一个闹钟
     * [When]已有2047个闹钟
     * [Given]
     * 1、设置0时00分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01943")
    @CasePrioritization("C")
    public void clock_api_0052() {
        AudioManager mAudioManager = (AudioManager) sContext.getSystemService(Context.AUDIO_SERVICE);
        int mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM);
        SharedPreferences mSharePre = sContext.getSharedPreferences("shared_prefs_alarm_app",
                Context.MODE_PRIVATE);
        String name = "";
        for (int i = 1; i <= 2047; i++) {
            name = "" + i;
            Alarm alarm = Alarm.build(true, 20, 00, 0, 2, name,
                    null,
                    mSharePre.getString("set_alram_ring_name", null),
                    mVolume,
                    -1,
                    0, 0);
            AlarmUtils.addNewAlarm(sContext, alarm, false);
        }
        int hour = 0;
        int minutes = 0;
        String label = "2048";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        boolean flag = false;
        if (alarms.get(0).getLabel().equals(label) && alarms.get(0).getHour() == hour && alarms.get(0).getMinutes() == minutes) {
            flag = true;
        }
        Assert.assertTrue("value should be equal", flag);
    }

    /**
     * 已有2048个闹钟，再新建一个闹钟
     * [When]已有2047个闹钟
     * [Given]
     * 1、设置0时00分
     * 2、响一次
     * 3、闹钟名称为空
     * 4、铃声与振动为：铃声：放假
     * 5、调用add_alarm接口创建闹钟
     * [Then]
     * 1、闹钟创建成功，保持开启，闹钟详细信息与创建时保持一致
     * 2、add_alarm接口result返回1；
     */
    @Test
    @CaseId("AT_APP_Clock_01944")
    @CasePrioritization("C")
    public void clock_api_0053() {
        AudioManager mAudioManager = (AudioManager) sContext.getSystemService(Context.AUDIO_SERVICE);
        int mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM);
        SharedPreferences mSharePre = sContext.getSharedPreferences("shared_prefs_alarm_app",
                Context.MODE_PRIVATE);
        String name = "";
        for (int i = 1; i <= 2048; i++) {
            name = "" + i;
            Alarm alarm = Alarm.build(true, 20, 00, 0, 2, name,
                    null,
                    mSharePre.getString("set_alram_ring_name", null),
                    mVolume,
                    -1,
                    0, 0);
            AlarmUtils.addNewAlarm(sContext, alarm, false);
        }
        int hour = 0;
        int minutes = 0;
        String label = "2049";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", -2, result.getInt("result"));
    }

    public static Uri getAudioContentUri(Context context, File imageFile) {
        String filePath = imageFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                new String[]{MediaStore.Audio.Media._ID}, MediaStore.Audio.Media.DATA + "=? ",
                new String[]{filePath}, null);
        if (cursor != null && cursor.moveToFirst()) {
            int id = cursor.getInt(cursor.getColumnIndex(MediaStore.Audio.AudioColumns._ID));
            Uri baseUri = Uri.parse("content://media/external/audio/media/");
            return Uri.withAppendedPath(baseUri, "" + id);
        } else {
            if (imageFile.exists()) {
                ContentValues values = new ContentValues();
                values.put(MediaStore.Audio.Media.DATA, filePath);
                return context.getContentResolver().insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values);
            } else {
                return null;
            }
        }
    }
}
