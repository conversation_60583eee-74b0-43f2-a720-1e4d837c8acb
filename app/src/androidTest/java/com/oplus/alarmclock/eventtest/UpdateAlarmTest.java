/************************************************************
 * Copyright 2000-2017 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * FileName       : com.oplus.alarmclock.eventtest.UpdateAlarmTest
 * Version Number : 1.0
 * Description    :
 * Author         : ********
 * Date           : 2022/12/28
 * History        :( ID,     Date,         Author, Description)
 * v1.0,  2020/7/27, ********, create
 ************************************************************/
package com.oplus.alarmclock.eventtest;

import android.content.ContentResolver;
import android.content.Context;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;

import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.UiDevice;

import com.oplus.alarmclock.utils.PhenixUtils;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.utils.AppUtils;
import com.oplus.alarmclock.utils.ClockTestUtils;
import com.oplus.alarmclock.utils.InitTestUtils;
import com.oplus.alarmclock.utils.MyWatcher;
import com.oplus.autotest.olt.testlib.annotations.CaseId;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import oppo.autotest.phenix.Phone;

public class UpdateAlarmTest {
    //oppo_data_app_std(oppo_data_app_std)
    private static Context sContext;
    private static Phone mPhone;

    @Rule
    public ActivityTestRule<AlarmClock> mMainRule = new ActivityTestRule<>(AlarmClock.class, false, false);

    @BeforeClass
    public static void classSetUp() {
        UiDevice device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
        MyWatcher myWatcher = new MyWatcher(device);
        device.registerWatcher("弹窗watcher", myWatcher);
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        InitTestUtils.ignoreMorningAlarm();
        AppUtils.skipGuidePage(sContext);
        mPhone = PhenixUtils.Companion.getInstance().getPhone();
    }

    @Before
    public void setUp() {
        ClockTestUtils.deleteAllAlarm();
        SystemClock.sleep(1500);
        if (ClockTestUtils.getAlarmClockListData(mMainRule).size() > 0) {
            ClockTestUtils.deleteAllAlarm();
        }
        mMainRule.finishActivity();
        Assert.assertEquals("delete all alarm fail", 0, ClockTestUtils.getAlarmClockListData(mMainRule).size());
        mMainRule.finishActivity();
    }

    @After
    public void tearDown() {
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
    }

    /**
     * 关闭保持开启20:00响一次闹钟
     * [When]已创建20:00响一次闹钟
     * [Given]调用close_alarm接口关闭此20:00闹钟
     * [Then]闹钟关闭成功，close_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01945")
    @CasePrioritization("C")
    public void clock_api_0054() {
        //创建20:00响一次闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //关闭20:00响一次闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
            Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
        }
    }

    /**
     * 关闭20:00重复闹钟最近一次闹钟
     * [When]已创建20:00法定工作日重复闹钟
     * [Given]调用close_alarm接口关闭此20:00法定工作日重复闹钟最近一次闹钟
     * [Then]闹钟关闭成功，close_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01946")
    @CasePrioritization("C")
    public void clock_api_0055() {
        //创建20:00法定工作日闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //关闭闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
            Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
        }
    }

    /**
     * 关闭20:00重复周期闹钟
     * [When]已创建20:00法定工作日闹钟
     * [Given]调用close_alarm接口关闭此20:00法定工作日重复闹钟
     * [Then]闹钟关闭成功，close_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01947")
    @CasePrioritization("C")
    public void clock_api_0056() {
        //创建20:00法定工作日闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //关闭闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
            Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
        }
    }

    /**
     * 关闭已关闭20:00闹钟
     * [When]已创建20:00响一次闹钟且起闹时已关闭此闹钟
     * [Given]调用close_alarm接口关闭已关闭20:00闹钟
     * [Then]闹钟关闭失败，close_alarm接口result返回-1
     */
    @Test
    @CaseId("AT_APP_Clock_01948")
    @CasePrioritization("C")
    public void clock_api_0057() {
        //创建20:00已关闭此闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //关闭闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
    }

    /**
     * 关闭已删除20:00闹钟
     * [When]
     * 1、已创建20:00法定工作日闹钟
     * 2、删除该闹钟
     * [Given]调用close_alarm接口关闭此已删除的20:00法定工作日闹钟
     * [Then]闹钟关闭失败，close_alarm接口result返回-1
     */
    @Test
    @CaseId("AT_APP_Clock_01949")
    @CasePrioritization("C")
    public void clock_api_0058() {
        //创建20:00闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        //关闭闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
            Assert.assertEquals("The result should equal -1", -1, result_close.getInt("result"));
        }
    }

    /**
     * 关闭已起闹过一次20:00闹钟
     * [When]已创建20:00响一次闹钟且起闹时已暂停此闹钟
     * [Given]调用close_alarm接口关闭此20:00闹钟
     * [Then]闹钟关闭成功，close_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01950")
    @CasePrioritization("C")
    public void clock_api_0059() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //创建20:00响一次闹钟
        int hour = 20;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //让闹钟起闹一次
        mPhone.server.settings.systemTimeSet(year, month, day, 19, 59, 59);
        SystemClock.sleep(4000);
        mPhone.server.settings.systemTimeSet(year, month, day, 20, 15, 15);
        //关闭闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
    }

    /**
     * 删除保持开启07:00闹钟
     * [When]已创建07:00响一次闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01951")
    @CasePrioritization("C")
    public void clock_api_0060() {
        //创建07:00闹钟
        int hour = 7;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            Bundle result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
            Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
        }
    }

    /**
     * 删除已关闭07:00响一次闹钟
     * [When]已创建07:00响一次闹钟且起闹时已关闭此闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01952")
    @CasePrioritization("C")
    public void clock_api_0061() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //创建07:00闹钟
        int hour = 7;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //让闹钟起闹一次
        mPhone.server.settings.systemTimeSet(year, month, day, 6, 59, 59);
        SystemClock.sleep(4000);
        mPhone.server.settings.systemTimeSet(year, month, day, 7, 15, 15);
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
    }

    /**
     * 删除07:00响一次已暂停闹钟
     * [When]已创建07:00响一次闹钟且起闹时已暂停此闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01953")
    @CasePrioritization("C")
    public void clock_api_0062() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //创建20:00闹钟
        int hour = 7;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 6, 59, 59);
        SystemClock.sleep(4000);
        //暂停闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //删除闹钟
        Bundle result_close = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_close.getInt("result"));
    }

    /**
     * 删除已删除07:00闹钟
     * [When]已创建07:00响一次闹钟，删除此闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回-3
     */
    @Test
    @CaseId("AT_APP_Clock_01954")
    @CasePrioritization("C")
    public void clock_api_0063() {
        int hour = 7;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        //再次删除删除闹钟
        Bundle result_delete = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        Assert.assertEquals("The result should equal -3", -3, result_delete.getInt("result"));
    }

    /**
     * 删除已关闭（仅关闭最近一次）07:00重复周期闹钟
     * [When]已创建07:00周一到周五重复周期闹钟，关闭最近一次闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01955")
    @CasePrioritization("C")
    public void clock_api_0064() {
        int hour = 7;
        int minutes = 0;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        days.add(3);
        days.add(4);
        days.add(5);
        days.add(6);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_delete = null;
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        }
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 删除已关闭07:00重复周期闹钟
     * [When]已创建07:00周一到周五重复周期闹钟，关闭此重复周期闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01956")
    @CasePrioritization("C")
    public void clock_api_0065() {
        int hour = 7;
        int minutes = 0;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        days.add(3);
        days.add(4);
        days.add(5);
        days.add(6);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_delete = null;
        if (alarms != null && alarms.size() > 0) {
            Bundle args2 = new Bundle();
            args2.putInt("alarm_hour", hour);
            args2.putInt("alarm_minute", minutes);
            args2.putLong("alarm_id", alarms.get(0).getId());
            result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        }
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 删除保持开启07:00重复周期闹钟
     * [When]已创建07:00周一到周五重复周期闹钟
     * [Given]调用delete_alarm接口删除此07:00闹钟
     * [Then]闹钟删除成功，delete_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01957")
    @CasePrioritization("C")
    public void clock_api_0066() {
        int hour = 7;
        int minutes = 0;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(2);
        days.add(3);
        days.add(4);
        days.add(5);
        days.add(6);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_delete = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 开启已开启11:59闹钟
     * [When]已创建11:59闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启成功，enable_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01958")
    @CasePrioritization("C")
    public void clock_api_0067() {
        int hour = 11;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //打开闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 开启11:59已关闭最近一次闹钟的重复周期闹钟
     * [When]已创建11:59周六周日重复周期闹钟，关闭最近一次闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启成功，enable_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01959")
    @CasePrioritization("C")
    public void clock_api_0068() {
        //周日闹钟，
        int hour = 11;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(8);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.inte nt.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //新建周六关闭闹钟
        ArrayList<Integer> days1 = new ArrayList<>();
        days1.add(7);
        Bundle args1 = new Bundle();
        args1.putIntegerArrayList("android.intent.extra.alarm.DAYS", days1);
        args1.putInt("android.intent.extra.alarm.HOUR", hour);
        args1.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args1.putString("label", label);
        args1.putInt("android.intent.action.ALARM_ENABLE", 0);
        args1.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args1);
        //打开闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 开启11:59已关闭所有重复周期闹钟
     * [When]已创建11:59周六周日重复周期闹钟，关闭此重复周期闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启成功，enable_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01960")
    @CasePrioritization("C")
    public void clock_api_0069() {
        int hour = 11;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(7);
        days.add(8);
        Bundle args = new Bundle();
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //打开闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 开启11:59响一次起闹后已关闭闹钟
     * [When]已创建11:59响一次闹钟且起闹时已关闭此闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启成功，enable_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01961")
    @CasePrioritization("C")
    public void clock_api_0070() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 11;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(7);
        days.add(8);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 11, 58, 59);
        SystemClock.sleep(4000);
        //关闭闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "close_alarm", null, null);
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //打开闹钟
        Bundle result_enable = null;
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 开启11:59响一次起闹后已暂停闹钟
     * [When]已创建11:59响一次闹钟且起闹时已暂停此闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启成功，enable_alarm接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01962")
    @CasePrioritization("C")
    public void clock_api_0071() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 11;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(7);
        days.add(8);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 11, 58, 59);
        SystemClock.sleep(4000);
        //暂停闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //开启闹钟
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 开启已删除11:59响一次闹钟
     * [When]已创建11:59响一次闹钟且已删除此闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启失败，enable_alarm接口result返回-3
     */
    @Test
    @CaseId("AT_APP_Clock_01963")
    @CasePrioritization("C")
    public void clock_api_0072() {
        int hour = 11;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        //打开闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal -3", -3, result_enable.getInt("result"));
    }

    /**
     * 开启已删除11:59重复周期闹钟
     * [When]已创建11:59周六周日闹钟且已删除此闹钟
     * [Given]调用enable_alarm接口开启此11:59闹钟
     * [Then]闹钟开启失败，enable_alarm接口result返回-3
     */
    @Test
    @CaseId("AT_APP_Clock_01964")
    @CasePrioritization("C")
    public void clock_api_0073() {
        int hour = 11;
        int minutes = 59;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(7);
        days.add(8);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        //打开闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "enable_alarm", null, args2);
        Assert.assertEquals("The result should equal -1", -3, result_enable.getInt("result"));
    }

    /**
     * 更新已开启12:00响一次闹钟
     * [When]已创建12:00响一次闹钟
     * [Given]调用update_alarm接口更新闹钟时间为11:58
     * [Then]更新成功，update_alarm接口result返回1，闹钟详细信息与更新后保持一致
     */
    @Test
    @CaseId("AT_APP_Clock_01965")
    @CasePrioritization("C")
    public void clock_api_0074() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //更新闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 13);
        args2.putInt("alarm_minute", 0);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00响一次起闹过已关闭闹钟
     * [When]1、已创建12:00响一次闹钟且已起闹关闭该闹钟
     * [Given]1、调用update_alarm接口更新闹钟时间为12:02
     * [Then]更新成功，update_alarm接口result返回1，闹钟详细信息与更新后保持一致
     */
    @Test
    @CaseId("AT_APP_Clock_01966")
    @CasePrioritization("C")
    public void clock_api_0075() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //更新闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 12);
        args2.putInt("alarm_minute", 2);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00响一次起闹过已暂停闹钟
     * [When]1、已创建12:00响一次闹钟且已起闹暂停该闹钟
     * [Given]调用update_alarm接口更新闹钟时间为12:00
     * [Then]更新成功，update_alarm接口result返回1，闹钟详细信息与更新后保持一致
     */
    @Test
    @CaseId("AT_APP_Clock_01967")
    @CasePrioritization("C")
    public void clock_api_0076() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //更新闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 12);
        args2.putInt("alarm_minute", 00);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00响一次起闹过已暂停闹钟
     * [When]1、已创建12:00响一次闹钟且已起闹暂停该闹钟
     * [Given]调用update_alarm接口更新闹钟时间为00:00
     * [Then]更新成功，update_alarm接口result返回1，闹钟详细信息与更新后保持一致
     */
    @Test
    @CaseId("AT_APP_Clock_01968")
    @CasePrioritization("C")
    public void clock_api_0077() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        ArrayList<Integer> days = new ArrayList<>();
        days.add(7);
        Bundle args = new Bundle();
        args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //更新闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 0);
        args2.putInt("alarm_minute", 00);
        args2.putLong("alarm_id", alarms.get(0).getId());
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 更新已删除12:00闹钟
     * [When]
     * 1、已创建12:00闹钟响一次闹钟
     * 2、删除该闹钟
     * [Given]调用update_alarm接口更新闹钟时间为12:00
     * [Then]更新失败，update_alarm接口result返回-3
     */
    @Test
    @CaseId("AT_APP_Clock_01969")
    @CasePrioritization("C")
    public void clock_api_0078() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 12);
        args2.putInt("alarm_minute", 00);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        //更新闹钟
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal -1", -1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00已关闭最近一次闹钟的重复周期闹钟
     * [When]已创建12:00工作日闹钟，且已关闭此重复周期闹钟最近一次闹钟
     * [Given]1、调用update_alarm接口更新闹钟时间为23:58
     * [Then]更新成功，update_alarm接口result返回1，闹钟详细信息与更新后保持一致
     */
    @Test
    @CaseId("AT_APP_Clock_01970")
    @CasePrioritization("C")
    public void clock_api_0079() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 23);
        args2.putInt("alarm_minute", 58);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //更新闹钟
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal 1", 1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00已关闭所有重复周期闹钟
     * [When]1、已创建12:00工作日闹钟，且已关闭此重复周期闹钟
     * [Given]1、调用update_alarm接口更新闹钟时间为24:00
     * [Then]更新不成功，update_alarm接口result返回-1，闹钟时间不改变
     */
    @Test
    @CaseId("AT_APP_Clock_01971")
    @CasePrioritization("C")
    public void clock_api_0080() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 24);
        args2.putInt("alarm_minute", 0);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //更新闹钟
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal -1", -1, result_enable.getInt("result"));
    }

    /**
     * 更新12:00已关闭所有重复周期闹钟
     * [When]已创建12:00响一次闹钟
     * [Given]调用update_alarm接口更新闹钟时间为23:60
     * [Then]更新不成功，update_alarm接口result返回-1，闹钟时间不改变
     */
    @Test
    @CaseId("AT_APP_Clock_01972")
    @CasePrioritization("C")
    public void clock_api_0081() {
        int hour = 12;
        int minutes = 00;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle result_enable = null;
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", 23);
        args2.putInt("alarm_minute", 60);
        args2.putLong("alarm_id", alarms.get(0).getId());
        //更新闹钟
        result_enable = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "update_alarm", null, args2);
        Assert.assertEquals("The result should equal -1", -1, result_enable.getInt("result"));
    }

    /**
     * 无闹钟时删除所有闹钟
     * [When]无闹钟
     * [Given]调用del_all_alarms接口删除所有闹钟
     * [Then]时钟不出现异常，del_all_alarms接口result返回-3
     */
    @Test
    @CaseId("AT_APP_Clock_01973")
    @CasePrioritization("C")
    public void clock_api_0082() {
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        Assert.assertEquals("The result should equal -3", -3, result_delete.getInt("result"));
    }

    /**
     * 仅有1个已关闭闹钟时删除所有闹钟
     * [When]已新建10:00闹钟且已关闭该闹钟
     * [Given]1、调用del_all_alarms接口删除所有闹钟
     * [Then]删除成功，del_all_alarms接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01974")
    @CasePrioritization("C")
    public void clock_api_0083() {
        int hour = 10;
        int minutes = 0;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putInt("android.intent.action.ALARM_ENABLE", 0);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        Bundle result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 有5个已开启重复周期闹钟5个已关闭重复周期闹钟删除所有闹钟
     * [When]有5个已开启重复周期闹钟5个已关闭重复周期闹钟
     * [Given]调用del_all_alarms接口删除所有闹钟
     * [Then]删除成功，del_all_alarms接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01975")
    @CasePrioritization("C")
    public void clock_api_0084() {
        int hour = 10;
        int minutes = 0;
        ContentResolver resolver = sContext.getContentResolver();
        for (int i = 0; i < 10; i++) {
            Bundle args = new Bundle();
            args.putInt("android.intent.extra.alarm.HOUR", hour);
            args.putInt("android.intent.extra.alarm.MINUTES", minutes);
            args.putString("label", "" + i);
            if (i < 5) {
                ArrayList<Integer> days = new ArrayList<>();
                days.add(6);
                args.putIntegerArrayList("android.intent.extra.alarm.DAYS", days);
            }
            args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
            Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
            Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        }
        Bundle result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 已有2048个闹钟，删除所有闹钟
     * [When]已有2048个闹钟
     * [Given]调用del_all_alarms接口删除所有闹钟
     * [Then]删除成功，del_all_alarms接口result返回1
     */
    @Test
    @CaseId("AT_APP_Clock_01976")
    @CasePrioritization("C")
    public void clock_api_0085() {
        AudioManager mAudioManager = (AudioManager) sContext.getSystemService(Context.AUDIO_SERVICE);
        int mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM);
        SharedPreferences mSharePre = sContext.getSharedPreferences("shared_prefs_alarm_app",
                Context.MODE_PRIVATE);
        String name = "";
        for (int i = 1; i <= 2048; i++) {
            name = "" + i;
            Alarm alarm = Alarm.build(true, 20, 00, 0, 2, name,
                    null,
                    mSharePre.getString("set_alram_ring_name", null),
                    mVolume,
                    -1,
                    0, 0);
            AlarmUtils.addNewAlarm(sContext, alarm, false);
        }
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result_delete = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        Assert.assertEquals("The result should equal 1", 1, result_delete.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，停止闹钟
     * [When]响铃一次23:59闹钟正在响铃
     * [Given]调用stop_alarm接口停止该闹钟
     * [Then]成功停止，stop_alarm接口result返回1，该闹钟关闭
     */
    @Test
    @CaseId("AT_APP_Clock_01977")
    @CasePrioritization("C")
    public void clock_api_0086() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，等待响铃时长到（稍后提醒次数已满）
     * [When]响铃一次23:59闹钟正在响铃（稍后提醒次数已满）
     * [Given]等待响铃时长到
     * [Then]成功停止，stop_alarm接口result返回1，该闹钟关闭
     */
    @Test
    @CaseId("AT_APP_Clock_01978")
    @CasePrioritization("C")
    public void clock_api_0087() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 重复周期23:59闹钟正在响铃，停止闹钟
     * [When]23:59法定工作日重复周期闹钟正在响铃
     * [Given]调用stop_alarm接口停止该闹钟
     * [Then]本次起闹成功停止，stop_alarm接口result返回1，闹钟保持开启
     */
    @Test
    @CaseId("AT_APP_Clock_01979")
    @CasePrioritization("C")
    public void clock_api_0088() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 重复周期23:59闹钟正在响铃，停止闹钟（稍后提醒次数已满）
     * [When]23:59法定工作日重复周期闹钟正在响铃（稍后提醒次数已满）
     * [Given]等待响铃时长到
     * [Then]本次起闹成功停止，stop_alarm接口result返回1，闹闹钟保持开启
     */
    @Test
    @CaseId("AT_APP_Clock_01980")
    @CasePrioritization("C")
    public void clock_api_0089() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，删除此闹钟
     * [When]响铃一次23:59闹钟正在响铃
     * [Given]调用delete_alarm接口删除此闹钟
     * [Then]stop_alarm接口result返回-1，起闹终止，时钟不出现异常
     */
    @Test
    @CaseId("AT_APP_Clock_01981")
    @CasePrioritization("C")
    public void clock_api_0090() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //删除闹钟
        ArrayList<Alarm> alarms = AlarmUtils.getAllAlarms(sContext);
        Bundle args2 = new Bundle();
        args2.putInt("alarm_hour", hour);
        args2.putInt("alarm_minute", minutes);
        args2.putLong("alarm_id", alarms.get(0).getId());
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "delete_alarm", null, args2);
        SystemClock.sleep(500);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，删除所有闹钟
     * [When]响铃一次23:59闹钟正在响铃
     * [Given]调用del_all_alarms接口删除所有闹钟
     * [Then]stop_alarm接口result返回-1，起闹终止，时钟不出现异常
     */
    @Test
    @CaseId("AT_APP_Clock_01982")
    @CasePrioritization("C")
    public void clock_api_0091() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //删除闹钟
        resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "del_all_alarms", null, null);
        SystemClock.sleep(500);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "stop_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，暂停闹钟
     * [When]响铃一次23:59闹钟正在响铃
     * [Given]调用snooze_alarm接口暂停此闹钟
     * [Then]成功暂停，snooze_alarm接口result返回1，该闹钟保持开启，稍后提醒时间后有闹钟
     */
    @Test
    @CaseId("AT_APP_Clock_01983")
    @CasePrioritization("C")
    public void clock_api_0092() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "snooze_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 响铃一次23:59闹钟正在响铃，等待响铃时长到（稍后提醒次数已满）
     * [When]响铃一次23:59闹钟正在响铃（稍后提醒次数已满）
     * [Given]等待响铃时长到
     * [Then]成功暂停，snooze_alarm接口result返回1，该闹钟保持开启，稍后提醒时间后有闹钟
     */
    @Test
    @CaseId("AT_APP_Clock_01984")
    @CasePrioritization("C")
    public void clock_api_0093() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "snooze_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 重复周期23:59闹钟正在响铃，停止闹钟
     * [When]23:59法定工作日重复周期闹钟正在响铃
     * [Given]调用snooze_alarm接口暂停此闹钟
     * [Then]成功暂停，snooze_alarm接口result返回1，该闹钟保持开启，稍后提醒时间后有闹钟
     */
    @Test
    @CaseId("AT_APP_Clock_01985")
    @CasePrioritization("C")
    public void clock_api_0094() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "snooze_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

    /**
     * 重复周期23:59闹钟正在响铃，停止闹钟（稍后提醒次数已满）
     * [When]23:59法定工作日重复周期闹钟正在响铃（稍后提醒次数已满）
     * [Given]等待响铃时长到
     * [Then]成功暂停，snooze_alarm接口result返回1，该闹钟保持开启，稍后提醒时间后有闹钟
     */
    @Test
    @CaseId("AT_APP_Clock_01986")
    @CasePrioritization("C")
    public void clock_api_0095() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DATE);
        mPhone.server.settings.systemTimeSet(year, month, day, 1, 1, 1);
        //新建闹钟
        int hour = 23;
        int minutes = 59;
        String label = "";
        Bundle args = new Bundle();
        args.putInt("android.intent.extra.alarm.HOUR", hour);
        args.putInt("android.intent.extra.alarm.MINUTES", minutes);
        args.putString("label", label);
        args.putString("android.intent.extra.alarm.RINGTONE", "content://media/internal/audio/media/198?title=ringtone_035&canonical=1");
        args.putInt("android.intent.extra.alarm.workday_switch", 1);
        ContentResolver resolver = sContext.getContentResolver();
        Bundle result = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "add_alarm", null, args);
        Assert.assertEquals("The result should equal 1", 1, result.getInt("result"));
        //起闹时钟
        mPhone.server.settings.systemTimeSet(year, month, day, 23, 58, 58);
        SystemClock.sleep(8000);
        //停止闹钟
        Bundle resultStop = resolver.call(Uri.parse("content://com.coloros.alarmclock.ai"), "snooze_alarm", null, null);
        Assert.assertEquals("The result should equal 1", 1, resultStop.getInt("result"));
    }

}
