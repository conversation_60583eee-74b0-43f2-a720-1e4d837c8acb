<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="1"
    android:versionName="1.0">

    <application android:label="@string/test_for_clock_api">
        <uses-library
            android:name="androidx.test.runner"
            android:required="false" />
    </application>
    <!--<instrumentation-->
        <!--android:name="androidx.test.runner.AndroidJUnitRunner"-->
        <!--android:functionalTest="false"-->
        <!--android:label="@string/test_for_clock_api"-->
        <!--android:targetPackage="com.oplus.alarmclock">-->
    <!--</instrumentation>-->

    <instrumentation
        android:name="com.oplus.autotest.olt.testlib.common.AndroidRunner"
        android:functionalTest="false"
        android:label="@string/test_for_clock_api"
        android:targetPackage="com.oplus.alarmclock">
        <meta-data
            android:name="listener"
            android:value="com.oplus.autotest.olt.testlib.common.TestRunListener" />
    </instrumentation>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />


</manifest>
