/************************************************************
 * Copyright 2017 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * 
 * Description : SystemUI provide to dismiss system notification when alarm notification is on.
 * 
 * History :( ID, Date, Author, Description) v1.0, 2017-7-14, <PERSON> create
 ************************************************************/
package com.android.systemui.statusbar;

import android.os.IBinder;

interface IHighPriorityHeadsUpService {
    void showHeadsUp(IBinder binder);
    void dismissHeadsUp();
}
