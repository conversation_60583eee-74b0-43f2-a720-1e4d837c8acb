/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DragonflyDataPacker.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.coloros.refusedesktop.viewmodel

import com.coloros.refusedesktop.model.TimerEntity
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.smartenginehelper.dsl.DSLCoder

class DragonflyDataPacker(private val mData: TimerEntity) : BaseDataPack() {
    companion object {
        private const val DATA_ID = "dragonfly_id"
        private const val KEY_CURRENT_TIME = "key_current_time"
        private const val KEY_TOTAL_TIME = "key_total_time"
        private const val KEY_NAME = "key_name"
        private const val KEY_STATUS = "key_status"
        private const val KEY_RING_PATH = "key_ring_path"
        private const val KEY_RING_TITLE = "key_ring_title"
        private const val KEY_TIME_STAMP = "key_time_stamp"
        private const val KEY_THEME_COLOR = "key_theme_color"
        private const val KEY_TIMER_LIST = "key_timer_list"
        private const val KEY_SELECTED_POSITION = "key_selected_position"
    }

    override fun onPack(coder: DSLCoder): Boolean {
        coder.run {
            mData.run {
                setCustomData(DATA_ID, KEY_CURRENT_TIME, mCurrentTime)
                setCustomData(DATA_ID, KEY_TOTAL_TIME, mTotalTime)
                setCustomData(DATA_ID, KEY_NAME, mName)
                setCustomData(DATA_ID, KEY_STATUS, mStatus)
                setCustomData(DATA_ID, KEY_RING_PATH, mRingPath)
                setCustomData(DATA_ID, KEY_RING_TITLE, mRingTitle)
                setCustomData(DATA_ID, KEY_THEME_COLOR, mThemeColor)
                setCustomData(DATA_ID, KEY_TIMER_LIST, mListJson)
                setCustomData(DATA_ID, KEY_SELECTED_POSITION, mSelectedPosition)
                //发送的数据相同，负一屏不会更新，所以加入时间戳确保每次都更新
                setCustomData(DATA_ID, KEY_TIME_STAMP, System.currentTimeMillis())
            }
        }
        return true
    }
}