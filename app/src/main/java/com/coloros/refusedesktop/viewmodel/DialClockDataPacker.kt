/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockDataPacker.kt.java
 ** Description:数据回调赋值
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop.viewmodel

import com.oplus.clock.common.utils.Log
import com.coloros.refusedesktop.Constants
import com.coloros.refusedesktop.model.DialClockModel
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.smartenginehelper.dsl.DSLCoder

class DialClockDataPacker(private val dialClockModel: DialClockModel) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(TAG, "dialClockModel=$dialClockModel")
        dialClockModel.mCityName?.let { coder.setCustomData(Constants.DIAL_CLOCK_TYPE, Constants.CITY_NAME, it) }
        dialClockModel.mTimeZoneOffsetTime?.let { coder.setCustomData(Constants.DIAL_CLOCK_TYPE, Constants.TIME_ZONE_OFFSET_TIME, it) }
        dialClockModel.mTimeZone?.let { coder.setCustomData(Constants.DIAL_CLOCK_TYPE, Constants.TIME_ZONE, it) }
        dialClockModel.mChangeValue?.let { coder.setCustomData(Constants.DIAL_CLOCK_TYPE, Constants.CHANGE_VALUE, it) }
        dialClockModel.mThemeColor?.let { coder.setCustomData(Constants.DIAL_CLOCK_TYPE, Constants.THEME_COLOR, it) }
        return true
    }

}