/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockDataPacker.kt.java
 ** Description:数据回调赋值
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop.viewmodel

import com.google.gson.Gson
import com.oplus.clock.common.utils.Log
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.coloros.refusedesktop.Constants
import com.coloros.refusedesktop.model.DialClockModel
import com.oplus.smartenginehelper.dsl.DSLCoder

class DialWorldClockDataPacker(private val dialClockModelList: List<DialClockModel>) :
    BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        val arraylist = Gson().toJson(dialClockModelList)
        Log.i("DialWorldClockDataPacker", "arraylist:$arraylist")
        arraylist.let {
            coder.setCustomData(
                Constants.DIAL_WORLD_CLOCK_TYPE,
                Constants.DIAL_LIST,
                it
            )
        }
        return true
    }
}
