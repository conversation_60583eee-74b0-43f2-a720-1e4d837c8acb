/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmClockWidgetProvider.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.coloros.refusedesktop

import android.annotation.SuppressLint
import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.text.TextUtils
import android.util.Log
import android.view.ContextThemeWrapper
import com.coloros.refusedesktop.model.TimerEntity
import com.coloros.refusedesktop.viewmodel.DragonflyDataPacker
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.google.gson.Gson
import com.oplus.alarmclock.R
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.alarmclock.timer.TimerNotificationManager
import com.oplus.alarmclock.timer.TimerService
import com.oplus.alarmclock.timer.TimerService.TimerBinder
import com.oplus.alarmclock.timer.data.OplusTimer
import com.oplus.alarmclock.timer.data.TimerDataHelper
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.ProcessCommunicateTimer.Companion.POSITION_NOT_VALUE
import com.oplus.alarmclock.utils.ProcessCommunicateTimer.Companion.POSITION_NO_SELECTED
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AlarmClockWidgetProvider : AppCardWidgetProvider() {
    companion object {
        private const val TAG = "AlarmClockWidgetProvider"
        private const val LAYOUT_NAME = "dragonfly_layout.json"
        private const val SP_NAME = "shared_prefs_alarm_app"
        private const val TIMER_WIDGET_CODE = "timer_widget_code"
        private const val TIMER_NAME_SP = "timer_desc_time"
        private const val STATUS_STOP = 0
        private const val STATUS_START = 1
        private const val STATUS_PAUSE = 2
    }

    private var mWidgetCode: String? = null
    private var mTimerService: TimerService? = null
    private var mDefaultTimerName: String? = null
    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            mTimerService = (service as TimerBinder).service
            mTimerService?.let {
                context?.run {
                    getTimerEntity(this, it) {
                        update(it)
                    }
                }
            }
        }

        override fun onServiceDisconnected(name: ComponentName) {
            mTimerService = null
        }
    }

    override fun getCardLayoutName(widgetCode: String): String {
        return LAYOUT_NAME
    }

    override fun onResume(context: Context, widgetCode: String) {
        mWidgetCode = widgetCode
        PrefUtils.putString(context, SP_NAME, TIMER_WIDGET_CODE, widgetCode)
        if (mTimerService == null) {
            bindTimerService()
        } else {
            mTimerService?.run {
                getTimerEntity(context, this) {
                    update(it)
                }
            }
        }
    }

    fun update(data: TimerEntity) {
        mWidgetCode = mWidgetCode ?: PrefUtils.getString(context, SP_NAME, TIMER_WIDGET_CODE, "")
        Log.d(TAG, "update—>mWidgetCode:$mWidgetCode,data:$data")
        mWidgetCode?.run {
            CardWidgetAction.postUpdateCommand(context, DragonflyDataPacker(data), this)
        }
        if (data.mStatus == STATUS_START) {
            mTimerService?.pauseTimer(0)
        }
        //当卡片可见时通知栏的通知不显示
        TimerNotificationManager.cancelTimerNotification(context)
    }

    private fun getTimerEntity(
        context: Context, service: TimerService, result: (data: TimerEntity) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            val timerPosition = withContext(coroutineContext) {
                getTimerSelectedPosition(context)
            }
            val mName = PrefUtils.getString(
                context, SP_NAME, TIMER_NAME_SP, getDefaultTimerName(context)
            )
            val mCurrentTime = service.getRemainTime(0)
            val mTotalTime = service.getTotalTime(0)
            val mStatus: Int = getTimerStatus(service)
            val ringPath: String = getRingPath(service)
            val ringTitle: String = getRingTitle(service)
            val themeColor = getThemeColor(context)
            val listJson = Gson().toJson(TimerDataHelper.getTimerList(context))
            result(
                TimerEntity(
                    mCurrentTime,
                    mTotalTime,
                    mName,
                    timerPosition,
                    mStatus,
                    ringPath,
                    ringTitle,
                    themeColor,
                    listJson
                )
            )
        }
    }

    private fun getDefaultTimerName(context: Context): String? {
        if (mDefaultTimerName == null) {
            mDefaultTimerName = context.resources.getString(R.string.timer_title)
        }
        return mDefaultTimerName
    }

    private fun getRingPath(service: TimerService): String {
        val ringPath = service.getTimerRingUri(0)
        return if (TextUtils.isEmpty(ringPath)) "" else ringPath
    }

    private fun getRingTitle(service: TimerService): String {
        val ringName = service.getTimerRingName(0)
        return if (TextUtils.isEmpty(ringName)) "" else ringName
    }

    private fun getTimerStatus(service: TimerService): Int {
        return if (service.isStart(0)) {
            STATUS_START
        } else if (mTimerService!!.isPause(0)) {
            STATUS_PAUSE
        } else {
            STATUS_STOP
        }
    }

    private fun bindTimerService() {
        context?.run {
            val intent = Intent(this, TimerService::class.java)
            applicationContext.bindService(intent, mConnection, Service.BIND_AUTO_CREATE)
            startService(intent)
        }
    }

    /**
     * 直接使用Provider的context获取不到主题色
     */
    private fun getThemeColor(context: Context): Int {
        val mThemeColorContext =
            ContextThemeWrapper(context.applicationContext, R.style.AppNoTitleTheme)
        COUIThemeOverlay.getInstance().applyThemeOverlays(mThemeColorContext)
        return COUIContextUtil.getAttrColor(mThemeColorContext, R.attr.couiColorPrimary)
    }

    private fun getTimerSelectedPosition(context: Context): Int {
        return mTimerService?.run {
            selectedTimer?.timerIndex ?: getPosition(context)
        } ?: POSITION_NOT_VALUE
    }

    private fun getPosition(context: Context): Int {
        return getPositionFromDb(context)?.timerIndex ?: POSITION_NO_SELECTED
    }

    @SuppressLint("Range")
    private fun getPositionFromDb(context: Context): OplusTimer? {
        var value: OplusTimer? = null
        val where = "${ClockContract.TimerTableColumns.SELECTED} == 1"
        val cursor =
            context.contentResolver.query(ClockContract.TIMER_CONTENT_URI, null, where, null, null)
        cursor?.run {
            while (cursor.moveToNext()) {
                value = OplusTimer().apply {
                    timerIndex = getInt(getColumnIndex(ClockContract.TimerTableColumns._ID))
                }
            }
            close()
        } ?: Log.d(TAG, "cursor  is null")
        return value
    }
}