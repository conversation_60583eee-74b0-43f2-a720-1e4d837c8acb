/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - Constants.kt.java
 ** Description:常量类
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop

class Constants {
    companion object {
        const val DIAL_CLOCK_JSON_FILE = "dial_clock.json"
        const val WPLUS_DIAL_CLOCK_JSON_FILE = "oneplus_dial_clock.json"
        const val DIAL_WORLD_CLOCK_JSON_FILE = "dial_world_clock.json"
        const val WPLUS_WORLD_CLOCK_JSON_FILE = "oneplus_dial_world_clock.json"
        const val DIAL_CLOCK_PREF_FILE_NAME = "dial_clock_pref_file_name"
        const val EXTRA_DIAL_CLOCK_WIDGET_CODE = "dial_clock_widget_code"
        const val EXTRA_DIAL_CLOCK_CITY_ID = "dial_clock_city_id"
        const val EXTRA_DIAL_CLOCK_CITY_NAME = "dial_clock_city_name"
        const val EXTRA_DIAL_CLOCK_CITY_TIMEZONE = "dial_clock_city_timezone"
        const val DIAL_CLOCK_TYPE = "dial_clock"
        const val DIAL_WORLD_CLOCK_TYPE = "dial_world_clock"
        const val CITY_NAME = "cityName"
        const val TIME_ZONE = "timeZone"
        const val TIME_ZONE_OFFSET_TIME = "timeZoneOffsetTime"
        const val CHANGE_VALUE = "changeValue"
        const val CARD_TYPE = "cardType"
        const val CARD_ID = "cardId"
        const val HOST_ID = "hostId"
        const val THEME_COLOR = "theme_color"
        const val DIAL_LIST = "dial_list"
        const val DIAL_CLOCK_TYPE_CODE = 69
        const val DIAL_WORLD_CLOCK_TYPE_CODE = 222220017

        const val TIME_SIX_AM = 6    //上午6点
        const val TIME_SIX_PM = 18   //下午6点
        const val RANDOM_NUM = 10000

        /** OS14上最多显示三个表盘  */
        const val DIAL_CLOCK_MAX_NUM_3 = 3

        /** OS13上最多显示四个表盘  */
        const val DIAL_CLOCK_MAX_NUM_4 = 4
    }
}