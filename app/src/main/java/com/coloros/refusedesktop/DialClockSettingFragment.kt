/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockSettingFragment.kt.java
 ** Description:表盘时钟设置相关fragment
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.globalclock.AddCityActivity
import com.oplus.alarmclock.globalclock.AddCityFragment
import com.oplus.alarmclock.globalclock.CityUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.Utils
import com.coloros.refusedesktop.model.DialClockModel
import com.coloros.refusedesktop.view.DialClockView
import com.coloros.refusedesktop.view.DialClockView13
import com.coloros.refusedesktop.view.DialClockView14
import com.coloros.refusedesktop.view.DialClockView15
import com.coloros.refusedesktop.view.DialClockView16
import com.coloros.refusedesktop.view.OOSDialClockView15
import com.coloros.refusedesktop.viewmodel.DialClockDataPacker
import com.coloros.refusedesktop.viewmodel.DialClockViewModel
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.databinding.FragmentDialClockSetting13Binding
import com.oplus.alarmclock.databinding.FragmentDialClockSetting14Binding
import com.oplus.alarmclock.databinding.FragmentDialClockSetting15Binding
import com.oplus.alarmclock.databinding.FragmentDialClockSetting16Binding
import com.oplus.alarmclock.databinding.FragmentDialClockSettingBinding
import com.oplus.alarmclock.databinding.FragmentOosDialClockSetting15Binding
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.cardwidget.domain.action.CardWidgetAction

class DialClockSettingFragment : COUIPanelFragment() {

    private var mDialClockModel = DialClockModel()
    private var mDialog: COUIBottomSheetDialog? = null
    private var mCardId = -1 //负一屏卡片id
    private var mCardType = -1 //卡片type
    private var mHostId = -1//桌面卡片id
    private var mViewBinding: FragmentDialClockSettingBinding? = null
    private var mViewBinding13: FragmentDialClockSetting13Binding? = null
    private var mViewBinding14: FragmentDialClockSetting14Binding? = null
    private var mViewBinding15: FragmentDialClockSetting15Binding? = null
    private var mViewBinding16: FragmentDialClockSetting16Binding? = null
    private var mViewOOSBinding15: FragmentOosDialClockSetting15Binding? = null
    private var mTvCityName: TextView? = null
    private var mLinearCity: LinearLayout? = null
    private var mViewDialClock: DialClockView? = null
    private var mViewDialClock13: DialClockView13? = null
    private var mViewDialClock14: DialClockView14? = null
    private var mViewDialClock15: DialClockView15? = null
    private var mViewDialClock16: DialClockView16? = null
    private var mViewOOSDialClock15: OOSDialClockView15? = null

    /**get widgetCode from cardType and cardId */
    private fun getWidgetId(cardType: Int, cardId: Int, hostId: Int): String {
        return "$cardType$CARDTYPE_SPLIT$cardId$CARDTYPE_SPLIT$hostId"
    }

    @SuppressLint("InflateParams")
    override fun initView(panelView: View?) {
        super.initView(panelView)
        toolbar.visibility = View.VISIBLE
        dragView.visibility = View.INVISIBLE
        val view = activity?.let {
            val dialVersion = Utils.getDialVersion()
            if (dialVersion >= Utils.VERSION_16) {
                if (!DeviceUtils.isWPlusPhone()) {
                    mViewBinding16 = FragmentDialClockSetting16Binding.inflate(LayoutInflater.from(it), null, false).apply {
                        mTvCityName = tvCityName
                        mLinearCity = linearCity
                        mViewDialClock16 = viewDialClock
                    }
                    mViewBinding16?.root
                } else {
                    mViewOOSBinding15 = FragmentOosDialClockSetting15Binding.inflate(LayoutInflater.from(it), null, false).apply {
                        mTvCityName = tvCityName
                        mLinearCity = linearCity
                        mViewOOSDialClock15 = viewDialClock
                    }
                    mViewOOSBinding15?.root
                }
            } else if (dialVersion >= Utils.VERSION_15) {
                if (!DeviceUtils.isWPlusPhone()) {
                    mViewBinding15 = FragmentDialClockSetting15Binding.inflate(LayoutInflater.from(it), null, false).apply {
                        mTvCityName = tvCityName
                        mLinearCity = linearCity
                        mViewDialClock15 = viewDialClock
                    }
                    mViewBinding15?.root
                } else {
                    mViewOOSBinding15 = FragmentOosDialClockSetting15Binding.inflate(LayoutInflater.from(it), null, false).apply {
                        mTvCityName = tvCityName
                        mLinearCity = linearCity
                        mViewOOSDialClock15 = viewDialClock
                    }
                    mViewOOSBinding15?.root
                }
            } else if (dialVersion >= Utils.VERSION_14) {
                mViewBinding14 = FragmentDialClockSetting14Binding.inflate(LayoutInflater.from(it), null, false).apply {
                    mTvCityName = tvCityName
                    mLinearCity = linearCity
                    mViewDialClock14 = viewDialClock
                }
                mViewBinding14?.root
            } else if (dialVersion >= Utils.VERSION_13) {
                mViewBinding13 = FragmentDialClockSetting13Binding.inflate(LayoutInflater.from(it), null, false).apply {
                    mTvCityName = tvCityName
                    mLinearCity = linearCity
                    mViewDialClock13 = viewDialClock
                }
                mViewBinding13?.root
            } else {
                mViewBinding = FragmentDialClockSettingBinding.inflate(LayoutInflater.from(it), null, false).apply {
                    mTvCityName = tvCityName
                    mLinearCity = linearCity
                    mViewDialClock = viewDialClock
                }
                mViewBinding?.root
            }
        }
        (contentView as? ViewGroup)?.addView(view)

        getBundleArg()

        draggableLinearLayout.setDividerVisibility(false)
        mTvCityName?.setTextColor(
            COUIContextUtil.getAttrColor(
                context,
                R.attr.couiColorPrimary
            )
        )
        mLinearCity?.setOnClickListener {
            jumpToAddCityActivity()
        }
        toolbar?.apply {
            title = getString(R.string.city_time_title)
            isTitleCenterStyle = true
            inflateMenu(R.menu.activity_add_alarm_menu)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    mDialog?.dismiss()
                    true
                }
            }
            menu.findItem(R.id.save).apply {
                setOnMenuItemClickListener {
                    activity?.apply {
                        DialClockViewModel.saveDialClockData(applicationContext, mDialClockModel)
                        CardWidgetAction.postUpdateCommand(
                            context,
                            DialClockDataPacker(mDialClockModel),
                            getWidgetId(mDialClockModel.mCardType, mDialClockModel.mCardID, mDialClockModel.mHostId)
                        )
                        mDialog?.dismiss()
                    }
                    true
                }
            }
        }
       /* setBottomButtonBar(getString(R.string.cancel), {
            //关闭界面
            mDialog?.dismiss()
        }, getString(R.string.save), {
            activity?.apply {
                DialClockViewModel.saveDialClockData(applicationContext, mDialClockModel)
                CardWidgetAction.postUpdateCommand(
                    context,
                    DialClockDataPacker(mDialClockModel),
                    DialClockViewModel.getDialClockWidgetCode(applicationContext, mDialClockModel)
                )
                mDialog?.dismiss()
            }
        }, "", null)*/

        mDialog =
            ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
        mDialog?.apply {
            setOnDismissListener {
                Log.d(FRAGMENT_TAG, "mDialog onDismiss")
                activity?.finish()
            }
        }

        mDialClockModel = DialClockViewModel.getDialClockData(
            context?.applicationContext
                ?: AlarmClockApplication.getInstance(), mCardType, mCardId, mHostId
        )
        if (isInMultiWindowMode() && FoldScreenUtils.isScreenRealUnfold()) {
            mViewDialClock?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }
            mViewDialClock13?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }
            mViewDialClock14?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }
            mViewDialClock15?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }
            mViewDialClock16?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }
            mViewOOSDialClock15?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_dial_split_margin_top)
            }

            mLinearCity?.let {
                setViewTopMargin(it, R.dimen.dial_clock_setting_city_split_margin_top)
            }
        }
        updateUIData()
    }

    private fun getBundleArg() {
        mCardId = arguments?.getInt(Constants.CARD_ID) ?: mCardId
        mCardType = arguments?.getInt(Constants.CARD_TYPE) ?: mCardType
        mHostId = arguments?.getInt(Constants.HOST_ID) ?: mHostId
    }

    private fun updateUIData() {
        mTvCityName?.run {
            text = mDialClockModel.mCityName
        }
        setDialClockData()
    }

    private fun setDialClockData() {
        mViewDialClock?.run {
            setData(mDialClockModel)
        }
        mViewDialClock13?.run {
            setData(mDialClockModel)
        }
        mViewDialClock14?.run {
            setData(mDialClockModel)
        }
        mViewDialClock15?.run {
            setData(mDialClockModel)
        }
        mViewDialClock16?.run {
            setData(mDialClockModel)
        }
        mViewOOSDialClock15?.run {
            setData(mDialClockModel)
        }
    }

    private fun jumpToAddCityActivity() {
        val addCityIntent = Intent(activity, AddCityActivity::class.java)
        addCityIntent.action = AddCityFragment.ACTION_ADD_WORLD_CLOCK
        addCityIntent.putExtra(AddCityActivity.IS_SHOW_PANEL, true)
        addCityIntent.putExtra(AddCityFragment.FROM_DIAL_CLOCK_KEY, true)
        startActivityForResult(addCityIntent, CITY_REQUEST_CODE)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if ((requestCode == CITY_REQUEST_CODE) && (Activity.RESULT_OK == resultCode)) {
            data?.let {
                mDialClockModel.mCityId =
                    it.getIntExtra(Constants.EXTRA_DIAL_CLOCK_CITY_ID, 0).toString()
                mDialClockModel.mCityName = it.getStringExtra(Constants.EXTRA_DIAL_CLOCK_CITY_NAME)
                mDialClockModel.mTimeZone =
                    it.getStringExtra(Constants.EXTRA_DIAL_CLOCK_CITY_TIMEZONE)
                mDialClockModel.mTimeZoneOffsetTime =
                    CityUtils.getTimeZoneOffsetStr(mDialClockModel.mTimeZone, "")
                updateUIData()
            }
        }
    }


    companion object {
        const val FRAGMENT_TAG = "DialClockSettingFragment"
        const val CITY_REQUEST_CODE = 0
        private const val CARDTYPE_SPLIT = "&"
    }

    private fun isInMultiWindowMode(): Boolean {
        return activity?.run { isInMultiWindowMode } ?: false
    }

    private fun setViewTopMargin(view: View?, id: Int) {
        view?.run {
            context?.run {
                resources.let {
                    (layoutParams as LinearLayout.LayoutParams).run {
                        topMargin = it.getDimensionPixelOffset(id)
                    }
                }
            }
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.coui_color_background_elevatedWithCard
                )
            )
    }
}