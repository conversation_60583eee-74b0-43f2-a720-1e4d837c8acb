/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockWidgetProvider.kt.java
 ** Description:负一屏卡片相关显示与数据刷新类
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop

import android.content.Context
import com.oplus.clock.common.utils.Log
import com.coloros.refusedesktop.viewmodel.DialClockDataPacker
import com.coloros.refusedesktop.model.DialClockModel
import com.coloros.refusedesktop.viewmodel.DialClockViewModel
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.oplus.cardwidget.util.getCardId
import com.oplus.cardwidget.util.getCardType
import com.oplus.cardwidget.util.getHostId

class DialClockWidgetProvider : AppCardWidgetProvider() {

    private var dialClockModel = DialClockModel()

    override fun getCardLayoutName(widgetCode: String): String {
        Log.d(TAG, "getCardLayoutName widgetCode: $widgetCode")
        return if (DeviceUtils.isWPlusPhone()) {
            Constants.WPLUS_DIAL_CLOCK_JSON_FILE
        } else {
            Constants.DIAL_CLOCK_JSON_FILE
        }
    }

    override fun onResume(context: Context, widgetCode: String) {
        Log.d(TAG, "onResume widgetCode: $widgetCode")
        DialClockViewModel.saveDialClockWidgetCode(context, widgetCode);

        //step1:在这里准备的你的业务数据，本地读取或者服务端拉取
        dialClockModel = DialClockViewModel.getDialClockData(context, widgetCode.getCardType(), widgetCode.getCardId(), widgetCode.getHostId())
        Log.d(TAG, "onResume dialClockModel=$dialClockModel")

        //step2:准备好数据后，自己在将数据post到远程进行刷新
        CardWidgetAction.postUpdateCommand(context, DialClockDataPacker(dialClockModel), widgetCode)
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        Log.d(TAG, "unSubscribed widgetCode : $widgetCode")
        DialClockViewModel.clearCardData(context, widgetCode.getCardType(), widgetCode.getCardId(), widgetCode.getHostId())
    }

    override fun onDestroy(context: Context, widgetCode: String) {
        super.onDestroy(context, widgetCode)
        Log.d(TAG, "onDestroy widgetCode : $widgetCode")
    }

    companion object {
        private const val TAG = "DialClockWidgetProvider"
    }
}