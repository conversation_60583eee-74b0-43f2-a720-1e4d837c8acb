/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockSettingActivity.kt.java
 ** Description:表盘时钟设置相关ACTIVITY
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop

import android.graphics.Color
import android.os.Build
import android.os.Bundle
import androidx.annotation.RequiresApi
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.globalclock.AddCityBottomSheetFragment
import com.oplus.clock.common.utils.Log
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment

class DialClockSettingActivity : BaseActivity() {

    private var mColorBottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    override fun onCreate(savedInstanceState: Bundle?) {
        setSupportActionBar(null)
        super.onCreate(savedInstanceState)
        window.navigationBarColor = Color.parseColor("#FAFAFA")
        Log.d(TAG, "onCreate")
        val dialClockSettingFragment = DialClockSettingFragment()
        dialClockSettingFragment.arguments = intent.extras
        showPanelFragment(dialClockSettingFragment)

    }

    private fun showPanelFragment(panelFragment: COUIPanelFragment) {
        val fragment =
            supportFragmentManager.findFragmentByTag(DialClockSettingFragment.FRAGMENT_TAG)
        mColorBottomSheetDialogFragment = if (fragment is AddCityBottomSheetFragment) {
            fragment
        } else {
            AddCityBottomSheetFragment().apply {
                arguments = Bundle().apply {
                    putBoolean(AddCityBottomSheetFragment.FROM_DIAL_CLOCK_SETTING, true)
                }
            }
        }
        mColorBottomSheetDialogFragment?.apply {
            setMainPanelFragment(panelFragment)
            show(supportFragmentManager, DialClockSettingFragment.FRAGMENT_TAG)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        mColorBottomSheetDialogFragment?.dismiss()
    }

    companion object {
        private const val TAG = "DialClockSettingActivity"
    }

}