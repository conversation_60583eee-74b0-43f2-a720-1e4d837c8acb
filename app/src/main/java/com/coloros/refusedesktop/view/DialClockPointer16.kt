/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - Dial<PERSON>lockPointer16.kt
 ** Description:指针View
 **
 ** Version: 1.0
 ** Date: 2025-06-03
 ** Author: W9002127
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** W9002127                       2025-06-03      1.0     Create this module
 ********************************************************************************/
@file:Suppress("MagicNumber", "MaximumLineLength")

package com.coloros.refusedesktop.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PaintFlagsDrawFilter
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import com.oplus.alarmclock.R
import kotlin.properties.Delegates

class DialClockPointer16 : DialClockBasePointer {

    private var mHourPointerShadowOffsetLength by Delegates.notNull<Float>()
    private var mHourPointerOffsetLength by Delegates.notNull<Float>()
    private var mMinutePointerOffsetLength by Delegates.notNull<Float>()
    private var mMinutePointerShadowOffsetLength by Delegates.notNull<Float>()
    private var mSecondPointerShadowEndWidth by Delegates.notNull<Float>()

    private var mPointerShadowOffsetLength by Delegates.notNull<Float>()

    private var mAxisCircleColor by Delegates.notNull<Int>()

    private val mPointerPaint by lazy { Paint(Paint.ANTI_ALIAS_FLAG) }
    private val mAxisPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = mAxisCircleColor
        }
    }
    private val mDrawFilter =
        PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

    private var mHourBitmap: Bitmap? = null
    private var mHourDarkBitmap: Bitmap? = null
    private var mHourShadowBitmap: Bitmap? = null
    private var mHourDarkShadowBitmap: Bitmap? = null
    private var mMinuteBitmap: Bitmap? = null
    private var mMinuteDarkBitmap: Bitmap? = null
    private var mMinuteShadowBitmap: Bitmap? = null
    private var mMinuteDarkShadowBitmap: Bitmap? = null
    private var mSecondBitmap: Bitmap? = null
    private var mSecondDarkBitmap: Bitmap? = null
    private var mSecondShadowBitmap: Bitmap? = null
    private var mSecondDarkShadowBitmap: Bitmap? = null
    private val mHourDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_hour_pointer)
    }
    private val mHourDarkDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_hour_dark_pointer)
    }
    private val mMinuteDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_minute_pointer)
    }
    private val mMinuteDarkDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_minute_dark_pointer)
    }
    private val mSecondDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_second_pointer)?.apply {
            setTint(mSecondPointerColor)
        }
    }
    private val mSecondDarkDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_second_dark_pointer)?.apply {
            setTint(mSecondPointerColor)
        }
    }
    private val mHourShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_hour_pointer_dark_shadow)
    }
    private val mHourDarkShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_hour_pointer_dark_shadow)
    }
    private val mMinuteShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_minute_pointer_dark_shadow)
    }
    private val mMinuteDarkShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_minute_pointer_dark_shadow)
    }
    private val mSecondShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_second_pointer_dark_shadow)?.apply {
            setTint(mSecondPointerColor)
        }
    }
    private val mSecondDarkShadowDrawable by lazy {
        AppCompatResources.getDrawable(context, R.drawable.ic_dial_clock_second_pointer_dark_shadow)?.apply {
            setTint(mSecondPointerColor)
        }
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        context.resources?.apply {
            mHourPointerOffsetLength =
                getDimension(R.dimen.dial_clock_hour_pointer_offset_length)
            mHourPointerShadowOffsetLength =
                getDimension(R.dimen.dial_clock_hour_pointer_shadow_offset_length)
            mMinutePointerOffsetLength =
                getDimension(R.dimen.dial_clock_minute_pointer_offset_length)
            mMinutePointerShadowOffsetLength =
                getDimension(R.dimen.dial_clock_minute_pointer_shadow_offset_length)
            mSecondPointerEndWidth =
                getDimension(R.dimen.dial_clock_second_pointer_end_width_16)
            mSecondPointerShadowEndWidth =
                getDimension(R.dimen.dial_clock_second_pointer_shadow_offset_length)
            mPointerShadowOffsetLength =
                getDimension(R.dimen.dial_clock_pointer_shadow_offset_length)
            mPointerCircleWidth =
                getDimension(R.dimen.dial_clock_pointer_circle_width_16)
            mAxisCircleColor = getColor(R.color.dial_clock_axis_circle_color, null)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mHourBitmap = drawableToBitmap(mHourDrawable)
        mHourDarkBitmap = drawableToBitmap(mHourDarkDrawable)
        mMinuteBitmap = drawableToBitmap(mMinuteDrawable)
        mMinuteDarkBitmap = drawableToBitmap(mMinuteDarkDrawable)
        mSecondBitmap = drawableToBitmap(mSecondDrawable)
        mSecondDarkBitmap = drawableToBitmap(mSecondDarkDrawable)
        mHourShadowBitmap = drawableToBitmap(mHourShadowDrawable)
        mHourDarkShadowBitmap = drawableToBitmap(mHourDarkShadowDrawable)
        mMinuteShadowBitmap = drawableToBitmap(mMinuteShadowDrawable)
        mMinuteDarkShadowBitmap = drawableToBitmap(mMinuteDarkShadowDrawable)
        mSecondShadowBitmap = drawableToBitmap(mSecondShadowDrawable)
        mSecondDarkShadowBitmap = drawableToBitmap(mSecondDarkShadowDrawable)
    }

    override fun drawHourPointer(canvas: Canvas) {
        val shadowBitmap = if (mIsDark) {
            mHourDarkShadowBitmap
        } else {
            mHourShadowBitmap
        }
        val pointerBitmap = if (mIsDark) {
            mHourDarkBitmap
        } else {
            mHourBitmap
        }
        //计算旋转的角度 时针旋转一圈后求余复位
        val rotateDegree =
            (mDialClockModel.mHour + mDialClockModel.mMinute / TIME_UNIT) * PER_HOUR_DEGREE
        canvas.run {
            drawPointerShadow(this, shadowBitmap, rotateDegree, mHourPointerShadowOffsetLength)
            drawPointer(this, pointerBitmap, rotateDegree, mHourPointerOffsetLength)
        }
    }

    override fun drawMinutePointer(canvas: Canvas) {
        val shadowBitmap = if (mIsDark) {
            mMinuteDarkShadowBitmap
        } else {
            mMinuteShadowBitmap
        }
        val pointerBitmap = if (mIsDark) {
            mMinuteDarkBitmap
        } else {
            mMinuteBitmap
        }
        //计算旋转的角度 分针旋转一圈后求余复位
        val rotateDegree =
            (mDialClockModel.mMinute + mDialClockModel.mSecond / TIME_UNIT) * PER_MINUTE_DEGREE
        //绘制分针
        canvas.run {
            drawPointerShadow(
                this,
                shadowBitmap,
                rotateDegree,
                mMinutePointerShadowOffsetLength
            )
            drawPointer(this, pointerBitmap, rotateDegree, mMinutePointerOffsetLength)
        }
    }

    override fun drawSecondPointer(canvas: Canvas) {
        val shadowBitmap = if (mIsDark) {
            mSecondDarkShadowBitmap
        } else {
            mSecondShadowBitmap
        }
        val pointerBitmap = if (mIsDark) {
            mSecondDarkBitmap
        } else {
            mSecondBitmap
        }
        //计算旋转的角度 秒针旋转一圈后求余复位
        val rotateDegree: Float =
            (mDialClockModel.mSecond * 1000 + mDialClockModel.mMilliSecond) * PER_SECOND_DEGREE
        //绘制秒针
        canvas.run {
            drawPointerShadow(this, shadowBitmap, rotateDegree, mSecondPointerShadowEndWidth)
            drawPointer(this, pointerBitmap, rotateDegree, mSecondPointerEndWidth)
        }
    }

    override fun drawPointerMidCap(canvas: Canvas) {
    }

    override fun drawSecondPointerMidCap(canvas: Canvas) {
        canvas.drawCircle(
            mCenterPoint.x,
            mCenterPoint.y,
            mPointerCircleWidth / TWO,
            mAxisPaint
        )
    }

    /**
     * 绘制指针
     * @param canvas
     * @param bitmap 指针图片
     * @param offsetY Y轴偏移量
     */
    private fun drawPointer(canvas: Canvas, bitmap: Bitmap?, rotateDegree: Float, offsetY: Float) {
        bitmap?.runCatching {
            if (!isRecycled) {
                canvas.save()
                canvas.rotate(rotateDegree, mCenterPoint.x, mCenterPoint.y)
                canvas.drawBitmap(
                    this,
                    mCenterPoint.x - width / 2,
                    mCenterPoint.y - height + offsetY,
                    mPointerPaint
                )
                canvas.restore()
            }
        }
    }

    /**
     * 绘制指针阴影
     * @param canvas
     * @param bitmap 指针阴影图片
     * @param offsetY Y轴偏移量
     */
    private fun drawPointerShadow(
        canvas: Canvas,
        bitmap: Bitmap?,
        rotateDegree: Float,
        offsetY: Float
    ) {
        bitmap?.runCatching {
            if (!isRecycled) {
                canvas.save()
                canvas.rotate(
                    rotateDegree,
                    mCenterPoint.x,
                    mCenterPoint.y + mPointerShadowOffsetLength
                )
                canvas.drawBitmap(
                    this,
                    mCenterPoint.x - width / 2,
                    mCenterPoint.y + mPointerShadowOffsetLength - height + offsetY,
                    mPointerPaint
                )
                canvas.restore()
            }
        }
    }

    private fun drawableToBitmap(drawable: Drawable?, w: Int? = null, h: Int? = null): Bitmap? {
        if (drawable == null) {
            return null
        }

        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight

        if (width <= 0 || height <= 0) {
            return null
        }
        val targetWidth = w ?: (width).toInt()
        val targetHeight = h ?: (height).toInt()
        // Create a Bitmap with the same dimensions as the Drawable
        val bitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // Enable anti-aliasing using PaintFlagsDrawFilter
        canvas.drawFilter = mDrawFilter

        // Set the bounds of the Drawable to match the Bitmap
        drawable.setBounds(0, 0, targetWidth, targetHeight)

        // Draw the Drawable onto the Canvas
        drawable.draw(canvas)

        return bitmap
    }
}