/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - Dial<PERSON>lockBasePointer.kt
 ** Description:指针BaseView
 **
 ** Version: 1.0
 ** Date: 2024-08-29
 ** Author: W9002127
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** W9002127                         2024-08-29          1.0     Create this module
 ********************************************************************************/
@file:Suppress("MagicNumber", "MaximumLineLength")

package com.coloros.refusedesktop.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import com.coloros.refusedesktop.model.DialClockModel
import com.coloros.refusedesktop.viewmodel.DialClockViewModel
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.AppFeatureUtils
import kotlin.properties.Delegates

abstract class DialClockBasePointer : View {
    protected var mDialClockHourPointerColor by Delegates.notNull<Int>()
    protected var mDialClockHourPointerColorNight by Delegates.notNull<Int>()

    protected var mDialClockMinutePointerColor by Delegates.notNull<Int>()
    protected var mDialClockMinutePointerColorNight by Delegates.notNull<Int>()
    protected var mDialClockMinutePointerStartColor by Delegates.notNull<Int>()
    protected var mDialClockMinutePointerEndColor by Delegates.notNull<Int>()

    protected var mDialClockPointShaderColor by Delegates.notNull<Int>()

    protected var mSecondPointerColor by Delegates.notNull<Int>()

    protected var mHourPointerLength by Delegates.notNull<Float>()
    protected var mHourPointerWidth by Delegates.notNull<Float>()
    protected var mMinutePointerLength by Delegates.notNull<Float>()
    protected var mMinutePointerWidth by Delegates.notNull<Float>()
    protected var mSecondPointerLength by Delegates.notNull<Float>()
    protected var mSecondPointerWidth by Delegates.notNull<Float>()
    protected var mSecondPointerEndWidth by Delegates.notNull<Float>()
    protected var mPointShaderX by Delegates.notNull<Float>()
    protected var mPointShaderY by Delegates.notNull<Float>()
    protected var mPointShaderB by Delegates.notNull<Float>()
    protected var mPointerCircleWidth by Delegates.notNull<Float>()

    protected var mIsDark = false //是否是亮色还是夜色
    protected lateinit var mCenterPoint: PointF
    protected var mDialClockModel = DialClockModel()

    //时指针画笔
    protected val mHourPointerBrightPaint by lazy {
        getPointerPaint(mDialClockHourPointerColor, mHourPointerWidth)
    }

    //暗色模式时指针画笔
    protected val mHourPointerDarkPaint by lazy {
        getPointerPaint(mDialClockHourPointerColorNight, mHourPointerWidth)
    }

    //分指针画笔
    protected val mMinutePointerBrightPaint by lazy {
        getPointerPaint(mDialClockMinutePointerColor, mMinutePointerWidth, mMinutePointerLength, mDialClockMinutePointerStartColor, mDialClockMinutePointerEndColor)
    }

    //暗色模式分指针画笔
    protected val mMinutePointerDarkPaint by lazy {
        getPointerPaint(mDialClockMinutePointerColorNight, mMinutePointerWidth)
    }

    //秒指针画笔
    protected val mSecondPointerPaint by lazy {
        getPointerPaint(mSecondPointerColor, mSecondPointerWidth)
    }

    //指针点画笔
    protected val mPointerMidCapBrightPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = mDialClockMinutePointerColor
        }
    }

    //暗色模式指针点画笔
    protected val mPointerMidCapDarkPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = mDialClockMinutePointerColorNight
        }
    }

    //秒点画笔
    protected val mSecondPointerMidCapPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = mSecondPointerColor
        }
    }



    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        context.resources?.apply {
            mHourPointerWidth = getDimension(R.dimen.dial_clock_hour_pointer_width_15)
            mHourPointerLength = getDimension(R.dimen.dial_clock_hour_pointer_length_15)
            mMinutePointerWidth = getDimension(R.dimen.dial_clock_minute_pointer_width_15)
            mMinutePointerLength = getDimension(R.dimen.dial_clock_minute_pointer_length_15)
            mSecondPointerWidth = getDimension(R.dimen.dial_clock_second_pointer_width_15)
            mSecondPointerLength = getDimension(R.dimen.dial_clock_second_pointer_length_15)
            mSecondPointerEndWidth = getDimension(R.dimen.dial_clock_second_pointer_end_width_15)
            mPointShaderX = getDimension(R.dimen.dial_clock_pointer_shader_x)
            mPointShaderY = getDimension(R.dimen.dial_clock_pointer_shader_y)
            mPointShaderB = getDimension(R.dimen.dial_clock_pointer_shader_b)
            mPointerCircleWidth = getDimension(R.dimen.dial_clock_pointer_circle_width_15)

            mDialClockHourPointerColor = getColor(R.color.dial_clock_hour_pointer_color_15)
            mDialClockHourPointerColorNight = getColor(R.color.dial_clock_hour_pointer_color_night_15)

            mDialClockMinutePointerColor = getColor(R.color.dial_clock_minute_pointer_color_15)
            mDialClockMinutePointerColorNight = getColor(R.color.dial_clock_minute_pointer_color_night_15)
            mDialClockMinutePointerStartColor = getColor(R.color.dial_clock_minute_pointer_start_color)
            mDialClockMinutePointerEndColor = getColor(R.color.dial_clock_minute_pointer_end_color)

            mDialClockPointShaderColor = getColor(R.color.dial_clock_pointer_shader_color)
        }

        setLayerType(LAYER_TYPE_SOFTWARE, null)
        //只在切换场景时获取全局颜色值和是否为暗色模式
        mSecondPointerColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary)
        adapterDarkColor()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        //设置中心点坐标
        mCenterPoint = PointF((w / TWO), (h / TWO))
    }

    override fun onDraw(canvas: Canvas) {
        drawHourPointer(canvas)
        drawMinutePointer(canvas)
        drawPointerMidCap(canvas)
        drawSecondPointer(canvas)
        drawSecondPointerMidCap(canvas)
    }

    /**
     * draw hour pointer
     */
    abstract fun drawHourPointer(canvas: Canvas)

    /**
     * draw minute pointer
     */
    abstract fun drawMinutePointer(canvas: Canvas)

    /**
     * draw second pointer
     */
    abstract fun drawSecondPointer(canvas: Canvas)

    /**
     * draw pointer midcap
     */
    abstract fun drawPointerMidCap(canvas: Canvas)

    /**
     * draw second pointer midcap
     */
    abstract fun drawSecondPointerMidCap(canvas: Canvas)

    fun setData(dialClockModel: DialClockModel) {
        this.mDialClockModel = dialClockModel
        adapterDarkColor()
        postInvalidateOnAnimation()
    }

    /**
     * 获取当前显示颜色（亮色，暗色）
     * 1.创建view的时候需要
     * 2.切换城市的时候需要
     * 3.时间到点时需要
     */
    private fun adapterDarkColor() {
        mIsDark = DialClockViewModel.judgeShowDarkColor(mDialClockModel)
    }

    protected fun getPointerPaint(color: Int, width: Float, length: Float = 0F, startColor: Int = -1, endColor: Int = -1): Paint {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.isAntiAlias = true
        paint.style = Paint.Style.FILL_AND_STROKE
        paint.color = color
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = width
        if (!AppFeatureUtils.isLightOS()) {
            if (length > 0) {
                paint.shader = LinearGradient(
                    mCenterPoint.x,
                    mCenterPoint.y,
                    mCenterPoint.x,
                    mCenterPoint.y - length,
                    startColor,
                    endColor,
                    Shader.TileMode.CLAMP
                )
            }
            paint.setShadowLayer(
                mPointShaderB,
                mPointShaderX,
                mPointShaderY,
                mDialClockPointShaderColor
            )
        }
        return paint
    }

    fun update() {
        DialClockViewModel.getCurrentTime(mDialClockModel)
        //增加状态改变时才去给颜色赋值
        if (mIsDark != DialClockViewModel.judgeShowDarkColor(mDialClockModel)) {
            adapterDarkColor()
        }
        contentDescription = context.resources.getString(
            R.string.hours, mDialClockModel.mHour.toString()
        ) + context.resources.getString(
            R.string.minutes, mDialClockModel.mMinute.toString()
        ) + mDialClockModel.mCityName + mDialClockModel.mTimeZoneOffsetTime
        postInvalidateOnAnimation()
    }

    companion object {
        private const val TAG = "DialClockView"
        const val TWO = 2.0f
        const val PER_SECOND_DEGREE = 360 / 12f / 5 / 1000 //一个圆周60刻度 每秒旋转6度
        const val PER_MINUTE_DEGREE = 360 / 12f / 5 //1刻度 每分钟旋转6度
        const val PER_HOUR_DEGREE = 360 / 12f  //5刻度 每小时旋转30度
        const val TIME_UNIT = 60f //1小时60分钟，1分钟60秒
    }
}