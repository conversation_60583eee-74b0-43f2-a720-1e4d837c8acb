/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockView.kt.java
 ** Description:表盘时钟自定义View
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
@file:Suppress("MagicNumber", "MaximumLineLength")

package com.coloros.refusedesktop.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.coloros.refusedesktop.model.DialClockModel
import com.coloros.refusedesktop.viewmodel.DialClockViewModel
import com.oplus.clock.common.utils.Log
import java.util.Observer

abstract class DialClockBaseView : FrameLayout, Observer {
    protected var mDialClockModel = DialClockModel()
    constructor(context: Context) : super(context) {
        init(context)
    }
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init(context)
    }

    private fun init(context: Context) {
        initView(context)
    }

    fun setData(dialClockModel: DialClockModel) {
        this.mDialClockModel = dialClockModel
        DialClockViewModel.getCurrentTime(mDialClockModel)
        Log.d(TAG, "setData() mDialClockModel=$mDialClockModel")
        setData()
    }

    /**
     * init view
     */
    protected abstract fun initView(context: Context)

    /**
     * set data
     */
    protected abstract fun setData()

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Log.d(TAG, "onAttachedToWindow")
        DialClockViewModel.startTimerUpdate(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.d(TAG, "onDetachedFromWindow")
        DialClockViewModel.releaseTimer(this)
    }

    companion object {
        private const val TAG = "DialClockBaseView"
    }
}