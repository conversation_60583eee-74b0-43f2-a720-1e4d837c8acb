/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockTable15.kt
 ** Description:表盘View
 **
 ** Version: 1.0
 ** Date: 2024-08-29
 ** Author: W9002127
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** W9002127                         2024-08-29          1.0     Create this module
 ********************************************************************************/
@file:Suppress("MagicNumber", "MaximumLineLength")

package com.coloros.refusedesktop.view

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import com.oplus.alarmclock.R

class DialClockTable15 : DialClockBaseTable {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        context.resources?.apply {
            mRectBgRadius = getDimension(R.dimen.dial_clock_rectBgRadius_14)
            mRectBgShaderRadius = getDimension(R.dimen.dial_clock_rectBg_shader_Radius)

            mNumberToCircleBordSize = getDimension(R.dimen.dial_clock_number_to_circle_bord_size_15)
            mScaleToCircleBordSize =
                getDimension(R.dimen.dial_clock_long_scale_to_circle_bord_size_15)
            mCityToCenterSize = getDimension(R.dimen.dial_clock_city_to_center_size_15)
            mTimeZoneToCenterSize = getDimension(R.dimen.dial_clock_timezone_to_center_size)

            mCityTextSize = getDimension(R.dimen.dial_clock_city_text_size_15)
            mScaleTextSize = getDimension(R.dimen.dial_clock_scale_text_size_15)

            mLongScaleWidth = getDimension(R.dimen.dial_clock_long_scale_width_15)
            mLongScaleLenth = getDimension(R.dimen.dial_clock_long_scale_lenth_15)
            mShortScaleWidth = getDimension(R.dimen.dial_clock_short_scale_width_15)
            mShortScaleLenth = getDimension(R.dimen.dial_clock_short_scale_lenth_15)

            mDialClockRectBgColor = getColor(R.color.dial_clock_rect_bg_color_13)
            mDialLinearGradientStartColor =
                getColor(R.color.dial_clock_lineargradient_start_color_15)
            mDialLinearGradientEndColor = getColor(R.color.dial_clock_lineargradient_end_color_15)

            mDialClockCircleBgColor = getColor(R.color.dial_clock_circle_bg_color_15)
            mCircleBgStartColorNight = getColor(R.color.dial_clock_circle_bg_start_color_15)
            mCircleBgEndColorNight = getColor(R.color.dial_clock_circle_bg_end_color_15)

            mDialClockHourTextColor = getColor(R.color.dial_clock_hour_text_color_15)
            mDialClockHourTextColorNight = getColor(R.color.dial_clock_hour_text_color_night_14)

            mDialClockCityTextColor = getColor(R.color.dial_clock_city_text_color_15)
            mDialClockCityTextColorNight = getColor(R.color.dial_clock_city_text_color_night_15)

            mDialClockTimeZoneTextColor = getColor(R.color.dial_clock_time_zone_text_color_15)
            mDialClockTimeZoneTextColorNight =
                getColor(R.color.dial_clock_time_zone_text_color_night)

            mDialClockLongScaleColor = getColor(R.color.dial_clock_long_scale_color)
            mDialClockLongScaleColorNight = getColor(R.color.dial_clock_long_scale_color_night)

            mDialClockShortScaleColor = getColor(R.color.dial_clock_short_scale_color_15)
            mDialClockShortScaleColorNight = getColor(R.color.dial_clock_short_scale_color_night_15)
        }
    }

    override fun getCircleRectCoefficient(): Float {
        return CIRCLE_BY_RECT_COEFFICIENT
    }

    override fun getNumberScaleFontWeight(): Int {
        return WEIGHT_MAX
    }

    override fun drawBackground(canvas: Canvas) {
        val mCircleBgPaint = if (mIsDark) {
            mCircleDarkLinearGradientBgPaint
        } else {
            mCircleBrightBgPaint
        }
        canvas.drawRoundRect(mRectBg, mRectBgRadius, mRectBgRadius, mRectBrightPaint)
        canvas.drawCircle(mCenterPoint.x, mCenterPoint.y, mCircleBgRadius, mCircleBgPaint)
    }

    override fun drawScale(canvas: Canvas) {
        for (i in 0 until CIRCLE_SCALE_COUNT) {
            canvas.save()
            //以画布为中心旋转
            canvas.rotate(
                i * (CIRCLE_WEEK_DEGREE / CIRCLE_SCALE_COUNT), mCenterPoint.x, mCenterPoint.y
            )
            //数字刻度/加粗刻度，从正上方0（也是12）开始，每隔5都是数字
            val isNumberScale = i % FIVE == 0
            if (isNumberScale) {
                val mLongScalePaint = if (mIsDark) {
                    mLongScaleDarkPaint
                } else {
                    mLongScaleBrightPaint
                }
                canvas.drawLine(
                    mCenterPoint.x,
                    mScaleTopY,
                    mCenterPoint.x,
                    mScaleTopY + mLongScaleLenth,
                    mLongScalePaint
                )
                //画数字  是数字刻度的时候，一定是5的倍数，获取这个倍数，从集合里取当前数值
                val numberIndex: Int = i / FIVE
                val mNumScalePaint = if (mIsDark) {
                    mNumScaleDarkPaint
                } else {
                    mNumScaleBrightPaint
                }
                val textBound = getTextBound(HOUR_TEXT_LIST[numberIndex].toString(), mNumScalePaint)
                canvas.rotate(
                    -i * (CIRCLE_WEEK_DEGREE / CIRCLE_SCALE_COUNT),
                    mCenterPoint.x,
                    mNumberTopY + textBound.height() / TWO
                )
                canvas.drawText(
                    HOUR_TEXT_LIST[numberIndex].toString(),
                    mFirstTextMidX - textBound.width() / TWO,
                    mNumberTopY + textBound.height(),
                    mNumScalePaint
                )
            } else {
                val mShortScalePaint = if (mIsDark) {
                    mShortScaleDarkPaint
                } else {
                    mShortScaleBrightPaint
                }
                canvas.drawLine(
                    mCenterPoint.x,
                    mScaleTopY,
                    mCenterPoint.x,
                    mScaleTopY + mShortScaleLenth,
                    mShortScalePaint
                )
            }
            canvas.restore()
        }
    }

    override fun drawCityText(canvas: Canvas) {
        mDialClockModel.apply {
            mCityName?.let {
                val result = getAdapterSizeText(it)
                val mCityTextPaint = if (mIsDark) {
                    mCityTextDarkPaint
                } else {
                    mCityTextBrightPaint
                }
                val cityTextBound = getTextBound(result, mCityTextPaint)
                canvas.drawText(
                    result,
                    mCenterPoint.x - cityTextBound.width() / TWO,
                    mCenterPoint.y - mCityToCenterSize,
                    mCityTextPaint
                )
            }
            mTimeZoneOffsetTime?.let {
                val mTimeZoneTextPaint = if (mIsDark) {
                    mTimeZoneTextDarkPaint
                } else {
                    mTimeZoneTextBrightPaint
                }
                val cityOffsetTimeTextBound = getTextBound(it, mTimeZoneTextPaint)
                canvas.drawText(
                    it,
                    mCenterPoint.x - cityOffsetTimeTextBound.width() / TWO,
                    mCenterPoint.y + mTimeZoneToCenterSize + cityOffsetTimeTextBound.height(),
                    mTimeZoneTextPaint
                )
            }
        }
    }

    companion object {
        private const val TAG = "DialClockTable15"
        const val CIRCLE_BY_RECT_COEFFICIENT = 0.838f //根据设计图：圆宽度124/矩形宽度148=系数0.833
    }
}