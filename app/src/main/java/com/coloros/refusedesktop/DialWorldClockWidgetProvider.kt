/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockWidgetProvider.kt.java
 ** Description:负一屏卡片相关显示与数据刷新类
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop

import android.content.Context
import com.oplus.clock.common.utils.Log
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.coloros.refusedesktop.model.DialClockModel
import com.coloros.refusedesktop.viewmodel.DialClockViewModel
import com.coloros.refusedesktop.viewmodel.DialWorldClockDataPacker
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.cardwidget.util.getCardId
import com.oplus.cardwidget.util.getCardType
import com.oplus.cardwidget.util.getHostId

class DialWorldClockWidgetProvider : AppCardWidgetProvider() {

    private var dialClockModelList = ArrayList<DialClockModel>()

    override fun getCardLayoutName(widgetCode: String): String {
        Log.i(TAG, "getCardLayoutName widgetCode: $widgetCode")
        return  if (DeviceUtils.isWPlusPhone()) {
            Constants.WPLUS_WORLD_CLOCK_JSON_FILE
        } else {
            Constants.DIAL_WORLD_CLOCK_JSON_FILE
        }
    }

    override fun onResume(context: Context, widgetCode: String) {
        Log.i(TAG, "onResume widgetCode: $widgetCode")
        DialClockViewModel.saveDialClockWidgetCode(context, widgetCode)

        //step1:在这里准备的你的业务数据，本地读取或者服务端拉取
        dialClockModelList = DialClockViewModel.getDialWorldClockData(context)
        Log.i(TAG, "onResume dialClockModelList=$dialClockModelList")

        //step2:准备好数据后，自己在将数据post到远程进行刷新
        CardWidgetAction.postUpdateCommand(
            context,
            DialWorldClockDataPacker(dialClockModelList),
            widgetCode
        )
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        Log.i(TAG, "unSubscribed widgetCode : $widgetCode")
        DialClockViewModel.clearCardData(context, widgetCode.getCardType(), widgetCode.getCardId(), widgetCode.getHostId())
    }

    override fun onDestroy(context: Context, widgetCode: String) {
        super.onDestroy(context, widgetCode)
        Log.i(TAG, "onDestroy widgetCode : $widgetCode")
    }

    companion object {
        private const val TAG = "DialWorldClockWidgetProvider"
    }
}