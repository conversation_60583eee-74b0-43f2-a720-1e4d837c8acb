/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - DialClockModel.kt.java
 ** Description:表盘时钟实体类和View创建
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.coloros.refusedesktop.model

import android.graphics.Color
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.utils.DeviceUtils
import java.util.*

class DialClockModel {

    var mCityId: String? = "" //城市Id
    var mLocale: Locale? = null
    var mCityName: String? = ""//城市名
    var mTimeZone: String? = ""//时区
    var mTimeZoneOffsetTime: String? = ""//晚几个小时
    var mChangeValue: String? = "" //这个值是一个动态值用于刷新界面
    var mCardType = -1 //卡片类型
    var mCardID = -1 //卡片Id，默认为1，当重复添加卡片时，Id递增
    var mHostId = -1 //0：添加在负一屏的卡片；1：添加在桌面的卡片
    var mThemeColor = Color.GREEN
    var mHour = 0
    var mMinute = 0
    var mSecond = 0
    var mMilliSecond = 0

    override fun toString(): String {

        return "mCityId=$mCityId mLocale=$mLocale mCityName=${
            if (DeviceUtils.isExpVersion(
                    AlarmClockApplication.getInstance()
                )
            ) "" else mCityName
        } mChangeValue=$mChangeValue " +
                "mTimeZoneOffsetTime=$mTimeZoneOffsetTime mTimeZone=$mTimeZone mCardType=$mCardType mCardID=$mCardID mHostId=$mHostId " +
                "mThemeColor=${Integer.toHexString(mThemeColor)}"
    }
}