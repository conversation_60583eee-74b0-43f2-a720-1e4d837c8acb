/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :IClockUpdateAidlInterface
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.coloros.alarmclock;

// Declare any non-default types here with import statements
//import com.coloros.alarmclock.aidl.PlatformClockInfo;
import com.coloros.alarmclock.PlatformClockInfo;
interface IClockUpdateAidlInterface {

   void dismissClock(long scheduleId);

   void snoozeClock(long scheduleId);

   void alarmClockRing(in PlatformClockInfo platformClockInfo);

   boolean isBindAlarmClock();

   String getChannelName();

   boolean getListenerIsNull();

   void onDataChanged(int action, int enableAssociate, long alarmId);
}
