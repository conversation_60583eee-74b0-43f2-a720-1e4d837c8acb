package com.coloros.alarmclock.widget;

import android.content.Context;
import android.util.AttributeSet;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.Utils;

public class OplusTimePickerCustomMiniClock extends OplusTimePickerCustomClock{
    public OplusTimePickerCustomMiniClock(Context context) {
        super(context);
    }

    public OplusTimePickerCustomMiniClock(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public OplusTimePickerCustomMiniClock(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.oplus_time_picker_custom_mini;
    }
}
