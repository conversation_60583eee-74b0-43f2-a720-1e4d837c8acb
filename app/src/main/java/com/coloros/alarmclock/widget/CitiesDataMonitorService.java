/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 **
 ** File: CitiesDataMonitorService.java
 ** Description: This class is used for monitoring the time
 *               format and citis data.
 ** Version: V0.1
 ** Date :   2018-09-04
 ** Author:  Liukun
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **   Liukun   2018-09-04     V0.1     Build the module.
 ****************************************************************/
package com.coloros.alarmclock.widget;

import android.annotation.SuppressLint;
import android.app.Service;
import android.appwidget.AppWidgetManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.SystemClock;
import android.provider.Settings;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.clock.common.utils.Log;
import com.coloros.widget.smallweather.ClockWidgetManager;

import java.util.Calendar;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

public class CitiesDataMonitorService extends Service {

    static final String ACTION_UPDATE_CITIES_LIST =
            "com.oplus.alarmclock.widget.UPDATE_CITIES_LIST";

    private static final String TAG = "CitiesDataMonitorService";
    private static final int HOUR_IN_MILLS = 3600 * 1000;
    private static long sEllapsedMills = 0;

    private Handler mHandler;

    private ContentObserver mWeatherChangeObserver;
    private ContentObserver mCitiesChangeObserver;

    private class WeatherChangeObserver extends ContentObserver {

        WeatherChangeObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean selfChange) {
            final Context context = CitiesDataMonitorService.this;
            DigitalAppWidgetProvider.updateWeatherInfoView(context);
        }
    }

    private class CitiesChangeObserver extends ContentObserver {

        CitiesChangeObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean b) {
            super.onChange(b);
            Log.d(TAG, "Cities data changed!");
            updateCitiesList();
        }
    }

    private final Runnable mCityTimeUpdater = new Runnable() {
        @Override
        public void run() {
            updateCitiesList();
            setCityTimeUpdater();
        }
    };

    private BroadcastReceiver mScreenReceiver = new BroadcastReceiver() {
        @RequiresApi(api = Build.VERSION_CODES.Q)
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "ScreenReceiver receive: " + action);
            if (Intent.ACTION_SCREEN_ON.equals(action)) {
                updateCitiesListIfNeed();
                setCityTimeUpdater();
            } else if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                if (mHandler != null) {
                    if (mHandler.hasCallbacks(mCityTimeUpdater)) {
                        mHandler.removeCallbacks(mCityTimeUpdater);
                    }
                }
            }
        }
    };

    @SuppressLint("NewApi")
    private void setCityTimeUpdater() {
        if (mHandler == null) {
            mHandler = new Handler();
        }

        long now = System.currentTimeMillis();
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(now);
        c.add(Calendar.HOUR_OF_DAY, 1);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);

        long delay = c.getTimeInMillis() - now + 2000; /*Delay 2 seconds*/
        Log.d(TAG, "Next hour: " + c + ", delay: " + delay);
        if (mHandler.hasCallbacks(mCityTimeUpdater)) {
            mHandler.removeCallbacks(mCityTimeUpdater);
        }
        mHandler.postDelayed(mCityTimeUpdater, delay);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "onCreate.");
        sEllapsedMills = 0;
        DigitalAppWidgetProvider.updateWeatherInfoView(this);
        registerContentObserver();
        registerWeatherInfoObserver();

        IntentFilter filter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
        filter.addAction(Intent.ACTION_SCREEN_ON);
        registerReceiver(mScreenReceiver, filter, RECEIVER_EXPORTED);

        mHandler = new Handler();
        setCityTimeUpdater();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        String action = (intent != null) ? intent.getAction() : null;
        Log.d(TAG, "onStartCommand: " + action);
        if (ACTION_UPDATE_CITIES_LIST.equals(action)) {
            updateCitiesList();
        } else {
            updateCitiesListIfNeed();
        }
        setCityTimeUpdater();

        if (WidgetUtils.hasAlreadyAddedWorldClockWidget(this)) {
            return START_STICKY;
        } else {
            stopSelf();
            return START_NOT_STICKY;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy.");
        unregisterContentObserver();
        unregisterWeatherInfoObserver();

        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        unregisterReceiver(mScreenReceiver);
        mScreenReceiver = null;
    }

    private void updateCitiesListIfNeed() {
        long ellapsedMills = SystemClock.elapsedRealtime();
        if (ellapsedMills - sEllapsedMills >= HOUR_IN_MILLS) {
            updateCitiesList();
        } else {
            Log.d(TAG, "Has updated in the past one hour, no need to update.");
        }
    }

    private void updateCitiesList() {
        Log.d(TAG, "updateCitiesList.");

        final Context context = this;
        AppWidgetManager appwidgetManager = AppWidgetManager.getInstance(context);
        ComponentName provider = new ComponentName(context, DigitalAppWidgetProvider.class);
        int[] ids = appwidgetManager.getAppWidgetIds(provider);
        appwidgetManager.notifyAppWidgetViewDataChanged(ids, R.id.world_city_list);

        sEllapsedMills = SystemClock.elapsedRealtime();
    }

    private void registerContentObserver() {
        if (mCitiesChangeObserver == null) {
            mCitiesChangeObserver = new CitiesChangeObserver(getMainThreadHandler());
        }
        getContentResolver().registerContentObserver(ClockContract.City.NEW_CITY_CONTENT_URI,
                true, mCitiesChangeObserver);
    }

    private void unregisterContentObserver() {
        if (mCitiesChangeObserver != null) {
            getContentResolver().unregisterContentObserver(mCitiesChangeObserver);
            mCitiesChangeObserver = null;
        }
    }

    private void registerWeatherInfoObserver() {
        if (mWeatherChangeObserver == null) {
            mWeatherChangeObserver = new WeatherChangeObserver(new Handler(Looper.getMainLooper()));
        }
        try {
            getContentResolver().registerContentObserver(
                    Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING), false,
                    mWeatherChangeObserver);
            getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_WEATHER_INFO_URI), false, mWeatherChangeObserver);

            getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_LOCAL_CITY_INFO_URI), false, mWeatherChangeObserver);

            getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_RESIDENT_CITY_INFO_URI), false, mWeatherChangeObserver);
        } catch (Exception e) {
            try {
                getContentResolver().registerContentObserver(
                        Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING_OLD), false,
                        mWeatherChangeObserver);
                getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_WEATHER_INFO_URI_OLD), false, mWeatherChangeObserver);

                getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_LOCAL_CITY_INFO_URI_OLD), false, mWeatherChangeObserver);

                getContentResolver().registerContentObserver(Uri.parse(ClockWidgetManager.OPLUS_NEW_RESIDENT_CITY_INFO_URI_OLD), false, mWeatherChangeObserver);
            } catch (Exception ex) {
                Log.e(TAG, "registerWeatherInfoObserver error:" + e.getMessage());
            }

        }

    }

    private void unregisterWeatherInfoObserver() {
        if (mWeatherChangeObserver != null) {
            getContentResolver().unregisterContentObserver(mWeatherChangeObserver);
            mWeatherChangeObserver = null;
        }
    }

    public Handler getMainThreadHandler() {
       return new Handler(getMainLooper()) { };
    }
}
