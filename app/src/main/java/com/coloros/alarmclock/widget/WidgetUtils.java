/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 **
 ** File: WidgetUtils.java
 ** Description: Helper class.
 ** Version: V0.1
 ** Date :   2018-09-04
 ** Author:  Liukun
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **   Liukun   2018-09-03     V0.1     Build the module.
 ****************************************************************/
package com.coloros.alarmclock.widget;

import android.appwidget.AppWidgetManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.RemoteViews;

import com.coloros.widget.smallweather.ClockWidgetUtils;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.CityUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.os.OplusBuild;
import com.oplus.servicesdk.WeatherRequest;
import com.oplus.weatherservicesdk.BaseCallBack;
import com.oplus.weatherservicesdk.model.SecureSettingsData;
import com.oplus.weatherservicesdk.service.WeatherBaseDataTask;
import com.coloros.widget.smallweather.ClockWidgetManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static android.appwidget.AppWidgetManager.OPTION_APPWIDGET_HOST_CATEGORY;
import static android.appwidget.AppWidgetProviderInfo.WIDGET_CATEGORY_KEYGUARD;
import static com.oplus.utils.CommonUtil.adapterRtlWeatherTxt;

public class WidgetUtils {
    public static final String ACTION_ADD_CITY_BY_WIDGET =
            "com.oplus.alarmclock.ADD_CITY_BY_WIDGET";

    public static final String ACTION_DELETE_WIDGET_OLD = "com.coloros.alarmclock.DELETE_WORLD_CLOCK_WIDGET";
    public static final String ACTION_DELETE_WIDGET = "com.oplus.alarmclock.DELETE_WORLD_CLOCK_WIDGET";
    public static final String METHOD_GET_LOCATION_CITY_WEATHER_INFO = "getLocationSecureSettingsData";

    private static final int COLOR_WHITE = 0;
    private static final int COLOR_DARK = 1;
    private static final int LEN_2 = 2;
    private static final int LEN_3 = 3;
    private static final int INDEX_0 = 0;
    private static final int INDEX_2 = 2;
    private static final int INDEX_3 = 3;
    private static final String TAG = "WidgetUtils";
    private static final boolean DEBUG_WEATHER = false;
    public static HashMap<String, ArrayList<City>> sCityMap = new HashMap<>();
    private static int sCurrentCount = 0;
    private static WeatherBaseDataTask sLocationWeatherBaseDataTask;
    private static String sWeatherInfo;

    static void setCurrentCount(int count) {
        sCurrentCount = count;
    }

    static int getCurrentCount() {
        return sCurrentCount;
    }

    public static void notifyUpdateWidgetCityList(Context context) {
        Log.d(TAG, "notifyUpdateWidgetCityList!");
        if (context == null) {
            Log.d(TAG, "notifyUpdateWidgetCityList context is null");
            return;
        }
        try {
            Intent intent = new Intent(context, CitiesDataMonitorService.class);
            intent.setAction(CitiesDataMonitorService.ACTION_UPDATE_CITIES_LIST);
            context.startService(intent);
        } catch (Exception e) {
            Log.e(TAG, "notifyUpdateWidgetCityList error:" + e);
        }
    }

    public static boolean hasAlreadyAddedWorldClockWidget(Context context) {
        if (context == null) {
            return false;
        }
        boolean result = false;
        try {
            AppWidgetManager wm = AppWidgetManager.getInstance(context);
            final ComponentName provider = new ComponentName(context, DigitalAppWidgetProvider.class);
            final int[] widgetIds = wm.getAppWidgetIds(provider);
            if ((widgetIds != null) && (widgetIds.length > 0)) {
                result = true;
            }
            Log.d(TAG, "hasAlreadyAddedWorldClockWidget " + result);
        } catch (Exception e) {
            Log.d(TAG, "hasAlreadyAddedWorldClockWidget e:" + e.getMessage());
        }
        return result;
    }

    public static boolean needUpdateWidget(Context context) {
        boolean isLocked = Utils.isUserKeyUnlocked(context);
        if (isLocked) {
            return false;
        }

        if (!DeviceUtils.isProvisioned(context)) {
            return false;
        }

        if (Utils.isKeyguardLock(context)) {
            return false;
        }

        if (!WidgetUtils.hasAlreadyAddedWorldClockWidget(context)) {
            return false;
        }

        return true;
    }

    public static boolean isWidgetClickable(AppWidgetManager widgetManager, int widgetId) {
        if (widgetManager == null) {
            return false;
        }
        final Bundle wo = widgetManager.getAppWidgetOptions(widgetId);
        return (wo != null)
                && (wo.getInt(OPTION_APPWIDGET_HOST_CATEGORY, -1) != WIDGET_CATEGORY_KEYGUARD);
    }

    static String getLocalWeatherInfoNew(final Context context) {
        try {
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_GET_LOCATION_CITY_WEATHER_INFO)
                    .setPackageName(ClockConstant.CLOCK_PACKAGE)
                    .setParams(null);
            //maybe async or synchronization,so should execute on a thread
            if (sLocationWeatherBaseDataTask == null) {
                sLocationWeatherBaseDataTask = new WeatherBaseDataTask(SecureSettingsData.class, context,
                        weatherRequest, new BaseCallBack<SecureSettingsData>() {
                    @Override
                    public void onSuccess(SecureSettingsData secureSettingsData) {
                        if ((context == null)
                                || (secureSettingsData == null)
                                || (secureSettingsData.weatherDesc == null)
                                || (secureSettingsData.temp == null)
                                || (secureSettingsData.tempUnit == null)
                        ) {
                            Log.i(TAG, "getLocalWeatherInfoNew is null");
                            //bug1986102  请求返回数据为null，暂时使用旧数据
                            getLocalWeatherInfoOld(context);
                            return;
                        }
                        String temperature
                                = adapterRtlWeatherTxt(context, secureSettingsData.tempUnit, getLocalTempInfo(context, secureSettingsData.temp));
                        String weatherInfo = secureSettingsData.weatherDesc
                                + context.getString(R.string.space)
                                + ClockWidgetManager.WEATHER_SPACE
                                + temperature
                                + ClockWidgetManager.WEATHER_SPACE
                                + context.getString(R.string.space);
                        Log.v(TAG, "getLocalWeatherInfoNew : " + weatherInfo);
                        sendWeatherBroadcast(context, weatherInfo);
                    }

                    @Override
                    public void onFail(String s) {
                        Log.e(TAG, "synNewLocalWeatherInfo,onFail:" + s);
                        sendWeatherBroadcast(context,null);
                    }
                });
            }
            sLocationWeatherBaseDataTask.startServiceRequest();
        } catch (Exception e) {
            Log.e(TAG, "synNewLocalWeatherInfo,Exception:" + e + " applicationContext:" + context.getApplicationContext());
            if (sLocationWeatherBaseDataTask != null) {
                //mLocationWeatherBaseDataTask.unRegisterConnectCallback();
            }
        }
        return null;
    }

    public static String getWeatherInfo() {
        return sWeatherInfo;
    }

    public static void getLocalWeatherInfo(Context context) {
        if (context == null) {
            Log.d(TAG, "getLocalWeatherInfo context is null");
            return;
        }
        /**
         * 这里需要和DigitalAppWidgetProvider的onReceive方法中逻辑同步，如果为锁屏状态，
         * 这时不刷新UI界面则不给内存中的sWeatherInfo赋值，已保证下次能正常更新界面
         */

        if (!needUpdateWidget(context)) {
            Log.d(TAG, "getLocalWeatherInfo No need to update widget.");
            return;
        }
        if (ClockWidgetUtils.commonWeatherServiceExist()) {
            getLocalWeatherInfoNew(context.getApplicationContext());
        } else {
            getLocalWeatherInfoOld(context);
        }
    }

    private static void sendWeatherBroadcast(Context context, String weather) {
        if (context == null) {
            Log.i(TAG, "not sendWeatherBroadcast context is null");
            return;
        }
        if (TextUtils.isEmpty(weather)) {
            Intent widgetReceiver = new Intent(DigitalAppWidgetProvider.ACTION_UPDATE_CITES_DATA);
            widgetReceiver.setPackage(context.getPackageName());
            context.sendBroadcast(widgetReceiver);
            Log.i(TAG, "sendCitesBroadcast");
        } else {
            if (weather.equals(sWeatherInfo)) {
                return;
            }
            sWeatherInfo = weather;
            Intent widgetReceiver = new Intent(DigitalAppWidgetProvider.ACTION_WEATHER_INFO_UPDATE);
            widgetReceiver.setPackage(context.getPackageName());
            context.sendBroadcast(widgetReceiver);
            Log.i(TAG, "sendWeatherBroadcast");
        }
    }

    private static void getLocalWeatherInfoOld(Context context) {
        if (context == null) {
            return;
        }
        String weatherInfo = Settings.Secure.getString(context.getContentResolver(), ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING);
        if (TextUtils.isEmpty(weatherInfo)) {
            weatherInfo = Settings.Secure.getString(context.getContentResolver(), ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING_OLD);
        }
        String weather = "";
        if (!TextUtils.isEmpty(weatherInfo)) {
            // weather info string, like this: 26::4::Showers
            String[] temp = weatherInfo.split("::");
            if (temp.length > LEN_2) {
                weather = trim(temp[INDEX_2], "");
            }

            String degree = "";
            String unit = "";
            if (temp.length > LEN_3) {
                degree = trim(temp[INDEX_0], "");
                unit = trim(temp[INDEX_3], "");
            }

            if (!TextUtils.isEmpty(degree) && !TextUtils.isEmpty(unit)) {
                String temperature = adapterRtlWeatherTxt(context, unit, getLocalTempInfo(context, degree));
                weather = weather + context.getString(R.string.space) + ClockWidgetManager.WEATHER_SPACE + temperature + ClockWidgetManager.WEATHER_SPACE + context.getString(R.string.space);
            }
        } else {
            Log.i(TAG, "getLocalWeatherInfoOld is null");
        }

        Log.v(TAG, "getLocalWeatherInfoOld: [" + weather + "]");
        if (DEBUG_WEATHER) {
            weather = context.getString(R.string.test_weather);
        }
        sendWeatherBroadcast(context, weather);
    }

    private static String getLocalTempInfo(Context context, String degree) {

        Log.i(TAG, "getLocalTempInfo  degree = " + degree);
        String tempDegree = "";
        if (context == null) {
            return degree;
        }

        try {
            int intDegree = Integer.parseInt(degree);
            tempDegree = context.getString(R.string.format_int_to_local, intDegree);
        } catch (Exception e) {
            Log.e(TAG, "getLocalWeatherInfoOld e : " + e.getMessage());
            tempDegree = degree;
        }
        Log.i(TAG, "getLocalTempInfo  temperature = " + tempDegree);
        return tempDegree;
    }

    private static String trim(String val, String dflt) {
        if ((val != null) && (!"null".equals(val))) {
            return val.trim();
        }
        return dflt;
    }

    private static int getCitiesCount(Context context) {
        List<City> list = CityUtils.getAllCities(context);
        return (list != null) ? list.size() : 0;
    }

    public static void asyncRecordWidgetState(final Context context, final String action, final boolean add) {

        final AsyncTask<Void, Void, Integer> loadTask = new AsyncTask<Void, Void, Integer>() {
            @Override
            protected Integer doInBackground(Void... parameters) {
                return getCitiesCount(context);
            }

            @Override
            protected void onPostExecute(Integer count) {
                super.onPostExecute(count);
                String op = (add) ? ClockOplusCSUtils.OP_ADD : ClockOplusCSUtils.OP_DEL;
                HashMap<String, String> map = new HashMap<>();
                map.put(ClockOplusCSUtils.ADD_OR_DEL_WIDGET, op);
                map.put(ClockOplusCSUtils.CITIES_COUNT, String.valueOf(count.intValue()));
                ClockOplusCSUtils.onCommon(context, action, map);
            }
        };
        loadTask.execute();
    }

    public static void asyncRecordCitiesState(final Context context, final int prevCount,
                                              final int curCount) {
        if (prevCount == curCount) {
            return;
        }

        final AsyncTask<Void, Void, Boolean> loadTask = new AsyncTask<Void, Void, Boolean>() {
            @Override
            protected Boolean doInBackground(Void... parameters) {
                return hasAlreadyAddedWorldClockWidget(context);
            }

            @Override
            protected void onPostExecute(Boolean val) {
                super.onPostExecute(val);

                String op = (curCount > prevCount) ? ClockOplusCSUtils.OP_ADD : ClockOplusCSUtils.OP_DEL;
                String exist = (val) ? ClockOplusCSUtils.EXIST : ClockOplusCSUtils.NONE_EXIST;
                HashMap<String, String> map = new HashMap<>();
                map.put(ClockOplusCSUtils.ADD_OR_DEL_CITY, op);
                map.put(ClockOplusCSUtils.CITIES_COUNT, String.valueOf(prevCount));
                map.put(ClockOplusCSUtils.WORLD_CLOCK_WIDGET_EXIST, exist);
                ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.WORLD_CLOCK_CITIES_CHANGED, map);
            }
        };
        loadTask.execute();
    }

    public static void getAppWidgetCityData() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            sCityMap = CityUtils.getAllLanguageCities(AlarmClockApplication.getInstance());
            Intent widgetReceiver = new Intent(DigitalAppWidgetProvider.ACTION_UPDATE_CITES_DATA);
            widgetReceiver.setPackage(AlarmClockApplication.getInstance().getPackageName());
            AlarmClockApplication.getInstance().sendBroadcast(widgetReceiver);
        });
    }

    /**
     * 隐藏刷新按钮，在外销coloros12及一下做兼容，显示刷新按钮
     */
    @SuppressWarnings("ConstantConditions")
    public static Bitmap hideRefreshButton(Context context, RemoteViews remoteView, int degree, int img, int imgParent) {
        if (DeviceUtils.isExpVersion(context) && (OplusBuild.getOplusOSVERSION() < OplusBuild.OsdkVersionCodes.OS_13_0)) {
            ClockWidgetManager instance = ClockWidgetManager.getInstance();
            Bitmap bitmap = instance.getRefreshImageView(context, degree, instance.getRefreshIconColor(), false);
            if (bitmap != null) {
                remoteView.setImageViewBitmap(img, bitmap);
                remoteView.setViewVisibility(imgParent, View.VISIBLE);
            } else {
                remoteView.setViewVisibility(imgParent, View.GONE);
            }
            return bitmap;
        } else {
            remoteView.setViewVisibility(imgParent, View.GONE);
            return null;
        }
    }
}
