/*
 * Copyright (C) 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.coloros.alarmclock.widget;

import static android.content.Intent.ACTION_DATE_CHANGED;
import static android.content.Intent.ACTION_LOCALE_CHANGED;
import static android.content.Intent.ACTION_TIMEZONE_CHANGED;
import static android.content.Intent.ACTION_TIME_CHANGED;
import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.view.View;
import android.widget.RemoteViews;

import androidx.annotation.NonNull;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.AddCityActivity;
import com.oplus.alarmclock.globalclock.AddCityFragment;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.mba.CalendarDisableInfo;
import com.oplus.alarmclock.mba.PackageDisabledManager;
import com.oplus.alarmclock.mba.WeatherDisableInfo;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.coloros.widget.smallweather.ClockWidgetManager;
import com.oplus.utils.CommonUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

public class DigitalAppWidgetProvider extends AppWidgetProvider {


    public static final String ACTION_CITIES_DATA_CHANGED = "com.oplus.alarmclock.action.CITIES_DATA_CHANGED";
    public static final String ACTION_CITIES_DATA_CHANGED_OLD = "com.coloros.alarmclock.action.CITIES_DATA_CHANGED";
    public static final String ACTION_UPDATE_CITES_DATA = "com.oplus.alarmclock.action.ACTION_UPDATE_CITES_DATA";

    // Intent action used for start CitiesDataMonitorService.
    public static final String ACTION_LAUNCHER_VISIBLE = "com.oplus.alarmclock.action.LAUNCHER_VISIBLE";
    public static final String ACTION_WEATHER_INFO_UPDATE = "com.oplus.alarmclock.action.WEATHER_INFO_UPDATE";

    public static final String OPLUS_WEATHER_PKG_NAME = "com.coloros.weather2";
    public static final String OPLUS_WEATHER_PKG_NAME2 = "net.oneplus.weather";
    public static final String OPLUS_CALENDAR_PKG_NAME = "com.coloros.calendar";
    public static final String GOOGLE_CALENDAR_PKG_NAME = "com.google.android.calendar";

    private static final String ACTION_CLICK_WEATHER = "com.oplus.alarmclock.action.CLICK_WEATHER";
    private static final String ACTION_CLICK_CALENDAR = "com.oplus.alarmclock.action.CLICK_CALENDAR";
    private static final String ACTION_CLICK_TIME = "com.oplus.alarmclock.action.CLICK_TIME";
    private static final String ACTION_VIEW_WORLD_CLOCK = "com.oplus.alarmclock.action.VIEW_WORLD_CLOCK";

    private static final String TAG = "DigitalWidgetProvider";

    private static final String ACTION_GO_TO_ADD_CITY = "com.oplus.alarmclock.action.GO_TO_ADD_CITY";

    @Override
    public void onEnabled(Context context) {
        super.onEnabled(context);

        Log.d(TAG, "onEnabled.");
        startCitiesMonitorService(context);

        WidgetUtils.getAppWidgetCityData();
        WidgetUtils.asyncRecordWidgetState(context.getApplicationContext(),
                ClockOplusCSUtils.WORLD_CLOCK_WIDGET_CHANGED, true);
    }

    @Override
    public void onDisabled(Context context) {
        super.onDisabled(context);

        Log.d(TAG, "onDisabled.");
        context.stopService(new Intent(context, CitiesDataMonitorService.class));

        WidgetUtils.asyncRecordWidgetState(context.getApplicationContext(),
                ClockOplusCSUtils.WORLD_CLOCK_WIDGET_CHANGED, false);
    }

    @Override
    public void onReceive(@NonNull Context context, @NonNull Intent intent) {
        super.onReceive(context, intent);

        final String action = intent.getAction();
        Log.d(TAG, "onReceive[action]: " + action);
        if (TextUtils.isEmpty(action)) {
            return;
        }
        if (ACTION_GO_TO_ADD_CITY.equals(action)) {
            if (!AddCityActivity.sIsShowing) {
                goToAddCity(context);
            }
            return;
        }
        if (!WidgetUtils.needUpdateWidget(context)) {
            Log.d(TAG, "No need to update widget.");
            return;
        }

        final AppWidgetManager wm = AppWidgetManager.getInstance(context);
        if (wm == null) {
            Log.d(TAG, "wm is null");
            return;
        }

        final ComponentName provider = new ComponentName(context, getClass());
        final int[] widgetIds = wm.getAppWidgetIds(provider);

        switch (action) {
            case ACTION_DATE_CHANGED:
            case ACTION_TIME_CHANGED:
            case ACTION_TIMEZONE_CHANGED:
            case ACTION_CITIES_DATA_CHANGED:
            case ACTION_LOCALE_CHANGED:
                WidgetUtils.getAppWidgetCityData();
                break;
            case ACTION_UPDATE_CITES_DATA:
                relayoutAllWidgets(context, wm, widgetIds);
                break;
            case ACTION_WEATHER_INFO_UPDATE:
                //receive broadcast when get weather info success
                if (!TextUtils.isEmpty(WidgetUtils.getWeatherInfo())) {
                    relayoutAllWidgets(context, wm, widgetIds);
                }
                Log.d(TAG, "update weatherinfo:" + WidgetUtils.getWeatherInfo());
                break;
            default:
                break;
        }

        if (action.equals(ACTION_LAUNCHER_VISIBLE)) {
            updateWeatherInfoView(context);
            startCitiesMonitorService(context);
        }
        handleAction(context, action);
    }

    /**
     * Called when widgets must provide remote views.
     */
    @Override
    public void onUpdate(Context context, AppWidgetManager wm, int[] widgetIds) {
        super.onUpdate(context, wm, widgetIds);
        Log.i(TAG, "onUpdate.");
        WidgetUtils.getAppWidgetCityData();
        relayoutAllWidgets(context, wm, widgetIds);
        startCitiesMonitorService(context);
    }

    private static void startCitiesMonitorService(Context context) {

        try {
            context.startService(new Intent(context, CitiesDataMonitorService.class));
        } catch (Exception e) {
            Log.e(TAG, "startCitiesMonitorService error:" + e);
        }
    }

    private static void relayoutAllWidgets(Context context, AppWidgetManager wm, int[] widgetIds) {

        for (int widgetId : widgetIds) {
            Log.d(TAG, "relayoutAllWidgets widgetId: " + widgetId);
            doRelayoutWidget(context, wm, widgetId);
        }
    }

    /**
     * Called when the app widget changes sizes.
     */
    @Override
    public void onAppWidgetOptionsChanged(Context context, AppWidgetManager wm, int widgetId,
                                          Bundle options) {
        super.onAppWidgetOptionsChanged(context, wm, widgetId, options);

        Log.d(TAG, "onAppWidgetOptionsChanged widgetId: " + widgetId);
        // scale the fonts of the clock to fit inside the new size
        doRelayoutWidget(context, AppWidgetManager.getInstance(context), widgetId);
    }

    private static void doRelayoutWidget(Context context, AppWidgetManager wm, int widgetId) {
        final RemoteViews widget = relayoutWidget(context, wm, widgetId);
        wm.updateAppWidget(widgetId, widget);
        wm.notifyAppWidgetViewDataChanged(widgetId, R.id.world_city_list);
    }

    private static RemoteViews relayoutWidget(Context context, AppWidgetManager wm, int widgetId) {
        // Create a remote view for the digital clock.
        final RemoteViews rv = new RemoteViews(context.getPackageName(), R.layout.digital_widget);

        // Configure child views of the remote view.
        final CharSequence dateFormat = getDateFormat(context);
        rv.setCharSequence(R.id.date, "setFormat12Hour", dateFormat);
        rv.setCharSequence(R.id.date, "setFormat24Hour", dateFormat);

        String timerFormat12Hour = Formatter.getTimeFormatWithoutAMPM(false);
        String timerFormat24Hour = Formatter.getTimeFormatWithoutAMPM(true);
        rv.setCharSequence(R.id.vertical_time_clock, "setFormat12Hour", timerFormat12Hour);
        rv.setCharSequence(R.id.vertical_time_clock, "setFormat24Hour", timerFormat24Hour);
        Log.d(TAG, "time format 12Hour : " + timerFormat12Hour + " 24hour: " + timerFormat24Hour);

        // Set an adapter on the world city list. That adapter connects to a Service via intent.
        final Intent intent = new Intent(context, DigitalAppWidgetCityService.class);
        intent.putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, widgetId);
        intent.setData(Uri.parse(intent.toUri(Intent.URI_INTENT_SCHEME)));
        rv.setRemoteAdapter(R.id.world_city_list, intent);
        rv.setViewVisibility(R.id.world_city_list, VISIBLE);

        List<City> cityList = new ArrayList<>(DataModel.getDataModel().getAllCities());
        Log.d(TAG, "cityList.size: " + cityList.size());
        rv.setViewVisibility(R.id.iv_add_city, cityList.size() > 0 ? VISIBLE : GONE);
        rv.setViewVisibility(R.id.empty_layout, cityList.size() > 0 ? GONE : VISIBLE);

        // Tapping on the widget opens the app (if not on the lock screen).
        if (WidgetUtils.isWidgetClickable(wm, widgetId)) {
            Log.d(TAG, "widget clickable");
            final Intent openApp = new Intent(context, DigitalAppWidgetProvider.class);
            openApp.setAction(ACTION_VIEW_WORLD_CLOCK);
            final PendingIntent pi = PendingIntent.getBroadcast(context, 0, openApp, Utils.getPendingIntentFlagAboveS(0));
            rv.setPendingIntentTemplate(R.id.world_city_list, pi);

            final Intent openClock = new Intent(context, DigitalAppWidgetProvider.class);
            openClock.setAction(ACTION_CLICK_TIME);
            final PendingIntent pi2 = PendingIntent.getBroadcast(context, 0, openClock, Utils.getPendingIntentFlagAboveS(0));
            rv.setOnClickPendingIntent(R.id.vertical_time_clock, pi2);

            final Intent clickCalendar = new Intent(context, DigitalAppWidgetProvider.class);
            clickCalendar.setAction(ACTION_CLICK_CALENDAR);
            final PendingIntent pi3 = PendingIntent.getBroadcast(context, 0, clickCalendar, Utils.getPendingIntentFlagAboveS(0));
            rv.setOnClickPendingIntent(R.id.date, pi3);

            final Intent clickWeather = new Intent(context, DigitalAppWidgetProvider.class);
            clickWeather.setAction(ACTION_CLICK_WEATHER);
            final PendingIntent pi4 = PendingIntent.getBroadcast(context, 0, clickWeather, Utils.getPendingIntentFlagAboveS(0));
            //2223969 属于国家电网在内网环境使用的手机 世界时间UI不显示温度、天气
            if (DeviceUtils.isDisableWeatherTemperature(context)) {
                rv.setViewVisibility(R.id.weather_info, View.INVISIBLE);
            } else {
                rv.setViewVisibility(R.id.weather_info, View.VISIBLE);
            }
            rv.setOnClickPendingIntent(R.id.weather_info, pi4);
            final Intent addCityIntent = new Intent(context, DigitalAppWidgetProvider.class);
            addCityIntent.setAction(ACTION_GO_TO_ADD_CITY);
            final PendingIntent pi5 = PendingIntent.getBroadcast(context, 0, addCityIntent, Utils.getPendingIntentFlagAboveS(0));
            rv.setOnClickPendingIntent(R.id.empty_layout, pi5);
            rv.setOnClickPendingIntent(R.id.iv_add_city, pi5);
        }

        //Am pm view.
        udpateTimeFormatTextView(context, rv);

        //Local weather info.
        if (TextUtils.isEmpty(WidgetUtils.getWeatherInfo())) {
            Log.d(TAG, "weatherInfo is null");
        } else {
            rv.setTextViewText(R.id.weather_info, WidgetUtils.getWeatherInfo());
        }

        return rv;
    }

    private void handleAction(Context context, String action) {
        switch (action) {
            case ACTION_CLICK_CALENDAR:
                if (!PackageDisabledManager.INSTANCE.checkDisabledAndJump(context, new CalendarDisableInfo())) {
                    ClockWidgetManager.getInstance().startAppByIntent(context, Intent.ACTION_MAIN, Intent.CATEGORY_APP_CALENDAR, null);
                }
                break;
            case ACTION_CLICK_WEATHER:
                if (!PackageDisabledManager.INSTANCE.checkDisabledAndJump(context, new WeatherDisableInfo())) {
                    ClockWidgetManager.getInstance().startWeatherAppByIntent(context, ClockWidgetManager.OPLUS_WEATHER_ACTION, null, false);
                }
                break;
            case ACTION_CLICK_TIME:
                ClockWidgetManager.getInstance().startAppByIntent(context, ClockWidgetManager.ALARM_CLOCK_ACTION, null, null);
                break;
            case ACTION_VIEW_WORLD_CLOCK:
                goToWorld(context);
                break;
        }
    }

    /**
     * @return the locale-specific date pattern
     */
    private static String getDateFormat(Context context) {
        final Locale locale = Locale.getDefault();
        final String skeleton = context.getString(R.string.abbrev_wday_month_day_no_year);
        return DateFormat.getBestDateTimePattern(locale, skeleton);
    }

    static void udpateTimeFormatTextView(Context context, RemoteViews rv) {
        if (context == null) {
            Log.d(TAG, "udpateTimeFormatTextView context is null");
            return;
        }
        boolean is24HourFormat = DateFormat.is24HourFormat(context);
        Log.d(TAG, "udpateTimeFormatTextView: is24HourFormat: " + is24HourFormat);

        if (is24HourFormat) {
            rv.setViewVisibility(R.id.am_pm, GONE);
        } else {
            rv.setViewVisibility(R.id.am_pm, VISIBLE);
        }
    }

    private static String getAMPM(Context context) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        String ampmFormat = "";
        if (DateFormat.is24HourFormat(context)) {
            ampmFormat = "";
        } else {
            ampmFormat = "a";
        }
        return (String) DateFormat.format(ampmFormat, calendar);
    }

    public static void updateWeatherInfoView(final Context context) {
        WidgetUtils.getLocalWeatherInfo(context);
    }

    private void goToWorld(Context context) {
        Intent intent = new Intent(context, AlarmClock.class);
        intent.setAction(AlarmClock.ACTION_VIEW_WORLD_CLOCKS);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        context.startActivity(intent);
    }

    private void goToAddCity(Context context) {
        Intent addCityIntent = new Intent(context, AddCityActivity.class);
        addCityIntent.setAction(AddCityFragment.ACTION_ADD_WORLD_CLOCK);
        addCityIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        addCityIntent.putExtra(AddCityFragment.CURRENT_COUNT, -1);
        addCityIntent.putExtra(AddCityActivity.IS_SHOW_PANEL, true);
        addCityIntent.putExtra(AddCityActivity.IS_FROM_WORD_TIME_PLUG, true);
        addCityIntent.putExtra(AddCityActivity.IS_FROM_WORD_CLOCK_OR_WIDGET,true);
        context.startActivity(addCityIntent);
    }
}
