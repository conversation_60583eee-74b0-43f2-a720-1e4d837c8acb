/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.coloros.alarmclock.widget;

import android.content.Context;
import android.content.Intent;
import android.text.format.DateFormat;
import android.view.View;
import android.widget.RemoteViews;
import android.widget.RemoteViewsService.RemoteViewsFactory;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.CityUtils;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import static android.appwidget.AppWidgetManager.EXTRA_APPWIDGET_ID;
import static android.appwidget.AppWidgetManager.INVALID_APPWIDGET_ID;
import static android.view.View.GONE;
import static android.view.View.VISIBLE;

/**
 * This factory produces entries in the world cities list view displayed
 * at the right part of the digital widget.
 */
class DigitalAppWidgetCityViewsFactory implements RemoteViewsFactory {

    private static final String TAG = "DigitalAppWidgetCityViewsFactory";
    private final Intent mFillInIntent = new Intent();

    private final int mWidgetId;
    private final Context mContext;
    private final String mSpace;
    private List<Integer> mShouldShowCountryCityIds;
    private String mCityName;
    private boolean mIsExp = false;


    private List<City> mCities = Collections.emptyList();

    DigitalAppWidgetCityViewsFactory(Context context, Intent intent) {
        mContext = context;
        mWidgetId = intent.getIntExtra(EXTRA_APPWIDGET_ID, INVALID_APPWIDGET_ID);
        mSpace = context.getString(R.string.space);
        mIsExp = DeviceUtils.isExpVersion(AlarmClockApplication.getInstance());
        mShouldShowCountryCityIds = Arrays.asList(AddGlobalCityListAdapter.getShouldShowCountryCityIds());
        Log.i(TAG, "Construct: " + mWidgetId);
    }

    @Override
    public void onCreate() {
        Log.i(TAG, "onCreate " + mWidgetId);
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy " + mWidgetId);
    }

    @Override
    public int getCount() {
        mCities = WidgetUtils.sCityMap.get(CityUtils.transLocaleForCity(Locale.getDefault()));
        int count = mCities == null ? 0 : mCities.size();
        Log.i(TAG, "getCount: " + count);
        WidgetUtils.setCurrentCount(count);
        return count;
    }

    @Override
    public RemoteViews getViewAt(int position) {
        if (position < 0 || position >= getCount()) {
            Log.e(TAG, "[getViewAt] Illegal position: " + position);
            return null;
        }
        final City city = mCities.get(position);

        Log.d(TAG, "[getViewAt: " + position + "]: " + city);

        final RemoteViews rv =
                new RemoteViews(mContext.getPackageName(), R.layout.world_clock_remote_list_item);

        // Show the left clock if one exists.
        if (city != null) {
            update(rv, city, R.id.clock, R.id.city_name, R.id.city_day, R.id.am_pm);
        } else {
            hide(rv, R.id.clock, R.id.city_name, R.id.city_day, R.id.am_pm);
        }

        final int count = getCount();
        // Hide last spacer in last row; show for all others.
        final boolean lastRow = position == count - 1;
        rv.setViewVisibility(R.id.city_spacer, lastRow ? VISIBLE : GONE);

        rv.setOnClickFillInIntent(R.id.widget_item, mFillInIntent);
        return rv;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public RemoteViews getLoadingView() {
        return new RemoteViews(mContext.getPackageName(), R.layout.layout_space);
    }

    @Override
    public int getViewTypeCount() {
        return 1;
    }

    @Override
    public boolean hasStableIds() {
        return false;
    }

    @Override
    public void onDataSetChanged() {
        Log.d(TAG, "onDataSetChanged");
        // Fetch the data on the main Looper.
        final RefreshRunnable refreshRunnable = new RefreshRunnable();
        DataModel.getDataModel().run(refreshRunnable);
        // Store the data in local variables.
        WidgetUtils.sCityMap = refreshRunnable.mCityMap;
    }

    private void update(RemoteViews rv, City city, int clockId, int labelId, int dayId, int ampmId) {

        String timerFormat12Hour = Formatter.getTimeFormatWithoutAMPM(false);
        String timerFormat24Hour = Formatter.getTimeFormatWithoutAMPM(true);

        rv.setCharSequence(clockId, "setFormat12Hour", timerFormat12Hour);
        rv.setCharSequence(clockId, "setFormat24Hour", timerFormat24Hour);
        rv.setCharSequence(ampmId, "setFormat12Hour", mContext.getResources().getString(R.string.vertical_digital_clock_24_hour_format_am));
        rv.setString(clockId, "setTimeZone", city.getTimezone());
        rv.setString(ampmId, "setTimeZone", city.getTimezone());
        //世界时钟插件 内销显示 修改地缘政治世界同名城市后缀国家  奥克兰（美国） 奥克兰（新西兰）  中国台北  中国香港 中国澳门
        if (!mIsExp && mShouldShowCountryCityIds.contains(city.getCityId())) {
            mCityName = city.getName() + " " + "(" + city.getCountry() + ")";
        } else {
            mCityName = city.getName();
        }
        Log.d(TAG, "update：" + mCityName);
        rv.setTextViewText(labelId, mCityName);

        rv.setTextViewText(dayId, CityUtils.getTimeZoneOffset(city.getTimezone()));

        rv.setViewVisibility(dayId, View.VISIBLE);
        rv.setViewVisibility(clockId, View.VISIBLE);
        rv.setViewVisibility(labelId, View.VISIBLE);

        udpateTimeFormatTextView(rv, ampmId);
    }

    private void hide(RemoteViews clock, int clockId, int labelId, int dayId, int ampmId) {
        clock.setViewVisibility(dayId, View.INVISIBLE);
        clock.setViewVisibility(clockId, View.INVISIBLE);
        clock.setViewVisibility(labelId, View.INVISIBLE);
        clock.setViewVisibility(ampmId, GONE);
    }

    private void udpateTimeFormatTextView(RemoteViews rv, int ampmId) {
        boolean is24HourFormat = DateFormat.is24HourFormat(mContext);
        Log.d(TAG, "udpateTimeFormatTextView: is24HourFormat: " + is24HourFormat);

        if (is24HourFormat) {
            rv.setViewVisibility(ampmId, GONE);
        } else {
            rv.setViewVisibility(ampmId, VISIBLE);
        }
    }

    /**
     * This Runnable fetches data for this factory on the main thread.
     */
    private static final class RefreshRunnable implements Runnable {

        private HashMap<String, ArrayList<City>> mCityMap;

        @Override
        public void run() {
            mCityMap = DataModel.getDataModel().getAllLanguageCities();
        }
    }
}
