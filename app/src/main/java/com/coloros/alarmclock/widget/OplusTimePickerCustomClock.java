/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: OplusTimePickerCustomClock.java
 ** Description: Widget for Clock timer picker. Based on OplusTimePickerCustom in oplus-framework.
 ** Version: V 1.0
 ** Date : 2018-07-05
 ** Author: <PERSON>long
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/

package com.coloros.alarmclock.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.format.DateUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.coloros.widget.smallweather.TimeInfoBuilder;
import com.coui.appcompat.picker.COUINumberPicker;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.Utils;

import java.util.Calendar;
import java.util.Locale;

/**
 * A view for selecting the time of day, in either 24 hour or AM/PM mode. The hour, each minute
 * digit, and AM/PM (if applicable) can be conrolled by vertical spinners. The hour can be entered
 * by keyboard input. Entering in two digit hours can be accomplished by hitting two digits within a
 * timeout of about a second (e.g. '1' then '2' to select 12). The minutes can be entered by
 * entering single digits. Under AM/PM mode, the user can hit 'a', 'A", 'p' or 'P' to pick. For a
 * dialog using this view, see {@link com.oplus.app.OplusTimePickerDialog}.
 * <p>
 * See the <a href="{@docRoot}resources/tutorials/views/hello-timepicker.html">Time Picker
 * tutorial</a>.
 * </p>
 */

public class OplusTimePickerCustomClock extends FrameLayout implements COUINumberPicker.OnValueChangeListener {
    private static final String TAG = "OplusTimePickerCustomClock";

    private static final boolean DEFAULT_ENABLED_STATE = true;

    private static final int HOURS_IN_HALF_DAY = 12;
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int TWELVE = 12;
    private static final int TWENTY_THREE = 23;
    private static final int FIFTY_NINE = 59;
    private static final int ONE_HUNDRED = 100;
    private static final float ALPHA_ZERO_POINT_FIVE = 0.5f;
    private static final float ALPHA_ONE = 1.0f;
    public final COUINumberPicker mOplusHourSpinner;

    public final COUINumberPicker mOplusMinuteSpinner;

    public final COUINumberPicker mOplusSecondSpinner;

    protected TextView mMinuteText;
    protected TextView mHourText;

    /**
     * A no-op callback used in the constructor to avoid null checks later in the code.
     */
    private final OnTimeChangedListener mNoOpChangeListener = new OnTimeChangedListener() {

        public void onTimeChanged(OplusTimePickerCustomClock view, int hourOfDay, int minute) {
        }
    };

    //#ifdef OPLUSOS_EDIT
    //<EMAIL>, 2015-03-14 : Add for PMD check--unused private fields
    /*
    private final LinearLayout mTimeBackground;
    */
    //#endif /* OPLUSOS_EDIT */
    private boolean mIsAnnouncement = true;
    // state
    private boolean mIs24HourView;

    private boolean mIsAm;

    private LinearLayout mTimePicker;

    private boolean mIsEnabled = DEFAULT_ENABLED_STATE;

    // callbacks
    private OnTimeChangedListener mOnTimeChangedListener;

    private Calendar mTempCalendar;

    private Locale mCurrentLocale;
    // #ifdef VENDOR_EDIT
    // <EMAIL>, 2017/04/05, add this symbol
    private boolean mIsCountDown = false;
    // #endif /* VENDOR_EDIT */

    private Context mContext;

    /**
     * The callback interface used to indicate the time has been adjusted.
     */
    public interface OnTimeChangedListener {

        /**
         * @param view      The view associated with this listener.
         * @param hourOfDay The current hour.
         * @param minute    The current minute.
         */
        void onTimeChanged(OplusTimePickerCustomClock view, int hourOfDay, int minute);
    }

    public OplusTimePickerCustomClock(Context context) {
        this(context, null);
    }

    public OplusTimePickerCustomClock(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
        setWillNotDraw(false);
    }

    public OplusTimePickerCustomClock(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mContext = context;
        // initialization based on locale
        setCurrentLocale(Locale.getDefault());

        int layoutResourceId = getLayoutResId();

        LayoutInflater inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        if (inflater != null) {
            inflater.inflate(layoutResourceId, this, true);
        }
        mTimePicker = findViewById(R.id.time_pickers);
        String colon = TimeInfoBuilder.getColon();
        mMinuteText = (TextView) findViewById(R.id.oplus_timepicker_minute_text);
        mHourText = (TextView) findViewById(R.id.oplus_timepicker_hour_text);
        mMinuteText.setText(colon);
        mHourText.setText(colon);
        mOplusHourSpinner = findViewById(R.id.hour);

        mOplusHourSpinner.setOnValueChangedListener(this);
        TextWeightUtils.setTextBold(mOplusHourSpinner.getSelectorTextPaint());

        mMinuteText.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
        mHourText.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);

        // minute
        mOplusMinuteSpinner = findViewById(R.id.minute);
        mOplusMinuteSpinner.setMinValue(ZERO);
        mOplusMinuteSpinner.setMaxValue(FIFTY_NINE);
        mOplusMinuteSpinner.setOnLongPressUpdateInterval(ONE_HUNDRED);
        mOplusMinuteSpinner.setOnValueChangedListener(this);
        mOplusMinuteSpinner.setWrapSelectorWheel(true);
        TextWeightUtils.setTextBold(mOplusMinuteSpinner.getSelectorTextPaint());

        //second
        mOplusSecondSpinner = findViewById(R.id.second);
        mOplusSecondSpinner.setMinValue(ZERO);
        mOplusSecondSpinner.setMaxValue(FIFTY_NINE);
        mOplusSecondSpinner.setOnLongPressUpdateInterval(ONE_HUNDRED);
        mOplusSecondSpinner.setOnValueChangedListener(this);
        mOplusSecondSpinner.setWrapSelectorWheel(true);
        TextWeightUtils.setTextBold(mOplusSecondSpinner.getSelectorTextPaint());

        // update controls to initial state
        updateHourControl();

        setOnTimeChangedListener(mNoOpChangeListener);

        // set to current time
        setCurrentHour(mTempCalendar.get(Calendar.HOUR_OF_DAY));
        setCurrentMinute(mTempCalendar.get(Calendar.MINUTE));

        if (!isEnabled()) {
            setEnabled(false);
        }
        // #ifdef VENDOR_EDIT
        // <EMAIL>, 2017/04/06, add for update formatter
        updateFormatter();
        // #endif /* VENDOR_EDIT */
        // set the content descriptions
    }


    @Override
    public void onValueChange(COUINumberPicker picker, int oldVal, int newVal) {
        onTimeChanged();
    }

    // #ifdef VENDOR_EDIT
    // <EMAIL>, 2017/04/05, add this function
    public void setIsCountDown(boolean isCountDown) {
        mIsCountDown = isCountDown;
        updateFormatter();
    }
    // #endif /* VENDOR_EDIT */

    protected int getLayoutResId() {
        if (this.getContext() instanceof BaseActivity contextImpl) {
            if (contextImpl.isTabletMode()) {
                return R.layout.oplus_time_picker_custom_large;
            } else if (contextImpl.isMidMode()) {
                return R.layout.oplus_time_picker_custom_mid;
            } else {
                return R.layout.oplus_time_picker_custom;
            }
        } else {
            return R.layout.oplus_time_picker_custom;
        }
    }

    // #ifdef VENDOR_EDIT
    // <EMAIL>, 2017/04/06, add for update formatter fuction
    private void updateFormatter() {
        if (mIsCountDown) {
            mOplusHourSpinner.setTwoDigitFormatter();
            mOplusMinuteSpinner.setTwoDigitFormatter();
            mOplusSecondSpinner.setTwoDigitFormatter();
        }
        if (!mIsCountDown && is24HourView()) {
            mOplusHourSpinner.setTwoDigitFormatter();
            mOplusMinuteSpinner.setTwoDigitFormatter();
            mOplusSecondSpinner.setTwoDigitFormatter();
        }
        if (!mIsCountDown && !is24HourView()) {
            mOplusHourSpinner.setFormatter(null);
            mOplusMinuteSpinner.setTwoDigitFormatter();
        }
        mOplusHourSpinner.requestLayout();
        mOplusMinuteSpinner.requestLayout();
        mOplusSecondSpinner.requestLayout();
    }
    // #endif /* VENDOR_EDIT */

    @Override
    public void setEnabled(boolean enabled) {
        if (mIsEnabled == enabled) {
            return;
        }
        super.setEnabled(enabled);
        mOplusMinuteSpinner.setEnabled(enabled);
        mOplusHourSpinner.setEnabled(enabled);
        mOplusSecondSpinner.setEnabled(enabled);
        mIsEnabled = enabled;

        if (enabled) {
            mOplusHourSpinner.setAlpha(ALPHA_ONE);
            mOplusMinuteSpinner.setAlpha(ALPHA_ONE);
            mOplusSecondSpinner.setAlpha(ALPHA_ONE);
            mHourText.setAlpha(ALPHA_ONE);
            mMinuteText.setAlpha(ALPHA_ONE);
        } else {
            mOplusHourSpinner.setAlpha(ALPHA_ZERO_POINT_FIVE);
            mOplusMinuteSpinner.setAlpha(ALPHA_ZERO_POINT_FIVE);
            mOplusSecondSpinner.setAlpha(ALPHA_ZERO_POINT_FIVE);
            mHourText.setAlpha(ALPHA_ZERO_POINT_FIVE);
            mMinuteText.setAlpha(ALPHA_ZERO_POINT_FIVE);
        }
    }

    @Override
    public boolean isEnabled() {
        return mIsEnabled;
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        setCurrentLocale(newConfig.locale);
    }

    /**
     * Sets the current locale.
     *
     * @param locale The current locale.
     */
    private void setCurrentLocale(Locale locale) {
        if (locale.equals(mCurrentLocale)) {
            return;
        }
        mCurrentLocale = locale;
        mTempCalendar = Calendar.getInstance(locale);
    }

    /**
     * Used to save / restore state of time picker
     */
    private static class SavedState extends BaseSavedState {
        @SuppressWarnings({"unused", "hiding"})
        public static final Creator<SavedState> CREATOR = new Creator<SavedState>() {

            public SavedState createFromParcel(Parcel in) {
                return new SavedState(in);
            }

            public SavedState[] newArray(int size) {
                return new SavedState[size];
            }
        };
        private final int mHour;

        private final int mMinute;

        private SavedState(Parcelable superState, int hour, int minute) {
            super(superState);
            mHour = hour;
            mMinute = minute;
        }

        private SavedState(Parcel in) {
            super(in);
            mHour = in.readInt();
            mMinute = in.readInt();
        }

        public int getHour() {
            return mHour;
        }

        public int getMinute() {
            return mMinute;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeInt(mHour);
            dest.writeInt(mMinute);
        }
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        Parcelable superState = super.onSaveInstanceState();
        return new SavedState(superState, getCurrentHour(), getCurrentMinute());
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        SavedState ss = (SavedState) state;
        super.onRestoreInstanceState(ss.getSuperState());
        setCurrentHour(ss.getHour());
        setCurrentMinute(ss.getMinute());
    }

    /**
     * Set the callback that indicates the time has been adjusted by the user.
     *
     * @param onTimeChangedListener the callback, should not be null.
     */
    public void setOnTimeChangedListener(OnTimeChangedListener onTimeChangedListener) {
        mOnTimeChangedListener = onTimeChangedListener;
    }

    /**
     * @return The current hour in the range (0-23).
     */
    public int getCurrentHour() {
        int currentHour = mOplusHourSpinner.getValue();
        if (is24HourView()) {
            return currentHour;
        } else if (mIsAm) {
            return currentHour % HOURS_IN_HALF_DAY;
        } else {
            return (currentHour % HOURS_IN_HALF_DAY) + HOURS_IN_HALF_DAY;
        }
    }

    /**
     * Set the current hour.
     */
    public void setCurrentHour(int currentHour) {
        int hour = currentHour;
        // why was Integer used in the first place?
        if (hour == getCurrentHour()) {
            return;
        }

        if (!is24HourView()) {
            // convert [0,23] ordinal to wall clock display
            if (currentHour >= HOURS_IN_HALF_DAY) {
                mIsAm = false;
                if (currentHour > HOURS_IN_HALF_DAY) {
                    hour = currentHour - HOURS_IN_HALF_DAY;
                }
            } else {
                mIsAm = true;
                if (currentHour == 0) {
                    hour = HOURS_IN_HALF_DAY;
                }
            }
        }
        mOplusHourSpinner.setValue(hour);
        onTimeChanged();
    }

    /**
     * 设置等级
     *
     * @param level 0清脆 1柔和
     */
    public void setVibrateLevel(int level) {
        mOplusHourSpinner.setVibrateLevel(level);
        mOplusMinuteSpinner.setVibrateLevel(level);
        mOplusSecondSpinner.setVibrateLevel(level);
    }

    /**
     * 设置强度
     *
     * @param intensity
     */
    public void setVibrateIntensity(float intensity) {
        mOplusHourSpinner.setVibrateIntensity(intensity);
        mOplusMinuteSpinner.setVibrateIntensity(intensity);
        mOplusSecondSpinner.setVibrateIntensity(intensity);
    }

    /**
     * Set whether in 24 hour or AM/PM mode.
     *
     * @param is24HourView True = 24 hour mode. False = AM/PM.
     */
    public void setIs24HourView(boolean is24HourView) {
        if (mIs24HourView == is24HourView) {
            return;
        }

        int currentHour = getCurrentHour();
        mIs24HourView = is24HourView;
        updateHourControl();
        // set value after spinner range is updated
        setCurrentHour(currentHour);
        updateFormatter();
        mOplusHourSpinner.requestLayout();

    }

    /**
     * @return true if this is in 24 hour view else false.
     */
    public boolean is24HourView() {
        return mIs24HourView;
    }

    /**
     * @return The current minute.
     */
    public int getCurrentMinute() {
        return mOplusMinuteSpinner.getValue();
    }

    /**
     * @return The current seconds..
     */
    public int getCurrentSecond() {
        return mOplusSecondSpinner.getValue();
    }

    /**
     * Set the current second (0-59).
     */
    public void setCurrentSecond(int currentSecond) {
        if (currentSecond == getCurrentSecond()) {
            return;
        }
        mOplusSecondSpinner.setValue(currentSecond);
        onTimeChanged();
    }

    /**
     * Set the current minute (0-59).
     */
    public void setCurrentMinute(int currentMinute) {
        if (currentMinute == getCurrentMinute()) {
            return;
        }
        mOplusMinuteSpinner.setValue(currentMinute);
        onTimeChanged();
    }

    @Override
    public int getBaseline() {
        return mOplusHourSpinner.getBaseline();
    }

    @Override
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
        onPopulateAccessibilityEvent(event);
        return true;
    }

    @Override
    public void onPopulateAccessibilityEvent(AccessibilityEvent event) {
        super.onPopulateAccessibilityEvent(event);

        int flags = DateUtils.FORMAT_SHOW_TIME;
        if (mIs24HourView) {
            flags |= DateUtils.FORMAT_24HOUR;
        } else {
            flags |= DateUtils.FORMAT_12HOUR;
        }
        mTempCalendar.set(Calendar.HOUR_OF_DAY, getCurrentHour());
        mTempCalendar.set(Calendar.MINUTE, getCurrentMinute());
        mTempCalendar.set(Calendar.SECOND, getCurrentSecond());
        String selectedDateUtterance = Formatter.formatTime(mContext, mTempCalendar
                .getTimeInMillis());
        event.getText().add(selectedDateUtterance);
    }

    private void updateHourControl() {
        if (is24HourView()) {
            mOplusHourSpinner.setMinValue(ZERO);
            mOplusHourSpinner.setMaxValue(TWENTY_THREE);
        } else {
            mOplusHourSpinner.setMinValue(ONE);
            mOplusHourSpinner.setMaxValue(TWELVE);
        }
        mOplusHourSpinner.setWrapSelectorWheel(true);
    }

    /**
     * 时间选择器小屏样式控制
     */
    public void updateSpinnerSplitParams(boolean isOslo, int orientation) {
        if (isOslo) {
            if (Configuration.ORIENTATION_LANDSCAPE == orientation) {
                LayoutParams layoutParams = (LayoutParams) mTimePicker.getLayoutParams();
                layoutParams.setMarginStart(getResources().getDimensionPixelOffset(R.dimen.timer_picker_split_left_blank_width));
                layoutParams.setMarginEnd(getResources().getDimensionPixelOffset(R.dimen.timer_picker_split_left_blank_width));
                mTimePicker.setLayoutParams(layoutParams);
            } else {
                return;
            }
        }
        LinearLayout.LayoutParams lpHour = (LinearLayout.LayoutParams) mOplusHourSpinner.getLayoutParams();
        LinearLayout.LayoutParams lpMinute = (LinearLayout.LayoutParams) mOplusMinuteSpinner.getLayoutParams();
        LinearLayout.LayoutParams lpSecond = (LinearLayout.LayoutParams) mOplusSecondSpinner.getLayoutParams();
        lpHour.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_split_height);
        lpMinute.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_split_height);
        lpSecond.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_split_height);
        mOplusHourSpinner.setLayoutParams(lpHour);
        mOplusMinuteSpinner.setLayoutParams(lpMinute);
        mOplusSecondSpinner.setLayoutParams(lpSecond);

        mOplusHourSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusHourSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusHourSpinner.setBackgroundRadius(getResources().getDimensionPixelSize(R.dimen.timer_picker_background_radius));
        mOplusHourSpinner.setDrawItemVerticalOffset(getResources().getDimensionPixelSize(R.dimen.timer_picker_vertical_offset));

        mOplusMinuteSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusMinuteSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusMinuteSpinner.setBackgroundRadius(getResources().getDimensionPixelSize(R.dimen.timer_picker_background_radius));
        mOplusMinuteSpinner.setDrawItemVerticalOffset(getResources().getDimensionPixelSize(R.dimen.timer_picker_vertical_offset));

        mOplusSecondSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusSecondSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_split_text_size));
        mOplusSecondSpinner.setBackgroundRadius(getResources().getDimensionPixelSize(R.dimen.timer_picker_background_radius));
        mOplusSecondSpinner.setDrawItemVerticalOffset(getResources().getDimensionPixelSize(R.dimen.timer_picker_vertical_offset));

        mHourText.setTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_split_text_size));
        mMinuteText.setTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_split_text_size));
        mHourText.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.timer_picker_colon_split_bottom_padding));
        mMinuteText.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.timer_picker_colon_split_bottom_padding));
    }

    /**
     * 设置平板分屏大于最小宽度的布局
     */
    public void updatePadSplitParams() {
        Log.v(TAG, "updatePadSplitParams: ");
        LinearLayout.LayoutParams lpHour = (LinearLayout.LayoutParams) mOplusHourSpinner.getLayoutParams();
        LinearLayout.LayoutParams lpMinute = (LinearLayout.LayoutParams) mOplusMinuteSpinner.getLayoutParams();
        LinearLayout.LayoutParams lpSecond = (LinearLayout.LayoutParams) mOplusSecondSpinner.getLayoutParams();
        lpHour.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_height_pad_split);
        lpMinute.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_height_pad_split);
        lpSecond.height = getResources().getDimensionPixelOffset(R.dimen.timer_picker_height_pad_split);
        mOplusHourSpinner.setLayoutParams(lpHour);
        mOplusMinuteSpinner.setLayoutParams(lpMinute);
        mOplusSecondSpinner.setLayoutParams(lpSecond);

        mOplusHourSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));
        mOplusHourSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));

        mOplusMinuteSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));
        mOplusMinuteSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));

        mOplusSecondSpinner.setFocusTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));
        mOplusSecondSpinner.setNormalTextSize(getResources().getDimensionPixelSize(R.dimen.timer_number_picker_pad_split_text_size));
    }

    private void onTimeChanged() {
        if (mIsAnnouncement) {
            sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_SELECTED);
        }

        if (mOnTimeChangedListener != null) {
            mOnTimeChangedListener.onTimeChanged(this, getCurrentHour(), getCurrentMinute());
        }
        mIsAnnouncement = true;
    }

    public void setAnnouncement() {
        mIsAnnouncement = !mIsAnnouncement;
    }

    public void setTimerPickerSuitableFontSize(int numberSizeType, int textSizeType, boolean isTableMode) {
        float fontScale = getContext().getResources().getConfiguration().fontScale;
        int defaultTextSize = getResources().getDimensionPixelSize(R.dimen.text_size_sp_39);
        Utils.setSuitableFontSize(mHourText, defaultTextSize, fontScale, textSizeType);
        Utils.setSuitableFontSize(mMinuteText, defaultTextSize, fontScale, textSizeType);
        if (isTableMode) {
            TextWeightUtils.setTextBold(mHourText);
            TextWeightUtils.setTextBold(mMinuteText);
        }
    }

    public void setBurmeseDiffusion() {
        String burmeseLanguage = "my";
        String language = Locale.getDefault().getLanguage();
        if (language.equals(burmeseLanguage)) {
            mOplusHourSpinner.setDiffusion(getResources().getDimensionPixelSize(R.dimen.timer_picker_diffusion_in_burmese));
            mOplusMinuteSpinner.setDiffusion(getResources().getDimensionPixelSize(R.dimen.timer_picker_diffusion_in_burmese));
            mOplusSecondSpinner.setDiffusion(getResources().getDimensionPixelSize(R.dimen.timer_picker_diffusion_in_burmese));
        }
    }
}
