
/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: V 1.0
 ** Date : 2019/1/4
 ** Author: long zhanfeng
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.alarmclock.widget;


import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.View;
import android.view.animation.PathInterpolator;
import android.widget.CompoundButton;
import android.widget.LinearLayout;

import com.oplus.alarmclock.utils.Utils;

public class CompoundButtonAnimation {

    private static final int ANIM_DURATION = 317;
    private static final int ANIM_CHECKBOX_OFFSET = 167;
    private static final int ANIM_CHECKBOX_DURATION = 180;
    private static final int ANIM_TRANSLATION_DURATION = 517;
    private static final float ALPHA_ONE = 1f;
    private static final float ALPHA_03F = 0.3f;
    private static final float ALPHA_01f = 0.1f;
    private static final float ALPHA_ZERO = 0.0f;
    private boolean mAnimationStart = false;
    private boolean mIsRtl;
    private PathInterpolator mEnterInterpolator;
    private int mAnimatingItemCount = 0;

    public interface OnAnimationCompleteCallback {
        void onAnimationComplete();

        void onAnimationStart();
    }

    public CompoundButtonAnimation(Context context) {

        mIsRtl = Utils.isRtl();
    }

    public boolean isAnimating() {
        return mAnimatingItemCount != 0;
    }

    public void setAnimationStart(boolean m) {
        this.mAnimationStart = m;
    }

    public boolean isAnimationStart() {
        return mAnimationStart;
    }

    public void startItemViewAnimation(View viewHint, View viewShow, int translationX) {
        startItemViewAnimation(viewHint, viewShow, translationX, null);
    }

    public void initLayout(View goneView, View visibleView, View translationView, int translationX) {
        translationView.setTranslationX(mIsRtl ? -translationX : translationX);
        visibleView.setAlpha(ALPHA_ONE);
        visibleView.setVisibility(View.VISIBLE);
        goneView.setAlpha(ALPHA_ZERO);
        goneView.setVisibility(View.INVISIBLE);
        if (mEnterInterpolator == null) {
            mEnterInterpolator = new PathInterpolator(ALPHA_03F, ALPHA_ZERO, ALPHA_01f, ALPHA_ONE);
        }
    }

    public void initEditLayout(final View alphaView, final View translationView, final int translationX) {
        translationView.setTranslationX(mIsRtl ? -translationX : translationX);
        translationView.setAlpha(ALPHA_ONE);
        translationView.setVisibility(View.VISIBLE);
        alphaView.setAlpha(ALPHA_ZERO);
        alphaView.setVisibility(View.INVISIBLE);
        if (mEnterInterpolator == null) {
            mEnterInterpolator = new PathInterpolator(ALPHA_03F, ALPHA_ZERO, ALPHA_01f, ALPHA_ONE);
        }
    }

    public void initNormalLayout(final View alphaView, final View translationView, final int translationX) {
        alphaView.setAlpha(ALPHA_ONE);
        alphaView.setVisibility(View.VISIBLE);
        translationView.setTranslationX(0);
        translationView.setVisibility(View.GONE);
        if (mEnterInterpolator == null) {
            mEnterInterpolator = new PathInterpolator(ALPHA_03F, ALPHA_ZERO, ALPHA_01f, ALPHA_ONE);
        }
    }

    /**
     * item 的背景卡片编辑动画
     *
     * @param itemBg
     * @param marginEnd
     * @param toMarginEnd
     */
    public void startItemCardBgAnimation(View itemBg, int marginEnd, int toMarginEnd) {
        if (mAnimationStart) {
            ValueAnimator valueAnimator = ValueAnimator.ofInt(marginEnd, toMarginEnd).setDuration(ANIM_DURATION);
            valueAnimator.addUpdateListener(animation -> {
                LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) itemBg.getLayoutParams();
                lp.rightMargin = (int) animation.getAnimatedValue();
                itemBg.setLayoutParams(lp);
            });
            valueAnimator.start();
        }

    }


    public void startItemViewAnimation(final View alphaView, final View translationView, final int translationX, final OnAnimationCompleteCallback callback) {

        if (mAnimationStart) {
            translationView.setTranslationX(0);
            if (alphaView.getVisibility() != View.VISIBLE) {
                alphaView.setVisibility(View.VISIBLE);
            }

            translationView.setAlpha(ALPHA_ZERO);
            translationView.animate()
                    .setInterpolator(mEnterInterpolator)
                    .setDuration(ANIM_CHECKBOX_DURATION)
                    .translationX(mIsRtl ? -translationX : translationX)
                    .alpha(ALPHA_ONE)
                    .setListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            mAnimatingItemCount--;
                            mAnimationStart = false;
                            if (callback != null) {
                                callback.onAnimationComplete();
                            }
                            alphaView.setVisibility(View.INVISIBLE);
                        }

                        @Override
                        public void onAnimationStart(Animator animation) {
                            mAnimatingItemCount++;
                            if (callback != null) {
                                callback.onAnimationStart();
                            }
                        }
                    })
                    .start();

            alphaView.setAlpha(ALPHA_ONE);
            alphaView.animate()
                    .alpha(ALPHA_ZERO)
                    .setInterpolator(mEnterInterpolator)
                    .setDuration(ANIM_CHECKBOX_DURATION)
                    .start();
        }

    }

    public void startItemViewAnimation(final View goneView, final View visibleView, final View translationView, int translationX) {
        if (mAnimationStart) {
            translationView.setTranslationX(mIsRtl ? -translationX : translationX);
            translationView.animate()
                    .setInterpolator(mEnterInterpolator)
                    .setDuration(ANIM_TRANSLATION_DURATION)
                    .translationX(0)
                    .setListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            mAnimatingItemCount--;
                            translationView.setTranslationX(0);
                            mAnimationStart = false;
                        }

                        @Override
                        public void onAnimationStart(Animator animation) {
                            mAnimatingItemCount++;
                        }
                    })
                    .start();
            goneView.setAlpha(ALPHA_ZERO);
            goneView.setVisibility(View.INVISIBLE);
            visibleView.setAlpha(ALPHA_ZERO);
            visibleView.setVisibility(View.VISIBLE);
            visibleView.animate()
                    .setStartDelay(ANIM_CHECKBOX_OFFSET)
                    .alpha(ALPHA_ONE)
                    .setInterpolator(mEnterInterpolator)
                    .setDuration(ANIM_CHECKBOX_DURATION)
                    .start();
        }

    }

    public void closeItemViewAnimation(final View rootView, final CompoundButton checkBox, int translationX) {
        closeItemViewAnimation(rootView, checkBox, translationX, null);
    }


    public void closeItemViewAnimation(final View alphaView, final View translationView, final float translationX, final OnAnimationCompleteCallback callback) {
        if (mAnimationStart) {
            translationView.setTranslationX(mIsRtl ? -translationX : translationX);
            translationView.setAlpha(ALPHA_ONE);
            translationView.animate()
                    .setInterpolator(mEnterInterpolator)
                    .setDuration(ANIM_CHECKBOX_DURATION)
                    .translationX(0)
                    .alpha(ALPHA_ZERO)
                    .setListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationStart(Animator animator) {
                            mAnimatingItemCount++;
                            if (callback != null) {
                                callback.onAnimationStart();
                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animator) {
                            mAnimatingItemCount--;
                            translationView.setTranslationX(0);
                            translationView.setVisibility(View.GONE);
                            mAnimationStart = false;
                            if (callback != null) {
                                callback.onAnimationComplete();
                            }
                        }
                    })
                    .start();

            alphaView.setAlpha(ALPHA_ZERO);
            if (alphaView.getVisibility() != View.VISIBLE) {
                alphaView.setVisibility(View.VISIBLE);
            }
            alphaView.animate()
                    .setInterpolator(mEnterInterpolator)
                    .alpha(ALPHA_ONE)
                    .setDuration(ANIM_CHECKBOX_DURATION)
                    .start();

        }

    }

    public void closeItemViewAnimation(final View goneView, final View visibleView, final View translationView, int translationX, boolean isNormalScreen) {
        if (mAnimationStart) {
            int translation = mIsRtl ? -translationX : translationX;
            if (isNormalScreen) {
                translationView.setTranslationX(translation);
                mAnimationStart = false;
                goneView.setAlpha(ALPHA_ZERO);
                goneView.setVisibility(View.INVISIBLE);
                visibleView.setAlpha(ALPHA_ONE);
                visibleView.setVisibility(View.VISIBLE);
            } else {
                translationView.setTranslationX(0);
                translationView.animate()
                        .setInterpolator(mEnterInterpolator)
                        .setDuration(ANIM_TRANSLATION_DURATION)
                        .translationX(translation)
                        .setListener(new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                mAnimatingItemCount--;
                                translationView.setTranslationX(translation);
                                mAnimationStart = false;
                            }

                            @Override
                            public void onAnimationStart(Animator animation) {
                                mAnimatingItemCount++;
                            }
                        })
                        .start();
                goneView.setAlpha(ALPHA_ZERO);
                goneView.setVisibility(View.INVISIBLE);
                visibleView.setAlpha(ALPHA_ZERO);
                visibleView.setVisibility(View.VISIBLE);
                visibleView.animate()
                        .setStartDelay(ANIM_CHECKBOX_OFFSET)
                        .alpha(ALPHA_ONE)
                        .setInterpolator(mEnterInterpolator)
                        .setDuration(ANIM_CHECKBOX_DURATION)
                        .start();
            }
        }

    }
}
