/****************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OxygenWeatherSingleImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/17
 ** Author: YeWen
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YeWen    2024/06/17     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.util.TypedValue
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.RequiresApi
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.ClockWidgetUtils
import com.coloros.widget.smallweather.OxygenWeatherSingle
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.SCREEN_DISPLAY_MODEL_KEY_DEFAULT
import com.oplus.alarmclock.utils.FoldScreenUtils.getScreenZoomSettingsIndex
import com.oplus.alarmclock.utils.FoldScreenUtils.isFold
import com.oplus.alarmclock.utils.FoldScreenUtils.isRealOslo
import com.oplus.clock.common.utils.Log
import com.oplus.font.OplusFontManager
import java.util.Locale


class OxygenWeatherSingleImpl(context: Context) : BaseWidgetImpl(context, OxygenWeatherSingle::class.java) {

    companion object {
        private const val TAG = "OxygenWeatherSingleImpl"
        /**OOS时钟涉及的元素*/
        private const val OOS_WIDGET_ELEMENT_WEEK = 1
        private const val OOS_WIDGET_ELEMENT_HOUR = 2
        private const val OOS_WIDGET_ELEMENT_DATA = 3
        private const val OOS_WIDGET_ELEMENT_WEATHER = 4
        private const val OOS_WIDGET_ELEMENT_WEATHER_ICON = 5
        private const val OOS_WIDGET_ELEMENT_CITY = 6
        private const val OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP = 7
        private const val OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM = 8
        private const val OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM = 9
        private const val OOS_WIDGET_ELEMENT_WEEK_MARGIN_TOP = 10
        /**华夫n*1的高度*/
        private const val OOS_WIDGET_HEIGHT_MIN = 96
        /**华夫2*n的宽度*/
        private const val OOS_WIDGET_WIDTH_MIN = 140
        /**华夫3*n的宽度*/
        private const val OOS_WIDGET_WIDTH_SMALL = 202
        /**华夫4*n的宽度*/
        private const val OOS_WIDGET_WIDTH_MAX = 257
        /**华夫4*6布局3*n的宽度*/
        private const val OOS_WIDGET_WIDTH_WAFFLE_SIX_SMALL = 238
        /**华夫4*5布局2*n的宽度*/
        private const val OOS_WIDGET_WIDTH_WAFFLE_FIVE_SMALL = 152
        /**雪鹰n*2的高度*/
        private const val OOS_WIDGET_HEIGHT_XUEYING_SMALL = 140
        /**雪鹰n*2的高度*/
        private const val OOS_WIDGET_HEIGHT_XUEYING_HORIZONTAL_MAX = 148
        /**雪鹰3*n的宽度*/
        private const val OOS_WIDGET_HEIGHT_XUEYING_HORIZONTAL_SMALL = 221
        /**华夫n*2的高度*/
        private const val OOS_WIDGET_HEIGHT_SMALL = 170
        /**华夫简易模式n*1的高度*/
        private const val OOS_WIDGET_HEIGHT_SIMPLE_MIN = 130
        /**华夫简易模式n*2的高度*/
        private const val OOS_WIDGET_HEIGHT_SIMPLE_SMALL = 286
        /**华夫简易模式2*n的宽度*/
        private const val OOS_WIDGET_WIDTH_SIMPLE_MIN = 225
        /**华夫简易模式3*n的宽度*/
        private const val OOS_WIDGET_WIDTH_SIMPLE_SMALL = 322
        /**平板2*n的宽度*/
        private const val OOS_WIDGET_WIDTH_TABLE_MIN = 275
        /**缩放系数*/
        private const val SCALING = 0.86f
        /**2*2双时钟竖直布局字体大小缩放系数*/
        private const val TWO_TWO_SCALING = 0.90f
        /**2*2单时钟small显示模式布局字体大小放大系数*/
        private const val SMALL_TWO_TWO_SCALING = 1.2f
        /**OnePlus Sans字体*/
        private const val ONEPLUS_SANS_FLAG = "OnePlus Sans"
    }

    private var curScaling = 1f

    override fun widgetType(): Int {
        return ClockType.WIDGET_TYPE_HOR
    }

    override fun getClockAllOverTheme(): Boolean {
        return false
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun layoutDualClockPortrait(): Int {
        return if (isVerticalDoubleLayout()) {
            R.layout.oxygen_vertical_double_clock_red_widget_view
        } else {
            R.layout.oxygen_horizontal_double_clock_red_widget_view
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun isVerticalDoubleLayout(): Boolean {
       return if ((widgetHeight > OOS_WIDGET_HEIGHT_MIN && widgetWidth < OOS_WIDGET_WIDTH_SMALL)
            || (widgetHeight > OOS_WIDGET_HEIGHT_SMALL
                    && widgetWidth in OOS_WIDGET_WIDTH_SMALL..OOS_WIDGET_WIDTH_MAX)) {
           true
        } else if (isRealOslo() && widgetHeight > OOS_WIDGET_HEIGHT_XUEYING_SMALL
            && widgetWidth in OOS_WIDGET_WIDTH_SMALL..OOS_WIDGET_WIDTH_TABLE_MIN) {
            true
        } else if (isScreenUnfold() && widgetHeight > OOS_WIDGET_HEIGHT_XUEYING_SMALL
            && widgetWidth in OOS_WIDGET_WIDTH_SMALL..OOS_WIDGET_WIDTH_TABLE_MIN) {
            true
        } else {
            false
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun layoutDualClockLand(): Int {
        return layoutDualClockPortrait()
    }

    override fun layoutSingleClockPortrait(): Int {
        return  R.layout.oxygen_one_line_hor_single_clock_red_widget_view_t
    }

    override fun layoutSingleClockLand(): Int {
        return layoutSingleClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun layoutThemeDualClockPortrait(): Int {
        return layoutDualClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun layoutThemeDualClockLand(): Int {
        return layoutDualClockPortrait()
    }

    override fun layoutThemeSingleClockPortrait(): Int {
        return layoutSingleClockPortrait()
    }

    override fun layoutThemeSingleClockLand(): Int {
        return layoutSingleClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun tableLayoutThemeDualClockPortrait(): Int {
        return layoutDualClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun tableLayoutDualClockPortrait(): Int {
        return layoutDualClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun tableLayoutDualClockLand(): Int {
        return layoutDualClockPortrait()
    }

    override fun tableLayoutSingleClockPortrait(): Int {
        return layoutSingleClockPortrait()
    }

    override fun tableLayoutSingleClockLand(): Int {
        return layoutSingleClockPortrait()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun tableLayoutThemeDualClockLand(): Int {
        return layoutDualClockPortrait()
    }

    override fun tableLayoutThemeSingleClockPortrait(): Int {
        return layoutSingleClockPortrait()
    }

    override fun tableLayoutThemeSingleClockLand(): Int {
        return layoutSingleClockPortrait()
    }

    override fun smallestWidgetHeight(): Int {
        return 0
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun updateDateView(remoteViews: RemoteViews, isOverTheme: Boolean, isDualClock: Boolean) {
        super.updateDateView(remoteViews, isOverTheme, isDualClock)
        if (isOverTheme) {
            return
        }

        context.let {
            getScalingFactor(isDualClock)
            Log.d(TAG, "FoldScreenUtils isFold:${isFold()}  isRealOslo : ${isRealOslo()}  isScreenUnfold():${isScreenUnfold()}")
            if (!isDualClock) {
                //单时钟需要重新设置local_week_info格式
                updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                updateDataFormat(
                    remoteViews, R.id.local_week_info,
                    it.getString(R.string.abbrev_wday_week)
                )
                updateSingleView(remoteViews)
            } else {
                updateDualView(remoteViews)
            }
        }
    }

    override fun updateWeatherView(
        remoteViews: RemoteViews,
        isOverTheme: Boolean,
        isDualClock: Boolean
    ) {
        super.updateWeatherView(remoteViews, isOverTheme, isDualClock)
        //Oxygen 单/双时钟都不显示天气描述信息
        val localWeatherInfo = ClockWidgetManager.getInstance().localNewWeatherInfo
        remoteViews.setTextViewText(
            R.id.local_weather_info_txt,
            localWeatherInfo
        )
        val weatherIconResId = ClockWidgetManager.getInstance().weatherIconResId
        if (weatherIconResId != 0) {
            remoteViews.setImageViewBitmap(
                R.id.iv_weather_type,
                ClockWidgetUtils.getWeatherIcon(
                    context,
                    weatherIconResId,
                    ClockWidgetManager.getInstance().curTextColor
                )
            )
        }

        if (isDualClock) {
            remoteViews.setContentDescription(R.id.resident_iv_weather_type, ClockWidgetManager.getInstance().localDescription)
            val residentWeatherIconResId = ClockWidgetManager.getInstance().residentWeatherIconResId
            if (weatherIconResId != 0) {
                remoteViews.setImageViewBitmap(
                    R.id.resident_iv_weather_type,
                    ClockWidgetUtils.getWeatherIcon(
                        context,
                        residentWeatherIconResId,
                        ClockWidgetManager.getInstance().curTextColor
                    )
                )
            }

            val weatherIntent = Intent(context, clazz)
            weatherIntent.action = ClockWidgetManager.WEATHER_CLICK_ACTION
            weatherIntent.putExtra(ClockWidgetManager.IS_RESIDENT_KEY, true)
            remoteViews.setOnClickPendingIntent(
                R.id.resident_iv_weather_type,
                getPendingIntent(weatherIntent, 0)
            )

            val residentWeatherInfo = ClockWidgetManager.getInstance().residentWeatherTemp
            remoteViews.setTextViewText(
                R.id.resident_weather_info_txt,
                residentWeatherInfo
            )
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun isScreenUnfold(): Boolean {
        context.let {
            return FoldScreenUtils.isScreenUnfold(it) && !FoldScreenUtils.isRealOslo()
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun updateSingleSpacingViewUnfold(remoteViews: RemoteViews?) {
        Log.i(TAG, "updateSingleView : isScreenUnfold = true")
        remoteViews?.setViewLayoutMarginDimen(
            R.id.local_weather_description,
            RemoteViews.MARGIN_TOP, 0
        )
    }

    /**
     * OOS插件缩放需要重新适配布局
     */
    @RequiresApi(Build.VERSION_CODES.S)
    private fun updateSingleView(remoteViews: RemoteViews?) {
        Log.i(TAG, "updateSingleView : widgetWidth = $widgetWidth widgetHeight = $widgetHeight")
        context.let {
            if (widgetHeight != 0 && widgetWidth != 0) {
                val sizeType = if (widgetHeight <= OOS_WIDGET_HEIGHT_MIN || widgetWidth < OOS_WIDGET_WIDTH_MIN) {
                    //华夫小号字体
                    0
                } else if (widgetWidth in OOS_WIDGET_WIDTH_MIN..OOS_WIDGET_WIDTH_SMALL) {
                    //华夫中号字体
                    1
                } else if (widgetHeight in OOS_WIDGET_HEIGHT_MIN..OOS_WIDGET_HEIGHT_SIMPLE_MIN
                    && widgetWidth > OOS_WIDGET_WIDTH_SMALL) {
                    //简易模式小号字体
                    0
                } else if (widgetHeight in OOS_WIDGET_HEIGHT_SMALL..OOS_WIDGET_HEIGHT_SIMPLE_SMALL
                    && widgetWidth in OOS_WIDGET_WIDTH_SMALL..OOS_WIDGET_WIDTH_SIMPLE_MIN) {
                    //简易模式中号字体
                    1
                } else {
                    2
                }
                if (sizeType == 0 && isScreenUnfold()) {
                    //判断是不是折叠展开状态，移除间距
                    updateSingleSpacingViewUnfold(remoteViews)
                }
                Log.i(TAG, "updateSingleView sizeType= $sizeType")
                val margin: Map<Int, Int>? = getSingleWidgetElementMargin(sizeType)
                if (margin != null) {
                    remoteViews?.setViewLayoutMarginDimen(R.id.ll_local_time_container,
                        RemoteViews.MARGIN_TOP, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP]!!)
                    remoteViews?.setViewLayoutMarginDimen(R.id.ll_local_time_container,
                        RemoteViews.MARGIN_BOTTOM, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM]!!)
                }
                val elements: Map<Int, Int>? = getSingleWidgetElementTextSize(sizeType)
                if (elements != null) {
                    //星期
                    remoteViews?.setTextViewTextSize(
                        R.id.local_week_info, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEEK]!!) * curScaling
                    )

                    //时钟小时
                    remoteViews?.setTextViewTextSize(
                        R.id.local_hour_txt, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling
                    )

                    //时钟分隔符：
                    remoteViews?.setTextViewTextSize(
                        R.id.local_colon_txt, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling
                    )

                    if (isOnePlusSans() || isArabicLanguage()) {
                        remoteViews?.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, getColonMarginBottom(sizeType, false))
                    }

                    //时钟分钟
                    remoteViews?.setTextViewTextSize(
                        R.id.local_minutes_txt, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling
                    )

                    // 日期
                    remoteViews?.setTextViewTextSize(
                        R.id.local_date_info_txt, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_DATA]!!) * curScaling
                    )

                    //天气信息
                    remoteViews?.setTextViewTextSize(
                        R.id.local_weather_info_txt, TypedValue.COMPLEX_UNIT_PX,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER]!!) * curScaling
                    )

                    //天气图标
                    remoteViews?.setViewLayoutWidth(
                        R.id.iv_weather_type,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!) * curScaling,
                        TypedValue.COMPLEX_UNIT_PX)
                    remoteViews?.setViewLayoutHeight(R.id.iv_weather_type,
                        it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!) * curScaling,
                        TypedValue.COMPLEX_UNIT_PX)
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun updateDualView(remoteViews: RemoteViews?) {
        Log.i(TAG, "updateDualView : widgetWidth = $widgetWidth widgetHeight = $widgetHeight")
        context.let {
            if (widgetHeight != 0 && widgetWidth != 0 && remoteViews != null) {
                var sizeType = 0
                if (widgetHeight <= OOS_WIDGET_HEIGHT_MIN) {
                    sizeType = 0
                    if (widgetWidth <= OOS_WIDGET_WIDTH_WAFFLE_FIVE_SMALL) {
                        remoteViews.setViewVisibility(R.id.local_date_info_txt, View.GONE)
                        remoteViews.setViewVisibility(R.id.resident_date_info_txt, View.GONE)
                    } else {
                        remoteViews.setViewVisibility(R.id.local_date_info_txt, View.VISIBLE)
                        remoteViews.setViewVisibility(R.id.resident_date_info_txt, View.VISIBLE)
                        updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                        updateDataFormatNoWeak(remoteViews, R.id.resident_date_info_txt)
                    }
                } else if (widgetHeight in OOS_WIDGET_HEIGHT_MIN..OOS_WIDGET_HEIGHT_SIMPLE_MIN
                    && widgetWidth > OOS_WIDGET_WIDTH_SMALL) {
                    sizeType = 0
                    remoteViews.setViewVisibility(R.id.local_date_info_txt, View.VISIBLE)
                    remoteViews.setViewVisibility(R.id.resident_date_info_txt, View.VISIBLE)
                    updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                    updateDataFormatNoWeak(remoteViews, R.id.resident_date_info_txt)
                } else {
                    remoteViews.setViewVisibility(R.id.local_date_info_txt, View.VISIBLE)
                    remoteViews.setViewVisibility(R.id.resident_date_info_txt, View.VISIBLE)
                    if (isScreenUnfold() && widgetHeight <= OOS_WIDGET_HEIGHT_XUEYING_HORIZONTAL_MAX
                        && widgetWidth <= OOS_WIDGET_HEIGHT_XUEYING_HORIZONTAL_SMALL) {
                        sizeType = 0
                        updateDataFormat(remoteViews, R.id.local_date_info_txt, getDataEMDFormat())
                        updateDataFormat(remoteViews, R.id.resident_date_info_txt, getDataEMDFormat())
                    } else if (widgetWidth < OOS_WIDGET_WIDTH_MIN) {
                        sizeType = 1
                        updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                        updateDataFormatNoWeak(remoteViews, R.id.resident_date_info_txt)
                    } else if (widgetWidth in OOS_WIDGET_WIDTH_MIN..OOS_WIDGET_WIDTH_WAFFLE_SIX_SMALL) {
                        sizeType = 1
                        updateDataFormat(remoteViews, R.id.local_date_info_txt, getDataEMDFormat())
                        updateDataFormat(remoteViews, R.id.resident_date_info_txt, getDataEMDFormat())
                    } else if (widgetHeight in OOS_WIDGET_HEIGHT_SIMPLE_MIN..OOS_WIDGET_HEIGHT_SIMPLE_SMALL
                        && (widgetWidth in OOS_WIDGET_WIDTH_SMALL..OOS_WIDGET_WIDTH_SIMPLE_MIN)) {
                        sizeType = 2
                        updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                        updateDataFormatNoWeak(remoteViews, R.id.resident_date_info_txt)
                    } else {
                        sizeType = 2
                        updateDataFormat(remoteViews, R.id.local_date_info_txt, getDataEMDFormat())
                        updateDataFormat(remoteViews, R.id.resident_date_info_txt, getDataEMDFormat())
                    }
                }
                Log.i(TAG, "updateDualView sizeType= $sizeType")
                updateDualViewHourMargin(sizeType, remoteViews)
                updateDualViewTextSize(sizeType, remoteViews)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun updateDualViewHourMargin(sizeType: Int, remoteViews: RemoteViews) {
        val margin: Map<Int, Int>? = getDualWidgetElementMargin(sizeType)
        if (margin != null) {
            remoteViews?.setViewLayoutMarginDimen(R.id.ll_local_time_container,
                RemoteViews.MARGIN_TOP, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP]!!)
            remoteViews?.setViewLayoutMarginDimen(R.id.ll_local_time_container,
                RemoteViews.MARGIN_BOTTOM, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM]!!)
            remoteViews?.setViewLayoutMarginDimen(R.id.ll_resident_time_container,
                RemoteViews.MARGIN_TOP, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP]!!)
            remoteViews?.setViewLayoutMarginDimen(R.id.ll_resident_time_container,
                RemoteViews.MARGIN_BOTTOM, margin[OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM]!!)
            remoteViews?.setViewLayoutMarginDimen(R.id.resident_city_txt,
                RemoteViews.MARGIN_BOTTOM, margin[OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM]!!)
            remoteViews?.setViewLayoutMarginDimen(R.id.local_city_txt,
                RemoteViews.MARGIN_BOTTOM, margin[OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM]!!)
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun updateDualViewTextSize(sizeType: Int, remoteViews: RemoteViews) {
        context?.let {
            val elements: Map<Int, Int>? = getDualWidgetElementTextSize(sizeType)
            if (elements != null) {
                //时钟小时
                updateTextViewSize(remoteViews, R.id.local_hour_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_hour_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling)

                //时钟分隔符:
                updateTextViewSize(remoteViews, R.id.local_colon_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_colon_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!) * curScaling)
                if (isOnePlusSans() || isArabicLanguage()) {
                    remoteViews.setViewLayoutMarginDimen(
                        R.id.local_colon_txt,
                        RemoteViews.MARGIN_BOTTOM, getColonMarginBottom(sizeType, true))
                    remoteViews.setViewLayoutMarginDimen(
                        R.id.resident_colon_txt,
                        RemoteViews.MARGIN_BOTTOM, getColonMarginBottom(sizeType, true))
                }

                //时钟分钟
                updateTextViewSize(remoteViews, R.id.local_minutes_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!)
                        * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_minutes_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_HOUR]!!)
                        * curScaling)

                //城市
                updateTextViewSize(remoteViews, R.id.local_city_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_CITY]!!) * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_city_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_CITY]!!) * curScaling)

                // 日期
                updateTextViewSize(remoteViews, R.id.local_date_info_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_DATA]!!)
                        * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_date_info_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_DATA]!!)
                        * curScaling)

                //天气信息
                updateTextViewSize(remoteViews, R.id.local_weather_info_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER]!!)
                        * curScaling)
                updateTextViewSize(remoteViews, R.id.resident_weather_info_txt, it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER]!!)
                        * curScaling)

                //天气图标
                remoteViews.setViewLayoutWidth(
                    R.id.iv_weather_type,
                    it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!),
                    TypedValue.COMPLEX_UNIT_PX)
                remoteViews.setViewLayoutHeight(R.id.iv_weather_type,
                    it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!),
                    TypedValue.COMPLEX_UNIT_PX)

                remoteViews.setViewLayoutWidth(
                    R.id.resident_iv_weather_type,
                    it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!),
                    TypedValue.COMPLEX_UNIT_PX)
                remoteViews.setViewLayoutHeight(R.id.resident_iv_weather_type,
                    it.resources.getDimension(elements[OOS_WIDGET_ELEMENT_WEATHER_ICON]!!),
                    TypedValue.COMPLEX_UNIT_PX)
            }
        }
    }


    private fun updateTextViewSize(remoteViews: RemoteViews?, units: Int, size: Float) {
        remoteViews?.setTextViewTextSize(units, TypedValue.COMPLEX_UNIT_PX, size)
    }


    private fun getSingleWidgetElementMargin(sizeType: Int): Map<Int, Int>? {
        val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
        Log.i(TAG, "isFlipFontUsed = $isFlipFontUsed")
        if (isFlipFontUsed) {
            return when (sizeType) {
                0 -> {
                    mapOf(
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_single_time_min_margin_flip_font,
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_single_time_min_margin
                    )
                }

                1, 2 -> {
                    mapOf(
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_single_time_min_margin_flip_font,
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_single_time_max_margin
                    )
                }

                else -> null
            }
        } else {
            return when (sizeType) {
                0 -> {
                    mapOf(
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_single_time_min_margin,
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_single_time_min_margin
                    )
                }

                1, 2 -> {
                    mapOf(
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_single_time_max_margin,
                        OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_single_time_max_margin
                    )
                }

                else -> null
            }
        }
    }

    private fun getSingleWidgetElementTextSize(sizeType: Int): Map<Int, Int>? {
       return when (sizeType) {
            0 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_single_clock_week_min_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_single_clock_hour_min_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_single_clock_data_min_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_single_clock_weather_min_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_single_clock_weather_icon_min_size) }

           1 -> { mapOf(
               OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_single_clock_week_middle_size,
               OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_single_clock_hour_middle_size,
               OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_single_clock_data_middle_size,
               OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_single_clock_weather_middle_size,
               OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_single_clock_weather_icon_middle_size) }

           2 -> { mapOf(
               OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_single_clock_week_big_size,
               OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_single_clock_hour_big_size,
               OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_single_clock_data_big_size,
               OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_single_clock_weather_big_size,
               OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_single_clock_weather_icon_big_size) }

           else -> null
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun getScalingFactor(isDualClock: Boolean) {
        Log.d(TAG, "getScreenZoomSettingsIndex : ${getScreenZoomSettingsIndex(context)}")
        curScaling = if (getScreenZoomSettingsIndex(context) > SCREEN_DISPLAY_MODEL_KEY_DEFAULT) {
            SCALING
        } else if (getScreenZoomSettingsIndex(context) < SCREEN_DISPLAY_MODEL_KEY_DEFAULT
            && isOnePlusSans()
            && !isDualClock) {
            SMALL_TWO_TWO_SCALING
        } else {
            1f
        }
        curScaling *= if (isVerticalDoubleLayout()) {
            TWO_TWO_SCALING
        } else {
            1f
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun getDualWidgetElementMargin(sizeType: Int): Map<Int, Int>? {
        if (isVerticalDoubleLayout()) {
            return when (sizeType) {
                0 -> { mapOf(
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_vertical_time_top_margin,
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_vertical_time_bottom_margin,
                    OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM to R.dimen.oos_widget_vertical_city_bottom_max_margin) }

                1, 2 -> { mapOf(
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_vertical_time_top_margin,
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_vertical_time_bottom_margin,
                    OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM to R.dimen.oos_widget_vertical_city_bottom_max_margin) }
                else -> null
            }
        } else {
            return when (sizeType) {
                0 -> { mapOf(
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_horizontal_time_top_margin,
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_horizontal_time_bottom_min_margin,
                    OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM to R.dimen.oos_widget_city_bottom_margin) }

                1, 2 -> { mapOf(
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_TOP to R.dimen.oos_widget_horizontal_time_top_margin,
                    OOS_WIDGET_ELEMENT_HOUR_MARGIN_BOTTOM to R.dimen.oos_widget_horizontal_time_bottom_max_margin,
                    OOS_WIDGET_ELEMENT_CITY_MARGIN_BOTTOM to R.dimen.oos_widget_horizontal_city_bottom_max_margin) }

                else -> null
            }
        }
    }

    private fun getDualWidgetElementTextSize(sizeType: Int): Map<Int, Int>? {
        if (isFold()) {
            return getFoldDualWidgetElementTextSize(sizeType)
        }
        return getDefaultDualWidgetElementTextSize(sizeType)
    }

    private fun getFoldDualWidgetElementTextSize(sizeType: Int): Map<Int, Int>? {
        return when (sizeType) {
            0 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_horizontal_dual_clock_week_min_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_horizontal_dual_clock_hour_min_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_horizontal_dual_clock_data_min_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_horizontal_dual_clock_weather_min_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_horizontal_dual_clock_weather_icon_min_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_horizontal_dual_clock_city_min_size) }

            1 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_vertical_fold_dual_clock_week_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_vertical_fold_dual_clock_hour_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_vertical_fold_dual_clock_data_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_vertical_fold_dual_clock_weather_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_vertical_fold_dual_clock_weather_icon_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_vertical_fold_dual_clock_city_size) }

            2 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_horizontal_dual_clock_week_max_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_horizontal_dual_clock_hour_max_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_horizontal_dual_clock_data_max_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_horizontal_dual_clock_weather_max_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_horizontal_dual_clock_weather_icon_max_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_horizontal_dual_clock_city_max_size) }

            else -> null
        }
    }

    private fun getDefaultDualWidgetElementTextSize(sizeType: Int): Map<Int, Int>? {
       return when (sizeType) {
            0 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_horizontal_dual_clock_week_min_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_horizontal_dual_clock_hour_min_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_horizontal_dual_clock_data_min_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_horizontal_dual_clock_weather_min_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_horizontal_dual_clock_weather_icon_min_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_horizontal_dual_clock_city_min_size) }

            1 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_vertical_dual_clock_week_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_vertical_dual_clock_hour_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_vertical_dual_clock_data_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_vertical_dual_clock_weather_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_vertical_dual_clock_weather_icon_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_vertical_dual_clock_city_size) }

            2 -> { mapOf(
                OOS_WIDGET_ELEMENT_WEEK to R.dimen.oos_widget_horizontal_dual_clock_week_max_size,
                OOS_WIDGET_ELEMENT_HOUR to R.dimen.oos_widget_horizontal_dual_clock_hour_max_size,
                OOS_WIDGET_ELEMENT_DATA to R.dimen.oos_widget_horizontal_dual_clock_data_max_size,
                OOS_WIDGET_ELEMENT_WEATHER to R.dimen.oos_widget_horizontal_dual_clock_weather_max_size,
                OOS_WIDGET_ELEMENT_WEATHER_ICON to R.dimen.oos_widget_horizontal_dual_clock_weather_icon_max_size,
                OOS_WIDGET_ELEMENT_CITY to R.dimen.oos_widget_horizontal_dual_clock_city_max_size) }

            else -> null
        }
    }

    private fun getColonMarginBottom(sizeType: Int, isDualClock: Boolean): Int {
        return when (sizeType) {
            0 -> if (isDualClock) R.dimen.layout_dp_2 else R.dimen.layout_dp_3
            else -> if (isDualClock) R.dimen.layout_dp_4 else R.dimen.layout_dp_7
        }
    }

    override fun isTableOwnerView(): Boolean {
        return true
    }

    private fun isOnePlusSans(): Boolean {
        return Settings.System.getString(context.contentResolver,
            "current_typeface_name") == ONEPLUS_SANS_FLAG
    }

    private fun isArabicLanguage(): Boolean {
        return "ar" == Locale.getDefault().language
    }
}