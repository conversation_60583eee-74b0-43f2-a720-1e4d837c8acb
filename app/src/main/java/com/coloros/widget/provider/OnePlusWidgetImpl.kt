/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OnePlusWidgetImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.text.TextUtils
import android.util.TypedValue
import android.widget.RemoteViews
import androidx.annotation.RequiresApi
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.OnePlusWidget
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.font.OplusFontManager

/**
 * 一加插件
 */
class OnePlusWidgetImpl(context: Context) : BaseWidgetImpl(context, OnePlusWidget::class.java) {
    companion object {
        private const val TAG = "ClockWidget.1PlusWidget"
    }

    override fun smallestWidgetHeight(): Int {
        return 0
    }

    override fun widgetType(): Int {
        return ClockType.WIDGET_TYPE_HOR
    }

    override fun layoutDualClockPortrait(): Int {
        return if (DeviceUtils.hasOplusTextClock()) {
            R.layout.op_double_clock_red_widget_view
        } else {
            R.layout.op_double_clock_widget_view
        }
    }

    override fun layoutDualClockLand(): Int {
        return if (DeviceUtils.hasOplusTextClock()) {
            R.layout.op_double_clock_red_widget_land_view
        } else {
            R.layout.op_double_clock_widget_land_view
        }
    }

    override fun layoutSingleClockPortrait(): Int {
        return if (DeviceUtils.hasOplusTextClock()) {
            R.layout.one_plus_red_widget_view
        } else {
            R.layout.one_plus_widget_view
        }
    }

    override fun layoutSingleClockLand(): Int {
        return if (DeviceUtils.hasOplusTextClock()) {
            R.layout.one_plus_red_widget_land_view
        } else {
            R.layout.one_plus_widget_land_view
        }
    }

    override fun layoutThemeDualClockPortrait(): Int {
        return R.layout.op_double_clock_overall_theme_widget_view
    }

    override fun layoutThemeDualClockLand(): Int {
        return R.layout.op_double_clock_overall_theme_widget_land_view
    }

    override fun layoutThemeSingleClockPortrait(): Int {
        return R.layout.one_plus_overall_theme_widget_view
    }

    override fun layoutThemeSingleClockLand(): Int {
        return R.layout.one_plus_overall_theme_widget_view
    }

    override fun updateDateView(remoteViews: RemoteViews, isOverTheme: Boolean, isDualClock: Boolean) {
        if (!isDualClock) {
            if (isOverTheme) {
                val dateInfo = ClockWidgetManager.getInstance().localMonthDay
                if (!TextUtils.isEmpty(dateInfo)) {
                    remoteViews.setTextViewText(R.id.local_date_info_txt, dateInfo)
                }
                val weekInfo = ClockWidgetManager.getInstance().localWeekDay
                if (!TextUtils.isEmpty(weekInfo)) {
                    remoteViews.setTextViewText(R.id.local_week_info, weekInfo)
                }
            } else {
                context.let {
                    updateDataFormatNoWeak(remoteViews, R.id.local_date_info_txt)
                    updateDataFormat(
                        remoteViews,
                        R.id.local_week_info,
                        it.getString(R.string.abbrev_wday_week)
                    )
                }
            }
        } else {
            super.updateDateView(remoteViews, isOverTheme, isDualClock)
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun updateWidgetMargin(
        remoteViews: RemoteViews,
        isDualClock: Boolean,
        isPortrait: Boolean,
        ratio: Float
    ) {
        super.updateWidgetMargin(remoteViews, isDualClock, isPortrait, ratio)
        if (isClockAllOverTheme) {
            return
        }
        kotlin.runCatching {
            val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
            if (isFlipFontUsed) {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.ll_local_time_container,
                            R.dimen.hor_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_layout,
                            R.dimen.hor_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.ll_resident_time_container,
                            R.dimen.hor_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_resident_time_container,
                            R.dimen.hor_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                    } else {
                        setLayoutMarginTop(
                            R.id.ll_local_time_layout,
                            R.dimen.one_plus_time_week_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_layout,
                            R.dimen.one_plus_time_date_flip_font_margin_v,
                            ratio
                        )
                    }
                }
            } else {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.ll_local_time_container,
                            R.dimen.hor_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.ll_resident_time_container,
                            R.dimen.hor_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_layout,
                            R.dimen.hor_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_resident_time_container,
                            R.dimen.hor_double_time_weather_margin_v,
                            ratio
                        )
                    } else {
                        setLayoutMarginTop(
                            R.id.ll_local_time_layout,
                            R.dimen.one_plus_time_week_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_layout,
                            R.dimen.one_plus_time_date_margin_v,
                            ratio
                        )
                    }
                }
            }
        }.onFailure {
            Log.d(TAG, "OplusFontManager get font is flip failed due to  ${it.message}")
            return
        }
    }

    @SuppressLint("NewApi")
    override fun updateTimeColonMargin(remoteViews: RemoteViews, isDualClock: Boolean): Boolean {
        if (!Utils.isAboveOS13()) {
            super.updateTimeColonMargin(remoteViews, isDualClock)
            return true
        }
        kotlin.runCatching {
            val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
            if (isFlipFontUsed || ClockWidgetManager.TIME_POINT == ClockWidgetManager.getInstance()
                    .getmTimeSeparator()
            ) {
                Log.d(TAG, "update colon flip font")
                remoteViews.setViewLayoutMarginDimen(
                    R.id.local_colon_txt,
                    RemoteViews.MARGIN_BOTTOM, 0
                )
                remoteViews.setViewLayoutMarginDimen(
                    R.id.resident_colon_txt,
                    RemoteViews.MARGIN_BOTTOM, 0
                )
            } else {
                Log.d(TAG, "update colon default font")
                if (isDualClock) {
                    if (FoldScreenUtils.isRealOslo()) {
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.layout_dp_7
                        )
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.resident_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.layout_dp_7
                        )
                    } else {
                        if (isPortrait) {  //op_double_clock_widget_view;
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.local_colon_txt,
                                RemoteViews.MARGIN_BOTTOM, R.dimen.layout_dp_6
                            )
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.resident_colon_txt,
                                RemoteViews.MARGIN_BOTTOM, R.dimen.layout_dp_6
                            )
                        }
                        //直板机双时钟横屏下没有local_colon_txt，暂不处理
                    }
                } else {
                    if (isPortrait) {  //one_plus_widget_view;
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.one_plus_colon_margin_bottom
                        )
                    }
                    //直板机单时钟横屏下没有local_colon_txt，暂不处理
                }
            }
            return true
        }.onFailure {
            return false
        }
        return false
    }

    override fun hasWeatherIcon(isDualClock: Boolean): Boolean {
        return false
    }

    public override fun widgetElementTextSize(isDualClock: Boolean, isPortrait: Boolean): Map<Int, Int> {
        return if (isDualClock) {
            if (isPortrait) {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.hor_double_time_size,
                    WIDGET_ELEMENT_COLON to R.dimen.hor_double_time_size,
                    WIDGET_ELEMENT_DATE to R.dimen.hor_double_date_weather_size,
                    WIDGET_ELEMENT_WEATHER to R.dimen.hor_double_date_weather_size,
                    WIDGET_ELEMENT_CITY to R.dimen.hor_double_date_weather_size,
                )
            } else {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.time_hour_txt_sz_land_t,
                    WIDGET_ELEMENT_COLON to R.dimen.time_hour_txt_sz_land_t,
                    WIDGET_ELEMENT_DATE to R.dimen.date_info_txt_sz,
                    WIDGET_ELEMENT_WEATHER to R.dimen.weather_info_txt_sz,
                    WIDGET_ELEMENT_CITY to R.dimen.local_city_txt_sz
                )
            }
        } else {
            if (isPortrait) {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.one_plus_time_size,
                    WIDGET_ELEMENT_COLON to R.dimen.one_plus_time_size,
                    WIDGET_ELEMENT_DATE to R.dimen.one_plus_date_size,
                    WIDGET_ELEMENT_WEEK to R.dimen.one_plus_week_size
                )
            } else {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.hor_single_clock_time_land_font_size_t,
                    WIDGET_ELEMENT_COLON to R.dimen.hor_single_clock_time_land_font_size_t,
                    WIDGET_ELEMENT_DATE to R.dimen.hor_single_clock_date_font_size,
                    WIDGET_ELEMENT_WEATHER to R.dimen.hor_single_clock_weather_font_size2
                )
            }
        }
    }
}