/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - IWidgetProxy.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.widget.RemoteViews

interface IWidgetProxy {
    /**
     * 获取插件的最小可显示高度(单位dp),用于处理OTA升级或者搬家场景，插件高度不够，显示不全的情况
     *
     * @return 插件最小可显示高度 -1表示不处理
     */
    fun smallestWidgetHeight(): Int

    fun createRemoteViews(): RemoteViews?

    fun updateRemoteViews(small: Boolean): RemoteViews?

    /**
     * 缩放一加定制插件,需要更新布局
     */
    fun updateOOSRemoteViews(width: Int, height: Int): RemoteViews?
}