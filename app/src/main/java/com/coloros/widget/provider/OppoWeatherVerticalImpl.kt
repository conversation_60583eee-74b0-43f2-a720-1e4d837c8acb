/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OppoWeatherVerticalImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.RequiresApi
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.OppoWeatherVertical
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.clock.common.utils.Log
import com.oplus.font.OplusFontManager

/**
 * 竖版插件
 */
class OppoWeatherVerticalImpl(
    context: Context
) : BaseWidgetImpl(context, OppoWeatherVertical::class.java) {
    companion object {
        private const val TAG = "ClockWidget.OplusWeatherVertical"
        private const val SMALLEST_WIDGET_HEIGHT = 220
    }

    override fun smallestWidgetHeight(): Int {
        return SMALLEST_WIDGET_HEIGHT
    }

    override fun widgetType(): Int {
        return ClockType.WIDGET_TYPE_VERTICAL
    }

    override fun layoutDualClockPortrait(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.vertical_double_clock_red_widget_view_t
        } else {
            R.layout.vertical_double_clock_widget_view_t
        }
    }

    override fun layoutDualClockLand(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.vertical_double_clock_red_widget_land_view_t
        } else {
            R.layout.vertical_double_clock_widget_land_view_t
        }
    }

    override fun layoutSingleClockPortrait(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.vertical_single_clock_red_widget_view_t
        } else {
            R.layout.vertical_single_clock_widget_view_t
        }
    }

    override fun layoutSingleClockLand(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.vertical_single_clock_red_widget_land_view_t
        } else {
            R.layout.vertical_single_clock_widget_land_view_t
        }
    }

    override fun layoutThemeDualClockPortrait(): Int {
        return R.layout.vertical_double_clock_overall_theme_widget_view
    }

    override fun layoutThemeDualClockLand(): Int {
        return R.layout.vertical_double_clock_overall_theme_widget_land_view
    }

    override fun layoutThemeSingleClockPortrait(): Int {
        return R.layout.vertical_single_clock_overall_theme_widget_view
    }

    override fun layoutThemeSingleClockLand(): Int {
        return R.layout.vertical_single_clock_overall_theme_widget_land_view
    }

    override fun setTimeClickEvent(
        remoteViews: RemoteViews,
        isOverTheme: Boolean,
        isDualClock: Boolean
    ) {
        val intent = timePendingIntent()
        remoteViews.setOnClickPendingIntent(R.id.time_layout, intent)
        if (isDualClock) {
            remoteViews.setOnClickPendingIntent(R.id.resident_time_layout, intent)
        }
    }

    override fun updateThemeTimeView(remoteViews: RemoteViews, isDualClock: Boolean) {
        remoteViews.setContentDescription(
                R.id.time_layout,
                ClockWidgetManager.getInstance().localFullTime
        )
        remoteViews.setViewVisibility(R.id.time_layout, View.VISIBLE)
        if (isDualClock) {
            remoteViews.setContentDescription(
                    R.id.resident_time_layout,
                    ClockWidgetManager.getInstance().residentFullTime
            )
            remoteViews.setViewVisibility(R.id.resident_time_layout, View.VISIBLE)
        }
        if (!updateLocalTimeResource(remoteViews) && isDualClock) {
            updateResidentTimeResource(remoteViews)
        }
    }

    override fun hasWeatherIcon(isDualClock: Boolean): Boolean {
        return true
    }

    override fun hasWeatherDescription(isOverTheme: Boolean, isDualClock: Boolean): Boolean {
        return !isDualClock
    }

    override fun getLocalWeatherInfo(isDualClock: Boolean): String? {
        return ClockWidgetManager.getInstance().localNewWeatherInfo
    }

    override fun getResidentWeatherInfo(isDualClock: Boolean): String? {
        return if (isDualClock) {
            ClockWidgetManager.getInstance().residentWeatherTemp
        } else {
            ClockWidgetManager.getInstance().residentWeatherInfo
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun updateWidgetMargin(
        remoteViews: RemoteViews,
        isDualClock: Boolean,
        isPortrait: Boolean,
        ratio: Float
    ) {
        super.updateWidgetMargin(remoteViews, isDualClock, isPortrait, ratio)
        if (isClockAllOverTheme) {
            return
        }
        kotlin.runCatching {
            val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
            if (isFlipFontUsed) {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.local_minutes_txt,
                            R.dimen.vertical_double_time_minutes_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.resident_minutes_txt,
                            R.dimen.vertical_double_time_minutes_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.time_layout,
                            R.dimen.vertical_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.time_layout,
                            R.dimen.vertical_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.resident_time_layout,
                            R.dimen.vertical_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.resident_time_layout,
                            R.dimen.vertical_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                    } else {
                        setLayoutMarginTop(
                            R.id.local_minutes_txt,
                            R.dimen.vertical_time_minutes_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.time_layout,
                            R.dimen.vertical_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.local_weather_description,
                            R.dimen.vertical_date_weather_flip_font_margin_v,
                            ratio
                        )
                    }
                }
            } else {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.local_minutes_txt,
                            R.dimen.vertical_double_time_minutes_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.resident_minutes_txt,
                            R.dimen.vertical_double_time_minutes_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.time_layout,
                            R.dimen.vertical_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.resident_time_layout,
                            R.dimen.vertical_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.time_layout,
                            R.dimen.vertical_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.resident_time_layout,
                            R.dimen.vertical_double_time_weather_margin_v,
                            ratio
                        )
                    } else {
                        setLayoutMarginTop(
                            R.id.local_minutes_txt,
                            R.dimen.vertical_time_minutes_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.time_layout,
                            R.dimen.vertical_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.local_weather_description,
                            R.dimen.vertical_date_weather_margin_v,
                            ratio
                        )
                    }
                }
            }
        }.onFailure {
            Log.d(TAG, "OplusFontManager get font is flip failed due to  ${it.message}")
            return
        }
    }

    public override fun widgetElementTextSize(isDualClock: Boolean, isPortrait: Boolean): Map<Int, Int> {
        return if (isDualClock) {
            if (isPortrait) {
                mapOf(
                        WIDGET_ELEMENT_TIME to R.dimen.vertical_double_time_size,
                        WIDGET_ELEMENT_DATE to R.dimen.vertical_double_date_weather_size,
                        WIDGET_ELEMENT_WEATHER to R.dimen.vertical_double_date_weather_size,
                        WIDGET_ELEMENT_CITY to R.dimen.vertical_double_date_weather_size,
                )
            } else {
                mapOf(
                        WIDGET_ELEMENT_TIME to R.dimen.vertical_time_font_size_horizontal_t,
                        WIDGET_ELEMENT_DATE to R.dimen.vertical_double_clock_weather_font_size,
                        WIDGET_ELEMENT_WEATHER to R.dimen.vertical_double_clock_city_font_size2,
                        WIDGET_ELEMENT_CITY to R.dimen.vertical_double_clock_weather_font_size
                )
            }
        } else {
            if (isPortrait) {
                mapOf(
                        WIDGET_ELEMENT_TIME to R.dimen.vertical_time_size,
                        WIDGET_ELEMENT_DATE to R.dimen.vertical_date_weather_size,
                        WIDGET_ELEMENT_WEATHER to R.dimen.vertical_date_weather_size
                )
            } else {
                mapOf(
                        WIDGET_ELEMENT_TIME to R.dimen.vertical_time_font_size_horizontal_t,
                        WIDGET_ELEMENT_DATE to R.dimen.vertical_single_clock_weather_font_size_t,
                        WIDGET_ELEMENT_WEATHER to R.dimen.vertical_single_clock_weather_font_size_t
                )
            }
        }
    }
}