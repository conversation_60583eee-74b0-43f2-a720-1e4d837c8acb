/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OppoWeatherSingleImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Build
import android.widget.RemoteViews
import androidx.annotation.RequiresApi
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.OppoWeatherSingle
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.font.OplusFontManager

class OppoWeatherSingleImpl(context: Context) : BaseWidgetImpl(context, OppoWeatherSingle::class.java) {
    companion object {
        private const val TAG = "ClockWidget.OppoWeatherSingle"
        private const val SMALLEST_WIDGET_HEIGHT = 72
        private const val SMALL_WIDGET_SCALE = 0.75f
    }

    override fun smallestWidgetHeight(): Int {
        return SMALLEST_WIDGET_HEIGHT
    }

    override fun smallWidgetScale(): Float {
        return SMALL_WIDGET_SCALE
    }

    override fun widgetType(): Int {
        return ClockType.WIDGET_TYPE_HOR
    }

    override fun layoutDualClockPortrait(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.one_line_double_clock_red_widget_view_t
        } else {
            R.layout.one_line_double_clock_widget_view_t
        }
    }

    override fun layoutDualClockLand(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.one_line_double_clock_red_widget_land_view_t
        } else {
            R.layout.one_line_double_clock_widget_land_view_t
        }
    }

    override fun layoutSingleClockPortrait(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.one_line_hor_single_clock_red_widget_view_t
        } else {
            R.layout.one_line_hor_single_clock_widget_view_t
        }
    }

    override fun layoutSingleClockLand(): Int {
        return if (DeviceUtils.isSupportRedOne()) {
            R.layout.one_line_hor_single_clock_red_widget_land_view_t
        } else {
            R.layout.one_line_hor_single_clock_widget_land_view_t
        }
    }

    override fun layoutThemeDualClockPortrait(): Int {
        return R.layout.one_line_double_clock_overall_theme_widget_view
    }

    override fun layoutThemeDualClockLand(): Int {
        return R.layout.one_line_double_clock_overall_theme_widget_land_view
    }

    override fun layoutThemeSingleClockPortrait(): Int {
        return R.layout.one_line_single_clock_overall_theme_widget_view
    }

    override fun layoutThemeSingleClockLand(): Int {
        return R.layout.one_line_single_clock_overall_theme_widget_land_view
    }

    override fun setWeatherClickEvent(
        remoteViews: RemoteViews,
        isOverTheme: Boolean,
        isDualClock: Boolean
    ) {
        super.setWeatherClickEvent(remoteViews, isOverTheme, isDualClock)
        if (isDualClock) {
            val weatherIntent = Intent(context, clazz)
            weatherIntent.action = ClockWidgetManager.WEATHER_CLICK_ACTION
            weatherIntent.putExtra(ClockWidgetManager.IS_RESIDENT_KEY, true)
            remoteViews.setOnClickPendingIntent(
                R.id.resident_iv_weather_type,
                getPendingIntent(weatherIntent, 0)
            )
        }
    }

    override fun getLocalWeatherInfo(isDualClock: Boolean): String? {
        return ClockWidgetManager.getInstance().localNewWeatherInfo
    }

    override fun getResidentWeatherInfo(isDualClock: Boolean): String? {
        return ClockWidgetManager.getInstance().residentWeatherTemp
    }

    override fun hasWeatherIcon(isDualClock: Boolean): Boolean {
        return true
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun updateWidgetMargin(
        remoteViews: RemoteViews,
        isDualClock: Boolean,
        isPortrait: Boolean,
        ratio: Float
    ) {
        super.updateWidgetMargin(remoteViews, isDualClock, isPortrait, ratio)
        kotlin.runCatching {
            val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
            if (isFlipFontUsed) {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.ll_local_time_container,
                            R.dimen.one_line_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_container,
                            R.dimen.one_line_double_time_date_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.ll_resident_time_container,
                            R.dimen.one_line_double_time_weather_flip_font_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_resident_time_container,
                            R.dimen.one_line_double_time_date_flip_font_margin_v,
                            ratio
                        )
                    }
                }
            } else {
                remoteViews.apply {
                    if (isDualClock) {
                        setLayoutMarginTop(
                            R.id.ll_local_time_container,
                            R.dimen.one_line_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_local_time_container,
                            R.dimen.one_line_double_time_date_margin_v,
                            ratio
                        )
                        setLayoutMarginTop(
                            R.id.ll_resident_time_container,
                            R.dimen.one_line_double_time_weather_margin_v,
                            ratio
                        )
                        setLayoutMarginBottom(
                            R.id.ll_resident_time_container,
                            R.dimen.one_line_double_time_date_margin_v,
                            ratio
                        )
                    }
                    if (!isDualClock && isPortrait) {
                        remoteViews.apply {
                            setLayoutMarginBottom(
                                R.id.layout_time,
                                R.dimen.one_line_parent_time_margin_v,
                                ratio
                            )
                            setLayoutMarginBottom(
                                R.id.layout_weather,
                                R.dimen.one_line_parent_weather_date_margin_v,
                                ratio
                            )
                        }
                    }
                }
            }
        }.onFailure {
            Log.d(TAG, "OplusFontManager get font is flip failed due to  ${it.message}")
            return
        }
    }

    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @SuppressLint("NewApi")
    override fun updateTimeColonMargin(remoteViews: RemoteViews, isDualClock: Boolean): Boolean {
        if (!Utils.isAboveOS13()) {
            super.updateTimeColonMargin(remoteViews, isDualClock)
            return true
        }
        return try {
            val isFlipFontUsed = OplusFontManager.getInstance().isFlipFontUsed
            if (isFlipFontUsed || ClockWidgetManager.TIME_POINT == ClockWidgetManager.getInstance()
                    .getmTimeSeparator()) {
                Log.d(TAG, "update colon flip font")
                remoteViews.setViewLayoutMarginDimen(
                    R.id.local_colon_txt,
                    RemoteViews.MARGIN_BOTTOM, 0
                )
                remoteViews.setViewLayoutMarginDimen(
                    R.id.resident_colon_txt,
                    RemoteViews.MARGIN_BOTTOM, 0
                )
            } else {
                Log.d(TAG, "update colon default font")
                if (lastClockType == ClockType.DOUBLE_CLOCK) {
                    if (isPortrait) {  //hor_double_clock_widget_view_t;
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM,
                            R.dimen.one_line_hor_double_clock_colon_padding
                        )
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.resident_colon_txt,
                            RemoteViews.MARGIN_BOTTOM,
                            R.dimen.one_line_hor_double_clock_colon_padding
                        )
                    } else { //hor_double_clock_widget_land_view_t
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.hor_double_clock_land_colon_padding
                        )
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.resident_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.hor_double_clock_land_colon_padding
                        )
                    }
                } else {
                    if (isPortrait) {  //hor_single_clock_widget_view_t;
                        if (FoldScreenUtils.isRealOslo()) {
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.local_colon_txt,
                                RemoteViews.MARGIN_TOP,
                                R.dimen.table_one_line_hor_single_clock_colon_padding_top
                            )
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.local_colon_txt,
                                RemoteViews.MARGIN_BOTTOM,
                                R.dimen.table_one_line_hor_single_clock_colon_padding
                            )
                        } else {
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.local_colon_txt,
                                RemoteViews.MARGIN_TOP,
                                R.dimen.one_line_hor_single_clock_colon_padding_top
                            )
                            remoteViews.setViewLayoutMarginDimen(
                                R.id.local_colon_txt,
                                RemoteViews.MARGIN_BOTTOM,
                                R.dimen.one_line_hor_single_clock_colon_padding
                            )
                        }
                    } else { //hor_single_clock_widget_land_view_t
                        remoteViews.setViewLayoutMarginDimen(
                            R.id.local_colon_txt,
                            RemoteViews.MARGIN_BOTTOM, R.dimen.hor_single_clock_land_colon_padding
                        )
                    }
                }
            }
            true
        } catch (e: Exception) {
            Log.d(TAG, "OplusFontManager get font is flip failed!" + e.message)
            false
        } catch (e: NoClassDefFoundError) {
            Log.d(TAG, "OplusFontManager get font is flip failed!" + e.message)
            false
        } catch (e: NoSuchMethodError) {
            Log.d(TAG, "OplusFontManager get font is flip failed!" + e.message)
            false
        }
    }

    public override fun widgetElementTextSize(isDualClock: Boolean, isPortrait: Boolean): Map<Int, Int> {
        return if (isDualClock) {
            if (isPortrait) {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.one_line_double_time_size,
                    WIDGET_ELEMENT_COLON to R.dimen.one_line_double_time_size,
                    WIDGET_ELEMENT_DATE to R.dimen.one_line_double_date_weather_size,
                    WIDGET_ELEMENT_WEATHER to R.dimen.one_line_double_date_weather_size,
                    WIDGET_ELEMENT_CITY to R.dimen.one_line_double_date_weather_size
                )
            } else {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.one_line_time_hour_txt_sz_land_t,
                    WIDGET_ELEMENT_COLON to R.dimen.one_line_time_hour_txt_sz_land_t,
                    WIDGET_ELEMENT_DATE to R.dimen.one_line_date_info_txt_sz,
                    WIDGET_ELEMENT_WEATHER to R.dimen.one_line_weather_info_txt_sz,
                    WIDGET_ELEMENT_CITY to R.dimen.one_line_local_city_txt_sz
                )
            }
        } else {
            if (isPortrait) {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.one_line_hor_single_clock_time_font_size_t,
                    WIDGET_ELEMENT_COLON to R.dimen.one_line_hor_single_clock_colon_font_size_t,
                    WIDGET_ELEMENT_DATE to R.dimen.one_line_hor_single_clock_weather_font_size2,
                    WIDGET_ELEMENT_WEATHER to R.dimen.one_line_hor_single_clock_weather_font_size2
                )
            } else {
                mapOf(
                    WIDGET_ELEMENT_TIME to R.dimen.one_line_hor_single_clock_time_land_font_size_t,
                    WIDGET_ELEMENT_COLON to R.dimen.one_line_hor_single_clock_time_land_font_size_t,
                    WIDGET_ELEMENT_DATE to R.dimen.one_line_hor_single_clock_date_font_size,
                    WIDGET_ELEMENT_WEATHER to R.dimen.one_line_hor_single_clock_date_font_size
                )
            }
        }
    }
}