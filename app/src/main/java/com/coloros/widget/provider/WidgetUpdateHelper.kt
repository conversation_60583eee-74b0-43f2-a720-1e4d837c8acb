/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - WidgetUpdateHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import androidx.annotation.VisibleForTesting
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.OnePlusWidget
import com.coloros.widget.smallweather.OppoWeather
import com.coloros.widget.smallweather.OppoWeatherMultiVertical
import com.coloros.widget.smallweather.OppoWeatherSingle
import com.coloros.widget.smallweather.OppoWeatherVertical
import com.coloros.widget.smallweather.OxygenWeatherSingle
import com.coloros.widget.smallweather.RealmeWeather
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.DisplayUtils
import com.oplus.clock.common.utils.Log

class WidgetUpdateHelper {

    companion object {
        private const val TAG = "WidgetUpdateHelper"
        val instance = Holder.sInstance
    }

    private val context: AlarmClockApplication? by lazy {
        AlarmClockApplication.getInstance()
    }

    var defaultDensity = -1

    private object Holder {
        val sInstance = WidgetUpdateHelper()
    }

    private val widgetClass by lazy {
        getAllWidgetClass()
    }

    fun updateAllWidgets() {
        if (context == null) {
            Log.e(TAG, "updateWidget context is null!")
            return
        }
        Log.d(TAG, "start update all widgets")
        val appWidgetManager = AppWidgetManager.getInstance(context)
        appWidgetManager?.let { manager ->
            widgetClass.forEach {
                updateWidget(context, it, manager)
            }
        }
        Log.d(TAG, "end update all widgets")
    }

    fun updateWidget(context: Context?, clazz: Class<*>, appWidgetManager: AppWidgetManager? = null) {
        if (context == null) {
            Log.e(TAG, "updateWidget context is null!")
            return
        }
        val manager = appWidgetManager ?: AppWidgetManager.getInstance(context)
        val(ids, proxy) = getWidgetAdapter(context, manager, clazz)
        Log.d(TAG, "updateWidget: $ids $clazz")
        if (ids != null && ids.isNotEmpty() && proxy != null) {
            if (proxy.smallestWidgetHeight() > 0) {
                val widgetPairs = checkResizeWidgetIds(
                    manager,
                    ids,
                    proxy.smallestWidgetHeight())

                widgetPairs?.apply {
                    first?.let { normalWidget ->
                        Log.d(TAG, "relayoutAllWidgets normalWidgetIds:${normalWidget.contentToString()}")
                        updateAppWidgets(manager, normalWidget, proxy)
                    }
                    second?.let { smallWidget ->
                        Log.d(TAG, "relayoutAllWidgets smallWidgetIds:${smallWidget.contentToString()}")
                        updateAppWidgets(manager, smallWidget, proxy, true)
                    }
                }
            } else {
                Log.d(TAG, "relayoutAllWidget :${ids.contentToString()}")
                updateAppWidgets(manager, ids, proxy)
            }
        }
    }

    private fun getWidgetAdapter(
        context: Context,
        appWidgetManager: AppWidgetManager,
        clazz: Class<*>?
    ): Pair<IntArray?, IWidgetProxy?> {
        kotlin.runCatching {
            Log.d(TAG, "getWidgetIds: $clazz")
            clazz?.let {
                val ids = appWidgetManager.getAppWidgetIds(ComponentName(context, clazz))
                if (ids.isNotEmpty()) {
                    val widgetView = getWidgetViewProxy(context, clazz)
                    return Pair(ids, widgetView)
                }
            }
        }
        return Pair(null, null)
    }

    @VisibleForTesting
    fun getWidgetViewProxy(context: Context?, clazz: Class<*>?): IWidgetProxy? {
        return context?.let {
            if (defaultDensity == -1) {
                defaultDensity = DisplayUtils.getDefaultDensity()
            }
            Log.d(TAG, "getWidgetViewProxy:$defaultDensity")
            val defaultContext = DisplayUtils.setDensityAndFontScale(it, defaultDensity, 1.0f)
            when (clazz) {
                OppoWeather::class.java -> OppoWeatherImpl(defaultContext!!)
                OppoWeatherSingle::class.java -> OppoWeatherSingleImpl(defaultContext!!)
                OppoWeatherMultiVertical::class.java -> OppoWeatherMultiVerticalImpl(defaultContext!!)
                OppoWeatherVertical::class.java -> OppoWeatherVerticalImpl(defaultContext!!)
                OnePlusWidget::class.java -> OnePlusWidgetImpl(defaultContext!!)
                RealmeWeather::class.java -> RealmeWeatherImpl(it)
                OxygenWeatherSingle::class.java -> OxygenWeatherSingleImpl(it)
                else -> null
            }
        }
    }

    private fun checkResizeWidgetIds(
        widgetManager: AppWidgetManager,
        widgetIds: IntArray?,
        smallestWidgetHeight: Int
    ): Pair<IntArray?, IntArray?>? {
        if (widgetIds == null) {
            Log.e(TAG, "checkResizeWidgetIds widget is null!")
            return null
        }
        Log.d(TAG, "checkResizeWidgetIds widget size: ${widgetIds.size}")
        val smallWidgetList = ArrayList<Int>()
        val widgetList = ArrayList<Int>()
        for (widgetId in widgetIds) {
            val option = widgetManager.getAppWidgetOptions(widgetId)
            if (option != null) {
                val maxHeight = option.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT)
                Log.d(TAG, "checkResizeWidgetIds widget height:$maxHeight")
                if (maxHeight in 1 until smallestWidgetHeight) {
                    smallWidgetList.add(widgetId)
                } else {
                    widgetList.add(widgetId)
                }
            }
        }
        return Pair(widgetList.toIntArray(), smallWidgetList.toIntArray())
    }

    private fun updateAppWidgets(
        appWidgetManager: AppWidgetManager,
        widgetIds: IntArray?,
        widgetProxy: IWidgetProxy?,
        small: Boolean = false
    ) {
        if (widgetIds == null || widgetIds.isEmpty()) {
            Log.e(TAG, "relayoutAllWidgets widget is null or empty!")
            return
        }
        kotlin.runCatching {
            if (widgetProxy is OxygenWeatherSingleImpl) {
                for (widgetId in widgetIds) {
                    val newOptions = appWidgetManager.getAppWidgetOptions(widgetId)
                    val width = newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH)
                    val height = newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT)
                    val remoteView = widgetProxy.updateOOSRemoteViews(width, height)
                    appWidgetManager.updateAppWidget(widgetId, remoteView)
                }
            } else {
                val remoteView = widgetProxy?.updateRemoteViews(small)
                Log.d(TAG, "relayoutAllWidgets widgetIds: ${widgetIds.contentToString()} remoteView:${remoteView?.toString()}")
                appWidgetManager.updateAppWidget(widgetIds, remoteView)
                Log.d(TAG, "relayoutAllWidgets finished")
            }
        }.onFailure {
            Log.e(TAG, "relayoutAllWidgets updateAppWidget error:${it.message}")
        }
    }

    fun updatePartialWidgets(
        context: Context?,
        clazz: Class<*>?,
        degree: Int,
    ) {
        context?.apply {
            kotlin.runCatching {
                val appWidgetManager = AppWidgetManager.getInstance(this)
                if (appWidgetManager == null) {
                    Log.d(TAG, "relayoutPartialWidgets wm is null")
                    return
                }
                val(ids, proxy) = getWidgetAdapter(this, appWidgetManager, clazz)
                if (ids != null && ids.isNotEmpty()) {
                    proxy?.createRemoteViews()?.let {
                        val refreshBitmap = ClockWidgetManager.getInstance().getRefreshImageView(
                            this, degree.toFloat(),
                            ClockWidgetManager.getInstance().refreshIconColor, false
                        )
                        if (refreshBitmap != null) {
                            it.setImageViewBitmap(
                                R.id.local_weather_info_refresh_img,
                                refreshBitmap
                            )
                        }
                        appWidgetManager.partiallyUpdateAppWidget(ids, it)
                        Log.d(TAG, "relayoutPartialWidgets finished")
                    }
                }
            }.onFailure {
                Log.e(TAG, "relayoutPartialWidgets failure ${it.message}")
            }
        }
    }

    private fun getAllWidgetClass(): ArrayList<Class<*>> {
        val widgetClass = ArrayList<Class<*>>()
        widgetClass.add(OppoWeather::class.java)
        widgetClass.add(OppoWeatherSingle::class.java)
        widgetClass.add(OppoWeatherMultiVertical::class.java)
        widgetClass.add(OppoWeatherVertical::class.java)
        context?.let { context ->
            runCatching {
                if (context.resources.getBoolean(R.bool.configure_date_weather_widget_enabled)) {
                    widgetClass.add(RealmeWeather::class.java)
                }
                if (context.resources.getBoolean(R.bool.configure_one_plus_widget_enabled)) {
                    widgetClass.add(OnePlusWidget::class.java)
                }
                if (context.resources.getBoolean(R.bool.configure_oos_clock_weather_widget_enabled)) {
                    widgetClass.add(OxygenWeatherSingle::class.java)
                }
            }.onFailure {
                Log.e(TAG, "getAllWidgetClass failed:${it.message}")
            }
        }
        return widgetClass
    }
}