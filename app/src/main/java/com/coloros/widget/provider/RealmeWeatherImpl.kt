/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - RealmeWeatherImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.text.format.DateFormat
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.VisibleForTesting
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.ClockWidgetUtils.getWeatherIcon
import com.coloros.widget.smallweather.RealmeWeather
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.utils.CommonUtil
import java.util.Locale

class RealmeWeatherImpl(val context: Context) : IWidgetProxy {

    companion object {
        private const val TAG = "ClockWidget.RPlusWeather"
        private val clazz = RealmeWeather::class.java
    }

    override fun smallestWidgetHeight(): Int {
        return 0
    }

    override fun createRemoteViews(): RemoteViews? {
        return RemoteViews(context.packageName, R.layout.date_weather_widget_view_small)
    }

    override fun updateRemoteViews(small: Boolean): RemoteViews? {
        Log.d(TAG, "relayoutWidget")
        val clockWidgetManager = ClockWidgetManager.getInstance()
        val remoteViews = createRemoteViews()
        remoteViews?.let {
            updateDataFormat(it, R.id.calendar)
            setWidgetViewClickEvent(it)
            val localWeatherInfo = clockWidgetManager.localNewWeatherInfo
            Log.d(TAG, "relayoutWidget localWeatherInfo = $localWeatherInfo")
            if (!TextUtils.isEmpty(localWeatherInfo)) {
                it.setTextViewText(R.id.local_weather_info_txt, localWeatherInfo)
            }
            it.setViewVisibility(R.id.iv_weather_type, View.VISIBLE)
            it.setViewVisibility(R.id.local_weather_info_txt, View.VISIBLE)
            it.setViewVisibility(R.id.divider_line, View.VISIBLE)
            clockWidgetManager.updateTextColor(ClockType.WIDGET_TYPE_DATE_WEATHER)
            updateAllChildTextViewColor(it, clockWidgetManager.curTextColor)
        }

        return remoteViews
    }

    override fun updateOOSRemoteViews(width: Int, height: Int): RemoteViews? {
        return null
    }

    @VisibleForTesting
    fun setWidgetViewClickEvent(remoteViews: RemoteViews) {
        //点击日期
        val calendarIntent = Intent(context, clazz)
        calendarIntent.action = ClockWidgetManager.CALENDAR_CLICK_ACTION
        val calendarPendingIntent = PendingIntent.getBroadcast(
            context, 0, calendarIntent, Utils.getPendingIntentFlagAboveS(
                PendingIntent.FLAG_UPDATE_CURRENT
            )
        )
        remoteViews.setOnClickPendingIntent(R.id.calendar, calendarPendingIntent)

        //点击天气
        val weatherIntent = Intent(context, clazz)
        weatherIntent.action = ClockWidgetManager.WEATHER_CLICK_ACTION
        val weatherPendingIntent = PendingIntent.getBroadcast(
            context, 0, weatherIntent, Utils.getPendingIntentFlagAboveS(
                PendingIntent.FLAG_UPDATE_CURRENT
            )
        )
        remoteViews.setOnClickPendingIntent(R.id.iv_weather_type, weatherPendingIntent)
        remoteViews.setOnClickPendingIntent(R.id.local_weather_info_txt, weatherPendingIntent)
    }

    @VisibleForTesting
    fun updateAllChildTextViewColor(remoteViews: RemoteViews, color: Int) {
        Log.d(TAG, "updateAllChildTextViewColor color = $color")
        remoteViews.setTextColor(R.id.calendar, color)
        remoteViews.setTextColor(R.id.local_weather_info_txt, color)
        //设置天气图标
        val weatherIconResId = ClockWidgetManager.getInstance().weatherIconResId
        if (weatherIconResId != 0) {
            remoteViews.setImageViewBitmap(
                R.id.iv_weather_type, getWeatherIcon(
                    context,
                    weatherIconResId, color
                )
            )
        }

        //设置divider line
        if (ClockWidgetManager.getInstance().haveDateAndWeatherWidget()) {
            val dividerLineBitmap = ClockWidgetManager.getInstance().updateDividerLineBitmap()
            if (dividerLineBitmap != null) {
                remoteViews.setImageViewBitmap(R.id.divider_line, dividerLineBitmap)
            }
        }
    }

    @VisibleForTesting
    fun updateDataFormat(remoteViews: RemoteViews, resId: Int) {
        var dataFormat: CharSequence = DateFormat.getBestDateTimePattern(
            Locale.getDefault(),
            context.getString(R.string.abbrev_wday_month_day_no_year)
        )
        if (CommonUtil.isZh(context)) {
            dataFormat = (context.getString(R.string.abbrev_chinese_date) + "\u0020\u0020"
                    + context.getString(R.string.abbrev_wday_week_short))
        }
        updateDataFormat(remoteViews, resId, dataFormat)
    }

    @VisibleForTesting
    fun updateDataFormat(remoteViews: RemoteViews, resId: Int, format: CharSequence) {
        Log.d(TAG, "updateDataFormat:$format")
        remoteViews.setCharSequence(resId, ClockWidgetManager.METHOD_SET_FORMAT_24HOUR, format)
        remoteViews.setCharSequence(resId, ClockWidgetManager.METHOD_SET_FORMAT_12HOUR, format)
    }
}