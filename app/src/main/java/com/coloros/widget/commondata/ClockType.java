/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description: define clock type
 * weather info
 * * Version:1.0
 * * Date :2018/9/5
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.commondata;


public class ClockType {
    public static final int SINGLE_CLOCK = 1;
    public static final int DOUBLE_CLOCK = 2;
    public static final int WIDGET_TYPE_VERTICAL = 5;
    public static final int WIDGET_TYPE_HOR = 6;
    //#ifdef OPLUSOS_EDIT
    //<EMAIL>#2037608, 2019/06/10, Add for a new widget type on rplus exp
    public static final int WIDGET_TYPE_DATE_WEATHER = 7;
    //#endif /* OPLUSOS_EDIT */
}
