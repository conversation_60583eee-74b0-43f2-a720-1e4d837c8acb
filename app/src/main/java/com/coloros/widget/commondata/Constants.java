/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description:constants
 * * Version:1.0
 * * Date :2018/9/29
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.commondata;


import com.oplus.alarmclock.R;

public class Constants {
    public static final String OPLUS_CUSTOM_APP_PERMISSION = "oppo.permission.OPPO_COMPONENT_SAFE";
    public static final String CLOCK_WIDGET_SP = "shared_prefs_clock_widget";
    public static final String WEATHER_WIDGET_SP = "shared_prefs_weather_widget";
    public static final String LAST_UPDATE_WEATHER_TIME_KEY = "clock_last_update_weather_time_key";
    public static final String CLOCK_STOPWATCH_FLASHBACK_KEY = "clock_stopwatch_flashback_key";
    public static final String CLOCK_TIMER_FLASHBACK_KEY = "clock_timer_flashback_key";
    public static final String WIDGET_CLOCK_TYPE = "widget_clock_type";

    /**
     * 天气服务14.7.0版本以下依旧为30min
     */
    public static final long UPDATE_WEATHER_INTERVAL_TIME_30_MIN = 30 * 60 * 1000;

    /**
     * 天气服务14.7.0版本开始把定位时间间隔调为3min
     */
    public static final long UPDATE_WEATHER_INTERVAL_TIME_3_MIN = 3 * 60 * 1000;

    /**
     * 定位失败 超过 持续时间 才显示定位失败
     */
    public static final long LOCATION_FAIL_LAST_TIME_30_MIN = 30 * 60 * 1000;
    public static final int LOCATION_ERROR_CODE_SUCCESS = 410000;
    public static final int LOCATION_ERROR_TIME_OUT = -410013;
    public static final String KEY_ERROR_CODE = "errorCode";


    public static final int[] DRAWABLE_ID_CLOCK_OVERALL_THEME = {
            R.drawable.ic_clock_widget_num_0,
            R.drawable.ic_clock_widget_num_1,
            R.drawable.ic_clock_widget_num_2,
            R.drawable.ic_clock_widget_num_3,
            R.drawable.ic_clock_widget_num_4,
            R.drawable.ic_clock_widget_num_5,
            R.drawable.ic_clock_widget_num_6,
            R.drawable.ic_clock_widget_num_7,
            R.drawable.ic_clock_widget_num_8,
            R.drawable.ic_clock_widget_num_9,
    };
    public static final int NUMBER_0 = 0;
    public static final int NUMBER_1 = 1;
    public static final int NUMBER_2 = 2;
    public static final int NUMBER_3 = 3;
    public static final int NUMBER_4 = 4;
    public static final int NUMBER_5 = 5;
    public static final int NUMBER_6 = 6;
    public static final int NUMBER_7 = 7;
    public static final int NUMBER_8 = 8;
    public static final int NUMBER_9 = 9;
    public static final int NUMBER_10 = 10;
}
