package com.coloros.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;

import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;

public class MiniTimePickerView extends OplusTimePickerCustomClock {
    private static final String TAG = "MiniTimePickerView";
    private final static float CONSTANT_2_0 = 2.0F;
    private static final int MIN_BACKGROUND_DIVIDER_HEIGHT = 1;
    private Context mContext;
    private int mBackgroundLeft;
    private int mBackgroundRadius;
    private int mBackgroundDividerHeight;
    private int mColorID = -1;
    private Paint mPaint;
    private RectF mRectF;

    public MiniTimePickerView(Context context) {
        this(context, (AttributeSet) null);
    }

    public MiniTimePickerView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
        if (Utils.isAboveQ()) {
            setForceDarkAllowed(false);
        }
    }


    public MiniTimePickerView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.mContext = context;
        setWillNotDraw(false);
        try {
            TypedArray typedArray = mContext.obtainStyledAttributes(attrs, R.styleable.select_time_background, defStyle, R.style.timer_dflt_value);
            mColorID = typedArray.getColor(R.styleable.select_time_background_timeBackground, -1);
            typedArray.recycle();
        } catch (Exception e) {
            Log.e(TAG, "MiniTimePickerView " + e.toString());
        }
        this.mBackgroundLeft = mContext.getResources().getDimensionPixelOffset(R.dimen.coui_selected_background_horizontal_padding);
        this.mBackgroundRadius = mContext.getResources().getDimensionPixelOffset(R.dimen.coui_selected_background_radius);
        this.mBackgroundDividerHeight = Math.max(
                getResources().getDimensionPixelOffset(com.support.picker.R.dimen.coui_number_picker_background_divider_height),
                MIN_BACKGROUND_DIVIDER_HEIGHT);
        mPaint = new Paint();
        mPaint.setColor(mColorID);
        mRectF = new RectF();
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        requestDisallowInterceptTouchEvent(true);
        return super.dispatchTouchEvent(event);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mPaint != null) {
            int top = (int) (getHeight() / 2f - mBackgroundRadius);
            canvas.drawRect(mBackgroundLeft, top, getWidth() - mBackgroundLeft, top + mBackgroundDividerHeight, mPaint);
            top = (int) (getHeight() / 2f + mBackgroundRadius);
            canvas.drawRect(mBackgroundLeft, top, getWidth() - mBackgroundLeft, top + mBackgroundDividerHeight, mPaint);
        }
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.mini_time_picker_custom;
    }
}
