/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.


 * All rights reserved.
 *
 * Description    :
 * History      :
 * (ID, Date, Author, Description)
 * V1.0  2015.01.13 fanzuo  creat
 *******************************************************/

package com.coloros.widget.engine;

import android.content.Context;
import android.content.pm.PackageManager.NameNotFoundException;
import android.graphics.Typeface;
import android.util.Log;

public class ContextUtil {
    private final String TAG = "ContextUtil";

    private static ContextUtil sInstance = null;

    private Context mContext;
    private Typeface mXthinTypeface = null;
    private Typeface mRegularTypeface = null;

    public synchronized static ContextUtil getInstance(Context context) {
        if (null == sInstance) {
            synchronized (ContextUtil.class) {
                sInstance = new ContextUtil(context);
            }
        }
        return sInstance;
    }

    private ContextUtil(Context context) {
        mContext = createEngineContext(context);
    }

    public Context getExactContext() {
        return mContext;
    }

    private Context createEngineContext(Context context) {
        Context smallweatherContext = null;
        try {
            smallweatherContext = context.createPackageContext(context.getPackageName(),
                    Context.CONTEXT_INCLUDE_CODE | Context.CONTEXT_IGNORE_SECURITY);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return smallweatherContext;
    }

    public void recycle() {
        //TODO Need to test whether deleting code (mContext = null) causes memory leak
        mXthinTypeface = null;
        mRegularTypeface = null;
    }

    public Typeface getXthinTypeface() {
        if (mXthinTypeface == null) {
            mXthinTypeface = createTypeface("system/fonts/ColorOSUI-XThin.ttf");
        }
        return mXthinTypeface;
    }

    public Typeface getRegularTypeface() {
        if (mRegularTypeface == null) {
            mRegularTypeface = createTypeface("system/fonts/ColorOSUI-Regular.ttf");
        }
        return mRegularTypeface;
    }

    public Typeface createTypeface(String typefacePath) {
        Typeface typeface = Typeface.DEFAULT;
        try {
            typeface = Typeface.createFromFile(typefacePath);
        } catch (Exception e) {
            // TODO: handle exception
            typeface = Typeface.DEFAULT;
            Log.e(TAG, "createTypeface get a exception, used the default typeface, typefacePath = "
                    + typefacePath + " , typeface = " + typeface);
        }
        return typeface;
    }
}
