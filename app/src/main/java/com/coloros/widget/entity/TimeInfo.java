/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description: time info
 * weather info
 * * Version:1.0
 * * Date :2018/9/5
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.entity;


import java.util.Objects;

public class TimeInfo {
    private String mYear;
    private String mHour;
    private String mMinutes;
    private String mAmpm;
    private String mDateInfo;
    private String mDateInfoAm;
    private String mTimeZone;
    private String mFullTime;
    private String mWeekDay;
    private String mMonthDay;
    private String mTimeSeparator;


    public String getHour() {
        return mHour;
    }

    public void setHour(String mHour) {
        this.mHour = mHour;
    }

    public String getDateInfo() {
        return mDateInfo;
    }

    public void setDateInfo(String mDateInfo) {
        this.mDateInfo = mDateInfo;
    }

    public String getAmpm() {
        return mAmpm;
    }

    public void setAmpm(String mAmpm) {
        this.mAmpm = mAmpm;
    }

    public String getMinutes() {
        return mMinutes;
    }

    public void setMinutes(String mMinutes) {
        this.mMinutes = mMinutes;
    }


    public String getTimeZone() {
        return mTimeZone;
    }

    public void setTimeZone(String mTimeZone) {
        this.mTimeZone = mTimeZone;
    }

    public String getFullTime() {
        return mFullTime;
    }

    public void setFullTime(String mFullTime) {
        this.mFullTime = mFullTime;
    }

    public String getWeekDay() {
        return mWeekDay;
    }

    public void setWeekDay(String mWeekDay) {
        this.mWeekDay = mWeekDay;
    }

    public String getMonthDay() {
        return mMonthDay;
    }

    public void setMonthDay(String mMonthDay) {
        this.mMonthDay = mMonthDay;
    }

    public String getYear() {
        return mYear;
    }

    public void setYear(String mYear) {
        this.mYear = mYear;
    }

    public String getDateInfoAm() {
        return mDateInfoAm;
    }

    public void setDateInfoAm(String mDateInfoAm) {
        this.mDateInfoAm = mDateInfoAm;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof TimeInfo)) {
            return false;
        }
        TimeInfo timeInfo = (TimeInfo) obj;
        return Objects.equals(mHour, timeInfo.mHour)
                && Objects.equals(mMinutes, timeInfo.mMinutes)
                && Objects.equals(mAmpm, timeInfo.mAmpm)
                && Objects.equals(mDateInfo, timeInfo.mDateInfo)
                && Objects.equals(mTimeZone, timeInfo.mTimeZone)
                && Objects.equals(mFullTime, timeInfo.mFullTime)
                && Objects.equals(mWeekDay, timeInfo.mWeekDay)
                && Objects.equals(mMonthDay, timeInfo.mMonthDay)
                && Objects.equals(mYear, timeInfo.mYear)
                && Objects.equals(mTimeSeparator, timeInfo.mTimeSeparator);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mHour, mMinutes, mAmpm, mDateInfo, mTimeZone, mFullTime, mWeekDay, mMonthDay, mYear, mTimeSeparator,mDateInfoAm);
    }

    public String getTimeSeparator() {
        return mTimeSeparator;
    }

    public void setTimeSeparator(String mTimeSeparator) {
        this.mTimeSeparator = mTimeSeparator;
    }
}
