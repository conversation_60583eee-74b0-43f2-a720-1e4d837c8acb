/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description: weather  entity
 * <p>
 * * Version:1.0
 * * Date :2018/9/5
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.entity;


import static com.oplus.utils.CommonUtil.adapterRtlWeatherTxt;

import com.coloros.widget.smallweather.ClockWidgetManager;
import com.oplus.alarmclock.AlarmClockApplication;

import java.util.Objects;

public class WeatherEntity implements Cloneable {
    /**
     * number of degree
     */
    private String mDegree;
    /**
     * type of weather
     */
    private String mType;
    /**
     * decription of weather
     */
    private String mDescription;
    /**
     * centigrade
     */
    private String mCentigrade;
    /**
     * cityCode
     */
    private String mCityCode;
    /**
     * cityName from setting
     */
    private String mCityName;
    /**
     * default city name
     */
    private String mDefaultName;
    /**
     * time zone
     */
    private String mTimeZone;

    /**
     * time zone
     */
    private String mParentCityCode;

    private int mLocationResultCode;

    public String getTimeZone() {
        return mTimeZone;
    }

    public void setTimeZone(String mTimeZone) {
        this.mTimeZone = mTimeZone;
    }

    public String getDefaultName() {
        return mDefaultName;
    }

    public void setDefaultName(String mDefaultName) {
        this.mDefaultName = mDefaultName;
    }

    public String getCityCode() {
        return mCityCode;
    }

    public void setCityCode(String mCityCode) {
        this.mCityCode = mCityCode;
    }

    public String getCentigrade() {
        return mCentigrade;
    }

    public void setCentigrade(String mCentigrade) {
        this.mCentigrade = mCentigrade;
    }

    public String getDescription() {
        return mDescription;
    }

    public void setDescription(String mDescription) {
        this.mDescription = mDescription;
    }

    public String getType() {
        return mType;
    }

    public void setType(String mType) {
        this.mType = mType;
    }

    public String getDegree() {
        return mDegree;
    }

    public void setDegree(String mDegree) {
        this.mDegree = mDegree;
    }

    public String getCityName() {
        return mCityName;
    }

    public void setCityName(String mCityName) {
        this.mCityName = mCityName;
    }

    public String getParentCityCode() {
        return mParentCityCode;
    }

    public void setParentCityCode(String mParentCityCode) {
        this.mParentCityCode = mParentCityCode;
    }

    public int getLocationResultCode() {
        return mLocationResultCode;
    }

    public void setLocationResultCode(int locationResultCode) {
        this.mLocationResultCode = locationResultCode;
    }

    @Override
    public String toString() {
        return "WeatherEntity{"
                + "mDegree='" + mDegree + '\''
                + ", mType='" + mType + '\''
                + ", mDescription='" + mDescription + '\''
                + ", mCentigrade='" + mCentigrade + '\''
                + ", mTimeZone='" + mTimeZone + '\''
                + ", mParentCityCode='" + mParentCityCode + '\''
                + ", mLocationResultCode='" + mLocationResultCode + '\''
                + '}';
    }

    @Override
    public WeatherEntity clone() {
        WeatherEntity weatherEntity = new WeatherEntity();
        weatherEntity.mCentigrade = this.mCentigrade;
        weatherEntity.mCityCode = this.mCityCode;

        weatherEntity.mCityName = this.mCityName;
        weatherEntity.mDefaultName = this.mDefaultName;

        weatherEntity.mDegree = this.mDegree;
        weatherEntity.mDescription = this.mDescription;

        weatherEntity.mParentCityCode = this.mParentCityCode;
        weatherEntity.mTimeZone = this.mTimeZone;

        weatherEntity.mType = this.mType;
        return weatherEntity;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof WeatherEntity)) {
            return false;
        }
        WeatherEntity other = (WeatherEntity) obj;
        return Objects.equals(mDegree, other.mDegree)
                && Objects.equals(mType, other.mType)
                && Objects.equals(mDescription, other.mDescription)
                && Objects.equals(mCentigrade, other.mCentigrade)
                && Objects.equals(mCityCode, other.mCityCode)
                && Objects.equals(mCityName, other.mCityName)
                && Objects.equals(mDefaultName, other.mDefaultName)
                && Objects.equals(mTimeZone, other.mTimeZone)
                && Objects.equals(mParentCityCode, other.mParentCityCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mDegree, mType, mDescription, mCentigrade, mCityCode, mCityName, mDefaultName, mTimeZone, mParentCityCode);
    }

    public String getWeatherInfo() {
        String temperature = adapterRtlWeatherTxt(AlarmClockApplication.getInstance(), mCentigrade, mDegree);
        return mDescription + ClockWidgetManager.COLON_SPACE + ClockWidgetManager.COLON_SPACE + temperature;
    }

    public String getNewWeatherInfo() {
        String temperature = adapterRtlWeatherTxt(AlarmClockApplication.getInstance(), mCentigrade, mDegree);
        return temperature + ClockWidgetManager.WEATHER_SPACE;
    }

}
