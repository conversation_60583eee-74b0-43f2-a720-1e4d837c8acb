/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AppRetrieveClientOS12.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.retrieve

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.oplus.clock.common.utils.Log
import com.oplus.safecenter.removableapp.aidl.IRemovableApp
import com.oplus.safecenter.removableapp.aidl.IRemovableAppClient

class AppRetrieveClientOS12 : IAppRetrieveClient() {

    private var isConnected = false
    private var iRemovableApp: IRemovableApp? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(TAG, "onServiceConnected")
            iRemovableApp = IRemovableApp.Stub.asInterface(service)
            isConnected = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(TAG, "onServiceDisconnected")
            isConnected = false
        }
    }

    override fun binderService(context: Context) {
        val cn = ComponentName("com.oplus.safecenter",
                "com.oplus.safecenter.removableapp.service.RemovableAppService")
        val serviceIntent = Intent()
        serviceIntent.setComponent(cn)
        context.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun unbindService(context: Context) {
        super.unbindService(context)
        context.unbindService(serviceConnection)
    }

    override fun restoreApp(context: Context, packageName: String, action: (Boolean) -> Unit) {
        Log.d(TAG, "restoreApp isConnected = $isConnected")
        if (isConnected) {
            runCatching {
                val bundle = Bundle()
                bundle.putString(EXTRA_INSTALLER, context.packageName)
                iRemovableApp?.restoreRemovableApp(packageName, object : IRemovableAppClient.Stub() {
                    override fun onRestoreFinished(
                        returnCode: Int,
                        packageName: String?,
                        intent: Intent?
                    ) {
                        Log.d(TAG, "package $packageName restore $returnCode")
                        action(returnCode == INSTALL_SUCCEEDED)
                    }
                }, bundle)
            }.onFailure {
                Log.e(TAG, "restore app failed:${it.message}")
            }
        }
    }
}