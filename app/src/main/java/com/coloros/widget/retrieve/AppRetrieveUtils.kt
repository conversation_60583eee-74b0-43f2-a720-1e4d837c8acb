/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AppRetrieveUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.retrieve

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object AppRetrieveUtils {

    private const val TAG = "AppRetrieveUtils"

    const val PACKAGE_WEATHER = "com.coloros.weather2"
    const val EVENT_UPDATE_APP_INFO = "event_app_info"

    private val REMOVABLEAPP_AUTHORITY_URI_S by lazy {
        Uri.parse("content://com.color.provider.removableapp")
    }
    private val REMOVABLEAPP_AUTHORITY_URI_T by lazy {
        Uri.parse("content://com.oplus.provider.removableapp")
    }
    private const val TABLE_NAME_REMOVABLEAPP = "removableapp"
    private val URI_REMOVABLEAPP by lazy {
        val authUri = if (Utils.isAboveOS13()) {
            REMOVABLEAPP_AUTHORITY_URI_T
        } else {
            REMOVABLEAPP_AUTHORITY_URI_S
        }
        Uri.withAppendedPath(authUri, TABLE_NAME_REMOVABLEAPP)
    }
    private const val COL_PACKAGE_NAME = "package_name"
    private const val COL_ICON = "icon"

    private var removeAppInfo: Pair<String, Bitmap?>? = null

    /**
     * 判断天气应用是否可以卸载找回
     * @param context
     * @return 可找回（true）,不可找回（false）
     */
    @JvmStatic
    fun isWeatherAppCanRetrieved(context: Context): Boolean {
        return !isAppInstalled(context, PACKAGE_WEATHER)
                && getRemovableApp(context, PACKAGE_WEATHER) != null
    }

    /**
     * 获取可找回天气应用对应的应用图标
     * @return bitmap
     */
    @JvmStatic
    fun getWeatherAppIcon(): Bitmap? {
        Log.d(TAG, "getWeatherAppIcon :${removeAppInfo?.first}, ${removeAppInfo?.second}")
        return removeAppInfo?.second
    }

    /**
     * 判断对应包名的应用是否已安装
     * @return 已安装（true），未安装（false）
     */
    @JvmStatic
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return runCatching {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        }.getOrDefault(false)
    }

    /**
     * 打开卸载找回本地activity
     * @param context
     * @param isResidentCity 是否天气常驻城市
     */
    @JvmStatic
    fun openAppRetrieveActivity(context: Context, isResidentCity: Boolean) {
        val intent = Intent(context, AppRetrieveActivity::class.java)
        intent.putExtra(AppRetrieveActivity.KEY_IS_WEATHER_RESIDENT_CITY, isResidentCity)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or
                Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
        context.startActivity(intent)
    }

    /**
     * 清空数据
     */
    @JvmStatic
    fun clearData() {
        Log.d(TAG, "clearData:$removeAppInfo")
        removeAppInfo = null
    }

    /**
     * 获取对应包名的可找回信息
     * @param context
     * @param packageName 需要找回的应用包名
     * @return Pair.first包名 Pair.second应用图标 null无应用可找回
     */
    @JvmStatic
    fun getRemovableApp(context: Context, packageName: String): Pair<String, Bitmap?>? {
        return runCatching {
            val where = "$COL_PACKAGE_NAME = ?"
            val whereArgs = arrayOf(packageName)
            val cursor = context.contentResolver.query(
                URI_REMOVABLEAPP,
                null,
                where,
                whereArgs,
                null
            )
            cursor?.let {
                cursor.moveToFirst()
                val colPackageName = cursor.getString(cursor.getColumnIndexOrThrow(COL_PACKAGE_NAME))
                val colIcon = cursor.getBlob(cursor.getColumnIndexOrThrow(COL_ICON))
                removeAppInfo = Pair(colPackageName, null)
                byteToBitmap(colPackageName, colIcon)
                Log.d(TAG, "get removable app :$colPackageName, ${removeAppInfo?.first}")
                removeAppInfo
            }
        }.onFailure {
            Log.e(TAG, "get removable app failed:${it.message}")
        }.getOrNull()
    }

    /**
     * 转换应用图标为Bitmap
     */
    @JvmStatic
    private fun byteToBitmap(packageName: String, data: ByteArray?) {
        data?.let {
            CoroutineScope(Dispatchers.IO).launch {
                val bitmap = BitmapFactory.decodeByteArray(data, 0, data.size)
                Log.d(TAG, "byteToBitmap:$bitmap")
                if (bitmap != null) {
                    launch(Dispatchers.Main) {
                        removeAppInfo = Pair(packageName, bitmap)
                        LiteEventBus.instance.send(EVENT_UPDATE_APP_INFO)
                    }
                }
            }
        }
    }
}