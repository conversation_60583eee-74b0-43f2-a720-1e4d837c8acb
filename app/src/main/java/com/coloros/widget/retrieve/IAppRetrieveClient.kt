/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - IAppRetrieveClient.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.retrieve

import android.content.Context
import com.oplus.alarmclock.utils.Utils

open class IAppRetrieveClient {

    companion object {
        const val TAG = "IAppRetrieveClient"
        const val EXTRA_INSTALLER = "installer"
        const val INSTALL_SUCCEEDED = 1

        /**
         * 创建卸载找回客户端，S及以下使用老接口
         */
        fun create(): IAppRetrieveClient = if (Utils.isAboveOS13()) {
            AppRetrieveClientOS13()
        } else {
            AppRetrieveClientOS12()
        }
    }

    /**
     * 绑定卸载找回服务
     */
    open fun binderService(context: Context) {}

    /**
     * 解除卸载找回服务的绑定
     */
    open fun unbindService(context: Context) {}

    /**
     * 找回应用
     * @param context
     * @param packageName 需要找回的应用的包名
     * @param action 结果返回，成功(true),失败(false)
     */
    open fun restoreApp(context: Context, packageName: String, action: (Boolean) -> Unit) {}
}