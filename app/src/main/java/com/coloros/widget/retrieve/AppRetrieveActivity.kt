/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AppRetrieveActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.retrieve

import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.ViewTreeObserver
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.widget.COUIAlertDialogMessageView
import com.coui.appcompat.imageview.COUIRoundImageView
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ToastManager
import com.oplus.anim.EffectiveAnimationView
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import com.oplus.utils.SmallWeatherWidgetStatistics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AppRetrieveActivity : BaseActivity() {
    companion object {
        const val TAG = "AppRetrieveActivity"
        const val KEY_IS_WEATHER_RESIDENT_CITY = "key_is_weather_residentcity"

        const val DIALOG_TYPE_PREPARE = 0
        const val DIALOG_TYPE_INSTALLING = 1
        const val DIALOG_TYPE_FAILED = 2
    }

    private var dialog: AlertDialog? = null
    private var dialogType: Int = -1
    private var uiMode: Int = 0
    private var isResidentCity = false
    private val retrieveClient = IAppRetrieveClient.create()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
        window.navigationBarColor = Color.parseColor("#01ffffff")
        uiMode = resources.configuration.uiMode
        isResidentCity = intent.getBooleanExtra(KEY_IS_WEATHER_RESIDENT_CITY, false)

        retrieveClient.binderService(this)
        showInstallPrepareDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        releaseEvent(force = true)
        retrieveClient.unbindService(this)
        resetDialog()
        AppRetrieveUtils.clearData()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged")
        if (uiMode != newConfig.uiMode) {
            Log.d(TAG, "uiMode change")
            /*切换暗色模式时重新弹窗以更换弹窗颜色*/
            uiMode = newConfig.uiMode
            restartDialog()
        }
    }

    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        Log.d(TAG, "onUserLeaveHint")
        resetDialog(true)
    }

    /**
     * 显示准备找回的提示框
     */
    private fun showInstallPrepareDialog() {
        val customLayout = layoutInflater.inflate(R.layout.layout_install_prepare_content, null)
        if (customLayout == null) {
            Log.e(TAG, "showInstallPrepareDialog failed: layout is null!")
            finish()
            return
        }
        resetDialog()
        dialog = COUIAlertDialogBuilder(this)
            .setView(customLayout)
            .setTitle(String.format(getString(R.string.install_tips_title), getString(R.string.install_weather)))
            .setPositiveButton(R.string.install_tips_install) { _, _ ->
                showInstallingDialog()
                retrieveClient.restoreApp(this, AppRetrieveUtils.PACKAGE_WEATHER) { success ->
                    if (success) {
                        handleInstallSuccess()
                    } else {
                        showInstallFailDialog()
                    }
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
        dialog?.apply {
            val iconView = customLayout.findViewById<COUIRoundImageView>(R.id.customImageview)
            iconView?.updateIcon()
            val messageView = customLayout.findViewById<COUIAlertDialogMessageView>(R.id.customMessage)
            messageView?.setText(R.string.install_content_weather)
            showAndMark(DIALOG_TYPE_PREPARE)
        }
    }

    /**
     * 显示安装中的提示框
     */
    private fun showInstallingDialog() {
        resetDialog()
        dialog = COUIAlertDialogBuilder(this, R.style.COUIAlertDialog_Rotating).show()
        dialog?.apply {
            addOnWindowAttachListener()
            showAndMark(DIALOG_TYPE_INSTALLING)
        }
    }

    /**
     * 显示安装失败提示框
     */
    private fun showInstallFailDialog() {
        resetDialog()
        dialog = COUIAlertDialogBuilder(this)
            .setTitle(R.string.install_tips_failed)
            .setPositiveButton(R.string.install_tips_reinstall) { _, _ ->
                showInstallingDialog()
                retrieveClient.restoreApp(this, AppRetrieveUtils.PACKAGE_WEATHER) { success ->
                    if (success) {
                        handleInstallSuccess()
                    } else {
                        showInstallFailDialog()
                    }
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
        dialog?.apply {
            showAndMark(DIALOG_TYPE_FAILED)
        }
    }

    private fun AlertDialog?.showAndMark(type: Int) {
        this?.let {
            Log.d(TAG, "showAndMark type:$type")
            setOnDismissListener {
                releaseEvent(type)
                finish()
            }
            setOnCancelListener {
                releaseEvent(type)
                finish()
            }
            show()
            dialogType = type
        }
    }

    /**
     * 更新找回应用的图标
     */
    private fun COUIRoundImageView.updateIcon() {
        if (!setIcon()) {
            LiteEventBus.instance.with(AppRetrieveUtils.EVENT_UPDATE_APP_INFO, hashCode().toString())
                .observe(this@AppRetrieveActivity) {
                    Log.d(TAG, "updateIcon observe")
                    setIcon()
                }
        }
    }

    private fun COUIRoundImageView.setIcon(): Boolean {
        val bitmap = AppRetrieveUtils.getWeatherAppIcon()
        Log.i(TAG, "setIcon:$bitmap")
        if (bitmap != null) {
            setImageBitmap(bitmap)
            return true
        }
        return false
    }

    /**
     * 找回成功时，弹出toast，跳转到天气应用内
     */
    private fun handleInstallSuccess() {
        lifecycleScope.launch(Dispatchers.Main) {
            ToastManager.showToast(this@AppRetrieveActivity, String.format(
                getString(R.string.install_tips_success),
                getString(R.string.install_weather)))

            ClockWidgetManager.getInstance().startWeatherAppByIntent(
                this@AppRetrieveActivity,
                ClockWidgetManager.OPLUS_WEATHER_ACTION,
                null,
                isResidentCity
            )
            SmallWeatherWidgetStatistics.statsClickWeatherInfo()
            resetDialog(true)
        }
    }

    private fun releaseEvent(dialogType: Int = -1, force: Boolean = false) {
        if (force || dialogType == DIALOG_TYPE_PREPARE) {
            LiteEventBus.instance.releaseEvent(AppRetrieveUtils.EVENT_UPDATE_APP_INFO)
        }
    }

    /**
     * 安装中弹窗弹出时，自动播放旋转动画
     */
    private fun AlertDialog?.addOnWindowAttachListener() {
        this?.window?.decorView?.apply {
            val rotatingView = findViewById<EffectiveAnimationView>(R.id.progress)
            findViewById<TextView>(R.id.progress_tips)?.let {
                COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G4)
                it.text = getString(R.string.install_tips_installing)
            }
            viewTreeObserver?.addOnWindowAttachListener(object :
                ViewTreeObserver.OnWindowAttachListener {
                override fun onWindowAttached() {
                    rotatingView?.playAnimation()
                }

                override fun onWindowDetached() {
                    rotatingView?.pauseAnimation()
                }
            })
        }
    }

    /**
     * 重启当前弹窗
     */
    private fun restartDialog() {
        dialog?.apply {
            if (isShowing) {
                when (dialogType) {
                    DIALOG_TYPE_PREPARE -> showInstallPrepareDialog()
                    DIALOG_TYPE_INSTALLING -> showInstallingDialog()
                    DIALOG_TYPE_FAILED -> showInstallFailDialog()
                    else -> resetDialog(true)
                }
            }
        }
    }

    /**
     * 重置弹窗
     */
    private fun resetDialog(finish: Boolean = false) {
        Log.d(TAG, "reset dialog:$dialog->$finish")
        dialog?.apply {
            setOnDismissListener(null)
            setOnCancelListener(null)
            dismiss()
        }
        dialog = null
        dialogType = -1
        if (finish) {
            finish()
        }
    }
}