/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AppRetrieveClientOS13.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.retrieve

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.IBinder
import com.oplus.clock.common.utils.Log
import com.oplus.exsystemservice.removableapp.aidl.IRemovableApp
import com.oplus.exsystemservice.removableapp.aidl.IRemovableAppClient

class AppRetrieveClientOS13 : IAppRetrieveClient() {

    var isConnected = false
    var iRemovableApp: IRemovableApp? = null

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(TAG, "onServiceConnected")
            iRemovableApp = IRemovableApp.Stub.asInterface(service)
            isConnected = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(TAG, "onServiceDisconnected")
            iRemovableApp = null
            isConnected = false
        }
    }

    override fun binderService(context: Context) {
        super.binderService(context)
        val cn = ComponentName("com.oplus.exsystemservice",
                "com.oplus.exsystemservice.removableapp.service.RemovableAppService")

        val serviceIntent = Intent()
        serviceIntent.setComponent(cn)
        context.bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun unbindService(context: Context) {
        super.unbindService(context)
        context.unbindService(serviceConnection)
    }

    override fun restoreApp(context: Context, packageName: String, action: (Boolean) -> Unit) {
        super.restoreApp(context, packageName, action)
        Log.d(TAG, "restoreApp isConnected = $isConnected $iRemovableApp")
        if (isConnected) {
            runCatching {
                val bundle = Bundle()
                bundle.putString(EXTRA_INSTALLER, context.packageName)
                iRemovableApp?.restoreRemovableApp(packageName, object : IRemovableAppClient.Stub() {
                    override fun onRestoreFinished(
                        returnCode: Int,
                        packageName: String?,
                        intent: Intent?
                    ) {
                        Log.d(TAG, "package $packageName restore $returnCode")
                        action(returnCode == INSTALL_SUCCEEDED)
                    }
                }, bundle)
            }.onFailure {
                Log.e(TAG, "restore app failed:${it.message}")
            }
        }
    }
}