/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:BaseAppWidget.java
 * * Description:BaseAppWidget
 * * Version:1.0
 * * Date :2020/12/19
 * * Author:qdq
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.coloros.widget.smallweather

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import com.coloros.widget.provider.WidgetUpdateHelper
import com.coloros.widget.retrieve.AppRetrieveUtils.isWeatherAppCanRetrieved
import com.coloros.widget.retrieve.AppRetrieveUtils.openAppRetrieveActivity
import com.oplus.alarmclock.mba.CalendarDisableInfo
import com.oplus.alarmclock.mba.OPlusWeatherDisableInfo
import com.oplus.alarmclock.mba.PackageDisabledManager.checkDisabledAndJump
import com.oplus.alarmclock.mba.PackageDisabledManager.isPkgDisabled
import com.oplus.alarmclock.mba.PackageDisabledManager.openEnableActivity
import com.oplus.alarmclock.mba.WeatherDisableInfo
import com.oplus.alarmclock.mba.WeatherServiceDisableInfo
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.clock.common.utils.Log
import com.oplus.utils.SmallWeatherWidgetStatistics.setContext
import com.oplus.utils.SmallWeatherWidgetStatistics.statsClickDateInfo
import com.oplus.utils.SmallWeatherWidgetStatistics.statsClickRefreshImgBtn
import com.oplus.utils.SmallWeatherWidgetStatistics.statsClickTimeInfo
import com.oplus.utils.SmallWeatherWidgetStatistics.statsClickWeatherInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.Executors

abstract class BaseAppWidget : AppWidgetProvider() {
    companion object {
        private const val TAG = "BaseAppWidget"
        private const val START_ROTATE_ANIMATION = 2
        private const val UPDATE_DELAY_TIME = 500 //避免重复刷新导致闪烁问题
    }

    protected var mContext: Context? = null
    protected var mDegree = 0
    protected var isPortrait = false

    override fun onReceive(context: Context, intent: Intent) {
        mContext = context
        Log.d(TAG, "onReceive action=${intent.action}  class:${getCurrentClass()}")
        super.onReceive(context, intent)
        intent.action?.let { handleAction(context, it, intent) }
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        Log.d(TAG, "onUpdate")
        ClockWidgetManager.getInstance().mIsUpdate = false
        updateData(context)
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle
    ) {
        super.onAppWidgetOptionsChanged(context, appWidgetManager, appWidgetId, newOptions)
        Log.d(TAG, "onAppWidgetOptionsChanged:${newOptions.getInt(AppWidgetManager.OPTION_APPWIDGET_MAX_HEIGHT)}")
        handleOptionChanged(context)
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        Log.d(TAG, "onDeleted")
        //更新缓存
        ClockWidgetManager.getInstance().putWidgetData(getCurrentClass(), context)
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
        Log.d(TAG, "onEnabled")
        updateData(context)
        ClockWidgetManager.getInstance().putHasWidgetToWeather()
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.d(TAG, "onDisabled")
        release()
    }

    protected open fun handleOptionChanged(context: Context) {
        updateData(context)
    }

    @Suppress("LongMethod")
    private fun handleAction(context: Context, action: String, intent: Intent?): Boolean {
        if (TextUtils.isEmpty(action)) {
            return true
        }
        setContext(mContext)
        when (action) {
            ClockWidgetManager.TIME_CLICK_ACTION -> {
                ClockWidgetManager.getInstance().startAppByIntent(
                    context,
                    ClockWidgetManager.ALARM_CLOCK_ACTION,
                    null,
                    null
                )
                statsClickTimeInfo()
                return false
            }

            ClockWidgetManager.REFRESH_CLICK_ACTION -> {
                if (!checkDisabledAndJump(context, WeatherServiceDisableInfo())) {
                    mHandler?.sendEmptyMessage(START_ROTATE_ANIMATION)
                    if (DeviceUtils.isExpVersion(context)) {
                        ClockWidgetManager.getInstance().checkLocationPermission(false, true)
                    } else {
                        BaseWidgetViewHelper.getInstance().getWeatherServiceStatus(context)
                    }
                    statsClickRefreshImgBtn()
                }
                return false
            }

            ClockWidgetManager.CALENDAR_CLICK_ACTION -> {
                if (!checkDisabledAndJump(context, CalendarDisableInfo())) {
                    ClockWidgetManager.getInstance().startAppByIntent(
                        context,
                        Intent.ACTION_MAIN,
                        Intent.CATEGORY_APP_CALENDAR,
                        null
                    )
                    statsClickDateInfo()
                }
                return false
            }

            ClockWidgetManager.WEATHER_CLICK_ACTION -> {
                var isResidentCity = false
                kotlin.runCatching {
                    if (intent != null) {
                        isResidentCity =
                            intent.getBooleanExtra(ClockWidgetManager.IS_RESIDENT_KEY, false)
                    }
                }
                val weather = WeatherDisableInfo()
                val opWeather = OPlusWeatherDisableInfo()
                if (!isResidentCity && isPkgDisabled(context, weather)) {
                    openEnableActivity(context, weather)
                } else if (!isResidentCity && isPkgDisabled(context, opWeather)) {
                    openEnableActivity(context, opWeather)
                } else if (isWeatherAppCanRetrieved(context)) {
                    openAppRetrieveActivity(context, isResidentCity)
                } else {
                    ClockWidgetManager.getInstance().startWeatherAppByIntent(
                        context,
                        ClockWidgetManager.OPLUS_WEATHER_ACTION,
                        null,
                        isResidentCity
                    )
                    statsClickWeatherInfo()
                }
                return false
            }

            ClockWidgetManager.RESIDENT_CITY_CLICK_ACTION -> {
                ClockWidgetManager.getInstance().startSettingAddResidentCity(context)
                return false
            }

            Intent.ACTION_LOCALE_CHANGED -> {
                CoroutineScope(Dispatchers.IO).launch {
                    WidgetUpdateHelper.instance.updateWidget(context, getCurrentClass())
                }
                return false
            }

            Intent.ACTION_TIMEZONE_CHANGED -> {
                updateData(context)
                return false
            }

            else -> {}
        }
        return true
    }

    private val mHandler: Handler? = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            Log.d(TAG, "handleMessage what=${msg.what}")
            if (START_ROTATE_ANIMATION == msg.what) {
                mDegree += ClockWidgetManager.IMAGE_ROTATE_DEGREE_INTERVAL
                this.removeMessages(START_ROTATE_ANIMATION)
                if (mDegree <= ClockWidgetManager.IMAGE_ROTATE_DEGREE_TOTAL) {
                    this.sendEmptyMessageDelayed(
                        START_ROTATE_ANIMATION,
                        ClockWidgetManager.IMAGE_ROTATE_TIME_INTERVAL.toLong()
                    )
                } else {
                    mDegree = 0
                }
                WidgetUpdateHelper.instance.updatePartialWidgets(
                    mContext,
                    getCurrentClass(),
                    mDegree
                )
            }
        }
    }

    protected fun updateData(context: Context) {
        if (ClockWidgetManager.getInstance().mIsUpdate) {
            Log.d(TAG, "updateData return update=true")
            return
        }
        ClockWidgetManager.getInstance().mIsUpdate = true

        Executors.newCachedThreadPool().execute {
            //更新缓存
            ClockWidgetManager.getInstance().putWidgetData(getCurrentClass(), context)
            ClockWidgetManager.getInstance().updateLocalData(true)
            if (ClockWidgetManager.getInstance().isClockAllOverTheme) {
                ClockWidgetManager.startClockWidgetService(context, true)
            } else {
                ClockWidgetManager.startClockWidgetWeatherService(context, true, false)
            }
            ClockWidgetManager.getInstance().requestWeatherData(false)
        }

        mHandler?.postDelayed({
            ClockWidgetManager.getInstance().mIsUpdate = false
            Log.d(TAG, "updateData return update=false")
        }, UPDATE_DELAY_TIME.toLong())
    }

    private fun release() {
        mHandler?.removeMessages(START_ROTATE_ANIMATION)
        ClockWidgetManager.getInstance().putHasWidgetToWeather()
        ClockWidgetManager.getInstance().stopClockWidgetService(mContext)
        ClockWidgetManager.getInstance().stopClockWidgetWeatherService(mContext)
        ClockWidgetManager.getInstance().mIsUpdate = false
    }

    /**
     * 是否包含对应类型的插件
     *
     * @return true 包含， false 不包含
     */
    protected abstract fun haveWidget(): Boolean

    /**
     * 对应类型插件的class
     * @return class
     */
    protected abstract fun getCurrentClass(): Class<*>
}