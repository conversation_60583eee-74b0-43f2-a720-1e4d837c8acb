/****************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OxygenWeatherSingle.kt
 ** Description:OxygenWeatherSingle helper
 ** Version: 1.0
 ** Date : 2024/06/17
 ** Author: YeWen
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/06/17     1.0            build this module
 ****************************************************************/
package com.coloros.widget.smallweather

import android.content.Context

class OxygenWeatherSingle : BaseAppWidget() {

    companion object {
        private const val TAG = "ClockWidget.OxygenWeatherSingle"
    }

    override fun getCurrentClass(): Class<*> {
        return javaClass
    }

    override fun haveWidget(): Boolean {
        return ClockWidgetManager.getInstance().haveOxygenWeatherSingleWidget()
    }

    override fun handleOptionChanged(context: Context) {
        ClockWidgetManager.getInstance().mIsUpdate = false
        updateData(context)
    }
}
