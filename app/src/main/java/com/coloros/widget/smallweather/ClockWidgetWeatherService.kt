package com.coloros.widget.smallweather

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import com.coloros.widget.commondata.Constants
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.app.OplusSplitScreenObserver
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.splitscreen.OplusSplitScreenManager
import com.oplus.weatherservicesdk.DebugLog.setAllowPrintSensitiveLog

class ClockWidgetWeatherService : Service() {

    companion object {
        private const val TAG = "ClockWidget.ClockWidgetWeatherService"
        private const val MSG_WAIT_REFRESH_TIMEOUT = 1
        private const val WAIT_REFRESH_TIME = 8 * 1000L
    }

    private var weatherCityInfoObserver: ContentObserver? = null
    private var weatherInfoObserver: ContentObserver? = null
    private var wallpaperColorObserver: ContentObserver? = null
    private var settingObserver: ContentObserver? = null
    private var locationSwitchObserver: ContentObserver? = null
    private var splitScreenObserver: OplusSplitScreenObserver? = null
    private var locationResultNotifyObserver: ContentObserver? = null

    private val refreshHandler: Handler = Handler(Looper.getMainLooper()) {
        //10秒后停止当前服务
        if (MSG_WAIT_REFRESH_TIMEOUT == it.what) {
            Log.d(TAG, "stop refresh service self")
            stopSelf()
        }
        false
    }

    private val mBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action
            if (Intent.ACTION_CONFIGURATION_CHANGED == action) {
                Log.d(TAG, "ACTION_CONFIGURATION_CHANGED")
                ClockWidgetState.saveScreenDisplayModelState()
                ClockWidgetManager.getInstance().updateLocalData(true)
            }
        }
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "ClockWidgetWeatherService onCreate:")
        registerAll()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "ClockWidgetWeatherService onStartCommand")
        if (refreshHandler.hasMessages(MSG_WAIT_REFRESH_TIMEOUT)) {
            //当前有等待发送的更新消息，清除等待发送的消息
            refreshHandler.removeMessages(MSG_WAIT_REFRESH_TIMEOUT)
        }
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance().applicationContext)) {
            setAllowPrintSensitiveLog(true)
        }
        intent?.let {
            val refresh = it.getBooleanExtra(ClockWidgetManager.EXTRA_NEED_REFRESH, true)
            checkConfigChanged(refresh)

            if (ClockWidgetManager.getInstance().haveNoWidget(this)) {
                //当前没有插件停止服务
                stopSelf()
                return START_NOT_STICKY
            }

            val needLocation = it.getBooleanExtra(ClockWidgetManager.EXTRA_NEED_LOCATION, false)
            if (needLocation) {
                ClockWidgetManager.getInstance().requestWeatherData(true)
                PrefUtils.putLong(
                    AlarmClockApplication.getInstance().applicationContext,
                    Constants.CLOCK_WIDGET_SP,
                    Constants.LAST_UPDATE_WEATHER_TIME_KEY,
                    System.currentTimeMillis()
                )
            } else {
                ClockWidgetManager.getInstance().requestWeatherData(false)
            }
            if (!ClockWidgetUtils.supportLandWidget() && !ClockWidgetUtils.supportMultiWallpaper()) {
                //延迟8秒停止服务
                refreshHandler.sendEmptyMessageDelayed(MSG_WAIT_REFRESH_TIMEOUT, WAIT_REFRESH_TIME)
            }
            return START_STICKY
        } ?: kotlin.run {
            stopSelf()
            return START_NOT_STICKY
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "ClockWidgetWeatherService onDestroy")
        unRegisterAll()
        if (refreshHandler.hasMessages(MSG_WAIT_REFRESH_TIMEOUT)) {
            refreshHandler.removeMessages(MSG_WAIT_REFRESH_TIMEOUT)
        }
    }

    private fun registerAll() {
        Log.d(TAG, "ClockWidgetWeatherService registerAll")
        registerWallpaperColorObserver()
        registerSettingObserver()
        registerLocationSwitchObserver()
        registerWeatherCityObserver()
        registerConfiguration()
        registerLocationResultNotifyObserver()
    }

    private fun unRegisterAll() {
        Log.d(TAG, "ClockWidgetWeatherService unRegisterAll")
        unregisterWallpaperColorObserver()
        unregisterSettingObserver()
        unregisterLocationSwitchObserver()
        unregisterWeatherObserver()
        unregisterReceiver(mBroadcastReceiver)
        unregisterLocationResultNotifyObserver()
    }

    private fun registerConfiguration() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_CONFIGURATION_CHANGED)
        registerReceiver(mBroadcastReceiver, intentFilter, Constants.OPLUS_CUSTOM_APP_PERMISSION, null)
    }

    /**
     * 注册壁纸更新监听
     */
    private fun registerWallpaperColorObserver() {
        if (wallpaperColorObserver == null) {
            wallpaperColorObserver = object : ContentObserver(refreshHandler) {
                override fun onChange(selfChange: Boolean) {
                    super.onChange(selfChange)
                    Log.d(TAG, "wallpaper color changed")
                    ClockWidgetState.saveWallpaperColor()
                    ClockWidgetManager.getInstance().postReloadRunnable()
                }
            }
        }
        wallpaperColorObserver?.run {
            contentResolver.registerContentObserver(
                    Settings.Secure.getUriFor(ClockWidgetManager.LAUNCHER_TEXT_COLOR_VALUE),
                    false,
                    this
            )
        }
    }

    /**
     * 注册单双时钟监听
     */
    private fun registerSettingObserver() {
        if (settingObserver == null) {
            settingObserver = object : ContentObserver(refreshHandler) {
                override fun onChange(selfChange: Boolean) {
                    super.onChange(selfChange)
                    Log.d(TAG, "SettingObserver changed")
                    ClockWidgetState.saveDualClockState()
                    ClockWidgetManager.getInstance().updateLocalData(false)
                    ClockWidgetManager.getInstance().requestWeatherData(false)
                }
            }
        }
        settingObserver?.run {
            contentResolver.registerContentObserver(
                    Settings.Secure.getUriFor(ClockWidgetManager.ENABLE_DUAL_CLOCK_SWITCH),
                    false,
                    this
            )
            contentResolver.registerContentObserver(
                    Settings.Secure.getUriFor(ClockWidgetManager.ENABLE_DUAL_CLOCK_CLOCK_WIDGET_SWITCH),
                    false,
                    this
            )
            contentResolver.registerContentObserver(
                    Settings.Secure.getUriFor(ClockWidgetManager.ENABLE_DUAL_CLOCK_SCREEN_CLOCK_SWITCH),
                    false,
                    this
            )
        }
    }

    /**
     * 注册天气监听
     */
    private fun registerWeatherCityObserver() {
        if (ClockWidgetUtils.commonWeatherServiceExist()) {
            if (weatherCityInfoObserver == null) {
                weatherCityInfoObserver = object : ContentObserver(refreshHandler) {
                    override fun onChange(b: Boolean) {
                        super.onChange(b)
                        Log.d(TAG, "onChange ,city weather info Change ")
                        if (AlarmClockApplication.getInstance() == null) {
                            Log.e(TAG, "onChange -- mContext is null!")
                            return
                        }
                        ClockWidgetManager.getInstance().updateLocalData(false)
                        ClockWidgetManager.getInstance().requestWeatherData(false)
                    }
                }
            }
            try {
                Log.d(TAG, "registerWeatherCityObserver")
                weatherCityInfoObserver?.run {
                    contentResolver.registerContentObserver(
                            Uri.parse(ClockWidgetManager.OPLUS_NEW_LOCAL_CITY_INFO_URI),
                            false, this
                    )
                    contentResolver.registerContentObserver(
                            Uri.parse(ClockWidgetManager.OPLUS_NEW_RESIDENT_CITY_INFO_URI),
                            false, this
                    )
                    contentResolver.registerContentObserver(
                            Uri.parse(ClockWidgetManager.OPLUS_NEW_WEATHER_INFO_URI),
                            false, this
                    )
                }
            } catch (e: Exception) {
                try {
                    weatherCityInfoObserver?.run {
                        contentResolver.registerContentObserver(
                                Uri.parse(ClockWidgetManager.OPLUS_NEW_LOCAL_CITY_INFO_URI_OLD),
                                false, this
                        )
                        contentResolver.registerContentObserver(
                                Uri.parse(ClockWidgetManager.OPLUS_NEW_RESIDENT_CITY_INFO_URI_OLD),
                                false, this
                        )
                        contentResolver.registerContentObserver(
                                Uri.parse(ClockWidgetManager.OPLUS_NEW_WEATHER_INFO_URI_OLD),
                                false, this
                        )
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                }
            }
            if (ClockWidgetUtils.weatherServiceVersionSupportPrivacy()) {
                weatherCityInfoObserver?.run {
                    contentResolver.registerContentObserver(
                            Uri.parse(BaseWidgetViewHelper.WEATHER_SERVICE_PRIVACY_URI),
                            false,
                            this
                    )
                }
            }
        } else {
            if (weatherInfoObserver == null) {
                weatherInfoObserver = object : ContentObserver(refreshHandler) {
                    override fun onChange(selfChange: Boolean) {
                        super.onChange(selfChange)
                        if (AlarmClockApplication.getInstance() == null) {
                            Log.d(TAG, "onChange -- mContext is null!")
                            return
                        }
                        ClockWidgetManager.getInstance().updateLocalData(false)
                        ClockWidgetManager.getInstance().requestWeatherData(false)
                    }
                }
            }
            try {
                weatherInfoObserver?.run {
                    contentResolver.registerContentObserver(
                            Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING),
                            false, this
                    )
                    contentResolver.registerContentObserver(
                            Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_RESIDENT_WEATHER_INFO_SETTING),
                            false, this
                    )
                }
            } catch (e: Exception) {
                weatherInfoObserver?.run {
                    contentResolver.registerContentObserver(
                            Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_WEATHER_INFO_SETTING_OLD),
                            false, this
                    )
                    contentResolver.registerContentObserver(
                            Settings.Secure.getUriFor(ClockWidgetManager.OPLUS_RESIDENT_WEATHER_INFO_SETTING_OLD),
                            false, this
                    )
                }
            }
        }
    }

    /**
     * 注册位置更新监听
     */
    private fun registerLocationSwitchObserver() {
        if (locationSwitchObserver == null) {
            locationSwitchObserver = object : ContentObserver(null) {
                override fun onChange(selfChange: Boolean) {
                    super.onChange(selfChange)
                    ClockWidgetManager.getInstance().locationSwitchChanged()
                }
            }
        }
        locationSwitchObserver?.run {
            contentResolver.registerContentObserver(
                    Settings.Secure.getUriFor(Settings.System.LOCATION_PROVIDERS_ALLOWED),
                    false, this
            )
        }
        //此监听在进入和退出分屏模式下才会触发，由于这个监听晚于configChange变化，所以这里需要单独判断
        kotlin.runCatching {
            if (!VersionUtils.isOsVersion15()) {
                if (splitScreenObserver == null) {
                    splitScreenObserver = object : OplusSplitScreenObserver() {
                        override fun onStateChanged(event: String, bundle: Bundle) {
                            if (OplusSplitScreenManager.EVENT_SPLIT_SCREEN_MODE_CHANGED == event) {
                                Log.d(TAG, "handleConfigChange onStateChanged")
                                ClockWidgetState.saveSplitScreenMode()
                                ClockWidgetManager.getInstance().updateLocalData(true)
                            }
                        }
                    }
                }
                OplusSplitScreenManager.getInstance()
                    .registerSplitScreenObserver(splitScreenObserver)
            }
        }
    }

    /**
     * 注册 定位结果
     */
    private fun registerLocationResultNotifyObserver() {
        runCatching {
            if (locationResultNotifyObserver == null) {
                locationResultNotifyObserver = object : ContentObserver(null) {
                    override fun onChange(selfChange: Boolean, uri: Uri?) {
                        super.onChange(selfChange, uri)
                        Log.d(TAG, "locationResultNotifyObserver onChange")
                        /*解析定位失败的结果 errorcode
                        定位失败：格式 -410xxx   -410 开头 为定位失败；
                        定位成功：格式 410000*/
                        uri?.getQueryParameter(Constants.KEY_ERROR_CODE)?.let {
                            Log.d(TAG, "locationResultNotifyObserver errorCode: $it")
                            BaseWidgetViewHelper.getInstance().updateLocateResult(it.toInt())
                        }
                    }
                }
                Log.d(TAG, "registerLocationResultNotifyObserver")
                locationResultNotifyObserver?.run {
                    contentResolver.registerContentObserver(
                        Uri.parse(ClockWidgetManager.OPLUS_WEATHER_LOCATION_RESULT_URI),
                        false, this
                    )
                }
            }
        }.onFailure {
            Log.e(TAG, "register location failed：${it.message}")
        }
    }

    private fun unregisterLocationResultNotifyObserver() {
        locationResultNotifyObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        locationResultNotifyObserver = null
    }


    private fun unregisterWallpaperColorObserver() {
        wallpaperColorObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        wallpaperColorObserver = null
    }

    private fun unregisterSettingObserver() {
        settingObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        settingObserver = null
    }

    private fun unregisterLocationSwitchObserver() {
        locationSwitchObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        locationSwitchObserver = null
        kotlin.runCatching {
            splitScreenObserver?.run {
                OplusSplitScreenManager.getInstance().unregisterSplitScreenObserver(this)
            }
            splitScreenObserver = null
        }
    }

    private fun unregisterWeatherObserver() {
        weatherInfoObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        weatherCityInfoObserver?.run {
            contentResolver.unregisterContentObserver(this)
        }
        weatherInfoObserver = null
        weatherCityInfoObserver = null
    }

    private fun checkConfigChanged(refresh: Boolean) {
        Log.d(TAG, "checkConfigChanged:$refresh")
        //当前插件内容（分屏，壁纸，单双时钟）配置是否修改
        if (refresh) {
            if (ClockWidgetState.hasSystemConfigChanged()) {
                //保存当前配置
                ClockWidgetState.saveWidgetState()
            }
            //更新插件内容
            ClockWidgetManager.getInstance().updateLocalData(true)
        } else {
            ClockWidgetManager.getInstance().updateLocalData(false)
        }
    }
}