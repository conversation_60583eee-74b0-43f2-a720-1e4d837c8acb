/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description:BaseWidgetView helper
 * * Version:1.0
 * * Date :2018/9/5
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.smallweather;

import static com.coloros.widget.smallweather.ClockWidgetManager.OPLUS_NEW_RESIDENT_CITY_INFO_URI;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;

import com.coloros.widget.commondata.ClockType;
import com.coloros.widget.commondata.ClockTypeSetting;
import com.coloros.widget.commondata.Constants;
import com.coloros.widget.entity.WeatherEntity;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.mba.PackageDisabledManager;
import com.oplus.alarmclock.mba.WeatherServiceDisableDomesticInfo;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.servicesdk.WeatherRequest;
import com.oplus.utils.CommonUtil;
import com.oplus.weatherservicesdk.BaseCallBack;
import com.oplus.weatherservicesdk.model.SecureSettingsData;
import com.oplus.weatherservicesdk.service.WeatherBaseDataTask;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class BaseWidgetViewHelper {

    public static final long COMMON_VERSION_CODE_40700 = 40700L;
    public static final String OPLUS_WEATHER_INFO_SETTING_OLD = "oppo_weather_info";
    public static final String OPLUS_WEATHER_INFO_SETTING = "oplus_weather_info";
    public static final String OPLUS_RESIDENT_WEATHER_INFO_SETTING_OLD = "oppo_weather_info_resident";
    public static final String OPLUS_RESIDENT_WEATHER_INFO_SETTING = "oplus_weather_info_resident";
    private static final String SHARED_PREFS_NAME_LOCATION_WEATHER_INFO = "location_weather_info";
    private static final String SHARED_PREFS_NAME_RESIDENT_WEATHER_INFO = "resident_weather_info";
    public static final int RESPONSE_CODE_LOCATION_PERMISSION_ALlWAS_ALLOWED = 0;
    public static final int RESPONSE_CODE_LOCATION_PERMISSION_USING_ALLOWED = -1;
    public static final int RESPONSE_CODE_WEATHER_SERVICE_CLOSED = -4;
    public static final int REQUEST_LOCATION_FAILED = -3;
    public static final String WEATHER_SERVICE_PRIVACY_URI = "content://com.oplusos.weather.service.provider.data/privacy";
    //在天气服务SDK版本大于等于8002001时，才存在getLocationCityDataAndUpdateWeatherInfoIfNeeded方法，以下版本使用getLocationSecureSettingsDataIfNullAfterDoLocation
    public static final int WEATHER_SDK_VERSION_8002001 = 8002001;
    //天气服务12.1.1引入隐私政策
    public static final int WEATHER_SDK_VERSION_12001000 = 12001000;

    /**
     * 天气服务14.7.0版本开始把定位时间间隔调为3min
     */
    public static final int WEATHER_SERVICE_VERSION_14007000 = 14007000;

    /**
     * 天气服务15.6.4版本支持双时钟定位失败图标
     */
    public static final int WEATHER_SERVICE_VERSION_15006004 = 15006004;

    protected static final String TAG = "ClockWidget.BaseWidgetViewHelper";
    private static final String METHOD_CHECK_WEATHER_SERVICE_LOCATION_PERMISSION = "checkWeatherServiceLocationPermission";
    private static final String METHOD_GET_RESIDENT_CITY_WEATHER_INFO = "getResidentSecureSettingsData";
    private static final String METHOD_GET_LOCATION_CITY_WEATHER_INFO = "getLocationCityDataAndUpdateWeatherInfoIfNeeded";
    private static final String METHOD_GET_LOCATION_DATE = "getLocationSecureSettingsDataIfNullAfterDoLocation";
    private static final String METHOD_PUT_WEATHER_SETTING = "putWeatherSettings";
    private static final String KEY_HAS_WIDGET = "clock_weather_widget_enabled";
    private static final String KEY_SUPPORT_SECOND_CITY = "setting_support_second_city";
    private static final String KEY_TIME_ZONE_NAME = "timezone_name";
    private static final int VALUE_SUPPORT_SECOND_CITY = 1;

    private static final CharSequence NUMBER_CHAR_ONE = "1";
    private static final int NUMBER_INT_ONE = 1;

    private volatile static BaseWidgetViewHelper sInstance;
    private WeatherEntity mNewLocationWeatherEntity;
    private WeatherEntity mNewResidentWeatherEntity;

    private boolean mToCheckLocationSwitch = false;
    private boolean mForResume = false;
    private Handler mHandler;

    public static BaseWidgetViewHelper getInstance() {
        if (sInstance == null) {
            synchronized (BaseWidgetViewHelper.class) {
                if (sInstance == null) {
                    sInstance = new BaseWidgetViewHelper();
                }
            }
        }
        return sInstance;
    }

    private BaseWidgetViewHelper() {
    }

    /**
     * get local weather info
     *
     * @return WeatherEntity
     */
    public WeatherEntity getLocalWeatherInfo(Context context) {
        if (context == null) {
            Log.d(TAG, "getLocalWeatherInfo context is null");
            return null;
        }
        if (ClockWidgetUtils.commonWeatherServiceExist()) {
            Log.d(TAG, "getLocalWeatherInfo isCommonWeatherServiceExist is true");
            if (mNewLocationWeatherEntity == null) {
                mNewLocationWeatherEntity = getWeatherFromSharedPreferences(context, SHARED_PREFS_NAME_LOCATION_WEATHER_INFO);
            }
            return mNewLocationWeatherEntity;
        } else {
            String weatherInfo = Settings.Secure.getString(context.getContentResolver(), OPLUS_WEATHER_INFO_SETTING);
            if (TextUtils.isEmpty(weatherInfo)) {
                weatherInfo = Settings.Secure.getString(context.getContentResolver(), OPLUS_WEATHER_INFO_SETTING_OLD);
            }
            if (DeviceUtils.isExpVersion(context)) {
                Log.d(TAG, "getLocalWeatherInfo,weatherInfo");
            } else {
                Log.d(TAG, "getLocalWeatherInfo,weatherInfo:" + weatherInfo);
            }
            return parserWeatherInfo(weatherInfo);
        }
    }

    private WeatherEntity getWeatherFromSharedPreferences(Context context, String key) {
        String weatherInfo = PrefUtils.getString(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, key, null);
        if (weatherInfo == null) {
            return null;
        }
        return parserWeatherInfo(weatherInfo);
    }

    private void saveWeatherToSharedPreferences(Context context, String key, WeatherEntity weatherEntity) {
        if (weatherEntity == null) {
            PrefUtils.remove(context, key);
            return;
        }
        String weatherInfo = weatherEntity.getDegree()
                + "::" + weatherEntity.getType()
                + "::" + weatherEntity.getDescription()
                + "::" + weatherEntity.getCentigrade()
                + "::" + weatherEntity.getCityCode()
                + "::" + weatherEntity.getCityName()
                + "::" + weatherEntity.getDefaultName()
                + "::" + weatherEntity.getTimeZone()
                + "::" + weatherEntity.getParentCityCode()
                + "::" + weatherEntity.getLocationResultCode();
        PrefUtils.putString(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, key, weatherInfo);
    }


    /**
     * get local weather info
     *
     * @return WeatherEntity
     */
    public WeatherEntity getResidentWeatherInfo(Context context) {
        if (context == null) {
            Log.d(TAG, "getResidentWeatherInfo context is null");
            return null;
        }
        if (ClockWidgetUtils.commonWeatherServiceExist()) {
            Log.d(TAG, "getResidentWeatherInfo isCommonWeatherServiceExist=true ");
            if (mNewResidentWeatherEntity == null) {
                mNewResidentWeatherEntity = getWeatherFromSharedPreferences(context, SHARED_PREFS_NAME_RESIDENT_WEATHER_INFO);
            }
            return mNewResidentWeatherEntity;
        } else {
            String weatherInfo = Settings.Secure.getString(context.getContentResolver(), OPLUS_RESIDENT_WEATHER_INFO_SETTING);
            if (TextUtils.isEmpty(weatherInfo)) {
                weatherInfo = Settings.Secure.getString(context.getContentResolver(), OPLUS_RESIDENT_WEATHER_INFO_SETTING_OLD);
            }
            if (DeviceUtils.isExpVersion(context)) {
                Log.d(TAG, "getResidentWeatherInfo weatherInfo");
            } else {
                Log.d(TAG, "getResidentWeatherInfo weatherInfo=" + weatherInfo);
            }
            WeatherEntity entity = parserWeatherInfo(weatherInfo);
            if (entity == null) {
                Log.d(TAG, "getResidentWeatherInfo entity is null");
                return null;
            }
            if (TextUtils.isEmpty(entity.getTimeZone())) {
                Log.d(TAG, "getResidentWeatherInfo getTimeZone is null");
                return null;
            }
            return entity;
        }
    }

    /**
     * parser weather inifo
     *
     * @return WeatherEntity
     */
    private WeatherEntity parserWeatherInfo(String weatherInfo) {
        WeatherEntity entity = new WeatherEntity();
        setEmptyString(entity);
        if (TextUtils.isEmpty(weatherInfo)) {
            Log.d(TAG, "parserWeatherInfo -- weatherInfo is null!");
            return null;
        }
        // correct weather info example is 26::4::Showers
        String[] temp = weatherInfo.split("::");
        for (int i = 0; i < temp.length; i++) {
            if ("null".equals(temp[i])) {
                temp[i] = "";
            }
        }
        if (temp.length > Constants.NUMBER_0) {
            entity.setDegree(CommonUtil.convertNumberToLocal(temp[Constants.NUMBER_0].trim()));
        }
        if (temp.length > Constants.NUMBER_1) {
            entity.setType(temp[Constants.NUMBER_1].trim());
        }
        if (temp.length > Constants.NUMBER_2) {
            entity.setDescription(temp[Constants.NUMBER_2].trim());
        }
        if (temp.length > Constants.NUMBER_3) {
            entity.setCentigrade(temp[Constants.NUMBER_3].trim());
        }
        if (temp.length > Constants.NUMBER_4) {
            entity.setCityCode(temp[Constants.NUMBER_4].trim());
        }
        if (temp.length > Constants.NUMBER_5) {
            entity.setCityName(temp[Constants.NUMBER_5].trim());
        }
        if (temp.length > Constants.NUMBER_6) {
            entity.setDefaultName(temp[Constants.NUMBER_6].trim());
        }
        if (temp.length > Constants.NUMBER_7) {
            entity.setTimeZone(temp[Constants.NUMBER_7].trim());
        }
        if (temp.length > Constants.NUMBER_8) {
            entity.setParentCityCode(temp[Constants.NUMBER_8].trim());
        }
        if (temp.length > Constants.NUMBER_9) {
            try {
                entity.setLocationResultCode(Integer.parseInt(temp[Constants.NUMBER_9]));
            } catch (NumberFormatException e) {
                Log.e(TAG, "parserWeatherInfo:" + e.getMessage());
            }
        }
        return entity;
    }

    private void setEmptyString(WeatherEntity weatherEntity) {
        weatherEntity.setTimeZone("");
        weatherEntity.setType("");
        weatherEntity.setCentigrade("");
        weatherEntity.setDefaultName("");
        weatherEntity.setCityName("");
        weatherEntity.setDescription("");
        weatherEntity.setDegree("");
        weatherEntity.setCityCode("");
        weatherEntity.setDegree("");
    }

    /**
     * set the type of show clock
     *
     * @param context             Context
     * @param isDoubleClockOpened double clock is opened
     */
    public void setCurClockType(Context context, boolean isDoubleClockOpened) {
        ClockTypeSetting.setClockType(getCurClockType(context, isDoubleClockOpened));
    }

    /**
     * get the type of show clock
     *
     * @param context             Context
     * @param isDoubleClockOpened double clock is opened
     */
    public int getCurClockType(Context context, boolean isDoubleClockOpened) {
        WeatherEntity localWeatherEntity = getLocalWeatherInfo(context);
        WeatherEntity residentWeatherEntity = getResidentWeatherInfo(context);

        if (DeviceUtils.isExpVersion(context)) {
            Log.d(TAG, "isExpVersion getCurClockType isDoubleClockOpened:" + isDoubleClockOpened + ",localWeatherEntity:" + localWeatherEntity
                    + ",residentWeatherEntity:" + residentWeatherEntity);
        } else {
            Log.d(TAG, "getCurClockType isDoubleClockOpened:" + isDoubleClockOpened + ",localWeatherEntity:" + localWeatherEntity
                    + ",residentWeatherEntity:" + residentWeatherEntity);
        }
        //设置中的双时钟开关关闭 （注意这里有坑：当在添加常驻地界面删除常驻地城市时，从设置中获取的开关状态为打开）
        if (!isDoubleClockOpened) {
            return ClockType.SINGLE_CLOCK;
        }
        //TODO 外销机 localWeatherEntity获取为null
        if ((residentWeatherEntity != null) && (localWeatherEntity != null)) {
            //3037013 恢复OS12判断定位城市和常驻地城市是否属于同一时区 是同一时区则显示单时钟（和OS11一致）
            if (isSameTimezone(context, localWeatherEntity, residentWeatherEntity)) {
                Log.d(TAG, "getCurClockType is same timezone:");
                return ClockType.SINGLE_CLOCK;
            }
            if (Utils.isAboveOS12()) {
                //2990787 OS12及以上天气服务关闭时，显示单时钟
                if (!agreeServiceStatement()) {
                    Log.d(TAG, "weather service is closed");
                    return ClockType.SINGLE_CLOCK;
                }
            }
            //判断定位城市和常驻地城市cityCode是否相同 相同则为单时钟
            if (!supportNewDoubleClock(context)
                    && isContainsRelationship(localWeatherEntity, residentWeatherEntity)) {
                Log.d(TAG, "getCurClockType is same cityCode");
                return ClockType.SINGLE_CLOCK;
            }
        } else {
            if ((localWeatherEntity == null) && (residentWeatherEntity == null)) {
                Log.d(TAG, "getCurClockType resident is null local is null");
                return ClockType.SINGLE_CLOCK;
            }
            //常驻地城市数据为空 则显示单时钟
            if (residentWeatherEntity == null) {
                Log.d(TAG, "getCurClockType resident is null");
                return ClockType.SINGLE_CLOCK;
            } else {
                //这里也需要判断常驻地城市cityCode是否存在 不存在则显示单时钟
                if (TextUtils.isEmpty(residentWeatherEntity.getCityCode()) && TextUtils.isEmpty(residentWeatherEntity.getParentCityCode())) {
                    Log.d(TAG, "getCurClockType cityCode =null parentCityCode=null");
                    return ClockType.SINGLE_CLOCK;
                }
                if (isSameTimezone(context, null, residentWeatherEntity)) {
                    return ClockType.SINGLE_CLOCK;
                }
            }
        }

        return ClockType.DOUBLE_CLOCK;
    }

    private boolean isSameTimezone(
            Context context,
            WeatherEntity localWeatherEntity,
            WeatherEntity residentWeatherEntity
    ) {
        String currentTimeZone = "";
        if (supportNewDoubleClock(context)) {
            currentTimeZone = ClockWidgetManager.getInstance().getSystemTimezone();
            Log.d(TAG, "isSameTimezone:" + currentTimeZone);
            if (TextUtils.equals(currentTimeZone, residentWeatherEntity.getTimeZone())) {
                Log.d(TAG, "getCurClockType is same timezone:");
                return true;
            } else {
                /* 在常驻城市与定位城市时区不同时，再通过provider获取常驻城市做时区校验，减少跨进程调用*/
                currentTimeZone = ClockWidgetUtils.getLocalTimeZone();
                Log.d(TAG, "isSameTimezone currentTimeZone: " + currentTimeZone);
                if (TextUtils.isEmpty(currentTimeZone)) {
                    return false;
                }
                String timeZoneName = getResidentTimeZoneName();
                String residentTimeZoneString = TextUtils.isEmpty(timeZoneName)
                        ? null
                        : ClockWidgetUtils.getTargetTimeZone(timeZoneName);
                Log.d(TAG, "isSameTimezone residentTimeZone: " + residentTimeZoneString);
                /* 常驻地城市时区信息与provider获取的常驻城市时区信息不一致时，需要重新配置 */
                if (!TextUtils.isEmpty(residentTimeZoneString)) {
                    String residentTimeZone = ClockWidgetUtils.getTimeZone(residentTimeZoneString) + "";
                    Log.d(TAG, "isSameTimezone residentTimeZone: " + residentTimeZone);
                    if (!TextUtils.equals(residentWeatherEntity.getTimeZone(), residentTimeZone)) {
                        mNewResidentWeatherEntity.setTimeZone(residentTimeZone);
                        saveWeatherToSharedPreferences(context, SHARED_PREFS_NAME_RESIDENT_WEATHER_INFO, mNewResidentWeatherEntity);
                    }
                }
                return TextUtils.equals(currentTimeZone, residentTimeZoneString);
            }
        } else {
            if (localWeatherEntity == null) {
                return false;
            } else {
                currentTimeZone = localWeatherEntity.getTimeZone();
            }
        }
        if (TextUtils.equals(currentTimeZone, residentWeatherEntity.getTimeZone())) {
            Log.d(TAG, "getCurClockType is same timezone:");
            return true;
        }
        return false;
    }

    private boolean isContainsRelationship(WeatherEntity localWeatherEntity, WeatherEntity residentWeatherEntity) {

        String localCityCode = localWeatherEntity.getCityCode();
        String residentCityCode = residentWeatherEntity.getCityCode();
        String localParentCityCode = localWeatherEntity.getParentCityCode();
        String residentParentCityCode = residentWeatherEntity.getParentCityCode();

        if (isEqualsStringWithContent(localCityCode, residentCityCode)) {
            return true;
        }
        if (isEqualsStringWithContent(localCityCode, residentParentCityCode)) {
            return true;
        }
        if (isEqualsStringWithContent(residentCityCode, localParentCityCode)) {
            return true;
        }
        return false;
    }

    private boolean isEqualsStringWithContent(String str1, String str2) {
        if (TextUtils.isEmpty(str1) || TextUtils.isEmpty(str2)) {
            return false;
        }
        return str1.equals(str2);
    }

    private void fillNewWeatherEntityData(SecureSettingsData secureSettingsData, WeatherEntity weatherEntity) {
        if ((secureSettingsData == null) || (weatherEntity == null)) {
            Log.d(TAG, "fillNewWeatherEntityData secureSettingsData or weatherEntity is null");
            return;
        }
        weatherEntity.setCentigrade((secureSettingsData.tempUnit == null)
                ? "" : (Utils.isAboveOS13() ? AlarmClockApplication.getInstance().getResources().getString(R.string.centigrade_t)
                : secureSettingsData.tempUnit));
        weatherEntity.setCityCode((secureSettingsData.cityCode == null) ? "" : secureSettingsData.cityCode);
        //<EMAIL>, 2019/07/30, change for bug 2199691  【19031】 The resident city show address in English ,
        // when the system in Simplified Chinese.
        weatherEntity.setCityName((secureSettingsData.cityNameLocal == null) ? "" : secureSettingsData.cityNameLocal);
        weatherEntity.setDefaultName((secureSettingsData.cityNameEn == null) ? "" : secureSettingsData.cityNameEn);
        //#endif
        weatherEntity.setDegree((secureSettingsData.temp == null) ? "" : CommonUtil.convertNumberToLocal(secureSettingsData.temp));
        weatherEntity.setType((String.valueOf(secureSettingsData.weatherType) == null) ? "" : String.valueOf(secureSettingsData.weatherType));
        weatherEntity.setDescription((secureSettingsData.weatherDesc == null) ? "" : secureSettingsData.weatherDesc);
        weatherEntity.setParentCityCode((secureSettingsData.parentCityCode == null) ? "" : secureSettingsData.parentCityCode);
        weatherEntity.setTimeZone((secureSettingsData.timeZone == null) ? "" : secureSettingsData.timeZone);
        weatherEntity.setLocationResultCode((secureSettingsData.locationResultCode));
    }

    public void updateLocateResult(int locResult) {
//        双时钟才显示定位图标：执行 更新定位 成功 失败图标
        if (mNewLocationWeatherEntity != null) {
            int oldLocationResultCode = mNewLocationWeatherEntity.getLocationResultCode();
            Log.d(TAG, "updateLocateResult new " + locResult + " old: "  + oldLocationResultCode);
            if (locResult != oldLocationResultCode) {
                mNewLocationWeatherEntity.setLocationResultCode(locResult);
                ClockWidgetManager.getInstance().setLastLocateResult(locResult);
                saveWeatherToSharedPreferences(AlarmClockApplication.getInstance().getApplicationContext(),
                        SHARED_PREFS_NAME_LOCATION_WEATHER_INFO, mNewLocationWeatherEntity);
            }
            if (ClockTypeSetting.isDoubleClockType()
                    && ((locResult == Constants.LOCATION_ERROR_CODE_SUCCESS)
                    != (oldLocationResultCode == Constants.LOCATION_ERROR_CODE_SUCCESS))) {
                ClockWidgetManager.getInstance().updateLocalData(true);
            }
        }
    }

    private synchronized void notifyChangedInfo(WeatherEntity newWeatherEntity,
                                                WeatherEntity oldWeatherEntity, boolean locCity) {

        boolean isCityChanged = isCityChanged(newWeatherEntity, oldWeatherEntity);
        boolean isWeatherInfoChanged = isWeatherInfoChanged(newWeatherEntity, oldWeatherEntity);
        boolean isLocResultChanged = isLocResultChanged(newWeatherEntity, oldWeatherEntity, locCity);

        if (isCityChanged || isWeatherInfoChanged || isLocResultChanged) {
            ClockWidgetManager.getInstance().updateLocalData(true);
        }
    }

    private boolean isCityChanged(WeatherEntity newWeatherEntity, WeatherEntity oldWeatherEntity) {
        if (newWeatherEntity == oldWeatherEntity) {
            return false;
        }
        if ((newWeatherEntity != null) && (oldWeatherEntity != null)) {
            return !TextUtils.equals(newWeatherEntity.getCityCode(), oldWeatherEntity.getCityCode());
        }
        return true;
    }

    private boolean isWeatherInfoChanged(WeatherEntity newWeatherEntity, WeatherEntity oldWeatherEntity) {
        if (newWeatherEntity == oldWeatherEntity) {
            return false;
        }
        if ((newWeatherEntity != null) && (oldWeatherEntity != null)) {

            return !TextUtils.equals(newWeatherEntity.getCentigrade(), oldWeatherEntity.getCentigrade())
                    || !TextUtils.equals(newWeatherEntity.getDegree(), oldWeatherEntity.getDegree())
                    || !TextUtils.equals(newWeatherEntity.getDescription(), oldWeatherEntity.getDescription())
                    || !TextUtils.equals(newWeatherEntity.getType(), oldWeatherEntity.getType());
        }

        return true;

    }

    private boolean isLocResultChanged(WeatherEntity newWeatherEntity, WeatherEntity oldWeatherEntity, boolean isLocation) {
        if (newWeatherEntity == oldWeatherEntity) {
            return false;
        }
        if (!isLocation) {
            return false;
        }
        if ((newWeatherEntity != null) && (oldWeatherEntity != null)) {
            return newWeatherEntity.getLocationResultCode() != oldWeatherEntity.getLocationResultCode();
        }
        return false;
    }

    private boolean mJustCheck = true;

    public void checkWeatherServiceLocationPermission(final Context context, boolean isJustCheck, final boolean toCheckLocationSwitch, final boolean forResume) {
        try {
            Log.d(TAG, "checkWeatherServiceLctPermission isJustCheck=" + isJustCheck + " toCheckLctSwitch=" + toCheckLocationSwitch + " forResume=" + forResume);
            mJustCheck = isJustCheck;
            mToCheckLocationSwitch = toCheckLocationSwitch;
            mForResume = forResume;

            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_CHECK_WEATHER_SERVICE_LOCATION_PERMISSION)
                    .setPackageName(context.getPackageName())
                    .setParams(null);

            WeatherBaseDataTask checkLocationPermissionTask = new WeatherBaseDataTask(Integer.class, context,
                    weatherRequest, new BaseCallBack<Integer>() {
                @Override
                public void onSuccess(Integer permissionCode) {
                    int intCode = permissionCode.intValue();
                    Log.d(TAG, "checkWeatherServiceLctPermission onSuccess code="
                            + intCode + " mJustCheck=" + mJustCheck);
                    if (!mJustCheck) {
                        if (RESPONSE_CODE_LOCATION_PERMISSION_ALlWAS_ALLOWED == intCode) {
                            requestLocationWeatherInfo(context, true);
                            requestResidentWeatherInfo(context);
                        } else if (!mForResume) {
                            if (RESPONSE_CODE_LOCATION_PERMISSION_USING_ALLOWED == intCode) {
                                requestLocationWeatherInfo(context, false);
                                requestResidentWeatherInfo(context);
                            }
                            requestWeatherServiceLocationPermission(context);
                        }
                    }
                    locationPermissionCallBack(permissionCode, mToCheckLocationSwitch);
                }

                @Override
                public void onFail(String s) {
                    Log.d(TAG, "checkWeatherServiceLctPermission onFail s=" + s);
                    locationPermissionCallBack(REQUEST_LOCATION_FAILED, mToCheckLocationSwitch);
                }
            });
            checkLocationPermissionTask.startServiceRequest();
        } catch (Exception e) {
            Log.e(TAG, "checkWeatherServiceLctPermission e=" + e.getMessage(), e);
        }
    }

    /**
     * 内销可关闭天气服务隐私声明，如是关闭状态需调用此方法显示启用弹窗
     *
     * @param context
     */
    public void getWeatherServiceStatus(Context context) {
        try {

            Context application = AlarmClockApplication.getInstance();
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_CHECK_WEATHER_SERVICE_LOCATION_PERMISSION)
                    .setPackageName(context.getPackageName())
                    .setParams(null);

            WeatherBaseDataTask checkLocationPermissionTask = new WeatherBaseDataTask(Integer.class, application,
                    weatherRequest, new BaseCallBack<Integer>() {
                @Override
                public void onSuccess(Integer permissionCode) {
                    Log.d(TAG, "getWeatherServiceStatus onSuccess code=" + permissionCode);

                    if (permissionCode == RESPONSE_CODE_WEATHER_SERVICE_CLOSED) {
                        getMainHandler().post(() -> PackageDisabledManager.INSTANCE.openEnableActivity(context, new WeatherServiceDisableDomesticInfo()));
                    } else {
                        ClockWidgetManager.getInstance().checkLocationPermission(false, true);
                    }
                }

                @Override
                public void onFail(String s) {
                    Log.d(TAG, "getWeatherServiceStatus onFail s=" + s);

                }
            });
            checkLocationPermissionTask.startServiceRequest();
        } catch (Exception e) {
            Log.e(TAG, "getWeatherServiceStatus e=" + e.getMessage(), e);
        }
    }

    private Handler getMainHandler() {
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        return mHandler;
    }

    public void openWeatherService() {
        Context context = AlarmClockApplication.getInstance();

        Uri contentUri = Uri.parse(WEATHER_SERVICE_PRIVACY_URI);
        ContentValues values = new ContentValues();
        values.put("statement_agreed", 1); // 0:不同意 1：同意
        int count = context.getContentResolver().update(contentUri, values, null, null);
        Log.i(TAG, "openWeatherService success=" + (count > 0));
    }

    public boolean agreeServiceStatement() {
        Cursor cursor = null;
        try {
            Context context = AlarmClockApplication.getInstance();
            Uri contentUri = Uri.parse(WEATHER_SERVICE_PRIVACY_URI);
            cursor = context.getContentResolver().query(contentUri, null, null, null, null);

            if ((cursor != null) && cursor.moveToFirst()) {
                int agree = cursor.getInt(cursor.getColumnIndexOrThrow("statement_agreed"));
                Log.i(TAG, "agreeServiceStatement agree=" + agree);
                return agree == 1;
            }

        } catch (Exception e) {
            Log.e(TAG, "agreeServiceStatement error=" + e);
        } finally {
            try {
                if (cursor != null) {
                    cursor.close();
                }
            } catch (Exception e) {
                Log.e(TAG, "agreeServiceStatement close error=" + e);
            }
        }
        //读不到获取异常的时候，默认开关状态应为true
        return true;
    }

    public void requestLocationWeatherInfo(final Context context, boolean isNeedLocation) {
        try {
            ArrayList<String> requestUpdateParams = null;
            String methodName = METHOD_GET_LOCATION_CITY_WEATHER_INFO;
            if (ClockWidgetUtils.weatherServiceVersionSupport()) {
                methodName = METHOD_GET_LOCATION_DATE;
            } else {
                requestUpdateParams = new ArrayList<>();
                requestUpdateParams.add(String.valueOf(isNeedLocation));
            }
            Log.d(TAG, "requestLctWeatherInfo methodName=" + methodName + " isNeed=" + isNeedLocation);
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(methodName)
                    .setPackageName(context.getPackageName())
                    .setParams(requestUpdateParams);

            //maybe async or synchronization,so should execute on a thread
            WeatherBaseDataTask locationWeatherInfoNewTask = new WeatherBaseDataTask(SecureSettingsData.class, context,
                    weatherRequest, new BaseCallBack<SecureSettingsData>() {
                @Override
                public void onSuccess(SecureSettingsData secureSettingsData) {
                    try {
                        if (DeviceUtils.isExpVersion(context)) {
                            Log.d(TAG, "requestLctWeatherInfo secureSettingsData = null? " + (secureSettingsData == null));
                        } else {
                            Log.d(TAG, "requestLctWeatherInfo secureSettingsData=" + secureSettingsData);
                        }
                        if (secureSettingsData != null) {
                            WeatherEntity lastLocationWeather = (mNewLocationWeatherEntity != null) ? mNewLocationWeatherEntity.clone() : null;
                            if (mNewLocationWeatherEntity == null) {
                                mNewLocationWeatherEntity = new WeatherEntity();
                            }
                            fillNewWeatherEntityData(secureSettingsData, mNewLocationWeatherEntity);
                            ClockWidgetManager.getInstance().setLastLocateResult(mNewLocationWeatherEntity.getLocationResultCode());
                            notifyChangedInfo(mNewLocationWeatherEntity, lastLocationWeather, true);
                            saveWeatherToSharedPreferences(context, SHARED_PREFS_NAME_LOCATION_WEATHER_INFO, mNewLocationWeatherEntity);
                        } else {
                            ClockWidgetManager.getInstance().updateLocalData(false);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "requestLctWeatherInfo,onWeatherInfoChanged,Exception:" + e.getMessage(), e);
                        ClockWidgetManager.getInstance().updateLocalData(false);
                    }
                }

                @Override
                public void onFail(String s) {
                    Log.w(TAG, "requestLctWeatherInfo,onFail:" + s);
                    ClockWidgetManager.getInstance().updateLocalData(false);
                }
            });
            locationWeatherInfoNewTask.startServiceRequest();
        } catch (Exception e) {
            Log.e(TAG, "requestLctWeatherInfo error", e);
            ClockWidgetManager.getInstance().updateLocalData(false);
        }
    }

    public void requestResidentWeatherInfo(Context context) {
        try {
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_GET_RESIDENT_CITY_WEATHER_INFO)
                    .setPackageName(context.getPackageName())
                    .setParams(null);

            //maybe async or synchronization,so should execute on a thread
            WeatherBaseDataTask residentWeatherBaseDataTask = new WeatherBaseDataTask(SecureSettingsData.class, context,
                    weatherRequest, new BaseCallBack<SecureSettingsData>() {
                @Override
                public void onSuccess(SecureSettingsData secureSettingsData) {
                    try {
                        if (DeviceUtils.isExpVersion(context)) {
                            Log.d(TAG, "requestNewResidentWeatherInfo,secureSettingsData == null ?" + (secureSettingsData == null));
                        } else {
                            Log.d(TAG, "requestNewResidentWeatherInfo,secureSettingsData:" + secureSettingsData);
                        }
                        WeatherEntity lastResidentWeather = (mNewResidentWeatherEntity != null) ? mNewResidentWeatherEntity.clone() : null;
                        if (secureSettingsData == null) {
                            mNewResidentWeatherEntity = null;
                        } else {
                            if (mNewResidentWeatherEntity == null) {
                                mNewResidentWeatherEntity = new WeatherEntity();
                            }
                            fillNewWeatherEntityData(secureSettingsData, mNewResidentWeatherEntity);
                        }
                        saveWeatherToSharedPreferences(context, SHARED_PREFS_NAME_RESIDENT_WEATHER_INFO, mNewResidentWeatherEntity);
                        notifyChangedInfo(mNewResidentWeatherEntity, lastResidentWeather, false);
                    } catch (Exception e) {
                        Log.e(TAG, "requestNewResidentWeatherInfo,onFail error=", e);
                        ClockWidgetManager.getInstance().updateLocalData(false);
                    }
                }

                @Override
                public void onFail(String s) {
                    Log.w(TAG, "requestNewResidentWeatherInfo,onFail:" + s);
                    ClockWidgetManager.getInstance().updateLocalData(false);
                }
            });
            residentWeatherBaseDataTask.startServiceRequest();
        } catch (Exception e) {
            String contextStr = (context != null) ? " applicationContext:" + context.getApplicationContext() : " context:null";
            Log.e(TAG, "requestNewResidentWeatherInfo,Exception:" + e + contextStr);
            ClockWidgetManager.getInstance().updateLocalData(false);
        }
    }

    public void requestWeatherServiceLocationPermission(Context context) {
        try {
            Log.d(TAG, "requestWeatherServiceLctPermission");
            ClockWidgetManager.getInstance().requestLocationPermission(context);
        } catch (Exception e) {
            Log.e(TAG, "requestWeatherServiceLctPermission,Exception:" + e);
        }
    }

    public void putWeatherSetting(Context context, boolean hasWidget) {
        try {
            Log.d(TAG, "putWeatherSetting:" + hasWidget);
            String settingInfo = null;
            try {
                JSONObject widgetInfo = new JSONObject();
                widgetInfo.put(KEY_HAS_WIDGET, hasWidget);
                settingInfo = widgetInfo.toString();
            } catch (JSONException e) {
                Log.e(TAG, "put weather setting info exception:" + e.getMessage());
                return;
            }
            if (TextUtils.isEmpty(settingInfo)) {
                Log.e(TAG, "put weather setting info is empty!");
                return;
            }
            Log.d(TAG, "putWeatherSetting info:" + settingInfo);
            ArrayList<String> params = new ArrayList<>();
            params.add(settingInfo);
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_PUT_WEATHER_SETTING)
                    .setPackageName(context.getPackageName())
                    .setParams(params);

            WeatherBaseDataTask putSettingTask = new WeatherBaseDataTask(
                    Integer.class, context, weatherRequest, null);
            putSettingTask.startServiceRequest();
        } catch (Exception e) {
            Log.e(TAG, "checkWeatherServiceLctPermission e=" + e.getMessage(), e);
        }
    }

    private void locationPermissionCallBack(int permissionCode, boolean toCheckLocationSwitch) {
        if ((permissionCode == RESPONSE_CODE_LOCATION_PERMISSION_ALlWAS_ALLOWED) && toCheckLocationSwitch
                && !CommonUtil.isLocationEnabled(AlarmClockApplication.getInstance().getApplicationContext())) {
            ClockWidgetManager.getInstance().startAppByIntent(Settings.ACTION_LOCATION_SOURCE_SETTINGS, null, null);
        } else {
            ClockWidgetManager.getInstance().updateWeatherInfo();
        }
    }

    public CharSequence redTextOneAboveOS13(Context context, String hour) {
        //一加内销，os13以下不兼容红1
        if (DeviceUtils.isApi31()) {
            return hour;
        }
        if ((!DeviceUtils.isSupportRedOne()) || (context == null) || (TextUtils.isEmpty(hour))) {
            return hour;
        }
        SpannableStringBuilder fullTimeBuilder = new SpannableStringBuilder(hour);
        if (hour.contains(NUMBER_CHAR_ONE)) {
            char[] hourChars = hour.toCharArray();
            for (int i = 0; i < hourChars.length; i++) {
                if (TextUtils.equals(NUMBER_CHAR_ONE, String.valueOf(hourChars[i]))) {
                    fullTimeBuilder.setSpan(new ForegroundColorSpan(-65536),
                            i, i + 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                }
            }
        }
        return fullTimeBuilder;
    }

    public CharSequence redTextOne(Context context, String fullTime, String hour) {
        if ((!DeviceUtils.isSupportRedOne()) || (context == null) || (TextUtils.isEmpty(fullTime)) || (TextUtils.isEmpty(hour))) {
            return fullTime;
        }
        /*一加特有字体，必须将":"使用unicode编码显示，不然不能居中显示
        if (fullTime.contains(ClockWidgetManager.COLON_STR)) {
            fullTime = fullTime.replace(ClockWidgetManager.COLON_STR, ClockWidgetManager.COLON_UNICODE);
        }*/
        SpannableStringBuilder fullTimeBuilder = new SpannableStringBuilder(fullTime);
        if (hour.contains(NUMBER_CHAR_ONE)) {
            char[] hourChars = hour.toCharArray();
            for (int i = 0; i < hourChars.length; i++) {
                if (TextUtils.equals(NUMBER_CHAR_ONE, String.valueOf(hourChars[i]))) {
                    fullTimeBuilder.setSpan(new ForegroundColorSpan(-65536),
                            i, i + 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
                }
            }
        }
        return fullTimeBuilder;
    }

    /**
     * 是否支持新的双时钟逻辑，新的逻辑，双时钟显示 对比系统时区和第二城市时区不同
     * KEY_SUPPORT_SECOND_CITY 找不到表示没有支持
     *
     * @param context
     * @return true 支持，false 不支持
     */
    private boolean supportNewDoubleClock(Context context) {
        try {
            int support = Settings.Secure.getInt(context.getContentResolver(), KEY_SUPPORT_SECOND_CITY);
            Log.d(TAG, "supportNewDoubleClock:" + support);
            return true;
        } catch (Settings.SettingNotFoundException exception) {
            Log.e(TAG, "supportNewDoubleClock exception:" + exception.getMessage());
            return false;
        }
    }

    /**
     * 通过provider获取常驻地的时区名
     */
    public String getResidentTimeZoneName() {
        Context context = AlarmClockApplication.getInstance();
        String timeZoneName = null;
        Cursor cursor = null;
        try {
            Uri contentUri = Uri.parse(OPLUS_NEW_RESIDENT_CITY_INFO_URI);
            cursor = context.getContentResolver().query(contentUri, new String[]{KEY_TIME_ZONE_NAME}, null, null, null);

            if ((cursor != null) && cursor.moveToFirst()) {
                int timeZoneIndex = cursor.getColumnIndexOrThrow(KEY_TIME_ZONE_NAME);
                timeZoneName = cursor.getString(timeZoneIndex);
                Log.i(TAG, "getResidentTimeZoneName timeZoneName=" + timeZoneName);
            }

        } catch (Exception e) {
            Log.e(TAG, "getResidentTimeZoneName error=" + e);
        } finally {
            try {
                if (cursor != null) {
                    cursor.close();
                }
            } catch (Exception e) {
                Log.e(TAG, "getResidentTimeZoneName close error=" + e);
            }
        }
        return timeZoneName;
    }

}
