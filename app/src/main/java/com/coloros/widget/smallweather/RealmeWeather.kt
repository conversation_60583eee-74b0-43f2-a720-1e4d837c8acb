/***********************************************************
 * Copyright (C), 2008-2019, RPlus Mobile Comm Corp., Ltd.
 * OPLUSOS_EDIT
 * File:RPlusWeather.java
 * Description: a new widget
 * Version:1.0
 * Date :2019/05/28
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.coloros.widget.smallweather

class RealmeWeather : BaseAppWidget() {

    companion object {
        private const val TAG = "ClockWidget.RPlusWeather"
    }

    override fun haveWidget(): Boolean {
        return ClockWidgetManager.getInstance().haveDateAndWeatherWidget()
    }

    override fun getCurrentClass(): Class<*> {
        return javaClass
    }
}