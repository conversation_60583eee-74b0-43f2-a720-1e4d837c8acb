/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:OplusWeather.java
 * * Description:BaseWidgetView helper
 * * Version:1.0
 * * Date :2019/1/5
 * * Author:tanliang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.coloros.widget.smallweather

import com.oplus.clock.common.utils.Log

/**
 * 横版插件
 */
class OppoWeather : BaseAppWidget() {
    companion object {
        private const val TAG = "ClockWidget.OppoWeather"
    }

    override fun getCurrentClass(): Class<*> {
        Log.d(TAG, "getCurrentClass: $javaClass")
        return javaClass
    }

    override fun haveWidget(): Bo<PERSON>an {
        return ClockWidgetManager.getInstance().haveHorWidget()
    }
}