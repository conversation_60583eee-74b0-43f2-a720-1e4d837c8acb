/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * File:TimeInfoBuilder.java
 * * Description: build the time entity
 * * Version:1.0
 * * Date :2018/11/16
 * * Author:tanliang
 *  * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.widget.smallweather;


import android.content.Context;
import android.text.TextUtils;
import android.text.format.DateFormat;

import com.coloros.widget.entity.TimeInfo;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.CommonUtil;

import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;

import androidx.annotation.NonNull;

public class TimeInfoBuilder {

    private static final String TAG = "ClockWidget.TimeInfoBuilder";
    private static final int NUMBER_10 = 10;
    private static final int NUMBER_60 = 60;
    private static final float TIMEZONE_OFFSET = 3600000.0f;

    private String mFullFormat;
    private String mHourFormat = "h";
    private String mMunitesFormat = "mm";
    private String mAmpmFormat = "a";
    private Context mContext;
    private String mTimerSeparator = ClockWidgetManager.COLON_UNICODE;

    public TimeInfoBuilder(Context mContext) {
        this.mContext = mContext;
    }

    public static String getColonUnicode() {
        String symbol = ClockWidgetManager.COLON_UNICODE;
        String result = "";
        try {
            java.text.DateFormat shortTimeFormat = java.text.DateFormat.getTimeInstance(java.text.DateFormat.SHORT, Locale.getDefault());
            Calendar calendar = Calendar.getInstance();
            result = shortTimeFormat.format(calendar.getTime());

            if (!TextUtils.isEmpty(result)) {
                if (result.contains(ClockWidgetManager.COLON_STR)) {
                    symbol = ClockWidgetManager.COLON_UNICODE;
                } else {
                    symbol = String.valueOf(result.charAt(result.indexOf(".")));
                }
            }
        } catch (Exception e) {
            symbol = ClockWidgetManager.COLON_UNICODE;
            Log.e(TAG, "initDateFormat, fullTime dateFormat occurs an exception, set symbol = :, exception = ", e);
        }
        return symbol;
    }

    public static String getColon() {
        String symbol = ClockWidgetManager.COLON_STR;
        String result = "";
        try {
            java.text.DateFormat shortTimeFormat = java.text.DateFormat.getTimeInstance(java.text.DateFormat.SHORT, Locale.getDefault());
            Calendar calendar = Calendar.getInstance();
            result = shortTimeFormat.format(calendar.getTime());

            if (!TextUtils.isEmpty(result)) {
                if (result.contains(ClockWidgetManager.COLON_STR)) {
                    symbol = ClockWidgetManager.COLON_STR;
                } else {
                    symbol = String.valueOf(result.charAt(result.indexOf(".")));
                }
            }
        } catch (Exception e) {
            symbol = ClockWidgetManager.COLON_STR;
            Log.e(TAG, "initDateFormat, fullTime dateFormat occurs an exception, set symbol = :, exception = ", e);
        }
        return symbol;
    }

    private void initDateFormat() {
        String symbol = getColon();
        mTimerSeparator = symbol;
        String locale = Locale.getDefault().toString();
        if (DateFormat.is24HourFormat(mContext)) {
            mFullFormat = "HH" + symbol + "mm";
            mAmpmFormat = "";
            mHourFormat = "HH";

            if ("ja_JP".equals(locale) || "vi_VN".equals(locale)) {
                mFullFormat = "H" + symbol + "mm";
                mHourFormat = "H";
            }
        } else {
            mFullFormat = "h" + symbol + "mm";
            mAmpmFormat = "a";
            mHourFormat = "h";
        }
    }

    /**
     * get hour ,munites,date ,ampm info of current time
     *
     * @return TimeInfo
     */
    public TimeInfo getLocalTimeInfo() {
        return getTimeInfo(System.currentTimeMillis());
    }

    public TimeInfo getTimeInfo(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        return packageTimeInfo(calendar.getTimeZone().getDisplayName(), calendar);
    }

    public TimeInfo getTimeInfo(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        return packageTimeInfo(calendar.getTimeZone().getDisplayName(), calendar);
    }

    /**
     * get resident time inifo
     *
     * @return TimeInfo
     */
    public TimeInfo getResidentTimeInfo(String residentTimeZone) {
        Log.d(TAG, "getResidentTimeInfo");
        String zonStr = forMateTimeZone(residentTimeZone);
        if (zonStr == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(zonStr));
        return packageTimeInfo(zonStr, calendar);
    }

    public float getSystemTimezone() {
        TimeZone timeZone = TimeZone.getDefault();
        int offset = timeZone.getOffset(System.currentTimeMillis());
        return offset / TIMEZONE_OFFSET;
    }

    /**
     * package time Info
     *
     * @return TimeInfo
     */
    private TimeInfo packageTimeInfo(String timeZone, Calendar calendar) {
        initDateFormat();
        String year = (String) DateFormat.format("yyyy", calendar);
        String hour = (String) DateFormat.format(mHourFormat, calendar);
        String minutes = (String) DateFormat.format(mMunitesFormat, calendar);
        String ampm = (String) DateFormat.format(mAmpmFormat, calendar);
        String date = (String) DateFormat.format(getDateFormat(mContext), calendar);
        String dateAm = (String) DateFormat.format(getDateAmFormat(mContext), calendar);
        String weekDay = (String) DateFormat.format(getDateFormatForWeekDay(mContext), calendar);
        String monthDay = (String) DateFormat.format(getDateFormatForMonthDay(mContext), calendar);
        String fullTime = (String) DateFormat.format(mFullFormat, calendar);
        Log.d(TAG, "getTimeInfo  hour = " + hour + " -- minutes =" + minutes + "     -- ampm =" + ampm
                + "  date:" + date + ",fullTime:" + fullTime + ",weekDay:" + weekDay + ",monthDay:" + monthDay);
        TimeInfo timeInfo = new TimeInfo();
        timeInfo.setHour(TextUtils.isEmpty(hour) ? "" : hour.trim());
        timeInfo.setMinutes(TextUtils.isEmpty(minutes) ? "" : minutes.trim());
        timeInfo.setAmpm(TextUtils.isEmpty(ampm) ? "" : ampm.trim());
        timeInfo.setTimeSeparator(mTimerSeparator);
        if (CommonUtil.isZh(mContext)) {
            String chineseDateInfo = getChineseDateInfo(calendar, mContext);
            timeInfo.setDateInfo(chineseDateInfo);
            String amPm = calendar.get(Calendar.AM_PM) == Calendar.AM ? mContext.getString(R.string.am) : mContext.getString(R.string.pm);
            timeInfo.setDateInfoAm(chineseDateInfo + "\u0020\u0020" + amPm);
        } else {
            timeInfo.setDateInfo(TextUtils.isEmpty(date) ? "" : date.trim());
            timeInfo.setDateInfoAm(dateAm);
        }
        timeInfo.setTimeZone(timeZone);
        timeInfo.setFullTime(TextUtils.isEmpty(fullTime) ? "" : fullTime.trim());
        timeInfo.setMonthDay(monthDay);
        timeInfo.setWeekDay(weekDay);
        timeInfo.setYear(year);
        return timeInfo;
    }


    private String getChineseDateInfo(@NonNull Calendar calendar, @NonNull Context context) {
        final String dateFormat = context.getString(R.string.abbrev_chinese_date);
        String date = (String) DateFormat.format(dateFormat, calendar);
        String week = formatCNWeek(context, calendar);
        return date + "\u0020\u0020" + week;
    }

    private static String getDateFormat(Context context) {
        return getDateFormat(context.getString(R.string.abbrev_wday_month_day_no_year));
    }

    private static String getDateAmFormat(Context context) {
        return getDateFormat(context.getString(R.string.abbrev_wday_month_day_no_year_ampm));
    }

    private static String getDateFormatForMonthDay(Context context) {
        return getDateFormat(context.getString(R.string.abbrev_wday_date));
    }

    private static String getDateFormatForWeekDay(Context context) {
        return getDateFormat(context.getString(R.string.abbrev_wday_week));
    }

    private static String getDateFormat(String skeleton) {
        return DateFormat.getBestDateTimePattern(Locale.getDefault(), skeleton);
    }

    @NonNull
    public static String formatCNWeek(@NonNull Context context, @NonNull Calendar calendar) {
        int i = calendar.get(Calendar.DAY_OF_WEEK);
        String[] stringArray = context.getResources().getStringArray(R.array.days_of_week_short);
        switch (i) {
            case Calendar.SUNDAY:
//                return context.getResources().getString(R.string.cn_sunday);
                return stringArray[6];
            case Calendar.MONDAY:
//                return context.getResources().getString(R.string.cn_monday);
                return stringArray[0];
            case Calendar.TUESDAY:
//                return context.getResources().getString(R.string.cn_tuesday);
                return stringArray[1];
            case Calendar.WEDNESDAY:
//                return context.getResources().getString(R.string.cn_wednesday);
                return stringArray[2];
            case Calendar.THURSDAY:
//                return context.getResources().getString(R.string.cn_thursday);
                return stringArray[3];
            case Calendar.FRIDAY:
//                return context.getResources().getString(R.string.cn_friday);
                return stringArray[4];
            case Calendar.SATURDAY:
//                return context.getResources().getString(R.string.cn_saturday);
                return stringArray[5];
            default:
                return "";
        }
    }

    /**
     * format time zone
     * some exameples:
     * input:8.0 output:GMT+8:00
     * input:5.75 output:GMT+5:45
     * input:-5.75 output:GMT-5:45
     * input:-5.5 output:GMT-5:30
     *
     * @param timeZone time from server
     * @return timezon after format for Calendar
     */
    String forMateTimeZone(String timeZone) {
        String zone = TextUtils.isEmpty(timeZone) ? "" : timeZone.trim();
        if (TextUtils.isEmpty(zone)) {
            Log.d(TAG, "zone is null");
            return null;
        }
        float time = Float.valueOf(zone);
        int hour = 0;
        int munites = 0;
        if (time > 0) {
            hour = (int) Math.floor(time);
            munites = (int) ((time - hour) * NUMBER_60);
        } else {
            hour = (int) Math.ceil(time);
            munites = (int) Math.abs((time - hour) * NUMBER_60);
        }
        String symbol = "";
        String mun = "";
        if (munites == 0) {
            mun = "00";
        } else if (munites < NUMBER_10) {
            mun = "0" + munites;
        } else if (munites < NUMBER_60) {
            mun = "" + munites;
        } else {
            mun = "00";
        }
        if ((time > 0) || (time == 0)) {
            symbol = "+";
        }
        String zoneStr = "GMT" + symbol + hour + ":" + mun;
        Log.d(TAG, "forMateTimeZone  hour = " + hour + " -- minutes:" + munites + "     -- mun =" + mun
                + "  symbol:" + symbol + ",fullTime:" + zoneStr + " time:" + time + " zoneStr:" + zoneStr);
        return zoneStr;
    }

    //#ifdef OPLUSOS_EDIT
    //<EMAIL>#2037608, 2019/06/10, Add for type DateWeatherWidget
    public TimeInfo getLocalDateInfo() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        String date = (String) DateFormat.format(getWdayMonthDayFormat(mContext), calendar);

        TimeInfo timeInfo = new TimeInfo();
        if (CommonUtil.isZh(mContext)) {
            timeInfo.setDateInfo(getChineseDateInfo(calendar, mContext));
        } else {
            timeInfo.setDateInfo(TextUtils.isEmpty(date) ? "" : date.trim());
        }

        return timeInfo;
    }

    private static String getWdayMonthDayFormat(Context context) {
        final Locale locale = Locale.getDefault();
        final String skeleton = context.getString(R.string.wday_month_day_no_year);
        return DateFormat.getBestDateTimePattern(locale, skeleton);
    }
    //#endif /* OPLUSOS_EDIT */
}
