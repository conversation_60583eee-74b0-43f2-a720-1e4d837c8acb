/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ClockWidgetState.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/6/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/6/5     1.0            build this module
 ****************************************************************/
package com.coloros.widget.smallweather

import android.content.Context
import android.content.SharedPreferences
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.coloros.widget.commondata.Constants
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.splitscreen.OplusSplitScreenManager

object ClockWidgetState {
    private const val TAG = "ClockWidgetState"

    private const val KEY_ENABLE_DUAL_CLOCK = "key_enable_dual_clock"
    private const val KEY_WALLPAPER_COLOR = "key_wallpaper_color"
    private const val KEY_SPLIT_SCREEN = "key_split_screen"
    private const val KEY_SCREEN_DISPLAY_MODEL = "key_screen_display_model"
    private const val KEY_LOC_WEATHER = "key_loc_weather"
    private const val KEY_RESIDENT_WEATHER = "key_resident_weather"
    private const val INVALID_VALUE = -1

    /**
     * 单双时钟
     */
    var enableDualClock: Int = INVALID_VALUE

    /**
     * 是否分屏
     */
    var isSplitScreen: Int = INVALID_VALUE

    /**
     * 是否更换壁纸
     */
    var wallpaperColor: String? = null

    /**
     * 显示大小
     */
    var screenDisplayModel: Int = FoldScreenUtils.SCREEN_DISPLAY_NORMAL

    private fun getSharedPreferences(): SharedPreferences {
        return Utils.getDeviceContext(AlarmClockApplication.getInstance()).getSharedPreferences(
                Constants.CLOCK_WIDGET_SP,
                Context.MODE_PRIVATE)
    }

    @JvmStatic
    fun saveWidgetState() {
        val editor = getSharedPreferences().edit()
        enableDualClock = systemDualClock()
        if (enableDualClock != INVALID_VALUE) {
            editor.putInt(KEY_ENABLE_DUAL_CLOCK, enableDualClock)
        }
        systemWallpaperColor()?.let { color ->
            if (color.isNotEmpty()) {
                wallpaperColor = color
                editor.putString(KEY_WALLPAPER_COLOR, color)
            }
        }
        isSplitScreen = systemSplitScreenMode()
        if (isSplitScreen != INVALID_VALUE) {
            editor.putInt(KEY_SPLIT_SCREEN, isSplitScreen)
        }
        screenDisplayModel = FoldScreenUtils.screenDisplayModel()
        if (screenDisplayModel != FoldScreenUtils.SCREEN_DISPLAY_NORMAL) {
            editor.putInt(KEY_SCREEN_DISPLAY_MODEL, screenDisplayModel)
        }
        editor.apply()
    }

    @JvmStatic
    fun saveDualClockState() {
        val editor = getSharedPreferences().edit()
        enableDualClock = systemDualClock()
        if (enableDualClock != INVALID_VALUE) {
            editor.putInt(KEY_ENABLE_DUAL_CLOCK, enableDualClock)
            editor.apply()
        }
    }

    @JvmStatic
    fun saveScreenDisplayModelState(): Boolean {
        val editor = getSharedPreferences().edit()
        val displayModel = FoldScreenUtils.screenDisplayModel()
        if (screenDisplayModel != displayModel) {
            screenDisplayModel = displayModel
            editor.putInt(KEY_SCREEN_DISPLAY_MODEL, screenDisplayModel)
            editor.apply()
            return true
        }
        return false
    }

    @JvmStatic
    fun saveWallpaperColor() {
        val editor = getSharedPreferences().edit()
        systemWallpaperColor()?.let { color ->
            if (color.isNotEmpty()) {
                wallpaperColor = color
                editor.putString(KEY_WALLPAPER_COLOR, color)
                editor.apply()
            }
        }
    }

    @JvmStatic
    fun saveSplitScreenMode() {
        val editor = getSharedPreferences().edit()
        isSplitScreen = systemSplitScreenMode()
        if (isSplitScreen != INVALID_VALUE) {
            editor.putInt(KEY_SPLIT_SCREEN, isSplitScreen)
            editor.apply()
        }
    }

    @JvmStatic
    fun readWidgetConfig(context: Context?) {
        context?.let {
            val sp = getSharedPreferences()
            enableDualClock = sp.getInt(KEY_ENABLE_DUAL_CLOCK, INVALID_VALUE)
            isSplitScreen = sp.getInt(KEY_SPLIT_SCREEN, INVALID_VALUE)
            wallpaperColor = sp.getString(KEY_WALLPAPER_COLOR, null)
            screenDisplayModel = sp.getInt(KEY_SCREEN_DISPLAY_MODEL, FoldScreenUtils.SCREEN_DISPLAY_NORMAL)
            Log.d(TAG, "readWidgetConfig, dual clock:$enableDualClock, split screen:$isSplitScreen, color:$wallpaperColor")
        }
    }

    @JvmStatic
    fun hasSystemConfigChanged(): Boolean {
        Log.d(TAG, "system config, dual clock:$enableDualClock, split screen:$isSplitScreen, " +
                "color:$wallpaperColor, screenDisplayModel:$screenDisplayModel")
        if (enableDualClock == -1 || enableDualClock != systemDualClock()) {
            Log.d(TAG, "dual clock changed")
            return true
        }
        if (isSplitScreen == -1 || isSplitScreen != systemSplitScreenMode()) {
            Log.d(TAG, "split screen changed")
            return true
        }
        if (wallpaperColor == null || wallpaperColor != systemWallpaperColor()) {
            Log.d(TAG, "wallpaper color changed")
            return true
        }
        if (screenDisplayModel != FoldScreenUtils.screenDisplayModel()) {
            Log.d(TAG, "screenDisplayModel changed")
            return true
        }
        return false
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    @JvmStatic
    fun systemDualClock(): Int {
        var dualClock = Settings.Secure.getInt(
                AlarmClockApplication.getInstance().contentResolver,
                ClockWidgetManager.ENABLE_DUAL_CLOCK_SWITCH,
                INVALID_VALUE
        )

        if (dualClock == 1) {
            dualClock = Settings.Secure.getInt(
                    AlarmClockApplication.getInstance().contentResolver,
                    ClockWidgetManager.ENABLE_DUAL_CLOCK_CLOCK_WIDGET_SWITCH,
                    INVALID_VALUE
            )
        }
        Log.d(TAG, "systemDualClock:$dualClock")
        return dualClock
    }

    @JvmStatic
    fun systemWallpaperColor(): String? {
        val wallpaperColor = Settings.Secure.getString(
                AlarmClockApplication.getInstance().contentResolver,
                ClockWidgetManager.LAUNCHER_TEXT_COLOR_VALUE
        )
        Log.d(TAG, "systemWallpaperColor:$wallpaperColor")
        return wallpaperColor
    }

    @JvmStatic
    fun systemSplitScreenMode(): Int {
        kotlin.runCatching {
            val splitScreenMode = OplusSplitScreenManager.getInstance().isInSplitScreenMode
            Log.d(TAG, "systemSplitScreenMode:$splitScreenMode")
            return if (splitScreenMode) 1 else 0
        }
        return INVALID_VALUE
    }
}