/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ContinueUtils.kt
 * * Description: common util
 * * Version:1.0
 * * Date :2022/10/11
 * * Author:NieXiaokang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.utils.DragonflyUtils.ScreenListener

@Suppress("UtilityClassWithPublicConstructor")
open class ContinueUtils {
    companion object {
        @JvmStatic
        fun registerListener(
            context: Context,
            utils: DragonflyUtils?,
            listener: ScreenListener?
        ): DragonflyUtils? {
            return if (FoldScreenUtils.isDragonfly()) {
                utils ?: DragonflyUtils().apply {
                    register(context, listener)
                }
            } else null
        }

        @JvmStatic
        fun unregisterListener(context: Context, utils: DragonflyUtils?) {
            utils?.run {
                unregister(context)
            }
        }
    }
}