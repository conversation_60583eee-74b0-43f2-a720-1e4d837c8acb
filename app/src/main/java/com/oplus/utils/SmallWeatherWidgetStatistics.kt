/**************************************************************
 * * Copyright (C), 2017, OPLUS Mobile Comm Corp., Ltd
 * * VENDOR_EDIT
 * * File: - SmallWeatherWidgetStatistics.kt
 * * Description: Manager the drawable resource for the weather icon.
 * * Version: 1.0
 * * Date : 2018/06/17
 * * Author: NieXiaokang
 * *
 * * ------------------- Revision  History: -------------------
 * *   <author>       <data>       <version >      <desc>
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import com.oplus.clock.common.utils.Log
import com.oplus.statistics.OplusTrack
import java.lang.ref.WeakReference

object SmallWeatherWidgetStatistics {
    private const val TAG = "SmallWeatherWidgetStatistics"
    private const val DEBUG = false
    private const val STATIC_STATISTICS_TAG = "200751"
    private const val DYNAMIC_STATISTICS_TAG = "200752"
    private const val EVENT_CLICK_TIME_INFO = "event_click_time_info"
    private const val EVENT_CLICK_DATE_INFO = "event_click_date_info"
    private const val EVENT_CLICK_WEATHER_INFO = "event_click_weather_info"
    private const val EVENT_CLICK_REFRESH_IMG_BTN = "event_click_refresh_img_btn"
    private var mContext: WeakReference<Context>? = null

    @JvmStatic
    fun setContext(context: Context?) {
        if ((mContext == null) && (context != null)) {
            mContext = WeakReference(context.applicationContext)
        }
    }

    /**
     * Stats event with info map for internal use.
     */
    @JvmStatic
    private fun onEvent(event: String, info: Map<String, String>, uploadNow: Boolean, dynamic: Boolean) {
        if (DEBUG) {
            Log.d(TAG, "onEvent. event = $event , info = $info , uploadNow = $uploadNow")
        }
        val context = mContext?.get()
        if (context == null) {
            Log.w(TAG, "onEvent. The mContext is null!")
            return
        }
        OplusTrack.onCommon(context, if (dynamic) DYNAMIC_STATISTICS_TAG else STATIC_STATISTICS_TAG, event, info)
    }

    @JvmStatic
    fun statsClickTimeInfo() {
        val infoMap = HashMap<String, String>()
        onEvent(EVENT_CLICK_TIME_INFO, infoMap, false, true)
    }

    @JvmStatic
    fun statsClickDateInfo() {
        val infoMap = HashMap<String, String>()
        onEvent(EVENT_CLICK_DATE_INFO, infoMap, false, true)
    }

    @JvmStatic
    fun statsClickWeatherInfo() {
        val infoMap = HashMap<String, String>()
        onEvent(EVENT_CLICK_WEATHER_INFO, infoMap, false, true)
    }

    @JvmStatic
    fun statsClickRefreshImgBtn() {
        val infoMap = HashMap<String, String>()
        onEvent(EVENT_CLICK_REFRESH_IMG_BTN, infoMap, false, true)
    }
}