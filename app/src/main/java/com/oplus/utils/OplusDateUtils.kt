/**************************************************************
 * * Copyright (C), 2017, OPLUS Mobile Comm Corp., Ltd
 * * VENDOR_EDIT
 * * File: - OplusDateUtils.kt
 * * Description: Manager the drawable resource for the weather icon.
 * * Version: 1.0
 * * Date : 2018/06/17
 * * Author: NieXiaokang
 * *
 * * ------------------- Revision  History: -------------------
 * *   <author>       <data>       <version >      <desc>
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import com.oplus.clock.common.utils.Log
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.GregorianCalendar
import java.util.TimeZone

object OplusDateUtils {
    const val TAG = "OplusDateUtils"
    private const val NEIGHT_BEGIN = 18
    private const val NEIGHT_EDN = 6

    /**
     * @Description<br></br> format time by given time display format
     * @param million time in milliseconds
     * @param pattern time format
     * @return
     */
    @JvmStatic
    fun getDateStringWithFormat(million: Long, pattern: String?): String {
        val calendar = GregorianCalendar()
        calendar.timeInMillis = million
        val simpleDateFormat = SimpleDateFormat(pattern)
        return simpleDateFormat.format(calendar.time)
    }

    /**
     * @Description<br></br> obtain hour part of time
     * @param context
     * @param dateMillion
     * @return
     */
    @JvmStatic
    fun getHourString(context: Context?, dateMillion: Long): String {
        val format = "HH"
        return getDateStringWithFormat(dateMillion, format)
    }

    /**
     * @Description<br></br> day or night by current time, divided from 6 o'clock
     * @return
     */
    @JvmStatic
    fun isNowDayTime(context: Context?): Boolean {
        val now = System.currentTimeMillis()
        return isTimeMillisDayTime(context, now)
    }

    @JvmStatic
    fun isResidentNowDayTime(timezoneId: String?): Boolean {
        return timezoneId?.let {
            val calendar = Calendar.getInstance(TimeZone.getTimeZone(it))
            val hourTime = calendar.get(Calendar.HOUR_OF_DAY)
            !((hourTime >= NEIGHT_BEGIN) || (hourTime < NEIGHT_EDN))
        } ?: true
    }

    /**
     * @Description<br></br> day or not by given time
     * @param timeMillion
     * @return
     */
    @JvmStatic
    fun isTimeMillisDayTime(context: Context?, timeMillion: Long): Boolean {
        var isDay = true
        val hour = getHourString(context, timeMillion)
        Log.d(TAG, "isTimeMillisDayTime -- hour = $hour")
        val hourTime = hour.toInt()
        if ((hourTime >= NEIGHT_BEGIN) || (hourTime < NEIGHT_EDN)) {
            isDay = false
        }
        return isDay
    }
}
