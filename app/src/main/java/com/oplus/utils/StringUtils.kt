/************************************************************
 * Copyright 2000-2016 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 *
 * FileName       : StringUtils.java
 * Version Number : 1.0
 * Description    : String util
 * Author         : clock
 * Date           : 2022-01-28
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2022-01-28, clock, create
 */
package com.oplus.utils

object StringUtils {
    val CHARS = charArrayOf(
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',  // 0-9
        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',  // 10-19
        'u', 'v', 'w', 'x', 'y', 'z',  // 20-25
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',  // 26-35
        'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',  // 36-45
        'U', 'V', 'W', 'X', 'Y', 'Z'
    ) // 46-51
    val oplus = String(
        charArrayOf(
            CHARS[14],
            CHARS[15],
            CHARS[15],
            CHARS[14]
        )
    )
    val oplusos = String(
        charArrayOf(
            CHARS[2],
            CHARS[14],
            CHARS[11],
            CHARS[14],
            CHARS[17],
            CHARS[14],
            CHARS[18]
        )
    )
    val wplus = String(
        charArrayOf(
            CHARS[14],
            CHARS[13],
            CHARS[4],
            CHARS[15],
            CHARS[11],
            CHARS[20],
            CHARS[18]
        )
    )
    private val OPLUS = String(
        charArrayOf(
            CHARS[40],
            CHARS[15],
            CHARS[15],
            CHARS[14]
        )
    )
    val CLOCK_DB = OPLUS + "Alarms.db"
    val CLOCK_W_DB = wplus + "_alarms.db"
    val SELECT_RINGTONE_OLD = oplus + "_select_ringtone_old"
    val SELECT_RINGTONE_NEW = oplus + "_select_ringtone_new"
    val SCREEN_RESOLUTION_ADJUST = oplusos + "_screen_resolution_adjust"
    val W_PREFERENCES = "com." + wplus + ".deskclock_preferences"
}