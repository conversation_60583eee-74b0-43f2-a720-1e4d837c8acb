/**************************************************************
 * * Copyright (C), 2017, OPLUS Mobile Comm Corp., Ltd
 * * VENDOR_EDIT
 * * File: - CommonUtil.kt
 * * Description: Manager the drawable resource for the weather icon.
 * * Version: 1.0
 * * Date : 2018/06/17
 * * Author: NieXiaokang
 * *
 * * ------------------- Revision  History: -------------------
 * *   <author>       <data>       <version >      <desc>
 ****************************************************************/
package com.oplus.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.Paint
import android.graphics.Point
import android.hardware.display.DisplayManager
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.util.Pair
import android.view.Display
import android.view.View
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.coloros.widget.entity.TimeInfo
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.ClockWidgetUtils
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.utils.FoldScreenUtils.isScreenRealUnfold
import com.oplus.alarmclock.utils.WindowUtil.getWindowSize
import com.oplus.clock.common.utils.DisplayUtils
import com.oplus.clock.common.utils.Log
import com.oplus.compat.os.UserHandleNative
import com.oplus.compat.provider.SettingsNative
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.splitscreen.OplusSplitScreenManager
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.Locale


object CommonUtil {
    private const val TAG = "CommonUtil"
    private const val SHADOW_COLOR_BLACK = 0x33000000 //0x55eeeeee;
    private const val SHADOW_COLOR_WHITE = -0x33000001 //0x3d000000;//0x55000000;//0x3d3d3d3d;
    private const val WHITE_TEXT_SHADOW_RADIUS = 6.0f
    private const val WHITE_TEXT_SHADOW_DX = 0f
    private const val WHITE_TEXT_SHADOW_DY = 0f
    private const val VERSION_CODES_M = 23
    private const val OPLUS_RESOLUTION_SWITCH_OLD = "oppo.resolutionswitch.feature.support"
    private const val OPLUS_RESOLUTION_SWITCH = "oplus.resolutionswitch.feature.support"
    private const val RESOLUTION_SWITCH_VALUE = "coloros_screen_resolution_adjust"
    private const val RESOLUTION_SWITCH_VALUE_S = "oplus_customize_screen_resolution_adjust"
    private const val RESOLUTION_DEFAULT_DISPLAY = 480
    private const val TWO_K_DEFAULT_DISPLAY = 640
    private const val SCREEN_TWO_K_WIDTH = 1440
    private const val RESOLUTION_FHD_VALUE = 2
    private const val RESOLUTION_QHD_VALUE = 3
    private const val RTL_START = "\u202B"
    private const val RTL_END = "\u202C"
    private const val MIN_RATIO = 0.8F
    private var sDisplayManager: DisplayManager? = null

    @JvmStatic
    fun isZh(context: Context): Boolean {
        val locale = context.resources.configuration.locale
        val language = locale.language
        return language.endsWith("zh")
    }

    /**
     * 日语和越南语，插件小时位不补0
     */
    @JvmStatic
    fun isAddZero(context: Context?): Boolean {
        return context?.let {
            val locales = it.resources.configuration.locales
            if (locales.isEmpty) {
                return true
            }
            val language = locales.get(0).language
            Log.d(TAG, "isAddZero language:$language")
            !(language.endsWith("ja") || language.endsWith("vi"))
        } ?: true
    }

    @JvmStatic
    fun getDefaultDisplayDensity(): Int {
        return DisplayUtils.getDefaultDensity()
    }

    @JvmStatic
    @SuppressLint("NewApi")
    fun setDefaultDisplay(context: Context?) {
        if (context == null) {
            return
        }
        if (Build.VERSION.SDK_INT > VERSION_CODES_M) {
            kotlin.runCatching {
                val resources = context.resources
                val origConfig = resources.configuration
                val size = getWindowSize(context)
                val defaultDpi = getDefaultDisplayDensity()
                Log.d(TAG, "setDefaultDisplay , origConfig: " + origConfig.densityDpi + ", default: " + defaultDpi)
                origConfig.densityDpi = defaultDpi
                Log.d(TAG, "setDefaultDisplay , defaultConfig: " + origConfig.densityDpi)
                if (origConfig.orientation == Configuration.ORIENTATION_PORTRAIT && size.width == SCREEN_TWO_K_WIDTH) {
                    Log.d(TAG, "setDefaultDisplay , screenWidth: 2k.")
                    origConfig.densityDpi = TWO_K_DEFAULT_DISPLAY
                } else if (context.packageManager.hasSystemFeature(OPLUS_RESOLUTION_SWITCH)
                    || context.packageManager.hasSystemFeature(OPLUS_RESOLUTION_SWITCH_OLD)
                ) {
                    var screenDensityId = Settings.Secure.getInt(context.contentResolver, RESOLUTION_SWITCH_VALUE, 0)
                    if (VersionUtils.isS()) {
                        screenDensityId = Settings.Secure.getInt(context.contentResolver, RESOLUTION_SWITCH_VALUE_S, 0)
                        Log.d(TAG, "is S model screenDensityId==$screenDensityId")
                        origConfig.densityDpi = RESOLUTION_DEFAULT_DISPLAY
                    } else if (VersionUtils.isR()) {
                        screenDensityId = SettingsNative.Secure.getIntForUser(
                            RESOLUTION_SWITCH_VALUE, 0, UserHandleNative.USER_SYSTEM
                        )
                        Log.d(TAG, "is R model")
                    }
                    Log.d(TAG, "setDefaultDisplay, screenDensityId: $screenDensityId")
                    if (screenDensityId == RESOLUTION_FHD_VALUE) {
                        origConfig.densityDpi = RESOLUTION_DEFAULT_DISPLAY
                    } else if (screenDensityId == RESOLUTION_QHD_VALUE) {
                        origConfig.densityDpi = TWO_K_DEFAULT_DISPLAY
                    } else {
                        /**
                         * other screenDensityId=1 横屏情况下 高度等于1440表示当前是2k
                         */
                        if (!isScreenPortrait() && size.height == SCREEN_TWO_K_WIDTH) {
                            origConfig.densityDpi = TWO_K_DEFAULT_DISPLAY
                        }
                    }
                }
                resources.updateConfiguration(
                    origConfig, resources.displayMetrics
                )
            }.onFailure {
                Log.e(TAG, "Exception: $it")
            }
        }
    }

    @JvmStatic
    @RequiresApi(api = Build.VERSION_CODES.Q)
    fun updateTextShadow(isShadowEnable: Boolean, textView: TextView, isTextWhite: Boolean) {
        val paint: Paint = textView.paint
        if (isShadowEnable) {
            var shadowColor = -1
            shadowColor = if (isTextWhite) {
                SHADOW_COLOR_BLACK
            } else {
                SHADOW_COLOR_WHITE
            }
            Log.d(TAG, "updateTextShadow setShadowLayer")
            paint.setShadowLayer(
                WHITE_TEXT_SHADOW_RADIUS, WHITE_TEXT_SHADOW_DX, WHITE_TEXT_SHADOW_DY, shadowColor
            )
        } else if (hasShadowLayer(paint)) {
            Log.d(TAG, "updateTextShadow clearShadowLayer")
            paint.clearShadowLayer()
        }
        textView.invalidate()
    }

    @JvmStatic
    fun getFormatHour(timeInfo: TimeInfo): Pair<Int, Int> {
        val hour = timeInfo.hour
        var hourTenDigits = 0
        var hourSingleDigits = 0
        if (hour.length == 0) {
            hourTenDigits = 0
            hourSingleDigits = 0
        } else if (hour.length == 1) {
            hourTenDigits = 0
            hourSingleDigits = ("" + hour[0]).toInt()
        } else if (hour.length == 2) {
            hourTenDigits = ("" + hour[0]).toInt()
            hourSingleDigits = ("" + hour[1]).toInt()
        }
        return Pair(hourTenDigits, hourSingleDigits)
    }

    @JvmStatic
    fun isRtl(context: Context?): Boolean {
        return context?.let {
            it.resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_RTL
        } ?: false
    }

    @JvmStatic
    fun adapterRtlWeatherTxt(context: Context?, centigrade: String?, degree: String?): String {
        val builder = StringBuilder()
        val centigradeStr = centigrade ?: ""
        val degreeStr = degree ?: ""
        return if (isRtl(context)) {
            builder.append(centigradeStr)
                .append(ClockWidgetManager.WEATHER_SPACE)
                .append(degreeStr)
                .toString()
        } else {
            builder.append(degreeStr)
                .append(ClockWidgetManager.WEATHER_SPACE)
                .append(centigradeStr)
                .toString()
        }
    }

    @JvmStatic
    fun adapterRtlText(context: Context?, text: String): String {
        var result = text
        return context?.let {
            if (isRtl(it)) {
                result = addDirectionSymbolForRtl(text)
            }
            result
        } ?: result
    }

    @JvmStatic
    private fun addDirectionSymbolForRtl(text: String): String {
        return RTL_START + text + RTL_END
    }

    @JvmStatic
    fun convertNumberToLocal(str: String?): String {
        if (TextUtils.isEmpty(str)) {
            return ""
        }
        var result = str ?: ""
        kotlin.runCatching {
            val degree = str?.toInt()
            degree?.let {
                result = NumberFormat.getNumberInstance(Locale.getDefault()).format(it.toLong())
            }
        }.onFailure {
            Log.e(TAG, "convertNumberToLocal e=$it")
        }
        return result
    }

    @JvmStatic
    fun isLocationEnabled(context: Context): Boolean {
        var result = false
        kotlin.runCatching {
            val locationMode = Settings.Secure.getInt(context.contentResolver, Settings.Secure.LOCATION_MODE)
            result = locationMode != Settings.Secure.LOCATION_MODE_OFF
        }
        return result
    }

    @JvmStatic
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private fun hasShadowLayer(paint: Paint?): Boolean {
        return (paint != null) && ((paint.shadowLayerRadius != 0f) || (paint.shadowLayerDx != 0f)
                || (paint.shadowLayerDy != 0f) || (paint.shadowLayerColor != 0))
    }

    @JvmStatic
    fun isScreenPortrait(): Boolean {
        if (sDisplayManager == null) {
            sDisplayManager =
                AlarmClockApplication.getInstance().getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        }
        var isPortrait = true
        val realDisplaySize = Point()
        val display = sDisplayManager!!.getDisplay(Display.DEFAULT_DISPLAY)
        if (display != null) {
            display.getRealSize(realDisplaySize)
            if (realDisplaySize.x > realDisplaySize.y) {
                isPortrait = false
            }
        } else {
            val orientation = AlarmClockApplication.getInstance().resources.configuration.orientation
            if (Configuration.ORIENTATION_LANDSCAPE == orientation) {
                isPortrait = false
            }
        }
        Log.d(TAG, "isScreenPortrait=" + isPortrait + " x=" + realDisplaySize.x + " y=" + realDisplaySize.y)
        return isPortrait
    }

    /**
     * 不处于分屏模式下，则时钟插件则都保持竖屏布局显示；在分屏模式下才会根据屏幕方向去加载不同布局
     * 不支持横屏布局显示竖屏布局
     */
    @JvmStatic
    fun isPortraitLayoutDirection(): Boolean {
        if (!ClockWidgetUtils.supportLandWidget()) {
            return true
        }
        kotlin.runCatching {
            if (!OplusSplitScreenManager.getInstance().isInSplitScreenMode) {
                return true
            }
        }.onFailure {
            return isScreenPortrait()
        }

        if (isScreenRealUnfold()) {
            Log.d(TAG, "peacock unfold, use portrait layout")
            return true
        }
        return isScreenPortrait()
    }

    @JvmStatic
    @SuppressLint("WrongConstant")
    fun getDisplayDpiRatio(context: Context?): Float {
        val defaultFontScale = 1f
        if (context == null) {
            return defaultFontScale
        }
        var screenDensityId = Settings.Secure.getInt(context.contentResolver, RESOLUTION_SWITCH_VALUE, 0)
        if (VersionUtils.isS()) {
            screenDensityId = Settings.Secure.getInt(context.contentResolver, RESOLUTION_SWITCH_VALUE_S, 0)
            Log.d(TAG, "is S model   screenDensityId==$screenDensityId")
        } else if (VersionUtils.isR()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                screenDensityId =
                    SettingsNative.Secure.getIntForUser(RESOLUTION_SWITCH_VALUE, 0, UserHandleNative.USER_SYSTEM)
            }
            Log.d(TAG, "is R model")
        }
        Log.d(TAG, "getDisplayDpiRatio, screenDensityId: $screenDensityId")
        var result = defaultFontScale
        kotlin.runCatching {
            val curConfig = context.resources.configuration
            val curDpi = curConfig.densityDpi
            var defaultDpi = getDefaultDisplayDensity()
            if (screenDensityId == RESOLUTION_QHD_VALUE) {
                defaultDpi = TWO_K_DEFAULT_DISPLAY
            }
            Log.d(TAG, "curDpi: " + curDpi + " defDpi: " + defaultDpi + " fontScale: " + curConfig.fontScale)
            val curFontScale = curConfig.fontScale
            /**
             * 如果字体、显示大小变化
             */
            if (curFontScale > defaultFontScale || curDpi > defaultDpi) {
                val curFontDec = BigDecimal(curFontScale.toDouble())
                val defFontDec = BigDecimal(defaultFontScale.toDouble())
                val fontRatio = defFontDec.divide(curFontDec, 1, BigDecimal.ROUND_HALF_UP).toFloat()
                val curDec = BigDecimal(curDpi)
                val defDec = BigDecimal(defaultDpi)
                val dpiRatio = defDec.divide(curDec, 1, BigDecimal.ROUND_HALF_UP).toFloat()
                val minRatio = MIN_RATIO
                result = if (fontRatio < dpiRatio) {
                    fontRatio.coerceAtLeast(minRatio)
                } else {
                    dpiRatio.coerceAtLeast(minRatio)
                }
            }
        }.onFailure {
            Log.e(TAG, "getDisplayDpiRatio fail!")
        }
        return result
    }
}