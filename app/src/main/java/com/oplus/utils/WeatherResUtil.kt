/**************************************************************
 * * Copyright (C), 2017, OPLUS Mobile Comm Corp., Ltd
 * * VENDOR_EDIT
 * * File: - WeatherResUtil.kt
 * * Description: Manager the drawable resource for the weather icon.
 * * Version: 1.0
 * * Date : 2018/06/17
 * * Author: NieXiaokang
 * *
 * * ------------------- Revision  History: -------------------
 * *   <author>       <data>       <version >      <desc>
 ****************************************************************/
package com.oplus.utils

import android.text.TextUtils
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log

object WeatherResUtil {
    private const val TAG = "WeatherResUtil"
    private const val FOUR_TWO = 42
    private const val FOUR_THREE = 43
    private const val FIVE_FOUR = 54
    private const val FIVE_FIVE = 55
    private const val FIVE_SIX = 56
    private const val FIVE_SEVEN = 57
    private const val FIVE_EIGHT = 58
    private const val SIX_EIGHT = 68
    private const val SIX_NINE = 69
    private val SPECIAL_WEATHER_TYPE_INDEX = intArrayOf(1, 2)
    private val NIGHT_RES_INDEX = intArrayOf(FOUR_TWO, FOUR_THREE)
    private val WEATHER_ICON = intArrayOf(
        R.drawable.ic_hour_sunny_day,  /* 晴天 */
        R.drawable.ic_hour_cloudy_day,  /* 多云 */
        R.drawable.ic_hour_overcast,  /* 阴天 */ //#ifndef OPLUSOS_EDIT
        //<EMAIL>#2131209, 2019/07/02, Modify for keep same with OplusWeather
        // R.drawable.ic_hour_rain_heavy, /* 阵雨 */
        //#else /* OPLUSOS_EDIT */
        R.drawable.ic_hour_rain_showers,  /* 阵雨 */ //#endif /* OPLUSOS_EDIT */
        R.drawable.ic_hour_thunderstorm,  /* 雷阵雨 */
        R.drawable.ic_hour_thundershower_hail,  /* 雷阵雨伴有冰雹 */
        R.drawable.ic_hour_sleet,  /* 雨夹雪 */
        R.drawable.ic_hour_rain_showers,  /* 小雨 */
        R.drawable.ic_hour_rain_heavy,  /* 中雨 */
        R.drawable.ic_hour_rain_storm,  /* 大雨 */
        R.drawable.ic_hour_rain_storm,  /* 暴雨 */
        R.drawable.ic_hour_rain_storm,  /* 大暴雨 */
        R.drawable.ic_hour_rain_storm,  /* 特大暴雨 */
        R.drawable.ic_hour_snow_light,  /* 阵雪 */
        R.drawable.ic_hour_snow_light,  /* 小雪 */
        R.drawable.ic_hour_snow_heavy,  /* 中雪 */
        R.drawable.ic_hour_snow_storm,  /* 大雪 */
        R.drawable.ic_hour_snow_storm,  /* 暴雪 */
        R.drawable.ic_hour_fog,  /* 雾 */ //#ifndef OPLUSOS_EDIT
        //<EMAIL>#2131209, 2019/07/02, Modify for keep same with OplusWeather
        //R.drawable.ic_hour_rain_light, /* 冻雨 */
        //#else /* OPLUSOS_EDIT */
        R.drawable.ic_hour_freez_rain,  /* 冻雨 */ //#endif /* OPLUSOS_EDIT */
        R.drawable.ic_hour_sand_dust,  /* 浮尘 */
        R.drawable.ic_hour_sand_dust,  /* 扬尘 */
        R.drawable.ic_hour_sand_storm,  /* 沙层暴 */
        R.drawable.ic_hour_sand_storm,  /* 强沙层暴 */
        R.drawable.ic_hour_rain_showers,  /* 小雨-中雨 */
        R.drawable.ic_hour_rain_heavy,  /* 中雨-大雨 */
        R.drawable.ic_hour_rain_storm,  /* 大雨-暴雨 */
        R.drawable.ic_hour_rain_storm,  /* 暴雨-特大暴雨 */
        R.drawable.ic_hour_rain_storm,  /* 大暴雨-特大暴雨 */
        R.drawable.ic_hour_snow_light,  /* 小雪-中雪 */
        R.drawable.ic_hour_snow_heavy,  /* 中雪-大雪 */
        R.drawable.ic_hour_snow_storm,  /* 大雪-暴雪 */
        R.drawable.ic_hour_fog,  /* 浓雾 */
        R.drawable.ic_hour_snow_light,  /* 雪 */
        R.drawable.ic_hour_fog,  /* 强浓雾 */ //#ifndef OPLUSOS_EDIT
        //<EMAIL>#2131209, 2019/07/02, Modify for keep same with OplusWeather
        //R.drawable.ic_hour_fog, /* 霾 */
        //R.drawable.ic_hour_fog, /* 中度霾 */
        //R.drawable.ic_hour_fog, /* 重度霾 */
        //R.drawable.ic_hour_fog, /* 严重霾 */
        //#else /* OPLUSOS_EDIT */
        R.drawable.ic_hour_haze,  /* 霾 */
        R.drawable.ic_hour_haze,  /* 中度霾 */
        R.drawable.ic_hour_haze,  /* 重度霾 */
        R.drawable.ic_hour_haze,  /* 严重霾 */ //#endif /* OPLUSOS_EDIT */
        R.drawable.ic_hour_fog,  /* 大雾 */
        R.drawable.ic_hour_fog,  /* 特强浓雾 */
        R.drawable.ic_hour_sunny_night, R.drawable.ic_hour_cloudy_night, R.drawable.ic_sunrise, R.drawable.ic_sunset
    )
    private val WEATHER_ICON_LENGTH = WEATHER_ICON.size
    private val OPLUS_SPECIAL_WEATHER_TYPE_INDEX = intArrayOf(FIVE_FOUR, FIVE_FIVE, FIVE_SIX, FIVE_SEVEN, FIVE_EIGHT)
    private val OPLUS_NIGHT_RES_INDEX = intArrayOf(SIX_EIGHT, SIX_EIGHT, SIX_NINE, SIX_NINE, SIX_NINE)
    private val OPLUS_WEATHER_ICON = intArrayOf(
        R.drawable.ic_hour_rain_heavy,  /*雨*/
        R.drawable.ic_hour_rain_showers,  /*阵雨*/
        R.drawable.ic_hour_rain_showers,  /*局部阵雨*/
        R.drawable.ic_hour_rain_showers,  /*小阵雨*/
        R.drawable.ic_hour_rain_storm,  /*强阵雨*/
        R.drawable.ic_hour_rain_showers,  /*小雨*/
        R.drawable.ic_hour_rain_showers,  /*小到中雨*/
        R.drawable.ic_hour_rain_heavy,  /*中雨*/
        R.drawable.ic_hour_rain_heavy,  /*中到大雨*/
        R.drawable.ic_hour_rain_storm,  /*大雨*/
        R.drawable.ic_hour_rain_storm,  /*大到暴雨*/
        R.drawable.ic_hour_rain_storm,  /*暴雨*/
        R.drawable.ic_hour_rain_storm,  /*暴雨到大暴雨*/
        R.drawable.ic_hour_rain_storm,  /*大暴雨*/
        R.drawable.ic_hour_rain_storm,  /*大暴雨到特大暴雨*/
        R.drawable.ic_hour_rain_storm,  /*特大暴雨*/
        R.drawable.ic_hour_freez_rain,  /*冻雨*/
        R.drawable.ic_hour_hail,  /*冰雹*/
        R.drawable.ic_hour_hail,  /*冰针*/
        R.drawable.ic_hour_hail,  /*冰粒*/
        R.drawable.ic_hour_snow_heavy,  /*结冰*/
        R.drawable.ic_hour_thunderstorm,  /*雷阵雨xxxx*/
        R.drawable.ic_hour_thundershower_hail,  /*雷阵雨伴有冰雹*/
        R.drawable.ic_hour_thunderstorm,  /*雷电*/
        R.drawable.ic_hour_thunderstorm,  /*雷暴*/
        R.drawable.ic_hour_snow_heavy,  /*雪*/
        R.drawable.ic_hour_snow_light,  /*阵雪*/
        R.drawable.ic_hour_snow_light,  /*小阵雪*/
        R.drawable.ic_hour_snow_light,  /*小雪*/
        R.drawable.ic_hour_snow_light,  /*小到中雪*/
        R.drawable.ic_hour_snow_heavy,  /*中雪*/
        R.drawable.ic_hour_snow_heavy,  /*中到大雪*/
        R.drawable.ic_hour_snow_storm,  /*大雪*/
        R.drawable.ic_hour_snow_storm,  /*大到暴雪*/
        R.drawable.ic_hour_snow_storm,  /*暴雪*/
        R.drawable.ic_hour_snow_storm,  /*暴风雪*/
        R.drawable.ic_hour_sleet,  /*雨夹雪*/
        R.drawable.ic_hour_fog,  /*雾*/
        R.drawable.ic_hour_fog,  /*冻雾*/
        R.drawable.ic_hour_fog,  /*薄雾*/
        R.drawable.ic_hour_fog,  /*大雾*/
        R.drawable.ic_hour_fog,  /*浓雾*/
        R.drawable.ic_hour_fog,  /*强浓雾*/
        R.drawable.ic_hour_fog,  /*特强浓雾*/
        R.drawable.ic_hour_sand_dust,  /*浮尘*/
        R.drawable.ic_hour_sand_storm,  /*扬沙*/
        R.drawable.ic_hour_sand_storm,  /*尘卷风*/
        R.drawable.ic_hour_sand_storm,  /*沙尘暴*/
        R.drawable.ic_hour_sand_storm,  /*强沙尘暴*/
        R.drawable.ic_hour_haze,  /*霾*/
        R.drawable.ic_hour_haze,  /*中度霾*/
        R.drawable.ic_hour_haze,  /*重度霾*/
        R.drawable.ic_hour_haze,  /*严重霾*/
        R.drawable.ic_hour_sunny_day,  /*晴*/
        R.drawable.ic_hour_sunny_day,  /*大部晴朗*/
        R.drawable.ic_hour_cloudy_day,  /*多云*/
        R.drawable.ic_hour_cloudy_day,  /*多云*/
        R.drawable.ic_hour_cloudy_day,  /*少云*/
        R.drawable.ic_hour_overcast,  /*阴*/
        R.drawable.ic_hour_wind,  /*风*/
        R.drawable.ic_hour_wind,  /*微风*/
        R.drawable.ic_hour_wind,  /*强风*/
        R.drawable.ic_hour_typhoon,  /*暴风*/
        R.drawable.ic_hour_typhoon,  /*飓风*/
        R.drawable.ic_hour_typhoon,  /*强风暴*/
        R.drawable.ic_hour_tornado,  /*龙卷风*/
        R.drawable.ic_hour_typhoon,  /*热带风暴*/
        R.drawable.ic_hour_sunny_night,  /*夜晴*/
        R.drawable.ic_hour_cloudy_night,  /*夜多云*/
        R.drawable.ic_sunrise, R.drawable.ic_sunset
    )

    private val OPLUS_WEATHER_ICON_LENGTH = OPLUS_WEATHER_ICON.size
    private var sCurrentWeatherIconIndex = -1

    @JvmStatic
    fun getWeatherIcon(type: String?, isDayTime: Boolean): Int {
        if (TextUtils.isEmpty(type)) {
            return R.drawable.ic_hour_cloudy_day
        }
        var index = R.drawable.ic_hour_cloudy_day
        kotlin.runCatching {
            index = type?.toInt() ?: R.drawable.ic_hour_cloudy_day
        }
        val correctWeatherIndex = getSpecialWeatherIconIndex(index, isDayTime)
        Log.d(
            TAG,
            "getWeatherIcon -- index:$index WEATHER_ICON_LENGTH:$WEATHER_ICON_LENGTH correctWeatherIndex:$correctWeatherIndex"
        )
        if (correctWeatherIndex < 1 || correctWeatherIndex > WEATHER_ICON_LENGTH) {
            Log.d(TAG, "getWeatherIconResIdByColorState -- sCurrentWeatherIconIndex = $sCurrentWeatherIconIndex")
            return R.drawable.ic_hour_cloudy_day
        }
        sCurrentWeatherIconIndex = correctWeatherIndex
        return WEATHER_ICON[correctWeatherIndex - 1]
    }

    @JvmStatic
    fun getOplusWeatherIcon(type: String?, isDayTime: Boolean): Int {
        val resId: Int = R.drawable.ic_hour_cloudy_day
        if (TextUtils.isEmpty(type)) {
            return resId
        }
        var index = resId
        kotlin.runCatching {
            index = type?.toInt() ?: resId
        }
        val correctWeatherIndex = geOplusSpecialWeatherIconIndex(index, isDayTime)
        Log.d(
            TAG,
            "getOpWeatherIcon -- index:$index OP_WEATHER_ICON_LENGTH:$OPLUS_WEATHER_ICON_LENGTH correctWeatherIndex:$correctWeatherIndex"
        )
        return if (correctWeatherIndex < 1 || correctWeatherIndex > OPLUS_WEATHER_ICON_LENGTH) {
            resId
        } else OPLUS_WEATHER_ICON[correctWeatherIndex - 1]
    }

    /**
     * For sunny and cloudy, the weather icon for day and night is different.
     */
    @JvmStatic
    private fun geOplusSpecialWeatherIconIndex(weatherType: Int, isDayTime: Boolean): Int {
        var resultNO = weatherType
        val size = OPLUS_SPECIAL_WEATHER_TYPE_INDEX.size
        for (i in 0 until size) {
            if (OPLUS_SPECIAL_WEATHER_TYPE_INDEX[i] == weatherType && !isDayTime) {
                resultNO = OPLUS_NIGHT_RES_INDEX[i]
                break
            }
        }
        return resultNO
    }

    /**
     * For sunny and cloudy, the weather icon for day and night is different.
     * @return
     */
    @JvmStatic
    private fun getSpecialWeatherIconIndex(weatherType: Int, isDayTime: Boolean): Int {
        var resultNO = weatherType
        val size = SPECIAL_WEATHER_TYPE_INDEX.size
        for (i in 0 until size) {
            if (SPECIAL_WEATHER_TYPE_INDEX[i] == weatherType && !isDayTime) {
                resultNO = NIGHT_RES_INDEX[i]
                break
            }
        }
        return resultNO
    }
}