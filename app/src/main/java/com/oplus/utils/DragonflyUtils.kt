/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: DragonflyUtils.kt
 * * Description: common util
 * * Version:1.0
 * * Date :2022/10/11
 * * Author:NieXiaokang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import android.database.ContentObserver
import android.provider.Settings

class DragonflyUtils {
    companion object {
        private const val SYSTEM_FOLDING_MODE_KEYS = "oplus_system_folding_mode"
        private const val SYSTEM_FOLDING_MODE_OPEN = 1//大屏
        private const val SYSTEM_FOLDING_MODE_CLOSE = 0//小屏
    }

    private var mListener: ScreenListener? = null
    private var mObserver: ContentObserver? = null

    fun register(context: Context, listener: ScreenListener?) {
        mListener = listener
        val foldingAngle = Settings.Global.getUriFor(SYSTEM_FOLDING_MODE_KEYS)
        mObserver = object : ContentObserver(null) {
            override fun onChange(selfChange: Boolean) {
                mListener?.run {
                    onScreenChange(isSmallScreen(context))
                }
            }
        }
        mObserver?.run {
            context.applicationContext.contentResolver.registerContentObserver(foldingAngle, false, this)
        }
    }

    fun unregister(context: Context) {
        mObserver?.run {
            context.applicationContext.contentResolver.unregisterContentObserver(this)
        }
        mListener = null
        mObserver = null
    }

    fun isSmallScreen(context: Context): Boolean {
        val state = Settings.Global.getInt(
            context.applicationContext.contentResolver, SYSTEM_FOLDING_MODE_KEYS, SYSTEM_FOLDING_MODE_OPEN
        )
        return state == SYSTEM_FOLDING_MODE_CLOSE
    }

    interface ScreenListener {
        fun onScreenChange(isSmallScreen: Boolean)
    }
}