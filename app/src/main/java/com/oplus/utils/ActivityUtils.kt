/***********************************************************
 * * Copyright (C), 2018-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ActivityUtils.kt
 * * Description: common util
 * * Version:1.0
 * * Date :2023/5/9
 * * Author:NieXiaokang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import android.content.Intent
import com.oplus.alarmclock.BaseActivity
import com.oplus.clock.common.utils.Log
import com.oplus.uah.UAHResClient
import com.oplus.uah.info.UAHEventRequest
import com.oplus.uah.info.UAHPerfConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

object ActivityUtils {
    private const val TAG = "ActivityUtils"
    private const val TWO_THOUSAND = 2000
    var sSettingActivity: ArrayList<WeakReference<BaseActivity>> = ArrayList()
    @JvmStatic
    fun startActivity(context: Context, action: String, packageName: String) {
        kotlin.runCatching {
            val intent = Intent(action)
            intent.`package` = packageName
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
            val info = context.packageManager.resolveActivity(intent, 0)
            if (info == null) {
                Log.d(TAG, "startActivity failed: activity is not find->action:$action,package:$packageName")
            } else {
                context.startActivity(intent)
                Log.d(TAG, "startActivity success action:$action,package:$packageName")
            }
        }.onFailure {
            Log.d(TAG, "startActivity failed: ${it.message}")
        }
    }

    @JvmStatic
    fun increaseFrequency() {
        GlobalScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val myClient = UAHResClient.get(ActivityUtils::class.java)
                if (myClient != null) {
                    val id = myClient.acquireEvent(UAHEventRequest(UAHPerfConstants.UAH_EVENT_ACTIVITY_START, "", TWO_THOUSAND, null))
                    Log.d(TAG, "UAHResRequest id: $id")
                }
            }.onFailure {
                Log.d(TAG, "increaseFrequency not support")
            }
        }
    }
}