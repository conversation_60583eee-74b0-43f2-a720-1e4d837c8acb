/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.


 * All rights reserved.
 *
 * Description    :
 * History      :
 * (ID, Date, Author, Description)
 * V1.0  2015.01.13 fanzuo  creat
 *******************************************************/

package com.oplus.realweather;

public class CommandIds {
    /**
     * these actions is useful to renderscrpt's widget get bitmap from widget
     */
    //public static final int COMMAND_LAUNCHER_GETWIDGET_BITMAP = 0x01;

    /**
     * tell the widget to recycle bitmap
     */
    public static final int COMMAND_LAUNCHER_DELETEWIDGET = 0x02;

    /**
     * tell widget to add and corresponding params's meaning
     * param[0] = 0, means in mini mode add
     * param[0] = 1, means in normal mode add
     * param[0] = 2, means in normal mode from one screen to another
     * param[0] = 3, means in mini mode from one screen to another
     * param[0] = 4, means in normal mode add and folder is showing
     * param[0] = 5, means in normal mode add and 3D interface is showing
     * param[0] = 6, means in normal mode add and main menu is showing
     * param[0] = 7, means in normal mode and launcher is in pause state
     */
    public static final int COMMAND_LAUNCHER_ADDWIDGET = 0x04;

    /**
     * next action is useful to all widget in the same screen change widget's
     * position param[0] = 0, means in mini mode param[0] = 1, means in normal
     * mode
     */
    public static final int COMMAND_LAUNCHER_UPDATEWIDGET_POS = 0x05;

    /**
     * launcher enter into mini mode
     */
    public static final int COMMAND_LAUNCHER_ENTER_MINI_STATE = 0x06;

    /**
     * launcher exit small state for Multi screen wallpaper
     */
    public static final int COMMAND_LAUNCHER_EXIT_SMALL_STATE_FOR_MULTI_SCREEN_WALLPAPER = 0x07;

    /**
     * workspace begin to change screen
     */
    public static final int COMMAND_LAUNCHER_SCREENCHANGE_BEGIN = 0x08;

    /**
     * workspace end screen change param[0]: is last time screen number
     * param[1]: is current screen number
     */
    public static final int COMMAND_LAUNCHER_SCREENCHANGE_END = 0x09;

    /**
     * long click drag over widget
     */
    public static final int COMMAND_LAUNCHER_LONGCLICK_WIDGET = 0x0a;

    /**
     * launcher enter background
     *  param[0] = 0,in mini mode
     *  param[0] = 1, in normal mode
     *  param[0] = 2, folder is showing;
     *  param[0] = 3, 3D interface is showing
     *  param[0] = 4, main menu is showing
     */
    public static final int COMMAND_LAUNCHER_PAUSE = 0x0b;

    /**
     * launcher resume to foreground
     * param[0] = 0, in mini mode
     * param[1] = 1, in normal mode
     * param[2] = 2, folder is showing
     * param[3] = 3, 3D interface is showing
     * param[4] = 4, main menu is showing
     */
    public static final int COMMAND_LAUNCHER_RESUME = 0x0c;

    /**
     * show app list
     */
    //public static final int COMMAND_LAUNCHER_SHOW_ALLAPPS = 0x0d;

    /**
     * hide app list
     */
    //public static final int COMMAND_LAUNCHER_HIDE_ALLAPPS = 0x0e;

    /**
     * show 3D interface
     */
    public static final int COMMAND_LAUNCHER_SHOW_3D_PREVIEW = 0x0f;

    /**
     * hide 3D interface
     */
    public static final int COMMAND_LAUNCHER_HIDE_3D_PREVIEW = 0x10;

    /**
     * launcher open folder
     */
    public static final int COMMAND_LAUNCHER_OPEN_FOLDER = 0x11;

    /**
     * launcher close folder
     */
    public static final int COMMAND_LAUNCHER_CLOSE_FOLDER = 0x12;

    /**
     * launcher lose focus
     */
    public static final int COMMAND_LAUNCHER_FOCUS_CHANGED = 0X13;

    /**
     * workspace begin to change screen in small mode
     */
    public static final int COMMAND_LAUNCHER_SCREENCHANGE_BEGIN_NOT_NORMAL_MODE = 21;

    /**
     * workspace end screen change in small mode param[0]: is last time screen
     * number param[1]: is current screen number
     */
    public static final int COMMAND_LAUNCHER_SCREENCHANGE_END_NOT_NORMAL_MODE = 20;

    public static final int COMMAND_LAUNCHER_RESUME_NOT_NORMAL_MODE = 0X16;

    public static final int COMMAND_HOME_KEY_PRESSED = 0x17;

}
