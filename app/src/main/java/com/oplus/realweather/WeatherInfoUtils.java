/**********************************************************************
 * Copyright 2013 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 *
 * Author : liguan
 * Date : 2015/4/1
 * Description : Used to update weather.
 **********************************************************************/
package com.oplus.realweather;

import android.net.Uri;

/**
 * @Description<br>
 * @Author<br>lichangwei
 * @Since<br>2011-9-30
 */
public class WeatherInfoUtils {

    private static final String TAG = "WeatherInfoUtils";
    public static final String WEATHER_INFO_TABLE = "weather_info";
    public static final String WEATHER_CITY_TABLE = "attent_city";
    public static final Uri WEATHER_SERVICE_URI = Uri.parse("content://com.coloros.weather.service.provider.data");
    public static final Uri WEATHER_CITY_CONTENT_URI = Uri.withAppendedPath(WEATHER_SERVICE_URI, WEATHER_CITY_TABLE);
    public static final Uri WEATHER_INFO_CONTENT_URI = Uri.withAppendedPath(WEATHER_SERVICE_URI, WEATHER_INFO_TABLE);

}
