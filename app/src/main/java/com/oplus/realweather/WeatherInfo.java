/**********************************************************************
 * Copyright 2013 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Author : liguan
 * Date : 2015/4/1
 * Description : Used to store weatherinfo returned from aidl.
 **********************************************************************/
package com.oplus.realweather;

public class WeatherInfo {
    private int mId;
    private long mCityId;
    private int mWeatherId;
    private int mDayWeatherId;
    private int mNightWeatherId;
    private Long mDate;

    private String mCurrentWeather;
    private String mCurrentTemp;
    private String mCurrentWindDirect;
    private String mCurrentWindPower;
    private String mCurrentHumidity;
    private String mCurrentUvIndex;
    private String mCurrentUvDesc;
    private String mDayWeather;
    private int mDayTemp;
    private String mDayWindDirect;
    private String mDayWindPower;
    private String mNightWeather;
    private int mNightTemp;
    private String mNightWindDirect;
    private String mNightWindPower;

    private String mAlert;
    private String mPic;
    private String mUrl;
    private String mRemark;
    private String mRemark2;

    public String getCurrentWeather() {
        return mCurrentWeather;
    }

    public void setCurrentWeather(String currentWeather) {
        this.mCurrentWeather = currentWeather;
    }

    public String getCurrentTemp() {
        return mCurrentTemp;
    }

    public void setCurrentTemp(String currentTemp) {
        this.mCurrentTemp = currentTemp;
    }

    public String getCurrentWindDirect() {
        return mCurrentWindDirect;
    }

    public void setCurrentWindDirect(String currentWindDirect) {
        this.mCurrentWindDirect = currentWindDirect;
    }

    public String getCurrentWindPower() {
        return mCurrentWindPower;
    }

    public void setCurrentWindPower(String currentWindPower) {
        this.mCurrentWindPower = currentWindPower;
    }

    public String getCurrentHumidity() {
        return mCurrentHumidity;
    }

    public void setCurrentHumidity(String currentHumidity) {
        this.mCurrentHumidity = currentHumidity;
    }

    public String getCurrentUvIndex() {
        return mCurrentUvIndex;
    }

    public void setCurrentUvIndex(String currentUvIndex) {
        this.mCurrentUvIndex = currentUvIndex;
    }

    public String getCurrentUvDesc() {
        return mCurrentUvDesc;
    }

    public void setCurrentUvDesc(String currentUvDesc) {
        this.mCurrentUvDesc = currentUvDesc;
    }

    public String getDayWeather() {
        return mDayWeather;
    }

    public void setDayWeather(String dayWeather) {
        this.mDayWeather = dayWeather;
    }

    public int getDayTemp() {
        return mDayTemp;
    }

    public void setDayTemp(int dayTemp) {
        this.mDayTemp = dayTemp;
    }

    public String getDayWindDirect() {
        return mDayWindDirect;
    }

    public void setDayWindDirect(String dayWindDirect) {
        this.mDayWindDirect = dayWindDirect;
    }

    public String getDayWindPower() {
        return mDayWindPower;
    }

    public void setDayWindPower(String dayWindPower) {
        this.mDayWindPower = dayWindPower;
    }

    public String getNightWeather() {
        return mNightWeather;
    }

    public void setNightWeather(String nightWeather) {
        this.mNightWeather = nightWeather;
    }

    public int getNightTemp() {
        return mNightTemp;
    }

    public void setNightTemp(int nightTemp) {
        this.mNightTemp = nightTemp;
    }

    public String getNightWindDirect() {
        return mNightWindDirect;
    }

    public void setNightWindDirect(String nightWindDirect) {
        this.mNightWindDirect = nightWindDirect;
    }

    public String getNightWindPower() {
        return mNightWindPower;
    }

    public void setNightWindPower(String nightWindPower) {
        this.mNightWindPower = nightWindPower;
    }

    public void setId(int id) {
        this.mId = id;
    }

    public int getId() {
        return mId;
    }

    public void setCityId(long cityId) {
        this.mCityId = cityId;
    }

    public long getCityId() {
        return mCityId;
    }

    public void setWeatherId(int weatherId) {
        this.mWeatherId = weatherId;
    }

    public int getWeatherId() {
        return mWeatherId;
    }

    public int getDayWeatherId() {
        return mDayWeatherId;
    }

    public void setDayWeatherId(int dayWeatherId) {
        this.mDayWeatherId = dayWeatherId;
    }

    public int getNightWeatherId() {
        return mNightWeatherId;
    }

    public void setNightWeatherId(int nightWeatherId) {
        this.mNightWeatherId = nightWeatherId;
    }

    public void setDate(Long date) {
        this.mDate = date;
    }

    public Long getDate() {
        return mDate;
    }


    public void setAlert(String alert) {
        this.mAlert = alert;
    }

    public String getAlert() {
        return mAlert;
    }

    public void setPic(String pic) {
        this.mPic = pic;
    }

    public String getPic() {
        return mPic;
    }

    public void setUrl(String url) {
        this.mUrl = url;
    }

    public String getUrl() {
        return mUrl;
    }

    public void setRemark(String remark) {
        this.mRemark = remark;
    }

    public String getRemark() {
        return mRemark;
    }

    public void setRemark2(String remark2) {
        this.mRemark2 = remark2;
    }

    public String getRemark2() {
        return mRemark2;
    }

    @Override
    public String toString() {
        String prefix = "WeatherInfo(mId=" + this.mId + " mCityId=" + this.mCityId + " mWeatherId=" + this.mWeatherId
                + " mDate=" + this.mDate + " mCurrentWeather=" + mCurrentWeather
                + " mCurrentTemp=" + mCurrentTemp + " mDayWeather=" + mDayWeather + " mDayTemp=" + mDayTemp
                + " mNightWeather=" + mNightWeather + " mNightTemp=" + mNightTemp + ")";
        return prefix;
    }
}
