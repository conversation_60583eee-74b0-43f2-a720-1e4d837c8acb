/**
 * Copyright (C), 2008-2013, OPLUS Mobile Comm Corp., Ltd
 * FileName: WeatherDataHelper.java
 * Author: Hugo
 * Date: 2013-10-28
 * Description:
 * History:
 * <author>     <time>     <version >     <desc>
 * Hugo     2013-10-28
 */

package com.oplus.realweather;

import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.database.ContentObserver;
import android.os.Handler;
import android.os.IBinder;
import android.text.format.DateUtils;

import com.oplus.aidl.AttentWeatherInfo;
import com.oplus.aidl.IExternalWeatherLocationListener;
import com.oplus.aidl.IExternalWeatherWidgetService;
import com.oplus.aidl.IPhoneStateListener;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;
import java.util.TimeZone;

public class WeatherDataHelper {
    public static final String TAG = "WeatherDataHelper";
    public static final int MSG_GETCITY_FAILURE = -1;
    public static final int MSG_GETCITY_SUCCESS = 1;
    public static final int MSG_GETCITY_EXISTED = 0;
    public static final float MINUTES_PER_HOUR = 60.0f;
    private static final int SECONDS_PER_MINUTE = 60;
    private static final int BIND_WEATHER_SERVICE_DELAY = 150;
    private final Context mContext;
    private final Handler mHandler = new Handler();
    public IExternalWeatherWidgetService mExternalWeatherService = null;
    private boolean mIsBound = false;
    private final Runnable mBindAidlServiceTask = new Runnable() {
        public void run() {
            try {
                Intent service = new Intent("com.oplus.weather.external.ExternalWeatherWidgetService");
                Intent explicitIntent = WeatherUtils.getExplicitIntent(mContext, service);
                if (explicitIntent == null) {
                    Log.w(TAG, "mBindAidlServiceTask.run(). The explicitIntent is null, so return.");
                    return;
                }
                mContext.bindService(explicitIntent, mConn, Context.BIND_AUTO_CREATE);
            } catch (Exception e) {
                Intent service = new Intent("com.oppo.weather.external.ExternalWeatherWidgetService");
                Intent explicitIntent = WeatherUtils.getExplicitIntent(mContext, service);
                if (explicitIntent == null) {
                    Log.w(TAG, "mBindAidlServiceTask.run(). The explicitIntent is null, so return.");
                    return;
                }
                mContext.bindService(explicitIntent, mConn, Context.BIND_AUTO_CREATE);
            }
            mIsBound = true;
        }
    };

    private IWeatherServiceCallback mIWeatherServiceCallback = null;
    private final ContentObserver mWeatherDatabaseWeatherInfoObserver = new ContentObserver(new Handler()) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            if (null != mIWeatherServiceCallback) {
                mIWeatherServiceCallback.onWeatherInfoChange();
            }
        }
    };

    private final ContentObserver mWeatherDatabaseCityObserver = new ContentObserver(new Handler()) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            if (null != mIWeatherServiceCallback) {
                mIWeatherServiceCallback.onCityChange();
            }
        }
    };

    public WeatherDataHelper(Context context, IWeatherServiceCallback callback) {
        mContext = context;
        mIWeatherServiceCallback = callback;
        Log.d(TAG, "WeatherDataHelper  registerContentObserver mWeatherDatabaseObserver");
        ContentResolver cr = mContext.getContentResolver();
        cr.registerContentObserver(WeatherInfoUtils.WEATHER_INFO_CONTENT_URI, true, mWeatherDatabaseWeatherInfoObserver);
        cr.registerContentObserver(WeatherInfoUtils.WEATHER_CITY_CONTENT_URI, true, mWeatherDatabaseCityObserver);
    }

    public void bindExternalWeatherService() {
        Log.d(TAG, "bindExternalWeatherService..Handler.postDelayed");
        mHandler.postDelayed(mBindAidlServiceTask, BIND_WEATHER_SERVICE_DELAY);
    }

    private final ServiceConnection mConn = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            mExternalWeatherService = IExternalWeatherWidgetService.Stub.asInterface(service);
            Log.d(TAG, "onServiceConnected. mExternalWeatherService = " + mExternalWeatherService);
            if (null != mIWeatherServiceCallback) {
                mIWeatherServiceCallback.onExternalWeatherServiceConnected();
            } else {
                Log.w(TAG, "onServiceConnected. mIWeatherServiceCallback = null");
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected.");
            mExternalWeatherService = null;
            if (null != mIWeatherServiceCallback) {
                mIWeatherServiceCallback.onExternalWeatherServiceDisconnected();
            }
            bindExternalWeatherService();
        }
    };

    public void unbindService() {
        Log.d(TAG, "unbindService.");
        try {
            if (mIsBound) {
                mContext.unbindService(mConn);
                mExternalWeatherService = null;
                mIsBound = false;
            }
        } catch (Exception e) {
            Log.e(TAG, "unbindService :Exception e = " + e);
        }
    }

    /**
     * select * from weather_info where city_id = id
     */
    public WeatherInfo getCurrentCityWeather(long id) {
        WeatherInfo weatherInfo = new WeatherInfo();
        int timezone = AutoLocationUtils.CHINA_TIME_ZONE;
        AttentWeatherInfo attentWeatherInfo = null;
        timezone = (int) getTimeZoneOfAttendCity(id);
        if (checkExternalWeatherService()) {
            try {
                attentWeatherInfo = mExternalWeatherService.getOneAttentCityWeatherInfoList(id, timezone);
                Log.d(TAG, "getCurrentCityWeather, attentWeatherInfo = " + attentWeatherInfo);
            } catch (Exception e) {
                Log.d(TAG, "getCurrentCityWeather, Exception");
                e.printStackTrace();
            }
        }

        if ((null != attentWeatherInfo) && (null != weatherInfo)) {
            int weatherId = attentWeatherInfo.getWeatherId();
            Log.d(TAG, "getCurrentCityWeather, weatherId = " + weatherId);
            weatherInfo.setWeatherId(weatherId);
            String currentWeather = getWeatherTypeById(weatherId, mContext);
            if (WeatherUtils.isInfoNone(currentWeather)) {
                currentWeather = attentWeatherInfo.getDayWeather();
            }
            weatherInfo.setCurrentWeather(currentWeather);
            int dayTemp = attentWeatherInfo.getDayTemp();
            weatherInfo.setDayTemp(dayTemp);
            int nightTemp = attentWeatherInfo.getNightTemp();
            weatherInfo.setNightTemp(nightTemp);
            String currentTemp = attentWeatherInfo.getCurrentTemp();
            Log.d(TAG, "getCurrentCityWeather, currentTemp = " + currentTemp);
            if (null != currentTemp) {
                if (!WeatherUtils.isInfoNone(currentTemp)
                        && !currentTemp.endsWith(mContext.getResources().getString(R.string.centigrade))) {
                    weatherInfo.setCurrentTemp(currentTemp);
                }
            }
        }
        Log.d(TAG, "getCurrentCityWeather, attentWeatherInfo = " + attentWeatherInfo);
        return weatherInfo;
    }

    public boolean isCurrentDay(int timezone, WeatherInfo tempInfo) {
        long data = tempInfo.getDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(data);

        Calendar curCalender = getCalendarByTimeZone(timezone);

        return (curCalender.get(Calendar.YEAR) == calendar.get(Calendar.YEAR))
                && (curCalender.get(Calendar.MONTH) == calendar.get(Calendar.MONTH))
                && (curCalender.get(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH));

    }

    public Calendar getCalendarByTimeZone(int timezone) {
        String strTimeZone = "GMT";

        if (timezone > 0) {
            strTimeZone += "+" + timezone;
        } else {
            strTimeZone += timezone;
        }

        TimeZone curTimeZone = TimeZone.getTimeZone(strTimeZone);
        Calendar calender = Calendar.getInstance(curTimeZone);
        return calender;
    }

    public long getCityCurrentTime(float timeZone) {
        return getTimeMillonWithTimeZone(timeZone);
    }

    public long getTimeMillonWithTimeZone(float timeZone) {
        float localTimeZone = getSystemTimeZone();
        float timeZoneDiff = timeZone - localTimeZone;

        return System.currentTimeMillis() + (long) timeZoneDiff
                * DateUtils.HOUR_IN_MILLIS;
    }

    public static float getSystemTimeZone() {
        TimeZone tz = Calendar.getInstance().getTimeZone();
        int off = tz.getRawOffset();

        off = off / (int) DateUtils.MINUTE_IN_MILLIS;

        int hour = Math.abs(off) / SECONDS_PER_MINUTE;
        float minutes = Math.abs(off) % MINUTES_PER_HOUR;

        if (off < 0) {
            return -(hour + minutes / MINUTES_PER_HOUR);
        } else {
            return hour + minutes / SECONDS_PER_MINUTE;
        }
    }

    public float getTimeZoneOfAttendCity(long cityId) {
        if (cityId < 0) {
            return AutoLocationUtils.CHINA_TIME_ZONE;
        }
        float timeZone = AutoLocationUtils.CHINA_TIME_ZONE;

        if (checkExternalWeatherService()) {
            try {
                timeZone = mExternalWeatherService.getTimeZoneOfAttendCity(cityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return timeZone;
    }

    public long getCityIdByAttentCityId(long attentCityId) {
        long cityId = -1;
        if (checkExternalWeatherService()) {
            try {
                cityId = mExternalWeatherService.getCityIdByAttentCityId(attentCityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityId;
    }

    public long getNextCityId(long cityId) {
        if (cityId < 0) {
            return -1;
        }

        return getAdjacentCityId(cityId, true);
    }

    public long getPreCityId(long cityId) {
        if (cityId < 0) {
            return -1;
        }
        return getAdjacentCityId(cityId, false);
    }

    /**
     * get the adjacent city Id
     *
     * @param curCityId: current city_id
     * @param isNext:    if true, get the next city Id; else get the previous city Id
     * @return
     */
    public long getAdjacentCityId(long curCityId, boolean isNext) {
        long cityId = -1;
        if (checkExternalWeatherService()) {
            try {
                cityId = mExternalWeatherService.getAdjacentCityId(curCityId, isNext);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityId;
    }

    public int getAttentCityCount(boolean includeLocalWeather) {
        int count = 0;
        if (checkExternalWeatherService()) {
            try {
                count = mExternalWeatherService.getAttentCityCount(includeLocalWeather);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return count;
    }

    public long getCurrentCityId(Context context) {
        long cityId = -1;
        if (checkExternalWeatherService()) {
            try {
                cityId = mExternalWeatherService.getCurrentCityId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityId;
    }

    public long getLocationCityId(Context context) {
        long cityId = -1;
        if (checkExternalWeatherService()) {
            try {
                cityId = mExternalWeatherService.getLocationCityId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityId;
    }

    public long getFirstAttentCityId(Context context) {
        long cityId = -1;
        if (checkExternalWeatherService()) {
            try {
                cityId = mExternalWeatherService.getFirstAttentCityId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityId;
    }

    public String getCurrentAttentCityName(long cityId, Context context) {
        String cityName = null;
        if (checkExternalWeatherService()) {
            try {
                cityName = mExternalWeatherService.getCurrentAttentCityName(cityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return cityName;
    }

    public String getCurrentAttentProvinceName(long cityId, Context context) {
        String provinceName = null;
        if (checkExternalWeatherService()) {
            try {
                provinceName = mExternalWeatherService.getCurrentAttentProvinceName(cityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return provinceName;
    }

    public String getCurrentAttentCountryName(long cityId, Context context) {
        String countryName = null;
        if (checkExternalWeatherService()) {
            try {
                countryName = mExternalWeatherService.getCurrentAttentCountryName(cityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return countryName;
    }

    public String getWeatherTypeById(int weatherId, Context context) {
        String weatherType = null;
        if (checkExternalWeatherService()) {
            try {
                weatherType = mExternalWeatherService.getWeatherTypeById(weatherId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return weatherType;
    }

    public boolean isLocationCity(long cityId) {
        boolean isLocationCity = false;
        if (checkExternalWeatherService()) {
            try {
                isLocationCity = mExternalWeatherService.isLocationCity(cityId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return isLocationCity;
    }

    public void updateWeatherinfo(boolean isNeedLocation) {
        Log.d(TAG, "updateWeatherInfo. isNeedLct = " + isNeedLocation);
        if (checkExternalWeatherService()) {
            try {
                mExternalWeatherService.updateWeatherinfo(isNeedLocation);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void registerLocationListener(IExternalWeatherLocationListener listener) {
        Log.d(TAG, "registerLctListener. listener = " + listener);
        if (checkExternalWeatherService()) {
            try {
                mExternalWeatherService.registerLocationListener(listener);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void unRegisterLocationListener() {
        Log.d(TAG, "unRegisterLctListener. ");
        if (checkExternalWeatherService()) {
            try {
                mExternalWeatherService.unRegisterLocationListener();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void listenPhoneState(IPhoneStateListener listener, int events) {
        Log.d(TAG, "listenPhoneState. listener = " + listener + " , events = " + events);
        if (checkExternalWeatherService()) {
            try {
                mExternalWeatherService.listenPhoneState(listener, events);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * If has bound the aidl service. If not, bind it.
     */
    public boolean checkExternalWeatherService() {
        if (mExternalWeatherService == null) {
            Log.w(TAG, "The Aidl service is not bound!");
            bindExternalWeatherService();
            return false;
        } else {
            return true;
        }
    }

    public void recycleAll() {
        Log.d(TAG, "recycleAll");
        unbindService();
        Log.d(TAG, "recycleAll  unregisterContentObserver mWeatherDatabaseObserver");
        ContentResolver cr = mContext.getContentResolver();
        cr.unregisterContentObserver(mWeatherDatabaseWeatherInfoObserver);
        cr.unregisterContentObserver(mWeatherDatabaseCityObserver);
    }
}
