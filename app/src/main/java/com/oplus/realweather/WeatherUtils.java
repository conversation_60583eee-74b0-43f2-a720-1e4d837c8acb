/**********************************************************************
 * Copyright 2013 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 *
 * Author : liguan
 * Date : 2015/4/1
 * Description :.
 **********************************************************************/
package com.oplus.realweather;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.text.TextUtils;
import android.util.Pair;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.IBaseChannel;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.ActivityUtils;

import java.util.List;

public class WeatherUtils {
    private static final String TAG = "WeatherUtils";
    private static final String ACTION_WEATHER_MORNING = "com.oplus.weather.action.MORNING_START";
    private static final String PACKAGE_WEATHER = "com.coloros.weather2";
    private static final String PACKAGE_WEATHER_W = "net.oneplus.weather";
    private static final String MORNING_PLAY_TIME = "weather_morning_play_time";
    private static final String SP_NAME = AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP;

    @SuppressLint("NewApi")
    public static boolean isInfoNone(String infor)
    {
        boolean isNone = false;
        if (TextUtils.isEmpty(infor) || "None".equals(infor)) {
            isNone = true;
        }
        return isNone;
    }

    /**
     * query service info with intent which has a service action, then return a new intent with service componentName
     * @param context
     * @param implicitIntent. intent with a service action
     * @return a new intent with service componentName
     */
    public static Intent getExplicitIntent(Context context, Intent implicitIntent) {
        PackageManager pm = context.getPackageManager();
        List<ResolveInfo> resolveInfo = pm.queryIntentServices(implicitIntent, 0);
        // Make sure only one match was found
        if ((resolveInfo == null) || (resolveInfo.size() != 1)) {
            Log.w(TAG, "getExplicitIntent, resolveInfo == null or more than one service have same intent action, return null");
            return null;
        }
        // Get component info and create ComponentName
        ResolveInfo serviceInfo = resolveInfo.get(0);
        String packageName = serviceInfo.serviceInfo.packageName;
        String className = serviceInfo.serviceInfo.name;
        ComponentName component = new ComponentName(packageName, className);

        // Create a new intent. Use the old one for extras and such reuse
        Intent explicitIntent = new Intent(implicitIntent);
        // Set the component to be explicit
        explicitIntent.setComponent(component);
        return explicitIntent;
    }

    /**
     * 拉起天气服务，条件：每天4-10点的第一个闹钟
     * 方式：拉起天气的activity，天气自己拉起服务（可以避免权限问题）
     */
    public static void startWeatherService(Context context) {
        Pair<Boolean, Long> pair = isFirstRingToday(context);
        boolean isFirstRingToday = pair.first;
        boolean isMorningTime = PlayMorningTools.isMorningTime(context);
        if (isFirstRingToday && isMorningTime) {
            updateRingTime(context);
            String packageWeather = getPackageWeather(context);
            ActivityUtils.startActivity(context, ACTION_WEATHER_MORNING, packageWeather);
        } else {
            Log.i(TAG, "no startWeatherService ->isFirstRingToday:" + isFirstRingToday + ",isMorningTime:" + isMorningTime);
        }
    }

    private static String getPackageWeather(Context context) {
        boolean isWplus = ChannelManager.INSTANCE.getChannelUtils().getChannel() == IBaseChannel.CHANNEL_WPLUS;
        boolean isExp = DeviceUtils.isExpVersion(context);
        return (isWplus && isExp) ? PACKAGE_WEATHER_W : PACKAGE_WEATHER;
    }

    /**
     * 是否是今天的第一次响铃
     */
    private static Pair<Boolean, Long> isFirstRingToday(Context context) {
        long playTimestamp = PrefUtils.getLong(context, SP_NAME, MORNING_PLAY_TIME, 0);
        boolean isHaveRing = (playTimestamp == 0) || (!MorningAlarmClock.isSameData(playTimestamp));
        return new Pair<>(isHaveRing, playTimestamp);
    }

    private static void updateRingTime(Context context) {
        PrefUtils.putLong(context, SP_NAME, MORNING_PLAY_TIME, System.currentTimeMillis());
    }
}
