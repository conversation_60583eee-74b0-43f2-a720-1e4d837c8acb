/**********************************************************************
 * Copyright 2013 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Author : zhouwurun
 * Date : 2015/3/6
 * Description : Used to auto location.
 **********************************************************************/
package com.oplus.realweather;

import static android.content.Context.RECEIVER_EXPORTED;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.RemoteException;
import android.os.SystemClock;
import android.telephony.PhoneStateListener;

import com.oplus.aidl.IExternalWeatherLocationListener;
import com.oplus.aidl.IPhoneStateListener;
import com.oplus.clock.common.utils.Log;
import com.coloros.widget.commondata.Constants;

/**
 * Used to auto location.
 */
public class AutoLocationUtils {
    private static final String TAG = "AutoLctUtils";

    public static final int CHINA_TIME_ZONE = 8;
    public static final long DELAY_TIME_UPDATE_WEATHER_FOR_CELL_LOCATION = 30 * 60 * 1000; // 30min
    public static final long DELAY_TIME_UPDATE_WEATHER_FOR_OTHER = 60 * 60 * 1000; // 1h

    private static final String ACTION_DELAY_UPDATE_WEATHER = "delay_update_weather";
    private static AutoLocationUtils sInstance = null;

    private final Context mContext;
    private final WeatherDataHelper mWeatherDBHelper;
    private final AlarmManager mAlarmManager;
    private final PendingIntent mUpdateWeatherPI;

    private long mLastUpdateWeatherTime = -1L;

    private boolean mAlreadyStartLocation = false;

    public synchronized static AutoLocationUtils getInstance(Context context, IWeatherServiceCallback callback) {
        if (null == sInstance) {
            synchronized (AutoLocationUtils.class) {
                sInstance = new AutoLocationUtils(context, callback);
            }
        }
        return sInstance;
    }

    private AutoLocationUtils(Context context, IWeatherServiceCallback callback) {
        mContext = context;

        mWeatherDBHelper = new WeatherDataHelper(context, callback);
        if (null != mWeatherDBHelper) {
            mWeatherDBHelper.bindExternalWeatherService();
        }

        mAlarmManager = (AlarmManager) mContext.getSystemService(Context.ALARM_SERVICE);

        // modify for bug 2826333 by wangan on 2020-02-20
        //Intent intent = new Intent(ACTION_DELAY_UPDATE_WEATHER);
        //mUpdateWeatherPI = PendingIntent.getBroadcast(mContext, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        mUpdateWeatherPI = null;
    }

    public void updateData() {
        if (mWeatherDBHelper != null) {
            int attentCityCount = mWeatherDBHelper.getAttentCityCount(true);
            startUpdateWeatherInfo(attentCityCount <= 0);
        }
    }

    public void startAutoLocation() {
        Log.d(TAG, "startAutoLct");
        if (mAlreadyStartLocation) {
            Log.w(TAG, "startAutoLct AlreadyStartAutoLct, return");
            return;
        }
        IntentFilter intentFilter = new IntentFilter(ACTION_DELAY_UPDATE_WEATHER);
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        mContext.registerReceiver(mUpdateWeatherReceiver, intentFilter, Constants.OPLUS_CUSTOM_APP_PERMISSION,null,RECEIVER_EXPORTED);
        mLastUpdateWeatherTime = -1L;
        listenCellLocation();
        mAlreadyStartLocation = true;
    }

    public void recycleAll() {
        Log.d(TAG, "recycleAll");
        mContext.unregisterReceiver(mUpdateWeatherReceiver);
        unListenCellLocation();
        if (null != mUpdateWeatherPI) {
            mAlarmManager.cancel(mUpdateWeatherPI);
        }

        if (null != mWeatherDBHelper) {
            mWeatherDBHelper.recycleAll();
        }
        mAlreadyStartLocation = false;
        synchronized (AutoLocationUtils.class) {
            sInstance = null;
        }
    }

    /**
     * @Description<br>Notify the weather service to refresh data.
     */
    public void startUpdateWeatherInfo(boolean isNeedLocation) {
        Log.d(TAG, "startUpdateWeatherInfo. mWeatherDBHelper = " + mWeatherDBHelper);
        if (mWeatherDBHelper != null) {
            mWeatherDBHelper.updateWeatherinfo(isNeedLocation);
            mWeatherDBHelper.registerLocationListener(mExternalWeatherLocationListener);
        }
    }

    public void listenCellLocation() {
        Log.d(TAG, "listenCellLct");
        if (mWeatherDBHelper != null) {
            mWeatherDBHelper.listenPhoneState(mPhoneStateListener, PhoneStateListener.LISTEN_CELL_LOCATION);
        }
    }

    private void unListenCellLocation() {
        Log.d(TAG, "unListenCellLct");
        if (mWeatherDBHelper != null) {
            mWeatherDBHelper.listenPhoneState(mPhoneStateListener, PhoneStateListener.LISTEN_NONE);
        }
    }

    private final IPhoneStateListener mPhoneStateListener = new IPhoneStateListener.Stub() {
        @Override
        public void onCellLocationChanged() {
            Log.d(TAG, "onCellLctChanged.");
            doLocationAndUpdateWeather(true, DELAY_TIME_UPDATE_WEATHER_FOR_CELL_LOCATION);
        }
    };

    private void doLocationAndUpdateWeather(boolean needDelayUpdate, long delayTime) {
        Log.d(TAG, "doLctAndUpdateWeather. needDelayUpdate = " + needDelayUpdate + " , delayTime = " + delayTime);
        Log.d(TAG, "doLctAndUpdateWeather. mLastUpdateWeatherTime = " + mLastUpdateWeatherTime);
        Log.d(TAG, "doLctAndUpdateWeather. SystemClock.elapsedRealtime() = " + SystemClock.elapsedRealtime());

        if ((mLastUpdateWeatherTime != -1L) && (SystemClock.elapsedRealtime() - mLastUpdateWeatherTime < delayTime)) {
            if ((null != mUpdateWeatherPI) && needDelayUpdate) {
                Log.d(TAG, "doLctAndUpdateWeather. Delay to update.");
                mAlarmManager.set(AlarmManager.ELAPSED_REALTIME, mLastUpdateWeatherTime + delayTime, mUpdateWeatherPI);
            }
        } else {
            Log.d(TAG, "doLctAndUpdateWeather. Now to update.");
            startUpdateWeatherInfo(true);
        }
    }

    private final BroadcastReceiver mUpdateWeatherReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();

            Log.d(TAG, "onReceive. action = " + action);

            if (ACTION_DELAY_UPDATE_WEATHER.equals(action)) {
                doLocationAndUpdateWeather(false, DELAY_TIME_UPDATE_WEATHER_FOR_CELL_LOCATION);
            } else if (Intent.ACTION_SCREEN_ON.equals(action)) {
                doLocationAndUpdateWeather(false, DELAY_TIME_UPDATE_WEATHER_FOR_OTHER);
            }
        }
    };

    private final IExternalWeatherLocationListener mExternalWeatherLocationListener = new IExternalWeatherLocationListener.Stub() {
        @Override
        public int onLocationCompelete(int result) throws RemoteException {
            Log.d(TAG, "onLctCompelete. result = " + result);
            switch (result) {
                case WeatherDataHelper.MSG_GETCITY_SUCCESS:
                    mAlarmManager.cancel(mUpdateWeatherPI);
                    mLastUpdateWeatherTime = SystemClock.elapsedRealtime();
                    break;
                case WeatherDataHelper.MSG_GETCITY_EXISTED:
                    mLastUpdateWeatherTime = SystemClock.elapsedRealtime();
                    break;
                case WeatherDataHelper.MSG_GETCITY_FAILURE:
                    break;
                default:
                    break;
            }
            mWeatherDBHelper.unRegisterLocationListener();
            return 0;
        }
    };

    public Object onCommand(int action, boolean isCurrentScreen) {
        Log.d(TAG, "onCommand. action = " + action + " , isCurrentScreen = " + isCurrentScreen);
        switch (action) {
            case CommandIds.COMMAND_LAUNCHER_SCREENCHANGE_END:
                doLocationAndUpdateWeather(false, DELAY_TIME_UPDATE_WEATHER_FOR_OTHER);
                break;
            case CommandIds.COMMAND_LAUNCHER_SCREENCHANGE_END_NOT_NORMAL_MODE:
                doLocationAndUpdateWeather(false, DELAY_TIME_UPDATE_WEATHER_FOR_OTHER);
                break;
            case CommandIds.COMMAND_LAUNCHER_RESUME:
                if (isCurrentScreen) {
                    doLocationAndUpdateWeather(false, DELAY_TIME_UPDATE_WEATHER_FOR_OTHER);
                }
                break;
            default:
                break;
        }

        Object reobj = null;
        return reobj;
    }

    public WeatherDataHelper getWeatherDBHelper() {
        return mWeatherDBHelper;
    }

    public boolean checkExternalWeatherService() {
        if (mWeatherDBHelper != null) {
            Log.d(TAG, "checkExternalWeatherService");
            return mWeatherDBHelper.checkExternalWeatherService();
        } else {
            return false;
        }
    }

    public int getAttentCityCount(boolean includeLocalWeather) {
        int count = 0;
        Log.d(TAG, "getAttentCityCount includeLocalWeather = " + includeLocalWeather);
        if (mWeatherDBHelper != null) {
            return mWeatherDBHelper.getAttentCityCount(includeLocalWeather);
        } else {
            Log.d(TAG, "mWeatherDBHelper = null");
        }
        return count;
    }

    public long getCityIdByAttentCityId(long cityId) {
        long resultcityId = -1;
        if (null != mWeatherDBHelper) {
            resultcityId = mWeatherDBHelper.getCityIdByAttentCityId(cityId);
        }
        return resultcityId;
    }

    public long getCurrentCityId(Context context) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getCurrentCityId(context);
        }
        return -1;
    }

    public long getLocationCityId(Context context) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getLocationCityId(context);
        }
        return -1;
    }

    public long getFirstAttentCityId(Context context) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getFirstAttentCityId(context);
        }
        return -1;
    }

    public long getPreCityId(long cityId) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getPreCityId(cityId);
        }
        return -1;
    }

    public long getNextCityId(long cityId) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getNextCityId(cityId);
        }
        return -1;
    }

    public String getCurrentAttentCityName(long cityId, Context context) {
        String cityName = null;
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getCurrentAttentCityName(cityId, context);
        }
        return cityName;
    }

    public String getCurrentAttentProvinceName(long cityId, Context context) {
        String provinceName = null;
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getCurrentAttentProvinceName(cityId, context);
        }
        return provinceName;
    }

    public String getCurrentAttentCountryName(long cityId, Context context) {
        String countryName = null;
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getCurrentAttentCountryName(cityId, context);
        }
        return countryName;
    }

    public WeatherInfo getCurrentCityWeather(long id) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getCurrentCityWeather(id);
        }
        return null;
    }

    public float getTimeZoneOfAttendCity(long cityId) {
        if (null != mWeatherDBHelper) {
            return mWeatherDBHelper.getTimeZoneOfAttendCity(cityId);
        }
        return CHINA_TIME_ZONE;
    }
}
