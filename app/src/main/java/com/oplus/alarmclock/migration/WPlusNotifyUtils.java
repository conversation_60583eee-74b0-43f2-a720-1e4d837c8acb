/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - 1PlusNotifyUtils.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/11/11     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.migration;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.coui.appcompat.snackbar.COUISnackBar;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;

public class WPlusNotifyUtils {

    private static final String TAG = "1PlusNotifyUtils";
    private static final int MIGRATE_SUCCESS_NOTIFICATION_ID = -1022;

    /**升级成功，发送通知*/
    public static void sendMigrateSuccessNotification() {
        Log.d(TAG, "send migrate success notification");
        Context context = AlarmClockApplication.getInstance();
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        if ((notificationManager != null)) {
            final Intent entryApkFromScreen = new Intent(context, AlarmClock.class);
            entryApkFromScreen.setAction(ClockConstant.ENTER_APK_FROM_SCREEN);
            entryApkFromScreen.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
            entryApkFromScreen.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                    | Intent.FLAG_ACTIVITY_SINGLE_TOP);

            final PendingIntent enterApp = PendingIntent.getActivity(context, 0,
                    entryApkFromScreen, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

            Notification.Builder b = null;
            Notification notification = null;
            b = new Notification.Builder(context, AlarmStateManager.CHANNEL_ID);
            b.setContentTitle(context.getString(R.string.update_tip));
            b.setWhen(System.currentTimeMillis());
            b.setShowWhen(false);
            b.setAutoCancel(false);
            b.setSmallIcon(R.drawable.ic_launcher_clock);
            b.setSound(null);

            CharSequence name = context.getString(R.string.clock_notification_label);
            NotificationChannel channel = new NotificationChannel(AlarmStateManager.CHANNEL_ID, name, NotificationManager.IMPORTANCE_DEFAULT);
            channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            notificationManager.createNotificationChannel(channel);
            notification = b.build();

            b.setContentIntent(enterApp);
            b.setOngoing(false);
            b.setContentText(context.getString(R.string.clock_update_notification));

            Notification.BigTextStyle bigTextStyle = new Notification.BigTextStyle();
            bigTextStyle.bigText(context.getString(R.string.clock_update_notification)).setBigContentTitle(context.getString(R.string.update_tip));
            b.setStyle(bigTextStyle);

            try {
                Log.d(TAG, "nm.notify:" + notificationManager + "id " + MIGRATE_SUCCESS_NOTIFICATION_ID);
                notificationManager.notify(MIGRATE_SUCCESS_NOTIFICATION_ID, notification);
            } catch (Exception e) {
                Log.e(TAG, "Migrate notify error:" + e);
            }
        }
    }

    /**一加数据迁移成功，首次进入时钟首页，显示底部提示*/
    public static void showMigrateSuccessTips(View parentView) {
        if (WPlusUtils.getNeedShowMigrateTips()) {
            Log.d(TAG, "show migrate success tip");
            Context context = AlarmClockApplication.getInstance();
            COUISnackBar snackBar = COUISnackBar.make(parentView, context.getString(R.string.clock_update_notice), 0);
            snackBar.setOnAction(R.string.understood, view -> {
                Log.d(TAG, "tip click dismiss");
            });
            snackBar.show();
            WPlusUtils.setNeedShowMigrateTips(false);
        }
    }
}
