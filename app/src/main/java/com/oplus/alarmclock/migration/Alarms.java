package com.oplus.alarmclock.migration;

import android.net.Uri;

import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.utils.StringUtils;

public class Alarms {
    private static final int DB_VERSION = 1;
    public static final String OPLUS_DB_NAME = StringUtils.INSTANCE.getCLOCK_DB();
    public static final String OPLUS_TABLE_NAME = "alarms";
    public static final int ALARMTIME = 0;
    public static final int VOLUME = 0;
    public static final String BACKGROUND = "";
    public static final int WORKDAYSWITCH = 0;
    public static final int HOLIDAYSWITCH = 0;
    public static final int OWNERUSERID = 0;
    public static final int CLOSEONCENEXTTIME = 0;
    public static final int CLOSEONCEPREVIOUSTIME = 0;


    private int _id;
    private int hour;
    private int minutes;
    private int daysofweek;
    private int alarmtime;
    private int enabled;
    private int alerttype;
    private String message;
    private int snooze;
    private String alert;
    private String ringName;
    private int volume;
    private int vibrate;
    private String backGround;
    private int deleteAfterUse;

    public int get_id() {
        return _id;
    }

    public void set_id(int _id) {
        this._id = _id;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getMinutes() {
        return minutes;
    }

    public void setMinutes(int minutes) {
        this.minutes = minutes;
    }

    public int getDaysOfWeek() {
        return daysofweek;
    }

    public void setDaysOfWeek(int daysofweek) {
        this.daysofweek = daysofweek;
    }

    public int getAlarmtime() {
        return alarmtime;
    }

    public void setAlarmtime(int alarmtime) {
        this.alarmtime = alarmtime;
    }

    public int getEnabled() {
        return enabled;
    }

    public void setEnabled(int enabled) {
        this.enabled = enabled;
    }

    public int getAlerttype() {
        return alerttype;
    }

    public void setAlerttype(int alerttype) {
        this.alerttype = alerttype;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getSnooze() {
        return snooze;
    }

    public void setSnooze(int snooze) {
        this.snooze = snooze;
    }

    public String getAlert() {
        return alert;
    }

    public void setAlert(String alert) {
        this.alert = alert;
    }

    public String getRingName() {
        return ringName;
    }

    public void setRingName(String ringName) {
        this.ringName = ringName;
    }

    public int getVolume() {
        return volume;
    }

    public void setVolume(int volume) {
        this.volume = volume;
    }

    public int getVibrate() {
        return vibrate;
    }

    public void setVibrate(int vibrate) {
        this.vibrate = vibrate;
    }

    public String getBackGround() {
        return backGround;
    }

    public void setBackGround(String backGround) {
        this.backGround = backGround;
    }

    public int getDeleteAfterUse() {
        return deleteAfterUse;
    }

    public void setDeleteAfterUse(int deleteAfterUse) {
        this.deleteAfterUse = deleteAfterUse;
    }

    public int getWorkdaySwitch() {
        return workdaySwitch;
    }

    public void setWorkdaySwitch(int workdaySwitch) {
        this.workdaySwitch = workdaySwitch;
    }

    public int getHolidaySwitch() {
        return holidaySwitch;
    }

    public void setHolidaySwitch(int holidaySwitch) {
        this.holidaySwitch = holidaySwitch;
    }

    public int getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(int ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public int getCloseOnceNextTime() {
        return closeOnceNextTime;
    }

    public void setCloseOnceNextTime(int closeOnceNextTime) {
        this.closeOnceNextTime = closeOnceNextTime;
    }

    public int getCloseOncePreviousTime() {
        return closeOncePreviousTime;
    }

    public void setCloseOncePreviousTime(int closeOncePreviousTime) {
        this.closeOncePreviousTime = closeOncePreviousTime;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    private int workdaySwitch;
    private int holidaySwitch;
    private int ownerUserId;
    private int closeOnceNextTime;
    private int closeOncePreviousTime;
    private String uuid;
    /**
     * 自定义闹钟排除的特殊日期
     */
    private String mSpecialAlarmDays = DatePickerUtils.SPLIT;

    public String getmSpecialAlarmDays() {
        return mSpecialAlarmDays;
    }

    public void setmSpecialAlarmDays(String mSpecialAlarmDays) {
        this.mSpecialAlarmDays = mSpecialAlarmDays;
    }

    public static int getDbVersion() {
        return DB_VERSION;
    }

    public static String getOplusDbName() {
        return OPLUS_DB_NAME;
    }

    public static String getOplusTableName() {
        return OPLUS_TABLE_NAME;
    }

    public  Alarm getClockAlarm() {
        return  Alarm.build(enabled == 1,hour, minutes,
        0, 1, message,
                Uri.EMPTY, ringName, volume,
        deleteAfterUse, vibrate, workdaySwitch, holidaySwitch);
    }

    @Override
    public String toString() {
        return "Alarms{" +
                "_id=" + _id +
                ", hour=" + hour +
                ", minutes=" + minutes +
                ", daysofweek=" + daysofweek +
                ", alarmtime=" + alarmtime +
                ", enabled=" + enabled +
                ", alerttype='" + alerttype + '\'' +
                ", message='" + message + '\'' +
                ", snooze=" + snooze +
                ", alert='" + alert + '\'' +
                ", ringName='" + ringName + '\'' +
                ", volume=" + volume +
                ", vibrate=" + vibrate +
                ", backGround='" + backGround + '\'' +
                ", deleteAfterUse=" + deleteAfterUse +
                ", workdaySwitch=" + workdaySwitch +
                ", holidaySwitch=" + holidaySwitch +
                ", ownerUserId=" + ownerUserId +
                ", closeOnceNextTime=" + closeOnceNextTime +
                ", closeOncePreviousTime=" + closeOncePreviousTime +
                ", uuid='" + uuid + '\'' +
                '}';
    }

    public class Columns {
        public static final String COLUMN_ID = "_id";
        public static final String COLUMN_HOUR = "hour";
        public static final String COLUMN_MINUTES = "minutes";
        public static final String COLUMN_DAYSOFWEEK = "daysofweek";
        public static final String COLUMN_ALARMTIME = "alarmtime";
        public static final String COLUMN_ENABLED = "enabled";
        public static final String COLUMN_ALERTTYPE = "alerttype";
        public static final String COLUMN_MESSAGE = "message";
        public static final String COLUMN_SNOOZE = "snooze";
        public static final String COLUMN_ALERT = "alert";
        public static final String COLUMN_RINGNAME = "ringName";
        public static final String COLUMN_VOLUME = "volume";
        public static final String COLUMN_VIBRATE = "vibrate";
        public static final String COLUMN_BACKGROUD = "backGround";
        public static final String COLUMN_DELETEAFTERUSE = "deleteAfterUse";
        public static final String COLUMN_WORKDAYSWITCH = "workdaySwitch";
        public static final String COLUMN_HOLIDAYSWITCH = "holidaySwitch";
        public static final String COLUMN_OWNERUSERID = "ownerUserId";
        public static final String COLUMN_CLOSEONCENEXTTIME = "closeOnceNextTime";
        public static final String COLUMN_CLOSEONCEPREVIOUSTIME = "closeOncePreviousTime";
        public static final String COLUMN_UUID = "uuid";

    }
}
