package com.oplus.alarmclock.migration;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.oplus.utils.StringUtils;


public class WPlusDatabaseHelper extends SQLiteOpenHelper {

    private static final int DB_VERSION = 30;
    public static final String WPLUS_DB_NAME = StringUtils.INSTANCE.getCLOCK_W_DB();
    public static final String WPLUS_TABLE_NAME = "alarm_templates_8";

    private static final String COLUMN_ID = "_id";
    public static final String COLUMN_HOUR = "hour";
    public static final String COLUMN_MINUTES = "minutes";
    public static final String COLUMN_DAYS_OF_WEEK = "daysofweek";
    public static final String COLUMN_ENABLED = "enabled";
    public static final String COLUMN_LABEL = "label";
    public static final String COLUMN_RINGTONE = "ringtone";
    public static final String COLUMN_VIBRATE = "vibrate";
    public static final String COLUMN_DELETE_AFTER_USE = "delete_after_use";
    public static final String COLUMN_WAKEUP = "wakeup";
    public static final String COLUMN_SPECIAL_ALARM_DAYS = "special_alarm_days";


    private static String CREATE_WPLUS_TABLE;

    private static WPlusDatabaseHelper databaseHelper;

    private WPlusDatabaseHelper(Context context) {

         super(context,WPLUS_DB_NAME, null, DB_VERSION);

    }

    public static WPlusDatabaseHelper getInstance(Context context) {

        if (databaseHelper == null) {
            databaseHelper = new WPlusDatabaseHelper(context);
        }
        return databaseHelper;
    }


    @Override
    public void onCreate(SQLiteDatabase db) {
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        onCreate(db);

    }




}
