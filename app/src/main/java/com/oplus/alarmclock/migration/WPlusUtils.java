package com.oplus.alarmclock.migration;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;

import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.provider.CityDatabaseHelper;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.ClockDatabaseHelper;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.IBaseChannel;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.utils.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class WPlusUtils {

    public static final int FROM_BOOT_COMPLETE = 1;
    public static final int FROM_MAIN_PAGE = 2;
    private static final String TAG = "1plusUtils";
    private static final String KEY_ADVANCE_NOTIFICATION = "notify_15_minutes_before";
    private static final String KEY_ALARM_SNOOZE = "snooze_duration";
    private static final String KEY_AUTO_SILENCE = "auto_silence";

    private static final String DATA_MIGRATED = "data_copied";
    private static final String WPLUS_DESKCLOCK_PREFERENCES = StringUtils.INSTANCE.getW_PREFERENCES();
    private static final String SHARED_PREF_FILE = "clock_shared_preference";

    private static final String NEED_SHOW_TIPS = "need_show_tips";
    private static final String NUMBER_OF_CITIES = "number_of_cities";

    private static final String KEY_DEFAULT_RINGTONE = "alarm_settings_ringtone_pref_key";
    private static boolean sHaveSpecialAlarm = false;
    /**
     * 稍后提醒间隔
     */
    private static String mSnoozeDuration = String.valueOf(ClockConstant.SNOOZE_AFTER_MIN);

    public static void startDBMigration(int from) {
        Log.d(TAG, "startDBMigration from: " + from);
        if (ChannelManager.INSTANCE.getChannelUtils().getChannel() != IBaseChannel.CHANNEL_WPLUS) {
            Log.d(TAG, "not op channel, not migrate");
            return;
        }
        if (getDataMigrated()) {
            Log.d(TAG, "already migrated ");
            return;
        }
        if (from == FROM_BOOT_COMPLETE) {
            doDBMigration();
        } else if (from == FROM_MAIN_PAGE) {
            new Handler().postDelayed(WPlusUtils::doDBMigration, 100);
        }
    }

    private synchronized static void doDBMigration() {
        Context context = AlarmClockApplication.getInstance();
        // city data and settings data copy
        WPlusUtils.copyWplusPreferencestoOplus(context);

        // wplus_alarm.db migration data to alarms.db migration
        try {
            List<AlarmTemplates> alarmTemplates = WPlusUtils.readFromAlarmTemplates(context);
            List<Alarms> alarms = WPlusUtils.convertToAlarms(alarmTemplates);
            List<ContentValues> values = WPlusUtils.prepareAlarmsContentValues(context, alarms);

            WPlusUtils.insertToOplusDb(context, values);
            setDataMigrated(true);
//            if (sHaveSpecialAlarm) {
//                WPlusNotifyUtils.sendMigrateSuccessNotification();
//                setNeedShowMigrateTips(true);
//            }

            // to make the alarms trigger to active state.
            AlarmStateManager.fixAlarmInstancesForSpeechAssistant(AlarmClockApplication.getInstance());
        } catch (Exception tableNotFound) {
            Log.e(TAG, "doDBMigration error: " + tableNotFound.getMessage());
        }
    }

    public static void copyWplusPreferencestoOplus(Context context) {
        String path2 = "/data/user_de/0/com.coloros.alarmclock/shared_prefs" + "/com.oneplus.deskclock_preferences.xml";
        File f2 = new File(path2);
        //LONG_PRESS_HINT
        boolean fileExists = f2.exists();
        SharedPreferences sharedPreferences = Utils.getDeviceContext(context).getSharedPreferences(WPLUS_DESKCLOCK_PREFERENCES,
                Context.MODE_PRIVATE);
        if (sharedPreferences.contains(KEY_ALARM_SNOOZE) || fileExists) {
            String snoozeDuration = sharedPreferences.getString(KEY_ALARM_SNOOZE, String.valueOf(ClockConstant.SNOOZE_AFTER_MIN)); //响铃间隔
            for (int i = 0; i < AlarmRepeat.REPEAT_ALERT_INTERVAL.length; i++) {
                if (AlarmRepeat.REPEAT_ALERT_INTERVAL[i] == Integer.parseInt(snoozeDuration)) {
                    mSnoozeDuration = String.valueOf(AlarmRepeat.REPEAT_ALERT_INTERVAL[i]);
                }
            }
            Log.d(TAG, " notifyBeforeRinging:" + mSnoozeDuration);
        }
        if (sharedPreferences.contains(KEY_ADVANCE_NOTIFICATION) || fileExists) {
            boolean notifyBeforeRinging = sharedPreferences.getBoolean(KEY_ADVANCE_NOTIFICATION, false);
            Log.d(TAG, " notifyBeforeRinging:" + notifyBeforeRinging);
            AlarmUtils.setOpenNextAlarmNotices(context, notifyBeforeRinging);
        }
        if (sharedPreferences.contains(KEY_AUTO_SILENCE) || fileExists) {
            String silenceAlarm = sharedPreferences.getString(KEY_AUTO_SILENCE, "1");  //响铃时长
            String opSilenceAlarm = "1";
            if (Integer.parseInt(silenceAlarm) == -1) {
                opSilenceAlarm = "1";
            } else {
                for (int i = 0; i < AlarmRepeat.REPEAT_ALERT_LENGTH.length; i++) {
                    if (Integer.parseInt(silenceAlarm) == AlarmRepeat.REPEAT_ALERT_LENGTH[i]) {
                        opSilenceAlarm = silenceAlarm;
                    }
                }
            }
            Log.d(TAG, " silenceAlarm:" + opSilenceAlarm);
            AlarmRepeat alarmRepeat = AlarmUtils.getAlarmRepeat(context);
            alarmRepeat.setmAlarmDuration(Integer.parseInt(opSilenceAlarm));  //响铃时长
            AlarmUtils.updateRepeatInfo(context, alarmRepeat);
        }
        if (sharedPreferences.contains(NUMBER_OF_CITIES) || fileExists) {
            // update cities list in cities.db
            int size = sharedPreferences.getInt(NUMBER_OF_CITIES, -1);
            if (size > 0) {
                for (int i = 0; i < size; i++) {
                    CityDatabaseHelper.KeyInfo cityObject = new CityDatabaseHelper.KeyInfo(context, sharedPreferences, i);
                    addKeyInfo(context, cityObject);
                }
            }
        }

    }


    public static void addKeyInfo(final Context context, CityDatabaseHelper.KeyInfo city) {
        if ((context == null) || (city == null)) {
            Log.i(TAG, "addCity context or city is null");
            return;
        }
        ContentValues values = new ContentValues();
        values.put(ClockContract.City.FLAG, 1);
        values.put(ClockContract.City.SORT_ORDER, city.getPos());
        Log.i(TAG, "addCity city = " + city.getEnName() + " " + city.getPos() + " cityId= " + city.getCityId());
        try {
            ContentResolver contentResolver = context.getContentResolver();
            contentResolver.update(ClockContract.City.NEW_CITY_CONTENT_URI, values,
                    ClockContract.City.CITY_ID + "=" + city.getCityId(), null);
        } catch (Exception e) {
            Log.e(TAG, "addCity error");
        }
    }


    @SuppressLint("Range")
    public static List<AlarmTemplates> readFromAlarmTemplates(Context context) {
        List<AlarmTemplates> alarmTemplatesList = new ArrayList<>();
        ClockDatabaseHelper databaseHelper = new ClockDatabaseHelper(context);
        SQLiteDatabase db = databaseHelper.getWritableDatabase();

        Cursor cursor = db.rawQuery("select * from  " + AlarmTemplates.WPLUS_TABLE_NAME + "  ", null);
        while (cursor != null && cursor.moveToNext()) {
            AlarmTemplates alarmTemplate = new AlarmTemplates();
            // alarmTemplate.set_id(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_ID)));
            alarmTemplate.setHour(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_HOUR)));
            alarmTemplate.setMinutes(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_MINUTES)));
            alarmTemplate.setDaysOfWeek(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_DAYS_OF_WEEK)));
            alarmTemplate.setEnabled(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_ENABLED)));
            alarmTemplate.setLabel(cursor.getString(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_LABEL)));
            alarmTemplate.setRingtone(cursor.getString(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_RINGTONE)));
            alarmTemplate.setVibrate(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_VIBRATE)));
            alarmTemplate.setDelete_after_use(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_DELETE_AFTER_USE)));
            alarmTemplate.setWakeup(cursor.getInt(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_WAKEUP)));
            if (DeviceUtils.isExpVersion(context)) {
                alarmTemplate.setSpecial_alarm_days(cursor.getString(cursor.getColumnIndex(AlarmTemplates.Columns.COLUMN_SPECIAL_ALARM_DAYS)));
            } else {
                alarmTemplate.setSpecial_alarm_days(DatePickerUtils.SPLIT);
            }
            alarmTemplatesList.add(alarmTemplate);
        }

        if (cursor != null) {
            cursor.close();
        }
        db.close();
        databaseHelper.close();
        return alarmTemplatesList;

    }

    public static List<Alarms> convertToAlarms(List<AlarmTemplates> listA) {

        if (listA == null) {
            return null;
        }

        List<Alarms> alarmsList = new ArrayList<>();

        for (AlarmTemplates alarmTemplate : listA) {
            Log.d(TAG, "alarmTemplate: " + alarmTemplate.toString());

            Alarms alarm = new Alarms();
            alarm.set_id(alarmTemplate.get_id());
            alarm.setHour(alarmTemplate.getHour());
            alarm.setMinutes(alarmTemplate.getMinutes());
            alarm.setDaysOfWeek(alarmTemplate.getDaysOfWeek());
            alarm.setEnabled(alarmTemplate.getEnabled());
            alarm.setAlarmtime(Alarms.ALARMTIME);
            alarm.setMessage(alarmTemplate.getLabel());
            alarm.setAlert(alarmTemplate.getRingtone());
            if (TextUtils.isEmpty(alarmTemplate.getRingtone())) {
                alarm.setAlert(ClockConstant.ALARM_ALERT_SILENT);
            }
            alarm.setVolume(Alarms.VOLUME);
            if (alarmTemplate.getVibrate() == 1) {
                alarm.setVibrate(WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE);
            } else {
                alarm.setVibrate(WaveformEffect.EFFECT_RINGTONE_NOVIBRATE);
            }

            alarm.setDeleteAfterUse(alarmTemplate.getDelete_after_use());
            alarm.setOwnerUserId(Alarms.OWNERUSERID);
            alarm.setCloseOnceNextTime(Alarms.CLOSEONCENEXTTIME);
            alarm.setCloseOncePreviousTime(Alarms.CLOSEONCEPREVIOUSTIME);
            alarm.setAlerttype(2);
            alarm.setmSpecialAlarmDays(alarmTemplate.getSpecial_alarm_days());
            alarmsList.add(alarm);
            Log.d(TAG, "add alarm " + alarm);
        }
        Log.d(TAG, "has special alarm " + sHaveSpecialAlarm);
        return alarmsList;
    }

    /**
     * 是否是指定日期的闹钟，或者响一次的闹钟
     * 指定一天日期或者响一次的闹钟：‘#18911#’
     * 无指定日期的闹钟：'#'
     * 指定多个日期的闹钟：'#18922#18923#'
     * 工作日类型的闹钟： ‘checked#’
     */
    private static boolean isSpecialDayAlarmOrOnceAlarm(String specialDayStr) {
        if (TextUtils.isEmpty(specialDayStr)) {
            return false;
        }
        if (specialDayStr.length() <= 1) {
            return false;
        }
        int firstIndexOf = TextUtils.indexOf(specialDayStr, '#');
        if ((firstIndexOf == 0) && (firstIndexOf != TextUtils.lastIndexOf(specialDayStr, '#'))) {
            return true;
        }
        return false;
    }

    public static List<ContentValues> prepareAlarmsContentValues(Context context, List<Alarms> list) {

        List<ContentValues> values = new ArrayList<>();
        for (Alarms alarm : list) {
            ContentValues values1 = new ContentValues();
            values1.put(Alarms.Columns.COLUMN_ID, alarm.get_id());
            values1.put(Alarms.Columns.COLUMN_HOUR, alarm.getHour());
            values1.put(Alarms.Columns.COLUMN_MINUTES, alarm.getMinutes());
            values1.put(Alarms.Columns.COLUMN_DAYSOFWEEK, alarm.getDaysOfWeek());
            values1.put(Alarms.Columns.COLUMN_ENABLED, alarm.getEnabled());
            values1.put(Alarms.Columns.COLUMN_MESSAGE, alarm.getMessage());
            values1.put(Alarms.Columns.COLUMN_ALERT, alarm.getAlert());
            values1.put(Alarms.Columns.COLUMN_VIBRATE, alarm.getVibrate());
            values1.put(Alarms.Columns.COLUMN_DELETEAFTERUSE, alarm.getDeleteAfterUse());
            values1.put(Alarms.Columns.COLUMN_ALERTTYPE, alarm.getAlerttype());
            values1.put(ClockContract.Alarm.WORKDAY_SWITCH, ClockConstant.ALARM_WORKDAY_SWITCH_OFF);
            values1.put(ClockContract.Alarm.HOLIDAY_SWITCH, ClockConstant.ALARM_HOLIDAY_SWITCH_OFF);
            values1.put(ClockContract.Alarm.SNOOZE_TIME, mSnoozeDuration);
            values1.put(ClockContract.Alarm.WORKDAY_TYPE, -1);
            values1.put(ClockContract.Alarm.WORKDAY_UPDATE_TIME, System.currentTimeMillis());
            values1.put(ClockContract.Alarm.SPECIAL_ALARM_DAYS, alarm.getmSpecialAlarmDays());
            values1.put(ClockContract.Alarm.RING_NUMBER, ClockConstant.SNOOZE_RING_NUM);
            values.add(values1);
        }

        return values;

    }

    public static void insertToOplusDb(Context context, List<ContentValues> list) {
        Uri uri = null;
        for (int i = 0; i < list.size(); i++) {
            uri = AlarmUtils.alarmResolverInsert(context.getContentResolver(), ClockContract.ALARM_CONTENT_URI, list.get(i));
            Log.i(TAG, "Inserted Uri op to oplus " + uri);
        }

        //1plus DataMigration changes for restriction on db mutliple copying
        if (uri != null) {
            setDataMigrated(true);
            Log.d(TAG, "onReceive: data copied  ");

            // delete rows once data migration is completed.
            ClockDatabaseHelper databaseHelper = new ClockDatabaseHelper(context);
            SQLiteDatabase db = databaseHelper.getWritableDatabase();
            db.delete(AlarmTemplates.WPLUS_TABLE_NAME, null, null);
            db.close();
            databaseHelper.close();
        }
    }

    //数据是否迁移成功
    public static boolean getDataMigrated() {
        return PrefUtils.getBoolean(AlarmClockApplication.getInstance(), SHARED_PREF_FILE, DATA_MIGRATED, false);
    }

    public static void setDataMigrated(boolean migrated) {
        PrefUtils.putBoolean(AlarmClockApplication.getInstance(), SHARED_PREF_FILE, DATA_MIGRATED, migrated);
    }

    //是否要显示迁移成功的tip，在有指定日期类型的闹钟的时候要显示
    public static void setNeedShowMigrateTips(boolean needShow) {
        PrefUtils.putBoolean(AlarmClockApplication.getInstance(), SHARED_PREF_FILE, NEED_SHOW_TIPS, needShow);
    }

    public static boolean getNeedShowMigrateTips() {
        return PrefUtils.getBoolean(AlarmClockApplication.getInstance(), SHARED_PREF_FILE, NEED_SHOW_TIPS, false);
    }
}
