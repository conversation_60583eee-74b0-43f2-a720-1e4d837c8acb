package com.oplus.alarmclock.migration;

public class AlarmTemplates {

    public static final String WPLUS_TABLE_NAME = "alarm_templates_8";

    private int _id;
    private int hour;
    private int minutes;
    private int daysofweek;
    private int enabled;
    private String label;
    private String ringtone;
    private int vibrate;
    private int delete_after_use;
    private int wakeup;
    private String special_alarm_days;

    public int get_id() {
        return _id;
    }

    public void set_id(int _id) {
        this._id = _id;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getMinutes() {
        return minutes;
    }

    public void setMinutes(int minutes) {
        this.minutes = minutes;
    }

    public int getDaysOfWeek() {
        return daysofweek;
    }

    public void setDaysOfWeek(int daysofweek) {
        this.daysofweek = daysofweek;
    }

    public int getEnabled() {
        return enabled;
    }

    public void setEnabled(int enabled) {
        this.enabled = enabled;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getRingtone() {
        return ringtone;
    }

    public void setRingtone(String ringtone) {
        this.ringtone = ringtone;
    }

    public int getVibrate() {
        return vibrate;
    }

    public void setVibrate(int vibrate) {
        this.vibrate = vibrate;
    }

    public int getDelete_after_use() {
        return delete_after_use;
    }

    public void setDelete_after_use(int delete_after_use) {
        this.delete_after_use = delete_after_use;
    }

    public int getWakeup() {
        return wakeup;
    }

    public void setWakeup(int wakeup) {
        this.wakeup = wakeup;
    }

    public String getSpecial_alarm_days() {
        return special_alarm_days;
    }

    public void setSpecial_alarm_days(String special_alarm_days) {
        this.special_alarm_days = special_alarm_days;
    }

    @Override
    public String toString() {
        return "AlarmTemplates{" +
                "_id=" + _id +
                ", hour=" + hour +
                ", minutes=" + minutes +
                ", daysofweek=" + daysofweek +
                ", enabled=" + enabled +
                ", label='" + label + '\'' +
                ", ringtone='" + ringtone + '\'' +
                ", vibrate=" + vibrate +
                ", delete_after_use=" + delete_after_use +
                ", wakeup=" + wakeup +
                ", special_alarm_days='" + special_alarm_days + '\'' +
                '}';
    }

    public static class Columns {
        public static final String COLUMN_ID = "_id";
        public static final String COLUMN_HOUR = "hour";
        public static final String COLUMN_MINUTES = "minutes";
        public static final String COLUMN_DAYS_OF_WEEK = "daysofweek";
        public static final String COLUMN_ENABLED = "enabled";
        public static final String COLUMN_LABEL = "label";
        public static final String COLUMN_RINGTONE = "ringtone";
        public static final String COLUMN_VIBRATE = "vibrate";
        public static final String COLUMN_DELETE_AFTER_USE = "delete_after_use";
        public static final String COLUMN_WAKEUP = "wakeup";
        public static final String COLUMN_SPECIAL_ALARM_DAYS = "special_alarm_days";
    }
}
