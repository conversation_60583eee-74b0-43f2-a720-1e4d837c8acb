/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.10.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;


public class TimerProgressView extends StopwatchView {
    private static final String TAG = "TimerProgressView";
    private static final float FILLET = 360f;
    private static final float START_ANGLE = -90;
    private int mProgressColor;
    private long mLeftTimeInMills = 0;
    private long mTotalTimeInMills = 0;
    private Paint mPaint = new Paint();
    private int mLineWidth = 0;
    private RectF mOval;
    private float mCurDegree = 0.0f;
    private int mBackgroundProgressColor;

    public TimerProgressView(Context context) {
        this(context, null);
    }

    public TimerProgressView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TimerProgressView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        mBackground = null;

        mLineWidth = context.getResources().getDimensionPixelOffset(R.dimen.timer_dial_width);
        mBackgroundProgressColor = context.getResources().getColor(R.color.time_progress_bg_default);
        mProgressColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary);

        if (Utils.isAboveQ()) {
            setForceDarkAllowed(false);
        }
    }

    public void setPaint() {
        if (mPaint != null) {
            mPaint.setAntiAlias(true);
            mPaint.setStyle(Paint.Style.STROKE);
            mPaint.setStrokeWidth(mLineWidth);
            mPaint.setStrokeCap(Paint.Cap.ROUND);
        }
    }


    @Override
    protected void setBackgroundRes() {
        /*set background null*/
    }

    public void setTotalTimeInMills(long totalMills) {
        mTotalTimeInMills = totalMills;
    }


    //Set progress background
    public void setProgressColorWithStatus(boolean start, boolean pause) {
        Log.d(TAG, "setProgressColorWithStatus:" + start);

        Context context = getContext();
        if (start) {
            mProgressColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary);
        }
        if (pause) {
            mProgressColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorSecondary);
        }

        postInvalidate();
    }


    @Override
    public void updateTimeProgress(long mills) {
        if (mTotalTimeInMills == 0) {
            Log.e(TAG, "Total time NOT set!");
            return;
        }
        mLeftTimeInMills = mills;
        if (mills <= TimerController.INTERVAL_PERIOD) {
            mLeftTimeInMills = 0;
        }

        mCurDegree = mLeftTimeInMills * FILLET / mTotalTimeInMills;
        if (mCurDegree < 0f) {
            mCurDegree = 0f;
        }
        Log.d(TAG, "updateTimeProgress");
    }


    @Override
    protected void onDraw(Canvas canvas) {
        Log.d(TAG, "onDraw ");
        linearMode(canvas);
    }

    public void linearMode(Canvas canvas) {
        int availableWidth = getRight() - getLeft();
        int availableHeight = getBottom() - getTop();
        if (mOval == null) {
            mOval = new RectF(0 + (mLineWidth / 2), 0 + (mLineWidth / 2),
                    availableWidth - (mLineWidth / 2), availableHeight - (mLineWidth / 2));
        }
        mPaint.setColor(mBackgroundProgressColor);
        canvas.drawArc(mOval, START_ANGLE, FILLET, false, mPaint);
        canvas.save();
        mPaint.setColor(mProgressColor);
        canvas.drawArc(mOval, START_ANGLE, mCurDegree, false, mPaint);
        canvas.save();
    }
}
