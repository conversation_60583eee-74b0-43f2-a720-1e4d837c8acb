/*********************************************************************************
 ** Copyright (C), 2008-2030, Oplus, All rights reserved.
 **
 ** File: - AlarmCloseModelPickLayout.kt
 ** Description:
 **    AlarmCloseModelPickLayout.
 **
 ** Version: 1.0
 ** Date: 2022-06-30
 ** Author: RongWenYang.Clock
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** RongWenYang    2022-06-30   1.0    Create this module
 ********************************************************************************/

package com.oplus.alarmclock.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R

class AlarmCloseModelPickLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val rootLayout =
        LayoutInflater.from(context).inflate(R.layout.setting_alert_close_pick_layout, this)
    private val title: TextView? = rootLayout.findViewById(R.id.pick_title)
    private val img: ImageView? = rootLayout.findViewById(R.id.pick_img)
    private val btn: RadioButton? = rootLayout.findViewById(R.id.pick_btn)
    private val checkedTextColor =
        COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimaryText)
    private val unCheckedTextColor =
        ContextCompat.getColor(context, R.color.setting_unselect_desc_text_color)

    private var imgId: Int = -1

    fun setTitle(id: Int) {
        title?.setText(id)
    }

    fun setImage(id: Int) {
        imgId = id
        img?.setImageResource(id)
    }

    fun setCheckChange(change: Boolean) {
        btn?.isChecked = change
        if (change) {
            title?.setTextColor(checkedTextColor)
        } else {
            title?.setTextColor(unCheckedTextColor)
        }
    }

    /**
     * 平板配置
     */
    fun setFlatConfig() {
        img?.let {
            val lat: MarginLayoutParams = it.layoutParams as MarginLayoutParams
            lat.leftMargin = context.resources.getDimensionPixelSize(R.dimen.layout_dp_5)
            lat.rightMargin = context.resources.getDimensionPixelSize(R.dimen.layout_dp_5)
            lat.height =
                context.resources.getDimensionPixelSize(R.dimen.setting_position_img_height_flat)
        }
        title?.let {
            val lat: MarginLayoutParams = it.layoutParams as MarginLayoutParams
            lat.topMargin = context.resources.getDimensionPixelSize(R.dimen.layout_dp_24)
        }
    }
}