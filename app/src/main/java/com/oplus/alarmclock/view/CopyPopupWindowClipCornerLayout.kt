/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CopyPopupWindowClipCornerLayout.kt
 ** Description: 长按版本复制布局ViewGroup
 ** Version: 1.0
 ** Date : 2022/5/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  dengqian  2022/5/19     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.view

import android.content.Context
import android.graphics.Outline
import android.util.AttributeSet
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.LinearLayout
import com.oplus.alarmclock.R

class CopyPopupWindowClipCornerLayout : LinearLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        val radius = context.resources.getDimensionPixelOffset(R.dimen.layout_dp_12)
        clipToOutline = true
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(0, 0, measuredWidth, measuredHeight, radius.toFloat())
            }
        }
    }
}