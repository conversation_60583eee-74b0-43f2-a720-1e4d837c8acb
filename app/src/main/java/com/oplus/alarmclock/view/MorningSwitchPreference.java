/*****************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MorningSwitchPreference.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/10/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>              <data>    <version >       <desc>
 **  <EMAIL>    2019/10/14    1.0     build this module
 ****************************************************************/
package com.oplus.alarmclock.view;

import static com.oplus.alarmclock.Morning.tools.MorningAlarmClock.getSummaryContent;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.AttributeSet;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.coui.appcompat.couiswitch.COUISwitch;
import com.coui.appcompat.preference.COUIPreference;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.PrefUtils;

import androidx.preference.Preference;
import androidx.preference.PreferenceViewHolder;

public class MorningSwitchPreference extends COUIPreference {
    private static final String TAG = "PreferenceWithTip";
    public static final int INTERVAL = 30;
    private int mImgIcon;
    private ImageView mSwitchIcon;
    private Context mContext;
    private SwitchStatusListener mSwitchStatus;
    private COUISwitch mSwitch;
    private TextView mSummary;

    private MorningSwitchPreference(Context context) {
        this(context, null);
    }

    public MorningSwitchPreference(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MorningSwitchPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
    }


    public interface SwitchStatusListener {
        public void onCheckedChanged(Boolean status);
    }

    public void setSwitchStatusListener(SwitchStatusListener statusListener) {
        this.mSwitchStatus = statusListener;
    }

    public void setChecked(boolean states) {
        if (mSwitch != null) {
            mSwitch.setChecked(states);
        }
    }

    public void setSwitchNoClicks() {

        if (mSwitch != null) {
            mSwitch.setChecked(false);
        }

    }

    @Override
    public void onBindViewHolder(PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);
        mSwitch = holder.itemView.findViewById(android.R.id.switch_widget);
        mSummary = holder.itemView.findViewById(R.id.summary);
        mSummary.setText(getSummaryContent(mContext));

        holder.itemView.setSoundEffectsEnabled(false);
        holder.itemView.setOnClickListener(view -> {
            if (mSwitch != null) {

                mSwitch.setTactileFeedbackEnabled(true);
                mSwitch.setShouldPlaySound(true);
                holder.itemView.setSoundEffectsEnabled(false);
                mSwitchStatus.onCheckedChanged(!mSwitch.isChecked());
            }
        });
        if (mSwitch != null) {
            boolean morningState = PlayMorningTools.isMorningReportEnable(mContext);
            mSwitch.setChecked(morningState);
            mSwitch.setTactileFeedbackEnabled(true);
            mSwitch.setShouldPlaySound(false);
            mSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                    if (mSwitchStatus != null) {
                    }
                }
            });
        }
    }

}
