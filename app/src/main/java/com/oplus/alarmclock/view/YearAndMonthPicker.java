package com.oplus.alarmclock.view;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.format.DateUtils;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.picker.COUIDatePicker;
import com.coui.appcompat.picker.COUINumberPicker;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.datepick.AlarmDatePicker;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.clock.common.utils.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Formatter;
import java.util.List;
import java.util.Locale;

public class YearAndMonthPicker extends FrameLayout {

    private static final String TAG = "YearAndMonthPicker";
    /**
     * The index which is ignorable.
     */
    public static final int IGNORED_YEAR = COUINumberPicker.SELECTOR_INDEX_IGNORE;

    private static final String DATE_FORMAT = "MM/dd/yyyy";
    private static final int LONGPRESS_UPDATE_INTERVAL = 100;
    private static final int MONTH_LONGPRESS_UPDATE_INTERVAL = 200;
    private static final boolean DEFAULT_ENABLED_STATE = true;
    private static final int A_LEAP_YEAR = 2020;

    public static final int ONE_HUNDRED_YEARS = 100;
    private LinearLayout mSpinners;
    private COUINumberPicker mMonthSpinner;
    private COUINumberPicker mYearSpinner;
    private final java.text.DateFormat mDateFormat = new SimpleDateFormat(DATE_FORMAT);

    private int mLeftPickerPosition = -1;
    private int mRightPickerPosition = -1;
    private Context mContext;
    private Locale mCurrentLocale;
    private OnDateChangedListener mOnDateChangedListener;
    private String[] mShortMonths;
    private int mNumberOfMonths;
    private IncompleteDate mTempDate;
    private Calendar mMinDate;
    private Calendar mMaxDate;
    private IncompleteDate mCurrentDate;
    private boolean mIsEnabled = DEFAULT_ENABLED_STATE;
    private Format mYearFormat;
    private Format mMonthFormat;
    private Format mDayFormat;
    private int mNormalColor = -1;
    private int mFocusColor = -1;
    private int mBackgroundRadius;
    private int mBackgroundLeft;

    /**
     * Whether to choose the year.
     */
    private boolean mYearIgnorable;
    private Calendar mEndDate;
    private int mMaxWidth;


    public YearAndMonthPicker(Context context) {
        this(context, null);
    }

    public YearAndMonthPicker(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public YearAndMonthPicker(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setForceDarkAllow(this, false);
        mContext = context;
        // initialization based on locale
        setCurrentLocale(Locale.getDefault());

        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        inflater.inflate(R.layout.view_year_month_picker, this, true);

        COUINumberPicker.OnValueChangeListener onChangeListener = new COUINumberPicker.OnValueChangeListener() {
            public void onValueChange(COUINumberPicker picker, int oldVal, int newVal) {
                mTempDate.setWith(mCurrentDate);
                // take care of wrapping of days and months to update greater fields
                if (picker == mMonthSpinner) {
                    mTempDate.set(Calendar.MONTH, newVal);
                } else if (picker == mYearSpinner) {
                    mTempDate.set(Calendar.YEAR, newVal);
                } else {
                    throw new IllegalArgumentException();
                }
                // now set the date to the adjusted one
                setDate(mTempDate);
                updateSpinners();
                updateCalendarView();
                notifyDateChanged();
            }
        };

        COUINumberPicker.OnScrollingStopListener onScrollingStopListener = new COUINumberPicker.OnScrollingStopListener() {
            @Override
            public void onScrollingStop() {
                sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_SELECTED);
            }
        };


        mSpinners = findViewById(R.id.pickers);

        mYearFormat = new Format(R.string.coui_year, "YEAR");
        mMonthFormat = new Format(R.string.coui_month, "MONTH");
        mDayFormat = new Format(R.string.coui_day, "DAY");
        mEndDate = Calendar.getInstance();

        // month
        mMonthSpinner = findViewById(R.id.month);
        mMonthSpinner.setMinValue(0);
        mMonthSpinner.setMaxValue(mNumberOfMonths - 1);
        mMonthSpinner.setOnLongPressUpdateInterval(MONTH_LONGPRESS_UPDATE_INTERVAL);
        mMonthSpinner.setOnValueChangedListener(onChangeListener);
        mMonthSpinner.setOnScrollingStopListener(onScrollingStopListener);

        // year
        mYearSpinner = findViewById(R.id.year);
        mYearSpinner.setOnLongPressUpdateInterval(LONGPRESS_UPDATE_INTERVAL);
        mYearSpinner.setOnValueChangedListener(onChangeListener);
        mYearSpinner.setOnScrollingStopListener(onScrollingStopListener);
        mYearSpinner.setIgnorable(mYearIgnorable);
        updateSpinnerColor();

        Calendar nowTime = Calendar.getInstance();

        //设置最小日期
        mTempDate.clear();
        mTempDate.set(nowTime.get(Calendar.YEAR), 0, 1);
//        setMinDate(mTempDate.mDate.getTimeInMillis());
        // set the max date giving priority of the maxDate over endYear
        //设置最大日期
        mTempDate.clear();
        int lastMonth = 11;
        int lastDay = 31;
        mTempDate.set(nowTime.get(Calendar.YEAR) + ONE_HUNDRED_YEARS, lastMonth, lastDay);
        setMaxDate(mTempDate.mDate.getTimeInMillis());

        mCurrentDate.setTimeInMillis(System.currentTimeMillis());
        init(mCurrentDate.get(Calendar.YEAR), mCurrentDate.get(Calendar.MONTH), mCurrentDate
                .get(Calendar.DAY_OF_MONTH), null);

        // re-order the number spinners to match the current date format
        reorderSpinners();

        if (mYearSpinner.isAccessibilityEnable()) {
            String talkbackTip = context.getResources().getString(R.string.picker_talkback_tip);
            mYearSpinner.addTalkbackSuffix(talkbackTip);
            mMonthSpinner.addTalkbackSuffix(talkbackTip);
        }

        mBackgroundRadius = context.getResources().getDimensionPixelOffset(R.dimen.coui_selected_background_radius);
        mBackgroundLeft = context.getResources().getDimensionPixelOffset(R.dimen.coui_selected_background_horizontal_padding);

        if (getImportantForAccessibility() == IMPORTANT_FOR_ACCESSIBILITY_AUTO) {
            setImportantForAccessibility(IMPORTANT_FOR_ACCESSIBILITY_YES);
        }


    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        Paint paint = new Paint();
        int color = mYearSpinner.getBackgroundColor();
        paint.setColor(color);
        canvas.drawRoundRect(mBackgroundLeft, getHeight() / 2f - mBackgroundRadius, getWidth() - mBackgroundLeft,
                getHeight() / 2f + mBackgroundRadius, mBackgroundRadius, mBackgroundRadius, paint);
        super.dispatchDraw(canvas);
    }

    // Override so we are in complete control of save / restore for this widget.
    @Override
    protected void dispatchRestoreInstanceState(SparseArray<Parcelable> container) {
        dispatchThawSelfOnly(container);
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        Parcelable superState = super.onSaveInstanceState();
        return new SavedState(superState, getYear(), getMonth(), getDayOfMonth());
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        SavedState ss = (SavedState) state;
        super.onRestoreInstanceState(ss.getSuperState());
        setDate(ss.mYear, ss.mMonth, ss.mDay);
        updateSpinners();
        updateCalendarView();
    }

    @Override
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
        onPopulateAccessibilityEvent(event);
        return true;
    }

    @Override
    public boolean isEnabled() {
        return mIsEnabled;
    }

    @Override
    public void setEnabled(boolean enabled) {
        if (mIsEnabled == enabled) {
            return;
        }
        super.setEnabled(enabled);
        mMonthSpinner.setEnabled(enabled);
        mYearSpinner.setEnabled(enabled);
        // mCalendarView.setEnabled(enabled);
        mIsEnabled = enabled;
    }

    @Override
    public void onPopulateAccessibilityEvent(AccessibilityEvent event) {
        super.onPopulateAccessibilityEvent(event);

        String selectedDateUtterance = formatDate();
        event.getText().add(selectedDateUtterance);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        setCurrentLocale(newConfig.locale);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int width = MeasureSpec.getSize(widthMeasureSpec);
        if ((mMaxWidth > 0) && (width > mMaxWidth)) {
            width = mMaxWidth;
        }
        int maxWidthMeasureSpec = MeasureSpec.makeMeasureSpec(width, widthMode);
        mMonthSpinner.clearNumberPickerPadding();
        mYearSpinner.clearNumberPickerPadding();
        measureChildConstrained(mMonthSpinner, widthMeasureSpec, heightMeasureSpec);
        measureChildConstrained(mYearSpinner, widthMeasureSpec, heightMeasureSpec);
        int padding = (width - mMonthSpinner.getMeasuredWidth() - mYearSpinner.getMeasuredWidth()) / 2;
        if (mSpinners.getChildAt(mLeftPickerPosition) instanceof COUINumberPicker) {
            ((COUINumberPicker) mSpinners.getChildAt(mLeftPickerPosition)).setNumberPickerPaddingLeft(padding);
        }
        if (mSpinners.getChildAt(mRightPickerPosition) instanceof COUINumberPicker) {
            ((COUINumberPicker) mSpinners.getChildAt(mRightPickerPosition)).setNumberPickerPaddingRight(padding);
        }
        super.onMeasure(maxWidthMeasureSpec, heightMeasureSpec);
    }

    private String formatDate() {
        if (!mCurrentDate.mIsIncomplete) {
            final int flags = DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_SHOW_YEAR;
            return DateUtils.formatDateTime(mContext, mCurrentDate.mDate.getTimeInMillis(), flags);
        } else {
            final int flags = DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_NO_YEAR;
            return DateUtils.formatDateTime(mContext, mCurrentDate.mDate.getTimeInMillis(), flags);
        }
    }


    private void measureChildConstrained(View child, int parentWidthSpec, int parentHeightSpec) {
        final MarginLayoutParams lp = (MarginLayoutParams) child.getLayoutParams();

        int childWidthSpec = getChildMeasureSpec(parentWidthSpec,
                getPaddingLeft() + getPaddingRight() + lp.leftMargin + lp.rightMargin, lp.width);
        int childHeightSpec = getChildMeasureSpec(parentHeightSpec,
                getPaddingTop() + getPaddingBottom() + lp.topMargin + lp.bottomMargin, lp.height);

        child.measure(childWidthSpec, childHeightSpec);
    }

    private void reorderSpinners() {
        String pattern = DateFormat.getBestDateTimePattern(Locale.getDefault(), "yyyyMMMMdd");
        if (isLayoutRtl()) {
            pattern = TextUtils.getReverse(pattern, 0, pattern.length()).toString();
        }
        mSpinners.removeAllViews();
        List<String> spinnerOrder = new ArrayList<String>();
        for (int i = 0; i < pattern.length(); i++) {
            switch (pattern.charAt(i)) {
                case 'd':
                    break;
                case 'M':
                    if (mMonthSpinner.getParent() == null) {
                        mSpinners.addView(mMonthSpinner);
                        spinnerOrder.add("M");
                    }
                    break;
                case 'y':
                    if (mYearSpinner.getParent() == null) {
                        mSpinners.addView(mYearSpinner);
                        spinnerOrder.add("y");
                    }
                    break;
                default:
            }

            if (mLeftPickerPosition == -1) {
                mLeftPickerPosition = mSpinners.getChildCount() - 1;
            }
            mRightPickerPosition = mSpinners.getChildCount() - 1;
        }
    }


    public boolean isLayoutRtl() {
        return (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL);
    }

    /**
     * Initialize the {@code COUIDatePicker}.
     *
     * @param year                  The year passed in.
     * @param monthOfYear           The month passed in.
     * @param dayOfMonth            The day of month passed in.
     * @param onDateChangedListener The listener passed in.
     */
    public void init(int year, int monthOfYear, int dayOfMonth, OnDateChangedListener onDateChangedListener) {
        setDate(year, monthOfYear, dayOfMonth);
        updateSpinners();
        updateCalendarView();
        mOnDateChangedListener = onDateChangedListener;
    }

    public void setDate(int year, int month) {
        setDate(year, month, 0);
        updateSpinners();
        updateCalendarView();
    }

    /**
     * Set the max date.
     *
     * @param maxDate The max date passed in.
     */
    public void setMaxDate(long maxDate) {
        mTempDate.setTimeInMillis(maxDate);
        if ((mTempDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))
                && (mTempDate.get(Calendar.DAY_OF_YEAR) != mMaxDate.get(Calendar.DAY_OF_YEAR))) {
            return;
        }
        mMaxDate.setTimeInMillis(maxDate);
        // mCalendarView.setMaxDate(maxDate);
        if (mCurrentDate.after(mMaxDate)) {
            mCurrentDate.setTimeInMillis(mMaxDate.getTimeInMillis());
            updateCalendarView();
        }
        updateSpinners();
    }


    /**
     * Set the min date.
     *
     * @param minDate The min date passed in.
     */
    public void setMinDate(long minDate) {
        mTempDate.setTimeInMillis(minDate);
        mMinDate.setTimeInMillis(minDate);
        // mCalendarView.setMinDate(minDate);
        if (mCurrentDate.before(mMinDate)) {
            mCurrentDate.setTimeInMillis(mMinDate.getTimeInMillis());
            updateCalendarView();
        }
        updateSpinners();
    }


    /**
     * @param outDate This is a temporary value and is only used to pass values.
     */
    private boolean parseDate(String date, Calendar outDate) {
        try {
            outDate.setTime(mDateFormat.parse(date));
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * Set whether the spinner is shown.
     *
     * @param shown True if the spinner is shown, false otherwise.
     */
    public void setSpinnersShown(boolean shown) {
        mSpinners.setVisibility(shown ? VISIBLE : GONE);
    }

    /**
     * Notifies the listener, if such, for a change in the selected date.
     */
    private void notifyDateChanged() {
        if (mOnDateChangedListener != null) {
            mOnDateChangedListener.onDateChanged(this, getYear(), getMonth(), getDayOfMonth());
        }
    }

    /**
     * Updates the calendar view with the current date.
     */
    private void updateCalendarView() {
//         mCalendarView.setDate(mCurrentDate.getTimeInMillis(), false, false);
    }


    private void updateSpinnerColor() {
        if (mNormalColor != -1) {
            mMonthSpinner.setPickerNormalColor(mNormalColor);
            mYearSpinner.setPickerNormalColor(mNormalColor);
        }

        if (mFocusColor != -1) {
            mMonthSpinner.setPickerFocusColor(mFocusColor);
            mYearSpinner.setPickerFocusColor(mFocusColor);
        }
    }

    private void updateSpinners() {
        mMonthSpinner.setFormatter(mMonthFormat);
        if ((mCurrentDate.get(Calendar.YEAR) == mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.YEAR) != mMaxDate.get(Calendar.YEAR))) {
            mMonthSpinner.setMinValue(mMinDate.get(Calendar.MONTH));
            mMonthSpinner.setMaxValue(mMinDate.getActualMaximum(Calendar.MONTH));
            mMonthSpinner.setWrapSelectorWheel(mMinDate.get(Calendar.MONTH) == 0);
        } else if ((mCurrentDate.get(Calendar.YEAR) != mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))) {
            mMonthSpinner.setMinValue(0);
            mMonthSpinner.setMaxValue(mMaxDate.get(Calendar.MONTH));
            mMonthSpinner
                    .setWrapSelectorWheel(mMaxDate.get(Calendar.MONTH) == mMaxDate.getActualMaximum(Calendar.MONTH));
        } else if ((mCurrentDate.get(Calendar.YEAR) == mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))) {
            mMonthSpinner.setMinValue(mMinDate.get(Calendar.MONTH));
            mMonthSpinner.setMaxValue(mMaxDate.get(Calendar.MONTH));
            mMonthSpinner.setWrapSelectorWheel((mMaxDate.get(Calendar.MONTH) == mMaxDate.getActualMaximum(Calendar.MONTH))
                    && (mMinDate.get(Calendar.MONTH) == 0));
        } else {
            mMonthSpinner.setMinValue(mCurrentDate.getActualMinimum(Calendar.MONTH));
            mMonthSpinner.setMaxValue(mCurrentDate.getActualMaximum(Calendar.MONTH));
            mMonthSpinner.setWrapSelectorWheel(true);
        }


        if ((mCurrentDate.get(Calendar.YEAR) == mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMinDate.get(Calendar.MONTH))
                && !((mCurrentDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMaxDate.get(Calendar.MONTH)))) {
        } else if (!((mCurrentDate.get(Calendar.YEAR) == mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMinDate.get(Calendar.MONTH)))
                && (mCurrentDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMaxDate.get(Calendar.MONTH))) {
        } else if ((mCurrentDate.get(Calendar.YEAR) == mMinDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMinDate.get(Calendar.MONTH))
                && (mCurrentDate.get(Calendar.YEAR) == mMaxDate.get(Calendar.YEAR))
                && (mCurrentDate.get(Calendar.MONTH) == mMaxDate.get(Calendar.MONTH))) {
        } else {
        }

        // make sure the month names are a zero based array
        // with the months in the month spinner
        String[] displayedValues = Arrays.copyOfRange(mShortMonths, mMonthSpinner.getMinValue(),
                mMonthSpinner.getMaxValue() + 1);
        //mMonthSpinner.setDisplayedValues(displayedValues);

        // year spinner range does not change based on the current date
        mYearSpinner.setMinValue(mMinDate.get(Calendar.YEAR));
        mYearSpinner.setMaxValue(mMaxDate.get(Calendar.YEAR));
        mYearSpinner.setWrapSelectorWheel(true);
        mYearSpinner.setFormatter(mYearFormat);
        // set the spinner values
        mYearSpinner.setValue(mCurrentDate.get(Calendar.YEAR));
        mMonthSpinner.setValue(mCurrentDate.get(Calendar.MONTH));
        int dayInvalidate = 27;//the day more than 27 should invalidate
    }

    private void setDate(int year, int month, int dayOfMonth) {
        mCurrentDate.set(year, month, dayOfMonth);
        clampDate();
    }


    private void setDate(IncompleteDate date) {
        mCurrentDate.setWith(date);
        clampDate();
    }

    /**
     * Ensure date is within the interval.
     */
    private void clampDate() {
        mCurrentDate.clampDate(mMinDate, mMaxDate);
    }


    /**
     * Get the selected year.
     *
     * @return The selected year.
     */
    public int getYear() {
        return mCurrentDate.get(Calendar.YEAR);
    }

    /**
     * Get the selected month.
     *
     * @return The selected month.
     */
    public int getMonth() {
        return mCurrentDate.get(Calendar.MONTH);
    }

    /**
     * Get the selected day of month.
     *
     * @return The selected day of month.
     */
    public int getDayOfMonth() {
        return mCurrentDate.get(Calendar.DAY_OF_MONTH);
    }


    private void setCurrentLocale(Locale locale) {
        if (locale.equals(mCurrentLocale)) {
            return;
        }
        mCurrentLocale = locale;
        mTempDate = getCalendarForLocale(mTempDate, locale);
        mMinDate = getCalendarForLocale(mMinDate, locale);
        mMaxDate = getCalendarForLocale(mMaxDate, locale);
        mCurrentDate = getCalendarForLocale(mCurrentDate, locale);
        mNumberOfMonths = mTempDate.getActualMaximum(Calendar.MONTH) + 1;
        mShortMonths = new String[mNumberOfMonths];
    }

    private IncompleteDate getCalendarForLocale(IncompleteDate oldCalendar, Locale locale) {
        if (oldCalendar == null) {
            return new IncompleteDate(locale);
        } else {
            IncompleteDate newCalendar = new IncompleteDate(locale);
            if (!oldCalendar.mIsIncomplete) {
                final long currentTimeMillis = oldCalendar.getTimeInMillis();
                newCalendar.setTimeInMillis(currentTimeMillis);
            } else {
                newCalendar.setWith(oldCalendar);
            }
            return newCalendar;
        }
    }

    private Calendar getCalendarForLocale(Calendar oldCalendar, Locale locale) {
        if (oldCalendar == null) {
            return Calendar.getInstance(locale);
        } else {
            final long currentTimeMillis = oldCalendar.getTimeInMillis();
            Calendar newCalendar = Calendar.getInstance(locale);
            newCalendar.setTimeInMillis(currentTimeMillis);
            return newCalendar;
        }
    }


    /**
     * Sets whether or not to allow force dark to apply to this view.
     *
     * @param view  The view against which to invoke the method.
     * @param allow allow Whether or not to allow force dark.
     */
    public static void setForceDarkAllow(View view, boolean allow) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            view.setForceDarkAllowed(allow);
        }
    }


    /**
     * A date which allow dates without year.
     */
    private static class IncompleteDate {
        /**
         * complete date.
         */
        private Calendar mDate;
        /**
         * whether current year is incomplete.
         */
        private boolean mIsIncomplete;

        public IncompleteDate(Locale locale) {
            mDate = Calendar.getInstance(locale);
        }

        public void setWith(IncompleteDate other) {
            mDate.setTimeInMillis(other.mDate.getTimeInMillis());
            mIsIncomplete = other.mIsIncomplete;
        }

        public long getTimeInMillis() {
            return mDate.getTimeInMillis();
        }

        public void setTimeInMillis(long millis) {
            this.mDate.setTimeInMillis(millis);
            mIsIncomplete = false;
        }

        public int get(int field) {
            if (!mIsIncomplete) {
                return mDate.get(field);
            } else {
                if (field == Calendar.DAY_OF_MONTH) {
                    return mDate.get(field);
                } else if (field == Calendar.MONTH) {
                    return mDate.get(field);
                } else if (field == Calendar.YEAR) {
                    return IGNORED_YEAR;
                }
            }
            // default: Invalid branch
            return mDate.get(field);
        }

        /**
         * Set the field and value.
         *
         * @param field Year, Month, Day
         * @param value value
         */
        public void set(int field, int value) {
            if (field == Calendar.YEAR) {
                if (value != IGNORED_YEAR) {
                    mIsIncomplete = false;

                    int oldMonth = mDate.get(Calendar.MONTH);
                    int oldDay = mDate.get(Calendar.DAY_OF_MONTH);

                    mDate.clear();
                    mDate.set(Calendar.YEAR, value);
                    mDate.set(Calendar.MONTH, oldMonth);
                    mDate.set(Calendar.DAY_OF_MONTH, clampDay(oldDay));
                } else {
                    mIsIncomplete = true;

                    int oldMonth = mDate.get(Calendar.MONTH);
                    int oldDay = mDate.get(Calendar.DAY_OF_MONTH);

                    mDate.clear();
                    mDate.set(field, A_LEAP_YEAR);
                    mDate.set(Calendar.MONTH, oldMonth);
                    mDate.set(Calendar.DAY_OF_MONTH, clampDay(oldDay));
                }
            } else if (field == Calendar.MONTH) {
                int oldYear = mDate.get(Calendar.YEAR);
                int oldDay = mDate.get(Calendar.DAY_OF_MONTH);

                mDate.clear();
                mDate.set(Calendar.YEAR, oldYear);
                mDate.set(Calendar.MONTH, value);
                mDate.set(Calendar.DAY_OF_MONTH, clampDay(oldDay));

            } else if (field == Calendar.DAY_OF_MONTH) {
                mDate.set(Calendar.DAY_OF_MONTH, clampDay(value));
            }
        }

        public void set(int year, int month, int day) {
            set(Calendar.YEAR, year);
            set(Calendar.MONTH, month);
            set(Calendar.DAY_OF_MONTH, day);
        }

        public void clear() {
            mDate.clear();
            mIsIncomplete = false;
        }

        public boolean before(Calendar date) {
            if (!mIsIncomplete) {
                return this.mDate.before(date);
            }
            return false;
        }

        public boolean after(Calendar date) {
            if (!mIsIncomplete) {
                return this.mDate.after(date);
            }
            return false;
        }

        int getActualMinimum(int field) {
            return mDate.getActualMinimum(field);
        }

        int getActualMaximum(int field) {
            return mDate.getActualMaximum(field);
        }

        void clampDate(Calendar minDate, Calendar maxDate) {
            if (!mIsIncomplete) {
                if (mDate.before(minDate)) {
                    set(Calendar.YEAR, minDate.get(Calendar.YEAR));
                    set(Calendar.MONTH, minDate.get(Calendar.MONTH));
                    set(Calendar.DAY_OF_MONTH, minDate.get(Calendar.DAY_OF_MONTH));
                } else if (mDate.after(maxDate)) {
                    set(Calendar.YEAR, maxDate.get(Calendar.YEAR));
                    set(Calendar.MONTH, maxDate.get(Calendar.MONTH));
                    set(Calendar.DAY_OF_MONTH, maxDate.get(Calendar.DAY_OF_MONTH));
                }
            }
        }

        int clampDay(int day) {
            int days = mDate.getActualMaximum(Calendar.DAY_OF_MONTH);
            if (day > days) {
                return days;
            }
            return day;
        }
    }

    /**
     * Class for managing state storing/restoring.
     */
    private static class SavedState extends BaseSavedState {

        @SuppressWarnings("all")
        // suppress unused and hiding
        public static final Creator<SavedState> CREATOR = new Creator<SavedState>() {

            public SavedState createFromParcel(Parcel in) {
                return new SavedState(in);
            }

            public SavedState[] newArray(int size) {
                return new SavedState[size];
            }
        };
        private final int mYear;
        private final int mMonth;
        private final int mDay;

        /**
         *
         */
        private SavedState(Parcelable superState, int year, int month, int day) {
            super(superState);
            mYear = year;
            mMonth = month;
            mDay = day;
        }

        /**
         * Constructor called from {@link #CREATOR}
         */
        private SavedState(Parcel in) {
            super(in);
            mYear = in.readInt();
            mMonth = in.readInt();
            mDay = in.readInt();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeInt(mYear);
            dest.writeInt(mMonth);
            dest.writeInt(mDay);
        }
    }

    class Format implements COUINumberPicker.Formatter {

        int mId;
        String mTag;

        Format(int id, String tag) {
            mId = id;
            mTag = tag;
        }

        @Override
        public String format(int arg0) {
            if (mTag.equals("MONTH")) {
                mEndDate.set(Calendar.MONTH, arg0);
                return DateUtils.formatDateTime(getContext(), mEndDate.getTimeInMillis(),
                        DateUtils.FORMAT_NO_MONTH_DAY | DateUtils.FORMAT_ABBREV_MONTH | DateUtils.FORMAT_NO_YEAR);
            }
            if (!Locale.getDefault().getLanguage().equals("zh")) {
                Formatter fmt = new Formatter(new StringBuilder(), mCurrentLocale);
                if (mTag.equals("YEAR")) {
                    fmt.format("%d", arg0);
                    return fmt.toString();
                }
                if (mTag.equals("DAY")) {
                    fmt.format("%02d", arg0);
                    return fmt.toString();
                }
            }
            return arg0 + getResources().getString(mId);
        }
    }


    /**
     * Get the listener.
     *
     * @return The listener.
     */
    public OnDateChangedListener getOnDateChangedListener() {
        return mOnDateChangedListener;
    }

    /**
     * Set the listener.
     *
     * @param onDateChangedListener The listener passed in.
     */
    public void setOnDateChangedListener(OnDateChangedListener onDateChangedListener) {
        mOnDateChangedListener = onDateChangedListener;
    }


    /**
     * The callback used to indicate the user changed the date.
     */
    public interface OnDateChangedListener {

        void onDateChanged(YearAndMonthPicker view, int year, int monthOfYear, int dayOfMonth);
    }


}
