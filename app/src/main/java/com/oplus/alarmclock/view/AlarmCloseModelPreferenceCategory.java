/*********************************************************************************
 ** Copyright (C), 2008-2030, Oplus, All rights reserved.
 **
 ** File: - AlarmCloseModelPreferenceCategory.kt
 ** Description:
 **    AlarmCloseModelPreferenceCategory.
 **
 ** Version: 1.0
 ** Date: 2022-06-30
 ** Author: RongWenYang.Clock
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** RongWenYang    2022-06-30   1.0    Create this module
 ********************************************************************************/

package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.preference.PreferenceViewHolder;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.coui.appcompat.preference.COUIPreferenceCategory;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;

import java.util.HashMap;

public class AlarmCloseModelPreferenceCategory extends COUIPreferenceCategory {

    private static final String TAG = "AlarmCloseModelPreferenceCategory";

    private AlarmCloseModelPickLayout mLeft;
    private AlarmCloseModelPickLayout mRight;
    private Context mContext;

    public AlarmCloseModelPreferenceCategory(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
    }

    @Override
    public void onBindViewHolder(PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);
        initHolder(holder);
    }

    private void initHolder(PreferenceViewHolder holder) {
        mLeft = holder.itemView.findViewById(R.id.left_pick);
        mRight = holder.itemView.findViewById(R.id.right_pick);
        mLeft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //AlarmCloseModelUtils.updateAlertCloseModel(mContext, AlarmCloseModelUtils.CLOSE_MODEL_BUTTON);
                callChangeListener(AlarmCloseModelUtils.CLOSE_MODEL_BUTTON);
                HashMap<String, String> map = new HashMap<>();
                map.put(ClockOplusCSUtils.KEY_CHOICE_CLOSE_ALARM_MODE, String.valueOf(AlarmCloseModelUtils.CLOSE_MODEL_BUTTON));
                ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_CHOICE_CLOSE_ALARM_MODE, map);
            }
        });
        mRight.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //AlarmCloseModelUtils.updateAlertCloseModel(mContext, AlarmCloseModelUtils.CLOSE_MODEL_SLIDE);
                callChangeListener(AlarmCloseModelUtils.CLOSE_MODEL_SLIDE);
                HashMap<String, String> map = new HashMap<>();
                map.put(ClockOplusCSUtils.KEY_CHOICE_CLOSE_ALARM_MODE, String.valueOf(AlarmCloseModelUtils.CLOSE_MODEL_SLIDE));
                ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_CHOICE_CLOSE_ALARM_MODE, map);
            }
        });

        initPre();
        setPositionValue(AlarmCloseModelUtils.Companion.getSInstance().getMCloseModel());
    }

    public void initPre() {
        int[] titles = new int[]{R.string.alert_close_btn, R.string.alert_close_slide};
        int[] imgs = new int[]{R.drawable.alarm_close_button, R.drawable.alarm_close_slide};
        int[] flatImages = new int[]{R.drawable.alarm_close_button_flat, R.drawable.alarm_close_slide_flat};
        boolean isFlat = FoldScreenUtils.isRealOslo();
        setLeftInfo(titles[0], isFlat ? flatImages[0] : imgs[0]);
        setRightInfo(titles[1], isFlat ? flatImages[1] : imgs[1]);
        if (isFlat) {
            mLeft.setMinimumHeight(mContext.getResources().getDimensionPixelOffset(R.dimen.setting_position_height_flat));
            mRight.setMinimumHeight(mContext.getResources().getDimensionPixelOffset(R.dimen.setting_position_height_flat));
            mLeft.setFlatConfig();
            mRight.setFlatConfig();
        } else {
            mLeft.setMinimumHeight(mContext.getResources().getDimensionPixelOffset(R.dimen.setting_position_height));
            mRight.setMinimumHeight(mContext.getResources().getDimensionPixelOffset(R.dimen.setting_position_height));
        }
    }

    private void setLeftInfo(int title, int img) {
        mLeft.setTitle(title);
        mLeft.setImage(img);
        mLeft.setCheckChange(false);
    }

    private void setRightInfo(int title, int img) {
        mRight.setTitle(title);
        mRight.setImage(img);
        mRight.setCheckChange(false);
    }

    public void setPositionValue(int position) {
        Log.d(TAG, "setPositionValue:" + position);
        if (position == AlarmCloseModelUtils.CLOSE_MODEL_BUTTON) {
            if (mLeft != null) {
                mLeft.setCheckChange(true);
            }
            if (mRight != null) {
                mRight.setCheckChange(false);
            }
        } else {
            if (mLeft != null) {
                mLeft.setCheckChange(false);
            }
            if (mRight != null) {
                mRight.setCheckChange(true);
            }
        }
    }
}
