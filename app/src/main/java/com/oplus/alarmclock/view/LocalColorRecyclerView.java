/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date : 2019-07-26
 ** Author: Yuxiaolong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.COUIRecyclerView;


public class LocalColorRecyclerView extends COUIRecyclerView {
    public LocalColorRecyclerView(@NonNull Context context) {
        super(context);
    }

    public LocalColorRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public LocalColorRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    /**
     * 去除列表顶部阴影
     * @return
     */
    @Override
    protected float getTopFadingEdgeStrength() {
        return 0;
    }

}
