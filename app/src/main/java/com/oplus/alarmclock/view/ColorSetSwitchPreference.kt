/*****************************************************************
 * Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - ColorSetSwitchPreference.java
 * Description: Disable click SwitchPreference sound
 * Version: 1.0
 * Date : 2020/05/20
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>              <data>    <version>       <desc>
 * <EMAIL>    020/05/20    1.0     build this module
</desc></version></data></author> */
package com.oplus.alarmclock.view

import android.content.Context
import android.util.AttributeSet
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUISwitchPreference

class ColorSetSwitchPreference : COUISwitchPreference {

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context) : super(context)

    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        holder.itemView.isSoundEffectsEnabled = false
    }

}
