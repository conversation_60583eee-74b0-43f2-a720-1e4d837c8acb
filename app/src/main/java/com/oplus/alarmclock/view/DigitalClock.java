/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Displays the time.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 * v1.1, 2018-8-20, <PERSON><PERSON><PERSON>, recode
 ************************************************************/

package com.oplus.alarmclock.view;

import static com.oplus.alarmclock.alarmclock.mini.AlarmMiniEvent.ALARM_LIST_DIALOG_AP_PM_PADDING;
import static com.oplus.alarmclock.alarmclock.mini.AlarmMiniEvent.ALARM_LIST_DIALOG_MINI_AP_PM_TEXT_SIZE;
import static com.oplus.alarmclock.alarmclock.mini.AlarmMiniEvent.ALARM_LIST_DIALOG_MINI_TEXT_SIZE;
import static com.oplus.alarmclock.utils.GarbAlarmUtils.TERM_SEPARATOR;
import static com.oplus.alarmclock.utils.TextWeightUtils.WEIGHT_EIGHT;
import static com.oplus.alarmclock.utils.TextWeightUtils.WEIGHT_MEDIUM;
import static com.oplus.alarmclock.utils.TextWeightUtils.WEIGHT_NINE;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.WorldClockBaseFragment;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.Utils;

import java.util.Calendar;
import java.util.Locale;

/**
 * Displays the time
 */
public class DigitalClock extends LinearLayout {

    private static final String TAG = "DigitalClock";
    private static final Float LOOP_ALARM_GRID_TIME_TEXT_SIZE = 46F;
    private static final Float LOOP_ALARM_GRID_TIME_AMPM_TEXT_SIZE = 26F;

    /**
     * 轮班闹钟列表字体大小
     */
    private static final Float LOOP_ALARM_LIST_TIME_TEXT_SIZE = 42F;
    private static final Float LOOP_ALARM_LIST_TIME_AMPM_TEXT_SIZE = 22F;

    /**
     * item弹窗字体大小
     */
    private static final Float LOOP_ALARM_DIALOG_TIME_AMPM_TEXT_SIZE = 20F;
    private static final Float LOOP_ALARM_DIALOG_TIME_TEXT_SIZE = 40F;

    private static final Float LOOP_ALARM_MINI_TIME_TEXT_SIZE = 26F;
    private static final Float LOOP_ALARM_MINI_TIME_AMPM_TEXT_SIZE = 16F;


    private TextView mAmPm;
    private TextView mTimeDisplay;

    private float mTimeTextSize;
    private float mTimeTextSize32;
    private float mAmpmTextSize;
    private Context mContext;
    private boolean mIsGridMode;
    private boolean mAmPmShowInSecondLine = false;



    public DigitalClock(Context context) {
        this(context, null);
    }

    public DigitalClock(Context context, AttributeSet attrs) {
        super(context, attrs);
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DigitalClock);
            mAmPmShowInSecondLine = typedArray.getBoolean(R.styleable.DigitalClock_amShowInSecondLine, false);
            typedArray.recycle();
        }
        mContext = context;
        initLayout(context);
    }

    private void initLayout(Context context) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(
                Context.LAYOUT_INFLATER_SERVICE);
        if (inflater != null) {
            View view = inflater.inflate(R.layout.digital_clock_layout, this);
            mTimeDisplay = (TextView) findViewById(R.id.timeDisplay);
            initAmPm(view);
            mTimeTextSize = context.getResources().getDimension(R.dimen.digital_clock_text_size);
            mTimeTextSize32 = context.getResources().getDimension(R.dimen.text_size_sp_30);
            mAmpmTextSize = context.getResources().getDimension(R.dimen.digit_clock_am_pm_text_size);
        } else {
            Log.e(TAG, "Get LayoutInflater error!");
        }
    }

    public void setIsGridMode(boolean isGridMode) {
        this.mIsGridMode = isGridMode;
    }

    public void setTextColor(int timecolor, float timealpha) {
        mTimeDisplay.setTextColor(timecolor);
        mTimeDisplay.setAlpha(timealpha);
    }

    public void setTextColor(int timecolor) {
        mTimeDisplay.setTextColor(timecolor);
    }

    public void setDisplayTimeAlpha(float alpha) {
        mTimeDisplay.setAlpha(alpha);
    }

    public void setAmPmAlpha(float alpha) {
        mAmPm.setAlpha(alpha);
    }

    public void setAmPmViewColor(int color) {
        mAmPm.setTextColor(color);
    }

    public void setTypeFace(Typeface typeFace) {
        mTimeDisplay.setTypeface(typeFace);
        mAmPm.setTypeface(typeFace);
    }

    public void updateTime(Calendar c) {
        String format = Formatter.getTimeFormatWithoutAMPM(mContext);
        CharSequence newTime = DateFormat.format(format, c);
        mTimeDisplay.setText(newTime.toString().replaceAll(TERM_SEPARATOR,"").trim());
        updateView((c.get(Calendar.AM_PM) == Calendar.AM));
    }

    public String getTime() {
        return mTimeDisplay.getText().toString();
    }

    public float getTimeviewDesiredWidth() {
        String text = "";
        CharSequence c = mTimeDisplay.getText();
        if (c != null) {
            text = c.toString();
        }
        return WorldClockBaseFragment.getDesiredWidth(text, mIsGridMode ? mTimeTextSize32 : mTimeTextSize);
    }

    public float getAmpmviewDesiredWidth() {
        String text = "";
        CharSequence c = mAmPm.getText();
        if (c != null) {
            text = c.toString();
        }
        return WorldClockBaseFragment.getDesiredWidth(text, mAmpmTextSize);
    }

    private void initAmPm(View parent) {
        Locale locale = Locale.getDefault();
        String country = locale.getCountry();
        String language = locale.getLanguage();
        mAmPmShowInSecondLine = mAmPmShowInSecondLine && ("ug".equals(language) || "uz".equals(language));
/*        if (("CN").equals(country) || ("TW").equals(country)) {
            if (mAmPmShowInSecondLine) {
                mAmPm = (TextView) parent.findViewById(R.id.am_pm);
            } else {
                mAmPm = (TextView) parent.findViewById(R.id.am_pm_chinese);
            }
        } else {
        }*/
        mAmPm = (TextView) parent.findViewById(R.id.am_pm);
//        TextWeightUtils.setTextBold(mAmPm);
        if (mAmPmShowInSecondLine) {

            post(() -> {
                getLayoutParams().height = LayoutParams.WRAP_CONTENT;
                setOrientation(VERTICAL);
                setGravity(Gravity.CENTER_HORIZONTAL);
            });

        }
    }

    private void updateView(boolean isMorning) {
        if (DateFormat.is24HourFormat(mContext)) {
            mAmPm.setVisibility(View.GONE);
            String timeStr = mTimeDisplay.getText().toString();
            if (!TextUtils.isEmpty(timeStr)) {
                String mTimeStart = "1";
                if (!timeStr.startsWith(mTimeStart)) {
                    LayoutParams layoutParams = (LayoutParams) mTimeDisplay.getLayoutParams();
                    if (layoutParams != null) {
                        int marginStart = 6;
                        layoutParams.setMarginStart(marginStart);
                    }
                }
            }
        } else {
            mAmPm.setVisibility(View.VISIBLE);
            if (isMorning) {
                mAmPm.setText(R.string.am);
            } else {
                mAmPm.setText(R.string.pm);
            }
        }
    }

    public void setTimeViewSuitableFontSize(int sizeType) {
        float fontScale = getContext().getResources().getConfiguration().fontScale;
        float defaultTextSize = mIsGridMode ? mTimeTextSize32 : mTimeTextSize;
        TextWeightUtils.setTextBold(mTimeDisplay);
        Utils.setSuitableFontSize(mTimeDisplay, defaultTextSize, fontScale, sizeType);
    }

    public void setAmPmSuitableFontSize(int sizeType) {
        float fontScale = getContext().getResources().getConfiguration().fontScale;
        float defaultTextSize = mAmpmTextSize;
        Utils.setSuitableFontSize(mAmPm, defaultTextSize, fontScale, sizeType);
    }

    /**
     * miniapp内时间字体大小
     */
    public void setViewMiniFontSize() {
        mTimeDisplay.setTextSize(TypedValue.COMPLEX_UNIT_PX, ALARM_LIST_DIALOG_MINI_TEXT_SIZE);
        mAmPm.setTextSize(TypedValue.COMPLEX_UNIT_PX, ALARM_LIST_DIALOG_MINI_AP_PM_TEXT_SIZE);
        mAmPm.setPadding(0, 0, 0, ALARM_LIST_DIALOG_AP_PM_PADDING);
        MarginLayoutParams layoutParams = (MarginLayoutParams) mAmPm.getLayoutParams();
        layoutParams.setMargins(0, 0, 0, 0);
        TextWeightUtils.setTextBold(mTimeDisplay);
        TextWeightUtils.setTextWeightCustomize(mAmPm, WEIGHT_MEDIUM);
        TextWeightUtils.setTextWeightCustomize(mTimeDisplay, WEIGHT_NINE);
    }

    /**
     * 设置轮班闹钟页面字体大小
     */
    public void setViewLoopAlarmFontSize() {
        float textSize = AlarmClockApplication.getInstance().getResources().getDimension(R.dimen.text_size_sp_14);
        float textSizeAmPm = AlarmClockApplication.getInstance().getResources().getDimension(R.dimen.text_size_sp_10);
        float fontScale = AlarmClockApplication.getInstance().getResources().getConfiguration().fontScale;
        Utils.setSuitableFontSize(mTimeDisplay, textSize, fontScale, COUIChangeTextUtil.G3);
        Utils.setSuitableFontSize(mAmPm, textSizeAmPm, fontScale, COUIChangeTextUtil.G3);
        mAmPm.setPadding(0, 0, 0, ALARM_LIST_DIALOG_AP_PM_PADDING);
        MarginLayoutParams layoutParams = (MarginLayoutParams) mAmPm.getLayoutParams();
        layoutParams.setMargins(0, 0, 0, 0);
    }

    /**
     * 轮班信息字体大小
     */
    public void setViewLoopAlarmGridFontSize() {
        mTimeDisplay.setTextSize(TypedValue.COMPLEX_UNIT_PX, LOOP_ALARM_GRID_TIME_TEXT_SIZE);
        mAmPm.setTextSize(TypedValue.COMPLEX_UNIT_PX, LOOP_ALARM_GRID_TIME_AMPM_TEXT_SIZE);
        mAmPm.setPadding(0, 0, 0, ALARM_LIST_DIALOG_AP_PM_PADDING);
        MarginLayoutParams layoutParams = (MarginLayoutParams) mAmPm.getLayoutParams();
        layoutParams.setMargins(0, 0, 0, 0);
        TextWeightUtils.setTextWeightCustomize(mTimeDisplay, WEIGHT_EIGHT);
        TextWeightUtils.setTextWeightMedium(mAmPm);
    }

    /**
     * 轮班闹钟弹窗字体
     */
    public void setViewLoopAlarmDialogFontSize() {
        mAmPm.setTypeface(null, Typeface.NORMAL);
        float textSize = AlarmClockApplication.getInstance().getResources().getDimension(R.dimen.text_size_sp_14);
        float textSizeAmPm = AlarmClockApplication.getInstance().getResources().getDimension(R.dimen.text_size_sp_14);
        int amPmMargin = AlarmClockApplication.getInstance().getResources().getDimensionPixelSize(R.dimen.layout_dp_3);
        mTimeDisplay.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        mAmPm.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizeAmPm);
        mAmPm.setPadding(0, 0, 0, 1);
        MarginLayoutParams layoutParams = (MarginLayoutParams) mAmPm.getLayoutParams();
        layoutParams.setMargins(amPmMargin, 0, amPmMargin, 0);
    }

    /**
     * miniaAPP 字体大小
     */
    public void setViewLoopAlarmMiniFontSize() {
        mTimeDisplay.setTextSize(TypedValue.COMPLEX_UNIT_PX, LOOP_ALARM_MINI_TIME_TEXT_SIZE);
        TextWeightUtils.setTextWeightCustomize(mTimeDisplay, WEIGHT_MEDIUM);
        mAmPm.setTextSize(TypedValue.COMPLEX_UNIT_PX, LOOP_ALARM_MINI_TIME_AMPM_TEXT_SIZE);
        mAmPm.setPadding(0, 0, 0, ALARM_LIST_DIALOG_AP_PM_PADDING);
        MarginLayoutParams layoutParams = (MarginLayoutParams) mAmPm.getLayoutParams();
        layoutParams.setMargins(0, 0, 0, 0);
    }

}