/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.10.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.PathInterpolator;

import androidx.core.content.ContextCompat;

import com.oplus.alarmclock.R;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.tintimageview.COUITintUtil;


public class StopwatchView extends View {

    protected Drawable mBackground;

    private static final String TAG = "StopwatchView";

    //for reset animation
    private static final long RESET_ANIMATION_DURATION = 500L;
    private static final long INVALIDATE_TIME = 30L;

    private static final int MIN_IN_MILLS = 60 * 1000;  //1 minute: 60000 ms.
    private static final float ONE_CIRCLE = 360f;
    private static final double MIN_NUMBER = 0.0000001;

    private static final PaintFlagsDrawFilter mPaintFlagsDrawFilter = new PaintFlagsDrawFilter(0,
            Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);

    private final PathInterpolator mResetAniInterpolator = new PathInterpolator(0.3f, 0f, 0.1f, 1f);
    private Drawable mTimeMark;
    private int[] mOffset;

    private boolean mChanged;
    private float mCurrentDegree = 0;

    //for reset animation
    private float mDegreeWhenAniStart;
    private long mAniStartTime;

    public StopwatchView(Context context) {
        this(context, null);
    }

    public StopwatchView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public StopwatchView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);

        mOffset = initOffset();

        mTimeMark = ContextCompat.getDrawable(context, R.drawable.ic_stopwatch_view_point);
        COUITintUtil.tintDrawable(mTimeMark, COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary));

        setBackgroundRes();
    }

    public void updateTimeProgress(long mills) {
        float degree = mCurrentDegree;
        int m = (int) (mills % MIN_IN_MILLS);
        mCurrentDegree = m * ONE_CIRCLE / MIN_IN_MILLS;
        if (Math.abs(degree - mCurrentDegree) > MIN_NUMBER) {
            postInvalidate();
        }
    }

    protected void setBackgroundRes() {
        setBackgroundResource(R.drawable.ic_stopwatch_scale);
    }

    protected void updateCurrentDegree(float degree) {
        mCurrentDegree = degree;
    }

    public void beginResetAnimation() {
        mDegreeWhenAniStart = mCurrentDegree;
        mAniStartTime = System.currentTimeMillis();
        dealAnimationAndDraw();
    }

    public void stopResetAnimation() {
        removeCallbacks(mInvalidateRunnable);
    }

    private void dealAnimationAndDraw() {
        int timeDif = (int) (System.currentTimeMillis() - mAniStartTime);
        if ((timeDif <= RESET_ANIMATION_DURATION) || (mCurrentDegree > 0f)) {
            mCurrentDegree = mDegreeWhenAniStart - mDegreeWhenAniStart
                    * mResetAniInterpolator.getInterpolation(timeDif * 1f / RESET_ANIMATION_DURATION);
            postInvalidate();
            postDelayed(mInvalidateRunnable, INVALIDATE_TIME);
        } else {
            mDegreeWhenAniStart = 0;
            mAniStartTime = 0L;

        }
    }

    private Runnable mInvalidateRunnable = new Runnable() {
        @Override
        public void run() {
            dealAnimationAndDraw();
        }
    };


    protected boolean getChanged() {
        return mChanged;
    }

    protected int[] initOffset() {
        return getResources().getIntArray(R.array.small_analog_args);
    }

    protected int[] getOffset() {
        return mOffset;
    }


    @Override
    protected void onSizeChanged(int w, int h, int oldW, int oldH) {
        super.onSizeChanged(w, h, oldW, oldH);
        mChanged = true;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.setDrawFilter(mPaintFlagsDrawFilter);
        boolean changed = mChanged;
        if (changed) {
            mChanged = false;
        }

        //Canvas width, height, centerX, centerY.
        int availableWidth = getRight() - getLeft();
        int availableHeight = getBottom() - getTop();

        int canvasCenterX = availableWidth / 2;
        int canvasCenterY = availableHeight / 2;

        //Draw time running mark.
        canvas.save();

        final float degree = mCurrentDegree;
        canvas.rotate(degree, canvasCenterX, canvasCenterY);

        final Drawable secondHand = mTimeMark;
        if (changed) {
            int w = secondHand.getIntrinsicWidth();
            int h = secondHand.getIntrinsicHeight();

            int left = canvasCenterX - (w / 2) + mOffset[1];
            int top = -(h / 2) + mOffset[0];
            int right = canvasCenterX + (w / 2) + mOffset[1];
            int bottom = (h / 2) + mOffset[0];

            secondHand.setBounds(left, top, right, bottom);

        }

        secondHand.draw(canvas);
        canvas.restore();
        //end.
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        super.onTouchEvent(event);
        return true;
    }

}
