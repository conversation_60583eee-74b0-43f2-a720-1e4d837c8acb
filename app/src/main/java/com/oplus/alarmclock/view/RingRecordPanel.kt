/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - RingRecordPanel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.oplus.alarmclock.databinding.PanelRingRecordBinding
import com.oplus.alarmclock.mvvm.ringrecord.RingRecordUtils
import com.oplus.alarmclock.view.modelview.BasePanel
import com.coloros.widget.smallweather.TimeInfoBuilder
import com.coui.appcompat.picker.COUIDatePicker
import java.util.Calendar

class RingRecordPanel(mContext: Context, mSelectTime: Long) : BasePanel(mContext),
    COUIDatePicker.OnDateChangedListener {
    private val mViewBinding = PanelRingRecordBinding.inflate(LayoutInflater.from(mContext))
    private val mTimeInfoBuilder by lazy {
        TimeInfoBuilder(
            mContext
        )
    }
    private lateinit var mListener: DataListener

    init {
        mViewBinding.datePicker.run {
            onDateChangedListener = this@RingRecordPanel
            setDateText(year, month, dayOfMonth)
            if (mSelectTime > 0) {
                Calendar.getInstance().apply {
                    timeInMillis = mSelectTime
                }.let {
                    updateDate(
                        it[Calendar.YEAR],
                        it[Calendar.MONTH],
                        it[Calendar.DAY_OF_MONTH]
                    )
                }
            }
        }
    }

    fun show(listener: DataListener) {
        mListener = listener
        showDialog(mViewBinding.root, true)
    }

    override fun confirm(view: View) {
        val time = mViewBinding.datePicker.run {
            Calendar.getInstance().apply {
                this[Calendar.YEAR] = year
                this[Calendar.MONTH] = month
                this[Calendar.DAY_OF_MONTH] = dayOfMonth
            }.timeInMillis
        }
        mListener.onResult(time)
        dismiss()
    }

    override fun cancel(view: View) {
        dismiss()
    }


    override fun getBundleWhenSaveInstanceState(): Bundle? {
        return null
    }

    override fun onDateChanged(datePicker: COUIDatePicker, year: Int, month: Int, day: Int) {
        setDateText(year, month, day)
    }

    private fun setDateText(year: Int, month: Int, day: Int) {
        val timeInfo = RingRecordUtils.getDate(year, month, day, mTimeInfoBuilder)
        mViewBinding.timeInfo = timeInfo
    }

    interface DataListener {
        fun onResult(time: Long)
    }
}