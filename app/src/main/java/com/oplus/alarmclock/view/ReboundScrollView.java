/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - ReboundScrollView.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/02/19
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2020/02/19     1.0            ReboundScrollView.java
 *  ***************************************************************
 */

package com.oplus.alarmclock.view;

import static com.oplus.alarmclock.alarmclock.AddAlarmFragment.TAB_TYPE_INDEX_1;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.view.animation.TranslateAnimation;
import android.widget.ScrollView;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.Utils;


public class ReboundScrollView extends ScrollView {


    public int mCurrentTab = 0;
    private static final String TAG = "ReboundScrollView";

    private static final float MOVE_FACTOR = 0.15f;

    private static final int ANIM_TIME = 200;

    private View mContentView;

    private float mStartY;

    private Rect mOriginalRect = new Rect();

    private boolean mCanPullDown = false;

    private boolean mCanPullUp = false;

    private boolean mIsMoved = false;

    private Rect mConflictRect;

    private OnScrollListener mListener;

    private boolean mSsConflict = false;

    public ReboundScrollView(Context context) {
        this(context, null);
    }

    public ReboundScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ReboundScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setNestedScrollingEnabled(true);
            setOverScrollMode(OVER_SCROLL_ALWAYS);
        }
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onFinishInflate() {
        if (getChildCount() > 0) {
            mContentView = getChildAt(0);
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);

        if (mContentView == null) {
            return;
        }

        mOriginalRect.set(mContentView.getLeft(), mContentView.getTop(), mContentView
                .getRight(), mContentView.getBottom());
    }

    public interface OnScrollListener {
        void onScroll(int l, int t, int oldl, int oldt);

        void stopScroll();

        boolean dispatchTouchEvent(MotionEvent event, Rect conflictRect);
    }

    public void setOnScrollListener(OnScrollListener mListener) {
        this.mListener = mListener;
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (null != mListener) {
            mListener.onScroll(l, t, oldl, oldt);
        }
    }

    /**
     * 查看 点击位置是否在view内
     *
     * @param targetView
     * @param xAxis
     * @param yAxis
     * @return
     */
    private boolean isTouchPointInView(View targetView, int xAxis, int yAxis) {
        if (targetView == null) {
            return false;
        }
        int[] location = new int[2];
        targetView.getLocationOnScreen(location);
        int left = location[0];
        int top = location[1];
        int right = left + targetView.getMeasuredWidth();
        int bottom = top + targetView.getMeasuredHeight();
        if (yAxis >= top && yAxis <= bottom && xAxis >= left && xAxis <= right) {
            return true;
        }
        return false;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (mContentView == null) {
            return super.dispatchTouchEvent(ev);
        }
        View list = findViewById(R.id.loop_alarm_cycle_list);
        if (Utils.isViewContains(ev, list) && mCurrentTab == TAB_TYPE_INDEX_1) {
            //工作日类型，滑动轮班闹钟list，取消嵌套滑动
            setNestedScrollingEnabled(false);
            return super.dispatchTouchEvent(ev);
        }
        setNestedScrollingEnabled(true);
        int action = ev.getAction();
        boolean isTouchOutOfScrollView = (ev.getY() >= this.getHeight()) || (ev.getY() <= 0);
        if (isTouchOutOfScrollView) {
            if ((action == MotionEvent.ACTION_CANCEL) || (action == MotionEvent.ACTION_UP)) {
                boundBack();
            }
        }
        switch (action) {
            case MotionEvent.ACTION_DOWN:

                mSsConflict = false;
                mCanPullDown = canPullDown();
                mCanPullUp = canPullUp();

                mStartY = ev.getY();

                if (mConflictRect == null) {
                    mConflictRect = new Rect();
                } else {
                    mConflictRect.setEmpty();
                }

                if (mListener != null && mListener.dispatchTouchEvent(ev, mConflictRect)) {
                    mSsConflict = true;
                    break;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                boundBack();
                if (mListener != null) {
                    mListener.stopScroll();
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (!mCanPullDown && !mCanPullUp) {
                    mStartY = ev.getY();
                    mCanPullDown = canPullDown();
                    mCanPullUp = canPullUp();

                    break;
                }

                float nowY = ev.getY();

                int deltaY = (int) (nowY - mStartY);

                boolean shouldMove = (mCanPullDown && (deltaY > 0)) || (mCanPullUp && (deltaY < 0))
                        || (mCanPullUp && mCanPullDown);

                if (shouldMove && !mSsConflict) {

                    int offset = (int) (deltaY * MOVE_FACTOR);

                    mContentView.layout(mOriginalRect.left, mOriginalRect.top + offset,
                            mOriginalRect.right, mOriginalRect.bottom + offset);

                    mIsMoved = true;
                }

                break;
            default:
                break;
        }

        return super.dispatchTouchEvent(ev);
    }

    private void boundBack() {
        if (!mIsMoved) {
            return;
        }

        TranslateAnimation anim = new TranslateAnimation(0, 0, mContentView.getTop(), mOriginalRect.top);
        anim.setInterpolator(new LinearInterpolator());
        anim.setDuration(ANIM_TIME);

        mContentView.startAnimation(anim);

        mContentView.layout(mOriginalRect.left, mOriginalRect.top,
                mOriginalRect.right, mOriginalRect.bottom);

        mCanPullDown = false;
        mCanPullUp = false;
        mIsMoved = false;

    }

    private boolean canPullDown() {
        return (getScrollY() == 0) || (mContentView.getHeight() < getHeight() + getScrollY());
    }

    private boolean canPullUp() {
        return mContentView.getHeight() <= (getHeight() + getScrollY());
    }

}
