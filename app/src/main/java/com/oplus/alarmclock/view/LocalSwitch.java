/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.10.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;

import com.coui.appcompat.couiswitch.COUISwitch;
import com.oplus.clock.common.utils.Log;

public class LocalSwitch extends COUISwitch {
    private static final String TAG = "LocalSwitch";

    private boolean mIgnoreLaioutFlag = false;

    public LocalSwitch(Context context) {
        super(context);
    }

    public LocalSwitch(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public LocalSwitch(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }

    public void setIgnoreLaioutFlag(boolean ignoreLaioutFlag) {
        mIgnoreLaioutFlag = ignoreLaioutFlag;
        Log.d(TAG, "setIgnoreLaioutFlag: " + mIgnoreLaioutFlag);
    }

    @Override
    public boolean isLaidOut() {
        boolean isLaidOut = !mIgnoreLaioutFlag && super.isLaidOut();
        Log.d(TAG, "isLaidOut: " + isLaidOut + ", mIgnoreLaioutFlag: " + mIgnoreLaioutFlag);
        return isLaidOut;
    }
}
