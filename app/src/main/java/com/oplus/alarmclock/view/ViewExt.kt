/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ViewExt.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view

import android.widget.TextView
import com.coloros.widget.smallweather.ClockWidgetManager
import com.coloros.widget.smallweather.TimeInfoBuilder
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.font.OplusFontManager

object ViewExt {

    private const val TAG = "ViewExt"
    private const val OFFSET = 2.5f

    /**
     * 获取冒号的string类型
     * @param useUnicode 是否使用中文冒号
     * return colon
     */
    @JvmStatic
    fun getColonStr(useUnicode: Boolean = false): String {
        return if (useUnicode && VersionUtils.isOsVersion15()) {
            runCatching {
                if (OplusFontManager.getInstance().isFlipFontUsed) {
                    TimeInfoBuilder.getColon()
                } else {
                    TimeInfoBuilder.getColonUnicode()
                }
            }.getOrDefault(TimeInfoBuilder.getColon())
        } else {
            TimeInfoBuilder.getColon()
        }
    }

    /**
     * 设置居中的冒号
     **/
    fun TextView.setColon(colon: String, offsetResId: Int, defaultResId: Int = 0) {
        if (colon == ClockWidgetManager.COLON_UNICODE) {
            runCatching {
                text = colon
                var paddingDimen = resources.getDimensionPixelSize(offsetResId)
                val textWidth = paint.measureText(colon)
                Log.d(TAG, "setColon unicode:$textWidth offset:$paddingDimen size:$textSize")
                if (paint.measureText(colon) + paddingDimen * OFFSET > 0) {
                    setPadding(paddingDimen, 0, paddingDimen, 0)
                } else if (defaultResId != 0) {
                    paddingDimen = resources.getDimensionPixelSize(defaultResId)
                    setPadding(paddingDimen, 0, paddingDimen, 0)
                }
            }.onFailure {
                text = TimeInfoBuilder.getColon()
            }
        } else {
            text = colon
        }
    }
}
