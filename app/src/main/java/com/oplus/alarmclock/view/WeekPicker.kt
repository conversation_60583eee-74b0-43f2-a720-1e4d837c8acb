/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseVBActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: Ni<PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/3/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view

import android.content.Context
import android.icu.text.DateFormatSymbols
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.alarmclock.R

/**
 * 周选择器
 */
class WeekPicker : FrameLayout {
    private var mRootView: LinearLayout? = null
    private var mWeekPickerListener: OnWeekPickerClickListener? = null

    constructor(context: Context?) : this(context, null)
    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context!!, attrs, defStyleAttr) {
        inflate(context, R.layout.week_picker_layout, this)
        mRootView = findViewById(R.id.root_view)
        initChip()
    }

    private fun initChip() {
        val array = DateFormatSymbols().getWeekdays(DateFormatSymbols.STANDALONE, DateFormatSymbols.NARROW)

        mRootView?.let { rootView ->
            for (i in 0..rootView.childCount) {
                val chipLayout = rootView.getChildAt(i) as? LinearLayout
                if ((chipLayout != null) && (chipLayout.childCount > 0)) {
                    val chip = chipLayout.getChildAt(0) as COUIChip
                    chip.apply {
                        text = array[i + 1]
                        setOnClickListener {
                            mWeekPickerListener?.onClick(it, i, isChecked)
                        }
                        COUIDarkModeUtil.setForceDarkAllow(this, false)
                    }
                }
            }
        }
    }

    fun update(checkArry: BooleanArray) {
        for (i in 0..checkArry.size) {
            val chipLayout = mRootView?.getChildAt(i) as? LinearLayout
            if ((chipLayout != null) && (chipLayout.childCount > 0)) {
                val chip = chipLayout.getChildAt(0) as COUIChip
                chip.apply {
                    isChecked = checkArry[i]
                }
            }
        }
    }

    fun setOnWeekPickerClickListener(listener: OnWeekPickerClickListener) {
        mWeekPickerListener = listener
    }

    interface OnWeekPickerClickListener {
        fun onClick(view: View, day: Int, isCheck: Boolean)
    }

    companion object {
        const val SUN = 0
        const val MON = 1
        const val TUE = 2
        const val WED = 3
        const val THU = 4
        const val FRI = 5
        const val SAT = 6
    }
}