/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - NestedScrollableHost.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/11/10
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/11/10     1.0            build this module
 ****************************************************************/

package com.oplus.alarmclock.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.constraintlayout.widget.ConstraintLayout
import kotlin.math.absoluteValue

class NestedScrollableHost : ConstraintLayout {
    companion object {
        const val SENSITIVE = 3f
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    private val touchSlop: Int = ViewConfiguration.get(context).scaledTouchSlop
    private var initialX = 0f
    private var initialY = 0f

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return true
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            handleInterceptTouchEvent(ev)
        }
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 内部拦截分发事件
     */
    private fun handleInterceptTouchEvent(e: MotionEvent) {
        if (e.action == MotionEvent.ACTION_DOWN) {
            initialX = e.x
            initialY = e.y
        } else if (e.action == MotionEvent.ACTION_MOVE) {
            val dx = e.x - initialX
            val dy = e.y - initialY
            val scaledDx = dx.absoluteValue
            val scaledDy = dy.absoluteValue
            //竖向滑动拦截事件
            if (scaledDx - scaledDy > touchSlop * SENSITIVE) {
                parent.requestDisallowInterceptTouchEvent(false)
            } else {
                parent.requestDisallowInterceptTouchEvent(true)
            }
        }
    }
}