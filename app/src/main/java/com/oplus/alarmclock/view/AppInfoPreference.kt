/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AppInfoPreference.kt
 ** Description: 设置关于Preference
 ** Version: 1.0
 ** Date : 2022/5/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  dengqian  2022/5/11     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.view

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.NinePatchDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.res.ResourcesCompat
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.poplist.COUIPopupWindow
import com.coui.appcompat.preference.COUIPreference
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ToastManager
import com.oplus.alarmclock.utils.Utils
import java.util.Locale

class AppInfoPreference : COUIPreference {

    var mAppIcon: ImageView? = null
    var mAppName: TextView? = null
    var mAppVersion: TextView? = null

    private var mIcon: Drawable? = null
    private var mName: String? = null
    private var mVersion: String? = null

    private var mDrawableRect: Rect = Rect()

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes) {
        val a = context!!.obtainStyledAttributes(attrs, R.styleable.AppInfoPreference, 0, 0)
        mName = a.getString(R.styleable.AppInfoPreference_appName)
        mIcon = a.getDrawable(R.styleable.AppInfoPreference_appIcon)
        mVersion = a.getString(R.styleable.AppInfoPreference_appVersion)
        a.recycle()
    }

    @SuppressLint("RestrictedApi", "ClipboardManagerDetector")
    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        mAppIcon = holder.findViewById(R.id.about_app_icon) as ImageView
        mAppName = holder.findViewById(R.id.about_app_name) as TextView
        mAppVersion = holder.findViewById(R.id.about_app_version) as TextView

        mAppIcon?.apply {
            (parent as? ViewGroup)?.apply {
                setOnClickListener(null)
                background = null
            }
            setImageDrawable(mIcon)
        }

        mAppName?.text = mName

        val window = COUIPopupWindow(context).apply {
            val mWindow = this
            val bgd = ResourcesCompat.getDrawable(context.resources, R.drawable.text_selection_toolbar, null) as NinePatchDrawable
            contentView = LayoutInflater.from(context).inflate(R.layout.popup_window_layout, null)
            bgd.getPadding(mDrawableRect)
            setBackgroundDrawable(ResourcesCompat.getDrawable(context.resources, R.drawable.text_selection_toolbar, null))
            contentView.findViewById<TextView>(R.id.popup_window_copy_body)?.apply {
                setOnClickListener {
                    val mClipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    mClipboardManager.setPrimaryClip(ClipData.newPlainText(null, mAppVersion?.text.toString()))
                    showToast(R.string.version_text_copied, true)
                    mWindow.dismiss()
                }
            }
            setDismissTouchOutside(true)
        }

        mAppVersion?.apply {
            text = context.getString(R.string.about_version, Utils.getVersionName())
            setOnLongClickListener {
                val offsetX = (mDrawableRect.left + mDrawableRect.right +
                        context.resources.getDimensionPixelOffset(R.dimen.layout_dp_76) - this.measuredWidth) / 2
                val offsetY = (this.measuredHeight + context.resources.getDimensionPixelOffset(R.dimen.layout_dp_48) +
                        context.resources.getDimensionPixelOffset(R.dimen.layout_dp_12) + mDrawableRect.top)
                if (isRtl()) {
                    window.showAsDropDown(this, -this.measuredWidth - offsetX, -offsetY)
                } else {
                    window.showAsDropDown(this, -offsetX, -offsetY)
                }
                true
            }
        }
    }

    private fun isRtl(): Boolean {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
    }
    private fun showToast(resId: Int, shortToast: Boolean) {
        ToastManager.showToast(resId, if (shortToast) Toast.LENGTH_SHORT else Toast.LENGTH_LONG)
    }
}