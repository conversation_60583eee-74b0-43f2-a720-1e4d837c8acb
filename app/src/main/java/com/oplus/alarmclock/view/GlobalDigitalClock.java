/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.08.22   liukun build
 *******************************************************/
package com.oplus.alarmclock.view;

import java.util.Calendar;
import java.util.TimeZone;

import android.content.Context;
import android.util.AttributeSet;

import com.oplus.clock.common.utils.Log;

public class GlobalDigitalClock extends DigitalClock {

    private static final String TAG = "GlobalDigitalClock";

    private TimeZone mTimezone;
    private String mTimeZoneID;


    public GlobalDigitalClock(Context context) {
        this(context, null);
    }

    public GlobalDigitalClock(Context context, AttributeSet attrs) {
        super(context, attrs);
        setIsGridMode(true);
    }

    private void onTimeUpdated(Calendar c) {
        if (getVisibility() != GONE) {
            updateTime(c);
        }
    }

    public void setTimeZoneID(String id) {
        if (null == id) {
            return;
        }
        mTimeZoneID = id;
        mTimezone = TimeZone.getTimeZone(id);
        onTimeChanged();
    }

    public String getTimeZoneID() {
        return mTimeZoneID;
    }

    public void onTimeChanged() {
        Calendar calendar = (mTimezone != null) ? Calendar.getInstance(mTimezone) : Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        onTimeUpdated(calendar);
    }

}
