/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Displays the time.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 * v1.1, 2018-8-20, <PERSON>kun, recode
 ************************************************************/

package com.oplus.alarmclock.view;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.Formatter;

import java.util.Calendar;
import java.util.Locale;

/**
 * Displays the time
 */
public class DigitalClockSmall extends RelativeLayout {
    private TextView mAmPm;
    private TextView mTime;
    private float mTimeTextSize;
    private float mAmPmTextSize;
    private int mTimeTextColor;
    private int mAmPmTextColor;

    public DigitalClockSmall(Context context) {
        this(context, null);
    }

    public DigitalClockSmall(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttrs(context, attrs);
        initLayout(context);
    }

    public void updateTime(int hour, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minutes);
        updateTime(calendar, false);
    }

    public void updateTime(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        updateTime(calendar, true);
    }

    private void initAttrs(Context context, AttributeSet attrs) {
        Resources resources = context.getResources();
        float defaultTimeTextSize = resources.getDimension(R.dimen.text_size_sp_12);
        float defaultAmPmTextSize = resources.getDimension(R.dimen.text_size_sp_8);
        int defaultTextColor = resources.getColor(R.color.text_black_alpha_55, null);
        mTimeTextSize = defaultTimeTextSize;
        mAmPmTextSize = defaultAmPmTextSize;
        mTimeTextColor = defaultTextColor;
        mAmPmTextColor = defaultTextColor;
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DigitalClockSmall);
            mTimeTextSize = typedArray.getDimension(R.styleable.DigitalClockSmall_text_size_time, defaultTimeTextSize);
            mAmPmTextSize = typedArray.getDimension(R.styleable.DigitalClockSmall_text_size_am_pm, defaultAmPmTextSize);
            mTimeTextColor = typedArray.getColor(R.styleable.DigitalClockSmall_text_color_time, defaultTextColor);
            mAmPmTextColor = typedArray.getColor(R.styleable.DigitalClockSmall_text_color_am_pm, defaultTextColor);
            typedArray.recycle();
        }
    }

    private void initLayout(Context context) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(
                Context.LAYOUT_INFLATER_SERVICE);
        if (inflater != null) {
            inflater.inflate(R.layout.digital_clock_small, this);
            initTextSizeAndColor();
        }
    }

    private void initAmPm() {
        Locale locale = Locale.getDefault();
        String country = locale.getCountry();
        String language = locale.getLanguage();
        boolean mAmPmShowInSecondLine = "ug".equals(language) || "uz".equals(language);
        if (("CN").equals(country) || ("TW").equals(country)) {
            if (mAmPmShowInSecondLine) {
                mAmPm = findViewById(R.id.am_pm);
            } else {
                mAmPm = findViewById(R.id.am_pm_chinese);
            }
        } else {
            mAmPm = findViewById(R.id.am_pm);
        }
    }

    private void updateView(boolean isMorning) {
        if (DateFormat.is24HourFormat(AlarmClockApplication.getInstance())) {
            mAmPm.setVisibility(View.GONE);
            String timeStr = mTime.getText().toString();
            if (!TextUtils.isEmpty(timeStr)) {
                String mTimeStart = "1";
                if (!timeStr.startsWith(mTimeStart)) {
                    LayoutParams layoutParams = (LayoutParams) mTime.getLayoutParams();
                    if (layoutParams != null) {
                        int marginStart = 6;
                        layoutParams.setMarginStart(marginStart);
                    }
                }
            }
        } else {
            mAmPm.setVisibility(View.VISIBLE);
            if (isMorning) {
                mAmPm.setText(R.string.am);
            } else {
                mAmPm.setText(R.string.pm);
            }
        }
    }

    private void initTextSizeAndColor() {
        mTime = findViewById(R.id.time);
        initAmPm();
        mTime.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTimeTextSize);
        mAmPm.setTextSize(TypedValue.COMPLEX_UNIT_PX, mAmPmTextSize);
        mTime.setTextColor(mTimeTextColor);
        mAmPm.setTextColor(mAmPmTextColor);
    }

    private void updateTime(Calendar calendar, Boolean haveSecond) {
        String format = Formatter.getTimeFormatWithoutAMPM(AlarmClockApplication.getInstance(), haveSecond);
        CharSequence newTime = DateFormat.format(format, calendar);
        mTime.setText(newTime);
        updateView((calendar.get(Calendar.AM_PM) == Calendar.AM));
    }
}
