/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : * View of full screen alarm clock and timer.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.view;


import static com.oplus.alarmclock.utils.GarbAlarmUtils.TERM_SEPARATOR;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.content.res.TypedArray;
import android.text.format.DateFormat;
import android.text.format.DateUtils;
import android.text.format.Time;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.AppFeatureUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.TextWeightUtils;

import java.util.Calendar;

public class TimerTimeView extends RelativeLayout {

    private static final String TAG = "TimerTimeView";
    private static final int TEXT_WEIGHT = 500;
    private TextView mAlarmTime;
    private TextView mAlarmDate;
    private Context mContext;
    private int mLastMinute = -1;
    private boolean mIsDragonfly = false;

    public TimerTimeView(Context context) {
        this(context, null);
    }

    public TimerTimeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TimerTimeView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initAttr(context, attrs);
        init(context);
        mContext = context;
    }

    private void initAttr(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray obtain = context.getResources().obtainAttributes(attrs, R.styleable.TimerTimeView);
            mIsDragonfly = obtain.getBoolean(R.styleable.TimerTimeView_is_dragonfly, false);
            obtain.recycle();
        }
    }

    private void init(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        Log.d(TAG, "mIsDragonfly: " + mIsDragonfly);
        int id = mIsDragonfly ? R.layout.clock_time_floating_view_dragonfly : R.layout.clock_time_floating_view;
        inflater.inflate(id, this, true);
        mAlarmTime = findViewById(R.id.alarm_time);
        mAlarmDate = findViewById(R.id.date_week_text);
        boolean isAboveOS14 = Utils.isAboveOS14();
        boolean isLightOS = AppFeatureUtils.isLightOS();
        if (mIsDragonfly) {
            TextWeightUtils.setTextWeightNoChange(mAlarmTime, (isAboveOS14 && !isLightOS) ? TextWeightUtils.WEIGHT_MAX : TextWeightUtils.WEIGHT_NINE);
        } else {
            TextWeightUtils.setTextBold(mAlarmTime);
        }
    }

    public void update() {
        final long millis = System.currentTimeMillis();
        Time time = new Time();
        time.set(millis);
        int minute = time.minute;
        if (minute == mLastMinute) {
            return;
        }
        mLastMinute = minute;

        Log.i(TAG, "update minute = " + minute);
        if ((mAlarmTime == null) || (mAlarmDate == null)) {
            return;
        }

        String format = Formatter.getTimeFormatWithoutAMPM(mContext);
        CharSequence newTime = DateFormat.format(format, Calendar.getInstance());
        mAlarmTime.setText(newTime.toString().replaceAll(TERM_SEPARATOR, "").trim());

        if (DeviceUtils.isExpVersion(mContext)) {
            String dateWeek = DateUtils.formatDateTime(mContext, millis, DateUtils.FORMAT_NO_YEAR
                    | DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_SHOW_WEEKDAY);
            mAlarmDate.setText(dateWeek);
        } else {
            String date = DateUtils.formatDateTime(mContext, millis,
                    DateUtils.FORMAT_NO_YEAR | DateUtils.FORMAT_SHOW_DATE);
            String week = DateUtils.formatDateTime(mContext, millis, DateUtils.FORMAT_SHOW_WEEKDAY);
            mAlarmDate.setText(date + " " + week);
        }
    }

    public void setSmall() {
        Context context = getContext();
        if (context != null) {
            Resources resources = context.getResources();
            float timeSize = resources.getDimension(R.dimen.layout_dp_56);
            float dataSize = resources.getDimension(R.dimen.layout_dp_12);
            setTextSize(timeSize, dataSize);
            MarginLayoutParams layoutParams = (MarginLayoutParams) mAlarmDate.getLayoutParams();
            layoutParams.topMargin = 0;
            mAlarmDate.setLayoutParams(layoutParams);
            float translationY = context.getResources().getDimension(R.dimen.layout_dp_4_fu);
            mAlarmDate.setTranslationY(translationY);
        }
    }

    /**
     * 皮套模式
     */
    public void setDeviceCaseView() {
        Context context = getContext();
        if (context != null) {
            Resources resources = context.getResources();
            float timeSize = resources.getDimension(R.dimen.layout_dp_40);
            float dataSize = resources.getDimension(R.dimen.layout_dp_10);
            setTextSize(timeSize, dataSize);
            mAlarmDate.setVisibility(GONE);
            Typeface typeface = Typeface.create(Typeface.create("sys-sans-en", Typeface.BOLD), 500, false);
            mAlarmTime.setTypeface(typeface);
        }
    }

    public void setTextSize(float timeSize, float dataSize) {
        mAlarmTime.setTextSize(TypedValue.COMPLEX_UNIT_PX, timeSize);
        mAlarmDate.setTextSize(TypedValue.COMPLEX_UNIT_PX, dataSize);
    }
}