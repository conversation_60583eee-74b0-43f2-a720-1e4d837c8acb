package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.oplus.clock.common.utils.Log;

import java.lang.ref.WeakReference;

public class MultiFingerView extends View {
    private static final String TAG = "MultiFingerView";
    private WeakReference<Callback> mCallback = null;

    public MultiFingerView(Context context) {
        super(context);
    }

    public MultiFingerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MultiFingerView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void registOnTouchEventCallback(Callback cb) {
        mCallback = new WeakReference<>(cb);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.d(TAG, "onTouchEvent: " + event.toString());
        if ((mCallback != null) && (mCallback.get() != null)) {
            try {
                mCallback.get().touchEvent(event);
            } catch (Exception e) {
                Log.e("MultiFingerView " + e.getMessage());
            }
        }
        return super.onTouchEvent(event);
    }

    public interface Callback {
        void touchEvent(MotionEvent event);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        Log.d(TAG, "dispatchTouchEvent: " + event.toString());
        return super.dispatchTouchEvent(event);
    }
}
