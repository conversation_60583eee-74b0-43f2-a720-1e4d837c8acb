/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date : 2019-08-02
 ** Author: Yuxiaolong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.oplus.alarmclock.R;

@SuppressLint("AppCompatCustomView")
public class NavigationTextView extends TextView {
    private int mItemMinWidth;
    private int mDefaultTextSize;
    private boolean mIsAlignStart;

    public NavigationTextView(Context context) {
        this(context, null);
    }

    public NavigationTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NavigationTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        Resources res = getResources();
        mItemMinWidth = res.getDimensionPixelSize(R.dimen.color_bottom_navigation_item_min_width);
        mDefaultTextSize = res.getDimensionPixelSize(R.dimen.color_navigation_item_default_text_size);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.NavigationTextView);
        mIsAlignStart = ta.getBoolean(R.styleable.NavigationTextView_alignStart, true);
        ta.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setText();
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private void setText() {
        float currentScale = getResources().getConfiguration().fontScale;
        int textSize = (int) COUIChangeTextUtil.getSuitableFontSize(mDefaultTextSize, currentScale, COUIChangeTextUtil.G2);
        setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        if (isPortrait()) {
            int strWidth = (int) getPaint().measureText(getText().toString());
            if (mIsAlignStart) {
                setTextAlignment(TEXT_ALIGNMENT_VIEW_START);
                if (getLayoutDirection() == LAYOUT_DIRECTION_RTL) {
                    setPadding(0, 0, ((strWidth >= mItemMinWidth) ? 0 : (mItemMinWidth - strWidth) / 2), 0);
                } else {
                    setPadding(((strWidth >= mItemMinWidth) ? 0 : (mItemMinWidth - strWidth) / 2), 0, 0, 0);
                }
            } else {
                setTextAlignment(TEXT_ALIGNMENT_VIEW_END);
                if (getLayoutDirection() == LAYOUT_DIRECTION_RTL) {
                    setPadding(((strWidth >= mItemMinWidth) ? 0 : (mItemMinWidth - strWidth) / 2), 0, 0, 0);
                } else {
                    setPadding(0, 0, ((strWidth >= mItemMinWidth) ? 0 : (mItemMinWidth - strWidth) / 2), 0);
                }
            }
        }
    }

    private boolean isPortrait() {
        return getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT;
    }
}
