/***********************************************************
 * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File: PrivacyPolicyAlert.java
 * Description: Privacy Policy Alert widget.
 * Version: V 1.0
 * Date : 2018-09-20
 * Author: <PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.oplus.alarmclock.view

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.RuntimePermissionAlert
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils.Companion.setStatementContentView
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FeedbackMultiChannelUtil
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.clock.common.utils.Log

class PrivacyPolicyAlert(private val mActivity: Activity, private val mCallback: PrivacyPolicyCallback?) {


    companion object {
        const val SP_KEY_PRIVACY_POLICY_ALERT = "privacy_policy_alert_should_show"
        const val SP_NAME = "privacy_policy_alert"
        const val SP_KEY_CTA_DIALOG = "cta_dialog_should_show";
        const val SP_NAME_EXP = "local_config";

        private const val TAG = "PrivacyPolicyAlert"

        @JvmStatic
        fun isFirstEntry(activity: Context): Boolean {
            return PrefUtils.getBoolean(activity, SP_NAME, SP_KEY_PRIVACY_POLICY_ALERT, true)
        }
    }

    private val mFeedbackUtil = FeedbackMultiChannelUtil()

    interface PrivacyPolicyCallback {
        fun doAfterPermitted()
        fun onExitClick()
    }

    interface DismissAnimationEndCallback {
        /**
         * 弹窗动画结束回调
         */
        fun dismissAnimationEnd()
    }
    /**
     * 外销  内销用户须知 （外销不需要集成FeedbackSDK）
     */
    fun checkPermitPrivacyPolicy(isFromMiniApp: Boolean): Boolean {
        Log.i(TAG, "checkPermitPrivacyPolicy")
        if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            //bug 2788955 要求外销机不用显示户须知 直接进入
            return true
        } else {
            return if (isFirstEntry(mActivity)) {
                Log.i(TAG, "checkPermitPrivacyPolicy isFirstEntry")
                showStatementView(isFromMiniApp)
                false
            } else {
                if (!isFromMiniApp) {
                    mFeedbackUtil.initFeedbackSDK()
                }
                true
            }
        }
    }

    /**
     * 外销机升级前后 SP共用。
     */
    fun isFirstEntryExp(mActivity: Activity): Boolean {
        val sp = mActivity.getSharedPreferences(RuntimePermissionAlert.SP_NAME, Context.MODE_PRIVATE)
        return sp.getBoolean(RuntimePermissionAlert.SP_KEY_CTA_DIALOG, true)
    }

    private fun showStatementView(isMiniApp: Boolean) {
        Log.i(TAG, "showStatementView")
        if (isMiniApp && mActivity is AppCompatActivity && mCallback != null) {
            setStatementContentView(mActivity, mCallback)
            return
        }
        if ((mActivity is FragmentActivity) && (mCallback != null)) {
            setStatementContentView(mActivity, mCallback, mFeedbackUtil)
        }
    }
}