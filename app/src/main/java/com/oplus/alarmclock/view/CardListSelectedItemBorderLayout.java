/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CardListSelectedItemBorderLayout.java
 ** Description: extends COUICardListSelectedItemLayout ,draw a border with it
 ** Version: 1.0
 ** Date : 2024/3/25
 ** Author: <EMAIL>
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  LiJin      2024/3/25     1.0            draw a border with it
 ****************************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.roundRect.COUIShapePath;
import com.oplus.alarmclock.R;


/**
 * itemAlpha动画，添加了选中边框
 * 根据isSelected()是否绘制边框
 */
public class CardListSelectedItemBorderLayout extends COUICardListSelectedItemLayout {
    private final Paint mPaint = new Paint();
    private final float mRadius;
    private int mStrokeWidth;
    private Path mPath;


    public CardListSelectedItemBorderLayout(Context context) {
        this(context, null);
    }

    public CardListSelectedItemBorderLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CardListSelectedItemBorderLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public CardListSelectedItemBorderLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        COUIDarkModeUtil.setForceDarkAllow(this, false);
        final TypedArray a = context.obtainStyledAttributes(attrs,
                R.styleable.COUICardListSelectedItemLayout, defStyleAttr, defStyleRes);
        mRadius = a.getDimensionPixelOffset(R.styleable.COUICardListSelectedItemLayout_couiCardRadius,
                COUIContextUtil.getAttrDimens(context, com.support.appcompat.R.attr.couiRoundCornerM));
        init(getContext());
        a.recycle();
    }


    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isSelected()) {
            canvas.drawPath(mPath, mPaint);
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        updatePath();
    }

    private void updatePath() {
        mPath.reset();
        float startWidth = (float) (mStrokeWidth * 0.5);
        RectF rectF = new RectF(startWidth, startWidth, getWidth() - startWidth, getHeight() - startWidth);
        mPath = COUIShapePath.getRoundRectPath(mPath, rectF, mRadius, true, true, true, true);
    }

    private void init(Context context) {
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mStrokeWidth = context.getResources().getDimensionPixelOffset(R.dimen.layout_dp_2);
        mPaint.setStrokeWidth(mStrokeWidth);
        int curCardBgColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimaryText);
        mPaint.setColor(curCardBgColor);
        mPath = new Path();
    }
}
