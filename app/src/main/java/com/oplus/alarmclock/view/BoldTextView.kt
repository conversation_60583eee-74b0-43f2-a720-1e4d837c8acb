/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BoldTextView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.oplus.alarmclock.R

class BoldTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : AppCompatTextView(context, attrs, defStyleAttr) {

    init {
        paint.style = Paint.Style.FILL_AND_STROKE
        context.resources?.run {
            val defaultWeight = getDimension(R.dimen.layout_dp_070)
            var textWeight = defaultWeight
            attrs?.run {
                val typedArray = obtainAttributes(attrs, R.styleable.BoldTextView)
                textWeight =
                    typedArray.getDimension(R.styleable.BoldTextView_text_weight, defaultWeight)
                typedArray.recycle()
            }
            paint.strokeWidth = textWeight
        }
    }
}