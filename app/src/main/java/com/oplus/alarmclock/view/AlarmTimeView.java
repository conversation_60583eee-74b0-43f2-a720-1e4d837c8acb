/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : * View of full screen alarm clock and timer.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.view;


import static com.oplus.alarmclock.utils.GarbAlarmUtils.TERM_SEPARATOR;

import android.content.Context;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.format.DateUtils;
import android.text.format.Time;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;

public class AlarmTimeView extends RelativeLayout {

    private static final String TAG = "AlarmTimeView";
    private static final String START_NUMBER_1 = "1";

    private TextView mAlarmTime;
    private TextView mAlarmDate;
    private Context mContext;

    private int mLastMinute = -1;

    public AlarmTimeView(Context context) {
        this(context, null);
    }

    public AlarmTimeView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AlarmTimeView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
        mContext = context;
    }

    private void init(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        inflater.inflate(R.layout.clock_time_floating_view, this, true);
        mAlarmTime = (TextView) findViewById(R.id.alarm_time);
        mAlarmDate = (TextView) findViewById(R.id.date_week_text);
    }

    public void update() {
        final long millis = System.currentTimeMillis();
        Time time = new Time();
        time.set(millis);
        int minute = time.minute;
        if (minute == mLastMinute) {
            return;
        }
        mLastMinute = minute;
        Log.i(TAG, "update minute = " + minute);
        if ((mAlarmTime == null) || (mAlarmDate == null)) {
            return;
        }
        String format = Formatter.getTimeFormatWithoutAMPM(mContext);
        CharSequence newTime = DateFormat.format(format, Calendar.getInstance());
        mAlarmTime.setText(newTime.toString().replaceAll(TERM_SEPARATOR, "").trim());

        String[] timeStr = TextUtils.split(newTime.toString(), "\n");
        if ((timeStr != null) && (timeStr.length == 2)) {
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mAlarmDate.getLayoutParams();
            if (timeStr[0].startsWith(START_NUMBER_1) && timeStr[1].startsWith(START_NUMBER_1)) {
                lp.setMarginStart(getContext().getResources().getDimensionPixelSize(R.dimen.alarm_time_date_left_margin));
            } else {
                lp.setMarginStart(0);
            }
        }

        if (DeviceUtils.isExpVersion(mContext)) {
            String dateWeek = DateUtils.formatDateTime(mContext, millis, DateUtils.FORMAT_NO_YEAR
                    | DateUtils.FORMAT_SHOW_DATE | DateUtils.FORMAT_SHOW_WEEKDAY);
            mAlarmDate.setText(dateWeek);
        } else {
            String date = DateUtils.formatDateTime(mContext, millis,
                    DateUtils.FORMAT_NO_YEAR | DateUtils.FORMAT_SHOW_DATE);
            String week = DateUtils.formatDateTime(mContext, millis, DateUtils.FORMAT_SHOW_WEEKDAY);
            mAlarmDate.setText(date + " " + week);
        }
    }
}