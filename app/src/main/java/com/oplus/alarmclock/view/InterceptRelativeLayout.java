/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - InterceptRelativeLayout.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/02/19
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2020/02/19     1.0            InterceptRelativeLayout.java
 *  ***************************************************************
 */

package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.RelativeLayout;

public class InterceptRelativeLayout extends RelativeLayout {

    private OnDispatchEventListener mListener;

    public void setListener(OnDispatchEventListener listener) {
        this.mListener = listener;
    }

    public InterceptRelativeLayout(Context context) {
        super(context);
    }

    public InterceptRelativeLayout(Context context,
                                   AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public InterceptRelativeLayout(Context context,
                                   AttributeSet attributeSet,
                                   int i) {
        super(context, attributeSet, i);
    }

    public InterceptRelativeLayout(Context context, AttributeSet attributeSet, int i, int i1) {
        super(context, attributeSet, i, i1);
    }

    public interface OnDispatchEventListener {
        /**
         * export the event before others
         *
         * @param e onViewDispatchEvent
         */
        void onDispatchEvent(MotionEvent e);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent motionEvent) {
        if (mListener != null) {
            mListener.onDispatchEvent(motionEvent);
        }
        return super.dispatchTouchEvent(motionEvent);
    }
}
