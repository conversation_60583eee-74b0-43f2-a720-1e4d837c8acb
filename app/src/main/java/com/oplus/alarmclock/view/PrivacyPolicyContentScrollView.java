/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: PrivacyPolicyContentScrollView.java
 ** Description: Custom scroll widget for Privacy Policy Alert.
 ** Version: V 1.0
 ** Date : 2018-09-20
 ** Author: <PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ScrollView;

import com.oplus.alarmclock.R;

public class PrivacyPolicyContentScrollView extends ScrollView {
    private int mHeight;

    public PrivacyPolicyContentScrollView(Context context) {
        super(context);
    }

    public PrivacyPolicyContentScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PrivacyPolicyContentScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.PrivacyPolicyContentScrollView);
        mHeight = array.getDimensionPixelOffset(R.styleable.PrivacyPolicyContentScrollView_max_height, 0);
        array.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        heightSize = Math.min(heightSize, mHeight);
        heightMeasureSpec = MeasureSpec.makeMeasureSpec(heightSize, heightMode);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }
}
