/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.10.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.oplus.alarmclock.view.viewpager.RtlViewPager;
import com.oplus.alarmclock.view.viewpager.RtlViewPager;

public class LocalViewPager extends RtlViewPager {

    private boolean mScrollable = true;

    public LocalViewPager(Context context) {
        super(context);
    }

    public LocalViewPager(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        if (mScrollable) {
            return super.onInterceptTouchEvent(motionEvent);
        } else {
            return false;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent motionEvent) {
        if (mScrollable) {
            return super.onTouchEvent(motionEvent);
        } else {
            return true;
        }
    }

    public void setScrollable(boolean scrollable) {
        mScrollable = scrollable;
    }
}
