/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockSetListPreference.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/8/22     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.preference.PreferenceViewHolder;

import com.coui.appcompat.preference.COUIListPreference;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.oplus.alarmclock.R;

public class ClockSetListPreference extends COUIListPreference {

    public ClockSetListPreference(Context context, AttributeSet attrs) {
        super(context, attrs, R.attr.couiJumpPreferenceStyle);
    }

    public ClockSetListPreference(Context context) {
        super(context);
    }

    @Override
    public void onBindViewHolder(PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);

        TextView summary = holder.itemView.findViewById(android.R.id.summary);
        if (summary != null) {
            summary.setTextColor(COUIContextUtil.getAttrColor(getContext(), R.attr.couiColorPrimary));
        }
    }

}
