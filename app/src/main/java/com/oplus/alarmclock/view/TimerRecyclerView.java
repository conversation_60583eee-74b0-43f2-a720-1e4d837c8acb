/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerRecyclerView.java
 ** Description: draw a circle scale animation
 ** Version: 1.0
 ** Date : 2021/3/17
 ** Author: He<PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  HeGai  2021/3/17     1.0            build this module
 ****************************************************************/


package com.oplus.alarmclock.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.COUIRecyclerView;

public class TimerRecyclerView extends COUIRecyclerView {

    public TimerRecyclerView(@NonNull Context context) {
        super(context);

    }

    public TimerRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TimerRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public interface ItemAnimationListener {
        void onAnimationUpdate(ValueAnimator animation);

        void onAnimationStart(Animator animation);

        void onAnimationEnd(Animator animation);

        void onAnimationCancel(Animator animation);

        void onAnimationRepeat(Animator animation);

    }

    /**
     * 去除列表顶部阴性
     * @return
     */
    @Override
    protected float getTopFadingEdgeStrength() {
        return 0;
    }

}
