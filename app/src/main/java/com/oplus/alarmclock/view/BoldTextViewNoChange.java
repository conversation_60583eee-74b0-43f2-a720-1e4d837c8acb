package com.oplus.alarmclock.view;

import android.content.Context;
import android.util.AttributeSet;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.Utils;

public class BoldTextViewNoChange extends androidx.appcompat.widget.AppCompatTextView {
    private final int SIZE_UNSPECIFIED = -1;
    private int mTextFontWeight;

    public BoldTextViewNoChange(Context context) {
        this(context, null);
    }

    public BoldTextViewNoChange(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BoldTextViewNoChange(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTextFontWeight = Utils.getAttrTextFontWeight(context, attrs, R.attr.text_font_weight, SIZE_UNSPECIFIED);
        if (mTextFontWeight != SIZE_UNSPECIFIED) {
            TextWeightUtils.setTextWeightNoChange(getPaint(), mTextFontWeight);
        }
    }
}
