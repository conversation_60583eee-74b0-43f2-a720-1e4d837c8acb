/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - LoopAlarmUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/17
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2023/11/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.app.Activity
import android.content.ContentProviderOperation
import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import android.util.Pair
import android.view.View
import android.widget.ImageView
import com.coui.appcompat.tooltips.COUIToolTips
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.aidl.PlatformUtils
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.alarmclock.AlarmSchedule
import com.oplus.alarmclock.alarmclock.AlarmUtils
import com.oplus.alarmclock.alarmclock.LegalHolidayUtil
import com.oplus.alarmclock.alarmclock.LegalHolidayUtil.Holiday
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.BACKUP_PLUGIN_BUILD_SPIT_STR
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.BACKUP_PLUGIN_BUILD_SPIT_STR_2
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_RING_TIME_DATA
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.MAX_CURRENT_VALUE
import com.oplus.alarmclock.provider.AlarmContract.QUERY_COLUMNS
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.Log.i
import java.util.Calendar
import java.util.concurrent.Executors
import java.util.concurrent.FutureTask
import kotlin.math.abs

/**
 * 轮班闹钟数据处理
 */
object LoopAlarmUtils {

    const val TAG = "LoopAlarmUtils"

    /**
     * 默认轮班周期
     */
    const val DEFAULT_LOOP_CYCLE = 4

    /**
     * 添加闹钟面板下滑关闭距离
     */
    const val PANEL_TOUCH_DOWN_DIS = 850

    /**
     * 默认工作天
     */
    const val DEFAULT_LOOP_WORK_DAY = 3

    const val STRING_SPIT_NUMBER_3 = 3

    /**
     * 新建轮班闹钟的默认时间
     */
    var mLoopAlarmHour: Int = 0
    var mLoopAlarmMinutes: Int = 0

    /**
     * 设置时间
     */
    @JvmStatic
    fun setLoopAlarmTime() {
        val c = Calendar.getInstance()
        mLoopAlarmHour = c[Calendar.HOUR_OF_DAY]
        mLoopAlarmMinutes = c[Calendar.MINUTE]
    }

    /**
     * 计算当前时间的轮班周期
     * @param loopCycle 轮班周期
     * @param loopDay 今天第几班
     * @param nowTime 当前时间
     * @param createTime 轮班闹钟创建时间
     * @return 开始周期-结束周期
     */
    @JvmStatic
    fun computeLoopCycleDate(loopCycle: Int, loopDay: Int, nowTime: Calendar, createTime: Calendar): Pair<Calendar, Calendar> {
        Log.i(TAG, "computeLoopCycleDate loopCycle:$loopCycle  loopDay:$loopDay  createTime:${Formatter.formatTime(createTime.timeInMillis)}")
        if (loopCycle <= 1) {
            return Pair(nowTime, nowTime)
        }
        val indexDay = computeDate(createTime, nowTime, loopCycle, loopDay)
        val endTime: Calendar = nowTime.clone() as Calendar
        nowTime.add(Calendar.DAY_OF_YEAR, -(indexDay - 1))
        endTime.add(Calendar.DAY_OF_YEAR, (loopCycle - indexDay))
        return Pair(nowTime, endTime)
    }

    /**
     * 轮班开始周期文本
     * @param year
     * @param month
     * @param day
     */
    @JvmStatic
    fun getLoopAlarmDialogLoopDayStr(alarm: Alarm?, context: Context?, year: Int, month: Int, day: Int): String {
        if (context == null || alarm == null) {
            Log.e(TAG, "getLoopAlarmDialogLoopDayStr context or alarm is null")
            return ""
        }
        val crateDate = Calendar.getInstance()
        crateDate.set(Calendar.YEAR, year)
        crateDate.set(Calendar.MONTH, month)
        crateDate.set(Calendar.DAY_OF_MONTH, day)
        val now = Calendar.getInstance()
        return if (now.timeInMillis <= crateDate.timeInMillis) {
            if (checkLoopAlarmResetDays(alarm.loopAlarmList, 0)) {
                ""
            } else {
                AlarmUtils.getElapsedTimeUntilLoopAlarmDescription(context, alarm, crateDate)
            }
        } else {
            val loopDay = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), crateDate, now)
            context.resources.getQuantityString(R.plurals.loop_alarm_now_loop_day, loopDay, loopDay)
        }
    }

    /**
     * 计算日期文本第几天
     * @param loopDay 第几天
     * @param alarm 轮班闹钟信息
     * @return 日期
     */
    @JvmStatic
    fun computeLoopAlarmDateText(context: Context?, loopDay: Int, alarm: Alarm?): String {
        if (context == null || alarm == null) {
            return ""
        }
        val createTime = Calendar.getInstance()
        createTime.timeInMillis = alarm.getmWorkdayUpdateTime()
        val nowTime = Calendar.getInstance()
        if (getTimeDistance(nowTime, createTime) > 0) {
            //创建时间大于当前时间，使用创建时间计算轮询
            nowTime.timeInMillis = createTime.timeInMillis
        }
        val cycleDate = computeLoopCycleDate(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), nowTime, createTime)
        val startTime = cycleDate.first
        startTime.add(Calendar.DAY_OF_YEAR, loopDay)
        val dis = getTimeDistance(startTime, Calendar.getInstance())
        return if (dis == 0) {
            //今天
            context.getString(R.string.today)
        } else {
            Utils.getLocaleDateMDFormat(context, startTime.timeInMillis)
        }
    }

    /**
     * 计算几天几班
     * @param list 轮班闹钟子列表
     * @return first 总共几班， second 休息的日期
     */
    @JvmStatic
    fun computeLoopDays(list: List<Alarm>): Pair<Int, String> {
        val resetDays = StringBuffer()
        var resetCount = 0
        for (index in list.indices) {
            if (!list[index].isEnabled) {
                resetDays.append(index + 1)
                resetDays.append(DatePickerUtils.SPLIT)
            } else {
                resetCount++
            }
        }
        return Pair(resetCount, resetDays.toString())
    }

    /**
     * 获取今日轮班闹钟时间
     * @param list 子闹钟列表
     * @param loopDay 第几天
     * @return first 小时 second 分钟
     */
    @JvmStatic
    fun computeLoopHourAndMinute(list: List<Alarm>, loopDay: Int): Pair<Int, Int> {
        val c = Calendar.getInstance()
        var hour = c[Calendar.HOUR_OF_DAY]
        var minute = c[Calendar.MINUTE]
        if (list.size >= loopDay && loopDay > 0) {
            val alarm = list[loopDay - 1]
            hour = alarm.hour
            minute = alarm.minutes
        }
        return Pair(hour, minute)
    }

    /**
     * 计算当前是第几天
     */
    @JvmStatic
    fun computeLoopAlarmDay(loopCycle: Int, loopDay: Int, crateTime: Calendar, nowTime: Calendar): Int {
        return computeDate(crateTime, nowTime, loopCycle, loopDay)
    }

    /**
     * 计算当前日期相对于创建日期在轮班周期内的第几天
     * @param crateTime 轮班闹钟创建时间
     * @param nowTime 当前时间
     * @param loopDay 第几班
     * @param loopCycle 轮班周期
     * @return 当前时间周期内第几天
     */
    @JvmStatic
    private fun computeDate(crateTime: Calendar, nowTime: Calendar, loopCycle: Int, loopDay: Int): Int {
        if (loopCycle == 0) {
            return 0
        }
        //获取时间差值
        val dis = getTimeDistance(crateTime, nowTime)
        Log.d(TAG, "computeDate  loopCycle:$loopCycle loopDay:$loopDay crateTime:${Formatter.formatTime(crateTime.timeInMillis)}  " +
                "nowTime:${Formatter.formatTime(nowTime.timeInMillis)}   dis:$dis  ")
        return if (dis == 0) {
            loopDay
        } else if (dis > 0) {
            if (loopCycle > dis) {
                return if (dis + loopDay > loopCycle) {
                    (dis + loopDay) % loopCycle
                } else {
                    dis + loopDay
                }
            }
            val lDay = dis % loopCycle
            if (lDay + loopDay > loopCycle) {
                return (lDay + loopDay) % loopCycle
            }
            return (lDay + loopDay)
        } else {
            val absDis = abs(dis)
            if (loopCycle > absDis) {
                return if (loopDay > absDis) {
                    loopDay - absDis
                } else {
                    loopCycle - (absDis - loopDay)
                }
            }
            val lDay = absDis % loopCycle
            if (lDay > loopDay) {
                return lDay + loopDay
            }
            if (loopDay - lDay == 0) {
                return loopCycle
            }
            return (loopDay - lDay)
        }
    }

    /**
     * 获取两个日期之间的差值
     */
    @JvmStatic
    fun getTimeDistance(begin: Calendar, end: Calendar): Int {
        val beginDays = DatePickerUtils.todayAfter1970days(begin).toInt()
        val endDays = DatePickerUtils.todayAfter1970days(end).toInt()
        return endDays - beginDays
    }

    /**
     * 子闹钟列表添加或者删除
     */
    @JvmStatic
    fun checkAlarmList(alarmList: MutableList<Alarm>, cycle: Int) {
        if (cycle > alarmList.size) {
            alarmList.addAll(addDefaultLoopAlarm(cycle - alarmList.size))
        } else {
            for (index in 1..(alarmList.size - cycle)) {
                alarmList.removeAt(alarmList.lastIndex)
            }
        }
    }

    /**
     * 根据闹钟ID获取轮班闹钟列表
     * @param alarmId 闹钟id
     * @return 轮班闹钟子闹钟列表
     */
    @JvmStatic
    fun getLoopAlarms(context: Context, alarmId: Int): ArrayList<Alarm> {
        val list = ArrayList<Alarm>()
        kotlin.runCatching {
            val selection = ClockContract.Alarm.LOOP_ID + "=" + alarmId
            val executorService = Executors.newSingleThreadExecutor()
            val task = FutureTask {
                var cursor: Cursor? = null
                cursor = context.contentResolver.query(ClockContract.ALARM_CONTENT_URI,
                        QUERY_COLUMNS, selection, null,
                        ClockContract.Alarm.LOOP_ALARM_SORT_ORDER)
                if (cursor != null) {
                    while (cursor.moveToNext()) {
                        list.add(AlarmUtils.createAlarmFromCur(cursor, context))
                    }
                }
                cursor?.close()
                list
            }
            executorService.submit(task)
            return task.get()
        }
        return list
    }

    /**
     * 添加默认闹钟
     * @param number 添加多少个闹钟
     */
    @JvmStatic
    fun addDefaultLoopAlarm(number: Int): ArrayList<Alarm> {
        val mAlarmList: ArrayList<Alarm> = ArrayList()
        for (index in 1..number) {
            val alarm = Alarm()
            alarm.hour = LoopAlarmUtils.mLoopAlarmHour
            alarm.minutes = LoopAlarmUtils.mLoopAlarmMinutes
            alarm.isEnabled = true
            alarm.setmLoopAlarmNumber(index)
            if (index == number) {
                alarm.isEnabled = false
            }
            mAlarmList.add(alarm)
        }
        return mAlarmList
    }

    /**
     * 获取轮班闹钟上次响铃时间
     * @param priorCalendar 当前时间
     * @return 轮班闹钟上次响铃时间
     */
    @JvmStatic
    fun getPreviousAlarmTime(alarm: Alarm?, priorCalendar: Calendar): Calendar {
        if (alarm == null) {
            Log.e(TAG, "getPreviousAlarmTime alarm is null")
            return Calendar.getInstance()
        }
        val holidayList = LegalHolidayUtil.getHolidayFromCache(Calendar.getInstance())
        alarm.loopAlarmList = getLoopAlarms(AlarmClockApplication.getInstance(), alarm.id.toInt())
        val createDate = Calendar.getInstance()
        createDate.timeInMillis = alarm.getmWorkdayUpdateTime()
        if (holidayList != null) {
            var found = false
            //休息日期
            val resetDays = alarm.getmLoopRestDays().split(DatePickerUtils.SPLIT)
            loopAlarmJumpOneDay(priorCalendar, alarm, false, null)
            while (!found) {
                val createDay = Calendar.getInstance()
                createDay.timeInMillis = alarm.getmWorkdayUpdateTime()
                val day = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), createDay, priorCalendar)
                Log.d(TAG, " getPreviousAlarmTime createDay:${Formatter.formatTime(createDay.timeInMillis)}  " +
                        "priorCalendar:${Formatter.formatTime(priorCalendar.timeInMillis)}  loopDay:$day  resetDays:$resetDays")
                if (createDay.timeInMillis > priorCalendar.timeInMillis) {
                    //创建时间大于上次响铃时间
                    found = true
                    continue
                }
                val isReset = checkReset(resetDays, day)
                if (isReset) {
                    //当天休息，跳过
                    loopAlarmJumpOneDay(priorCalendar, alarm, false, null)
                    continue
                }
                found = checkHoliday(alarm, priorCalendar, holidayList)
            }
            val dis = getTimeDistance(createDate, priorCalendar)
            return if (dis >= 0) {
                priorCalendar
            } else {
                createDate
            }
        } else {
            val dis = getTimeDistance(createDate, priorCalendar)
            return if (dis >= 0) {
                getPreviousAlarmTimeNoHoliday(alarm, priorCalendar)
            } else {
                createDate
            }
        }
    }

    /**
     * 检查休息日
     */
    @JvmStatic
    private fun checkReset(resetDays: List<String>, day: Int): Boolean {
        var isReset = false
        for (reset in resetDays) {
            //当天休息
            if (!TextUtils.isEmpty(reset) && reset.toInt() == day) {
                isReset = true
            }
        }
        return isReset
    }

    /**
     * 检查节假日
     */
    @JvmStatic
    private fun checkHoliday(alarm: Alarm, priorCalendar: Calendar, holidayList: List<LegalHolidayUtil.Holiday>): Boolean {
        var found = false
        if (alarm.holidaySwitch == ClockConstant.ALARM_HOLIDAY_SWITCH_ON) {
            val workdayHolidaySwitch = LegalHolidayUtil.queryWorkdayOrHoliday(priorCalendar, holidayList,
                    alarm.workdaySwitch, Calendar.getInstance(), alarm)
            if (workdayHolidaySwitch == LegalHolidayUtil.COLOR_TYPE_WORKDAY) {
                found = true
            }
            if (!found) {
                //节假日，跳过
                loopAlarmJumpOneDay(priorCalendar, alarm, false, null)
            }
            Log.d(TAG, "getPreviousAlarmTime found:$found")
        } else {
            found = true
        }
        return found
    }


    /**
     * 获取上个闹钟响铃时间（无节假日）
     */
    @JvmStatic
    private fun getPreviousAlarmTimeNoHoliday(alarm: Alarm?, priorCalendar: Calendar): Calendar {
        if (alarm == null) {
            Log.e(TAG, "getPreviousAlarmTimeNoHoliday alarm is null")
            return Calendar.getInstance()
        }
        var found = false
        //休息日期
        val resetDays = alarm.getmLoopRestDays().split(DatePickerUtils.SPLIT)
        while (!found) {
            loopAlarmJumpOneDay(priorCalendar, alarm, false, null)
            val createDay = Calendar.getInstance()
            createDay.timeInMillis = alarm.getmWorkdayUpdateTime()
            val day = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), createDay, priorCalendar)
            Log.d(TAG, " getPreviousAlarmTimeNoHoliday createDay:${Formatter.formatTime(createDay.timeInMillis)}  " +
                    "priorCalendar:${Formatter.formatTime(priorCalendar.timeInMillis)}  loopDay:$day  resetDays:$resetDays")
            if (createDay.timeInMillis > priorCalendar.timeInMillis) {
                //创建时间大于上次响铃时间
                found = true
                continue
            }
            var isReset = false
            for (reset in resetDays) {
                //当天休息
                if (!TextUtils.isEmpty(reset) && reset.toInt() == day) {
                    isReset = true
                }
            }
            if (isReset) {
                //当天休息，跳过
                loopAlarmJumpOneDay(priorCalendar, alarm, false, null)
                continue
            }
            found = true
        }
        return priorCalendar
    }

    /**
     * 获取轮班闹钟关闭一次后的下次响铃时间
     * @param nextInstanceTime 当前时间
     * @return 关闭一次后闹钟的下次响铃时间
     */
    @JvmStatic
    fun getCloseOnceNextAlarmTime(alarm: Alarm?, nextInstanceTime: Calendar): Calendar {
        if (alarm == null) {
            Log.e(TAG, "getCloseOnceNextAlarmTime alarm is null")
            return Calendar.getInstance()
        }
        Log.i(TAG, "getCloseOnceNextAlarmTime getCloseOnceNextAlarmTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
        //获取轮班闹钟列表
        alarm.loopAlarmList = getLoopAlarms(AlarmClockApplication.getInstance(), alarm.id.toInt())
        val createTime = Calendar.getInstance()
        createTime.timeInMillis = alarm.getmWorkdayUpdateTime()
        if (createTime > nextInstanceTime) {
            //如果当前时间小于闹钟起始时间，则将时间设置为闹钟起始时间
            nextInstanceTime.timeInMillis = createTime.timeInMillis
        }
        val currentMills = nextInstanceTime.timeInMillis
        //获取闹钟下次响铃时间
        nextInstanceTime.timeInMillis = getNextAlarmTime(alarm, nextInstanceTime).timeInMillis
        i(TAG, "getCloseOnceNextAlarmTime nextInstanceTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
        val scheduleMills = nextInstanceTime.timeInMillis
        if (scheduleMills < currentMills) {
            i(TAG, "getCloseOnceNextAlarmTime Calendar +1")
            loopAlarmJumpOneDay(nextInstanceTime, alarm, true, null)
        }
        val holidayList = LegalHolidayUtil.getHolidayFromCache(Calendar.getInstance())
        //检查是否休息或者节假日
        whileLoopAlarm(holidayList, alarm, nextInstanceTime, null)
        val scheduleMillsNext = nextInstanceTime.timeInMillis
        val calendarCurrentNext = Calendar.getInstance()
        calendarCurrentNext[Calendar.YEAR] = nextInstanceTime[Calendar.YEAR]
        calendarCurrentNext[Calendar.DAY_OF_YEAR] = nextInstanceTime[Calendar.DAY_OF_YEAR]
        calendarCurrentNext[Calendar.SECOND] = 0
        calendarCurrentNext[Calendar.MILLISECOND] = 0
        val currentMillsNext = calendarCurrentNext.timeInMillis
        i(TAG, "getCloseOnceNextAlarmTime  isCancelNextAlarm true 当前日期响铃时间 = " + Formatter.formatTime(scheduleMills) + "  " +
                "当前时间 = " + Formatter.formatTime(currentMills) +
                " 下次响铃时间 " + Formatter.formatTime(scheduleMillsNext) + "  " +
                "下次响铃日期时间 -- " + Formatter.formatTime(currentMillsNext))
        if (scheduleMillsNext <= currentMillsNext || scheduleMills >= currentMills) {
            i(TAG, "getCloseOnceNextAlarmTime  isCancelNextAlarm true  add one day ")
            loopAlarmJumpOneDay(nextInstanceTime, alarm, true, null)
        }
        //检查是否休息或者节假日
        whileLoopAlarm(holidayList, alarm, nextInstanceTime, createTime)
        Log.i(TAG, "getCloseOnceNextAlarmTime nextInstanceTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
        return nextInstanceTime
    }


    @JvmStatic
    fun getNextAlarmTime(alarm: Alarm?, nextInstanceTime: Calendar): Calendar {
        return getNextAlarmTime(alarm, nextInstanceTime, null)
    }

    /**
     * 获取下次响铃时间
     * @param nextInstanceTime 当前时间
     * @return 下次响铃时间
     */
    @JvmStatic
    fun getNextAlarmTime(alarm: Alarm?, nextInstanceTime: Calendar, alarmWorkTime: Calendar?): Calendar {
        if (alarm == null) {
            Log.e(TAG, "getNextAlarmTime alarm is null")
            return Calendar.getInstance()
        }
        val currentMills = nextInstanceTime.timeInMillis
        //轮班闹钟列表为空
        if (alarm.loopAlarmList.size == 0) {
            val list = getLoopAlarms(AlarmClockApplication.getInstance(), alarm.id.toInt())
            alarm.loopAlarmList = list
        }
        //获取今日闹钟时间
        val createTime: Calendar
        if (alarmWorkTime == null) {
            createTime = Calendar.getInstance()
            createTime.timeInMillis = alarm.getmWorkdayUpdateTime()
        } else {
            createTime = alarmWorkTime
        }
        if (createTime >= nextInstanceTime) {
            //如果当前时间小于闹钟起始时间，则将时间设置为闹钟起始时间
            nextInstanceTime.timeInMillis = createTime.timeInMillis
        }
        val loopDay = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), createTime, nextInstanceTime)
        val time = computeLoopHourAndMinute(alarm.loopAlarmList, loopDay)

        nextInstanceTime[Calendar.HOUR_OF_DAY] = time.first
        nextInstanceTime[Calendar.MINUTE] = time.second
        nextInstanceTime[Calendar.SECOND] = 0
        nextInstanceTime[Calendar.MILLISECOND] = 0
        val scheduleMills = nextInstanceTime.timeInMillis
        Log.d(TAG, "-------getNextAlarmTime currentMills:${Formatter.formatTime(currentMills)} " +
                "scheduleMills:${Formatter.formatTime(scheduleMills)} loopDay:$loopDay  list:${alarm.loopAlarmList.size}")
        //超过当前时间，加一天
        if (scheduleMills < currentMills) {
            loopAlarmJumpOneDay(nextInstanceTime, alarm, true, alarmWorkTime)
        }
        val holidayList = LegalHolidayUtil.getHolidayFromCache(Calendar.getInstance())
        //检查是否有休息和节假日
        whileLoopAlarm(holidayList, alarm, nextInstanceTime, alarmWorkTime)
        Log.d(TAG, "getNextAlarmTime nextInstanceTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
        val hour = nextInstanceTime.get(Calendar.HOUR_OF_DAY)
        val minutes = nextInstanceTime.get(Calendar.MINUTE)
        val ringTIme = Pair(hour, minutes)
        Log.d(TAG, "getNextAlarmTime hour:$hour  minutes:$minutes")
        if (alarmWorkTime == null) {
            LiteEventBus.instance.send(EVENT_LOOP_ALARM_RING_TIME_DATA, ringTIme)
        }
        return nextInstanceTime
    }


    /**
     * 检查下次响铃时间是否有休息日或者节假日
     * @param holidayList 节假日列表
     * @param alarm 轮班闹钟信息
     * @param nextInstanceTime 响铃时间
     * @param alarmWorkTime 闹钟创建时间
     * @return 下次响铃时间
     */
    @JvmStatic
    private fun whileLoopAlarm(holidayList: List<LegalHolidayUtil.Holiday>?, alarm: Alarm?, nextInstanceTime: Calendar, alarmWorkTime: Calendar?) {
        if (alarm == null) {
            Log.e(TAG, "whileLoopAlarm alarm is null")
            return
        }
        var mWhileLoopAlarm = 0
        if (checkLoopAlarmResetDays(alarm.loopAlarmList, 0)) {
            //轮班班次全部休息没有班次，例4天0班
            return
        }
        var found = false
        val resetDays = alarm.getmLoopRestDays().split(DatePickerUtils.SPLIT)
        while (!found) {
            mWhileLoopAlarm++
            val createDay = Calendar.getInstance()
            val createTime = Calendar.getInstance()
            if (alarmWorkTime == null) {
                createTime.timeInMillis = alarm.getmWorkdayUpdateTime()
            } else {
                createTime.timeInMillis = alarmWorkTime.timeInMillis
            }
            Log.d(TAG, "createDay:${Formatter.formatTime(createDay.timeInMillis)} " +
                    "nextInstanceTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
            val day = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), createTime, nextInstanceTime)

            var isReset = false
            for (reset in resetDays) {
                //当天休息
                if (!TextUtils.isEmpty(reset) && reset.toInt() == day) {
                    isReset = true
                }
            }
            Log.d(TAG, "loopDay:$day isReset:$isReset")
            if (isReset) {
                //当天休息，跳过
                loopAlarmJumpOneDay(nextInstanceTime, alarm, true, alarmWorkTime)
                continue
            }
            found = whileCheckHoliday(alarm, holidayList, nextInstanceTime, alarmWorkTime)
            Log.d(TAG, "skip found:$found")
            if (mWhileLoopAlarm >= MAX_CURRENT_VALUE) {
                //循环一个最大周期未找到下次响铃时间
                Log.e(TAG, "whileLoopAlarm")
                found = true
            }
        }
        Log.i(TAG, "whileLoopAlarm nextInstanceTime:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
    }

    /**
     * 检查节假日
     */
    @JvmStatic
    private fun whileCheckHoliday(alarm: Alarm, holidayList: List<Holiday>?, nextInstanceTime: Calendar, alarmWorkTime: Calendar?): Boolean {
        var found = false
        if (alarm.holidaySwitch == ALARM_HOLIDAY_SWITCH_ON && holidayList != null) {
            val workdayHolidaySwitch = LegalHolidayUtil.queryWorkdayOrHoliday(nextInstanceTime, holidayList,
                    alarm.workdaySwitch, Calendar.getInstance(), alarm)
            if (workdayHolidaySwitch == LegalHolidayUtil.COLOR_TYPE_WORKDAY) {
                found = true
            }
            Log.d(TAG, "found:$found")
            if (!found) {
                //节假日，跳过
                loopAlarmJumpOneDay(nextInstanceTime, alarm, true, alarmWorkTime)
            }
        } else {
            found = true
        }
        return found
    }


    /**
     * 轮班闹钟跳过一天，设置为下一天的响铃时间
     * @param nextInstanceTime 下次响铃时间
     * @param alarm 闹钟信息
     * @param isAdd true 加一天 false 减一天
     */
    @JvmStatic
    private fun loopAlarmJumpOneDay(nextInstanceTime: Calendar, alarm: Alarm?, isAdd: Boolean, createDate: Calendar?) {
        if (alarm == null) {
            Log.e(TAG, "loopAlarmJumpOneDay alarm is null")
            return
        }
        if (isAdd) {
            nextInstanceTime.add(Calendar.DAY_OF_YEAR, 1)
        } else {
            nextInstanceTime.add(Calendar.DAY_OF_YEAR, -1)
        }
        val createTime = Calendar.getInstance()
        if (createDate == null) {
            createTime.timeInMillis = alarm.getmWorkdayUpdateTime()
        } else {
            createTime.timeInMillis = createDate.timeInMillis
        }
        //获取下一天的响铃时间
        val loopDay = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), createTime, nextInstanceTime)
        val time = computeLoopHourAndMinute(alarm.loopAlarmList, loopDay)
        nextInstanceTime[Calendar.HOUR_OF_DAY] = time.first
        nextInstanceTime[Calendar.MINUTE] = time.second
        Log.d(TAG, "loopAlarmJumpOneDay jump to loopDay :$loopDay  date:${Formatter.formatTime(nextInstanceTime.timeInMillis)}")
    }

    /**
     * 添加轮班闹钟
     * @param alarm 闹钟信息
     * @param loopList 子闹钟列表
     * @param isUpdateCreateTime  是否更新创建时间
     */
    @JvmStatic
    fun addLoopAlarm(context: Context?, alarm: Alarm?, enabled: Boolean, loopList: List<Alarm>, isUpdateCreateTime: Boolean): Boolean {
        if (context == null || alarm == null) {
            Log.e(TAG, "addLoopAlarm alarm or context is null")
            return false
        }
        kotlin.runCatching {
            val alarmId: Long
            val success: Boolean
            alarm.setmCloseOnceTimeNext(System.currentTimeMillis())
            val values = AlarmUtils.createContentValues(context, alarm, isUpdateCreateTime)
            values.put(ClockContract.Alarm.ENABLED, if (enabled) 1 else 0)
            val uri = AlarmUtils.alarmResolverInsert(context.contentResolver, ClockContract.ALARM_CONTENT_URI, values)
            PlatformUtils.sUpdateType = PlatformUtils.UPDATE_TYPE_OPERATION_NEW_ALARM
            success = uri != null
            //添加主轮班闹钟
            if (success) {
                alarmId = ContentUris.parseId(uri)
                alarm.id = alarmId
                if (enabled) {
                    AlarmUtils.enableAlarm(context, alarm, false)
                }
                //添加子闹钟
                for (subAlarm in loopList) {
                    subAlarm.setmLoopID(alarmId.toInt())
                    saveSubLoopAlarm(context, subAlarm)
                }
            }
            return success
        }
        return false
    }

    /**
     * 添加轮班闹钟子闹钟
     */
    @JvmStatic
    fun saveSubLoopAlarm(context: Context?, list: List<Alarm>, alarmId: Int) {
        if (context == null) {
            Log.e(TAG, "saveSubLoopAlarm context is null")
            return
        }
        for (subAlarm in list) {
            subAlarm.setmLoopID(alarmId)
            saveSubLoopAlarm(context, subAlarm)
        }
    }

    /**
     * 保存子闹钟
     */
    @JvmStatic
    fun saveSubLoopAlarm(context: Context?, subAlarm: Alarm?) {
        if (context == null || subAlarm == null) {
            Log.e(TAG, "saveSubLoopAlarm context is null")
            return
        }
        val alarmValues = AlarmUtils.createContentValues(context, subAlarm, false)
        alarmValues.put(ClockContract.Alarm.ENABLED, if (subAlarm.isEnabled) 1 else 0)
        alarmValues.put(ClockContract.Alarm.MESSAGE, "")
        AlarmUtils.alarmResolverInsert(context.contentResolver, ClockContract.ALARM_CONTENT_URI, alarmValues, false)
    }

    /**
     * 删除轮班闹钟关联闹钟
     */
    @JvmStatic
    fun deleteLoopAlarm(context: Context?, alarm: Alarm?) {
        if (context == null || alarm == null) {
            Log.e(TAG, "deleteLoopAlarm context or alarm is null")
            return
        }
        kotlin.runCatching {
            val loopAlarmList = getLoopAlarms(context, alarm.id.toInt())
            for (loopAlarm in loopAlarmList) {
                deleteAlarm(context, loopAlarm)
            }
        }
    }

    /**
     * 删除闹钟
     */
    @JvmStatic
    fun deleteAlarm(context: Context?, alarm: Alarm?) {
        if (context == null || alarm == null) {
            Log.e(TAG, "deleteAlarm context or alarm is null")
            return
        }
        Log.d(TAG, "deleteLoopAlarm  alarm:$alarm")
        kotlin.runCatching {
            val operations = java.util.ArrayList<ContentProviderOperation>()
            val uri = ContentUris.withAppendedId(ClockContract.ALARM_CONTENT_URI, alarm.id)
            operations.add(ContentProviderOperation.newDelete(uri).build())
            Utils.applyDbOperations(context, operations)
        }
    }

    /**
     * 获取闹钟列表轮班闹钟文本
     * @param alarm 闹钟信息
     * @param nextTime 下次响铃时间
     */
    @JvmStatic
    fun loopAlarmTypeString(context: Context?, alarm: Alarm?, nextTime: Long): Pair<String, String> {
        if (context == null || alarm == null) {
            Log.e(TAG, "loopAlarmTypeString context or alarm is null")
            return Pair("", "")
        }
        val dayDiff = Utils.getDayDiff(nextTime)
        val dateTimeString = Utils.getLocaleDateMDFormat(context, nextTime)
        val openDateString = if (dayDiff == 0) {
            context.getString(R.string.today)
        } else if (dayDiff == 1) {
            context.getString(R.string.tomorrow)
        } else {
            dateTimeString
        }
        val daySeq: String = getLoopAlarmLoopDayStr(alarm, context, nextTime)
        val ringTimeStr = if (dayDiff == 0) {
            context.getString(R.string.loop_alarm_next_ring_alarm, openDateString)
        } else if (dayDiff == 1) {
            context.getString(R.string.loop_alarm_next_ring_alarm, openDateString)
        } else {
            context.getString(R.string.loop_alarm_next_ring_alarm, openDateString)
        }
        return Pair(loopAlarmInfoText(context, alarm), "$ringTimeStr $daySeq")
    }

    /**
     * 轮班第几天文本
     */
    @JvmStatic
    fun getLoopAlarmLoopDayStr(alarm: Alarm?, context: Context?, nextTime: Long): String {
        if (context == null || alarm == null) {
            Log.e(TAG, "getLoopAlarmLoopDayStr context or alarm is null")
            return ""
        }
        val create = Calendar.getInstance()
        create.timeInMillis = alarm.getmWorkdayUpdateTime()
        val nowTime = Calendar.getInstance()
        nowTime.timeInMillis = nextTime
        val loopDay = computeLoopAlarmDay(alarm.getmLoopCycleDays(), alarm.getmLoopDay(), create, nowTime)
        return context.resources.getQuantityString(R.plurals.loop_alarm_cycle_day, loopDay, loopDay)
    }

    /**
     * 获取闹钟列表轮班闹钟文本
     */
    @JvmStatic
    fun loopAlarmInfoText(context: Context?, alarm: Alarm?): String {
        if (context == null || alarm == null) {
            Log.e(TAG, "loopAlarmInfoText context or alarm is null")
            return ""
        }
        val alarmStr = context.resources.getQuantityString(R.plurals.loop_count_number, alarm.getmLoopCycleDays(),
                alarm.getmLoopCycleDays(), alarm.getmLoopWorkDays(), alarm.getmLoopWorkDays())
        val loopTypeStr = context.getString(R.string.loop_type_alarm)
        if (alarm.holidaySwitch == 1 && !DeviceUtils.isExpVersion(context)) {
            val holidayStr = context.getString(R.string.exclude_holiday)
            return "$loopTypeStr $alarmStr $holidayStr"
        }
        return "$loopTypeStr $alarmStr"
    }


    /**
     * 对比轮班闹钟列表是否有修改
     * @param newList 新列表
     * @param oldList 老列表
     * @return 修改的闹钟id
     */
    @JvmStatic
    fun contrastAlarmList(newList: List<Alarm>?, oldList: List<Alarm>?): String {
        val contraStr = StringBuffer()
        if (newList == null || oldList == null) {
            Log.i(TAG, "list null")
            return contraStr.toString()
        }
        val str = if (newList.size > oldList.size) {
            contrastAlarmListStr(oldList, newList)
        } else {
            contrastAlarmListStr(newList, oldList)
        }
        contraStr.append(str)
        return contraStr.toString()
    }


    /**
     * 对比当前闹钟时间或开关是否有修改
     */
    @JvmStatic
    private fun contrastAlarmListStr(newList: List<Alarm>, oldList: List<Alarm>): String {
        val contraStr = StringBuffer()
        for (i in newList.indices) {
            val newAlarm = newList[i]
            val oldAlarm = oldList[i]
            if (newAlarm.hour != oldAlarm.hour || newAlarm.minutes != oldAlarm.minutes || newAlarm.isEnabled != oldAlarm.isEnabled) {
                contraStr.append("${DatePickerUtils.SPLIT}$i")
            }
        }
        return contraStr.toString()
    }

    /**
     * 更新主闹钟响铃时间
     * 响铃获取切换时区等重置闹钟的情况或重新计算闹钟时间，设置下次响铃时间后需把时间更新到轮班主闹钟上
     * @param alarmSchedule 响铃时间
     */
    @JvmStatic
    fun updateLoopAlarmTime(alarmSchedule: AlarmSchedule) {
        kotlin.runCatching {
            if (alarmSchedule.alarm == null) {
                val alarm = AlarmUtils.getAlarm(AlarmClockApplication.getInstance(), alarmSchedule.alarmId)
                alarmSchedule.alarm = alarm
            }
            if (alarmSchedule.alarm != null && alarmSchedule.alarm.getmLoopSwitch() == ALARM_HOLIDAY_SWITCH_ON) {
                val alarm = alarmSchedule.alarm
                val scheduleTime = Calendar.getInstance()
                //过滤稍后提醒时间
                if (alarmSchedule.alarmState != ClockContract.Schedule.SNOOZE_STATE) {
                    scheduleTime.timeInMillis = alarmSchedule.time
                    val hour = scheduleTime.get(Calendar.HOUR_OF_DAY)
                    val minute = scheduleTime.get(Calendar.MINUTE)
                    Log.i(TAG, "updateLoopAlarmTime hour:$hour  minute:$minute")
                    AlarmUtils.updateAlarmTime(AlarmClockApplication.getInstance(), alarm.id, hour, minute)
                }
            }
        }
    }

    /**
     * 包装搬家轮班闹钟子闹钟数据
     * @param id 主闹钟id
     * @param reset 休息日
     * @param context
     * @return 包装后的数据
     */
    @JvmStatic
    fun buildLoopAlarmBackupStr(id: Int, reset: String, context: Context?): String {
        if (context == null) {
            Log.e(TAG, "buildLoopAlarmBackupStr context is null")
            return ""
        }
        val backup = StringBuilder()
        kotlin.runCatching {
            val loopAlarmList = getLoopAlarms(context, id)
            for (alarm in loopAlarmList) {
                val enable = if (alarm.isEnabled) {
                    1
                } else {
                    0
                }
                backup.append("${alarm.hour},${alarm.minutes},$enable,${alarm.getmLoopAlarmNumber()}$BACKUP_PLUGIN_BUILD_SPIT_STR_2")
            }
            backup.append("$BACKUP_PLUGIN_BUILD_SPIT_STR$reset")
        }
        return backup.toString()
    }

    /**
     * @param backupStr 恢复的数据
     * @param id  主闹钟id
     */
    @JvmStatic
    fun parserLoopAlarmBackupStr(backupStr: String, id: Int, parentAlarm: Alarm?): List<Alarm> {
        Log.i(TAG, "parserLoopAlarmBackupStr:$backupStr")
        val loopAlarmList = mutableListOf<Alarm>()
        if (parentAlarm == null) {
            Log.e(TAG, "parserLoopAlarmBackupStr alarm is null")
            return loopAlarmList
        }
        kotlin.runCatching {
            val backList = backupStr.split(BACKUP_PLUGIN_BUILD_SPIT_STR)
            val details = backList[0]
            parentAlarm.setmLoopRestDays(backList[1])
            val alarms = details.split(BACKUP_PLUGIN_BUILD_SPIT_STR_2)
            for (alarmStr in alarms) {
                if (!TextUtils.isEmpty(alarmStr)) {
                    val alarm = Alarm()
                    val timeStr = alarmStr.split(",")
                    alarm.hour = timeStr[0].toInt()
                    alarm.minutes = timeStr[1].toInt()
                    alarm.isEnabled = timeStr[2].toInt() == 1
                    alarm.setmLoopAlarmNumber(timeStr[STRING_SPIT_NUMBER_3].toInt())
                    loopAlarmList.add(alarm)
                }
            }
        }
        return loopAlarmList
    }

    /**
     * 轮班闹钟提示弹窗
     */
    @JvmStatic
    fun showLoopAlarmTips(mWorkdDayImageRight: ImageView?, context: Context?): COUIToolTips? {
        var tips: COUIToolTips? = null
        mWorkdDayImageRight?.let {
            context?.run {
                if (context is Activity && !context.isFinishing) {
                    tips = COUIToolTips(this).apply {
                        setDismissOnTouchOutside(false)
                        setContent(resources.getString(R.string.add_loop_type_alarm))
                        showWithDirection(it, COUIToolTips.ALIGN_BOTTOM)
                    }
                    PrefUtils.putBoolean(context, PrefUtils.LOOP_ALARM_TIPS, PrefUtils.LOOP_ALARM_TIPS_KEY, true)
                }
            }
        }
        return tips
    }

    /**
     * 工作日tab提示弹窗
     */
    @JvmStatic
    fun showWorkDayAlarmTips(view: View?, context: Context?): COUIToolTips? {
        var tips: COUIToolTips? = null
        view?.let {
            context?.run {
                if (context is Activity && !context.isFinishing) {
                    tips = COUIToolTips(this).apply {
                        setDismissOnTouchOutside(false)
                        setContent(resources.getString(R.string.add_loop_type_alarm))
                        showWithDirection(it, COUIToolTips.ALIGN_BOTTOM)
                    }
                    PrefUtils.putBoolean(context, PrefUtils.WORK_DAY_ALARM_TIPS, PrefUtils.WORK_DAY_ALARM_TIPS_KEY, true)
                }
            }
        }
        return tips
    }

    /**
     * 检查轮班闹钟子列表是否只有一个开启
     * param loopAlarmList 轮班闹钟列表
     * param openCount 需要打开的数量
     * return 是否有openCount开启
     */
    @JvmStatic
    fun checkLoopAlarmResetDays(loopAlarmList: List<Alarm>, openCount: Int): Boolean {
        if (loopAlarmList.isEmpty()) {
            return false
        }
        var index = 0
        for (itemAlarm in loopAlarmList) {
            if (itemAlarm.isEnabled) {
                index++
            }
        }
        return index == openCount
    }
}