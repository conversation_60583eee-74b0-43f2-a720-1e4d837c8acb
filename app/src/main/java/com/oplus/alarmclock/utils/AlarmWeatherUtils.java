/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmWeatherUtils.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/7/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/7/15     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;

import com.oplus.clock.common.utils.Log;
import com.oplus.servicesdk.WeatherRequest;
import com.oplus.weatherservicesdk.BaseCallBack;
import com.oplus.weatherservicesdk.Utils.WeatherServiceVersionUtils;
import com.oplus.weatherservicesdk.model.SecureSettingsData;
import com.oplus.weatherservicesdk.service.WeatherBaseDataTask;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Random;

import static com.coloros.alarmclock.widget.WidgetUtils.METHOD_GET_LOCATION_CITY_WEATHER_INFO;

public class AlarmWeatherUtils implements Runnable {

    public static final int WEATHER_TYPE_NONE = -1;
    public static final int WEATHER_DEFAULT_ALERT_RES = ChannelManager.INSTANCE.getLightOSUtils().getWeatherDefaultResId();
    private static final String TAG = "AlarmWeatherUtils";
    private static final long GET_WEATHER_TIME_OUT = 500L;
    private static final int LENGTH_2 = 2;
    private static final int INDEX_1 = 1;

    private static final String WEATHER_ALARM_DEFAULT = "weather_alarm_default.ogg";
    private static final String WEATHER_ALARM_CLOUD = "weather_alarm_cloud.ogg";
    private static final String WEATHER_ALARM_CLOUD2 = "weather_alarm_cloud2.ogg";
    private static final String WEATHER_ALARM_CLOUD3 = "weather_alarm_cloud3.ogg";
    private static final String WEATHER_ALARM_CLOUD4 = "weather_alarm_cloud4.ogg";
    private static final String WEATHER_ALARM_HAZE = "weather_alarm_haze.ogg";
    private static final String WEATHER_ALARM_HAZE2 = "weather_alarm_haze2.ogg";
    private static final String WEATHER_ALARM_RAIN = "weather_alarm_rain.ogg";
    private static final String WEATHER_ALARM_RAIN2 = "weather_alarm_rain2.ogg";
    private static final String WEATHER_ALARM_SNOW = "weather_alarm_snow.ogg";
    private static final String WEATHER_ALARM_SNOW2 = "weather_alarm_snow2.ogg";
    private static final String WEATHER_ALARM_SUN = "weather_alarm_sun.ogg";
    private static final String WEATHER_ALARM_SUN2 = "weather_alarm_sun2.ogg";
    private static final String WEATHER_ALARM_SUN3 = "weather_alarm_sun3.ogg";
    private static final String WEATHER_ALARM_SUN4 = "weather_alarm_sun4.ogg";
    private static final String WEATHER_ALARM_THUNDERSTORM = "weather_alarm_thunderstorm.ogg";
    private static final String WEATHER_ALARM_THUNDERSTORM2 = "weather_alarm_thunderstorm2.ogg";
    private static final String WEATHER_ALARM_THUNDERSTORM3 = "weather_alarm_thunderstorm3.ogg";
    private static final String WEATHER_ALARM_WIND = "weather_alarm_wind.ogg";
    private static final String WEATHER_ALARM_WIND2 = "weather_alarm_wind2.ogg";
    private static final String[] WEATHER_CLOUD = {WEATHER_ALARM_CLOUD, WEATHER_ALARM_CLOUD2, WEATHER_ALARM_CLOUD3, WEATHER_ALARM_CLOUD4};
    private static final String[] WEATHER_HAZE = {WEATHER_ALARM_HAZE, WEATHER_ALARM_HAZE2};
    private static final String[] WEATHER_RAIN = {WEATHER_ALARM_RAIN, WEATHER_ALARM_RAIN2};
    private static final String[] WEATHER_SNOW = {WEATHER_ALARM_SNOW, WEATHER_ALARM_SNOW2};
    private static final String[] WEATHER_SUN = {WEATHER_ALARM_SUN, WEATHER_ALARM_SUN2, WEATHER_ALARM_SUN3, WEATHER_ALARM_SUN4};
    private static final String[] WEATHER_THUNDERSTORM = {WEATHER_ALARM_THUNDERSTORM, WEATHER_ALARM_THUNDERSTORM2, WEATHER_ALARM_THUNDERSTORM3};
    private static final String[] WEATHER_WIND = {WEATHER_ALARM_WIND, WEATHER_ALARM_WIND2};

    private HashMap<Integer, String> mWeatherAlertMap;

    private boolean mLoadWeatherComplete = false;
    private Handler mHandler = new Handler();
    private LoadWeatherTypeListener mListener;

    public AlarmWeatherUtils(LoadWeatherTypeListener listener) {
        this.mListener = listener;
    }

    public void run() {
        if (!this.mLoadWeatherComplete) {
            this.mLoadWeatherComplete = true;
            if (this.mListener != null) {
                Object weatherAlertResOrName = Utils.isAboveR() ? WEATHER_ALARM_DEFAULT : WEATHER_DEFAULT_ALERT_RES;
                this.mListener.onLoadComplete(false, weatherAlertResOrName);
            }
        }
    }

    public void freed() {
        mListener = null;
    }

    private void onLoadWeatherComplete(boolean success, boolean isNewDataSource, int weatherType) {
        if (!mLoadWeatherComplete) {
            this.mLoadWeatherComplete = true;
            this.mHandler.removeCallbacks(this);
            if (this.mListener != null) {
                //R上使用文件名，然后去系统读取对应铃声的uri，Q上使用本地铃声文件资源id
                Object weatherAlertResOrName = Utils.isAboveR() ? getRandomWeatherAlertStringName(weatherType)
                        : ChannelManager.INSTANCE.getLightOSUtils().getWeatherAlertResIdByWeatherType(weatherType, isNewDataSource);
                this.mListener.onLoadComplete(success, weatherAlertResOrName);
            }
        }
    }

    public void getWeatherInfo(Context context) {
        if (WeatherServiceVersionUtils.isCommonWeatherServiceExist(context)) {
            this.getLocalWeatherInfoNew(context.getApplicationContext());
        } else {
            this.getLocalWeatherInfoOld(context);
        }
    }

    private void getLocalWeatherInfoNew(Context context) {
        Log.d(TAG, "get weather new");

        try {
            this.mLoadWeatherComplete = false;
            this.mHandler.postDelayed(this, GET_WEATHER_TIME_OUT);
            final WeatherRequest weatherRequest = new WeatherRequest()
                    .setRequestID(String.valueOf(System.currentTimeMillis()))
                    .setCallMethodName(METHOD_GET_LOCATION_CITY_WEATHER_INFO)
                    .setPackageName(ClockConstant.CLOCK_PACKAGE)
                    .setParams(null);
            WeatherBaseDataTask locationWeatherBaseDataTask = new WeatherBaseDataTask(SecureSettingsData.class,
                    context, weatherRequest, new BaseCallBack<SecureSettingsData>() {
                public void onSuccess(SecureSettingsData secureSettingsData) {
                    Log.d(TAG, "new weather onSuccess " + secureSettingsData);
                    if (secureSettingsData == null) {
                        onLoadWeatherComplete(false, true, WEATHER_TYPE_NONE);
                    } else {
                        onLoadWeatherComplete(true, true, secureSettingsData.weatherType);
                    }
                }

                public void onFail(String s) {
                    Log.e(TAG, "new weather onFail:" + s);
                    onLoadWeatherComplete(false, true, WEATHER_TYPE_NONE);
                }
            });
            locationWeatherBaseDataTask.startServiceRequest();
        } catch (Exception var4) {
            Log.e(TAG, "new weather Exception:" + var4);
            onLoadWeatherComplete(false, true, WEATHER_TYPE_NONE);
        }
    }

    private void getLocalWeatherInfoOld(Context context) {
        String weatherInfo = Settings.Secure.getString(context.getContentResolver(), "oplus_weather_info");
        if (TextUtils.isEmpty(weatherInfo)) {
            weatherInfo = Settings.Secure.getString(context.getContentResolver(), "oppo_weather_info");
        }
        Log.d(TAG, "old weatherInfo: " + weatherInfo);
        int type = WEATHER_TYPE_NONE;
        if (!TextUtils.isEmpty(weatherInfo)) {
            String[] temp = weatherInfo.split("::");
            if (temp.length > LENGTH_2) {
                String weatherType = temp[INDEX_1];
                if (!TextUtils.isEmpty(weatherType)) {
                    try {
                        weatherType = weatherType.trim();
                        Log.d(TAG, "old weather type str: " + weatherType);
                        type = Integer.parseInt(weatherType);
                    } catch (NumberFormatException var7) {
                        Log.d(TAG, "old weather type cast exception");
                        type = WEATHER_TYPE_NONE;
                    }
                }
            }
        }

        onLoadWeatherComplete(type != WEATHER_TYPE_NONE, false, type);
    }

    public static boolean isDynamicWeatherAlert(String alert) {
        return ClockConstant.ALARM_ALERT_DYNAMIC_WEATHER.equals(alert);
    }

    private String getWeatherAlertStringName(int weatherType) {
        initWeatherAlertMap();
        if (mWeatherAlertMap.containsKey(weatherType)) {
            return mWeatherAlertMap.get(weatherType);
        }
        return WEATHER_ALARM_DEFAULT;
    }

    /**
     * 根据天气类型获取这一类型下的随机天气名称
     * @param weatherType 天气类型
     * @return 天气名称
     */
    private String getRandomWeatherAlertStringName(int weatherType) {
        return switch (weatherType) {
            case Number.NUMBER_1,
                 Number.NUMBER_2,
                 Number.NUMBER_3,
                 Number.NUMBER_4,
                 Number.NUMBER_5,
                 Number.NUMBER_6,
                 Number.NUMBER_7,
                 Number.NUMBER_11,
                 Number.NUMBER_12,
                 Number.NUMBER_13,
                 Number.NUMBER_14,
                 Number.NUMBER_15,
                 Number.NUMBER_16,
                 Number.NUMBER_17,
                 Number.NUMBER_18,
                 Number.NUMBER_19,
                 Number.NUMBER_20,
                 Number.NUMBER_21 -> getWeatherNameByRandom(WEATHER_RAIN);
            case Number.NUMBER_8,
                 Number.NUMBER_9,
                 Number.NUMBER_10,
                 Number.NUMBER_22,
                 Number.NUMBER_23,
                 Number.NUMBER_24,
                 Number.NUMBER_25 -> getWeatherNameByRandom(WEATHER_THUNDERSTORM);
            case Number.NUMBER_26,
                 Number.NUMBER_27,
                 Number.NUMBER_28,
                 Number.NUMBER_29,
                 Number.NUMBER_30,
                 Number.NUMBER_31,
                 Number.NUMBER_32,
                 Number.NUMBER_33,
                 Number.NUMBER_34,
                 Number.NUMBER_35,
                 Number.NUMBER_36,
                 Number.NUMBER_37 -> getWeatherNameByRandom(WEATHER_SNOW);
            case Number.NUMBER_38,
                 Number.NUMBER_39,
                 Number.NUMBER_40,
                 Number.NUMBER_41,
                 Number.NUMBER_42,
                 Number.NUMBER_43,
                 Number.NUMBER_44,
                 Number.NUMBER_50,
                 Number.NUMBER_51,
                 Number.NUMBER_52,
                 Number.NUMBER_53 -> getWeatherNameByRandom(WEATHER_HAZE);
            case Number.NUMBER_45,
                 Number.NUMBER_46,
                 Number.NUMBER_47,
                 Number.NUMBER_48,
                 Number.NUMBER_49 -> getWeatherNameByRandom(WEATHER_WIND);
            case Number.NUMBER_54,
                 Number.NUMBER_55,
                 Number.NUMBER_60,
                 Number.NUMBER_61 -> getWeatherNameByRandom(WEATHER_SUN);
            case Number.NUMBER_56,
                 Number.NUMBER_57,
                 Number.NUMBER_58,
                 Number.NUMBER_59,
                 Number.NUMBER_62,
                 Number.NUMBER_63,
                 Number.NUMBER_64,
                 Number.NUMBER_65,
                 Number.NUMBER_66,
                 Number.NUMBER_67 -> getWeatherNameByRandom(WEATHER_CLOUD);
            default -> WEATHER_ALARM_DEFAULT;
        };
    }

    // 天气和铃声映射关系：alm 60010 里查看铃声映射表
    private void initWeatherAlertMap() {
        if (mWeatherAlertMap == null) {
            mWeatherAlertMap = new HashMap<>();
            mWeatherAlertMap.put(Number.NUMBER_0, WEATHER_ALARM_DEFAULT);
            mWeatherAlertMap.put(Number.NUMBER_1, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_2, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_3, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_4, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_5, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_6, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_7, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_8, WEATHER_ALARM_THUNDERSTORM2);
            mWeatherAlertMap.put(Number.NUMBER_9, WEATHER_ALARM_THUNDERSTORM2);
            mWeatherAlertMap.put(Number.NUMBER_10, WEATHER_ALARM_THUNDERSTORM2);
            mWeatherAlertMap.put(Number.NUMBER_11, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_12, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_13, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_14, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_15, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_16, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_17, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_18, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_19, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_20, WEATHER_ALARM_RAIN2);
            mWeatherAlertMap.put(Number.NUMBER_21, WEATHER_ALARM_RAIN);
            mWeatherAlertMap.put(Number.NUMBER_22, WEATHER_ALARM_THUNDERSTORM);
            mWeatherAlertMap.put(Number.NUMBER_23, WEATHER_ALARM_THUNDERSTORM);
            mWeatherAlertMap.put(Number.NUMBER_24, WEATHER_ALARM_THUNDERSTORM3);
            mWeatherAlertMap.put(Number.NUMBER_25, WEATHER_ALARM_THUNDERSTORM3);
            mWeatherAlertMap.put(Number.NUMBER_26, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_27, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_28, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_29, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_30, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_31, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_32, WEATHER_ALARM_SNOW);
            mWeatherAlertMap.put(Number.NUMBER_33, WEATHER_ALARM_SNOW2);
            mWeatherAlertMap.put(Number.NUMBER_34, WEATHER_ALARM_SNOW2);
            mWeatherAlertMap.put(Number.NUMBER_35, WEATHER_ALARM_SNOW2);
            mWeatherAlertMap.put(Number.NUMBER_36, WEATHER_ALARM_SNOW2);
            mWeatherAlertMap.put(Number.NUMBER_37, WEATHER_ALARM_SNOW2);
            mWeatherAlertMap.put(Number.NUMBER_38, WEATHER_ALARM_HAZE);
            mWeatherAlertMap.put(Number.NUMBER_39, WEATHER_ALARM_HAZE);
            mWeatherAlertMap.put(Number.NUMBER_40, WEATHER_ALARM_HAZE);
            mWeatherAlertMap.put(Number.NUMBER_41, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_42, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_43, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_44, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_45, WEATHER_ALARM_WIND);
            mWeatherAlertMap.put(Number.NUMBER_46, WEATHER_ALARM_WIND);
            mWeatherAlertMap.put(Number.NUMBER_47, WEATHER_ALARM_WIND);
            mWeatherAlertMap.put(Number.NUMBER_48, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_49, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_50, WEATHER_ALARM_HAZE);
            mWeatherAlertMap.put(Number.NUMBER_51, WEATHER_ALARM_HAZE);
            mWeatherAlertMap.put(Number.NUMBER_52, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_53, WEATHER_ALARM_HAZE2);
            mWeatherAlertMap.put(Number.NUMBER_54, WEATHER_ALARM_SUN2);
            mWeatherAlertMap.put(Number.NUMBER_55, WEATHER_ALARM_SUN4);
            mWeatherAlertMap.put(Number.NUMBER_56, WEATHER_ALARM_CLOUD);
            mWeatherAlertMap.put(Number.NUMBER_57, WEATHER_ALARM_CLOUD2);
            mWeatherAlertMap.put(Number.NUMBER_58, WEATHER_ALARM_CLOUD3);
            mWeatherAlertMap.put(Number.NUMBER_59, WEATHER_ALARM_CLOUD4);
            mWeatherAlertMap.put(Number.NUMBER_60, WEATHER_ALARM_SUN3);
            mWeatherAlertMap.put(Number.NUMBER_61, WEATHER_ALARM_SUN3);
            mWeatherAlertMap.put(Number.NUMBER_62, WEATHER_ALARM_WIND);
            mWeatherAlertMap.put(Number.NUMBER_63, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_64, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_65, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_66, WEATHER_ALARM_WIND2);
            mWeatherAlertMap.put(Number.NUMBER_67, WEATHER_ALARM_WIND2);
        }
    }

    /**
     * 随机获取同一类型天气的随机铃声
     *
     * @param weathers 同一类型下天气集
     * @return 随机天气铃声
     */
    private String getWeatherNameByRandom(String[] weathers) {
        int index = 0;
        try {
            index = new Random().nextInt(weathers.length);
            Log.d(TAG, "getWeatherNameByRandom index:" + index + " weathers:" + Arrays.toString(weathers));
        } catch (Exception e) {
            Log.e(TAG, "getWeatherNameByRandom Exception:" + e.getMessage());
        }
        return weathers[index];
    }

    public interface LoadWeatherTypeListener {
        void onLoadComplete(boolean success, Object weatherAlertResOrName);
    }
}
