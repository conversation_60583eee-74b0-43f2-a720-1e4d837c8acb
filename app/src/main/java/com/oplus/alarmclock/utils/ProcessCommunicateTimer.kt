/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ProcessCommunicateTimer.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.content.ServiceConnection
import android.content.ComponentName
import android.content.Intent
import android.content.ContentValues
import android.os.Bundle
import android.os.IBinder
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.coloros.refusedesktop.model.TimerEntity
import com.coloros.widget.commondata.Constants
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.alarmclock.timer.TimerFloatingViewService
import com.oplus.alarmclock.timer.TimerService
import com.oplus.alarmclock.timer.TimerService.TimerBinder
import com.oplus.alarmclock.timer.data.OplusTimer
import com.oplus.clock.common.utils.Log
import kotlinx.coroutines.*

class ProcessCommunicateTimer(private val mContext: Context?) {
    companion object {
        const val TIMER_STATUS = "timer_status"
        const val TIMER_TOTAL_TIME = "timer_total_time"
        const val TIMER_CURRENT_TIME = "timer_current_time"
        const val TIMER_NAME = "timer_name"
        const val TIMER_SELECTED_POSITION = "timer_selected_position"
        const val POSITION_NO_SELECTED = -1

        /**
         * 避免引擎和时钟发版不一致导致状态不一致
         */
        const val POSITION_NOT_VALUE = -2
        private const val TAG = "ProcessCommunicateTimer"
        private const val TIMER_RING_PATH = "key_ring_path"
        private const val TIMER_RING_TITLE = "key_ring_title"
        private const val TIMER_NAME_SP = "timer_desc_time"
        private const val TIMER_CURRENT_TIME_SP = "timer_data"
        private const val ACTION_STATUS = "action_status"
        private const val TIMER0_REMAIN_TIME = "timer0_remain"
        private const val TIMER_DRAGONFLY_TIME = "timer_dragonfly_time"
        private const val SYNC_TIME = 0
        private const val END_TIME = 1
        private const val STATUS_STOP = 0
        private const val STATUS_START = 1
        private const val STATUS_PAUSE = 2
        private const val SP_NAME = "shared_prefs_alarm_app"
    }

    private var mDefaultTimerRing: String? = null
    private var mTimerService: TimerService? = null
    private var mBundle: Bundle? = null
    private var mDefaultTimerName: String? = null
    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            mTimerService = (service as TimerBinder).service
            handleTimer(mBundle)
        }

        override fun onServiceDisconnected(name: ComponentName) {
            mTimerService = null
        }
    }

    fun handleTimer(bundle: Bundle?) {
        mContext?.run {
            mBundle = bundle
            if (mTimerService == null) {
                startTimerService(this)
            } else {
                bundle?.let {
                    when (val status = it.getInt(ACTION_STATUS, 0)) {
                        SYNC_TIME -> onSyncTime(this, it)
                        END_TIME -> onEndTime(this, it)
                        else -> Log.d(TAG, "handleTimer else: $status")
                    }
                }
            }
        } ?: Log.d(TAG, "handleTimer context is null")
    }

    private fun onSyncTime(context: Context, bundle: Bundle) {
        val status = bundle.getInt(TIMER_STATUS, STATUS_STOP)
        val currentTime = bundle.getLong(TIMER_CURRENT_TIME, 0)
        val totalTime = bundle.getLong(TIMER_TOTAL_TIME, 0)
        val name = bundle.getString(TIMER_NAME) ?: ""
        val position = bundle.getInt(TIMER_SELECTED_POSITION, POSITION_NOT_VALUE)
        if ((currentTime == 0L) && (totalTime == 0L)) {
            return
        }
        var ringPath = getRingPath()
        var ringTitle = getRingTitle()
        if (TextUtils.isEmpty(ringPath) || TextUtils.isEmpty(ringTitle)) {
            val ring = TimerUtils.getDefaultTimerRingtoneUri(context).toString()
            mDefaultTimerRing = mDefaultTimerRing ?: ring
            ringPath = ring
            ringTitle = ""
        }
        mTimerService?.run {
            setTimerRingUri(ringPath, 0)
            setTimerRingName(ringTitle, 0)
            setTotalTime(totalTime, currentTime, 0)
            setTimerName(name, 0)
        }
        setStatusToService(status)
        when (status) {
            STATUS_START -> setTimeStartOrPause(context, currentTime, name, true)
            STATUS_PAUSE -> setTimeStartOrPause(context, currentTime, name, false)
            STATUS_STOP -> setTimeStop()
            else -> Log.d(TAG, "onSyncTime else status: $status")
        }
        PrefUtils.putBoolean(
            context, SP_NAME, TimerConstant.TIMER_NEED_TO_ALARM_PREFERENCE, true
        )
        sendSyncBroadcast(context, saveTimeToSp(context, bundle))
        saveSelectedTimerToDb(context, position, name)
    }

    private fun onEndTime(context: Context, bundle: Bundle) {
        val userId = AppPlatformUtils.getCurrentUser()
        var ring = bundle.getString(TIMER_RING_PATH, "")
        var ringTitle = bundle.getString(TIMER_RING_TITLE, "")
        if (TextUtils.isEmpty(ring) || TextUtils.isEmpty(ringTitle)) {
            mDefaultTimerRing =
                mDefaultTimerRing ?: TimerUtils.getDefaultTimerRingtoneUri(context).toString()
            ring = mDefaultTimerRing
            ringTitle = ""
        }
        val time = bundle.getLong(TIMER_TOTAL_TIME, 0)
        saveTotalTime(context, time)
        TimerFloatingViewService.startTimer(context, "0", "Timer", userId, ring, ringTitle)
    }

    private fun saveTimeToSp(context: Context, bundle: Bundle): TimerEntity {
        val status = bundle.getInt(
            TIMER_STATUS, STATUS_STOP
        )
        val currentTime = if (status == STATUS_STOP) 0 else bundle.getLong(
            TIMER_CURRENT_TIME, 0
        )
        val totalTime = if (status == STATUS_STOP) 0 else bundle.getLong(
            TIMER_TOTAL_TIME, 0
        )
        val position = bundle.getInt(TIMER_SELECTED_POSITION, POSITION_NOT_VALUE)
        val defaultName = getDefaultTimerName(context)
        val timeName = bundle.getString(TIMER_NAME, defaultName)
        val ringPath = getRingPath()
        val ringTitle = getRingTitle()
        PrefUtils.putInt(
            context, SP_NAME, TIMER_STATUS, status
        )
        PrefUtils.putLong(
            context, SP_NAME, TIMER_CURRENT_TIME_SP, currentTime
        )
        PrefUtils.putLong(
            context, SP_NAME, TIMER0_REMAIN_TIME, currentTime
        )
        PrefUtils.putLong(
            context, SP_NAME, TIMER_TOTAL_TIME, totalTime
        )
        PrefUtils.putString(
            context, SP_NAME, TIMER_NAME_SP, timeName
        )
        Log.d(
            TAG, "saveTimeToSp status:$status,currentTime:$currentTime,totalTime$totalTime"
        )
        return TimerEntity(
            currentTime, totalTime, timeName, position, status, ringPath, ringTitle, 0, ""
        )
    }

    private fun startTimerService(context: Context) {
        val intent = Intent(context, TimerService::class.java)
        context.applicationContext.bindService(intent, mConnection, Service.BIND_AUTO_CREATE)
        context.startService(intent)
    }

    private fun saveTotalTime(context: Context, time: Long) {
        val name = AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP
        PrefUtils.putLong(context, name, TIMER_DRAGONFLY_TIME, time)
    }

    private fun sendSyncBroadcast(context: Context, data: TimerEntity) {
        Log.d(TAG, data.toString())
        val intent = Intent(TimerConstant.SYNCHRONIZE_TIME_BROADCAST)
        intent.putExtra(TIMER_STATUS, data.mStatus)
        intent.putExtra(TIMER_CURRENT_TIME, data.mCurrentTime)
        intent.putExtra(TIMER_TOTAL_TIME, data.mTotalTime)
        intent.putExtra(TIMER_NAME, data.mName)
        intent.putExtra(TIMER_SELECTED_POSITION, data.mSelectedPosition)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }

    private fun getDefaultTimerName(context: Context): String {
        if (mDefaultTimerName == null) {
            mDefaultTimerName = context.resources.getString(R.string.timer_title)
        }
        return mDefaultTimerName ?: context.resources.getString(R.string.timer_title)
    }

    private fun getRingPath(): String {
        val ringPath = mTimerService?.getTimerRingUri(0) ?: ""
        return if (TextUtils.isEmpty(ringPath)) "" else ringPath
    }

    private fun getRingTitle(): String {
        val ringName = mTimerService?.getTimerRingName(0) ?: ""
        return if (TextUtils.isEmpty(ringName)) "" else ringName
    }

    private fun setStatusToService(status: Int) {
        mTimerService?.setStart(0, status == STATUS_START)
        mTimerService?.setPause(0, status == STATUS_PAUSE)
    }

    /**
     * 因为闪回初始化是异步的，加listener确保后续数据正常推送给闪回
     */
    private fun initFlashBack(
        context: Context,
        currentTime: Long,
        name: String?,
        createListener: FlashBackHelper.CreateListener
    ): Boolean {
        if (FlashBackHelper.getInstance().haveManager(Constants.CLOCK_TIMER_FLASHBACK_KEY)) {
            return false
        } else {
            context.run {
                resources.run {
                    val defaultTitle = getString(R.string.timer_title)
                    val title =
                        if (TextUtils.isEmpty(name) || (defaultTitle == name)) defaultTitle else "$defaultTitle | $name"
                    FlashBackHelper.initManager(
                        Constants.CLOCK_TIMER_FLASHBACK_KEY,
                        Formatter.getTimerStr(currentTime, context),
                        title,
                        getString(R.string.text_timer_btn_pause),
                        getColor(R.color.flash_back_bottom_left_red_color, null),
                        getColor(R.color.flash_back_bottom_content_white_color, null),
                        getString(R.string.cancel, null),
                        getColor(R.color.flash_back_bottom_right_gray_color, null),
                        getColor(R.color.flash_back_bottom_content_black_color, null),
                        createListener
                    )
                }
            }
            return true
        }
    }

    private fun setTimeStartOrPause(context: Context, time: Long, name: String?, isStart: Boolean) {
        val shouldAddListener = initFlashBack(context, time, name) {
            sendDataToFlashBack(context, time, name, isStart)
        }
        if (isStart) {
            mTimerService?.startTimerByDragonfly(shouldAddListener)
        } else {
            mTimerService?.pauseTimerByDragonfly(shouldAddListener)
        }
        sendDataToFlashBack(context, time, name, isStart)
    }

    private fun setTimeStop() {
        FlashBackHelper.getInstance().releaseManager(Constants.CLOCK_TIMER_FLASHBACK_KEY)
        mTimerService?.stopTimerAlert(0)
        mTimerService?.stopTimer(0)
    }

    private fun getFlashBackTitle(context: Context, name: String?): String {
        return context.resources.run {
            val timerTitle = getString(R.string.timer_title)
            if (TextUtils.isEmpty(name) || (timerTitle == name)) {
                timerTitle
            } else {
                "$timerTitle | $name"
            }
        }
    }

    private fun sendDataToFlashBack(context: Context, time: Long, name: String?, isStart: Boolean) {
        if (isStart) {
            FlashBackHelper.getInstance().setTimerPlay(Constants.CLOCK_TIMER_FLASHBACK_KEY)
        } else {
            val timeStr = Formatter.getTimerStr(time, context)
            FlashBackHelper.getInstance().sendData(Constants.CLOCK_TIMER_FLASHBACK_KEY, timeStr)
            FlashBackHelper.getInstance().setTimerPause(Constants.CLOCK_TIMER_FLASHBACK_KEY)
        }
        val title = getFlashBackTitle(context, name)
        FlashBackHelper.getInstance().setContent(Constants.CLOCK_TIMER_FLASHBACK_KEY, title)
    }

    private fun saveSelectedTimerToDb(context: Context, position: Int, name: String) {
        if (position >= 0) {
            CoroutineScope(Dispatchers.IO).launch {
                setSelectedTimer(context, position, name)
            }
        }
    }

    private fun setSelectedTimer(context: Context, position: Int, name: String) {
        var isSelected = false
        getSelectedTimer(context).forEach {
            if (position == it.timerIndex) {
                isSelected = true
            } else {
                saveTimerToDb(context, it.timerIndex, 0)
                Log.d(
                    TAG,
                    "setSelectedTimer timerIndex: ${it.timerIndex} description:${it.description} selected:0"
                )
            }
        }
        if (isSelected) {
            Log.d(
                TAG, "timerIndex: $position description:$name has selected"
            )
        } else {
            saveTimerToDb(context, position, 1)
            Log.d(
                TAG, "setSelectedTimer timerIndex: $position description:$name selected:1"
            )
        }
    }

    @SuppressLint("Range")
    private fun getSelectedTimer(context: Context): MutableList<OplusTimer> {
        val where = "${ClockContract.TimerTableColumns.SELECTED} == 1"
        val cursor =
            context.contentResolver.query(ClockContract.TIMER_CONTENT_URI, null, where, null, null)
        val list = arrayListOf<OplusTimer>()
        cursor?.run {
            while (moveToNext()) {
                list.add(OplusTimer().apply {
                    timerIndex = getInt(getColumnIndex(ClockContract.TimerTableColumns._ID))
                    description =
                        getString(getColumnIndex(ClockContract.TimerTableColumns.DESCRIPTION))
                })
            }
            close()
        }
        return list
    }

    private fun saveTimerToDb(context: Context, position: Int, selected: Int) {
        val where = "${ClockContract.TimerTableColumns._ID} == $position"
        val value = ContentValues().apply {
            put(ClockContract.TimerTableColumns.SELECTED, selected)
        }
        context.contentResolver.update(ClockContract.TIMER_CONTENT_URI, value, where, null)
    }
}