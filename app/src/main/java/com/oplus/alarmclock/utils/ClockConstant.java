/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.media.AudioManager;

public class ClockConstant {

    public static final String CLOCK_PACKAGE = "com.coloros.alarmclock";
    public static final String OPLUS_SAFE_PERMISSION = "oppo.permission.OPPO_COMPONENT_SAFE";

    /**
     * AlarmActivity and AlarmService (when unbound) listen for this broadcast intent so that other
     * applications can snooze the alarm (after ALARM_ALERT_ACTION and before ALARM_DONE_ACTION).
     */
    public static final String ALARM_SNOOZE_ACTION = "com.oplus.alarmclock.ALARM_SNOOZE";
    /**
     * AlarmActivity and AlarmService listen for this broadcast intent so that other applications
     * can dismiss the alarm (after ALARM_ALERT_ACTION and before ALARM_DONE_ACTION).
     */
    public static final String ALARM_DISMISS_ACTION = "com.oplus.alarmclock.ALARM_DISMISS";
    /**
     * A public action sent by AlarmService when AlarmStateManger has changed state.
     */
    public static final String ALARM_DISMISS_SERVICE_ACTION = "com.oplus.alarmclock.ALARM_DISMISS_SERVICE_ACTION";
    /**
     * A public action sent by AlarmService when the alarm has started.
     */
    public static final String ALARM_ALERT_ACTION = "com.oplus.alarmclock.ALARM_ALERT";
    /**
     * A public action sent by AlarmService when the alarm has stopped for any reason.
     */
    public static final String ALARM_CANCEL_NOTIFICATION_ACTION = "com.oplus.alarmclock.ALARM_CANCEL_NOTIFICATION_ACTION";
    /**
     * A public action sent by AlarmService when the alarm has stopped for any reason.
     */
    public static final String ALARM_SNOOZE_OR_DISMISS_ACTION = "com.oplus.alarmclock.ALARM_SNOOZE_OR_DISMISS_ACTION";
    /**
     * A public action sent by AlarmService when AlarmStateManger has changed state.
     */
    public static final String ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION = "com.oplus.alarmclock.ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION";

    /**
     * A public action sent by AlarmService when update ringtone info
     */
    public static final String ALARM_RINGTONE_INFO_ACTION = "com.oplus.alarmclock.ALARM_RINGTONE_INFO_ACTION";

    /**
     * 结束闹钟全屏响铃页面，切换为强提醒浮窗
     */
    public static final String EVENT_FINISH_ALARM_FULLSCREEN = "com.oplus.alarmclock.EVENT_FINISH_ALARM_FULLSCREEN";

    public static final int DEFAULT_ALARM_RING = AudioManager.STREAM_ALARM;
    /**
     * This action triggers the AlarmReceiver as well as the AlarmKlaxon. It
     * is a public action used in the manifest for receiving Alarm broadcasts
     * from the alarm manager.
     */
    public static final String CLOCK_ALARM_ALERT_ACTION = "com.oplus.alarmclock.alarmclock.ALARM_ALERT";

    public static final String ALARM_START = "com.oplus.alarmclock.alarmclock.ALARM_START";

    public static final String ALARM_GO_STOP_OLD = "com.oppo.alarmclock.alarmclock.ALARM_GO_STOP";
    public static final String ALARM_GO_STOP = "com.oplus.alarmclock.alarmclock.ALARM_GO_STOP";

    public static final String ALARM_GO_SNOOZE_OLD = "com.oppo.alarmclock.alarmclock.ALARM_GO_SNOOZE";
    public static final String ALARM_GO_SNOOZE = "com.oplus.alarmclock.alarmclock.ALARM_GO_SNOOZE";

    // This is a private action used when the user clears all notifications.
    public static final String CLEAR_NOTIFICATION_OLD = "com.oppo.alarmclock.alarmclock.clear_notification";
    public static final String CLEAR_NOTIFICATION = "com.oplus.alarmclock.alarmclock.clear_notification";

    // This string is used to indicate a silent alarm in the db.
    public static final String ALARM_ALERT_SILENT = "silent";

    // This intent is sent from the notification when the user cancels the
    // snooze alert.
    public static final String CANCEL_SNOOZE_OLD = "com.oppo.alarmclock.alarmclock.cancel_snooze";
    public static final String CANCEL_SNOOZE = "com.oplus.alarmclock.alarmclock.cancel_snooze";


    // This intent is sent from the notification when the user click notification
    public static final String ENTER_APK = "com.oplus.alarmclock.alarmclock.enter_apk";

    // This intent is sent from the notification when the user slide screen notification
    public static final String ENTER_APK_FROM_SCREEN = "com.oplus.alarmclock.alarmclock.enter_apk_from_screen";

    // This intent is sent from the notification when the user delete the notice for next alarm
    public static final String DELETE_NEXT_ALARM_NOTICE = "com.oplus.alarmclock.alarmclock.delete_next_alarm_notice";

    /**
     * This intent is sent from the notification when the user delete the notice for next alarm
     */
    public static final String SNOOZE_ALARM_FROM_NOTIFICATION = "com.oplus.alarmclock.alarmclock.snooze_alarm_from_notification";

    // This string is used when passing an Alarm object through an intent.
    public static final String ALARM_INTENT_EXTRA = "intent.extra.alarm";
    public static final String ALARM_INTENT_CANCEL_NEXT = "intent.next.cancel.alarm";

    // This extra is the raw Alarm object data. It is used in the
    // AlarmManagerService to avoid a ClassNotFoundException when filling in
    // the Intent extras.
    public static final String ALARM_RAW_DATA = "intent.extra.alarm_raw";

    //Local broadcast.
    public static final String WORLD_CLOCK_DATA_CHANGED = "com.oplus.alarmclock.WORLD_CLOCK_DATA_CHANGED";
    //End.

    // when the notification id is bigger than 1000, it will not appear on the locked screen.
    public static final int NOTIFICATION_ELMINATED = 1000;

    public static final int SNOOZE_SWITCH_OFF = 0;
    public static final int SNOOZE_SWITCH_ON_5_MIN = 1;


    public static final int SNOOZE_DEFAULT_FIVE = 5;
    public static final int SNOOZE_DEFAULT_TEN = 10;
    public static final int SNOOZE_DEFAULT_FIFTEEN = 15;
    public static final int SNOOZE_DEFAULT_TWENTY = 20;
    public static final int SNOOZE_DEFAULT_TWENTY_FIVE = 25;
    public static final int SNOOZE_DEFAULT_TWENTY_THRITY = 30;
    /**
     * 稍后提醒时间默认5分钟
     */
    public static final int SNOOZE_AFTER_MIN = 5;
    /**
     * 默认响铃次数
     */
    public static final int SNOOZE_RING_NUM = 3;

    /**
     * 默认轮班周期4天
     */
    public static final int LOOP_DEFAULT_CYCLE_DAYS = 4;
    /**
     * 默认三班
     */
    public static final int LOOP_DEFAULT_WORK_DAYS = 3;
    public static final int LOOP_DEFAULT_ID = -1;
    public static final String IS_NOT_NEED_UPDATE_CITY_LIST = "is_not_need_update_city_list";

    //owner user id
    public static final int OWNER_USER_ID = 0;

    //work mode user id
    public static final int WORKMODE_USER_ID = 9;
    //when timer is running, Intent.PACKAGE_DATA_CLEARED is send, Timer should not ring
    public static final String KEY_TIMER_DATA_CLEAR = "timer_data_clear";

    public static final String ACTION_STOP_RING = "oplus.intent.action.AlarmAlert.STOPRING";
    /**
     * AlarmActivity listens for this broadcast intent, so that other applications can snooze the
     * alarm (after ALARM_ALERT_ACTION and before ALARM_DONE_ACTION).
     */
    public static final String CLOCK_ALARM_SNOOZE_ACTION = "com.oplus.alarmclock.deskclock.ALARM_SNOOZE";
    /**
     * AlarmActivity listens for this broadcast intent, so that other applications can dismiss the
     * alarm (after ALARM_ALERT_ACTION and before ALARM_DONE_ACTION).
     */


    public static final String SCREEN_OFF_REASON = "sys.power.screenoff.reason";

    public static final int GO_TO_SLEEP_REASON_POWER_BUTTON = 4;// change for android 5.0
    public static final String ALARM_HOLIDAY_SWITCH_PREFERENCE = "holiday_switch_preference";
    public static final String ALERT_CYCLE_PREFERENCE_STRING = "day_ring_frequence_string_id";
    public static final String ALERT_CYCLE_PREFERENCE = "day_ring_frequence";
    public static final String EXTRA_VIBRATE_TYPE = "vibrate_type_for_linearmotor_vibrate";
    public static final String FINAL_VIBRATE_TYPE = "final_vibrate_type";
    public static final String FINAL_VIBRATE_TITLE_RES_KEY = "final_vibrate_title_res_key";

    public static final String ALARM_ID = "alarm_id";
    public static final String IS_LOOP_ALARM = "is_loop_alarm";
    public static final String MINI_ALARM_CLICK_POSTION = "mini_alarm_click_postion";
    public static final String ALARM_COUNT = "alarm_count";
    //重建新建弹窗时，之前闹钟/计时是否是新建的，只在重创建闹钟/计时的场景使用
    public static final String KEY_IS_NEW_WHEN_RESTORE = "is_new_when_restore";

    public static final int[] RAPID_STRONG_WAVEFORM_AMPLITUDE = new int[]{0, 250, 0, 250};

    public static final long RAPID_MIDDLE_ONESHOT_TIME = 50L;
    public static final int MIDDLE_AMPLITUDE = 175;
    public static final int GO_TO_SLEEP_REASON_APPLICATION = 0;// for onkeylock

    public static final String ALARM_ALERT_DYNAMIC_WEATHER = "dynamic_weather_alert";

    public static final String ALARM_SPOTIFY_RINGTONE_NAME = "oplus_spotify_ringtone_display";

    public static final int FLAG_UNREGISTER_SHOW_WHEN_LOCKED = 0;

    public static final int CHECKBOX_STATE = 0;
    public static final String SETTING_PKG = "com.android.settings";

    /**
     * 节假日开关状态
     */
    public static final int ALARM_HOLIDAY_SWITCH_OFF = 0;
    public static final int ALARM_HOLIDAY_SWITCH_ON = 1;
    /**
     * 工作日开关状态
     */
    public static final int ALARM_WORKDAY_SWITCH_ON = 1;
    public static final int ALARM_WORKDAY_SWITCH_OFF = 0;

    public static final String MORNING_OPLUS_SPEECH_INTENT = "heytap.speech.intent.action.TEXT_DIRECTIVE";
    public static final String MORNING_OPLUS_SPEECHASSIST = "com.heytap.speechassist.agent.MessengerService";

    /**
     * 通知权限在应用启动、计时、秒表页面是否有动态请求过
     */
    public static final String ALARM_NOTIFY_PREF_FILE_NAME = "alarm_notify_pref_file_name";
    public static final String KEY_ALARM_HAS_REQUEST_NOTIFY = "alarm_has_request_notify";
    public static final String KEY_STOPWATCH_HAS_REQUEST_NOTIFY = "stopwatch_has_request_notify";
    public static final String KEY_TIMER_HAS_REQUEST_NOTIFY = "timer_has_request_notify";
    public static final String KEY_TIMER_MINI_AUTO_HAS_REQUEST_NOTIFY = "timer_mini_auto_has_request_notify";
    public static final String KEY_TIMER_MINI_TRIGGER_HAS_REQUEST_NOTIFY = "timer_mini_trigger_has_request_notify";

    /**
     * 皮套打开状态
     */
    public static final int DEVICE_CASE_OPEN = 1;

    /**
     * 皮套关闭状态
     */
    public static final int DEVICE_CASE_CLOSE = 0;

    /**
     * 光追时钟阴影绘制持续时间
     */
    public static final int EFFECT_VIEW_COUNT_DOWN_TIMER = 5000;

    /**
     * 桌面悬停模式
     */
    public static final int WINDOW_LAYOUT_TABLE_TOP_POSTURE = 1;
    /**
     * 书籍悬停模式
     */
    public static final int WINDOW_LAYOUT_BOOK_POSTURE = 2;
    /**
     * 分离模式
     */
    public static final int WINDOW_LAYOUT_SEPARATING = 3;
    /**
     * 普通模式
     */
    public static final int WINDOW_LAYOUT_NORMAL_MODE = 4;
    /**
     * 流体云sp name
     */
    public static final String SEEDLING_PREF_FILE_NAME = "seedling_pref_file_name";
    /**
     * 流体云 当前card id
     */
    public static final String KEY_SEEDLING_CARD_ID = "seedling_card_id";
    public static final String KEY_SEEDLING_ALARM_CARD_ID = "seedling_alarm_snooze_card_id";
    public static final String KEY_SEEDLING_STOPWATCH_CARD_ID = "seedling_stopwatch_card_id";
    public static final String KEY_SEEDLING_GARB_ALARM_CARD_ID = "seedling_alarm_garb_card_id";
    /**
     * 流体云卡片状态
     */
    public static final int STATUS_LOADCARD_DEFAULT = 0;
    public static final int STATUS_LOADCARD_LOADING = 1;
    public static final int STATUS_LOADCARD_FAIL = 2;
    public static final int STATUS_LOADCARD_SUCCESS = 3;


    /**
     * SPContentProvider 的参数
     */
    public static final String TYPE_STRING_SET = "string_set";
    public static final String TYPE_STRING = "string";
    public static final String TYPE_INT = "int";
    public static final String TYPE_LONG = "long";
    public static final String TYPE_FLOAT = "float";
    public static final String TYPE_BOOLEAN = "boolean";
    public static final String VALUE = "value";
    public static final String NULL_STRING = "null";
    public static final String TYPE_CONTAIN = "contain";
    public static final String TYPE_CLEAN = "clean";
    public static final String TYPE_GET_ALL = "get_all";
    public static final String CURSOR_COLUMN_NAME = "cursor_name";
    public static final String CURSOR_COLUMN_TYPE = "cursor_type";
    public static final String CURSOR_COLUMN_VALUE = "cursor_value";

    public static final String EVENT_ADD_ALARM_FINISH_PAGE = "event_add_alarm_finish_page";
    /**
     * 设置工作日类型
     */
    public static final String EVENT_SET_WORK_DAY_TYPE = "event_set_work_day_type";

    /**
     * 内外屏接续中间态描述
     */
    public static final String EXTRA_DESCRIPTION = "oplus.intent.extra.DESCRIPTION";

    /**
     * 流体云胶囊显示倒计时的最小值，单位为分钟
     */
    public static final int FLUID_CLOUD_COUNT_DOWN_TIME = 10;

    /**
     * 展示旧的流体云通知
     */
    public static final String SHOW_FLOATING_WINDOW = "com.oplus.alarmclock.SHOW_FLOATING_WINDOW";
    /**
     * 隐藏就的通知
     */
    public static final String HIDE_FLOATING_WINDOW = "com.oplus.alarmclock.HIDE_FLOATING_WINDOW";

    /**
     * 删除单个通知后，删除SharedPreferences
     */
    public static final String DELETE_ONE_NOTIFICATION = "com.oplus.alarmclock.alarmclock.delete_one_notification";

    /**
     * 更新下次响铃事件
     */
    public static final String EVENT_ELAPSED_TIME_UNTIL_NEXT_ALARM = "event_elapsed_time_until_next_alarm";
}
