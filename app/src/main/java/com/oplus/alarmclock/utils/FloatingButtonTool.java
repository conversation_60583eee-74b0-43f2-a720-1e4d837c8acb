/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - FloatingButtonTool.java
 ** Description: draw a circle scale animation
 ** Version: 1.0
 ** Date : 2021/3/17
 ** Author: He<PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  HeGai  2021/4/23     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.view.View;

import androidx.appcompat.widget.AppCompatImageView;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;
import com.oplus.clock.common.utils.Log;


/*闹钟和世界时钟悬浮按钮工具类*/
public class FloatingButtonTool {
    private static final String TAG = "FloatingButtonTool";
    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();

    //设置悬浮按钮点击事件
    @SuppressLint("NewApi")
    public void setFloatingButton(int type, Context context, COUIFloatingButton couiFloatingButton, String contentDescription) {
        if ((context != null) && (couiFloatingButton != null)) {
            AppCompatImageView mainButton = couiFloatingButton.getMainFloatingButton();
            couiFloatingButton.setMainFabDrawable(context.getDrawable(R.drawable.color_floating_button_icon_add));
            couiFloatingButton.setForceDarkAllowed(false);
            if (mainButton != null) {
                mainButton.setContentDescription(contentDescription);
            }
            couiFloatingButton.setOnChangeListener(new COUIFloatingButton.OnChangeListener() {
                @Override
                public boolean onMainActionSelected() {
                    try {
                        if (type == AlarmClock.TAB_INDEX_ALARMCLOCK) {
                            if (mDoubleClickHelper.canClick()) {
                                Activity activity = (Activity) context;
                                if ((activity != null) && !activity.isFinishing() && (activity instanceof AlarmClock)) {
                                    Log.d(TAG, " TAB_INDEX_ALARMCLOCK");
                                    //添加闹钟检查是否同意音频须知
                                    StatementDialogUtils.Companion.setMediaStatementContentView(activity,
                                            new PrivacyPolicyAlert.PrivacyPolicyCallback() {
                                        @Override
                                        public void doAfterPermitted() {
                                            //2788955 外销机申请权限，推迟到选择铃声时再申请。
                                            ((AlarmClock) activity).requestRuntimePermissions(true, true, false);
                                        }

                                        @Override
                                        public void onExitClick() {
                                            Log.i(TAG, "mediaStatement exit");
                                        }
                                    }, false);
                                }
                            }
                        }
                        if (type == AlarmClock.TAB_INDEX_GLOBALCITY) {
                            if (mDoubleClickHelper.canClick()) {
                                Log.d(TAG, " TAB_INDEX_GLOBALCITY");
                                Activity activity = (Activity) context;
                                if ((activity != null) && !activity.isFinishing() && (activity instanceof AlarmClock)) {
                                    WorldClockViewFragment worldClockViewFragment = ((AlarmClock) activity).getmWorldClockFragment();
                                    if (worldClockViewFragment != null) {
                                        worldClockViewFragment.onFABClicked();
                                    }
                                }
                            }
                            return false;
                        }
                        return false;
                    } catch (Exception e) {
                        Log.i(TAG, " setOnChangeListener" + e.toString());
                        return false;
                    }
                }

                @Override
                public void onToggleChanged(boolean b) {

                }
            });
        } else {
            //参数异常
            Log.d(TAG, " setFloatingButton couiFloatingButton ");
        }
    }

    //更新悬浮按钮显隐
    public void updateFloatingButton(Context context, COUIFloatingButton couiFloatingButton, boolean ifShow, boolean enabled) {
        if (couiFloatingButton == null) {
            //参数异常
            Log.d(TAG, " updateFloatingButton couiFloatingButton ");
            return;
        }
        if (!ifShow) {
            couiFloatingButton.setVisibility(View.GONE);
        } else {
            couiFloatingButton.setVisibility(View.VISIBLE);
            setFloatBtnEnabled(context, couiFloatingButton, enabled);
        }
    }


    //设置悬浮按钮的背景颜色
    private void setFloatBtnEnabled(Context context, COUIFloatingButton couiFloatingButton, boolean enabled) {
        if ((context != null) && (couiFloatingButton != null)) {
            if (enabled) {
                couiFloatingButton.setMainFloatingButtonBackgroundColor(ColorStateList.valueOf(COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary)));
                couiFloatingButton.getMainFloatingButton().setBackgroundColor(COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary));
            } else {
                couiFloatingButton.setMainFloatingButtonBackgroundColor(ColorStateList.valueOf(context.getColor(R.color.main_floating_button_bg_color)));
                couiFloatingButton.getMainFloatingButton().setBackgroundColor(context.getColor(R.color.main_floating_button_bg_color));
            }
        } else {
            //参数异常
            Log.d(TAG, " setFloatBtnEnabled couiFloatingButton ");
        }
    }
}
