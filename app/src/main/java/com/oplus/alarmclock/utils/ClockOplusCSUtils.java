/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.WorkDayTypeUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.clock.common.utils.Log;
import com.oplus.statistics.OplusTrack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class ClockOplusCSUtils {

    // User Action:
    // Alarm
    public static final String STR_PRESS_ALARM_TAB = "tab_alarm";
    public static final String STR_PRESS_ALARM_NEW_MENU = "tab_alarm_new";
    public static final String STR_PRESS_ALARM_SNOOZE_MENU = "tab_alarm_snooze";
    public static final String STR_PRESS_ALARM_DISMISS_MENU = "tab_alarm_dismiss";

    public static final String STR_PRESS_ALARM_CANCEL_SNOOZE_MENU = "tab_alarm_cancel_snooze";
    public static final String EVENT_ELLIPTIC_STOP_ALARM_KLAXON = "evnet_elliptic_stop_alarm_klaxon";
    public static final String EVENT_ALARM_NUMS_KEY = "event_alarm_nums_key";
    public static final String EVENT_ALARM_NUMS_ON_CREATE = "event_alarm_nums_on_create";
    public static final String EVENT_VOICE_ALARM_ADD = "event_voice_alarm_add";
    public static final String EVENT_VOICE_ALARM_ADD_SUCCUSS = "event_voice_alarm_add_success";
    public static final String EVENT_ALARM_ACTION_HOUR = "event_alarm_action_hour";
    public static final String EVENT_ALARM_ACTION_MINUTE = "event_alarm_action_minute";
    public static final String EVENT_ALARM_HOUR = "event_alarm_hour";
    public static final String EVENT_ALARM_MINUTE = "event_alarm_minute";
    public static final String EVENT_ALARM_AND_ACTION_DIFF = "alarm_and_action_diff";
    public static final String EVENT_CHOOSE_DYNAMIC_SOUND = "choose_dynamic_sound_1";
    public static final String EVENT_BREENO_SET_ROUTE_ALARM = "set_route_alarm";
    public static final String EVENT_BREENO_ADD_ALARM_COUNT = "jump_count";
    public static final String EVENT_SET_ROUTE_ALARM_CANCEL = "click_cancel";
    public static final String EVENT_SET_ROUTE_ALARM_DONE = "click_done";
    public static final String KEY_ALARM_ID = "key_alarm_id";
    public static final String KEY_ALARM_INTERRUPT_REASON = "key_alarm_interrupt_reason";
    public static final String KEY_ALARM_REGISTER_INSTANCE_STEP = "key_alarm_register_instance_step";

    // Global
    public static final String STR_PRESS_GLOBAL_TAB = "tab_gloabl";
    public static final String STR_PRESS_GLOBAL_ADD_MENU = "tab_gloabl_add";
    public static final String STR_PRESS_GLOBAL_DELETE_MENU = "tab_gloabl_delete";
    public static final String EVENT_GLOBLE_CLOCK_SEARCH_VIEW_CLICK = "event_globle_clock_search_view_click";
    public static final String EVENT_GLOBLE_CLOCK_NO_RESULT = "event_globle_clock_no_result";
    public static final String EVENT_GLOBLE_CLOCK_NO_RESULT_KEY = "event_globle_clock_no_result_key";
    // Timer
    public static final String STR_PRESS_TIMER_TAB = "tab_timer";
    public static final String STR_PRESS_TIMER_START_MENU = "tab_timer_start_menu";
    public static final String STR_PRESS_TIMER_RESET_MENU = "tab_timer_reset";
    public static final String STR_PRESS_TIMER_PAUSE_MENU = "tab_timer_pause_menu";
    public static final String EVENT_TIMER_POSITION = "event_timer_position";
    public static final String EVENT_TIMER_DESCRIPTION = "event_timer_description";
    public static final String EVENT_TIMER_DURATION = "event_timer_duration";
    public static final String EVENT_TIMER_DATA_COLLECTOR = "event_timer_data_collector";
    // Stopwatch
    public static final String STR_PRESS_STOPWATCH_TAB = "tab_stopwatch";
    public static final String STR_PRESS_STOPWATCH_START_MENU = "tab_stopwatch_start_menu";
    public static final String STR_PRESS_STOPWATCH_PAUSE_MENU = "tab_stopwatch_pause_menu";
    public static final String STR_PRESS_STOPWATCH_CONTINUE_MENU = "tab_stopwatch_continue";
    public static final String STR_PRESS_STOPWATCH_RECORD_MENU = "tab_stopwatch_record";
    public static final String STR_PRESS_STOPWATCH_RESET_MENU = "tab_stopwatch_reset";
    // launcher shortcuts
    public static final String STR_SHORTCUT_SET_ALARM = "shortcut_set_alarm";
    public static final String STR_SHORTCUT_VOICE_SET_ALARM = "shortcut_voice_set_alarm";

    // world clock widget
    public static final String WORLD_CLOCK_WIDGET_CHANGED = "world_clock_widget_changed";
    public static final String ADD_OR_DEL_WIDGET_IN_CLOCK_APP = "add_or_del_world_clock_widget";
    public static final String CITIES_COUNT = "cities_count";
    public static final String ADD_OR_DEL_WIDGET = "add_or_del_widget";   // 1: add  -1: del.

    public static final String WORLD_CLOCK_CITIES_CHANGED = "world_clock_cities_changed";
    public static final String ADD_OR_DEL_CITY = "add_or_del_city";         // 1: add  -1: del.
    public static final String WORLD_CLOCK_WIDGET_EXIST = "widget_exist";   // 1: exist 0: not.


    public static final String ALARM_SETTING_UPDATE_LENGTH_BEFORE = "update_length_before";
    public static final String ALARM_SETTING_UPDATE_LENGTH_AFTER = "update_length_after";
    public static final String ALARM_SETTING_UPDATE_INTERVAL_BEFORE = "update_interval_before";
    public static final String ALARM_SETTING_UPDATE_INTERVAL_AFTER = "update_interval_after";
    public static final String ALARM_SETTING_UPDATE_NUM_BEFORE = "update_num_before";
    public static final String ALARM_SETTING_UPDATE_NUM_AFTER = "update_num_after";


    public static final String SETTING_FROM_CLOCK = "setting_from_clock";
    public static final String SETTING_FROM_GLOBAL_CLOCK = "setting_from_global_clock";
    public static final String SETTING_FROM_STOPWATCH = "setting_from_stopwatch";
    public static final String SETTING_FROM_TIMER = "setting_from_timer";

    public static final String SETTING_TO_UPDATE_LENGTH = "setting_to_update_length";
    public static final String SETTING_TO_UPDATE_INTERVAL = "setting_to_update_interval";
    public static final String SETTING_TO_UPDATE_NUM = "setting_to_update_num";
    public static final String SETTING_TO_UPDATE_TIME_OR_DATE = "setting_to_update_time_or_date";
    public static final String SETTING_TO_FEEDBACK_CLICK = "feedback_click";

    public static final String SETTING_SHOW_NEXT_ALARM_NOTICES = "setting_show_next_alarm_notices";

    public static final String EVENT_SETTING_TO_NEXT_ALARM_NOTICE = "event_setting_to_next_alarm_notice";
    public static final String KEY_SETTING_NEXT_ALARM_NOTICE = "key_setting_next_alarm_notice";

    public static final String EVENT_CHOICE_CLOSE_ALARM_MODE = "event_choice_close_alarm_mode";
    public static final String KEY_CHOICE_CLOSE_ALARM_MODE = "close_alarm_mode";

    public static final String EVENT_NEXT_ALARM_NOTICE_SCREEN = "event_next_alarm_notice_screen";
    public static final String KEY_NEXT_ALARM_NOTICE_SCREEN = "key_next_alarm_notice_screen";
    public static final String VALUE_CANCEL_NEXT_ALARM_NOTICE = "value_cancel_next_alarm_notice";
    public static final String VALUE_ENTRY_APK_FROM_NEXT_ALARM_NOTICES = "value_entry_apk_from_next_alarm_notices";
    public static final String VALUE_DELETE_NEXT_ALARM_NOTICE = "value_delete_next_alarm_notice ";
    public static final String OP_ADD = "1";
    public static final String OP_DEL = "-1";
    public static final String EXIST = "1";
    public static final String NONE_EXIST = "0";
    // end.
    /**
     * 世界时钟时区变化
     */
    public static final String EVENT_TIME_ZONE_CHANGE = "event_time_zone_change";
    public static final String KEY_CURRENT_TIME_ZONE = "key_current_time_zone";
    public static final String KEY_CURRENT_OFF_SET = "key_current_off_set";
    public static final String KEY_CURRENT_TIME_STAMP = "key_current_time_stamp";
    /**
     * 进入悬停各个页面的次数
     */
    public static final String EVENT_ENTRY_HOVER_EVENT = "event_enter_hover_table";
    public static final String ENTRY_HOVER_TABLE_TYPE = "enter_hover_table_type";
    /**
     * 铃声设置埋点
     */
    public static final String EVENT_SINGLE_RING_SETTING = "event_single_ring_setting";
    public static final String EVENT_SINGLE_RING_SETTING_TYPE = "event_single_ring_setting_type";
    /**
     * 振动设置埋点
     */
    public static final String EVENT_SINGLE_VIBRATE_SETTING = "event_single_vibrate_setting";
    public static final String EVENT_SINGLE_VIBRATE_SETTING_TYPE = "event_single_vibrate_setting_type";

    /**
     * 默认铃声设置埋点
     */
    public static final String EVENT_DEFAULT_RING_SETTING = "event_default_ring_setting";

    /**
     * 进入悬停页面的停留时长
     */
    public static final String EVENT_ENTRY_HOVER_EVENT_DURATION = "event_enter_hover_table_duration";
    public static final String ENTRY_HOVER_TABLE_TYPE_DURATION = "enter_hover_table_type_duration";
    /**
     * miniApp
     */
    public static final String EVENT_ENTRY_MINI_APP = "event_entry_mini_app";
    /**
     * miniApp 新建闹钟
     */
    public static final String EVENT_MINI_APP_NEW_ALARM = "event_mini_app_new_alarm";
    /**
     * miniApp 编辑闹钟
     */
    public static final String EVENT_MINI_APP_EDIT_ALARM = "event_mini_app_edit_alarm";
    /**
     * miniApp 开启/关闭闹钟
     */
    public static final String EVENT_MINI_APP_OPEN_OR_CLOSE_ALARM = "event_mini_app_open_or_close_alarm";
    /**
     * miniApp 新建闹钟-更多设置
     */
    public static final String EVENT_MINI_APP_MORE_SETTING = "event_mini_app_more_setting";
    /**
     * miniApp 外屏展开内屏接续
     */
    public static final String EVENT_MINI_APP_CONTINUE_ACTIVITY = "event_mini_app_continue_activity";
    public static final String MINI_APP_CONTINUE_BEFORE_ACTIVITY = "mini_app_continue_before_activity";
    public static final String MINI_APP_CONTINUE_AFTER_ACTIVITY = "mini_app_continue_after_activity";
    public static final String MINI_APP_ACTIVITY_ALARM_LIST = "mini_app_activity_alarm_list";
    public static final String MINI_APP_ACTIVITY_ADD_ALARM = "mini_app_activity_add_alarm";

    /**
     * 轮班闹钟类型点击
     */
    public static final String EVENT_LOOP_ALARM_TYPE_CLICK = "event_loop_alarm_click";
    /**
     * 轮班闹钟节假日开关点击
     */
    public static final String EVENT_LOOP_ALARM_HOLIDAY_SWITCH = "event_loop_alarm_holiday_switch";
    /**
     * 保存轮班闹钟
     */
    public static final String EVENT_LOOP_ALARM_SAVE = "event_loop_alarm_save";

    /**
     * 铃声渐响开关点击
     */
    public static final String EVENT_BELL_GRADUALLY_RINGS_SWITCH = "event_bell_gradually_rings_switch";
    /**
     * 铃声渐响开关点击事件字段
     */
    public static final String EVENT_ATTRIBUTE_ID_GRADUALLY_RINGS = "bell_gradually_rings";
    /**
     * 打开
     */
    public static final String OPEN = "1";
    /**
     * 关闭
     */
    public static final String CLOSE = "0";
    /**
     * 创建日历闹钟的数量
     */
    public static final String CREATE_CALENDAR_ALARM_COUNT = "event_create_calendar_alarm_count";
    public static final String CALENDAR_ALARM_TYPE = "calendar_alarm_type";

    /**
     * 皮套模式下闹钟响铃次数
     */
    public static final String EVENT_DEVICE_CASE_ALARM_COUNT = "event_device_case_alarm_count";
    /**
     * 皮套模式下计时器响铃次数
     */
    public static final String EVENT_DEVICE_CASE_TIMER_COUNT = "event_device_case_timer_count";

    /**
     * 皮套模式下关闭闹钟的方式
     */
    public static final String EVENT_DEVICE_CASE_CLOSE_ALARM = "event_device_case_close_alarm";
    public static final String DEVICE_CASE_CLOSE_ALARM_TYPE = "device_case_close_alarm_type";
    public static final String CLOSE_ALARM_TYPE_SNOOZE = "1";
    public static final String CLOSE_ALARM_TYPE_CLOSE = "2";
    /**
     * 皮套模式下关闭计时器
     */
    public static final String EVENT_DEVICE_CASE_CLOSE_TIMER = "event_device_case_close_timer";


    /**
     * 流体云卡片事件类型
     */
    public static final String EVENT_FLUID_CARD_RING_CANCEL = "1";
    public static final String EVENT_FLUID_CARD_RING_SNOOZE = "2";
    public static final String EVENT_FLUID_CARD_RING_SLIDE = "3";
    public static final String EVENT_FLUID_CARD_SNOOZE_CANCEL = "4";
    public static final String EVENT_FLUID_CARD_ACTION_TYPE = "event_fluid_card_action_type";

    /**
     *  流体云卡片操作埋点， 1 响铃后点击关闭 2响铃点击稍后提醒按钮 3 响铃后滑动关闭 4 稍后提醒点击关闭
     */
    public static final String EVENT_FLUID_CARD_SNOOZE_AND_RING = "event_fluid_card_ring_snooze";

    /**
     * 日历闹钟
     */
    public static final String CALENDAR_ALARM = "1";
    /**
     * 日历重复闹钟
     */
    public static final String CALENDAR_REPEAT_ALARM = "2";

    /**
     * 世界时钟
     */
    public static final Integer TABLE_WORLD_CLOCK = 1;
    /**
     * 秒表
     */
    public static final Integer TABLE_STOP_WATCH = 2;
    /**
     * 计时
     */
    public static final Integer TABLE_TIMER = 3;

    /**
     * 铃声/振动类型 1 默认 2 非默认 3 无
     */
    public static final String RING_AND_VIBRATE_TYPE_DEFAULT = "1";
    public static final String RING_AND_VIBRATE_TYPE_NOT_DEFAULT = "2";
    public static final String RING_AND_VIBRATE_TYPE_NULL = "3";

    //notification of timer
    public static final String EVENT_ID_TIMER_NOTIFICATION = "event_timer_notification_operation";
    public static final String EVENT_KEY_TIMER_NOTIFICATION = "timer_notification_operation_type";
    public static final String TIMER_NOTIFICATION_TYPE_CLICK = "click_notification";
    public static final String TIMER_NOTIFICATION_TYPE_CANCEL = "cancel_timer";
    public static final String TIMER_NOTIFICATION_TYPE_CONTINUE = "continue_timer";
    public static final String EVENT_LOCK_SCREEN_TIMER_RING_DURATION = "event_lock_screen_timer_ring_duration";
    public static final String EVENT_LOCK_SCREEN_TIMER_RING_DURATION_KEY = "event_lock_screen_timer_ring_duration_key";
    public static final String EVENT_LOCK_SCREEN_ALARM_RING_DURATION = "event_lock_screen_alarm_ring_duration";
    public static final String EVENT_LOCK_SCREEN_ALARM_RING_DURATION_KEY = "event_lock_screen_alarm_ring_duration_key";
    public static final String ALARM_INFO_TYPE_EDIT = "edit";
    public static final String ALARM_INFO_TYPE_add = "add";
    public static final String KEY_RECEIVED_BROADCAST = "key_received_broadcast";

    // turn off repeat alarm value(close once repeat alarm) start
    public static final int TURN_OFF_REPEAT_ALARM_VALUE_CLOSE_ONCE = 1;
    public static final int TURN_OFF_REPEAT_ALARM_VALUE_CLOSE_ALARM = 2;
    public static final int TURN_OFF_REPEAT_ALARM_VALUE_DO_NOTHING = 3;
    // turn off repeat alarm value(close once repeat alarm) end

    public static final String SNOOZE_CLOCK = "0";
    public static final String CLOSE_CLOCK = "1";

    private static final String EVENT_TIMER_INFO = "event_timer_info";
    private static final String TIMER_INFO_NAME = "timer_name";
    private static final String TIMER_INFO_POSITION = "timer_position";
    private static final String TIMER_INFO_IS_FROM_AI = "timer_create";
    private static final String TIMER_INFO_MUSIC = "timer_music";
    private static final String TIMER_INFO_START_SCREEN = "timer_start_screen";

    private static final String TIMER_IS_FROM_AI = "0";
    private static final String TIMER_IS_NOT_FROM_AI = "1";

    private static final String ALARM_COUNT_OPEN = "alarm_count_open";

    private static final String ALARM_OFF_MODE = "alarm_off_mode";
    private static final String OFF_MODE = "off_mode";

    private static final String ALARM_CLOSE = "alarm_close";
    private static final String ALARM_CLOSE_MODE = "alarm_close_mode";
    private static final String ALARM_CLOSE_SCREEN = "alarm_close_screen";

    private static final String OUT_SCREEN = "0";
    private static final String IN_SCREEN = "1";

    private static final String EVENT_TIMER_COUNT = "event_timer_count";
    private static final String TIMER_COUNT = "timer_count";

    private static final String ADD_TIMER = "add_timer";
    private static final String TIMER_DURATION = "timer_duration";
    private static final String TIMER_RING = "timer_ring";

    // App ID:
    private static final String CLOCK_APP_ID = "20015";
    private static final String TAG = "ClockOPlusCSUtils";
    private static final boolean LOCAL_DEBUG = false;

    private static final String ALARM_REPEAT_INFO = "alarm_repeat_info";
    private static final String ALARM_REPEAT_INFO_LENGTH = "alarm_repeat_info_length";
    private static final String ALARM_REPEAT_INFO_INTERVAL = "alarm_repeat_info_interval";
    private static final String ALARM_REPEAT_INFO_NUM = "alarm_repeat_info_num";

    //setting pager,set workday type
    private static final String SETTING_WORKDAY_TYPE_EVENT = "setting_workday_type_event";
    private static final String SETTING_WORKDAY_TYPE_CURRENT_TYPE = "setting_workday_type_current_type";
    private static final String SETTING_WORKDAY_TYPE_CLOCK_NUM = "setting_workday_type_clock_num";

    // turn off repeat alarm event(close once repeat alarm)
    private static final String TURN_OFF_REPEAT_ALARM_EVENT = "turn_off_repeat_alarm_event";
    private static final String TURN_OFF_REPEAT_ALARM_VALUE_TYPE = "turn_off_repeat_alarm_value_type";

    private static final String EVENT_WIDGET_SWITCH_CLOCK_TYPE = "event_widget_switch_clock_type";
    private static final String KEY_WIDGET_CLOCK_TYPE = "widget_clock_type";

    private static final String LOG_TAG_EV_EVENT = "KVEvent";

    public static void onEvent(Context context, String eventId) {
        onEvent(context, eventId, "");
    }

    public static void onEvent(Context context, String eventId, String eventTag) {
        if (context == null) {
            return;
        }
        HashMap<String, String> eventMap = new HashMap<>();
        eventMap.put("eventTag", eventTag);
        eventMap.put("eventCount", String.valueOf(1));
        eventMap.put("duration", String.valueOf(0));
        OplusTrack.onCommon(context, LOG_TAG_EV_EVENT, eventId, eventMap);
    }

    public static void onCommon(Context context, String eventId) {
        onCommon(context, eventId, new HashMap<String, String>());
    }

    public static void onCommon(Context context, String eventId, HashMap<String, String> map) {
        if (context == null) {
            return;
        }
        OplusTrack.onCommon(context, CLOCK_APP_ID, eventId, map);
        if (LOCAL_DEBUG) {
            Log.d(TAG, "eventId: " + eventId);
            if ((map != null) && !map.isEmpty()) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    Log.d(TAG, entry.getKey() + ": " + entry.getValue());
                }
            }
        }
    }

    public static void statisticsEvent(Context context, String eventId) {
        HashMap<String, String> map = new HashMap<>();
        map.put("eventTag", eventId);
        map.put("eventCount", String.valueOf(1));
        map.put("duration", String.valueOf(0));
        ClockOplusCSUtils.onCommon(context, eventId, map);
    }

    public static void statisticsClockClose(Context context, String snoozeClock) {
        boolean isSmall = FoldScreenUtils.isDragonflySmallScreen(context);
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.ALARM_CLOSE_MODE, snoozeClock);
        map.put(ClockOplusCSUtils.ALARM_CLOSE_SCREEN, isSmall ? ClockOplusCSUtils.OUT_SCREEN : ClockOplusCSUtils.IN_SCREEN);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.ALARM_CLOSE, map);
    }

    public static void statisticsClockCount(Context context, int size) {
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockConstant.ALARM_COUNT, String.valueOf(size));
        ClockOplusCSUtils.onCommon(context, ClockConstant.ALARM_COUNT, map);
    }

    public static void statisticsClockOpenCount(Context context, ArrayList<Alarm> arrayList) {
        int countOpen = 0;
        for (Alarm alarm : arrayList) {
            if (alarm.isEnabled() && !alarm.isCloseOnceAlarmClock()) {
                countOpen++;
            }
        }
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.ALARM_COUNT_OPEN, String.valueOf(countOpen));
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.ALARM_COUNT_OPEN, map);
    }

    public static void statisticsCloseModel(String mode, Context context) {
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.OFF_MODE, mode);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.ALARM_OFF_MODE, map);
    }

    public static void statisticsOutScreenTimerStart(String timerName, String ringName, long duration, Context context) {
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.TIMER_INFO_START_SCREEN, ClockOplusCSUtils.OUT_SCREEN);
        map.put(ClockOplusCSUtils.TIMER_INFO_NAME, timerName);
        map.put(ClockOplusCSUtils.EVENT_TIMER_DURATION, Formatter.getTimerDuration(duration));
        map.put(ClockOplusCSUtils.TIMER_INFO_MUSIC, ringName);
        map.put(ClockOplusCSUtils.TIMER_INFO_POSITION, "0");
        map.put(ClockOplusCSUtils.TIMER_INFO_IS_FROM_AI, ClockOplusCSUtils.TIMER_IS_NOT_FROM_AI);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.EVENT_TIMER_INFO, map);
    }

    public static void statisticsAddTimer(String title, long duration, String ringName, Context context) {
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.TIMER_INFO_NAME, title);
        map.put(ClockOplusCSUtils.TIMER_DURATION, Formatter.getTimerDuration((duration <= 0) ? 1 : duration));
        map.put(ClockOplusCSUtils.TIMER_RING, ringName);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.ADD_TIMER, map);
    }

    public static void statisticsAlarmSettingInfo(Context context, AlarmRepeat alarmRepeat) {
        if (context == null) {
            return;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put(ALARM_REPEAT_INFO_LENGTH, String.valueOf(alarmRepeat.getmAlarmDuration()));
        map.put(ALARM_REPEAT_INFO_INTERVAL, String.valueOf(alarmRepeat.getmAlarmInterval()));
        map.put(ALARM_REPEAT_INFO_NUM, String.valueOf(alarmRepeat.getmAlarmNum()));
        boolean isNeedShowNextAlarm = AlarmUtils.isOpenNextAlarmNotices(context);
        Log.d("onCreateView isNeedShowNextAlarm : " + isNeedShowNextAlarm);
        map.put(SETTING_SHOW_NEXT_ALARM_NOTICES, String.valueOf(isNeedShowNextAlarm));
        boolean morningClockStatus = MorningAlarmClock.ifSupportMorningBroadcast(context.getApplicationContext());
        if (morningClockStatus) {
            map.put(PlayMorningTools.MORNING_BURIED_POINT_SWITCH, getMorningSwitchStatus(context.getApplicationContext()) + "");
        }
        onCommon(context, ClockOplusCSUtils.ALARM_REPEAT_INFO, map);
    }

    public static boolean getMorningSwitchStatus(Context context) {
        boolean status = false;
        if (context != null) {
            status = PlayMorningTools.isMorningReportEnable(context);
        }
        return status;
    }


    public static void statisticsNextAlarmNotices(Context context, String actionNextAlarm) {
        HashMap<String, String> map = new HashMap<>();
        map.put(KEY_NEXT_ALARM_NOTICE_SCREEN, actionNextAlarm);
        onCommon(context, ClockOplusCSUtils.EVENT_NEXT_ALARM_NOTICE_SCREEN, map);
    }

    public static void setWorkdayType(Context context, int workdayType, int workdayTypeClockNum) {
        if (context != null) {
            HashMap<String, String> map = new HashMap<>();
            map.put(SETTING_WORKDAY_TYPE_CURRENT_TYPE, String.valueOf(workdayType));
            map.put(SETTING_WORKDAY_TYPE_CLOCK_NUM, String.valueOf(workdayTypeClockNum));
            onCommon(context, ClockOplusCSUtils.SETTING_WORKDAY_TYPE_EVENT, map);
        }
    }

    /**
     * 统计闹钟工作日类型和工作日闹钟的数量
     *
     * @param context
     */
    public static void setWorkdayTypeEvent(Context context) {
        //法定工作日
        setWorkdayType(context, WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY, AlarmUtils.getWorkdayTypeClockNumByType(context, WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY));
        //单休工作日
        setWorkdayType(context, WorkDayTypeUtils.WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY, AlarmUtils.getWorkdayTypeClockNumByType(context, WorkDayTypeUtils.WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY));
        //大小周工作日（本周休周日）
        setWorkdayType(context, WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY, AlarmUtils.getWorkdayTypeClockNumByType(context, WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY));
        //大小制工作日（本周休周六,周日）
        setWorkdayType(context, WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE, AlarmUtils.getWorkdayTypeClockNumByType(context, WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE));
    }

    public static void uploadTimerInfoOPlusFromAi(long seconds) {
        uploadTimerInfoOPlusIsTempTimer(seconds);
    }

    private static void uploadTimerInfoOPlusIsTempTimer(long seconds) {
        uploadTimerInfoOPlus(null, null, seconds, true, null);
    }

    /**
     * 用于在计时器启动时，统计计时器信息（点击开始按钮，语音启动计时）
     *
     * @param oplusTimer
     * @param service
     * @param secondsOrIndex
     * @param isFromAi
     * @param context
     */
    public static void uploadTimerInfoOPlus(OplusTimer oplusTimer, TimerService service, long secondsOrIndex, boolean isFromAi, Context context) {
        HashMap<String, String> map = new HashMap<>();
        if (context != null) {
            boolean isSmall = FoldScreenUtils.isDragonflySmallScreen(context);
            map.put(ClockOplusCSUtils.TIMER_INFO_START_SCREEN, isSmall ? ClockOplusCSUtils.OUT_SCREEN : ClockOplusCSUtils.IN_SCREEN);
        }
        if (isFromAi) {
            map.put(ClockOplusCSUtils.TIMER_INFO_NAME, AlarmClockApplication.getInstance().getResources().getString(R.string.timer_title));
            map.put(ClockOplusCSUtils.EVENT_TIMER_DURATION, Formatter.getCountDownTime(context, secondsOrIndex));
            map.put(ClockOplusCSUtils.TIMER_INFO_MUSIC, "default");
            map.put(ClockOplusCSUtils.TIMER_INFO_POSITION, "0");
            map.put(ClockOplusCSUtils.TIMER_INFO_IS_FROM_AI, TIMER_IS_FROM_AI);
        } else {
            map.put(ClockOplusCSUtils.TIMER_INFO_IS_FROM_AI, TIMER_IS_NOT_FROM_AI);
            if (oplusTimer != null) {
                map.put(ClockOplusCSUtils.TIMER_INFO_NAME, getDescription(oplusTimer));
                map.put(ClockOplusCSUtils.EVENT_TIMER_DURATION, Formatter.getTimerDuration(oplusTimer.getDuration()));
                map.put(ClockOplusCSUtils.TIMER_INFO_MUSIC, oplusTimer.getRingName());
                map.put(ClockOplusCSUtils.TIMER_INFO_POSITION, String.valueOf(secondsOrIndex + 1));
            } else {
                map.put(ClockOplusCSUtils.TIMER_INFO_NAME, service.getTimerName((int) secondsOrIndex));
                map.put(ClockOplusCSUtils.EVENT_TIMER_DURATION, Formatter.getTimerDuration(service.getTotalTime((int) secondsOrIndex)));
                map.put(ClockOplusCSUtils.TIMER_INFO_MUSIC, service.getTimerRingName((int) secondsOrIndex));
                map.put(ClockOplusCSUtils.TIMER_INFO_POSITION, "0");
            }
        }

        ClockOplusCSUtils.onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_TIMER_INFO, map);
    }

    private static String getDescription(OplusTimer oplusTimer) {
        if (TextUtils.isEmpty(oplusTimer.getTextDes())) {
            return oplusTimer.getDescription();
        } else {
            return oplusTimer.getTextDes();
        }
    }

    public static void statisticsTimerCount(Context context, int timerCount) {
        HashMap<String, String> map = new HashMap<>();
        map.put(TIMER_COUNT, String.valueOf(timerCount));
        Log.i(TAG, "statisticsTimerCount timerCount : " + timerCount);
        onCommon(context, ClockOplusCSUtils.EVENT_TIMER_COUNT, map);
    }


    /**
     * for close once
     */
    public static void statisticsTurnOffRepeatAlarm(Context context, int value) {
        if ((value >= TURN_OFF_REPEAT_ALARM_VALUE_CLOSE_ONCE) && (value <= TURN_OFF_REPEAT_ALARM_VALUE_DO_NOTHING)) {
            HashMap<String, String> map = new HashMap<>();
            map.put(ClockOplusCSUtils.TURN_OFF_REPEAT_ALARM_VALUE_TYPE, String.valueOf(value));
            ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.TURN_OFF_REPEAT_ALARM_EVENT, map);
        }
    }

    public static void statisticsRouteAlarm(String type) {
        Log.i(TAG, "statisticsRouteAlarm :" + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(type, type);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_BREENO_SET_ROUTE_ALARM, map);
    }

    /**
     * 进入悬停模式
     *
     * @param type
     */
    public static void statisticsHoverTable(String type) {
        Log.i(TAG, "statisticsHoverTable :" + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(ENTRY_HOVER_TABLE_TYPE, type);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_ENTRY_HOVER_EVENT, map);
    }

    /**
     * 铃声埋点
     *
     * @param type
     */
    public static void statisticsRingSetting(String type) {
        Log.i(TAG, "statisticsHoverTable :" + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(EVENT_SINGLE_RING_SETTING_TYPE, type);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_SINGLE_RING_SETTING, map);
    }

    /**
     * 振动埋点
     *
     * @param type
     */
    public static void statisticsVibrateSetting(String type) {
        Log.i(TAG, "statisticsHoverTable :" + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(EVENT_SINGLE_VIBRATE_SETTING_TYPE, type);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_SINGLE_VIBRATE_SETTING, map);
    }

    /**
     * 创建日历闹钟埋点
     *
     * @param type
     */
    public static void statisticsCreateCalendarAlarm(String type) {
        Log.i(TAG, "statisticsCreateCalendarAlarm :" + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(CALENDAR_ALARM_TYPE, type);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.CREATE_CALENDAR_ALARM_COUNT, map);
    }

    /**
     * miniApp外屏展开内屏接续
     *
     * @param type
     */
    public static void statisticsMniAppContinue(String type) {
        Log.i(TAG, "statisticsMniAppContinue  type: " + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(MINI_APP_CONTINUE_BEFORE_ACTIVITY, type);
        map.put(MINI_APP_CONTINUE_AFTER_ACTIVITY, type);
        onCommon(AlarmClockApplication.getInstance(), EVENT_MINI_APP_CONTINUE_ACTIVITY, map);
    }

    /**
     * 悬停页面停留时长
     *
     * @param duration
     * @param type
     */
    public static void statisticsHoverTableDuration(String duration, String type) {
        Log.i(TAG, "statisticsHoverTableDuration  type: " + type + " duration:" + duration);
        HashMap<String, String> map = new HashMap<>();
        map.put(ENTRY_HOVER_TABLE_TYPE, type);
        map.put(ENTRY_HOVER_TABLE_TYPE_DURATION, duration);
        onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_ENTRY_HOVER_EVENT_DURATION, map);
    }

    /**
     * 皮套模式下关闭闹钟
     *
     * @param type 类型
     */
    public static void statisticsDeviceCaseCloseAlarm(String type) {
        Log.i(TAG, "statisticsDeviceCaseCloseAlarm  type: " + type);
        HashMap<String, String> map = new HashMap<>();
        map.put(DEVICE_CASE_CLOSE_ALARM_TYPE, type);
        onCommon(AlarmClockApplication.getInstance(), EVENT_DEVICE_CASE_CLOSE_ALARM, map);
    }

    /**
     * 插件，单时钟和双时钟发生切换时触发
     * type:1 单时钟；2 双时钟
     *
     * @param type
     */
    public static void statisticsWidgetWitchClockType(int type) {
        HashMap<String, String> map = new HashMap<>();
        map.put(KEY_WIDGET_CLOCK_TYPE, String.valueOf(type));
        onCommon(AlarmClockApplication.getInstance(), EVENT_WIDGET_SWITCH_CLOCK_TYPE, map);
    }

    public static void statisticsNextAlarmNotice(Context context, String value) {
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.KEY_SETTING_NEXT_ALARM_NOTICE, value);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.EVENT_SETTING_TO_NEXT_ALARM_NOTICE, map);
    }

    /**
     * 铃声渐响埋点
     * @param isBellGraduallyRings 铃声渐响开关状态
     */
    public static void statisticsGraduallyRings(boolean isBellGraduallyRings) {
        HashMap<String, String> map = new HashMap<>();
        map.put(EVENT_ATTRIBUTE_ID_GRADUALLY_RINGS, isBellGraduallyRings ? OPEN : CLOSE);
        ClockOplusCSUtils.onCommon(
                AlarmClockApplication.getInstance(),
                ClockOplusCSUtils.EVENT_BELL_GRADUALLY_RINGS_SWITCH,
                map
        );
    }

}