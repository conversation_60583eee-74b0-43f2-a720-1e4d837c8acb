/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.07.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.utils;

import android.app.OplusWhiteListManager;
import android.content.Context;

import com.oplus.clock.common.utils.Log;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

public class ProcessGuard {

    private static final String TAG = "ProcessGuard";

    private static final String PKG_NAME = "com.coloros.alarmclock";
    private static final String WHITELISTMANAGER = "android.app.OppoWhiteListManager";
    private static final String OPLUS_WHITELISTMANAGER = "android.app.OplusWhiteListManager";

    private static final long ONE_MINUTE = 60 * 1000;    // 1 minute.

    public static void startProtect(Context context, String tag) {
        startProtect(context, tag, ONE_MINUTE);
    }

    // This method is used to avoid AlarmClock process killed by system
    // recentTask(available from master 7.1).
    public static void startProtect(Context context, String tag, long mills) {
        if (Utils.isNOrLater()) {
            try {
                Class<?> whiteListMgrCls = Class.forName(getWhiteListClassName());
                Constructor<?> constructor = whiteListMgrCls.getConstructor(Context.class);
                Object oplusWhiteListManager = constructor.newInstance(context);
                Method addStageProtectInfo = whiteListMgrCls.getMethod("addStageProtectInfo",
                        String.class, long.class);
                if (mills < ONE_MINUTE) {
                    mills = ONE_MINUTE;
                }
                addStageProtectInfo.invoke(oplusWhiteListManager, PKG_NAME, mills);
                Log.v(TAG, "startProtect[" + tag + "] " + mills + " ms.");
            } catch (Exception e) {
                Log.e(TAG, "startProtect Exception: " + e.getMessage());
            }
        }
    }

    public static void endProtect(Context context, String tag) {
        if (Utils.isNOrLater()) {
            try {
                Class<?> whiteListMgrCls = Class.forName(getWhiteListClassName());
                Constructor<?> constructor = whiteListMgrCls.getConstructor(Context.class);
                Object oplusWhiteListManager = constructor.newInstance(context);
                Method removeStageProtectInfo = whiteListMgrCls.getMethod("removeStageProtectInfo",
                        String.class);
                removeStageProtectInfo.invoke(oplusWhiteListManager, PKG_NAME);
                Log.v(TAG, "endProtect[" + tag + "].");
            } catch (Exception e) {
                Log.e(TAG, "endProtect Exception: " + e.getMessage());
            }
        }
    }

    private static String getWhiteListClassName() {
        return Utils.isAboveR() ? OPLUS_WHITELISTMANAGER : WHITELISTMANAGER;
    }
}
