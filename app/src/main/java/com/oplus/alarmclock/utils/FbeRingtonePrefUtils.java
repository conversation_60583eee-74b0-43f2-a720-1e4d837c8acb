/************************************************************
 * Copyright 2019 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : fbe ringtone utils.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2019-2-25, yll, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.text.TextUtils;

import com.oplus.clock.common.utils.Log;

import java.util.Map;
import java.util.Set;

class FbeRingtonePrefUtils {

    private static final String TAG = "FbeRingtonePrefUtils";

    private static final String PREFERENCE_NAME = "fbe_ringtone_map_pref";

    static void putRingtoneData(Context context, Uri ringtoneUri, String fileName) {
        Log.d(TAG, "putRingtoneData uri : " + ringtoneUri + "  fileName: " + fileName);
        if (TextUtils.isEmpty(fileName) || (ringtoneUri == null)) {
            return;
        }
        PrefUtils.putString(context, PREFERENCE_NAME, fileName, ringtoneUri.toString());
    }

    static String getFileNameFromUri(Context context, Uri uri){
        if (uri == null) {
            return null;
        }
        String uriStr = uri.toString();
        Map<String, String> allData = null;
        try {
            allData = (Map<String, String>) PrefUtils.getAll(context, PREFERENCE_NAME);
        } catch (Exception e) {
            Log.e(TAG, "getAll error: " + e.getMessage());
        }
        if ((allData == null) || (allData.isEmpty())) {
            Log.d(TAG, "data is null ");
            return null;
        }
        Set<Map.Entry<String, String>> entrySet = allData.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            if (uriStr.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }

}
