/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ColorUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.animation.ArgbEvaluator

fun getColorArray(colorStart: Int, colorEnd: Int, listSize: Int): IntArray {
    return IntArray(listSize).apply {
        val evaluator = ArgbEvaluator()
        val size = (listSize - 1) * 1F
        for (i in 0 until listSize) {
            val percent = i / size
            this[i] = evaluator.evaluate(percent, colorStart, colorEnd) as Int
        }
    }
}