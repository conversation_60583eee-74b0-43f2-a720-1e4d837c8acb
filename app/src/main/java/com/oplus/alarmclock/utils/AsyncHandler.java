/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Helper class for managing the background thread used to perform io operations and
 * handle async broadcasts.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/

package com.oplus.alarmclock.utils;

import android.os.Handler;
import android.os.HandlerThread;

public final class AsyncHandler {
    private static final HandlerThread sHandlerThread = new HandlerThread("AsyncHandler");
    private static final Handler sHandler;

    static {
        sHandlerThread.start();
        sHandler = new Handler(sHandlerThread.getLooper());
    }

    private AsyncHandler() {
    }

    public static void post(Runnable r) {
        sHandler.post(r);
    }
}
