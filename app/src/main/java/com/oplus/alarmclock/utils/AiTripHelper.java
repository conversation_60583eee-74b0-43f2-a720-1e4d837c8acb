/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AiTripHelper.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/08/05
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  Hewei  2020/08/05    1.0            用于breeno速览行程卡片新建日程提醒闹钟
 *  ***************************************************************
 */

package com.oplus.alarmclock.utils;

import android.app.Activity;
import android.content.Intent;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.clock.common.utils.Log;

public class AiTripHelper {
    public static final String OPENER_ID_KEY = "opener_id";
    public static final int OPENER_ID_VALUE = 1;
    public static final String EXTRA_MESSAGE = "android.intent.extra.alarm.MESSAGE";

    private static final String TAG = "AiTripHelper";

    private static final String AI_SET_ALARM_DONE = "AI_SET_ALARM_DONE";
    private static final String AI_PKG_NAME = "com.coloros.assistantscreen";
    private static final int DEFAULT_OPENER_ID = -1;

    public static boolean isFromAiTrip(Activity activity) {

        try {
            if (verifyActivity(activity)) {
                Intent intent = activity.getIntent();
                Log.d(TAG, "isFromAiTrip:");
                return (AlarmClock.ACTION_ALARM_CLOCK_CARD_SET_ALARM.equals(intent.getAction())
                        || AlarmClock.ACTION_ALARM_CLOCK_CARD_SET_ALARM_OLD.equals(intent.getAction()))
                        && (intent.getIntExtra(OPENER_ID_KEY, DEFAULT_OPENER_ID) == OPENER_ID_VALUE);
            }
        } catch (Exception e) {
            Log.e(TAG, "isFromAiTrip e:" + e);
        }

        return false;
    }

    public static Intent getAiTripIntent(Activity activity) {

        Intent intent = new Intent();
        try {
            if (verifyActivity(activity) && isFromAiTrip(activity)) {
                Intent mainIntent = activity.getIntent();
                intent.putExtra(OPENER_ID_KEY, mainIntent.getIntExtra(OPENER_ID_KEY, DEFAULT_OPENER_ID));
                intent.putExtra(EXTRA_MESSAGE, mainIntent.getStringExtra(EXTRA_MESSAGE));
                Log.d(TAG, "getAiTipIntent");
            }
        } catch (Exception e) {
            Log.e(TAG, "getAiTipIntent e:" + e);
        }
        return intent;
    }

    public static void setAlarmDone(Activity activity) {
        if (verifyActivity(activity)) {
            ClockOplusCSUtils.statisticsRouteAlarm(ClockOplusCSUtils.EVENT_SET_ROUTE_ALARM_DONE);

            Intent feedbackIntent = new Intent();
            feedbackIntent.setAction(AI_SET_ALARM_DONE);
            feedbackIntent.setPackage(AI_PKG_NAME);
            activity.sendBroadcast(feedbackIntent);
            activity.finish();
            Log.d(TAG, "setAlarmDone");
        }

    }

    private static boolean verifyActivity(Activity activity) {
        return ((activity instanceof AlarmClock) && !activity.isFinishing());
    }
}