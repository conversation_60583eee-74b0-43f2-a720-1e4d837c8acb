/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ReflectUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import java.lang.reflect.Field
import java.lang.reflect.Method

//执行静态方法
fun invokeStatic(
    cls: Class<*>,
    method: String,
    parameterTypes: Array<Class<*>>,
    vararg args: Any?
): Any? {
    return invoke(cls, method, parameterTypes, null, *args)
}

//执行方法
@Suppress("SpreadOperator")
fun invoke(
    cls: Class<*>,
    method: String,
    parameterTypes: Array<Class<*>>,
    obj: Any? = null,
    vararg args: Any?
): Any? {
    return getMethod(cls, method, *parameterTypes).invoke(obj, *args)
}

//获取方法
@Suppress("SpreadOperator")
fun getMethod(cls: Class<*>, method: String, vararg parameterTypes: Class<*>): Method {
    return cls.getDeclaredMethod(method, *parameterTypes).apply { isAccessible = true }
}

//获取变量
private fun getField(cls: Class<*>, field: String): Field {
    return cls.getDeclaredField(field).apply { isAccessible = true }
}

//设置普通变量
fun setField(cls: Class<*>, obj: Any?, field: String, value: Any): Field {
    return getField(cls, field).apply { set(obj, value) }
}

//设置静态变量
fun setStaticField(cls: Class<*>, field: String, value: Any): Field {
    return setField(cls, null, field, value)
}