package com.oplus.alarmclock.utils;

import static com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper.CANCEL_ALARM;
import static com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper.CLICK_SNOOZE;
import static com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper.METHOD_CLOSE_GARB_ALARM;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper;
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper;
import com.oplus.alarmclock.stopwatch.StopWatchSeedlingHelper;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.clock.common.utils.Log;

public class ProcessCommunicateProvider extends ContentProvider {
    public static final String AUTHORITY = "com.oplus.alarmclock.provider.communicate";
    public static final String METHOD_JUMP_TO_ADDCITY = "jump_to_addcity";
    public static final String METHOD_DRAGONFLY_TIMER = "dragonfly_timer";
    private static final String TAG = "ProcessCommunicateProvider";
    private static final String ACTION_COM_OPLUS_ALARMCLOCK_ADD_WORLD_CLOCK = "com.oplus.alarmclock.ADD_WORLD_CLOCK";
    private static final String IS_SHOW_PANEL = "is_show_panel";
    private static final String IS_FROM_WORD_CLOCK_OR_WIDGET = "is_from_word_clock_or_widget";
    private ProcessCommunicateTimer mTimer;
    private Handler mHandler;

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public String getType(Uri uri) {
        return "";
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        return null;
    }

    @Override
    public boolean onCreate() {
        return false;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        return null;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public Bundle call(String method, String name, Bundle extras) {
        Log.d(TAG, "call:" + method);
        long identity = Binder.clearCallingIdentity();
        switch (method) {
            case METHOD_JUMP_TO_ADDCITY:
                Log.d(TAG, "METHOD_JUMP_TO_ADDCITY");
                jumpToAddCityActivity(getContext());
                break;
            case METHOD_DRAGONFLY_TIMER:
                handleTimer(extras);
                break;
            case StopWatchSeedlingHelper.METHOD_CONTROL_OPERATE:
            case StopWatchSeedlingHelper.METHOD_FUNCTION_OPERATE:
                //因为涉及到更新UI,需要切换到主线程处理
                getHandler().post(() -> StopWatchSeedlingHelper.handleMethodCalls(getContext(), method, extras));
                break;
            case TimerSeedlingHelper.METHOD_OPERATE:
            case TimerSeedlingHelper.METHOD_CANCEL:
            case TimerSeedlingHelper.METHOD_RESTARTTIMER:
                //因为涉及到更新UI,需要切换到主线程处理
                getHandler().post(() -> TimerSeedlingHelper.handleMethodCalls(getContext(), method, extras));
                break;
            case CANCEL_ALARM:
                //流体云卡点击取消稍后提醒
                getHandler().post(() -> AlarmSnoozeSeedingHelper.handleMethodCalls(getContext(), extras));
                break;
            case CLICK_SNOOZE:
                //流体云卡点击稍后提醒
                getHandler().post(() -> AlarmSnoozeSeedingHelper.handleMethodCallSnooze(getContext(), extras));
                break;
            case METHOD_CLOSE_GARB_ALARM:
                getHandler().post(() -> GarbAlarmSeedlingHelper.handleMethodCalls(getContext(), method, extras));
                break;
        }
        Binder.restoreCallingIdentity(identity);
        return null;
    }

    private void jumpToAddCityActivity(Context context) {
        Intent addCityIntent = new Intent(ACTION_COM_OPLUS_ALARMCLOCK_ADD_WORLD_CLOCK);
        addCityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        addCityIntent.putExtra(IS_SHOW_PANEL, true);
        addCityIntent.putExtra(IS_FROM_WORD_CLOCK_OR_WIDGET, true);
        try {
            context.startActivity(addCityIntent);
        } catch (Exception e) {
            Log.e(TAG, "jumpToAddCityActivity error:" + e.getMessage());
        }
    }

    private void handleTimer(Bundle bundle) {
        if (mTimer == null) {
            mTimer = new ProcessCommunicateTimer(getContext());
        }
        mTimer.handleTimer(bundle);
    }

    private Handler getHandler() {
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        return mHandler;
    }
}