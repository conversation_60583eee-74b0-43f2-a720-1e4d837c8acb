/************************************************************
 * Copyright 2019 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : fbe ringtone utils.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2019-2-14, yll, create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;

import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.clock.common.utils.Log;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;

import androidx.annotation.RequiresApi;

public class FbeRingUtils {

    public static final String TAG = "FbeRingUtils";

    private static File sRingFileDir;

    private static void initRingFileDir(Context context) {
        if (null == sRingFileDir) {
            sRingFileDir = Utils.getDeviceContext(context).getDir("rings", Context.MODE_PRIVATE);
        }
        if (!sRingFileDir.exists()) {
            sRingFileDir.mkdirs();
        }
    }

    public static String getRingFilePath(Context context) {
        initRingFileDir(context);
        return sRingFileDir.getAbsolutePath();
    }

    public static boolean checkFBESupport(Context context) {
        return DeviceUtils.isFbeEnabled() && Utils.isNOrLater();
    }

    public static void copyRingFileToInternalStorage(Context context, Uri originRingUri) {
        if ((!checkFBESupport(context)) || (null == originRingUri)) {
            Log.e(TAG, "copy, fbe not support or uri is null");
            return;
        }
        if (AlarmWeatherUtils.isDynamicWeatherAlert(originRingUri.toString())) {
            Log.i(TAG, "there's no need to copy dynamic weather alert file");
            return;
        }
        if (AlarmSpotifyUtils.INSTANCE.isSpotifyRing(originRingUri.toString(), false)) {
            Log.i(TAG, "there's no need to copy spotify alert file");
            return;
        }
        new AsyncCopyTask(context, originRingUri).execute();
    }


    public static void deleteInternalRingFile(Context context, Uri ringtoneUri) {
        if ((!checkFBESupport(context)) || (ringtoneUri == null)) {
            Log.e(TAG, "delete, fbe not support or ringtoneUri is null");
            return;
        }
        if (AlarmWeatherUtils.isDynamicWeatherAlert(ringtoneUri.toString())) {
            Log.i(TAG, "there's no need to delete dynamic weather alert file");
            return;
        }
        if (AlarmSpotifyUtils.INSTANCE.isSpotifyRing(ringtoneUri.toString(), false)) {
            Log.i(TAG, "there's no need to delete spotify alert file");
            return;
        }
        initRingFileDir(context);
        ArrayList<Alarm> allAlarm = AlarmUtils.getAllAlarms(context);
        for (Alarm alarm : allAlarm) {
            if ((null != alarm.getAlert()) && (alarm.isEnabled())) {
                if (TextUtils.equals(alarm.getAlert().toString(), ringtoneUri.toString())) {
                    Log.e(TAG, "there is other alarm using the ring, do not delete");
                    return;
                }
            }
        }
        try {
            String fileName = FbeRingtonePrefUtils.getFileNameFromUri(context, ringtoneUri);
            if (TextUtils.isEmpty(fileName)) {
                Log.e(TAG, "delete file name is null");
                return;
            }
            File file = new File(sRingFileDir, fileName);
            Log.i(TAG, "delete ring file: " + file);
            file.delete();
        } catch (Exception e) {
            Log.e(TAG, "delete file error " + e.getMessage());
        }
    }

    public static void deleteInternalRingFile(Context context, long alarmId) {
        Alarm alarm = AlarmUtils.getAlarm(context, alarmId);
        if (alarm != null) {
            deleteInternalRingFile(context, alarm.getAlert());
        }
    }

    public static void deleteAllInternalRingFile(Context context) {
        if (checkFBESupport(context)) {
            new AsyncDeleteAllTask(context).execute();
        }
    }

    public static String getInternalRingPathFromUri(Context context, Uri ringtoneUri) {
        initRingFileDir(context);
        if (null == ringtoneUri) {
            Log.d(TAG, "ringtoneUri is null");
            return null;
        }
        Log.d(TAG, "ringtoneUri :" + ringtoneUri);
        String fileName = FbeRingtonePrefUtils.getFileNameFromUri(context, ringtoneUri);
        if (TextUtils.isEmpty(fileName)) {
            Log.d(TAG, "getFileNameFromUri null");
            return null;
        }
        File file = new File(sRingFileDir, fileName);
        if (file.exists()) {
            return file.getAbsolutePath();
        }
        return null;
    }

    public static void deleteUnusedRingtoneFile(Context context) {
        if (checkFBESupport(context)) {
            initRingFileDir(context);
            new AsyncDeleteUnusedFileTask(context).execute();
        }
    }

    private static class AsyncDeleteUnusedFileTask extends AsyncTask<Void, Void, Boolean> {
        private WeakReference<Context> mAppContext;

        AsyncDeleteUnusedFileTask(Context context) {
            mAppContext = new WeakReference<>(context.getApplicationContext());
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            Context context = mAppContext.get();
            if ((context != null) && (sRingFileDir != null) && (sRingFileDir.exists())) {
                ArrayList<String> usedfileList = new ArrayList<>();
                ArrayList<Alarm> allAlarm = AlarmUtils.getAllAlarms(context);
                for (Alarm alarm : allAlarm) {
                    if ((null != alarm.getAlert()) && (alarm.isEnabled())) {
                        String fileName = FbeRingtonePrefUtils.getFileNameFromUri(context, alarm.getAlert());
                        if (!TextUtils.isEmpty(fileName)) {
                            usedfileList.add(fileName);
                            Log.d(TAG, "add usedFile：" + fileName);
                        }
                    }
                }
                File[] files = sRingFileDir.listFiles();
                if ((files != null) && (files.length > 0)) {
                    for (File file: files) {
                        Log.d(TAG, "file.getName()：" + file.getName());
                        boolean fileUsed = false;
                        for (int i = 0; i < usedfileList.size(); i++) {
                            if (TextUtils.equals(file.getName(), usedfileList.get(i))) {
                                Log.d(TAG, "file used：" + file.getName());
                                fileUsed = true;
                                break;
                            }
                        }
                        if (!fileUsed) {
                            Log.d(TAG, "delete unused ring file " + file.getAbsolutePath());
                            file.delete();
                        }
                    }
                }
            }
            return true;
        }
    }


    private static class AsyncCopyTask extends AsyncTask<Void, Void, Boolean> {
        private WeakReference<Context> mAppContext;
        private Uri mUri;

        AsyncCopyTask(Context context,  Uri alertUri) {
            mAppContext = new WeakReference<>(context.getApplicationContext());
            mUri = alertUri;
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        @Override
        protected Boolean doInBackground(Void... voids) {
            if (mAppContext.get() != null) {
                initRingFileDir(mAppContext.get());
                Log.d(TAG, "RING_FILE_DIR:" + sRingFileDir + " uri: " + mUri);
                boolean isStorageLegacy = Environment.isExternalStorageLegacy();
                Log.d(TAG, "Environment.isExternalStorageLegacy(): " + isStorageLegacy);

                String ringPath = AlarmRingUtils.getMusicPathFromUriString(mAppContext.get(), mUri.toString());
                if (TextUtils.isEmpty(ringPath)) {
                    Log.d(TAG, "copy file ringPath is null");
                    return false;
                }
                String fileName = getFileNameFromPath(ringPath);
                if (TextUtils.isEmpty(fileName)) {
                    Log.d(TAG, "copy file fileName is null");
                    return false;
                }
                File toFile = new File(sRingFileDir, fileName);
                Log.d(TAG, "copyFile fromPath:" + ringPath + " fileName: " + fileName);

               String fileNameRingtone = FbeRingtonePrefUtils.getFileNameFromUri(mAppContext.get(), mUri);
                if (toFile.exists() && (!TextUtils.isEmpty(fileNameRingtone) && TextUtils.equals(fileNameRingtone,fileName))) {
                    Log.d(TAG, "copyFile file exist");
                    return false;
                }
                FileInputStream fis = null;
                FileOutputStream fos = null;
                boolean success = true;
                try {
                    FbeRingtonePrefUtils.putRingtoneData(mAppContext.get(), mUri, fileName);
                    if (!isStorageLegacy) {
                        try {
                            ParcelFileDescriptor pf = mAppContext.get().getContentResolver().openFileDescriptor(mUri, "r");
                            if (pf != null) {
                                fis = new ParcelFileDescriptor.AutoCloseInputStream(pf);
                            } else {
                                fis = new FileInputStream(ringPath);
                            }
                        } catch (FileNotFoundException e) {
                           Log.e(TAG, "file not found :" + e.getMessage());
                        }
                    } else {
                        fis = new FileInputStream(ringPath);
                    }
                    fos = new FileOutputStream(toFile);
                    int length = -1;
                    byte[] buf = new byte[1024];
                    if (fis != null) {
                        while ((length = fis.read(buf)) != -1) {
                            fos.write(buf, 0, length);
                        }
                        fos.flush();
                    }
                } catch (Exception e) {
                    success = false;
                    Log.e(TAG, "copyFile error: " + e.getMessage());
                } finally {
                    if (fis != null) {
                        try {
                            fis.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (fos != null) {
                        try {
                            fos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
                Log.d(TAG, "copyFile success: " + success + " to :" + toFile);
                return success;
            }
            return false;
        }

        @SuppressLint("getLastPathSegmentRisk")
        private String getFileNameFromPath(String path) {
            return Uri.parse(path).getLastPathSegment();
        }
    }

    private static class AsyncDeleteAllTask extends AsyncTask<Void, Void, Boolean> {

        private WeakReference<Context> mAppContext;

        AsyncDeleteAllTask(Context context) {
            mAppContext = new WeakReference<>(context.getApplicationContext());
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            if (mAppContext.get() != null) {
                if ((sRingFileDir != null) && (sRingFileDir.exists())) {
                   File[] files = sRingFileDir.listFiles();
                   if ((files != null) && (files.length > 0)) {
                       for (File file: files) {
                           file.delete();
                       }
                   }
                }
                Log.d(TAG, "delete all ring file");
                return true;
            }
            return false;
        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            super.onPostExecute(aBoolean);
        }
    }
}
