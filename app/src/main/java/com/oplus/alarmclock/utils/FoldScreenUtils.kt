/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - FoldScreenUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/7/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/7/13     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.content.res.OplusBaseConfiguration
import android.provider.Settings
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log
import com.oplus.content.OplusFeatureConfigManager
import com.oplus.flexiblewindow.FlexibleWindowManager
import com.oplus.util.OplusTypeCastingHelper
import com.oplus.utils.CommonUtil
import java.lang.ref.WeakReference

object FoldScreenUtils {

    /**
     * 世界时钟表盘偏移量
     */
    const val OTHER_WORLDCLOCK_STAND_LOCATION_Y = 80

    /**
     * 孔雀/白天鹅显示较小表盘偏移量
     */
    const val OTHER_TIMER_STAND_LOCATION_Y = -100

    /**
     * 孔雀/白天鹅显示标准表盘偏移量
     */
    const val TIMER_LAYOUT_LOCATION_Y = -100

    /**
     * 计时器孔雀/白天鹅显示较小列表偏移量
     */
    const val TIMER_LAYOUT_SMALL_LOCATION_Y = 120

    /**
     * 显示大小正常
     */
    const val SCREEN_DISPLAY_NORMAL = 0

    /**
     * 显示大小为小
     */
    const val SCREEN_DISPLAY_SMALL = 1

    /**
     * 显示大小为大
     */
    const val SCREEN_DISPLAY_LARGE = 2

    /**
     * 蜻蜓悬停，延迟更新列表高度
     */
    const val STOP_WATCH_DELAY_CHANGE_HEIGHT = 100

    /**
     * 悬停进入时，延迟切换悬停模式，等待UI加载完成
     */
    const val HOVER_DELAY = 200L

    /**
     * 世界时钟蜻蜓悬停间距
     */
    const val DRAGONFLY_WORLDCLOCK_STAND_LIST_LOCATION_Y = 150
    const val DRAGONFLY_WORLDCLOCK_SMALL_LIST_LOCATION_Y = 300
    const val DRAGONFLY_WORLDCLOCK_LARGE_LIST_LOCATION_Y = 70

    /**
     * 秒表蜻蜓悬停间距
     */
    const val DRAGONFLY_STOPWATCH_STAND_LIST_LOCATION_Y = 200
    const val DRAGONFLY_STOPWATCH_SMALL_LIST_LOCATION_Y = 400
    const val DRAGONFLY_STOPWATCH_LARGE_LIST_LOCATION_Y = 80

    /**
     * 计时器蜻蜓悬停间距
     */
    const val DRAGONFLY_TIMER_STAND_LIST_LOCATION_Y = 200
    const val DRAGONFLY_TIMER_SMALL_LIST_LOCATION_Y = 400
    const val DRAGONFLY_TIMER_LARGE_LIST_LOCATION_Y = 70

    /**
     * 蜻蜓正常模式下表盘悬停偏移量
     */
    const val DRAGONFLY_CLOCK_DIAL_LOCATION_Y = 5
    const val DRAGONFLY_CLOCK_DIAL_SMALL_LOCATION_Y = 30

    const val DIAL_TEXT_SCALE_MOVE = 0.82F

    /**
     * 浮窗缩小比例
     */
    const val FLOATING_WINDOW_SCALE = 0.8F

    /**
     * 浮窗秒表底部内容缩放比例
     */
    const val FLOATING_WINDOW_STOP_WATCH_SCALE = 0.72F
    const val FLOATING_WINDOW_STOP_WATCH_TITLE_SCALE = 0.7F
    const val FLOATING_WINDOW_TEXT_SCALE = 1.2F
    const val FLOATING_WINDOW_TEXT_MOVE_SCALE = 1.25F

    /**
     * 折叠屏表盘缩放大小
     */
    const val DIAL_SCALE_MOVE = 1.22F

    /**
     * 秒表表盘时间偏移量
     */
    const val STOP_WATCH_TIME_TOP_MARGIN = 94
    const val NUMBER_TWO = 2F

    const val DIAL_SCALE_DELAY = 500

    /**
     * 悬停场景切换时间
     */
    const val CHANG_SCENE_DURATION = 180

    private const val TAG = "FoldScreenUtils"
    private const val SYSTEM_FOLDING_MODE_KEYS = "oplus_system_folding_mode"
    private const val SYSTEM_FOLDING_MODE_OPEN = 1
    private const val SYSTEM_FOLDING_MODE_CLOSE = 0

    /**
     * 蜻蜓
     */
    private const val FEATURE_FOLD = "oplus.hardware.type.fold"
    private const val DRAGONFLY = "oplus.software.fold_remap_display_disabled"
    private const val MINI_APP = "oplus.software.support_secondary_mini_app"

    /**
     * 平板最小sw
     */
    private const val LARGE_SCREEN_SW = 685

    /**
     * 中屏最小sw
     */
    private const val MID_SCREEN_SW = 534

    /**
     * 显示大小标准大小
     */
    const val SCREEN_DISPLAY_MODEL_KEY_DEFAULT = 2

    /**
     * 设置里面的显示大小KEY值
     */
    private const val SETTINGS_SCREEN_ZOOM: String = "settings_screen_zoom"
    private const val SETTINGS_SCREEN_ZOOM_INDEX_KEY: String = "settings_screen_zoom_index_key"
    private const val DISPLAY_DENSITY_INDEX_MANUAL: String = "display_density_index_manual"

    enum class UiMode {
        SMALL, NORMAL, MIDDLE, LARGE_VERTICAL, LARGE_HORIZONTAL
    }

    // 在configChange时需要切换所有activity方向时才使用 UIConfigMonitor里保存的ResponsiveUIConfig.uiStatus 的值，
    // 否则使用activity重新获取一遍uiStatus，避免获取到的值不准确
    @JvmStatic
    fun updateUIOrientation(activity: Activity, isConfigChange: Boolean = false) {
        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get()
        if (activityReference == null) {
            Log.w(TAG, "activity memory has been recycled and cannot be updateUIOrientation")
            return
        }

        val orientation = if (isDragonflySmallScreen(activityReference) || (!isDragonfly() && (isScreenRealUnfold() || isRealOslo()))) {
            ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        } else {
            ActivityInfo.SCREEN_ORIENTATION_NOSENSOR
        }
        if (activityReference.requestedOrientation != orientation) {
            activityReference.requestedOrientation = orientation
        }
    }

    //折叠屏，屏幕是否展开
    @JvmStatic
    fun isScreenUnfold(activity: Context): Boolean {
        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get() ?: return false
        return activityReference.resources.getBoolean(R.bool.is_unfold)
    }

    /**注意 此方法在大屏 分屏情况下，获取的值是 FOLD，也就是小屏*/
    @JvmStatic
    private fun isScreenFold(activity: Context): Boolean {
        val value = ResponsiveUIConfig.getDefault(activity).uiStatus.value
        return (value == null) || (value == UIConfig.Status.FOLD)
    }

    /**
     * 获取屏幕真实的折叠和展开状态，true 则表示是大屏
     * 即使在大屏分屏的情况下，也能获取到是大屏状态
     * */
    @JvmStatic
    fun isScreenRealUnfold(): Boolean {
        val context = AlarmClockApplication.getInstance()
        var realFoldState = SYSTEM_FOLDING_MODE_CLOSE
        try {
            realFoldState = Settings.Global.getInt(
                    context.contentResolver,
                    SYSTEM_FOLDING_MODE_KEYS,
                    SYSTEM_FOLDING_MODE_CLOSE
            )
        } catch (e: Exception) {
            Log.e(TAG, "read real screen status error ${e.message} ")
        }
        return realFoldState == SYSTEM_FOLDING_MODE_OPEN
    }

    /**
     * 是否是平板
     * 物理获取方式，即使分屏情况下，获取到的也是true
     * */
    @JvmStatic
    fun isRealOslo(): Boolean {
        return DeviceUtils.isTableScreen(AlarmClockApplication.getInstance())
    }

    /**
     * 是否是平板横屏
     * 根据屏幕尺寸判断，返回的是物理的横竖屏
     */
    @JvmStatic
    fun isRealOsloLandscape(): Boolean {
        return isRealOslo() && DeviceUtils.isLandscapeScreen()
    }

    /**
     * 是否是平板竖屏
     * 根据屏幕尺寸判断，返回的是物理的横竖屏
     */
    @JvmStatic
    fun isRealOsloPortrait(): Boolean {
        return isRealOslo() && !DeviceUtils.isLandscapeScreen()
    }

    /**
     * 是否是分屏模式（孔雀和平板横屏没有分屏模式）
     * 孔雀分屏后是常规屏
     * 平板横屏分屏后：1/3是常规屏；1/2是孔雀屏；2/3是平板
     * @return
     */
    @JvmStatic
    fun isInDealMultiWindowMode(isInMultiWindowMode: Boolean): Boolean {
        return if (isScreenRealUnfold() || isRealOsloLandscape()) {
            false
        } else isInMultiWindowMode
    }

    /**
     * 判断设备是否是蜻蜓
     * FEATURE_FOLD：是否有折叠能力
     * DRAGONFLY：禁用大小屏切换的能力
     * @return
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun isDragonfly(): Boolean {
        return try {
            val dragonfly = OplusFeatureConfigManager.getInstance().hasFeature(DRAGONFLY)
            isFold() && dragonfly
        } catch (e: Exception) {
            Log.d(TAG, "isDragonfly e :$e")
            false
        }
    }

    /**
     * 是否有折叠能力
     */
    @JvmStatic
    fun isFold(): Boolean {
        return OplusFeatureConfigManager.getInstance().hasFeature(FEATURE_FOLD)
    }

    /**
     *  判断是否为平板分屏且大于平板的最小尺寸
     */
    @JvmStatic
    fun isMidLargeScreen(context: Context, uiMode: UiMode?): Boolean {
        val sw: Int = context.resources.configuration.smallestScreenWidthDp
        if (UiMode.MIDDLE == uiMode && sw >= LARGE_SCREEN_SW) {
            return true
        }
        return false
    }

    @JvmStatic
    fun isPadSplitMiddle(context: Context, isSplit: Boolean): Boolean {
        val sw: Int = context.resources.configuration.smallestScreenWidthDp
        if (isRealOslo() && isSplit && sw >= LARGE_SCREEN_SW) {
            return true
        }
        return false
    }

    /**
     * 判断是否是蜻蜓小屏
     */
    @JvmStatic
    fun isDragonflySmallScreen(context: Context): Boolean {
        val status = Settings.Global.getInt(context.contentResolver, SYSTEM_FOLDING_MODE_KEYS, -1)
        return isDragonfly() && (status == SYSTEM_FOLDING_MODE_CLOSE)
    }

    /**
     * 上下折叠屏是否支持mini app
     */
    @JvmStatic
    fun isSupportMiniApp(context: Context): Boolean {
        return try {
            AppFeatureUtils.isFeatureSupport(context.contentResolver, MINI_APP)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 当前显示显示大小
     */
    @JvmStatic
    fun screenDisplayModel(): Int {
        val resources = AlarmClockApplication.getInstance().resources
        val curConfig = resources.configuration
        val defaultDpi = CommonUtil.getDefaultDisplayDensity()
        val curDpi = curConfig.densityDpi
        return if (curDpi > defaultDpi) {
            SCREEN_DISPLAY_LARGE
        } else if (curDpi < defaultDpi) {
            SCREEN_DISPLAY_SMALL
        } else {
            SCREEN_DISPLAY_NORMAL
        }
    }

    /**
     * 是否平板的大屏屏幕
     *
     * @param context
     */
    @JvmStatic
    fun isLargeScreen(context: Context): Boolean {
        val sw: Int = context.resources.configuration.smallestScreenWidthDp
        if (sw >= MID_SCREEN_SW) {
            return true
        }
        return false
    }

    /**
     * 当前是否未浮窗
     *
     */
    @JvmStatic
    fun isFlexibleScenario(context: Context): Boolean {
        kotlin.runCatching {
            val baseConfig = OplusTypeCastingHelper.typeCasting<OplusBaseConfiguration>(OplusBaseConfiguration::class.java,
                    context.resources.configuration)
                    ?: return false
            val newScenario = baseConfig.mOplusExtraConfiguration.scenario
            return newScenario == FlexibleWindowManager.LAUNCH_SCENARIO_FLEXIBLE
        }.onFailure {
            Log.e(TAG, "isFlexibleScenario failed")
        }
        return false
    }

    /**
     * 获取当前屏幕状态
     * 平板	折叠屏展开  是否横屏  是否分屏  屏幕尺寸（sw） 布局类型
     * 是	   NA	    是	    否	      NA	     大横屏
     * 是	   NA	    是	    是	     <600	      常规
     * 是	   NA	    是	    是	     >=600	       中
     * 是	   NA	    否	    否	      NA	     大竖屏
     * 是	   NA	    否	    是	     <600	       小
     * 是	   NA	    否	    是	     >=600	       中
     * 否	   否	    NA	    否	      NA	      常规
     * 否	   否	    NA	    是	      NA	       小
     * 否	   是	    NA	    否	      NA	       中
     * 否	   是	    NA	    是	      NA	      常规
     * 蜻蜓     NA       NA      NA        NA          常规
     * @param context
     * @param isInMultiWindowMode 是否分屏
     */
    @JvmStatic
    fun uiMode(context: Context, isInMultiWindowMode: Boolean): UiMode {
        return if (isRealOslo()) {
            val sw = context.resources.configuration.smallestScreenWidthDp
            val isLand = DeviceUtils.isLandscapeScreen()
            Log.d(TAG, "uiMode pad isInMultiWindowMode:$isInMultiWindowMode, sw:$sw, land:$isLand")
            if (isLand) {
                if (isInMultiWindowMode) {
                    if (sw < MID_SCREEN_SW) {
                        UiMode.NORMAL
                    } else {
                        UiMode.MIDDLE
                    }
                } else {
                    UiMode.LARGE_HORIZONTAL
                }
            } else {
                if (isInMultiWindowMode) {
                    if (sw < MID_SCREEN_SW) {
                        UiMode.SMALL
                    } else {
                        UiMode.MIDDLE
                    }
                } else {
                    UiMode.LARGE_VERTICAL
                }
            }
        } else {
            Log.d(TAG, "uiMode not pad isInMultiWindowMode:$isInMultiWindowMode")
            if (isDragonfly()) {
                UiMode.NORMAL
            } else {
                if (isScreenRealUnfold()) {
                    if (isInMultiWindowMode) {
                        UiMode.NORMAL
                    } else {
                        UiMode.MIDDLE
                    }
                } else {
                    if (isInMultiWindowMode) {
                        UiMode.SMALL
                    } else {
                        UiMode.NORMAL
                    }
                }
            }
        }
    }

    /**
     * 获取系统设置-显示大小存储的值
     */
    @JvmStatic
    fun getScreenZoomSettingsIndex(context: Context): Int {
        val sharedPreferences = context.getSharedPreferences(
            SETTINGS_SCREEN_ZOOM,
            Context.MODE_PRIVATE
        )
        val currentIndex = if ((sharedPreferences != null) && sharedPreferences.contains(SETTINGS_SCREEN_ZOOM_INDEX_KEY)) {
            sharedPreferences.getInt(SETTINGS_SCREEN_ZOOM_INDEX_KEY, SCREEN_DISPLAY_MODEL_KEY_DEFAULT)
        } else {
            Settings.System.getInt(context.contentResolver, DISPLAY_DENSITY_INDEX_MANUAL, SCREEN_DISPLAY_MODEL_KEY_DEFAULT)
        }
        return currentIndex
    }
}