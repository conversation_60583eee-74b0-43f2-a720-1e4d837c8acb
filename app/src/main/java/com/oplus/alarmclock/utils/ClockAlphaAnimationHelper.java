/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockAlphaAnimationHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/2/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/2/7     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.clock.common.utils.Log;

public class ClockAlphaAnimationHelper {

    public static final int DURATION_160 = 160;
    private static final String TAG = "ClockAnimationUtils";
    private static final float FLOAT_1 = 1f;
    private static final float FLOAT_0 = 0f;

    private boolean mAnimating = false;

    public interface UpdateMenuItemCallBack {
        void updateMenuItem();
    }

    public boolean isAnimating() {
        return mAnimating;
    }

    public void playAlphaOutInAnimation(View view, UpdateMenuItemCallBack callBack) {
        playAlphaOutInAnimation(view, callBack, false);
    }

    public void playAlphaOutInAnimation(View view, UpdateMenuItemCallBack callBack, boolean clearTitle) {
        Log.d(TAG, "playAlphaOutInAnimation mAnimating: " + mAnimating);
        if (mAnimating) {
            return;
        }
        mAnimating = true;
        ObjectAnimator alphaOutAnimation = ObjectAnimator.ofFloat(view, "alpha", FLOAT_1, FLOAT_0);
        ObjectAnimator alphaInAnimation = ObjectAnimator.ofFloat(view, "alpha", FLOAT_0, FLOAT_1);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.setDuration(DURATION_160);
        animatorSet.setInterpolator(new LinearInterpolator());
        animatorSet.play(alphaOutAnimation).before(alphaInAnimation);
        alphaOutAnimation.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) { }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (callBack != null) {
                    callBack.updateMenuItem();
                    if ((view != null) && (view instanceof COUIToolbar) && clearTitle) {
                        ((COUIToolbar) view).setTitle("");
                    }
                }
            }
            @Override
            public void onAnimationCancel(Animator animation) {
            }
            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        alphaInAnimation.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }
            @Override
            public void onAnimationEnd(Animator animation) {
                mAnimating = false;
            }
            @Override
            public void onAnimationCancel(Animator animation) {
                mAnimating = false;
            }
            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        animatorSet.start();
    }
}
