/****************************************************************
 ** Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - NotificationUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: yang<PERSON>uang
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  yangchenguang  2024/12/11     1.0            build this module
 ****************************************************************/

package com.oplus.alarmclock.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Context.NOTIFICATION_SERVICE
import android.content.pm.ServiceInfo
import android.service.notification.StatusBarNotification
import androidx.core.app.ServiceCompat.startForeground
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AlarmStateManager
import com.oplus.alarmclock.stopwatch.StopwatchNotificationManager
import com.oplus.alarmclock.timer.TimerNotificationManager
import com.oplus.clock.common.utils.Log

class NotificationUtils {
    companion object {
        private const val TAG = "NotificationUtils"
        //前台服务渠道ID
        const val FOREGROUND_SERVICE_CHANNEL_ID = "clock_foreground_service_channel_id"
        //流体云去重字段
        const val FLUID_SERVICE_ID = "op_fluid_serviceId"
        const val STOP_TIMER_FOREGROUND_SERVICE = "stop_timer_foreground_service"
        const val STOP_STOPWATCH_FOREGROUND_SERVICE = "stop_stopwatch_foreground_service"
        const val ALARM = 0
        const val TIMER = 1
        const val STOPWATCH = 2
    }

    /**
     *
     * 前台服务通知设置为普通通知格式
     * @param notificationID 旧通知ID
     * @param cardType 流体云类型：闹钟、计时器、秒表
     */
    fun sendForegroundNotificationByOld(
        context: Context?,
        service: Service?,
        notificationID: Int?,
        cardType: Int?
    ) {
        if (context == null || service == null || notificationID == null || cardType == null) {
            return
        }
        val notification = getNotification(context, notificationID)
        notification?.apply {
            startForeground(
                service,
                notificationID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
            )
            Log.d(TAG, "sendForegroundNotificationByOld: notification: $notification")
        } ?: sendForegroundNotification(context, service, notificationID, cardType)
    }

    fun sendForegroundNotification(
        context: Context?,
        service: Service?,
        notificationID: Int?,
        cardType: Int?
    ) {
        if (context == null || service == null || notificationID == null || cardType == null) {
            return
        }
        if (Utils.isAboveO()) {
            val nm = context.getSystemService(NOTIFICATION_SERVICE) as? NotificationManager
            if (nm != null) {
                val channelName = getChannelName(context, cardType)
                val channelID = getChannelID(cardType)
                var channel: NotificationChannel? = null
                if (channelID != null && channelName != null) {
                    channel = NotificationChannel(
                        channelID, channelName,
                        NotificationManager.IMPORTANCE_LOW
                    )
                }
                channel?.let {
                    nm.createNotificationChannel(it)
                }
                val builder = Notification.Builder(context, channelID)
                val notification = builder.build()
                startForeground(
                    service,
                    notificationID,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                )
                Log.i(TAG, "sendForegroundNotification: oldNotification channel")
            }
        } else {
            Log.e(TAG, "Failed to get NotificationManager")
        }
    }


    /**
     *
     * 闹钟响铃、计时器响铃前台服务使用默认通知
     *
     */
    fun sendDefaultForegroundNotification(
        context: Context?,
        service: Service?,
    ) {
        if (context == null || service == null) {
            return
        }
        if (Utils.isAboveO()) {
            val nm = context.getSystemService(NOTIFICATION_SERVICE) as? NotificationManager
            if (nm != null) {
                val channel = NotificationChannel(
                    FOREGROUND_SERVICE_CHANNEL_ID, context.getString(R.string.alarm_service),
                    NotificationManager.IMPORTANCE_LOW
                )
                nm.createNotificationChannel(channel)
                val builder = Notification.Builder(context, FOREGROUND_SERVICE_CHANNEL_ID)
                val notification = builder.build()
                startForeground(
                    service,
                    Int.MIN_VALUE,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                )
                Log.i(TAG, "startForegroundService with Default Notification")
            } else {
                Log.e(TAG, "Failed to get NotificationManager")
            }
        }
    }


    private fun getNotification(context: Context, notificationID: Int): Notification? {
        val nm = context.getSystemService(NOTIFICATION_SERVICE) as? NotificationManager
        val activeNotifications: Array<out StatusBarNotification>? =
            nm?.getActiveNotifications()
        activeNotifications?.forEach {
            if (it.id == notificationID && it.packageName == context.packageName) {
                return it.notification
            }
        }
        return null
    }

    /**
     *
     * 获取闹钟稍后提醒、计时中Notification_ChannelName
     *
     */
    private fun getChannelName(context: Context, cardType: Int): String? {
        return when (cardType) {
            ALARM -> context.getString(R.string.clock_notification_label)
            STOPWATCH -> context.getString(R.string.stopwatch_notification_label)
            TIMER -> context.getString(R.string.timer_notification_label)
            else -> null
        }
    }
    /**
     *
     * 获取闹钟稍后提醒、计时中Notification_ChannelID
     *
     */
    private fun getChannelID(cardType: Int): String? {
        return when (cardType) {
            ALARM -> AlarmStateManager.CHANNEL_ID
            STOPWATCH -> StopwatchNotificationManager.STOPWATCH__NOTIFICATION_CHANNEL_ID
            TIMER -> TimerNotificationManager.TIMER_NOTIFICATION_CHANNEL_ID
            else -> null
        }
    }
}