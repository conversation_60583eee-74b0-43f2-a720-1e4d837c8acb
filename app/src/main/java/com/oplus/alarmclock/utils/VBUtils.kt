/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - VBUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
@file:Suppress("UNCHECKED_CAST")

package com.oplus.alarmclock.utils

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import java.lang.reflect.Type

fun <T : ViewBinding> initViewBinding(type: Type, inflater: LayoutInflater): T {
    return inflate(type as Class<T>, inflater, null)
}

fun <T : ViewBinding> initViewBinding(cls: Class<T>, inflater: LayoutInflater): T {
    return inflate(cls, inflater)
}

fun <T : ViewBinding> initViewBinding(
    cls: Class<T>,
    inflater: LayoutInflater,
    container: ViewGroup?
): T {
    return inflate(cls, inflater, container)
}

fun <T : ViewBinding> inflate(cls: Class<T>, inflater: LayoutInflater): T {
    return invoke(cls, "inflate", arrayOf(LayoutInflater::class.java), null, inflater) as T
}

fun <T : ViewBinding> inflate(cls: Class<T>, inflater: LayoutInflater, parents: ViewGroup?): T {
    return invoke(
        cls,
        "inflate",
        arrayOf(LayoutInflater::class.java, ViewGroup::class.java, Boolean::class.java),
        null,
        inflater,
        parents,
        false
    ) as T
}
