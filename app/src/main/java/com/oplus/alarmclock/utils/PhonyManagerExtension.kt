/*
 *
 *  * ***************************************************************
 *  * Copyright (C), 2010-2021, OPLUS Mobile Comm Corp., Ltd.
 *  * VENDOR_EDIT
 *  * File:  - PhonyManagerExtension.kt
 *  * Version: 1.0
 *  * Date : 2021/08/13
 *  * Author: hewei
 *  * ***************************************************************
 *
 */

package com.oplus.alarmclock.utils

import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.telephony.TelephonyManager.CALL_STATE_IDLE
import com.oplus.clock.common.utils.Log

private const val TAG = "PhonyManagerExtension"

fun TelephonyManager.getTelephonyCallState(): Int {

    return if (!com.oplus.alarmclock.utils.Utils.isAboveS()) {
        callState
    } else {
        CALL_STATE_IDLE
    }
}

fun TelephonyManager.listen(listener: PhoneStateListener, events: Int) {
    if (!Utils.isAboveS()) {
        listen(listener, events)
    } else {
        Log.i(TAG, "S do not invoke listen!")
    }
}


