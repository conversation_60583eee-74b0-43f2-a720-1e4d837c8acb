/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmAdapterSpaceItemDecoration.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/5/25
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin     2022/5/25     1.0            add file
 ****************************************************************/
@file:Suppress("FuncSingleCommentRule", "NestedBlockDepth", "LongMethod", "WorldClockAnimationManager", "EmptyIfBlock",
        "NoBlankLineBeforeRbrace", "ParameterListWrapping", "SpacingAroundParens", "ParameterStyleBracesRule")

package com.oplus.alarmclock.utils

import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode

class CityAdapterSpaceItemDecoration(
    private val isHover: Boolean = false
) : RecyclerView.ItemDecoration() {

    private val m16Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_16)
    }
    private val m115Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_96)
    }
    private val m6Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_6)
    }

    override fun getItemOffsets(outRect: Rect, view: View,
                                parent: RecyclerView, state: RecyclerView.State) {
        val lp = view.findViewById<View>(R.id.content_layout) ?: return
        (parent.adapter as? CityListAdapter)?.apply {
            when (uiMode) {
                UiMode.LARGE_VERTICAL -> {
                    if (list.size == 1) {
                        setMarginHorizontal(lp, m115Dp, m115Dp)
                    } else {
                        setAlarmItemMargin(parent.getChildAdapterPosition(view), lp)
                    }
                }
                UiMode.MIDDLE -> {
                    if (isHover) {
                        setAlarmItemMargin(parent.getChildAdapterPosition(view), lp)
                    } else {
                        //折叠屏
                        setAlarmItemMargin(parent.getChildAdapterPosition(view), lp)
                    }
                }
                else -> setMarginHorizontal(lp, m16Dp, m16Dp)
            }
        }
    }

    /**
     * 设置闹钟的左右margin
     */
    private fun setAlarmItemMargin(potion: Int, lp: View) {
        if (potion % 2 == 0) {
            if (Utils.isRtl()) {
                setMarginHorizontal(lp, m6Dp, m16Dp)
            } else {
                setMarginHorizontal(lp, m16Dp, m6Dp)
            }
        } else {
            if (Utils.isRtl()) {
                setMarginHorizontal(lp, m16Dp, m6Dp)
            } else {
                setMarginHorizontal(lp, m6Dp, m16Dp)
            }
        }
    }

    /**
     * 设置item的padding
     */
    private fun setMarginHorizontal(lp: View, start: Int, end: Int) {
        lp.apply {
            val layoutP = layoutParams as ViewGroup.MarginLayoutParams
            layoutP.leftMargin = start
            layoutP.rightMargin = end
            layoutParams = layoutP
        }
    }
}