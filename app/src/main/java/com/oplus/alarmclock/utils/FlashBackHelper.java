package com.oplus.alarmclock.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.flashbacksdk.FlashViews;
import com.oplus.flashbacksdk.FlashViewsManager;
import com.oplus.flashbacksdk.IViewClickListener;
import com.coloros.widget.commondata.Constants;

import java.util.HashMap;
import java.util.Map;

public class FlashBackHelper {
    private static final String TAG = "FlashBackHelper";
    /**
     * 给闪回发送数据间隔时间
     */
    private static final int INTERVAL_TIME = 500;
    private static final String FLASH_VIEWS_FOR_AIRVIEW_ENABLE = "flash_views_for_airview_enable";
    private static final long THOUSAND = 1000;
    private static final int ZERO = 0;
    private static final long ELAPSED_TIME = 1000;
    private static final int LIMIT_MINUTE = 1;
    private static final int LIMIT_SECOND = 30;
    private static final int FIFTEEN = 15;
    private static final int SIXTY = 60;
    private static final Map<String, FlashViewsManager> sManagers = new HashMap<>();
    private static final Map<String, OnClickListener> sListeners = new HashMap<>();
    private static final Map<String, Integer> sFlashRemove = new HashMap<>();
    private static Context sContext;
    private static FlashBackHelper sInstance = null;
    private long mStopWatchTag = 0;
    private long mTimerTag = 0;

    /**
     * 计时器当前时间
     */
    private String mCurrentTimerTitle = "";
    /**
     * 计时器当前时间
     */
    private long mTimerCurrentTime;

    /**
     * 秒表当前时间
     */
    private String mCurrentStopWatchTitle = "";
    /**
     * 秒表当前时间
     */
    private long mStopWatchCurrentTime;

    private FlashBackHelper() {
        sContext = AlarmClockApplication.getInstance().getApplicationContext();
    }

    @TargetApi(30)
    public static synchronized FlashBackHelper getInstance() {
        if (sInstance == null) {
            Log.v(TAG, "sInstance == null");
            sInstance = new FlashBackHelper();
        }
        return sInstance;
    }

    public static void initManager(String managerKey, String title, String content, String leftBtnStr, int leftBtnColor, int leftBtnContentColor, String rightBtnStr, int rightBtnColor, int rightBtnContentColor, CreateListener createListener) {
        FlashViewsManager manager = new FlashViewsManager.Builder(sContext)
                .setFlashViewsLayout(FlashViews.LAYOUT_BUTTON_TWO)
                .build(new FlashViewsManager.ResultCallback() {
                    @Override
                    public void onCreated() {
                        if ((sManagers.size() > 0) && (sManagers.get(managerKey) != null)) {
                            FlashViewsManager localManager = sManagers.get(managerKey);
                            localManager.setTitleColor(sContext.getResources().getColor(R.color.flash_back_title_color));
                            setTitle(localManager, title);
                            localManager.setContentColor(sContext.getResources().getColor(R.color.flash_back_content_color));
                            setContent(localManager, content);
                            localManager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, leftBtnColor);
                            localManager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, leftBtnContentColor);
                            localManager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, leftBtnStr);
                            localManager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, rightBtnColor);
                            localManager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, rightBtnContentColor);
                            localManager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, rightBtnStr);
                            localManager.setReturnActivity(getReturnIntent(managerKey));
                            localManager.setOnClickListener(FlashViews.VIEW_BUTTON_LEFT, new IViewClickListener() {
                                @Override
                                public void onClick() {
                                    if ((sListeners.size() > 0) && (sListeners.get(managerKey) != null)) {
                                        sListeners.get(managerKey).onLeftButtonClick();
                                    }
                                }
                            });
                            localManager.setOnClickListener(FlashViews.VIEW_BUTTON_RIGHT, new IViewClickListener() {
                                @Override
                                public void onClick() {
                                    if ((sListeners.size() > 0) && (sListeners.get(managerKey) != null)) {
                                        sListeners.get(managerKey).onRightButtonClick();
                                    }
                                }
                            });
                        }
                        if (createListener != null) {
                            createListener.onCreated();
                        }
                    }

                    @Override
                    public void onDestroyed() {
                        Log.v(TAG, "sManager onDestroyed key:" + managerKey);
                        if (!sFlashRemove.containsKey(managerKey)) {
                            if (sManagers.size() > 0) {
                                sManagers.remove(managerKey);
                            }
                        } else {
                            sFlashRemove.remove(managerKey);
                        }

                    }
                });
        sManagers.put(managerKey, manager);
    }

    public void setListener(String managerKey, OnClickListener onClickListener) {
        if (!isSupportFlashBack()) {
            return;
        }
        if (!sListeners.containsKey(managerKey)) {
            sListeners.put(managerKey, onClickListener);
        }
    }

    public void sendData(String managerKey, String title, String content,
                         String leftBtnStr, int leftBtnColor, int leftBtnContentColor,
                         String rightBtnStr, int rightBtnColor, int rightBtnContentColor,
                         OnClickListener onClickListener) {
        if (!isSupportFlashBack()) {
            return;
        }
        if (!sManagers.containsKey(managerKey)) {
            initManager(managerKey, title, content, leftBtnStr, leftBtnColor, leftBtnContentColor, rightBtnStr, rightBtnColor, rightBtnContentColor, null);
            setListener(managerKey, onClickListener);
        } else {
            if (Constants.CLOCK_TIMER_FLASHBACK_KEY.equals(managerKey)) {
                sendData(managerKey, title);
                return;
            }
            long nowTime = System.currentTimeMillis();
            if (!mCurrentStopWatchTitle.equals(title)) {
                mStopWatchCurrentTime = nowTime;
                mCurrentStopWatchTitle = title;
            }
            long diffTime = nowTime - mStopWatchCurrentTime;
            if (diffTime / INTERVAL_TIME == 1 || diffTime == 0) {
                if (diffTime / INTERVAL_TIME == 1) {
                    mStopWatchCurrentTime = nowTime;
                }
                FlashViewsManager manager = getCurrentManager(managerKey);
                if ((manager != null) && (manager.isBinding())) {
                    setTitle(manager, title);
                }
            }

        }
    }

    public void sendData(String key, String title) {
        if (!isSupportFlashBack()) {
            return;
        }
        long nowTime = System.currentTimeMillis();
        if (!mCurrentTimerTitle.equals(title)) {
            mTimerCurrentTime = nowTime;
            mCurrentTimerTitle = title;
        }
        long diffTime = nowTime - mTimerCurrentTime;
        if (diffTime / INTERVAL_TIME == 1 || diffTime == 0) {
            if (diffTime / INTERVAL_TIME == 1) {
                mTimerCurrentTime = nowTime;
            }
            FlashViewsManager manager = getCurrentManager(key);
            if ((manager != null) && manager.isBinding()) {
                manager.setTitle(title);
            }
        }
    }

    public void removeListener(String key) {
        sListeners.remove(key);
    }

    public boolean haveListener(String key) {
        return sListeners.containsKey(key);
    }

    public boolean haveManager(String key) {
        return sManagers.containsKey(key);
    }

    public void requestMarker(String key, long remainTime) {
        if (!isSupportFlashBack() || !isMarker(key, remainTime)) {
            return;
        }
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.requestFocus();
        }
    }

    private static Intent getReturnIntent(String managerKey) {
        Intent intent = new Intent(sContext, AlarmClock.class);
        intent.setAction("com.oplus.alarmclock.AlarmClock");
        if (Constants.CLOCK_STOPWATCH_FLASHBACK_KEY.equals(managerKey)) {
            intent.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_STOPWATCH);
        } else if (Constants.CLOCK_TIMER_FLASHBACK_KEY.equals(managerKey)) {
            intent.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_OPLUSTIME);
        }
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        return intent;
    }

    private boolean isMarker(String key, long remainTime) {
        if (Constants.CLOCK_STOPWATCH_FLASHBACK_KEY.equals(key)) {
            if ((SystemClock.elapsedRealtime() - mStopWatchTag) > ELAPSED_TIME) {
                mStopWatchTag = SystemClock.elapsedRealtime();
                int second = (int) (remainTime / THOUSAND % SIXTY);
                return second % FIFTEEN == ZERO;
            }
            return false;
        } else if (Constants.CLOCK_TIMER_FLASHBACK_KEY.equals(key)) {
            if ((SystemClock.elapsedRealtime() - mTimerTag) > ELAPSED_TIME) {
                mTimerTag = SystemClock.elapsedRealtime();
                int minute = (int) (remainTime / (SIXTY * THOUSAND) % SIXTY);
                int second = (int) (remainTime / THOUSAND % SIXTY);
                return (minute >= LIMIT_MINUTE) && (second % LIMIT_SECOND == ZERO);
            }
            return false;
        }
        return false;
    }


    private FlashViewsManager getCurrentManager(String managerKey) {
        return sManagers.size() > 0 ? sManagers.get(managerKey) : null;
    }

    private static void setTitle(FlashViewsManager manager, String title) {
        if (!TextUtils.isEmpty(title)) {
            manager.setTitle(title);
        }
    }

    private static void setContent(FlashViewsManager manager, String content) {
        if (!TextUtils.isEmpty(content)) {
            manager.setContent(content);
        }
    }

    public void setContent(String key, String contentStr) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.setContent(contentStr);
        }
    }

    public void setStopWatchPlay(String key) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_left_red_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getString(R.string.text_timer_btn_pause));
            manager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_right_green_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getString(R.string.record));
        }
    }

    public void setStopWatchPause(String key) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_left_green_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getString(R.string.text_timer_btn_continue));
            manager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_right_gray_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_content_black_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getString(R.string.RePostion));
        }
    }

    public void setTimerReset(String key) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_left_green_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getString(R.string.text_timer_btn_continue));
            manager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_right_gray_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_content_black_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getString(R.string.cancel));
        }
    }

    public void setTimerPause(String key) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {
            manager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_left_green_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getString(R.string.text_timer_btn_continue));
            manager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_right_gray_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_content_black_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getString(R.string.cancel));
        }
    }

    public void setTimerPlay(String key) {
        FlashViewsManager manager = getCurrentManager(key);
        if ((manager != null) && manager.isBinding()) {

            manager.setButtonColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_left_red_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getColor(R.color.flash_back_bottom_content_white_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_LEFT, sContext.getResources().getString(R.string.text_timer_btn_pause));
            manager.setButtonColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_right_gray_color));
            manager.setButtonContentColor(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getColor(R.color.flash_back_bottom_content_black_color));
            manager.setButtonContent(FlashViews.VIEW_BUTTON_RIGHT, sContext.getResources().getString(R.string.cancel));
        }
    }

    private boolean isSupportFlashBack() {
        try {
            int supprot = Settings.Secure.getInt(sContext.getContentResolver(), FLASH_VIEWS_FOR_AIRVIEW_ENABLE, 0);
            return !Utils.isAboveOS14() && (supprot == 1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void releaseManager(String key) {
        if (!isSupportFlashBack()) {
            return;
        }
        FlashViewsManager manager = getCurrentManager(key);
        if (manager != null) {
            Log.v(TAG, "releaseManager key:" + key);
            sFlashRemove.put(key, 1);
            manager.destroy();
            sManagers.remove(key);
        }
        OnClickListener listener = sListeners.get(key);
        if (listener != null) {
            Log.v(TAG, "releaseListener key:" + key);
            sListeners.remove(key);
        }
        if (Constants.CLOCK_TIMER_FLASHBACK_KEY.equals(key)) {
            mTimerTag = 0;
        } else {
            mStopWatchTag = 0;
        }
    }


    public void releaseFlashViewsManager() {
        if (!isSupportFlashBack()) {
            return;
        }
        if (sManagers.size() > 0) {
            Log.v(TAG, "releaseFlashViewsManager");
            releaseManager(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY);
            releaseManager(Constants.CLOCK_TIMER_FLASHBACK_KEY);
            sManagers.clear();
        }
        if (sListeners.size() > 0) {
            sListeners.clear();
        }
    }

    public interface OnClickListener {
        void onLeftButtonClick();

        void onRightButtonClick();
    }

    public interface CreateListener {
        void onCreated();
    }
}