package com.oplus.alarmclock.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.clock.common.utils.Log;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class RingtoneUtil {
    private static final String TAG = "RingtoneUtil";
    private static final Map<String, String> mTitleMaps = new HashMap<>();

    public static String getInternalRingtoneTitle(Context context, Uri ringtoneUri) {
        Cursor cursor = null;
        String title = "";
        String path = "";
        try {
            cursor = context.getContentResolver().query(ringtoneUri,
                    new String[]{isInternalFileUri(ringtoneUri.toString()) ? MediaStore.Audio.Media.TITLE : MediaStore.Audio.Media.DISPLAY_NAME,
                            MediaStore.Audio.AudioColumns.DATA}, null, null, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    title = cursor.getString(0);
                    path = cursor.getString(1);
                } else {
                    Log.w(TAG, "getRingtoneTitle can not find " + ringtoneUri + " in media provider!");
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "getRingtoneTitle().query " + ringtoneUri + " error " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return title;
    }

    public static Uri getInternalRingtoneUri(Context context, String ringtoneTitle) {
        if (TextUtils.isEmpty(ringtoneTitle)) {
            return null;
        }
        final Uri baseUri = MediaStore.Audio.Media.INTERNAL_CONTENT_URI;
        ContentResolver contentResolver = context.getContentResolver();
        final String titleNoSuffix = removeExtension(ringtoneTitle);
        final String ringtoneNameWithSuffix = titleNoSuffix + ".ogg";
        try (Cursor cursor = contentResolver.query(baseUri,
                new String[]{MediaStore.MediaColumns._ID, MediaStore.MediaColumns.TITLE, MediaStore.MediaColumns.DATA},
                "(" + MediaStore.MediaColumns.TITLE + " =?) OR ("
                        + MediaStore.MediaColumns.DISPLAY_NAME + " like ?)",
                new String[]{titleNoSuffix, ringtoneNameWithSuffix}, null)) {
            if ((cursor != null) && cursor.moveToFirst()) {
                final long id = cursor.getLong(0);
                final String title = cursor.getString(1);
                final String path = cursor.getString(2);
                Log.i(TAG, "getInternalRingtoneUri: " + ringtoneTitle + ", id: " + id + ", title: " + title + ", path: " + path);
                return getInternalUri(baseUri, id, title);
            }
        }
        return null;
    }

    public static String getOldTitleFromUri(Uri oldUri) {
        if (oldUri == null) {
            return null;
        }
        String ringtoneTitle = null;
        try {
            Uri uri = Uri.parse(oldUri.toString());
            ringtoneTitle = uri.getQueryParameter("title");
        } catch (Exception e) {
            Log.e(TAG, "getOldTitleFromUri parse ringtoneTitle error:" + e.getMessage());
        }
        return ringtoneTitle;
    }

    public static String getNewTitleFromOldTitle(String title) {
        if (TextUtils.isEmpty(title)) {
            return null;
        }
        mTitleMaps.clear();
        Context context = AlarmClockApplication.getInstance();
        String label = null;
        try {
            label = context.getResources().getString(context.getResources().getIdentifier("oplus_brand_label", "string", "oplus"));
        } catch (Exception e) {
            Log.e(TAG, "getNewTitleFromOldTitle get oplus_brand_label error:" + e.getMessage());
        }
        String[] titleArray = new String[0];
        try {
            titleArray = context.getResources().getStringArray(context.getResources().getIdentifier("internal_ogg_info", "array", "oplus"));
        } catch (Exception e) {
            Log.e(TAG, "getNewTitleFromOldTitle get internal_ogg_info error:" + e.getMessage());
        }
        if (!TextUtils.isEmpty(title) && (titleArray.length > 0) && (!"P".equals(label)) && (mTitleMaps.isEmpty())) {
            for (String item : titleArray) {
                if (!TextUtils.isEmpty(item)) {
                    String[] child = TextUtils.split(item, "/");
                    if (child.length == 2) {
                        mTitleMaps.put(child[0], child[1]);
                    }
                }
            }
        }
        if (!mTitleMaps.isEmpty()) {
            return mTitleMaps.get(title);
        }
        return null;
    }

    private static boolean isExternalFileUri(String uri) {
        Pattern pattern = Pattern.compile("^content://\\S*media/external\\S*");
        return pattern.matcher(uri).matches();
    }

    public static boolean isInternalFileUri(String uri) {
        return (uri != null) && uri.startsWith("content://media/internal");
    }

    public static String removeExtension(String filename) {
        if (filename == null) {
            return null;
        }
        int index = filename.lastIndexOf(".");
        if (index == -1) {
            return filename;
        } else {
            return filename.substring(0, index);
        }
    }

    private static Uri getInternalUri(Uri baseUri, long id, String title) {
        Uri.Builder builder = baseUri.buildUpon();
        builder.appendEncodedPath(String.valueOf(id));
        if (!TextUtils.isEmpty(title)) {
            builder.appendQueryParameter("title", title)
                    .appendQueryParameter("canonical", "1");
        }
        return builder.build();
    }
}
