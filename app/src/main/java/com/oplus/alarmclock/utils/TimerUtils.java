/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: TimerUtils.java
 ** Description: Util class for OplusClock timer.
 ** Version: V 1.0
 ** Date : 2018-07-05
 ** Author: <PERSON>long
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.net.Uri;

import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;

public class TimerUtils {
    private static final String TAG = "TimerUtils";

    public static final int SECOND_IN_MINUTES = 60;
    public static final int DEFAULT_TIMER = 15;

    private static final int MIN_HOUR_MULTIPLE = 3600;
    private static final int MINS_IN_HOUR = 60;

    private static boolean sForegroundServiceHasStarted = false;

    public static void inflateTimer(OplusTimePickerCustomClock timerPicker, long duration) {
        if (timerPicker == null) {
            return;
        }
        timerPicker.setCurrentHour((int) (duration / (MIN_HOUR_MULTIPLE)));
        timerPicker.setCurrentMinute((int) ((duration / MINS_IN_HOUR) % MINS_IN_HOUR));
        timerPicker.setCurrentSecond((int) (duration % MINS_IN_HOUR));
    }

    public static Uri getDefaultTimerRingtoneUri(Context context) {
        return AlarmRingUtils.getDefaultRingtoneUri(context, true);
    }

    public static boolean isForegroundServiceStarted() {
        return sForegroundServiceHasStarted;
    }

    public static void setForegroundServiceStarted(boolean started) {
        sForegroundServiceHasStarted = started;
    }
}
