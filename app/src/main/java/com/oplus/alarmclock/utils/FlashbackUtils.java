/*
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: FlashbackUtils.java
 ** Version: V 1.0
 ** Date : 2020-01-16
 ** Author: hegai
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/

package com.oplus.alarmclock.utils;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.ServiceConnection;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.text.TextUtils;

import com.oplus.airview.IAirViewProxy;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.clock.common.utils.Log;

import org.json.JSONObject;

import java.util.List;

public class FlashbackUtils {
    public static final long VERSIONCODE = 7000001;
    public static final int STYLE_STOPWATCH = 1; //1 stopWatch
    public static final int STYLE_TIMER = 2; //2 timer
    public static final String FLASHBACK_STATE_STORE = "flashback_state_store";
    private static final String TAG = "FlashbackUtils";
    private static final String SERVICE_ACTION = "oplus.intent.action.AIR_VIEW_SERVICE";
    private static final String DISALLOW_USING_AIR_VIEW_STRING = "oplus.floatassistant.airview.disable";
    private static final String KEY_APP_TOGGLE = "airview_com.oplus.alarmclock";
    private static final String SERVICE_PKG = "com.coloros.floatassistant";
    private static final String KEY_TITLE = "title";
    private static final String KEY_AIR_VIEW_TOGGLE = "air_view_toggle";
    private static final String KEY_TRANSACTION_TYPE = "transactionType";
    private static final String KEY_CONTENT = "content";
    private static final String PERMISSION_PROCESS = "com.android.permissioncontroller";
    private static final String KEY_TOTAL_MILLIS_TIME = "totalMillisTime";
    private static final String KEY_IMAGE_TYPE = "imageType";
    private static final String KEY_TIMEOUT = "timeout";
    private static final int TRANSACTION_STOPWATCH = 3;
    private static final int TRANSACTION_TIMER = 2;
    private static final int TOGGLE_ON = 1;
    private static final int TOGGLE_APP_ON = 1;
    private static final int FLASBACKUTILS_MARK_0 = 0;
    private static final int FLASBACKUTILS_MARK_1 = 1;
    private static final boolean STATUS_TRUE = true;
    private static final boolean STATUS_FALSE = false;
    private IAirViewProxy mFlashbackProxy;
    private boolean mBindAirViewService = false;
    private FlashbackStatusListener mFlashbackStatusListener;

    public FlashbackUtils(FlashbackStatusListener flashbackStatusListener) {
        this.mFlashbackStatusListener = flashbackStatusListener;
    }

    public static boolean isSupport(Context context) {
        boolean mark = true;
        if (context != null) {
            mark = context.getPackageManager().hasSystemFeature(DISALLOW_USING_AIR_VIEW_STRING);
        }
        if (!mark) {
            Log.i(TAG, "isSupport true");
        } else {
            Log.e(TAG, "isSupport false");
        }
        return mark;
    }

    /**
     * The version number needs to be greater than 7000001 to support flashback
     * More than 7000001 yes
     * Less than or equal to 7000001  no
     */
    public static long getFlashbackSdk(Context context) {
        long versionCode = 0;
        if (context != null) {
            PackageManager manager = context.getPackageManager();
            if (manager != null) {
                try {
                    PackageInfo info = manager.getPackageInfo(SERVICE_PKG, 0);
                    if (info != null) {
                        versionCode = info.versionCode;
                    }
                } catch (PackageManager.NameNotFoundException e) {
                    Log.e(TAG, "getFlashbackSdk" + e.getMessage());
                }
            }
        }
        Log.i(TAG, "getFlashbackSdk" + versionCode);
        return versionCode;
    }


    public static boolean flashbackSwitchState(Context context) {
        boolean state = false;
//        Settings.Secure.getInt(context.getContentResolver(), KEY_AIR_VIEW_TOGGLE, TOGGLE_ON);
        if (TOGGLE_ON == 1) {
            Log.i(TAG, "flashbackSwitchState true");
            state = true;
        } else {
            state = false;
            Log.e(TAG, "flashbackSwitchState false");
        }
        return state;
    }

    public static boolean flashbackApplicationSwitchState(Context context) {
        boolean state = false;
//        Settings.Secure.getInt(context.getContentResolver(), KEY_APP_TOGGLE, TOGGLE_APP_ON);
        if (TOGGLE_APP_ON == 1) {
            Log.i(TAG, "flashbackApplicationSwitchState true");
            state = true;
        } else {
            state = false;
            Log.e(TAG, "flashbackApplicationSwitchState false");
        }
        return state;
    }


    public void display(Context context, String title, String content, int iconType, long timeout, long totalMillisTime, int style) {

        if ((context != null) && !DeviceUtils.isExpVersion(AlarmClockApplication.getInstance()) && !isForeground(context, context.getPackageName())) {
            if ((mFlashbackProxy == null) || (context == null)) {
                Log.e(TAG, "display mIAirViewProxy || context is null ");
                return;
            }
            Log.d(TAG, "display  title  iconType timeout : " + "title " + title + " content" + "iconType " + iconType + "timeout " + timeout);
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put(KEY_TITLE, title);
                jsonObject.put(KEY_CONTENT, content);
                jsonObject.put(KEY_IMAGE_TYPE, iconType);
                jsonObject.put(KEY_TIMEOUT, timeout);
                if (style == STYLE_TIMER) {
                    jsonObject.put(KEY_TOTAL_MILLIS_TIME, totalMillisTime);
                    jsonObject.put(KEY_TRANSACTION_TYPE, TRANSACTION_TIMER);
                } else if (style == STYLE_STOPWATCH) {
                    jsonObject.put(KEY_TRANSACTION_TYPE, TRANSACTION_STOPWATCH);
                }
                if (mFlashbackProxy != null) {
                    Log.d(TAG, "display jsonObject" + jsonObject.toString());
                    mFlashbackProxy.display(context.getPackageName(), jsonObject.toString());
                }
            } catch (Exception e) {
                Log.e(TAG, "display JSONException: " + e.getMessage());
            }
        }
    }


    private static boolean isForeground(Context context, String applicationPackageName) {
        if ((context == null) || TextUtils.isEmpty(applicationPackageName)) {
            return false;
        }
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> list = activityManager.getRunningTasks(FLASBACKUTILS_MARK_1);
        if ((list != null) && (list.size() > FLASBACKUTILS_MARK_0)) {
            Log.d(TAG, "list.size " + list.size());
            ComponentName name = list.get(FLASBACKUTILS_MARK_0).topActivity;
            if (name != null) {
                Log.d(TAG, "topActivity : " + name.getPackageName());
            }
            if ((name != null) && applicationPackageName.equals(name.getPackageName()) || (name != null) && name.getPackageName().equals(PERMISSION_PROCESS)) {
                return true;
            }
        } else {
            Log.d(TAG, "list is null or list size 0");
        }
        return false;
    }


    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "mIAirViewProxy onServiceConnected");
            mFlashbackProxy = IAirViewProxy.Stub.asInterface(service);
            mBindAirViewService = true;
            if ((mFlashbackStatusListener != null) && (mFlashbackProxy != null)) {
                mFlashbackStatusListener.success(mFlashbackProxy, STATUS_TRUE);
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "mIAirViewProxy onServiceDisconnected");
            mBindAirViewService = false;
            mFlashbackProxy = null;
            if ((mFlashbackStatusListener != null) && (mFlashbackProxy != null)) {
                mFlashbackStatusListener.failure(mFlashbackProxy, STATUS_TRUE);
            }
        }
    };


    public void bindAirViewService(Context context) {
        try {
            if ((context != null) && (!isSupport(context))) {
//                if (getFlashbackSdk(context) > VERSIONCODE) {
//                    Log.i(TAG, "bindService");
//                    Intent mIntent = new Intent(SERVICE_ACTION);
//                    mIntent.setPackage(SERVICE_PKG);
//                    mIntent.setIdentifier(context.getPackageName());
//                    context.bindService(mIntent, mConnection, Service.BIND_AUTO_CREATE);
//                } else {
//                    Log.e(TAG, "flashback version number is less than  VERSIONCODE");
//                }
            } else {
                Log.e(TAG, "the device does not support flashback ");
            }
        } catch (Exception e) {
            Log.e(TAG, "bindAirViewService error" + e.getMessage());
        }
    }

    public void unBindAirViewService(Context context, IAirViewProxy iAirViewProxy) {
        try {
            if ((context == null) || (iAirViewProxy == null) || (mConnection == null)) {
                Log.d(TAG, "unBindAirViewService error null");
                return;
            }
            Log.d(TAG, "unBindAirViewService");
            if ((iAirViewProxy != null) && mBindAirViewService) {
                context.unbindService(mConnection);
            }
        } catch (Exception e) {
            Log.e(TAG, "unBindAirViewService" + e.getMessage());
        }
    }


    public void cancel(Context context, IAirViewProxy iAirViewProxy) {
        try {
            if ((context == null) || (iAirViewProxy == null) || (mConnection == null)) {
                Log.d(TAG, "unBindAirViewService error null");
                return;
            }
            Log.d(TAG, "unBindAirViewService");
            if ((iAirViewProxy != null) && mBindAirViewService) {
                iAirViewProxy.cancel(context.getPackageName());
            }
        } catch (Exception e) {
            Log.e(TAG, "cancel" + e.getMessage());
        }
    }


    public interface FlashbackStatusListener {
        void success(IAirViewProxy iAirViewProxy, boolean status);

        void failure(IAirViewProxy iAirViewProxy, boolean status);
    }
}