package com.oplus.alarmclock.utils;

import android.graphics.Color;

/*********************************************************************************
 ** Copyright (C), 2008-2015, OPLUS Mobile Comm Corp., Ltd
 ** OPLUSOS_EDIT, All rights reserved.
 **
 ** File: - ColorLinearGradientUtil.java
 ** Description:
 **     get gradient color between start color and end start
 **
 ** Version: 1.0
 ** Date: 2019-09-04
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>   <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-09-04   1.0         Create this moudle
 ********************************************************************************/
public class ColorLinearGradientUtil {

    private int mRedStart;
    private int mBlueStart;
    private int mGreenStart;
    private int mRedEnd;
    private int mBlueEnd;
    private int mGreenEnd;

    public ColorLinearGradientUtil(int startColor, int endColor) {
        setStartColor(startColor);
        setEndColor(endColor);
    }

    public void setStartColor(int startColor) {
        mRedStart = Color.red(startColor);
        mBlueStart = Color.blue(startColor);
        mGreenStart = Color.green(startColor);
    }

    public void setEndColor(int endColor) {
        mRedEnd = Color.red(endColor);
        mBlueEnd = Color.blue(endColor);
        mGreenEnd = Color.green(endColor);
    }

    public int getColor(float radio) {
        int red = (int) (mRedStart + ((mRedEnd - mRedStart) * radio + 0.5));
        int green = (int) (mGreenStart + ((mGreenEnd - mGreenStart) * radio + 0.5));
        int blue = (int) (mBlueStart + ((mBlueEnd - mBlueStart) * radio + 0.5));
        return Color.argb(255, red, green, blue);
    }

}
