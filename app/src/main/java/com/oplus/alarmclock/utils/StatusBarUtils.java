/*******************************************************
 * Copyright 2007 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 *
 * 2018-07-07   Liukun  Build.
 *******************************************************/
package com.oplus.alarmclock.utils;

import static com.oplus.alarmclock.utils.Utils.isAboveOS121;
import static com.oplus.alarmclock.utils.Utils.isAboveS;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.clock.common.utils.Log;
import java.lang.reflect.Method;

public class StatusBarUtils {

    private static final String TAG = "StatusBarUtils";

    @SuppressLint("WrongConstant")
    public static void collapseStatusBar(Context context) {
        try {
            final Object statusBarManager = context.getSystemService("statusbar");
            if (statusBarManager != null) {
                final Method collapse = statusBarManager.getClass().getMethod("collapsePanels");
                collapse.invoke(statusBarManager);
            }
        } catch (final Exception e) {
            Log.e(TAG, "collapseStatusBar Exception: " + e.getMessage());
        }
    }

    public static void setStatusBarAlarmIconVisible(Context context, boolean visible) {
        //只处理OS12.1以上、S版本以上的内销版本
        if (isAboveOS121() && isAboveS() && !DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            try {
                Settings.System.putInt(context.getContentResolver(), "clock_icon_enabled", visible ? 1 : 0);
            } catch (Exception e) {
                Log.e(TAG, "Settings.System error" + e.getMessage());
            }
        }
        setStatusBarIcon(context, visible);
    }

    private static void setStatusBarIcon(Context context, boolean enabled) {
        if (context == null) {
            Log.d(TAG, "setStatusBarIcon context is null");
            return;
        }
        /*To fix this bug2867123*/
        if (Utils.isAboveQ()) {
            int currentId = AppPlatformUtils.getCurrentUser();
            int myUserId = AppPlatformUtils.myUserId();

            if (currentId != myUserId) {
                Log.i(TAG, "The running user is not the current user, so return directly！");
                return;
            }
        }

        Log.i(TAG, "checkIfShowStatusIcon alarmSet:" + enabled);
        Intent alarmChanged = new Intent("android.intent.action.ALARM_CHANGED");// Intent.ACTION_ALARM_CHANGED
        alarmChanged.putExtra("alarmSet", enabled);
        context.sendBroadcast(alarmChanged);
    }
}
