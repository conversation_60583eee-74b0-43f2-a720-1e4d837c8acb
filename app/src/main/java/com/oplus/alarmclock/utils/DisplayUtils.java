/***********************************************************
 * * Copyright (C), 2008-2021, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: DisplayUtils
 * * Description: 屏幕尺寸相关的工具类
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.graphics.Insets;
import android.graphics.Rect;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Size;
import android.view.Display;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.view.WindowMetrics;

import androidx.annotation.RequiresApi;

import com.oplus.clock.common.utils.Log;

public class DisplayUtils {

    private static final String TAG = "DisplayUtils";

    public static int getWindowHeight(Context context) {
        Size size = getWindowSize(context, false);
        if (FoldScreenUtils.isRealOslo()) {
            return size.getHeight();
        }
        return Math.max(size.getHeight(), size.getWidth());
    }

    /**
     * 获取整个屏幕的高度，包括状态栏和底部导航栏
     *
     * @param context
     * @return
     */
    public static Size getRealWindowSize(Context context) {
        return getWindowSize(context, true);
    }

    /**
     * 获取屏幕的高度，不包括状态和底部导航栏的高度
     *
     * @param context
     * @return
     */
    public static Size getWindowSize(Context context) {
        return getWindowSize(context, false);
    }

    /**
     * 获取屏幕的最大高度
     *
     * @param context
     * @return
     */
    public static Size getMaxWindowSize(Context context) {
        if (isAboveR()) {
            return getMaxWindowSizeAboveR(context);
        }
        return getWindowSizeBelowQ(context, true);
    }

    /**
     * 获取屏幕的高度
     *
     * @param context
     * @param isReal  是否包括状态和底部导航栏的高度
     * @return
     */
    public static Size getWindowSize(Context context, boolean isReal) {
        if (isAboveR()) {
            return getWindowSizeAboveR(context, isReal);
        }
        return getWindowSizeBelowQ(context, isReal);
    }

    /**
     * 版本是R及以上
     *
     * @return
     */
    public static boolean isAboveR() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R;
    }

    /**
     * 获取屏幕的高度，适用于Q及之前
     *
     * @param context
     * @param isReal  是否包括状态和底部导航栏的高度
     * @return
     */
    public static Size getWindowSizeBelowQ(Context context, boolean isReal) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        Display display = windowManager.getDefaultDisplay();
        if (isReal) {
            display.getRealMetrics(displayMetrics);
        } else {
            display.getMetrics(displayMetrics);
        }
        Size size = new Size(displayMetrics.widthPixels, displayMetrics.heightPixels);
        Log.d(TAG, "getWindowSize：Q size:" + size + ",real:" + isReal);
        return size;
    }

    /**
     * 获取整个屏幕的高度,适用于R及之后
     *
     * @param context
     * @param isReal  是否包括状态和底部导航栏的高度
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.R)
    public static Size getWindowSizeAboveR(Context context, boolean isReal) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        WindowMetrics metrics = windowManager.getCurrentWindowMetrics();
        Rect bounds = metrics.getBounds();
        Log.d(TAG, "getWindowSize：R bounds:" + bounds);
        if (isReal) {
            Size size = new Size(bounds.width(), bounds.height());
            Log.d(TAG, "getWindowRealSize：R size:" + size);
            return size;
        } else {
            final WindowInsets windowInsets = metrics.getWindowInsets();
            Insets insets = windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.navigationBars() | WindowInsets.Type.displayCutout());
            Log.d(TAG, "getWindowSize：R insets:" + insets);
            int insetsWidth = insets.right + insets.left;
            // top:状态栏 bottom：导航栏
            int insetsHeight = insets.top + insets.bottom;

            Size size = new Size(bounds.width() - insetsWidth, bounds.height() - insetsHeight);
            Log.d(TAG, "getWindowSize：R size:" + size);
            return size;
        }
    }

    /**
     * 获取整个屏幕的最大高度,适用于R及之后
     *
     * @param context
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.R)
    public static Size getMaxWindowSizeAboveR(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        WindowMetrics metrics = windowManager.getMaximumWindowMetrics();

        Rect bounds = metrics.getBounds();
        Log.d(TAG, "getMaxWindowSize：R bounds:" + bounds);
        return new Size(bounds.width(), bounds.height());
    }
}
