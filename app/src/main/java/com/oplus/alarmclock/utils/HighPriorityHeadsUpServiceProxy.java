/************************************************************
 * Copyright 2017 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : when alarm alert is on, dismiss system notification. when alarm alert is off, reset
 * system notification.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2017-7-14, <PERSON> create
 ************************************************************/
package com.oplus.alarmclock.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Binder;
import android.os.IBinder;
import android.os.RemoteException;

import com.android.systemui.statusbar.IHighPriorityHeadsUpService;
import com.oplus.clock.common.utils.Log;

public class HighPriorityHeadsUpServiceProxy {
    private static final String TAG = "HeadsUpProxy";

    private Binder mBinder = new Binder();
    private Context mContext;
    private OnBindSystemUISuccessListener mOnBindSuccessListener;
    private boolean mIsBind = false;
    private IHighPriorityHeadsUpService mHeadsUpService;
    private ComponentName mServiceComponentName = new ComponentName("com.android.systemui",
            "com.android.systemui.statusbar.HighPriorityHeadsUpService");

    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            Log.i(TAG, "onServiceConnected");
            mHeadsUpService = IHighPriorityHeadsUpService.Stub.asInterface(service);
            notifyShowHeadsUp();
            if (mOnBindSuccessListener != null) {
                mOnBindSuccessListener.onBindSystemUISuccess();
            }
        }

        public void onServiceDisconnected(ComponentName className) {
            Log.w(TAG, "onServiceDisconnected");
        }
    };

    public HighPriorityHeadsUpServiceProxy(Context context) {
        mContext = context;
    }

    public void show(OnBindSystemUISuccessListener listener) {
        mOnBindSuccessListener = listener;
        Intent intent = new Intent();
        intent.setComponent(mServiceComponentName);
        Log.i(TAG, "bindService");
        mContext.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
        mIsBind = true;
    }

    public void dismiss() {
        notifyDismissHeadsUp();
        mHeadsUpService = null;
    }

    public void unbindService() {
        if (mConnection != null && (mContext != null) && mIsBind) {
            mContext.unbindService(mConnection);
            mIsBind = false;
        }
    }

    private void notifyShowHeadsUp() {
        Log.i(TAG, "notifyShowHeadsUp");
        if (mHeadsUpService != null) {
            try {
                mHeadsUpService.showHeadsUp(mBinder);
            } catch (RemoteException e) {
                Log.w(TAG, "showHeadsUp error! " + e);
            }
        } else {
            Log.w(TAG, "service is null while show!");
        }
    }

    private void notifyDismissHeadsUp() {
        Log.i(TAG, "notifyDismissHeadsUp");
        if (mHeadsUpService != null) {
            try {
                mHeadsUpService.dismissHeadsUp();
            } catch (RemoteException e) {
                Log.w(TAG, "dismissHeadsUp error!" + e);
            }
        } else {
            Log.w(TAG, "service is null while dismissHeadsUp!");
        }
    }

    public interface OnBindSystemUISuccessListener {
        void onBindSystemUISuccess();
    }
}