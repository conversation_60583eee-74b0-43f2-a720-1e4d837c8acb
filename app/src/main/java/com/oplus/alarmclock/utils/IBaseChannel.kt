/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IBaseChannel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/1/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/1/5     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.content.Context
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R

interface IBaseChannel {

    companion object {
        const val CHANNEL_OPLUS = 1
        const val CHANNEL_RPLUS = 3
        const val CHANNEL_WPLUS = 5
    }

    fun getChannel() : Int


    fun setWPlusMoreIcon(context : Context, toolbar : COUIToolbar?) {
        //do nothing
    }

    fun getWPlusEmptyIcon(context: Context, iconId: Int): Int {
        //do nothing
        return 0
    }

}