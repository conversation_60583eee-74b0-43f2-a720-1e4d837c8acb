/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ColorStatusBarResponseUtil.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/2/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/2/19     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.VersionUtils

class ColorStatusBarResponseUtil(val mActivity: Activity) {

    interface StatusBarClickListener {
        fun onStatusBarClicked()
    }

    companion object {
        const val TAG = "ColorStatusBarResponseUtil"
    }

    private var mReceiver: BroadcastReceiver? = null
    private var mIsRegistered = false
    private var mStatusBarClickListener: StatusBarClickListener? = null


    fun register() {
        initReceiver()
    }

    private fun initReceiver() {
        if (!mIsRegistered) {
            mReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    Log.i(
                        TAG,
                        "The broadcast receiever was registered successfully and receives the broadcast"
                    )
                    if (mStatusBarClickListener != null) {
                        mStatusBarClickListener!!.onStatusBarClicked()
                        Log.i(
                            TAG,
                            "onStatusBarClicked is called at time :" + System.currentTimeMillis()
                        )
                    }
                }
            }
            val intentFilter = IntentFilter()
            if (VersionUtils.isOsVersion11_3()) {
                intentFilter.addAction("com.oplus.clicktop")
            } else {
                intentFilter.addAction("com.color.clicktop")
            }
            try {
                mActivity.registerReceiver(this.mReceiver, intentFilter, Context.RECEIVER_EXPORTED)
                mIsRegistered = true
            } catch (e: IllegalStateException) {
                Log.d(TAG, "register e:$e")
            }
        }
    }


    fun unRegister() {
        if (mIsRegistered) {
            mIsRegistered = false
            if (mReceiver != null) {
                mActivity.unregisterReceiver(mReceiver)
            }
        }
    }

    fun setStatusBarClickListener(listener: StatusBarClickListener?) {
        mStatusBarClickListener = listener
    }
}