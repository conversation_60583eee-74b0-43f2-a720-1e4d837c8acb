/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DoubleClickHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/5/9
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/5/9     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.os.SystemClock

class DoubleClickHelper {

    companion object{
        const val MENU_CLICK_DURATION = 500L
    }

    private var mLastClickTime = 0L

    fun canClick(): Bo<PERSON>an {
        val elapsed = SystemClock.elapsedRealtime()
        if (elapsed - mLastClickTime > MENU_CLICK_DURATION) {
            mLastClickTime = elapsed
            return true
        }
        return false
    }
}