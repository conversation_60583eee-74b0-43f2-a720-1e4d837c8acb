package com.oplus.alarmclock.utils

import com.oplus.alarmclock.AlarmClockApplication
import com.heytap.addon.os.LinearmotorVibrator

object LinearMotorVibratorManager {

    private val sLinearMotorVibrator: LinearmotorVibrator = LinearmotorVibrator(com.oplus.alarmclock.AlarmClockApplication.getInstance())
    fun getLinearMotorVibrator() : LinearmotorVibrator {
        return sLinearMotorVibrator
    }
}