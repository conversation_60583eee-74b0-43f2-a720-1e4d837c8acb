/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - PermisssionUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/8/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/8/7     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.Manifest
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.clock.common.utils.Log

object PermissionUtils {

    const val TAG = "PermissionUtils"

    fun hasStoragePermission(): Boolean {

        val context = AlarmClockApplication.getInstance();
        if (Utils.isAboveT()) {
            val hasMediaPermission = PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(context,
                Manifest.permission.READ_MEDIA_AUDIO)
            Log.i(TAG, "hasStoragePermission request access to READ_MEDIA_AUDIO status:$hasMediaPermission")
            return hasMediaPermission
        }
        val hasPermission = PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(context,
            Manifest.permission.READ_EXTERNAL_STORAGE)
        Log.i(TAG, "hasStoragePermission request access to READ_EXTERNAL_STORAGE status:$hasPermission")
        return hasPermission
    }

}