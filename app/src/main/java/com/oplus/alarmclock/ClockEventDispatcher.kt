/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2021, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - ClockEventDispatcher.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 22021/01/07
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2021/01/07     1.0            ClockEventDispatcher.kt
 *  ***************************************************************
 */

package com.oplus.alarmclock

import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import com.oplus.alarmclock.AlarmClock.ACTION_AI_SET_TIMER
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_CARD_SET_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_CARD_SET_ALARM_OLD
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_IOT_SET_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_IOT_SET_ALARM_OLD
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_MINI_APP_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_MINI_APP_ALARM_EXP
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_MINI_APP_ALARM_MEDIA_DIALOG
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_MINI_APP_SET_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_SET_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_TIMER_MINI_SET_TIMER
import com.oplus.alarmclock.AlarmClock.ACTION_ALARM_CLOCK_VOICE_SET_ALARM
import com.oplus.alarmclock.AlarmClock.ACTION_OPEN_CLOCK
import com.oplus.alarmclock.AlarmClock.ACTION_OPEN_CLOCK_OLD
import com.oplus.alarmclock.AlarmClock.ACTION_START_BY_NOTIFICATION
import com.oplus.alarmclock.AlarmClock.ACTION_VIEW_WORLD_CLOCKS
import com.oplus.alarmclock.AlarmClock.HANDLE_API__SET_TIMER
import com.oplus.alarmclock.AlarmClock.TAB_INDEX_ALARMCLOCK
import com.oplus.alarmclock.AlarmClock.TAB_INDEX_GLOBALCITY
import com.oplus.alarmclock.AlarmClock.TAB_INDEX_OPLUSTIME
import com.oplus.alarmclock.alarmclock.AlarmReceiver
import com.oplus.alarmclock.timer.TimerAlertReceiver
import com.oplus.alarmclock.timer.TimerNotificationManager
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.alarmclock.view.PrivacyPolicyAlert
import com.oplus.clock.common.utils.Log


class ClockEventDispatcher {

    private var dispatcherAction: DispatcherAction? = null

    fun unRegisterDispatcherAction() {
        dispatcherAction = null
    }

    fun registerDispatcherAction(context: AlarmClock) {
        dispatcherAction = context
    }

    fun dispatch(intent: Intent?) {
        if ((dispatcherAction == null) || (intent == null) || TextUtils.isEmpty(intent.action)) {
            return
        }

        if (dispatcherAction!!.onIntercept()) {
            Log.i(TAG, "dispatch intercept")
            return
        }

        val action = intent.action
        Log.i(TAG, "dispatch action = $action")

        when (action) {
            ACTION_ALARM_CLOCK_VOICE_SET_ALARM -> dispatcherAction!!.setAlarmByVoice()
            ACTION_ALARM_CLOCK_SET_ALARM -> {
                dispatcherAction!!.setAlarmByShortcut()
                intent.action = ""
            }
            ACTION_ALARM_CLOCK_MINI_APP_SET_ALARM -> {
                //miniapp启动添加闹钟页面
                dispatcherAction!!.setAlarmByMiniApp()
                intent.action = ""
            }
            ACTION_ALARM_CLOCK_MINI_APP_ALARM -> {
                //miniapp启动闹钟首页
                dispatcherAction!!.startAlarmByMiniApp()
                intent.action = ""
            }
            ACTION_ALARM_CLOCK_MINI_APP_ALARM_MEDIA_DIALOG -> {
                //音频须知接续
                dispatcherAction!!.startMediaDialogAlarmByMiniApp()
                intent.action = ""
            }
            ACTION_ALARM_CLOCK_MINI_APP_ALARM_EXP -> {
                //外销启动通知授权
                dispatcherAction!!.startAlarmByMiniAppExp()
                intent.action = ""
            }
            ACTION_AI_SET_TIMER -> {
                dispatcherAction!!.setTimerByAi()
                intent.action = ""
            }
            ACTION_VIEW_WORLD_CLOCKS -> dispatcherAction!!.openAppByWorldWidget()
            TimerAlertReceiver.ACTION_ENTER_AND_OPEN_TIMER -> {
                dispatcherAction!!.openApp()
                TimerNotificationManager.statisticsTimerNotificationOperation(
                        com.oplus.alarmclock.AlarmClockApplication.getInstance(), ClockOplusCSUtils.TIMER_NOTIFICATION_TYPE_CLICK)
            }
            ClockConstant.ENTER_APK -> {
                AlarmReceiver.dealEnterApk()
                dispatcherAction!!.openApp()
            }
            ClockConstant.ENTER_APK_FROM_SCREEN -> {
                AlarmReceiver.dealEnterApkFromScreen(intent)
                AlarmReceiver.dealEnterApkFromNotify(intent)
                dispatcherAction!!.openApp()
            }
            ACTION_START_BY_NOTIFICATION,
            ACTION_OPEN_CLOCK_OLD,
            ACTION_OPEN_CLOCK -> dispatcherAction!!.openApp()

            ACTION_ALARM_CLOCK_IOT_SET_ALARM_OLD,
            ACTION_ALARM_CLOCK_IOT_SET_ALARM ->
                if (!PrivacyPolicyAlert.isFirstEntry(AlarmClockApplication.getInstance())) {
                    dispatcherAction!!.setAlarmByIOT()
                }
            ACTION_ALARM_CLOCK_CARD_SET_ALARM_OLD,
            ACTION_ALARM_CLOCK_CARD_SET_ALARM ->
                if (!PrivacyPolicyAlert.isFirstEntry(AlarmClockApplication.getInstance())) {
                    dispatcherAction!!.setAlarmByAi()
                }
            ACTION_ALARM_CLOCK_TIMER_MINI_SET_TIMER -> dispatcherAction!!.openAppByTimerMini()
            HANDLE_API__SET_TIMER -> dispatcherAction!!.handleApiSetTimer()

        }
        dispatcherAction!!.onDispatchEnd()
    }

    fun getTabIndex(intent: Intent?): Int {
        return intent?.let {
            it.action?.let { action ->
                when (action) {
                    ACTION_ALARM_CLOCK_VOICE_SET_ALARM,
                    ACTION_ALARM_CLOCK_SET_ALARM,
                    ACTION_ALARM_CLOCK_IOT_SET_ALARM_OLD,
                    ACTION_ALARM_CLOCK_IOT_SET_ALARM,
                    ACTION_ALARM_CLOCK_CARD_SET_ALARM_OLD,
                    ACTION_ALARM_CLOCK_CARD_SET_ALARM,
                    ACTION_ALARM_CLOCK_MINI_APP_SET_ALARM,
                    ACTION_ALARM_CLOCK_MINI_APP_ALARM,
                    ACTION_ALARM_CLOCK_MINI_APP_ALARM_MEDIA_DIALOG,
                    ACTION_ALARM_CLOCK_MINI_APP_ALARM_EXP -> TAB_INDEX_ALARMCLOCK

                    ACTION_AI_SET_TIMER,
                    ACTION_ALARM_CLOCK_TIMER_MINI_SET_TIMER,
                    HANDLE_API__SET_TIMER -> TAB_INDEX_OPLUSTIME

                    ACTION_VIEW_WORLD_CLOCKS -> TAB_INDEX_GLOBALCITY
                    TimerAlertReceiver.ACTION_ENTER_AND_OPEN_TIMER,
                    ClockConstant.ENTER_APK,
                    ClockConstant.ENTER_APK_FROM_SCREEN,
                    ACTION_START_BY_NOTIFICATION,
                    ACTION_OPEN_CLOCK_OLD,
                    ACTION_OPEN_CLOCK -> {
                        runCatching {
                            var index = it.getIntExtra(
                                AlarmClock.ACTION_PART_TAB_INDEX,
                                TAB_INDEX_ALARMCLOCK
                            )
                            if (index == TAB_INDEX_ALARMCLOCK) {
                                it.getStringExtra(AlarmClock.ACTION_PART_TAB_INDEX)?.apply {
                                    if (isNotEmpty()) {
                                        index = this.toInt()
                                    }
                                }
                            }
                            if (index < TAB_INDEX_ALARMCLOCK || index > TAB_INDEX_OPLUSTIME) {
                                index = TAB_INDEX_OPLUSTIME
                            }
                            index
                        }.getOrDefault(TAB_INDEX_ALARMCLOCK)
                    }

                    else -> TAB_INDEX_ALARMCLOCK
                }
            } ?: TAB_INDEX_ALARMCLOCK
        } ?: TAB_INDEX_ALARMCLOCK
    }

    internal interface DispatcherAction {
        fun onIntercept(): Boolean
        fun onDispatchEnd()
        fun setAlarmByVoice()
        fun setAlarmByShortcut()
        fun setAlarmByMiniApp()
        fun startAlarmByMiniApp()
        fun startMediaDialogAlarmByMiniApp()
        fun startAlarmByMiniAppExp()
        fun setTimerByAi()
        fun openAppByWorldWidget()
        fun openApp()
        fun setAlarmByIOT()
        fun setAlarmByAi()
        fun openAppByTimerMini()
        fun handleApiSetTimer()
    }

    companion object {
        private const val TAG = "ClockEventDispatcher"

        fun getDispatchAction(activity: Activity?): String? {
            return activity?.intent?.action
        }


        fun shouldFinishMain(intent: Intent?): Boolean {

            val action = intent?.action
            Log.i(TAG, "shouldFinishMain action:$action")
            return TextUtils.equals(action, ACTION_ALARM_CLOCK_IOT_SET_ALARM) || TextUtils.equals(action, ACTION_ALARM_CLOCK_IOT_SET_ALARM_OLD)

        }
    }
}