/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WeatherDisableController.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/8/10     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.app.Activity
import com.oplus.alarmclock.R
import com.coloros.alarmclock.widget.DigitalAppWidgetProvider

class OPlusWeatherDisableInfo : IDisabledPkgInfo {

    override fun getTypeCode(): Int {
        return IDisabledPkgInfo.TYPE_WEATHER_OPLUS
    }

    override fun getPkgName(): String {
        return DigitalAppWidgetProvider.OPLUS_WEATHER_PKG_NAME2
    }

    override fun getDialogTitleResId(): Int {
        return R.string.enable_weather
    }

    override fun getDialogMessageText(activity: Activity): CharSequence {
        return activity.getString(R.string.enable_weather_desc)
    }
}