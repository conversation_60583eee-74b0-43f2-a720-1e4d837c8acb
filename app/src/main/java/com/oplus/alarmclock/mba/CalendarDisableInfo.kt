/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CaleandarDisableController.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/8/10     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.app.Activity
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DeviceUtils
import com.coloros.alarmclock.widget.DigitalAppWidgetProvider

class CalendarDisableInfo : IDisabledPkgInfo {

    override fun getTypeCode(): Int {
        return IDisabledPkgInfo.TYPE_CALENDAR
    }

    override fun getPkgName(): String {
        return if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance()))
            DigitalAppWidgetProvider.GOOGLE_CALENDAR_PKG_NAME
        else
            DigitalAppWidgetProvider.OPLUS_CALENDAR_PKG_NAME
    }

    override fun getDialogTitleResId(): Int {
        return R.string.enable_calendar
    }

    override fun getDialogMessageText(activity: Activity): CharSequence {
        return activity.getString(R.string.enable_calendar_desc)
    }
}