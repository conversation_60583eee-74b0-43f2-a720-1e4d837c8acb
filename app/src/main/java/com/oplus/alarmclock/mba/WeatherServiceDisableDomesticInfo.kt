/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WeatherServiceDisableController.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  Hewei     2021/8/31     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ClickUtils
import com.oplus.clock.common.utils.Log

class WeatherServiceDisableDomesticInfo : IDisabledPkgInfo {

    companion object {
        const val PKG_NAME_WEATHER_SERVICE = "com.coloros.weather.service"
        private const val TAG = "WeatherServiceDisableDomesticInfo"
    }

    override fun getTypeCode(): Int {
        return IDisabledPkgInfo.TYPE_WEATHER_SERVICE_DOMESTIC
    }

    override fun getPkgName(): String {
        return PKG_NAME_WEATHER_SERVICE
    }

    override fun getDialogTitleResId(): Int {
        return R.string.enable_weather_service
    }

    override fun getDialogMessageText(activity: Activity): CharSequence {
        return getStatementSequence(activity)
    }

    override fun getPositiveButtonResId(): Int {
        return R.string.agree_and_enable
    }

    private fun getStatementSequence(activity: Activity): CharSequence {
        // Hyper Link
        val context = AlarmClockApplication.getInstance()
        //高亮文本
        val linkString = context.resources.getString(R.string.clock_privacy_click_text)
        //原始文本
        val resId = R.string.enable_weather_service_desc_new
        val appStatement = context.resources.getString(resId, linkString)
        val spannableString = SpannableString(appStatement)
        //高亮文本的开始下标
        val start = appStatement.indexOf(linkString)
        //高亮文本结束下标
        val end = start + linkString.length

        spannableString.setSpan(object : ClickableSpan() {
            override fun onClick(widget: View) {
                Log.d(TAG, "click span")
                if (ClickUtils.clickable()) {
                    openWeatherPrivacyPage(activity)
                }
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.color = context.getColor(R.color.couiBlueTintControlNormal)
            }
        }, start, end, Spanned.SPAN_MARK_MARK)

        return spannableString
    }

    private fun openWeatherPrivacyPage(activity: Activity) {
        try {
            val intent = Intent("com.oplus.weather.serivce.PRIVACY")
            intent.setPackage(getPkgName())
            activity.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "openWeatherPrivacyPage error: " + e.message)
        }
    }

    /**调用此方法才能让点击 ClickableSpan 生效*/
    fun setMessageMovementMethod(dialog: AlertDialog) {
        val message = dialog.findViewById<TextView>(android.R.id.message)
        message?.movementMethod = LinkMovementMethod.getInstance()
    }
}