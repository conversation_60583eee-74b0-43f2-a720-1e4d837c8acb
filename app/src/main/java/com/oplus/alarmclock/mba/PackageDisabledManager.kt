/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BasePkgDisabledHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/8/10     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.alarmclock.R
import com.coloros.widget.smallweather.BaseWidgetViewHelper
import com.coloros.widget.smallweather.ClockWidgetManager

object PackageDisabledManager {

    private const val TAG = "PackageDisabledManager"
    //组件未被禁用
    private const val SUCCESS = 0
    //组件被禁用
    private const val PACKAGE_DISABLED = 1
    //组件不存在，被卸载
    private const val PACKAGE_MISSED = 2

    fun checkDisabledAndJump(context: Context, pkgInfo: IDisabledPkgInfo) : Boolean {
        if (isPkgDisabled(context, pkgInfo)) {
            openEnableActivity(context, pkgInfo)
            return true
        }
        return false
    }

    @JvmStatic
    fun isPkgDisabled(context: Context, pkgInfo: IDisabledPkgInfo) : Boolean {
        val pkgStatus = getPkgStatusCode(context, pkgInfo)
        Log.d(TAG, "pkg ${pkgInfo.getPkgName()}  status  is $pkgStatus")
        return pkgStatus == PACKAGE_DISABLED
    }

    @JvmStatic
    fun openEnableActivity(context: Context, pkgInfo: IDisabledPkgInfo) {
        val isActivity = context is Activity
        val intent = Intent(context, TransparentDialogActivity::class.java)
        intent.putExtra(TransparentDialogActivity.KEY_DIALOG_TYPE, pkgInfo.getTypeCode())
        intent.putExtra(TransparentDialogActivity.KEY_SHOW_IN_LAUNCHER, !isActivity)
        if (!isActivity) {
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
        }
        context.startActivity(intent)
    }

    @JvmStatic
    fun getEnableIntent(context: Context, pkgInfo: IDisabledPkgInfo): Intent {
        val isActivity = context is Activity
        val intent = Intent(context, TransparentDialogActivity::class.java)
        intent.putExtra(TransparentDialogActivity.KEY_DIALOG_TYPE, pkgInfo.getTypeCode())
        intent.putExtra(TransparentDialogActivity.KEY_SHOW_IN_LAUNCHER, !isActivity)
        if (!isActivity) {
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
        }
        return intent
    }

    fun showEnableDialog(activity: Activity, pkgInfo: IDisabledPkgInfo): AlertDialog {
        val dialog = COUIAlertDialogBuilder(activity)
            .setTitle(pkgInfo.getDialogTitleResId())
            .setMessage(pkgInfo.getDialogMessageText(activity))
            .setPositiveButton(pkgInfo.getPositiveButtonResId()) { _, _ ->

                if (pkgInfo.getTypeCode() == IDisabledPkgInfo.TYPE_WEATHER_SERVICE_DOMESTIC) {
                    BaseWidgetViewHelper.getInstance().openWeatherService()
                    ClockWidgetManager.getInstance().checkLocationPermission(false, true)
                } else {
                    gotoSettings(activity, pkgInfo)
                }

            }
            .setNegativeButton(R.string.cancel, null)
            .create()
        dialog.show()
        if (pkgInfo is WeatherServiceDisableDomesticInfo) {
            pkgInfo.setMessageMovementMethod(dialog)
        }
        return dialog
    }

    private fun gotoSettings(activity: Activity, pkgInfo: IDisabledPkgInfo) {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.fromParts("package", pkgInfo.getPkgName(), null)
        activity.startActivity(intent)
    }

    private fun getPkgStatusCode(context: Context, pkgInfo: IDisabledPkgInfo) : Int {
        val applicationInfo: ApplicationInfo? = getAppInfo(context, pkgInfo.getPkgName())
        return if (applicationInfo != null) {
            if (applicationInfo.enabled) SUCCESS else PACKAGE_DISABLED
        } else {
            PACKAGE_MISSED
        }
    }

    private fun getAppInfo(context: Context, pkgName: String): ApplicationInfo? {
        val pm = context.packageManager
        return try {
            pm.getApplicationInfo(pkgName,  PackageManager.MATCH_DISABLED_COMPONENTS)
        } catch (e: PackageManager.NameNotFoundException) {
            null
        }
    }
}