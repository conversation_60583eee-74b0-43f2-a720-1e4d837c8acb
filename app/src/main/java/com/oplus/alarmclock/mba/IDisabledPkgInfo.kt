/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IDisabledPkgInfo.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/8/10     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.app.Activity
import com.oplus.alarmclock.R

interface IDisabledPkgInfo {

    companion object {
        const val TYPE_CALENDAR = 0
        const val TYPE_WEATHER = 1
        const val TYPE_WEATHER_SERVICE = 2
        const val TYPE_APP_PLATFORM = 3
        const val TYPE_WEATHER_SERVICE_DOMESTIC = 4
        const val TYPE_WEATHER_OPLUS = 5

        fun getPkgInfoByTypeCode(type: Int): IDisabledPkgInfo? {
            return when (type) {
                TYPE_CALENDAR -> CalendarDisableInfo()
                TYPE_WEATHER -> WeatherDisableInfo()
                TYPE_WEATHER_SERVICE -> WeatherServiceDisableInfo()
                TYPE_WEATHER_SERVICE_DOMESTIC -> WeatherServiceDisableDomesticInfo()
                TYPE_WEATHER_OPLUS -> OPlusWeatherDisableInfo()
                else -> null
            }
        }
    }

    fun getTypeCode() : Int

    fun getPkgName(): String

    fun getDialogTitleResId(): Int

    fun getDialogMessageText(activity: Activity): CharSequence

    fun getPositiveButtonResId(): Int {
        return R.string.enable_component
    }
}