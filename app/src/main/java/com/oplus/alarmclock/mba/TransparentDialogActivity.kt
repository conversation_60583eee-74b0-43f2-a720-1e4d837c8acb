/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TransparentDialogActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/8/10     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.mba

import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log
import com.coloros.widget.smallweather.BaseWidgetViewHelper

class TransparentDialogActivity : BaseActivity() {

    companion object {
        const val TAG = "TransparentDialogActivity"
        const val KEY_DIALOG_TYPE = "key_dialog_type"
        const val KEY_SHOW_IN_LAUNCHER = "key_show_in_launcher"
    }

    private var mDialog: AlertDialog? = null
    private var mUIMode: Int = 0
    private var mDisabledINfo: IDisabledPkgInfo? = null
    //是否显示在桌面
    private var mShowInLauncher: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
        window.navigationBarColor = Color.parseColor("#01ffffff")
        val type = intent.getIntExtra(KEY_DIALOG_TYPE, -1)
        mDisabledINfo = IDisabledPkgInfo.getPkgInfoByTypeCode(type)

        Log.d(TAG, "get pckInfo $mDisabledINfo")
        if (mDisabledINfo == null) {
            finish()
        } else {
            mShowInLauncher = intent.getBooleanExtra(KEY_SHOW_IN_LAUNCHER, false)
            mDialog = PackageDisabledManager.showEnableDialog(this, mDisabledINfo!!)
            mDialog!!.setOnDismissListener { finish() }
            mUIMode = resources.configuration.uiMode
        }
    }

    override fun finish() {
        super.finish()
        if (!mShowInLauncher) {
            overridePendingTransition(0, R.anim.anim_fade_out_slow)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
    }

    override fun onStart() {
        super.onStart()

        if (shouldDismissDialog()) {
            mDialog?.dismiss()
        }
    }

    private fun shouldDismissDialog(): Boolean {

        if (mDisabledINfo != null) {

            if (!PackageDisabledManager.isPkgDisabled(this, mDisabledINfo!!)) {
                if (mDisabledINfo!!.getTypeCode() == IDisabledPkgInfo.TYPE_WEATHER_SERVICE_DOMESTIC) {
                    if (BaseWidgetViewHelper.getInstance().agreeServiceStatement()) {
                        return true
                    }
                } else {
                    return true
                }
            }
        }
        return false
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged")
        if (mUIMode != newConfig.uiMode) {
            Log.d(TAG, "uiMode change")
            //切换暗色模式时重新弹窗以更换弹窗颜色
            mUIMode = newConfig.uiMode
            mDialog?.setOnDismissListener(null)
            mDialog?.dismiss()
            mDialog = PackageDisabledManager.showEnableDialog(this, mDisabledINfo!!)
            mDialog?.setOnDismissListener { finish() }
        }
    }

    override fun onUserLeaveHint() {
        super.onUserLeaveHint()
        Log.d(TAG, "onUserLeaveHint")
        if (mShowInLauncher) {
            mDialog?.dismiss()
        }
    }
}