/*****************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: HeadScaleWithSearchBhv.java
 ** Description: Behavior for search view
 ** Version: V 1.0
 ** Date : 2019-07-26
 ** Author: Yuxiaolong
 **
 ****************************************************************/
package com.oplus.alarmclock.behavior;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.searchview.COUISearchBar;
import com.coui.appcompat.searchview.COUISearchViewAnimate;
import com.facebook.rebound.SimpleSpringListener;
import com.facebook.rebound.Spring;
import com.facebook.rebound.SpringSystem;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;

public class HeadScaleWithSearchBhv extends CoordinatorLayout.Behavior<AppBarLayout> {

    public int mSearchViewInitPaddingRL;
    public COUIRecyclerView mInterceptTouchEventRecipient;
    private static final float SEARCH_TEXT_ICON_ALPHA = 0.3f;
    private static final String TAG = "HeadScaleWithSearchBhv";
    private static final int SEARCH_VIEW_BG_INIT_ALPHA = 38;
    private static final int SEARCHVIEW_MIN_HEIGHT = 1;
    private static final int CONSTANT_5 = 5;
    private static final int CONSTANT_2 = 2;
    private static final int CONSTANT_255 = 255;
    private static final int SEARCH_VIEW_BG_CHANGE_ALPHA = 51;
    private int mCurrentOffset;
    private int mListFirstChildEndY = 0;
    private int mSearchHeightChangeEndY = 0;
    private AppBarLayout mAppBarLayout;
    private View mChild;
    private COUIRecyclerView mScrollView;
    private COUISearchBar mSearchView;
    private Drawable mSearchViewBackGround;
    private int mSearchViewInitHeight;
    private int mSearchViewMinHeight;
    private TextView mSearchViewText;
    private ImageView mSearchViewIcon;
    private Context mContext;
    private int mStandardScroll;
    private float mSearchHeightRange;
    private float mToolBarTitleRange;
    private float mSearchPaddingRange;
    private float mSearchAlphaRange;
    private int mListFirstChildInitY;
    private int mSearchViewChangeY = 4;
    private int mLocation[] = new int[2];
    private int[] mSearchBarLocation;
    private RectF mRectF;
    private float mPressPositionY = 0;
    private int mSearchChangeOffset;
    private int mSearchHeightChangeOffset;
    private int mSearchAlphaChangeOffset;
    private int mSearchPaddingRLStartChangeY;
    private int mSearchPaddingRLEndChangeY;
    private int mSearchAlphaChangeEndY;
    private int mSearchPaddingChangeOffset;
    private int mToolbarTitleAlphaChangeInitY;
    private int mToolbarTitleAlphaChangeOffset;
    private int mTitleMarginChangeEndY;
    private int mTitleMarginChangeInitY;
    private int mTitleMarginChangeOffset;
    private int mSearchHintTextColor;
    private Resources mResources;
    private OnScrollStateChangedListener mOnScrollStateChangedListener;
    private Spring mSpring;
    private ReboundListener mReboundListener;
    private int mTempLocationY;
    private boolean mFlagListScroll = true;
    private boolean mIsFirstIn = true;
    private boolean mLastDownPointInAppBarLayout = false;

    public HeadScaleWithSearchBhv() {
    }

    public HeadScaleWithSearchBhv(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }


    private void init(Context context) {
        if (context != null) {
            mResources = context.getResources();
            mContext = context;
        } else {
            mResources = AlarmClockApplication.getInstance().getResources();
        }

        mSearchHeightChangeOffset = mResources.getDimensionPixelOffset(R.dimen.search_height_range_min_height);
        mSearchAlphaChangeOffset = mResources.getDimensionPixelOffset(R.dimen.search_alpha_range_min_count_height);
        mStandardScroll = mResources.getDimensionPixelOffset(R.dimen.standard_scroll_height);

        mSearchHintTextColor = COUIContextUtil.getAttrColor(mContext, R.attr.couiColorHintNeutral);

        mSearchViewMinHeight = (int) (SEARCHVIEW_MIN_HEIGHT * mResources.getDisplayMetrics().density);
        mReboundListener = new ReboundListener();
    }


    //判断点击屏幕的位置是否在输入框的边界中
    public boolean isBoundary(AppBarLayout child, MotionEvent ev) {
        if ((child != null) && (ev != null)) {
            if (mSearchBarLocation == null) {
                mSearchBarLocation = new int[CONSTANT_2];
                mRectF = new RectF();
            }
            child.getLocationOnScreen(mSearchBarLocation);
            mRectF.left = mSearchBarLocation[0];
            mRectF.top = mSearchBarLocation[1];
            mRectF.right = mRectF.left + child.getMeasuredWidth();
            mRectF.bottom = mRectF.top + child.getMeasuredHeight();
            return mRectF.contains(ev.getRawX(), ev.getRawY());
        }
        return false;
    }


    @Override
    public boolean onInterceptTouchEvent(@NonNull CoordinatorLayout parent, @NonNull AppBarLayout child, @NonNull MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                boolean statusDown = isBoundary(child, ev);
                if (statusDown) {
                    mPressPositionY = ev.getY();
                    mInterceptTouchEventRecipient.onTouchEvent(ev);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                boolean statusMove = isBoundary(child, ev);
                if (statusMove && (Math.abs(ev.getY() - mPressPositionY) > CONSTANT_5)) {
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mPressPositionY = 0;
                break;
        }
        return super.onInterceptTouchEvent(parent, child, ev);
    }

    @Override
    public boolean onTouchEvent(@NonNull CoordinatorLayout parent, @NonNull AppBarLayout child, @NonNull MotionEvent ev) {
        if (mInterceptTouchEventRecipient != null) {
            mInterceptTouchEventRecipient.onTouchEvent(ev);
        }
        return true;
    }

    @Override
    public boolean onStartNestedScroll(CoordinatorLayout parent, AppBarLayout child,
                                       View directTargetChild, View target, int nestedScrollAxes, int type) {
        mIsFirstIn = false;
        if ((parent == null) || (child == null) || (directTargetChild == null) || (target == null)) {
            return false;
        }
        // Return true if we're nested scrolling vertically, and we have scrollable children
        // and the scrolling view is big enough to scroll
        final boolean started = ((nestedScrollAxes & ViewCompat.SCROLL_AXIS_VERTICAL) != 0)
                && ((parent.getHeight() - directTargetChild.getHeight()) <= child.getHeight());
        if (started) {
            if (mListFirstChildInitY <= 0) {
                mContext = parent.getContext();
                mAppBarLayout = child;
                mSearchView = mAppBarLayout.findViewById(R.id.globalSearchView);

                int searchViewInitPaddingRL = mSearchView.getPaddingStart();
                if (searchViewInitPaddingRL != 0) {
                    mSearchViewInitPaddingRL = searchViewInitPaddingRL;
                }
                int searchViewInitHeight = mSearchView.getHeight();
                if ((searchViewInitHeight == 0) || (searchViewInitHeight > mSearchViewMinHeight)) {
                    mSearchViewInitHeight = searchViewInitHeight;
                }

                mSearchViewText = mSearchView.getSearchEditText();
                mSearchViewBackGround = mSearchView.getSearchEditText().getBackground();
                mSearchViewIcon = mSearchView.findViewById(R.id.animated_search_icon);
                mScrollView = (COUIRecyclerView) target;
                mListFirstChildInitY = mAppBarLayout.getMeasuredHeight() + mResources.getDimensionPixelOffset(R.dimen.list_to_ex_top_padding);
                mListFirstChildEndY = mListFirstChildInitY - mStandardScroll;
                mSearchPaddingRLStartChangeY = mListFirstChildInitY - mStandardScroll / 2;
                mSearchPaddingRLEndChangeY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.search_width_range_min_count_height);
                mSearchPaddingChangeOffset = mSearchPaddingRLStartChangeY - mSearchPaddingRLEndChangeY;
                mTitleMarginChangeInitY = mListFirstChildInitY - mStandardScroll / 2;
                mTitleMarginChangeEndY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.standard_scroll_height);
                mSearchAlphaChangeEndY = mListFirstChildInitY - mSearchAlphaChangeOffset;
                mTitleMarginChangeOffset = mTitleMarginChangeInitY - mTitleMarginChangeEndY;
                mToolbarTitleAlphaChangeInitY = mListFirstChildEndY + mResources.getDimensionPixelOffset(R.dimen.toolbar_title_alpha_range_max_count_height);
                mToolbarTitleAlphaChangeOffset = mSearchPaddingRLEndChangeY - mTitleMarginChangeEndY;

                mSearchHeightChangeEndY = mListFirstChildEndY + mSearchHeightChangeOffset;
                mSearchChangeOffset = mListFirstChildInitY - mSearchHeightChangeEndY;
                if (mSpring == null) {
                    mSpring = SpringSystem.create().createSpring();
                }
                mSpring.addListener(mReboundListener);
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                    mScrollView.setOnScrollChangeListener(new View.OnScrollChangeListener() {
                        @Override
                        public void onScrollChange(View view, int i, int i1, int i2, int i3) {
                            if (mFlagListScroll) {
                                onListScroll();
                            }
                        }
                    });
                }
                mScrollView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        super.onScrollStateChanged(recyclerView, newState);
                        if (mOnScrollStateChangedListener != null) {
                            mOnScrollStateChangedListener.onScrollStateChanged(recyclerView, newState);
                        }
                        switch (newState) {
                            case COUIRecyclerView.SCROLL_STATE_IDLE:
                                if ((null != mScrollView.getChildAt(1)) && mFlagListScroll) {
                                    mScrollView.getChildAt(1).getLocationOnScreen(mLocation);
                                    if ((mLocation[1] < mListFirstChildInitY) && (mSearchView != null) && (mSearchView.getPaddingStart() != 0)
                                            && (mSearchView.getSearchState() == COUISearchBar.STATE_NORMAL)) {
                                        if (mLocation[1] > (mListFirstChildInitY - mStandardScroll / 2)) {
                                            mTempLocationY = 0;
                                            mSpring.setCurrentValue(0);
                                            mSpring.setEndValue(mLocation[1] - mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.list_to_ex_top_padding));
                                        } else if (mLocation[1] > mTitleMarginChangeEndY) {
                                            mTempLocationY = 0;
                                            mSpring.setCurrentValue(0);
                                            mSpring.setEndValue(mLocation[1] - mTitleMarginChangeEndY - mResources.getDimensionPixelOffset(R.dimen.list_to_ex_top_padding));
                                        }
                                    }
                                }
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                            if (mFlagListScroll) {
                                onListScroll();
                            }
                        }
                    }
                });
            }

        }
        return false;
    }


    private class ReboundListener extends SimpleSpringListener {
        @Override
        public void onSpringUpdate(Spring spring) {
            if (mSpring == null) {
                return;
            }
            Log.d(TAG, "onSpringUpdate " + ((int) mSpring.getEndValue()));
            if ((mScrollView != null) && (mTempLocationY != (int) mSpring.getEndValue())) {
                mScrollView.scrollBy(0, (int) (spring.getCurrentValue() - mTempLocationY));
            } else {
                mSpring.setAtRest();
            }
            mTempLocationY = (int) spring.getCurrentValue();

        }
    }

    private void onListScroll() {
        mChild = null;
        mIsFirstIn = false;
        if ((mScrollView != null) && (mSearchView != null) && (mSearchView.getSearchState() == COUISearchBar.STATE_NORMAL)) {
            if (mScrollView instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) mScrollView;
                if (viewGroup.getChildCount() > 0) {
                    for (int i = 0; i < viewGroup.getChildCount(); i++) {
                        if (viewGroup.getChildAt(i).getVisibility() == View.VISIBLE) {
                            this.mChild = viewGroup.getChildAt(i);
                            break;
                        }
                    }
                }
            }

            if (mChild == null) {
                mChild = mScrollView;
            }

            int location[] = new int[2];
            mChild.getLocationInWindow(location);
            //int y = location[1];
            int y = mListFirstChildInitY - mScrollView.computeVerticalScrollOffset();
            int newOffset = 0;

            if (y < (mSearchHeightChangeEndY)) {
                newOffset = mSearchChangeOffset;
            } else if (y > mListFirstChildInitY) {
                newOffset = 0;
            } else {
                //(0,50)
                newOffset = mListFirstChildInitY - y;
            }
            mCurrentOffset = Math.abs(newOffset);

            if (y >= mSearchHeightChangeEndY) {
                mSearchHeightRange = mCurrentOffset / (float) (mSearchChangeOffset);
            } else {
                mSearchHeightRange = 1f;
            }

            changeHintTextViewBackground();


            if (y >= mSearchAlphaChangeEndY) {
                mSearchAlphaRange = mCurrentOffset / (float) mSearchAlphaChangeOffset;
                changeSearchTextIcon();
            } else {
                mSearchAlphaRange = 1f;
                changeSearchTextIcon();
            }

            if (y < mTitleMarginChangeEndY) {
                newOffset = mTitleMarginChangeOffset;
            } else if (y > mTitleMarginChangeInitY) {
                newOffset = 0;
            } else {
                //(50,90)
                newOffset = mTitleMarginChangeInitY - y;
            }
            mCurrentOffset = Math.abs(newOffset);

            if (y > mSearchPaddingRLStartChangeY) {
                mSearchPaddingRange = 0;
                mSearchView.setPaddingRelative(mSearchViewInitPaddingRL, mSearchView.getPaddingTop(),
                        mSearchViewInitPaddingRL, mSearchView.getPaddingBottom());
            } else {
                mSearchPaddingRange = ((mCurrentOffset > mSearchPaddingChangeOffset) ? mSearchPaddingChangeOffset : mCurrentOffset) / (float) mSearchPaddingChangeOffset;

                mSearchView.setPaddingRelative((int) (mSearchViewInitPaddingRL * (1 - mSearchPaddingRange)),
                        mSearchView.getPaddingTop(), (int) (mSearchViewInitPaddingRL
                                * (1 - mSearchPaddingRange)), mSearchView.getPaddingBottom());
            }
            if (y > mToolbarTitleAlphaChangeInitY) {
                mToolBarTitleRange = 0;
            } else {
                mCurrentOffset -= mToolbarTitleAlphaChangeInitY - mListFirstChildEndY;
                mToolBarTitleRange = mCurrentOffset / (float) mToolbarTitleAlphaChangeOffset;
                mToolBarTitleRange = Math.min(mToolBarTitleRange, 1f);
            }
            changeSearchHeight();
        }
    }

    private void changeSearchTextIcon() {
        mSearchViewText.setTextColor(Color.argb((int) ((SEARCH_TEXT_ICON_ALPHA * (1 - mSearchAlphaRange)) * CONSTANT_255),
                Color.red(mSearchHintTextColor), Color.green(mSearchHintTextColor), Color.blue(mSearchHintTextColor)));
        mSearchViewText.setAlpha(1 - mSearchAlphaRange);
        mSearchViewIcon.setAlpha(1 - mSearchAlphaRange);
    }

    private void changeSearchHeight() {
        if (mSearchView != null) {
            AppBarLayout.LayoutParams mSearchViewLayoutParams = (AppBarLayout.LayoutParams) mSearchView.getLayoutParams();
            mSearchViewLayoutParams.height = (int) (mSearchViewInitHeight * (1 - mSearchHeightRange));
            float ratio = 1 - mSearchHeightRange;
            mSearchView.setSearchViewAnimateHeightPercent(ratio);
            mSearchView.setLayoutParams(mSearchViewLayoutParams);
            mSearchView.setTranslationY(-((mSearchHeightRange) * (mSearchViewInitHeight) / mSearchViewChangeY));
        }
    }

    private void changeHintTextViewBackground() {
        if ((mContext != null) && COUIDarkModeUtil.isNightMode(mContext)) {
            if (mSearchViewBackGround instanceof GradientDrawable) {
                ((GradientDrawable) mSearchViewBackGround).setColor(Color.argb(((int) (SEARCH_VIEW_BG_INIT_ALPHA
                        + (SEARCH_VIEW_BG_CHANGE_ALPHA - SEARCH_VIEW_BG_INIT_ALPHA)
                        * mSearchHeightRange)), CONSTANT_255, CONSTANT_255, CONSTANT_255));
            }
        }
    }

    public void setScaleEnable(boolean isEnable) {
        mFlagListScroll = isEnable;
    }

    public boolean getIsFirstIn() {
        return mIsFirstIn;
    }

    public interface OnScrollStateChangedListener {
        void onScrollStateChanged(RecyclerView view, int scrollState);
    }


    public void setOnScrollStateChangedListener(OnScrollStateChangedListener listener) {
        mOnScrollStateChangedListener = listener;
    }

}

