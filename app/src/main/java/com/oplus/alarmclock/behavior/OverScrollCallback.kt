/*
 *
 *  * ***************************************************************
 *  * Copyright (C), 2010-2021, OPLUS Mobile Comm Corp., Ltd.
 *  * VENDOR_EDIT
 *  * File:  - OverScrollCallback.kt
 *  * Version: 1.0
 *  * Date : 2021/04/08
 *  * Author: hewei
 *  * ---------------------Revision History: -----------------------
 *  * <author>    <data>                        <version >     <desc>
 *  * hewei       2021/04/08   1.0            OverScrollCallback.kt
 *  * ***************************************************************
 *
 *
 */

package com.oplus.alarmclock.behavior

import android.view.View


interface OverScrollCallback {

    //获取阻尼因子
    fun getDampingFactor(child: View): Float

    //获取列表当前的位移距离
    fun getOffset(child: View): Int

    //更新列表的位移
    fun updateOffset(child: View, offset: Int)

    //列表本身是否能滑动
    fun canOverScroll(child: View): Boolean

    //松手后是否能回弹
    fun canSpringBack(child: View): Boolean

    //获取列表最大的位移
    fun getMaxOverScrollOffset(child: View): Int

//    //列表回弹
//    fun springBack(child: View,translationY:Int)
//
//    //停止回弹
//    fun stopSpringBack()

    //记录列表消耗的距离
    fun onNestedPreScrollList(child: View, target: View, distance: Int): Int

    //执行滑动
    fun scroll(child: View, distance: Int, min: Int = 0, max: Int = Int.MAX_VALUE): Int
}