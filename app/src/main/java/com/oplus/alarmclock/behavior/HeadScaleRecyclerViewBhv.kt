/*********************************************************************************
 ** Copyright (C), 2008-2016, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUSOS_EDIT, All rights reserved.
 **
 ** File: - HeadScaleRecyclerViewBhv.kt
 ** Description: Implement behavior of main interface
 **
 ** Version: 1.0
 ** Date: 2019-02-01
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Yupeng.<PERSON><PERSON>@ROM.SDK             2019-01-07   1.0         Create this module
 ** <EMAIL>              2020-03-27   1.1         Convert this module into Kotlin
 ********************************************************************************/
package com.oplus.alarmclock.behavior

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log
import com.facebook.rebound.SimpleSpringListener
import com.facebook.rebound.Spring

class HeadScaleRecyclerViewBhv(context: Context, attrs: AttributeSet?) : HeadBaseScroll(context, attrs) {

    companion object {
        private const val TAG = "HeadScaleRecyclerViewBhv"
    }

    private val mReboundListener: ReboundListener
    private var mSmallTabBehavior: com.oplus.alarmclock.behavior.SmallTabBehavior? = null
    private var mIsEditMode = true

    constructor (context: Context, attrs: AttributeSet?, smallTabBehavior: com.oplus.alarmclock.behavior.SmallTabBehavior) : this(context, attrs) {
        mSmallTabBehavior = smallTabBehavior
    }

    init {
        mReboundListener = ReboundListener()
        mSpring.addListener(mReboundListener)
    }

    override fun onStartNestedScroll(parent: CoordinatorLayout, child: AppBarLayout,
                                     directTargetChild: View, target: View, nestedScrollAxes: Int, type: Int): Boolean {
        // Return true if we're nested scrolling vertically, and we have scrollable children
        // and the scrolling view is big enough to scroll
        val started = (nestedScrollAxes and ViewCompat.SCROLL_AXIS_VERTICAL != 0
                && parent.height - directTargetChild.height <= child.height)
        Log.i(TAG, "onStartNestedScroll: started:$started")
        if (started) {
            if (mListFirstChildInitY <= 0) {
                mContext = parent.context
                mAppBarLayout = child
                mToolbar = mAppBarLayout?.findViewById(R.id.toolbar)
                mScrollView = target

                mOriginLocationY = mListFirstChildInitY
                mListFirstChildEndY = mListFirstChildInitY - mStandardScroll / 2
                mToolbarChangeInitY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.toolbar_title_start_change_offset)
                mTitleMarginChangEndY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.title_margin_top_change_offset)
                mToolbarChangeOffset = mToolbarChangeInitY - mTitleMarginChangEndY
                mDividerWidthChangeInitY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.line_width_range_count_height)

                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                    mScrollView?.setOnScrollChangeListener { _, _, _, _, _ ->
                        if (mScaleEnable) {
                            if (mIsEditMode) {
                                onListScroll()
                            }
                        }
                    }

                }
                if (mScrollView is COUIRecyclerView) {
                    (mScrollView as COUIRecyclerView).addOnScrollListener(object : RecyclerView.OnScrollListener() {
                        override fun onScrollStateChanged(recyclerView: RecyclerView, state: Int) {
                            when (state) {
                                COUIRecyclerView.SCROLL_STATE_IDLE -> {
                                    if (mIsEditMode) {
                                        startRebound()
                                    }
                                }
                            }
                        }
                    })
                } else if (mScrollView is ScrollView) {
                    (mScrollView as ScrollView).setOnTouchListener { view, event ->
                        if (event?.action == MotionEvent.ACTION_UP) {
                            if (mIsEditMode) {
                                startRebound()
                            }
                        }
                        false
                    }
                }
            }
        }
        return false
    }


    fun setEditMode(mode: Boolean) {
        mIsEditMode = mode

        if (!mIsEditMode) {
            mListFirstChildInitY = 0

            if (mScrollView != null) {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                    mScrollView!!.setOnScrollChangeListener(null)
                }
            }
        }
    }

    private inner class ReboundListener : SimpleSpringListener() {
        override fun onSpringUpdate(spring: Spring) {

            if (!mIsEditMode) {
                return
            }

            if (mTempLocationY != mSpring.endValue.toInt()) {
                if (mScrollView is COUIRecyclerView) {
                    (mScrollView as COUIRecyclerView).scrollBy(0, (spring.currentValue - mTempLocationY).toInt())
                }
                if (mScrollView is ScrollView) {
                    (mScrollView as ScrollView).scrollBy(0, (spring.currentValue - mTempLocationY).toInt())
                }
            } else {
                mSpring.setAtRest()
            }
            mTempLocationY = mSpring.currentValue.toInt()
        }
    }

    fun endSpringScroll() {
        mSpring.setAtRest()
    }

    fun startRebound() {
        if ((mScrollView as ViewGroup).childCount == 1) {
            return
        }
        (mScrollView as ViewGroup).getChildAt(1)?.getLocationOnScreen(mListViewLocation)

        if (mScaleEnable) {
            if (mLocation[1] < mTitleInitLocationY && mLocation[1] >= mMiddleLocationY) {
                mTempLocationY = 0
                mSpring.currentValue = 0.0
                mSpring.endValue = mListViewLocation[1] - mOriginLocationY.toDouble()
            } else if (mListViewLocation[1] > mListFirstChildEndY && mLocation[1] < mMiddleLocationY && mLocation[1] >= mTitleInitLocationY) {
                mTempLocationY = 0
                mSpring.currentValue = 0.0
                mSpring.endValue = mListViewLocation[1] - mListFirstChildEndY.toDouble()
            }
        }
    }
}