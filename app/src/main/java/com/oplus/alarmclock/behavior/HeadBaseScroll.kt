/*********************************************************************************
 ** Copyright (C), 2008-2016, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUSOS_EDIT, All rights reserved.
 **
 ** File: - HeadBaseScroll.kt
 ** Description: Implement behavior of main interface
 **
 ** Version: 1.0
 ** Date: 2019-02-01
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Yupeng.<PERSON><PERSON>@ROM.SDK             2019-01-07   1.0         Create this module
 ** <EMAIL>              2020-03-27   1.1         Convert this module into Kotlin
 ********************************************************************************/
package com.oplus.alarmclock.behavior

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior
import com.coui.appcompat.contextutil.COUIContextUtil
import com.google.android.material.appbar.AppBarLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.facebook.rebound.Spring
import com.facebook.rebound.SpringSystem
import kotlin.math.abs


open class HeadBaseScroll(context: Context, attrs: AttributeSet?) : Behavior<AppBarLayout>(context, attrs) {

    companion object {
        private const val TAG = "HeadBaseScroll"
    }

    protected val mResources: Resources = context.resources
    protected var mContext: Context = context

    protected var mAppBarLayout: AppBarLayout? = null
    protected var mToolbar: COUIToolbar? = null
    protected var mScrollView: View? = null
    protected var mLargeTitleParams: AppBarLayout.LayoutParams? = null
    protected var mDividerParams: ViewGroup.LayoutParams? = null
    protected var mChild: View? = null

    protected var mCurrentOffset = 0
    protected var mListFirstChildInitY = 0
    protected var mTotalScaleRange = 0
    protected var mScaleEnable = true
    protected var mStandardScroll = 0
    protected var mTextViewPaddingTop = 0
    protected var mTitleInitHeight = 0
    protected var mLargeTitleHeight = 0
    protected var mDividerLineMarginRL = 0
    protected var mTitleInitLocationY = 0
    protected var mMiddleLocationY = 0
    protected var mToolbarChangeInitY = 0
    protected var mTitleMarginChangEndY = 0
    protected var mToolbarChangeOffset = 0
    protected var mDividerWidthChangeInitY = 0
    protected var mListFirstChildPadding = 0
    private var mDividerAlphaChangeOffset = 0
    private var mTitleAlphaChangeOffset = 0
    protected var mTitleMarginChangeOffset = 0
    private var mToolbarStartChangeOffset = 0
    private var mTransparentColor = 0
    private var mTitleColor = 0
    private var mDividerWidthRange = 0f
    private var mDividerAlphaRange = 0f
    private var mToolbarTitleAlphaRange = 0f
    private var mTitleMarginTopRange = 0f
    private var mTitleAlphaRange = 0f
    protected var mSpringSystem: SpringSystem = SpringSystem.create()
    protected var mSpring: Spring = mSpringSystem.createSpring()
    protected var mOriginLocationY = 0
    protected var mTempLocationY = 0
    protected var mLocation = IntArray(2)
    protected var mListViewLocation = IntArray(2)
    protected var mTitlePaddingTop = 0
    protected var mFirstIn = true
    var mListFirstChildEndY = 0

    init {
        mListFirstChildPadding = mResources.getDimensionPixelOffset(R.dimen.category_top_padding)
        mTitleAlphaChangeOffset = mResources.getDimensionPixelOffset(R.dimen.title_alpha_rang_min_count_height)
        mDividerAlphaChangeOffset = mResources.getDimensionPixelOffset(R.dimen.line_alpha_range_change_offset)
        mToolbarStartChangeOffset = mResources.getDimensionPixelOffset(R.dimen.toolbar_title_start_change_offset)
        mTitleMarginChangeOffset = mResources.getDimensionPixelOffset(R.dimen.title_margin_top_change_offset)
        mDividerLineMarginRL = mResources.getDimensionPixelOffset(R.dimen.common_margin)
        mStandardScroll = mResources.getDimensionPixelOffset(R.dimen.standard_scroll_height)
        mTransparentColor = COUIContextUtil.getAttrColor(mContext, R.color.coui_transparence)
        mTitleColor = COUIContextUtil.getAttrColor(mContext, R.attr.couiColorPrimaryNeutral)
    }


    fun onListScroll() {
        mChild = null
        if (mScrollView is ViewGroup) {
            val viewGroup = mScrollView as ViewGroup
            if (viewGroup.childCount > 0) {
                for (i in 0 until viewGroup.childCount) {
                    if (viewGroup.getChildAt(i).visibility == View.VISIBLE) {
                        mChild = viewGroup.getChildAt(i)
                        break
                    }
                }
            }
        }
        if (mChild == null) {
            mChild = mScrollView
        }
        val location = IntArray(2)
        mChild?.getLocationOnScreen(location)
        val y = location[1]
        var newOffset: Int
        /**
         * @param titleAlphaRange 大标题Alpha变化系数，以25dp为有效滑动距离算出, mListFirstChildInitY-25dp<=y<=mListFirstChildInitY.
         */
        newOffset = when {
            y < mListFirstChildInitY - mTitleAlphaChangeOffset -> {
                mTitleAlphaChangeOffset
            }
            y > mListFirstChildInitY -> {
                0
            }
            else -> {
                //            (0,25)
                mListFirstChildInitY - y
            }
        }
        mCurrentOffset = newOffset
        if (y > mListFirstChildInitY - mTitleAlphaChangeOffset) {
            mTitleAlphaRange = abs(mCurrentOffset) / mTitleAlphaChangeOffset.toFloat()
        }
        /**
         * @param lineWidthRange 分割线Width变化系数，以25dp为有效滑动距离算出, mListFirstChildEndY<=y<=mListFirstChildInitY-25dp.
         */
        newOffset = when {
            y < mListFirstChildEndY -> {
                mDividerWidthChangeInitY - mListFirstChildEndY
            }
            y > mDividerWidthChangeInitY -> {
                0
            }
            else -> {
                //            (25,50)
                mDividerWidthChangeInitY - y
            }
        }
        mCurrentOffset = newOffset
        if (y >= mListFirstChildEndY) {
            mDividerWidthRange = abs(mCurrentOffset) / (mDividerWidthChangeInitY - mListFirstChildEndY).toFloat()

            if (mDividerParams is AppBarLayout.LayoutParams) {
                (mDividerParams as AppBarLayout.LayoutParams)?.let {
                    it.setMargins((mDividerLineMarginRL * (1 - mDividerWidthRange)).toInt(), it.topMargin,
                            (mDividerLineMarginRL * (1 - mDividerWidthRange)).toInt(), it.bottomMargin)
                }
            } else if (mDividerParams is CoordinatorLayout.LayoutParams) {
                (mDividerParams as CoordinatorLayout.LayoutParams)?.let {
                    it.setMargins((mDividerLineMarginRL * (1 - mDividerWidthRange)).toInt(), it.topMargin,
                            (mDividerLineMarginRL * (1 - mDividerWidthRange)).toInt(), it.bottomMargin)
                }
            }
        } else {
            if (mDividerParams is AppBarLayout.LayoutParams) {
                (mDividerParams as AppBarLayout.LayoutParams).let {
                    it.setMargins(0, it.topMargin, 0, it.bottomMargin)
                }
            } else if (mDividerParams is CoordinatorLayout.LayoutParams) {
                (mDividerParams as CoordinatorLayout.LayoutParams).let {
                    it.setMargins(0, it.topMargin, 0, it.bottomMargin)
                }
            }
        }
        /**
         * @param  marginTopRange 大标题MarginTop变化系数，以40dp为有效滑动距离算出, mListFirstChildInitY-40dp<=y<=mListFirstChildInitY.
         */
        newOffset = when {
            y < mTitleMarginChangEndY -> {
                mTitleMarginChangeOffset
            }
            y > mListFirstChildInitY -> {
                0
            }
            else -> { //(0,40)
                mListFirstChildInitY - y
            }
        }
        mCurrentOffset = abs(newOffset)
        mToolbar?.setTitleTextColor(
            Color.argb(
                (255).toInt(),
                Color.red(mTitleColor),
                Color.green(mTitleColor),
                Color.blue(mTitleColor)
            )
        )
    }

    fun setScaleEnable(enable: Boolean) {
        mScaleEnable = enable
    }
}