/*****************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: SecondToolbarBehavior.java
 ** Description: the behavior for AppBarLayout and Toolbar
 ** Version: V 1.0
 ** Date : 2019-07-26
 ** Author: Yuxiaolong
 **
 ****************************************************************/
package com.oplus.alarmclock.behavior;

import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;

import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.Utils;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;

public class SecondToolbarBehavior extends CoordinatorLayout.Behavior<AppBarLayout> implements AbsListView.OnScrollListener {
    public static final int ALPHA_CHANGE_OFFSET = 15;
    public static final int FIRST_CHILD_INIT_Y_OFFSET = 35;

    private View mDivider;
    private View mScrollView;
    private View mChild;
    private View mChildTemp;
    private int mLocationY;
    private int mNewOffset;
    private int mCurrentOffset;
    private int mLocation[] = new int[2];
    private int mMaxWidth;
    private ViewGroup.LayoutParams mDividerParams;
    private int mMarginLeftRight;
    private int mListFirstChildInitY;
    private int mDividerAlphaChangeEndY;
    private int mDividerAlphaChangeOffset;
    private int mDividerWidthChangeEndY;
    private int mDividerWidthChangeInitY;
    private int mDividerWidthChangeOffset;
    private float mDividerAlphaRange;
    private float mDividerWidthRange;
    private Resources mResources;
    public int mDividerInitWidth;

    public SecondToolbarBehavior() {
        super();
    }

    public SecondToolbarBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        if (context != null) {
            mResources = context.getResources();
        } else {
            mResources = AlarmClockApplication.getInstance().getResources();
        }
        mMarginLeftRight = 2 * mResources.getDimensionPixelOffset(R.dimen.common_margin);
        mDividerAlphaChangeOffset = mResources.getDimensionPixelOffset(R.dimen.line_alpha_range_change_offset);
        mDividerWidthChangeOffset = mResources.getDimensionPixelOffset(R.dimen.divider_width_change_offset);
    }

    @Override
    public boolean onStartNestedScroll(@NonNull CoordinatorLayout coordinatorLayout, @NonNull AppBarLayout child, @NonNull View directTargetChild, @NonNull View target, int axes, int type) {
        if ((coordinatorLayout == null) || (child == null) || (directTargetChild == null) || (target == null)) {
            return false;
        }
        final boolean started = (axes & ViewCompat.SCROLL_AXIS_VERTICAL) != 0
                && coordinatorLayout.getHeight() - directTargetChild.getHeight() <= child.getHeight();
        if (started) {
            if (mListFirstChildInitY <= 0) {
                mListFirstChildInitY = child.getMeasuredHeight();
                if (child.getContext() != null) {
                    if (FlexibleWindowUtils.isSupportFlexibleActivity()
                            && FlexibleWindowUtils.isFlexibleActivitySuitable(mResources.getConfiguration())) {
                        mListFirstChildInitY += Utils.getStatusBarHeight(child.getContext());
                    }
                }
                mScrollView = target;
                mDivider = child.findViewById(R.id.divider_line);
                mDividerInitWidth = mDivider.getWidth();
                mDividerParams = mDivider.getLayoutParams();
                mMaxWidth = child.getMeasuredWidth();
                mDividerAlphaChangeEndY = mListFirstChildInitY - mDividerAlphaChangeOffset - ALPHA_CHANGE_OFFSET;
                mDividerWidthChangeInitY = mListFirstChildInitY - mResources.getDimensionPixelOffset(R.dimen.divider_width_start_count_offset);
                mDividerWidthChangeEndY = mDividerWidthChangeInitY - mDividerWidthChangeOffset;
                mListFirstChildInitY -= FIRST_CHILD_INIT_Y_OFFSET;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                target.setOnScrollChangeListener(new View.OnScrollChangeListener() {
                    @Override
                    public void onScrollChange(View view, int i, int i1, int i2, int i3) {
                        onListScroll();
                    }
                });
            } else if (target instanceof AbsListView) {
                AbsListView listView = (AbsListView) target;
                listView.setOnScrollListener(this);
            }
        }
        return false;
    }

    @Override
    public void onScrollStateChanged(AbsListView absListView, int i) {

    }

    @Override
    public void onScroll(AbsListView absListView, int i, int i1, int i2) {
        onListScroll();
    }

    private void onListScroll() {
        mChild = null;
        if (mScrollView instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) mScrollView;
            if (viewGroup.getChildCount() > 0) {
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    if (viewGroup.getChildAt(i).getVisibility() == View.VISIBLE) {
                        this.mChild = viewGroup.getChildAt(i);
                        if (mChildTemp == null) {
                            mChildTemp = mChild;
                        }
                        break;
                    }
                }
            }
        }
        if (mChild != mChildTemp) {
            return;
        }
        if (mChild == null) {
            mChild = mScrollView;
        }
        mChild.getLocationOnScreen(mLocation);
        mLocationY = mLocation[1];
        mNewOffset = 0;
        if (mLocationY < mDividerAlphaChangeEndY) {
            mNewOffset = mDividerAlphaChangeOffset;
        } else if (mLocationY > mListFirstChildInitY) {
            mNewOffset = 0;
        } else {
//            (0,10)
            mNewOffset = mListFirstChildInitY - mLocationY;
        }
        mCurrentOffset = mNewOffset;
        if (!(mDividerAlphaRange > 1)) {
            mDividerAlphaRange = Math.abs(mCurrentOffset) / (float) mDividerAlphaChangeOffset;
            mDivider.setAlpha(mDividerAlphaRange);
        }

        if (mLocationY < mDividerWidthChangeEndY) {
            mNewOffset = mDividerWidthChangeOffset;
        } else if (mLocationY > mDividerWidthChangeInitY) {
            mNewOffset = 0;
        } else {
//            (10,35)
            mNewOffset = mDividerWidthChangeInitY - mLocationY;
        }
        mCurrentOffset = mNewOffset;
        mDividerWidthRange = Math.abs(mCurrentOffset) / (float) mDividerWidthChangeOffset;
        mDividerParams.width = (int) (mDividerInitWidth + mMarginLeftRight * mDividerWidthRange);
        mDivider.setLayoutParams(mDividerParams);
    }
}
