/*****************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: SmallTabBehavior.java
 ** Description:
 ** Version: V 1.0
 ** Date : 2019-09-05
 ** Author: Yuxiaolong
 **
 ****************************************************************/
package com.oplus.alarmclock.behavior;

import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;

import com.coui.appcompat.tablayout.COUITabLayout;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;

import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewpager2.widget.ViewPager2;

public class SmallTabBehavior extends CoordinatorLayout.Behavior<AppBarLayout> implements AbsListView.OnScrollListener {

    private static final String TAG = "SmallTabBehavior";
    private int mListFirstChildInitY;
    private ViewPager2 mViewPager;
    private View mScrollView;
    private int mCurrentOffset;
    private COUITabLayout mTabLayout;
    private boolean mHandleScroll = false;
    private View mChild = null;
    private int mTitleTop;
    private View mDividerLine;
    private int mDividerAlphaChangeEndY;
    private int mDividerWidthChangeInitY;
    private int mDividerWidthChangeEndY;
    private int mDividerWidthChangeOffset;
    private int mDividerALphaChangeOffset;
    private int mMarginLeftRight;
    private CoordinatorLayout.LayoutParams mDividerParams;
    private float mDividerWidthRange;
    private float mDividerAlphaRange;
    private Resources mResources;
    private int mLocation[] = new int[2];
    private AlarmClock.LocalFragmentPagerAdapter mViewPagerAdapter;

    private HeadScaleRecyclerViewBhv mEditModeBehavior;
    private boolean mIsEditMode = false;

    public SmallTabBehavior() {
    }

    public SmallTabBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
        mEditModeBehavior = new HeadScaleRecyclerViewBhv(context, attrs, this);
        Log.i(TAG, "SmallTabBehavior: " + mEditModeBehavior);
    }

    private void init(Context context) {
        if (context != null) {
            mResources = context.getResources();
        } else {
            mResources = AlarmClockApplication.getInstance().getResources();
        }
        mDividerALphaChangeOffset = mResources.getDimensionPixelOffset(R.dimen.line_alpha_range_change_offset);
        mDividerWidthChangeOffset = mResources.getDimensionPixelOffset(R.dimen.divider_width_change_offset);
        mMarginLeftRight = mResources.getDimensionPixelOffset(R.dimen.common_margin);
    }

    @Override
    public boolean onStartNestedScroll(CoordinatorLayout parent, final AppBarLayout colorAppBarLayout, View view, View target, int i, int i1) {
        if ((parent == null) || (colorAppBarLayout == null) || (target == null)) {
            return false;
        }
        if (mIsEditMode) {
            mEditModeBehavior.onStartNestedScroll(parent, colorAppBarLayout, view, target, i, i1);
            mTabLayout = null;
            return false;
        }

        if (null == mTabLayout) {
            for (int index = 0; index < colorAppBarLayout.getChildCount(); index++) {
                if (colorAppBarLayout.getChildAt(index) instanceof FrameLayout) {
                    FrameLayout frameLayout = (FrameLayout) colorAppBarLayout.getChildAt(index);
                    for (int indexInner = 0; indexInner < frameLayout.getChildCount(); indexInner++) {
                        if (frameLayout.getChildAt(index) instanceof COUITabLayout) {
                            mTabLayout = (COUITabLayout) frameLayout.getChildAt(index);
                        }
                    }
                }
            }

            mViewPager = parent.findViewById(R.id.view_pager);
            mScrollView = target;
            if (mViewPager.getAdapter() != null) {
                mViewPagerAdapter = (AlarmClock.LocalFragmentPagerAdapter) mViewPager.getAdapter();
            }

            int location[] = new int[2];
            colorAppBarLayout.getLocationOnScreen(location);
            mTitleTop = location[1];
            mListFirstChildInitY = colorAppBarLayout.getMeasuredHeight();
            mDividerAlphaChangeEndY = mListFirstChildInitY - mDividerALphaChangeOffset;
            mDividerWidthChangeInitY = mDividerAlphaChangeEndY;
            mDividerWidthChangeEndY = mDividerWidthChangeInitY - mDividerWidthChangeOffset;
        }

        if ((mViewPagerAdapter != null) && (mViewPagerAdapter.getCurrentFragment() != null)) {

            mDividerLine = mViewPagerAdapter.getCurrentFragment().mDividerLine;
            if (mDividerLine != null) {
                mDividerParams = ((CoordinatorLayout.LayoutParams) mDividerLine.getLayoutParams());
            }
            if ((mTabLayout != null) && (!mTabLayout.isEnabled())) {
                mHandleScroll = false;
                return false;
            }
            mScrollView = target;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                target.setOnScrollChangeListener(new View.OnScrollChangeListener() {
                    @Override
                    public void onScrollChange(View view, int i, int i1, int i2, int i3) {
                        onListScroll();
                    }
                });
            } else if (target instanceof AbsListView) {
                AbsListView listView = (AbsListView) target;
                listView.setOnScrollListener(this);
            }
            mHandleScroll = canChildScrollUp(colorAppBarLayout) || (mCurrentOffset != 0);
        }
        return false;
    }

    @Override
    public void onScrollStateChanged(AbsListView view, int scrollState) {

    }

    @Override
    public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
        if (mIsEditMode) {
            return;
        }
        onListScroll();
    }

    public void setEditMode(boolean mode) {
        mIsEditMode = mode;
        mEditModeBehavior.setEditMode(mode);
    }

    public void setDividerLine(View dividerLine) {
        mDividerLine = dividerLine;
    }

    public View getDividerLine() {
        return mDividerLine;
    }

    public void resetScroll() {
        mEditModeBehavior.endSpringScroll();
    }

    private void onListScroll() {
        if (mDividerLine != null) {
            mDividerLine.getLocationOnScreen(mLocation);
        }
        mChild = null;
        if (mScrollView instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) mScrollView;
            if (viewGroup.getChildCount() > 0) {
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    if (viewGroup.getChildAt(i).getVisibility() == View.VISIBLE) {
                        this.mChild = viewGroup.getChildAt(i);
                        break;
                    }
                }
            }
        }
        if (mChild == null) {
            mChild = mScrollView;
        }
        int location[] = new int[2];
        mChild.getLocationOnScreen(location);
        int y = location[1];
        int newOffset = 0;

        if (y < mDividerAlphaChangeEndY) {
            newOffset = mDividerALphaChangeOffset;
        } else if (y > mListFirstChildInitY) {
            newOffset = 0;
        } else {
            // (0,10)
            newOffset = mListFirstChildInitY - y;
        }
        mCurrentOffset = newOffset;
        if (mDividerLine != null) {
            if (y > mDividerAlphaChangeEndY) {
                mDividerAlphaRange = Math.abs(mCurrentOffset) / (float) (mDividerALphaChangeOffset);
                mDividerLine.setAlpha(mDividerAlphaRange);
            } else {
                mDividerLine.setAlpha(1f);
            }
        }
        if (y < mDividerWidthChangeEndY) {
            newOffset = mDividerWidthChangeOffset;
        } else if (y > mDividerWidthChangeInitY) {
            newOffset = 0;
        } else {
            //(10,35)
            newOffset = mDividerWidthChangeInitY - y;
        }
        mCurrentOffset = newOffset;
        if ((mDividerParams != null) && (mDividerLine != null)) {
            if (y > mDividerWidthChangeInitY) {
                mDividerWidthRange = 0;
                mDividerParams.setMargins(mMarginLeftRight, mDividerParams.topMargin, mMarginLeftRight, mDividerParams.bottomMargin);
                mDividerLine.setLayoutParams(mDividerParams);
            } else {
                mDividerWidthRange = Math.abs(mCurrentOffset) / (float) (mDividerWidthChangeOffset);
                mDividerParams.setMargins((int) (mMarginLeftRight * (1 - mDividerWidthRange)), mDividerParams.topMargin, (int) (mMarginLeftRight * (1 - mDividerWidthRange)), mDividerParams.bottomMargin);
                mDividerLine.setLayoutParams(mDividerParams);
            }
        }
    }


    private int getViewPositionY(View view) {
        View firstChild = null;
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            if (viewGroup.getChildCount() > 0) {
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    if (viewGroup.getChildAt(i).getVisibility() == View.VISIBLE) {
                        firstChild = viewGroup.getChildAt(i);
                        break;
                    }
                }
            }
        }
        if (firstChild == null) {
            firstChild = view;
        }
        int[] position = new int[2];
        firstChild.getLocationOnScreen(position);
        return position[1] - mTitleTop;
    }


    private boolean canChildScrollUp(AppBarLayout colorAppBarLayout) {
        if (mScrollView == null) {
            return true;
        }

        return getViewPositionY(mScrollView) < colorAppBarLayout.getMeasuredHeight();
    }

}
