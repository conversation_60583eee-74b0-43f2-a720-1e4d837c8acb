/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :PlatformUtilsClockServices
 * Service for health app and clock interaction
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.oplus.alarmclock.aidl;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;

import com.coloros.alarmclock.IClockAidlInterface;
import com.coloros.alarmclock.IClockUpdateAidlInterface;
import com.coloros.alarmclock.PlatformClockInfo;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper;
import com.oplus.alarmclock.alert.AlarmAlertUtilsKt;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.clock.common.utils.Log;

import androidx.annotation.Nullable;

import kotlin.Triple;

public class PlatformUtilsClockServices extends Service {

    private static final String TAG = "PlatformUtilsClockServices";


    public PlatformUtilsClockServices() {
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return new MyBinder();
    }


    static class MyBinder extends IClockAidlInterface.Stub {

        @Override
        public void bindAlarmClock() {
        }


        @Override
        public boolean dismissClock(long scheduleId) {

            Log.i(TAG, "dismissClock scheduleId =" + scheduleId);
            try {
                AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                if ((currentSchedule != null) && (scheduleId == currentSchedule.getId())) {
                    Log.i(TAG, "dismissClock scheduleId =" + scheduleId + "   currentSchedule :" + currentSchedule.toString());
                    if (currentSchedule.getAlarm() != null && currentSchedule.getAlarm().getmGarbSwitch() == 1) {
                        //秒抢闹钟
                        GarbAlarmSeedlingHelper.reTractSeedlingCard(AlarmClockApplication.getInstance(), true);
                        Log.d(TAG, "garbAlarm  dismiss");
                        AlarmRingOperateUtils.closeAlarm(currentSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_WATCH);
                        return true;
                    }
                    AlarmAlertUtilsKt.alarmDismiss(AlarmClockApplication.getInstance());
                    AlarmUtils.stopAlarm(AlarmClockApplication.getInstance(), currentSchedule.getAlarmId());
                    ScheduleUtils.cancelNotification(AlarmClockApplication.getInstance(), currentSchedule.getId());
                    AlarmRingOperateUtils.closeAlarm(currentSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_WATCH);
                } else {
                    if (currentSchedule == null) {
                        Log.i(TAG, " dismissClock : No alarm is ringing now");
                    } else {
                        Log.i(TAG, " dismissClock : currentSchedule : " + currentSchedule.getId() + "   , scheduleId : " + scheduleId);
                    }
                }
                return true;
            } catch (Exception e) {
                Log.e(TAG, " dismissClock e : " + e.getMessage());
                return false;
            }
        }

        @Override
        public boolean snoozeClock(long scheduleId) {
            Log.i(TAG, "snoozeClock scheduleId =" + scheduleId);
            try {
                AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                if ((currentSchedule != null) && (currentSchedule.getId() == scheduleId)) {
                    Log.i(TAG, "dismissClock scheduleId =" + scheduleId + "   currentSchedule :" + currentSchedule.toString());
                    int snoozeTime = currentSchedule.getSnoonzeTime();
                    Log.i(TAG, "snoozeClock  snoozeTime = " + snoozeTime);
                    if (snoozeTime < (currentSchedule.getAlarm().getRingNum() - 1)) {
                        Log.i(TAG, "snoozeClock  this is not  last alarm ring ,can continue to snooze");
                        AlarmAlertUtilsKt.alarmSnooze(AlarmClockApplication.getInstance());
                    } else {
                        Log.i(TAG, "snoozeClock  this is last alarm ring ,so ,stop this alarm");
                        AlarmAlertUtilsKt.alarmDismiss(AlarmClockApplication.getInstance());
                        AlarmUtils.stopAlarm(AlarmClockApplication.getInstance(), currentSchedule.getAlarmId());
                    }
                    ScheduleUtils.cancelNotification(AlarmClockApplication.getInstance(), currentSchedule.getId());

                } else {
                    if (currentSchedule == null) {
                        Log.i(TAG, " snoozeClock : No alarm is ringing now");
                    } else {
                        Log.i(TAG, " snoozeClock : currentSchedule : " + currentSchedule.getId() + "   , scheduleId : " + scheduleId);
                    }
                }
                return true;
            } catch (Exception e) {
                Log.e(TAG, " snoozeClock e : " + e.getMessage());
                return false;
            }
        }

        //手机收到iot通知后的回调
        @Override
        public void notifyDataChange(int enableAssociate, long alarmId) {
            try {
                if (enableAssociate == 0) {
                    return;
                }
                boolean success = AlarmUtils.updateIOTEnabled(enableAssociate, alarmId);
                Log.i(TAG, "notifyDataChange result:" + success + "，enableAssociate:" + enableAssociate + ",alarmId:" + alarmId);
            } catch (Exception e) {
                Log.e(TAG, "notifyDataChange e:" + e);
            }
        }

        @Override
        public boolean registerListener(IClockUpdateAidlInterface listener) {
            Log.i(TAG, "registerListener listener =" + listener);
            try {
                PlatformUtils.getInstance().registerListener(listener);
                return true;
            } catch (Exception e) {
                Log.e(TAG, " registerListener e : " + e.getMessage());
                return false;
            }
        }

        @Override
        public void unbindAlarmClock() {
            Log.i(TAG, "unbindAlarmClock");
        }

        @Override
        public void reWakeUpCurrentAlarmRing() {
            Log.e(TAG, " reWakeUpCurrentAlarmRing ");
            try {
                AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                if (currentSchedule != null) {
                    String alarmLabel = currentSchedule.getAlarmLabel();
                    int ringNum = ClockConstant.SNOOZE_RING_NUM;
                    int snoozeTime = ClockConstant.SNOOZE_AFTER_MIN;
                    int garbSwitch = 0;
                    long alarmTime = currentSchedule.getTime();
                    if (currentSchedule.getAlarm() != null) {
                        ringNum = currentSchedule.getAlarm().getRingNum();
                        snoozeTime = currentSchedule.getAlarm().getSnoonzeItem();
                        garbSwitch = currentSchedule.getAlarm().getmGarbSwitch();
                        if (garbSwitch == 1) {
                            alarmTime = GarbAlarmUtils.getGarbAlarmTime(currentSchedule.getAlarm()).getTimeInMillis();
                        }
                    }
                    Triple<Boolean, Integer, Integer> data = new Triple<>(garbSwitch == 1, snoozeTime, ringNum);
                    PlatformUtils.getInstance().clockAlarmRing(AlarmClockApplication.getInstance(),
                            PlatformUtils.getInstance().getPlatformClockInfo(currentSchedule.getId(), alarmLabel,
                                    alarmTime, currentSchedule.isSnoozeAvailble(ringNum),
                                    data), PlatformUtils.SEND_ALL);
                }
            } catch (Exception e) {
                Log.e(TAG, " getCurrentAlertAlarmInfo e : " + e.getMessage());
            }
        }

        @Override
        public PlatformClockInfo getCurrentAlarm() throws RemoteException {
            AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
            if (currentSchedule != null) {
                String alarmLabel = currentSchedule.getAlarmLabel();
                int ringNum = ClockConstant.SNOOZE_RING_NUM;
                int snoozeTime = ClockConstant.SNOOZE_AFTER_MIN;
                int garbSwitch = 0;
                long alarmTime = currentSchedule.getTime();
                if (currentSchedule.getAlarm() != null) {
                    ringNum = currentSchedule.getAlarm().getRingNum();
                    snoozeTime = currentSchedule.getAlarm().getSnoonzeItem();
                    garbSwitch = currentSchedule.getAlarm().getmGarbSwitch();
                    if (garbSwitch == 1) {
                        alarmTime = GarbAlarmUtils.getGarbAlarmTime(currentSchedule.getAlarm()).getTimeInMillis();
                    }
                }
                Triple<Boolean, Integer, Integer> data = new Triple<>(garbSwitch == 1, snoozeTime, ringNum);
                PlatformClockInfo platformClockInfo = PlatformUtils.getInstance().getPlatformClockInfo(currentSchedule.getId(), alarmLabel,
                        alarmTime, currentSchedule.isSnoozeAvailble(ringNum),
                        data);
                return platformClockInfo;
            }
            return null;
        }
    }
}