/************************************************************
 * Copyright 2016 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :PlatformUtils
 * Tool class for health app and clock interaction
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.oplus.alarmclock.aidl;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.coloros.alarmclock.IClockUpdateAidlInterface;
import com.coloros.alarmclock.PlatformClockInfo;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.clock.common.utils.Log;

import java.util.Locale;

import kotlin.Triple;

public class PlatformUtils {
    public static final int SEND_HEARTH = 1;
    public static final int SEND_DEEP_THINKER = 2;
    public static final int SEND_ALL = 3;
    public static final int NOTIFY_ACTION_DELETE = 0;
    public static final int NOTIFY_ACTION_UPDATE = 1;
    public static final int NOTIFY_ACTION_DISABLE = 2;
    public static final int UPDATE_TYPE_DEFAULT = 0;//默认无意义
    public static final int UPDATE_TYPE_CLOCK_CLOSE = 1;//用户点击关闭
    public static final int UPDATE_TYPE_CLOCK_SNOOZE = 2;//用户点击稍后提醒
    public static final int UPDATE_TYPE_OPERATION_OPEN_ALARM = 3;//用户操作开启闹钟
    public static final int UPDATE_TYPE_OPERATION_CLOSE_ALARM = 4;//用户操作关闭闹钟
    public static final int UPDATE_TYPE_OPERATION_NEW_ALARM = 5;//用户操作新建闹钟
    public static final int UPDATE_TYPE_OPERATION_UPDATE_ALARM = 6;//用户操作更改闹钟
    public static final int UPDATE_TYPE_IOT = 7;//IOT主动调用notifyDataChange
    private static final int STATUS_ALARM_RING = 1;
    private static final int STATUS_ALARM_DISMISS = 2;
    private static final int STATUS_ALARM_SNOOZE = 3;
    private static final int MAX_AUTO_BIND_NUM = 2;
    private static final String TAG = "PlatformUtils";
    private static final String PLATFORM_CLOCK_BROADCAST_AWAKEN_CLOCK_ACTION = "com.coloros.platformalarmclock.platform.awaken_clock_action";
    private static final String PLATFORM_CLOCK_BROADCAST_PATH = "com.coloros.platformalarmclock.PlatformClockBroadcastReceiver";
    private static final String PLATFORM_CLOCK_BROADCAST_PERMISSION = "com.coloros.alarmclock.permission.ACCESS_CLOCK_RECEIVER_PLATFORM";
    //health APP
    private static final String HEALTH_APP_PKG = "com.heytap.health";
    private static final String HEALTH_EXP_APP_PKH = "com.heytap.health.international";
    /**
     * 起床感知
     */
    private static final String DEEP_THINKER = "com.oplus.deepthinker";
    private static final String IOT_PKG = "com.heytap.smarthome";
    private static final String[] PLATFORM_CLOCK_BROADCAST_AWAKEN_APP_PACKAGE = {HEALTH_APP_PKG, HEALTH_EXP_APP_PKH};
    public static int sUpdateType = UPDATE_TYPE_DEFAULT;
    public static long sIotAlarmId;
    private static PlatformUtils sInstance = new PlatformUtils();
    private IClockUpdateAidlInterface mHealthListener;//健康APP
    private IClockUpdateAidlInterface mSmartHomeListener;//智能家居APP
    /**
     * 起床感知
     */
    private IClockUpdateAidlInterface mDeepThinkerListener;
    private long mScheduleId = -1;
    private PlatformClockInfo mPlatformClockInfo = null;
    private PlatformClockInfo mDeepThinkerPlatformClockInfo = null;
    private long mDeepThinkerScheduleId = -1;
    private int mCurrentStatus = 0;
    private int mDeepThinkerCurrentStatus = 0;
    private int mAutoBindTimes = 0;
    private int mDeepThinkerAutoBindTimes = 0;
    private int mAutoBindTimesSmart = 0;
    private boolean mIsCancelByOtherApp = false;
    private int mIotAction = -1;
    private int mIotEnableAssociate = -1;

    public static PlatformUtils getInstance() {
        return sInstance;
    }

    int getCurrentStatus() {
        return mCurrentStatus;
    }

    int getCurrentIotAction() {
        return mIotAction;
    }

    PlatformClockInfo getPlatformClockInfo() {
        return mPlatformClockInfo;
    }

    public void resetPlatformClockInfoAndStatus() {
        mCurrentStatus = 0;
        mScheduleId = -1;
        mAutoBindTimes = 0;
    }

    public void resetDeepThinkerPlatformClockInfoAndStatus() {
        mDeepThinkerCurrentStatus = 0;
        mDeepThinkerScheduleId = -1;
        mDeepThinkerAutoBindTimes = 0;
    }

    public void resetPlatformClockInfoAndStatusSmart() {
        mAutoBindTimesSmart = 0;
        mIotAction = -1;
        mIotEnableAssociate = -1;
    }

    long getScheduleId() {
        return mScheduleId;
    }

    void setIsCancelByOtherApp() {
        mIsCancelByOtherApp = true;
    }

    void resetIsCancelByOtherApp() {
        mIsCancelByOtherApp = false;
    }


    public void registerListener(IClockUpdateAidlInterface listener) {
        try {
            String channelName = listener.getChannelName();
            Log.i(TAG, "registerListener  listener = " + channelName);
            switch (channelName) {
                case IOT_PKG:
                    mSmartHomeListener = listener;
                    initIot();
                    break;
                case HEALTH_APP_PKG:
                case HEALTH_EXP_APP_PKH:
                    mHealthListener = listener;
                    initSmart();
                    break;
                case DEEP_THINKER:
                    mDeepThinkerListener = listener;
                    initDeepThinker();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "registerListener : " + e.getMessage());
        }
    }

    private void initSmart() {
        try {
            AlarmClockApplication context = AlarmClockApplication.getInstance();
            Log.i(TAG, "invoking after binding clock service successful status==>" + mCurrentStatus);
            if (mCurrentStatus != 0) {
                if (mCurrentStatus == PlatformUtils.STATUS_ALARM_RING) {
                    clockAlarmRing(context, mPlatformClockInfo, SEND_HEARTH);
                } else {
                    if (mScheduleId > 0) {
                        if (mCurrentStatus == PlatformUtils.STATUS_ALARM_DISMISS) {
                            dismissClock(context, mScheduleId, SEND_HEARTH);
                        } else if (mCurrentStatus == PlatformUtils.STATUS_ALARM_SNOOZE) {
                            snoozeClock(context, mScheduleId, SEND_HEARTH);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "bindAlarmClock : " + e.getMessage());
        }
    }

    /**
     * 初始化起床感知
     */
    private void initDeepThinker() {
        try {
            AlarmClockApplication context = AlarmClockApplication.getInstance();
            Log.i(TAG, "invoking after binding clock service successful status==>" + mCurrentStatus);
            if (mDeepThinkerCurrentStatus != 0) {
                if (mDeepThinkerCurrentStatus == PlatformUtils.STATUS_ALARM_RING) {
                    //如有消息未发出，重新发送
                    clockAlarmRing(context, mDeepThinkerPlatformClockInfo, SEND_DEEP_THINKER);
                } else {
                    if (mDeepThinkerScheduleId > 0) {
                        if (mDeepThinkerCurrentStatus == PlatformUtils.STATUS_ALARM_DISMISS) {
                            dismissClock(context, mDeepThinkerScheduleId, SEND_DEEP_THINKER);
                        } else if (mDeepThinkerCurrentStatus == PlatformUtils.STATUS_ALARM_SNOOZE) {
                            snoozeClock(context, mDeepThinkerScheduleId, SEND_DEEP_THINKER);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "bindAlarmClock : " + e.getMessage());
        }
    }

    private void initIot() {
        Log.i(TAG, "initIot  mIotAction：" + mIotAction + ",mIotAlarmId:" + sIotAlarmId);
        if ((mIotAction != -1) && (sIotAlarmId != -1)) {
            Log.i(TAG, "notifyDataChange");
            notifyDataChange();
        }
    }

    public void unregisterListener(String channelName) {
        try {
            Log.e(TAG, "unregisterListener  listener = " + channelName);
            switch (channelName) {
                case IOT_PKG:
                    mSmartHomeListener = null;
                    break;
                case HEALTH_APP_PKG:
                case HEALTH_EXP_APP_PKH:
                    mHealthListener = null;
                    break;
                case DEEP_THINKER:
                    mDeepThinkerListener = null;
                default:
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "registerListener : " + e.getMessage());
        }
    }

    /**
     * 关闭闹钟
     *
     * @param context
     * @param scheduleId
     * @param type
     */
    public void dismissClock(Context context, long scheduleId, int type) {
        ClockOplusCSUtils.statisticsClockClose(context, ClockOplusCSUtils.CLOSE_CLOCK);
        try {
            if ((type == SEND_HEARTH) || (type == SEND_ALL)) {
                if (!isClientListenerIsNull(mHealthListener)) {
                    mHealthListener.dismissClock(scheduleId);
                    Log.i(TAG, "dismissClock channelName = " + mHealthListener.getChannelName());
                    resetPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mHealthListener == null) ? "dismissClock listener is null " : "dismissClock listener.getListenerIsNull(): true");
                    mCurrentStatus = STATUS_ALARM_DISMISS;
                    mScheduleId = scheduleId;
                    sendBroadcastToBindService(context);
                }
            }
            if ((type == SEND_DEEP_THINKER) || (type == SEND_ALL)) {
                //起床感知
                if (!isClientListenerIsNull(mDeepThinkerListener)) {
                    Log.e(TAG, "mDeepThinkerListener NOT NULL");
                    mDeepThinkerListener.dismissClock(scheduleId);
                    resetDeepThinkerPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mDeepThinkerListener == null) ? " mDeepThinkerListener dismissClock listener is null " : "true");
                    mDeepThinkerCurrentStatus = STATUS_ALARM_DISMISS;
                    mDeepThinkerScheduleId = scheduleId;
                    sendDeepThinkerBroadcastToBindService(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "dismissClock : " + e.getMessage());
        }
    }

    /**
     * 稍后提醒
     *
     * @param context
     * @param scheduleId
     * @param type
     */
    public void snoozeClock(Context context, long scheduleId, int type) {
        ClockOplusCSUtils.statisticsClockClose(context, ClockOplusCSUtils.SNOOZE_CLOCK);
        try {
            if ((type == SEND_HEARTH) || (type == SEND_ALL)) {
                if (!isClientListenerIsNull(mHealthListener)) {
                    mHealthListener.snoozeClock(scheduleId);
                    Log.i(TAG, "snoozeClock channelName = " + mHealthListener.getChannelName());
                    resetPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mHealthListener == null) ? "snoozeClock listener is null " : "snoozeClock listener.getListenerIsNull(): true");
                    mCurrentStatus = STATUS_ALARM_SNOOZE;
                    mScheduleId = scheduleId;
                    sendBroadcastToBindService(context);
                }
            }
            if ((type == SEND_DEEP_THINKER) || (type == SEND_ALL)) {
                //起床感知
                if (!isClientListenerIsNull(mDeepThinkerListener)) {
                    mDeepThinkerListener.snoozeClock(scheduleId);
                    resetDeepThinkerPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mDeepThinkerListener == null) ? " listener is null " : "not null");
                    mDeepThinkerCurrentStatus = STATUS_ALARM_SNOOZE;
                    mDeepThinkerScheduleId = scheduleId;
                    sendDeepThinkerBroadcastToBindService(context);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "snoozeClock : " + e.getMessage());
        }
    }

    /**
     * 闹钟响铃
     *
     * @param context
     * @param platformClockInfo
     * @param type
     */
    public void clockAlarmRing(Context context, PlatformClockInfo platformClockInfo, int type) {
        try {
            if ((type == SEND_HEARTH) || (type == SEND_ALL)) {
                if (!isClientListenerIsNull(mHealthListener)) {
                    Log.i(TAG, "invoke clockAlarmRing for health");
                    mHealthListener.alarmClockRing(platformClockInfo);
                    resetPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mHealthListener == null) ? "clockAlarmRing listener is null " : " not null");
                    mPlatformClockInfo = platformClockInfo;
                    mCurrentStatus = STATUS_ALARM_RING;
                    sendBroadcastToBindService(context);
                }
            }

            if ((type == SEND_DEEP_THINKER) || (type == SEND_ALL)) {
                //起床感知
                if (!isClientListenerIsNull(mDeepThinkerListener)) {
                    Log.i(TAG, "invoke clockAlarmRing for DeepThinker");
                    mDeepThinkerListener.alarmClockRing(platformClockInfo);
                    resetDeepThinkerPlatformClockInfoAndStatus();
                } else {
                    Log.e(TAG, (mDeepThinkerListener == null) ? " mDeepThinkerListener  is null " : "not null");
                    mDeepThinkerPlatformClockInfo = platformClockInfo;
                    mDeepThinkerCurrentStatus = STATUS_ALARM_RING;
                    sendDeepThinkerBroadcastToBindService(context);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "clockAlarmRing error ");
        }
    }

    /**
     * check client listener is of not null
     * if it is null, need to rebind our app
     *
     * @param listener
     * @return
     */
    private boolean isClientListenerIsNull(IClockUpdateAidlInterface listener) {
        if (listener == null) {
            Log.e(TAG, "isClientListenerIsNull listener is null ");
        } else {
            try {
                return listener.getListenerIsNull();
            } catch (Exception e) {
                Log.e(TAG, "getListenerIsNull");
            }
        }
        return true;
    }

    public void notifyDataChange() {
        notifyDataChange(mIotAction, mIotEnableAssociate, sIotAlarmId);
    }

    public void notifyDataChange(int action, int enableAssociate, long alarmId) {
        try {
            Log.i(TAG, "action:" + action + ",enableAssociate:" + enableAssociate + ",alarmId:" + alarmId);
            sIotAlarmId = alarmId;
            mIotAction = action;
            mIotEnableAssociate = enableAssociate;
            Context context = AlarmClockApplication.getInstance();
            if (IotUtils.isNewVersion(context)) {
                IotUtils.notifyDataChange(context, action, enableAssociate, alarmId);
                Log.i(TAG, "invoke notifyDataChange by broadcast");
                resetPlatformClockInfoAndStatusSmart();
            } else {
                if (!isClientListenerIsNull(mSmartHomeListener)) {
                    mSmartHomeListener.onDataChanged(action, enableAssociate, alarmId);
                    Log.i(TAG, "invoke notifyDataChange channelName = " + mSmartHomeListener.getChannelName());
                    resetPlatformClockInfoAndStatusSmart();
                } else {
                    Log.i(TAG, "notifyDataChange sendBroadcastToIOTBindService");
                    sendBroadcastToIOTBindService(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "notifyDataChange : " + e.getMessage());
        }
    }

    public PlatformClockInfo getPlatformClockInfo(long scheduleId, String tagName, long alarmTime,
                                                  boolean delayReminder, Triple<Boolean, Integer, Integer> alarmInfo) {

        PlatformClockInfo platformClockInfo = new PlatformClockInfo();
        platformClockInfo.setScheduleId(scheduleId);
        platformClockInfo.setTagName(tagName);
        platformClockInfo.setAlarmTime(alarmTime);
        platformClockInfo.setDelayReminder(delayReminder);
        platformClockInfo.setLanguage(Locale.getDefault().getLanguage());
        platformClockInfo.setmRingNum(alarmInfo.getThird());
        platformClockInfo.setmSnoozeTime(alarmInfo.getSecond());
        platformClockInfo.setmIsGarbAlarm(alarmInfo.getFirst());
        Log.i(TAG, "clockAlarmRing  platformClockInfo = " + platformClockInfo.toString());
        return platformClockInfo;
    }

    private void handleSendBroadcast(Context context, String pkg) {
        try {
            Intent intent = new Intent(PlatformUtils.PLATFORM_CLOCK_BROADCAST_AWAKEN_CLOCK_ACTION);
            intent.setComponent(new ComponentName(pkg, PlatformUtils.PLATFORM_CLOCK_BROADCAST_PATH));
            intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.sendBroadcast(intent, PLATFORM_CLOCK_BROADCAST_PERMISSION);
            Log.i(TAG, "handleSendBroadcast  to ask other app bind our app : getAction =" + intent.getAction() + " packageName = " + pkg);
        } catch (Exception e) {
            Log.e(TAG, "handleSendBroadcast to ask other app bind our app : " + e.getMessage());
        }
    }

    private void sendBroadcastToBindService(Context context) {
        Log.i(TAG, "sendBroadcastToBindService sendBroadcastToBindService :" + mAutoBindTimes);
        if ((context != null) && (mAutoBindTimes <= MAX_AUTO_BIND_NUM) && !mIsCancelByOtherApp) {
            ++mAutoBindTimes;
            handleSendBroadcastToBindService(context);
        } else if (mAutoBindTimes > MAX_AUTO_BIND_NUM) {
            Log.e(TAG, "sendBroadcastToBindService mAutoBindTimes > MAX_AUTO_BIND_NUM mAutoBindTimes = " + mAutoBindTimes);
            resetPlatformClockInfoAndStatus();
        } else {
            Log.i(TAG, "sendBroadcastToBindService other ");
        }
    }

    public void handleSendBroadcastToBindService(Context context) {
        for (String packageName : PLATFORM_CLOCK_BROADCAST_AWAKEN_APP_PACKAGE) {
            try {
                handleSendBroadcast(context, packageName);
            } catch (Exception e) {
                Log.e(TAG, "sendBroadcastToBindService to ask other app bind our app : " + e.getMessage());
            }
        }
    }

    /**
     * 绑定起床感知
     *
     * @param context
     */
    private void sendDeepThinkerBroadcastToBindService(Context context) {
        Log.i(TAG, "sendDeepThinkerBroadcastToBindService sendBroadcastToBindService :" + mAutoBindTimes);
        if ((context != null) && (mDeepThinkerAutoBindTimes <= MAX_AUTO_BIND_NUM) && !mIsCancelByOtherApp) {
            ++mDeepThinkerAutoBindTimes;
            handleSendDeepThinkerBroadcast(context);
        } else if (mDeepThinkerAutoBindTimes > MAX_AUTO_BIND_NUM) {
            Log.e(TAG, "sendDeepThinkerBroadcastToBindService mAutoBindTimes > MAX_AUTO_BIND_NUM mAutoBindTimes = " + mAutoBindTimes);
            resetDeepThinkerPlatformClockInfoAndStatus();
        } else {
            Log.i(TAG, "sendDeepThinkerBroadcastToBindService other ");
        }
    }

    /**
     * 发送起床感知
     *
     * @param context
     */
    private void handleSendDeepThinkerBroadcast(Context context) {
        try {
            //发送起床感知
            handleSendBroadcast(context, DEEP_THINKER);
        } catch (Exception e) {
            Log.e(TAG, "handleSendBroadcast");
        }
    }

    public void handleClockRingFoHealth() {
        AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
        if (currentSchedule != null) {
            Log.i(TAG, "start alarm handleClockRingFoHealth");
            int ringNum = ClockConstant.SNOOZE_RING_NUM;
            int snoozeTime = ClockConstant.SNOOZE_AFTER_MIN;
            int garbSwitch = 0;
            long alarmTime = currentSchedule.getTime();
            if (currentSchedule.getAlarm() != null) {
                ringNum = currentSchedule.getAlarm().getRingNum();
                snoozeTime = currentSchedule.getAlarm().getSnoonzeItem();
                garbSwitch = currentSchedule.getAlarm().getmGarbSwitch();
                if (garbSwitch == 1) {
                    alarmTime = GarbAlarmUtils.getGarbAlarmTime(currentSchedule.getAlarm()).getTimeInMillis();
                }
            }
            Triple<Boolean, Integer, Integer> data = new Triple<>(garbSwitch == 1, snoozeTime, ringNum);
            clockAlarmRing(AlarmClockApplication.getInstance(), PlatformUtils.getInstance().getPlatformClockInfo(currentSchedule.getId(),
                    currentSchedule.getAlarmLabel(), alarmTime,
                    currentSchedule.isSnoozeAvailble(ringNum),
                    data), SEND_ALL);
        }
    }

    private void sendBroadcastToIOTBindService(Context context) {
        Log.i(TAG, "start sendBroadcastToIOTBindService");
        if ((context != null) && (mAutoBindTimesSmart <= MAX_AUTO_BIND_NUM) && !mIsCancelByOtherApp) {
            ++mAutoBindTimesSmart;
            try {
                handleSendBroadcastToIOTBindService(context);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (mAutoBindTimesSmart > MAX_AUTO_BIND_NUM) {
            resetPlatformClockInfoAndStatusSmart();
        } else {
            Log.i(TAG, "sendBroadcastToIOTBindService other ");
        }
    }


    public void handleSendBroadcastToIOTBindService(Context context) {
        handleSendBroadcast(context, IOT_PKG);
    }


    public boolean filterPackageName(String channel, String pkg) {
        return TextUtils.equals(channel, pkg);
    }
}