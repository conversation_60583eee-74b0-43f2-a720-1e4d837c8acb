/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockIotReceiver.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.aidl

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.oplus.alarmclock.alarmclock.AlarmUtils
import com.oplus.alarmclock.alarmclock.IOTUtil
import com.oplus.clock.common.utils.Log

class ClockIotReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "ClockIotReceiver"
        private const val DATA_BUNDLE = "clock.intent.key.bundle"
        private const val DATA_TYPE = "clock.intent.key.type"
        private const val TYPE_UPDATE_ALARM = 10
        private const val ENABLE_YES_ARR = "clock.intent.key.associate.scene.true.id.array"
        private const val ENABLE_NO_ARR = "clock.intent.key.associate.scene.false.id.array"
    }

    @SuppressLint("UnsafeBroadcastReceiverActionDetector")
    override fun onReceive(context: Context?, intent: Intent?) {
        intent?.run {
            val type = getIntExtra(DATA_TYPE, 0)
            Log.d(TAG, "type：$type")
            if (type == TYPE_UPDATE_ALARM) {
                getBundleExtra(DATA_BUNDLE)?.also {
                    it.getLongArray(ENABLE_YES_ARR)?.forEach { id ->
                        Log.d(TAG, "alarm true -> id: $id")
                        AlarmUtils.updateIOTEnabled(IOTUtil.ENABLE_ASSOCIATE, id)
                    }
                    it.getLongArray(ENABLE_NO_ARR)?.forEach { id ->
                        Log.d(TAG, "alarm false -> id: $id")
                        AlarmUtils.updateIOTEnabled(IOTUtil.DISABLE_ASSOCIATE, id)
                    }
                } ?: Log.d(TAG, "bundle is null")
            } else {
                Log.d(TAG, "unknown type:$type")
            }
        } ?: Log.d(TAG, "intent is null")
    }
}