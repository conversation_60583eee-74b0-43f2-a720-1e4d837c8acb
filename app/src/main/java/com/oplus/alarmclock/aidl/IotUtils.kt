/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IotUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.aidl

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.os.Build
import android.util.Log

object IotUtils {
    private const val TAG = "IotUtils"
    private const val EXTRA_ID = "clock.intent.key.id"
    private const val EXTRA_ACTION = "clock.intent.key.type"
    private const val EXTRA_ENABLE = "clock.intent.key.enable"
    private const val PERMISSION = "com.oppo.permission.safe.IOT"
    private const val IOT_PKG = "com.heytap.smarthome"
    private const val IOT_PKG_OLD = "com.oppo.ohome"
    private const val IOT_RECEIVER = "scene.ui.condition.timing.clock.SystemClockAppReceiver"
    private const val RECEIVER = "$IOT_PKG.$IOT_RECEIVER"
    private const val VERSION_ERR = -1
    private const val IOT_VERSION = 20400

    @JvmStatic
    fun isNewVersion(context: Context): Boolean {
        val version = getIotVersion(context)
        Log.d(TAG, "version: $version")
        return version >= IOT_VERSION
    }

    @JvmStatic
    fun notifyDataChange(context: Context, action: Int, enableAssociate: Int, alarmId: Long) {
        sendBroadcast(context, action, enableAssociate, alarmId)
    }

    @JvmStatic
    private fun sendBroadcast(context: Context, action: Int, enableAssociate: Int, alarmId: Long) {
        val intent = Intent()
        val iotPackage =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) IOT_PKG else IOT_PKG_OLD
        intent.component = ComponentName(iotPackage, RECEIVER)
        intent.putExtra(EXTRA_ID, alarmId)
        intent.putExtra(EXTRA_ACTION, action)
        intent.putExtra(EXTRA_ENABLE, enableAssociate)
        context.sendBroadcast(intent, PERMISSION)
    }

    @JvmStatic
    private fun getIotVersion(context: Context): Int {
        val versionNow = getVersion(context, IOT_PKG)
        return if (versionNow != VERSION_ERR) {
            versionNow
        } else {
            getVersion(context, IOT_PKG_OLD)
        }
    }

    @JvmStatic
    @Suppress("DEPRECATION")
    private fun getVersion(context: Context, packageName: String): Int {
        return context.packageManager.run {
            try {
                getPackageInfo(packageName, PackageManager.GET_CONFIGURATIONS).versionCode
            } catch (e: NameNotFoundException) {
                Log.d(TAG, "getVersion $packageName e: $e")
                return VERSION_ERR
            }
        }
    }
}