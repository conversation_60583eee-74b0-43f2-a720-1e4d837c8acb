package com.oplus.alarmclock.ai;

import android.annotation.SuppressLint;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.content.pm.SigningInfo;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.clock.common.utils.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

public class DataBackContentProvider extends ContentProvider {
    private static final String METHOD_ADD_ALARM = "add_alarm";
    private static final String TAG = "DataTransferContentProvider";
    private static final String PROVIDER_PERMISSION = "com.oplus.alarmclock.permission.DATA_BACKUP_SAFE";

    private static final String CALL_PACKAGE = "com.pangu.itools";
    private static final String CALL_SHA1_SIGNATURE = "79 3A 36 45 CB 68 EE AD E1 48 0C 39 65 58 B4 55 1D 5F 63 AD";

    private static final String EXTRA_ALARM_LIMIT = "alarm_limit";
    private static final String EXTRA_ALARM_ID_LIST = "alarm_id_list";
    private static final String EXTRA_ALARM_HOUR_LIST = "alarm_hour_list";
    private static final String EXTRA_ALARM_MIN_LIST = "alarm_min_list";
    private static final String EXTRA_ALARM_LABEL_LIST = "alarm_label_list";
    private static final String EXTRA_ALARM_STATE_LIST = "alarm_state_list";
    private static final String EXTRA_ALARM_REPEAT_LIST = "alarm_repeat_list";
    private static final String EXTRA_ALARM_ENABLE_ASSOCIATE_LIST = "alarm_enableAssociate_list";
    private static final String EXTRA_ALARM_TIME_LIST = "alarm_time_list";
    private static final String EXTRA_WORKDAY_SWITCH_LIST = "workday_switch_list";
    private static final String EXTRA_HOLIDAY_SWITCH_LIST = "holiday_switch_list";
    private static final String EXTRA_REPEAT_SET_LIST = "alarm_repeat_set_list";
    private static final String EXTRA_ALARM_SNOONZE_ITEM_LIST = "alarm_snoonze_items_list";
    private static final String EXTRA_ALARM_SNOONZE_TIME_LIST = "alarm_snoonze_times_list";
    private static final String EXTRA_ALARM_UUID_LIST = "alarm_uuid_list";
    private static final String EXTRA_UPDATE_TYPE = "alarm_update_type";

    private static final String EXTRA_RESULT = "result";
    private static final int RESULT_SUCCESS = 1;
    private static final int RESULT_ERROR = -1;
    private static final int RESULT_ERROR_COUNT_LIMIT = -2;
    private final String SHA_1 = "SHA-1";
    private Context mContext;

    @Override
    public boolean onCreate() {
        mContext = this.getContext();
        return true;
    }

    @Nullable
    @Override
    public Bundle call(@NonNull String method, @Nullable String arg, @Nullable Bundle extras) {
        Log.d(TAG, "call:" + method + " callPackage:" + getCallingPackage());
        Bundle result = new Bundle();
        if (!matchDataCaller()) {
            return result;
        }
        if (mContext.checkCallingPermission(PROVIDER_PERMISSION) != PackageManager.PERMISSION_GRANTED) {
            return result;
        }

        if (method.equals(METHOD_ADD_ALARM)) {
            Log.d(TAG, "METHOD_ADD_ALARM");
            if (AiAlarmUtils.getAlarmsCount(mContext) >= AlarmClockFragment.MAX_ALARM_COUNT) {
                result.putInt(EXTRA_ALARM_LIMIT, AlarmClockFragment.MAX_ALARM_COUNT);
                result.putInt(EXTRA_RESULT, RESULT_ERROR_COUNT_LIMIT);
                Log.d(TAG, "METHOD_ADD_ALARM RESULT_ERROR_COUNT_LIMIT");
            } else {
                Alarm alarm = AiAlarmUtils.handleSetAlarm(mContext, extras);
                boolean success = alarm != null;
                if (success) {
                    AlarmPreferenceUtils.Companion.getInstance().addAlarmInfo(System.currentTimeMillis(), alarm);
                }
                Log.d(TAG, "METHOD_ADD_ALARM RESULT" + success);
                result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS
                        : RESULT_ERROR);
                putExtrasIntoResultBundle(result, alarm);
            }
        }
        Log.d(TAG, "call: " + method + ", extras: " + extras + ", result: " + result);
        return result;
    }

    private void putExtrasIntoResultBundle(Bundle result, Alarm alarm) {
        if ((result != null) && (alarm != null)) {
            List<Alarm> list = new ArrayList<>();
            list.add(alarm);
            putExtrasIntoResultBundle(result, list);
        }
    }

    private void putExtrasIntoResultBundle(Bundle result, List<Alarm> list) {
        if ((result != null) && (list != null) && !list.isEmpty()) {
            final int size = list.size();
            int[] hours = new int[size];
            int[] mins = new int[size];
            String[] labels = new String[size];
            boolean[] states = new boolean[size];
            long[] ids = new long[size];
            String[] repeat = new String[size];
            int[] enableAssociates = new int[size];
            long[] times = new long[size];
            int[] workday = new int[size];
            int[] holiday = new int[size];
            int[] repeatSets = new int[size];
            int[] snoonzeItems = new int[size];
            int[] snoonzeTimes = new int[size];
            int[] updateTypes = new int[size];
            String[] uuids = new String[size];

            fillAlarmsInfo(list, ids, hours, mins, labels, states, repeat, enableAssociates, times,
                    workday, holiday, repeatSets, snoonzeItems, snoonzeTimes, uuids, updateTypes);
            result.putLongArray(EXTRA_ALARM_ID_LIST, ids);
            result.putIntArray(EXTRA_ALARM_HOUR_LIST, hours);
            result.putIntArray(EXTRA_ALARM_MIN_LIST, mins);
            result.putStringArray(EXTRA_ALARM_LABEL_LIST, labels);
            result.putBooleanArray(EXTRA_ALARM_STATE_LIST, states);
            result.putStringArray(EXTRA_ALARM_REPEAT_LIST, repeat);
            result.putIntArray(EXTRA_ALARM_ENABLE_ASSOCIATE_LIST, enableAssociates);
            result.putLongArray(EXTRA_ALARM_TIME_LIST, times);
            result.putIntArray(EXTRA_WORKDAY_SWITCH_LIST, workday);
            result.putIntArray(EXTRA_HOLIDAY_SWITCH_LIST, holiday);
            result.putIntArray(EXTRA_REPEAT_SET_LIST, repeatSets);
            result.putIntArray(EXTRA_ALARM_SNOONZE_ITEM_LIST, snoonzeItems);
            result.putIntArray(EXTRA_ALARM_SNOONZE_TIME_LIST, snoonzeTimes);
            result.putStringArray(EXTRA_ALARM_UUID_LIST, uuids);
            result.putIntArray(EXTRA_UPDATE_TYPE, updateTypes);
            Log.d(TAG, "putExtrasIntoResultBundle updateType: " + PlatformUtils.sUpdateType);
        }
    }

    private void fillAlarmsInfo(List<Alarm> list, long[] ids, int[] hours, int[] mins, String[] labels,
                                boolean[] states, String[] repeats, int[] enableAssociates, long[] time, int[] workday, int[] holiday,
                                int[] repeatSets, int[] snoonzeItems, int[] snoonzeTimes, String[] uuids, int[] updateTypes) {
        final int size = list.size();
        for (int i = 0; i < size; i++) {
            Alarm alarm = list.get(i);
            long id = alarm.getId();
            ids[i] = id;
            hours[i] = alarm.getHour();
            mins[i] = alarm.getMinutes();
            labels[i] = alarm.getLabel();
            states[i] = alarm.isEnabled();
            enableAssociates[i] = alarm.getEnableAssociate();
            workday[i] = alarm.getWorkdaySwitch();
            holiday[i] = alarm.getHolidaySwitch();
            int repeatSet = alarm.getRepeatSet();
            uuids[i] = alarm.getUUID();
            repeats[i] = RepeatSet.getDescription(mContext, repeatSet, alarm.getWorkdaySwitch(), alarm.getHolidaySwitch(), true, alarm, false);
            repeatSets[i] = repeatSet;
            snoonzeItems[i] = alarm.getSnoonzeItem();
            AlarmSchedule schedule = AiAlarmUtils.getAlarmSchedule(alarm);
            if (schedule != null) {
                time[i] = schedule.getTime();
                snoonzeTimes[i] = schedule.getSnoonzeTime();
            } else {
                time[i] = AlarmUtils.getAlarmNextTime(alarm, null);
                snoonzeTimes[i] = 0;
            }
            Log.d(TAG, "id:" + id + ",mIotAlarmId:" + PlatformUtils.sIotAlarmId + ",updateType:" + PlatformUtils.sUpdateType);
            updateTypes[i] = (id == PlatformUtils.sIotAlarmId) ? PlatformUtils.sUpdateType : PlatformUtils.UPDATE_TYPE_DEFAULT;
        }
    }

    /**
     * 鉴权调用者
     */
    private boolean matchDataCaller() {
        if (CALL_PACKAGE.equals(getCallingPackage())) {
            List<String> signatureList = getRawSignature(getCallingPackage());
            Log.d(TAG, "signatureList " + signatureList);
            if (signatureList != null && !signatureList.isEmpty()) {
                for (String signStr : signatureList) {
                    Log.d(TAG, "signStr:" + signStr);
                    if (!TextUtils.isEmpty(signStr)) {
                        if (signStr.equals(CALL_SHA1_SIGNATURE)) {
                            return true;
                        }
                    }
                }
            }

        } else {
            Log.d(TAG, "getCallingPackage error " + getCallingPackage());
        }
        return false;
    }

    /**
     * 通过包名获取签名信息
     *
     * @param packageName 包名
     * @return 返回sha1签名信息
     */
    private List<String> getRawSignature(String packageName) {
        if ((packageName) == null || (packageName.length() == 0)) {
            Log.e(TAG, "packageName is null or length error");
            return null;
        }
        List<String> signatureList = new ArrayList<>();
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {

                SigningInfo signingInfo = mContext.getPackageManager().getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES).signingInfo;
                if (signingInfo.hasMultipleSigners()) {
                    Signature[] signatures = signingInfo.getApkContentsSigners();
                    for (Signature signature : signatures) {
                        @SuppressLint("UnsafeHashAlgorithmDetector") MessageDigest digest = MessageDigest.getInstance(SHA_1);
                        digest.update(signature.toByteArray());
                        signatureList.add(bytesToHex(digest.digest()));
                    }
                } else {
                    Signature[] signatures = signingInfo.getSigningCertificateHistory();
                    for (Signature signature : signatures) {
                        @SuppressLint("UnsafeHashAlgorithmDetector") MessageDigest digest = MessageDigest.getInstance(SHA_1);
                        digest.update(signature.toByteArray());
                        signatureList.add(bytesToHex(digest.digest()));
                    }
                }

            } else {

                Signature[] signatures = mContext.getPackageManager().getPackageInfo(packageName, PackageManager.GET_SIGNATURES).signatures;
                for (Signature signature : signatures) {
                    @SuppressLint("UnsafeHashAlgorithmDetector") MessageDigest digest = MessageDigest.getInstance(SHA_1);
                    digest.update(signature.toByteArray());
                    signatureList.add(bytesToHex(digest.digest()));
                }
            }
            return signatureList;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "PackageNameNotFound Error " + e.getMessage());
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "NoSuchAlgorithm Error " + e.getMessage());
        }
        return null;
    }

    /**
     * 将sha1转换字符串
     */
    private static String bytesToHex(byte[] bytes) {
        final StringBuilder builder = new StringBuilder();
        for (byte aByte : bytes) {
            builder.append(String.format("%02x", aByte)).append(" ");
        }
        return builder.toString().trim().toUpperCase();
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection, @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        return null;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }
}