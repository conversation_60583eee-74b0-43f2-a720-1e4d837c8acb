/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.04.10   liukun build
 *******************************************************/
// OPLUS Java File Skip Rule:ParameterNumber
package com.oplus.alarmclock.ai;

import static android.provider.AlarmClock.EXTRA_DAYS;
import static android.provider.AlarmClock.EXTRA_RINGTONE;
import static com.oplus.alarmclock.utils.AlarmRingUtils.getRingNameFromOld;

import android.content.Context;
import android.media.AudioManager;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.alert.AlarmAlertUtilsKt;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FbeRingUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class AiAlarmUtils {

    private static final String TAG = "AiAlarmUtils";

    public static final String EXTRA_LABEL = "label";

    private static final String PREFS_SET_RINGTONE = "set_alram_ringtone";
    private static final String PREFS_SET_RING_NAME = "set_alram_ring_name";

    private static final int[] WEEK_ADD_NUM = {1, 2, 4, 8, 16, 32, 64, 128};
    private static final int MAX_HOUR = 24;
    private static final int MAX_MINUTES = 60;

    public static int getAlarmsCount(Context context) {
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        return ((list != null) ? list.size() : 0);
    }

    public static Alarm handleSetAlarm(Context context, Bundle bundle, boolean isAlarms) {
        Log.d(TAG, "bundle: " + bundle);
        int hour = -1;
        int minutes = -1;
        byte dayOfWeek = 0;
        String message = "";
        String alert = null;
        int isDelete = -1;
        ArrayList<Integer> days = null;
        int workdaySwitch = 0;
        int alarmSwitch = 1;
        long specialDay = 0;
        if (bundle == null) {
            return null;
        } else {
            hour = bundle.getInt(AlarmClock.EXTRA_HOUR, -1);
            minutes = bundle.getInt(AlarmClock.EXTRA_MINUTES, 0);
            dayOfWeek = bundle.getByte(AlarmClock.EXTRA_DAYS, (byte) -1);
            message = bundle.getString(EXTRA_LABEL);
            alert = bundle.getString(EXTRA_RINGTONE);
            isDelete = bundle.getInt(AlarmClock.EXTRA_DELETE_AFTER_USE, 0);
            days = bundle.getIntegerArrayList(EXTRA_DAYS);
            workdaySwitch = bundle.getInt(AlarmClock.EXTRA_WORKDAY_SWITCH, 0);
            alarmSwitch = bundle.getInt(AlarmClock.EXTRA_ALARM_ENABLE, 1);
            specialDay = bundle.getLong(ClockContract.Alarm.SPECIAL_ALARM_DAYS);
        }
        if ((hour >= MAX_HOUR) || (minutes >= MAX_MINUTES)) {
            return null;
        }
        if (TextUtils.isEmpty(message)) {
            message = context.getString(R.string.default_label);
        }

        int alertType = 1; // vibrate
        if (!android.provider.AlarmClock.VALUE_RINGTONE_SILENT.equals(alert)) {
            alertType = 2; // vibrate & ring
        }
        Log.d(TAG, "hour: " + hour + " minutes: " + minutes + " message: " + message + " alert: "
                + alert + " dayOfWeek: " + dayOfWeek + "alarmSwitch:" + alarmSwitch);

        int repeatSet = dayOfWeek;
        if (dayOfWeek == (byte) -1) {
            repeatSet = getDaysFromIntent(days);
        }

        // isDelete = 1 : delete alarm in alarm list after use
        // isDelete = 0 : don't delete alarm after use
        if (isDelete == -1) {
            isDelete = RepeatSet.isRepeat(repeatSet) ? 0 : 1;
        }

        return setAlarmFromIntent(context, repeatSet, hour, minutes, message, alert, alertType,
                isDelete, workdaySwitch, alarmSwitch == 1, isAlarms, specialDay);
    }

    public static Alarm handleSetAlarm(Context context, Bundle bundle) {
        return handleSetAlarm(context, bundle, false);
    }

    private static Alarm setAlarmFromIntent(Context context, int repeatSet,
                                            int hour, int minutes, String message, String alert, int alertType, int isDelete,
                                            int workdaySwitch, boolean alarmEnable, boolean isAlarms, long special) {
        String defaultRingUri = alert;
        if (TextUtils.isEmpty(defaultRingUri)) {
            //获取默认铃声地址
            Uri uri = AlarmRingUtils.getDefaultRingtoneUri(context, true);
            if (uri != null) {
                defaultRingUri = uri.toString();
            } else {
                defaultRingUri = "";
            }
        }

        Log.i(TAG, "setAlarmFromIntent  alert = " + defaultRingUri);
        String defaultRing = PrefUtils.getString(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, PREFS_SET_RING_NAME, null);
        if (TextUtils.isEmpty(defaultRing) && StatementDialogUtils.Companion.isMediaAgree()) {
            RingtoneManager ringtoneManager = new RingtoneManager(context);
            ringtoneManager.setType(RingtoneManager.TYPE_ALARM);
            int index = ringtoneManager.getRingtonePosition(Uri.parse(defaultRingUri));
            if (index >= 0) {
                Ringtone ringtone = ringtoneManager.getRingtone(index);
                if (ringtone != null) {
                    String title = ringtone.getTitle(context);
                    Log.d(TAG, "title = " + title);
                    if (!TextUtils.isEmpty(title)) {
                        defaultRing = getRingNameFromOld(context, title);
                    }
                }
            }
        }
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        int volume = audioManager.getStreamVolume(AudioManager.STREAM_ALARM);
        Alarm alarm = Alarm.build(alarmEnable, hour, minutes, repeatSet, alertType, message,
                Uri.parse(defaultRingUri), defaultRing, volume, isDelete,
                AlarmRingUtils.getDefaultVibrate(context), workdaySwitch, 0);
        if (special != 0) {
            alarm.setmSpecialAlarmDays(DatePickerUtils.SPLIT + special + DatePickerUtils.SPLIT);
        }
        if (!isAlarms) {
            AlarmUtils.addNewAlarm(context, alarm, alarmEnable);
            AlarmUtils.updateAlarmNextTime(context);
        }
        Log.d(TAG, "setupAlarmInstance instance = " + alarm);
        return alarm;
    }

    public static int getDaysFromIntent(final ArrayList<Integer> days) {
        int repeatSet = 0;
        if ((days != null) && (!days.isEmpty())) {
            final int[] daysArray = new int[days.size()];
            int sum = 0;
            for (int i = 0; i < days.size(); i++) {
                daysArray[i] = days.get(i);
                Log.d(TAG, "" + i + ": " + daysArray[i]);
                if (daysArray[i] - 2 >= 0) {
                    sum = sum + WEEK_ADD_NUM[daysArray[i] - 2];
                } else {
                    sum = sum + WEEK_ADD_NUM[daysArray[i] + 5];
                }
            }
            repeatSet = sum;
        }
        Log.d(TAG, "repeatSet = " + repeatSet);
        return repeatSet;
    }


    static List<Alarm> getAlarms(Context context, int hour, int minute) {
        ArrayList<Alarm> result = new ArrayList<>();
        ArrayList<Alarm> list = AlarmUtils.getAllAlarms(context);
        if ((list != null) && (!list.isEmpty())) {
            for (Alarm a : list) {
                if ((a.getHour() == hour) && (a.getMinutes() == minute)) {
                    result.add(a);
                }
            }
        }

        return result;
    }

    static boolean disableAllAlarms(Context context) {
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        if ((list != null) && !list.isEmpty()) {
            Alarm preAlarm = null;
            for (Alarm a : list) {
                if (a.isEnabled()) {
                    try {
                        preAlarm = a.clone();
                    } catch (CloneNotSupportedException e) {
                        e.printStackTrace();
                    }
                    boolean success = AlarmUtils.disableAlarm(context, a.getId(), true);
                    if (success) {
                        AlarmRingOperateUtils.closeAlarm(a, AlarmRingOperateUtils.CLOSE_ALARM_LIST);
                        AlarmPreferenceUtils.Companion.getInstance()
                                .editAlarmInfo(System.currentTimeMillis(), preAlarm, a, false);
                    }
                }
            }
        }
        FbeRingUtils.deleteAllInternalRingFile(context);
        AlarmUtils.updateAlarmNextTime(context);
        return true;
    }

    public static boolean delAllAlarms(Context context) {
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        if ((list != null) && !list.isEmpty()) {
            for (Alarm alarm : list) {
                AlarmUtils.deleteAlarm(context, alarm.getId(), true);
                AlarmPreferenceUtils.Companion.getInstance().deleteAlarmInfo(System.currentTimeMillis(), alarm);
            }
            PrefUtils.removeNotification(context,null,null,false,true);
        }
        FbeRingUtils.deleteAllInternalRingFile(context);
        return true;
    }

    static void stopAlarm(Context context, AlarmSchedule alarmSchedule) {
        if (alarmSchedule != null) {
            Log.d(TAG, "stopAlarm: " + alarmSchedule);
            Log.d(TAG, "[dismiss]: cancelNotification: " + alarmSchedule.getId());
            AlarmAlertUtilsKt.alarmDismiss(context);
            AlarmUtils.stopAlarm(context, alarmSchedule.getAlarmId());
            ScheduleUtils.cancelNotification(context, alarmSchedule.getId());
        }
    }

    public static void snoozeAlarm(Context context, long alarmScheduleId) {
        Log.i(TAG, "snoozeAlarm: " + alarmScheduleId);
        AlarmAlertUtilsKt.alarmSnooze(context);
        ScheduleUtils.cancelNotification(context, alarmScheduleId);
    }

    //if 0, it is an invalid value
    public static AlarmSchedule getAlarmSchedule(Alarm alarm) {
        Context context = AlarmClockApplication.getInstance();
        List<AlarmSchedule> alarmScheduleList = ScheduleUtils.getSchedulesOfAlarm(context, alarm);
        if ((alarmScheduleList != null) && (alarmScheduleList.size() > 0) && (alarmScheduleList.get(0) != null)) {
            return alarmScheduleList.get(0);
        }
        return null;
    }

     static List<Alarm> getSectionAlarm(Context context, long startTime, long endTime,
                                       int hour, int min,
                                       int endHour, int endMin) {
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        if ((list == null) || list.isEmpty()) {
            Log.w(TAG, "getSectionAlarm current alarm list is empty");
            return null;
        }

        List<Alarm> result = new ArrayList<>();

        final long startMillis = formatTime(startTime);
        final long endMillis = formatTime(endTime);

        Log.i(TAG, "getSectionAlarm startTime:" + Formatter.formatTime(startTime) + ",endTime:" + Formatter.formatTime(endTime)
                + "alarm interval time=" + hour + ":" + min + "-" + endHour + ":" + endMin);

        list.forEach(alarm -> {
            if (isValid(hour, min) && isValid(endHour, endMin)) {

                if ((compareTime(alarm.getHour(), alarm.getMinutes(), hour, min) >= 0)
                        && (compareTime(alarm.getHour(), alarm.getMinutes(), endHour, endMin) <= 0)) {
                    result.add(alarm);
                }

            } else {
                long alarmTime = getFiredAlarmTimeFromCalendar(alarm, getStartCalendar(startTime, endTime));
                Log.i(TAG, "getSectionAlarm alarm:" + alarm + ",fired time:" + Formatter.formatTime(alarmTime));

                if ((alarmTime >= startMillis) && (alarmTime <= endMillis)) {
                    Log.d(TAG, "getSectionAlarm match alarm:,alarmId:" + alarm.getId());
                    result.add(alarm);
                }
            }
        });
        return result;
    }


    public static boolean isValid(int hour, int min) {
        int zero = 0;
        int hour24 = 24;
        int min60 = 60;
        return ((hour >= zero) && (hour < hour24) && (min >= zero) && (min < min60));
    }


    public static long compareTime(int cHour, int cMin, int hour, int min) {
        long oneMin = 60 * 1000;
        long oneHour = 60 * oneMin;

        long cTime = cHour * oneHour + cMin * oneMin;
        long time = hour * oneHour + min * oneMin;
        return cTime - time;
    }

    private static long getFiredAlarmTimeFromCalendar(Alarm alarm, Calendar calendar) {

        //如果是响一次的闹钟，不存在重复周期，计算响铃时间要以当前时间为标准
        if (!RepeatSet.isRepeat(alarm.getRepeatSet()) && (alarm.getWorkdaySwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_OFF)
                && alarm.getmLoopSwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_OFF) {
            calendar = Calendar.getInstance();
        }

        Calendar c = AlarmUtils.getAlarmTimeByCalendar(alarm, calendar, "getFiredAlarmTimeFromCalendar");
        return c.getTimeInMillis();
    }


    private static Calendar getStartCalendar(long startTime, long endTime) {

        Calendar startCalendar = formatCalendar(startTime);
        Calendar endCalendar = formatCalendar(endTime);

        if (startCalendar.equals(endCalendar)) {
            startCalendar.add(Calendar.MINUTE, -1);
        }
        Log.d(TAG, "getStartCalendar time:" + Formatter.formatTime(startCalendar.getTimeInMillis()));
        return startCalendar;

    }

    private static long formatTime(long mills) {
        return formatCalendar(mills).getTimeInMillis();
    }

    private static Calendar formatCalendar(long mills) {

        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(mills);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c;
    }

    public static int[] getWeekAddNum() {
        return WEEK_ADD_NUM.clone();
    }
}
