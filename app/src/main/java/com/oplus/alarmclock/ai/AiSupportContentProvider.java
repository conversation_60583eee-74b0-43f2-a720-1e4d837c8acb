/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.04.10   liukun build
 *******************************************************/
// OPLUS Java File Skip Rule:MethodLength, MethodComplexity, ExceptionsSensitive, NestedBranchDepth
package com.oplus.alarmclock.ai;

import static com.oplus.alarmclock.alarmclock.LegalHolidayUtil.COLOR_TYPE_HOLIDAY;

import android.app.ActivityOptions;
import android.app.Service;
import android.content.ComponentName;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.database.Cursor;
import android.database.sqlite.SQLiteException;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmSettingActivity;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.LegalHolidayUtil;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.CityUtils;
import com.oplus.alarmclock.globalclock.WorldClockBaseFragment;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerAlertReceiver;
import com.oplus.alarmclock.timer.TimerKlaxon;
import com.oplus.alarmclock.timer.TimerPreUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.timer.mini.OplusTimerMiniActivity;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

public class AiSupportContentProvider extends ContentProvider {

    public static final int DEFAULT_TIMER_INDEX = 0;
    public static final int AI_TIMER_ID = 11;
    //Note: Internal use only.
    public static final String METHOD_MARK_CURRENT_ALARM = "mark_current_alarm";
    public static final String EXTRA_CURRENT_ALARM_SCHEDULE = "extra_current_alarm_schedule";
    public static final String ACTION_EXIT_ALARM_EDIT_BY_BREENO = "exit_alarm_edit_by_breeno";
    public static final String EXTRA_ALARM_ID = "alarm_id";
    public static final long DELETE_ALARM_ALL_ID = -2;
    private static final String TAG = "AiSupportContentProvider";

    public static final String CONTENT = "content://";
    public static final String AUTHORITY = "com.coloros.alarmclock.ai";
    public static final String TIMER_EXTENDS = "/timer/changed";
    public static final Uri AI_CLOCK_URI = Uri.parse(CONTENT + AUTHORITY);

    // About alarm.
    private static final String METHOD_ADD_ALARM = "add_alarm";
    private static final String METHOD_CLOSE_ALARM = "close_alarm";
    private static final String METHOD_DELETE_ALARM = "delete_alarm";
    private static final String METHOD_ENABLE_ALARM = "enable_alarm";
    private static final String METHOD_UPDATE_ALARM = "update_alarm";
    private static final String METHOD_MATCH_ALARM = "match_alarm";

    private static final String METHOD_CHANGE_MORNING_SWITCH = "change_morning_switch";
    private static final String METHOD_GET_MORNING_SWITCH = "get_morning_switch";

    private static final String METHOD_GET_ALARM_LIST = "get_alarm_list";
    /**
     * 灵感组件获取闹钟列表
     */
    private static final String METHOD_GET_ALARM_LIST_CARD = "get_alarm_list_card";
    /**
     * 查看当前是否节假日
     */
    private static final String QUERY_WORKDAY_OR_HOLIDAY = "query_workday_or_holiday";
    /**
     * 灵感组件获取下次响铃时间
     */
    private static final String QUERY_ALARM_NEXT_RING_TIME = "query_alarm_next_ring_time";
    private static final String EXTRA_ALARM_LIMIT = "alarm_limit";
    private static final String EXTRA_TIME_HOUR = "alarm_hour";
    private static final String EXTRA_TIME_MIN = "alarm_minute";
    private static final String EXTRA_CLOSE_TYPE = "close_type";
    private static final int CLOSE_TYPE_ONCE = 0;
    private static final int CLOSE_TYPE_FULL = 1;
    private static final long ONE_THOUSAND = 1000L;
    private static final String EXTRA_SNOOZE_TIME = "snooze_time";
    private static final String EXTRA_TIME_END_HOUR = "alarm_end_hour";
    private static final String EXTRA_TIME_END_MIN = "alarm_end_minute";
    private static final String EXTRA_ALARM_ID_LIST = "alarm_id_list";
    private static final String EXTRA_ALARM_HOUR_LIST = "alarm_hour_list";
    private static final String EXTRA_ALARM_MIN_LIST = "alarm_min_list";
    private static final String EXTRA_ALARM_LABEL_LIST = "alarm_label_list";
    private static final String EXTRA_ALARM_STATE_LIST = "alarm_state_list";
    private static final String EXTRA_ALARM_REPEAT_LIST = "alarm_repeat_list";
    private static final String EXTRA_ALARM_ENABLE_ASSOCIATE_LIST = "alarm_enableAssociate_list";
    private static final String EXTRA_ALARM_TIME_LIST = "alarm_time_list";
    private static final String EXTRA_MORNING_SWITCH = "morning_switch_status";
    private static final String EXTRA_START_TIME = "alarm_start_time";
    private static final String EXTRA_END_TIME = "alarm_end_time";
    private static final String EXTRA_WORKDAY_SWITCH_LIST = "workday_switch_list";
    private static final String EXTRA_HOLIDAY_SWITCH_LIST = "holiday_switch_list";
    private static final String EXTRA_REPEAT_SET_LIST = "alarm_repeat_set_list";
    private static final String EXTRA_ALARM_SNOONZE_ITEM_LIST = "alarm_snoonze_items_list";
    private static final String EXTRA_ALARM_SNOONZE_TIME_LIST = "alarm_snoonze_times_list";
    private static final String EXTRA_ALARM_UUID_LIST = "alarm_uuid_list";
    private static final String EXTRA_UPDATE_TYPE = "alarm_update_type";
    private static final String EXTRA_ALARM_SNOOZE_TIME_LIST = "alarm_snooze_time_list";
    private static final String EXTRA_ALARM_WORKDAY_TYPE_LIST = "alarm_workday_type_list";
    private static final String EXTRA_ALARM_WORKDAY_UPDATE_TIME_LIST = "alarm_workday_update_time_list";
    private static final String EXTRA_ALARM_SPECIAL_ALARM_DAYS_LIST = "alarm_special_alarm_days_list";
    private static final String EXTRA_ALARM_DEFAULT_ALARM_LIST = "alarm_default_alarm_list";
    private static final String EXTRA_ALARM_RING_NUM_LIST = "alarm_ring_num_list";
    private static final String EXTRA_ALARM_LOOP_SWITCH_LIST = "alarm_loop_switch_list";
    private static final String EXTRA_ALARM_LOOP_CYCLE_DAYS_LIST = "alarm_loop_cycle_days_list";
    private static final String EXTRA_ALARM_LOOP_ID_LIST = "alarm_loop_id_list";
    private static final String EXTRA_ALARM_LOOP_WORK_DAYS_LIST = "alarm_loop_work_days_list";
    private static final String EXTRA_ALARM_LOOP_ALARM_NUMBER_LIST = "alarm_loop_alarm_number_list";
    private static final String EXTRA_ALARM_LOOP_DAY_LIST = "alarm_loop_day_list";
    private static final String EXTRA_ALARM_LOOP_RESET_DAYS_LIST = "alarm_loop_reset_days_list";
    //about world clock
    private static final String METHOD_GET_CLOCK_LIST = "get_world_clock_list";
    private static final String METHOD_UPDATE_CLOCK_LIST = "update_world_clock_list";
    private static final String EXTRA_LOCALE = "extra_locale";
    private static final String EXTRA_CITY_NAME_LIST = "city_name_list";
    private static final String EXTRA_CITY_TIMEZONE_LIST = "city_timezone_list";
    private static final String EXTRA_CITY_ID_LIST = "city_id_list";
    private static final String EXTRA_CITY_SORT_LIST = "city_sort_list";
    private static final String EXTRA_CITY_UPDATE_TYPE = "city_update_type";
    private static final int EXTRA_CITY_ADD_LIST = 0x01;
    private static final int EXTRA_CITY_DELETE_LIST = 0x02;
    private static final String EXTRA_CITY_ID = "city_id";
    private static final String EXTRA_CITY_NAME = "city_name";
    // About result.
    private static final String EXTRA_RESULT = "result";
    private static final int RESULT_SUCCESS = 1;
    private static final int RESULT_ERROR = -1;
    private static final int RESULT_ERROR_COUNT_LIMIT = -2;
    private static final int RESULT_NO_ALARM_FOUND = -3;
    private static final int RESULT_FOUND_MUL_ALARMS = -4;
    private static final int RESULT_TIMER_IS_RUNNING = -5;
    private static final int RESULT_NO_TIMER = -6;
    private static final int RESULT_TIMER_ALREADY_IN_STATE = -7;
    private static final String METHOD_STOP_ALARM = "stop_alarm";
    private static final String METHOD_SNOOZE_ALARM = "snooze_alarm";
    private static final String METHOD_CLOSE_ALL_ALARMS = "close_all_alarms";
    private static final String METHOD_DEL_ALL_ALARMS = "del_all_alarms";
    private static final String METHOD_CHECK_TIMER = "check_timer";
    private static final String METHOD_START_TIMER = "start_timer";
    private static final String METHOD_PAUSE_TIMER = "pause_timer";
    private static final String METHOD_RESUME_TIMER = "resume_timer";
    private static final String METHOD_CANCEL_TIMER = "cancel_timer";
    private static final String METHOD_STOP_TIMER_ALERT = "stop_timer_alert";
    private static final String EXTRA_OVERRIDE_CURRENT_TIMER = "override_current_timer";
    private static final String EXTRA_TIMER_ID = "timer_id";
    private static final String EXTRA_TIMER_DURATION = "duration";
    private static final String EXTRA_TIMER_LEFT_TIME = "left_time";
    private static final String EXTRA_TIMER_TIME_STAMP = "time_stamp";
    private static final String EXTRA_TIMER_TIME_STATUS = "timer_status";
    private static final String EXTRA_TIMER_DESCRIPTION = "description";
    private static final String EXTRA_START_TIMER_UI = "start_timer_ui";
    private static final String QUERY_CALENDAR = "query_calendar";
    private static final String CARD_RESULT_DATA = "data";
    private static final String CARD_RESULT_ALARM_SIZE = "alarmSize";
    private static final String CARD_RESULT_DATA_HOLIDAY = "1";
    private static final String CARD_RESULT_DATA_NORMAL = "0";
    public static boolean sAiStartTimerMark = false;
    private final Object mLock = new Object();
    private Context mContext;
    private TimerService mTimerService;
    private ServiceConnection mTimerServiceConnection;

    @Override
    public boolean onCreate() {
        mContext = this.getContext();
        return true;
    }

    private void startTimerService(Context context) {
        initTimerServiceConnection();
        Intent intent = new Intent(context, TimerService.class);
        context.getApplicationContext().bindService(intent, mTimerServiceConnection,
                Service.BIND_AUTO_CREATE);
        context.startService(intent);
    }

    private void initTimerServiceConnection() {
        if (mTimerServiceConnection == null) {
            mTimerServiceConnection = new ServiceConnection() {
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {
                    Log.i(TAG, "onServiceConnected Bind successfully mTimerServiceConnection");
                    synchronized (mLock) {
                        mTimerService = ((TimerService.TimerBinder) service).getService();
                        mLock.notifyAll();
                    }
                }

                @Override
                public void onServiceDisconnected(ComponentName name) {
                    Log.i(TAG, "onServiceDisconnected");
                    mTimerService = null;
                }
            };
        }
    }

    @Override
    public Bundle call(@NonNull String method, @NonNull String name, Bundle args) {
        Log.d(TAG, "call:" + method);
        Bundle result = new Bundle();
        long identity = Binder.clearCallingIdentity();
        switch (method) {
            case METHOD_MARK_CURRENT_ALARM:
                Log.d(TAG, "METHOD_MARK_CURRENT_ALARM");
                break;
            case METHOD_ADD_ALARM:
                Log.d(TAG, "METHOD_ADD_ALARM");
                addAlarm(args, result);
                break;
            case METHOD_CLOSE_ALARM:
                closeAlarm(args, result);
                break;
            case METHOD_DELETE_ALARM:
                deleteAlarm(args, result);
                break;
            case METHOD_ENABLE_ALARM:
                enableAlarm(args, result);
                break;
            case METHOD_UPDATE_ALARM:
                updateAlarm(args, result);
                break;
            case METHOD_MATCH_ALARM:
                handleFilterAlarm(mContext, args, result);
                break;
            case METHOD_CLOSE_ALL_ALARMS:
                closeAllAlarms(result);
                break;
            case METHOD_DEL_ALL_ALARMS:
                deleteAllAlarms(result);
                break;
            case METHOD_STOP_ALARM:
                Log.d(TAG, "METHOD_STOP_ALARM");
                stopAlarm(result);
                break;
            case METHOD_SNOOZE_ALARM:
                Log.d(TAG, "METHOD_SNOOZE_ALARM");
                snoozeAlarm(args, result);
                break;
            case METHOD_CHECK_TIMER:
                Log.d(TAG, "METHOD_CHECK_TIMER");
                checkTimer(args, result);
                break;
            case METHOD_START_TIMER:
                Log.d(TAG, "METHOD_START_TIMER");
                startTimer(args, result);
                break;
            case METHOD_PAUSE_TIMER:
                Log.d(TAG, "METHOD_PAUSE_TIMER");
                pauseTimer(args, result);
                break;
            case METHOD_RESUME_TIMER:
                Log.d(TAG, "METHOD_RESUME_TIMER");
                resumeTimer(args, result);
                break;
            case METHOD_CANCEL_TIMER:
                Log.d(TAG, "METHOD_CANCEL_TIMER");
                cancelTimer(args, result);
                break;
            case METHOD_STOP_TIMER_ALERT:
                Log.d(TAG, "METHOD_STOP_TIMER_ALERT");
                TimerAlertReceiver.sendMessageToCloseFullScreenAlert(mContext);
                break;
            case METHOD_GET_CLOCK_LIST:
                Log.d(TAG, "METHOD_GET_CLOCK_LIST");
                handleGetCityList(mContext, args, result);
                break;
            case METHOD_UPDATE_CLOCK_LIST:
                Log.d(TAG, "METHOD_UPDATE_CLOCK_LIST");
                handleUpdateCityList(mContext, args, result);
                break;
            case METHOD_CHANGE_MORNING_SWITCH:
                handleChangeMorningSwitch(mContext, args, result);
                break;
            case METHOD_GET_ALARM_LIST:
                handleGetAlarmList(mContext, args, result);
                break;
            case METHOD_GET_MORNING_SWITCH:
                getMorningSwitchStatus(mContext, args, result);
                break;
            case METHOD_GET_ALARM_LIST_CARD:
                handleCardGetAlarmList(mContext, result);
                break;
            case QUERY_WORKDAY_OR_HOLIDAY:
                queryWorkdayOrHoliday(args, result);
                break;
            case QUERY_ALARM_NEXT_RING_TIME:
                queryAlarmNextRingTime(mContext, result);
                break;
            default:
                break;
        }

        Log.d(TAG, "call: " + method + ", args: " + args + ", result: " + result);
        Binder.restoreCallingIdentity(identity);
        return result;
    }

    /**
     * add_alarm
     *
     * @param args
     * @param result
     */
    public void addAlarm(Bundle args, Bundle result) {
        ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_VOICE_ALARM_ADD);
        if (AiAlarmUtils.getAlarmsCount(mContext) >= AlarmClockFragment.MAX_ALARM_COUNT) {
            result.putInt(EXTRA_ALARM_LIMIT, AlarmClockFragment.MAX_ALARM_COUNT);
            result.putInt(EXTRA_RESULT, RESULT_ERROR_COUNT_LIMIT);
            Log.d(TAG, "METHOD_ADD_ALARM RESULT_ERROR_COUNT_LIMIT");
        } else {
            Alarm alarm = AiAlarmUtils.handleSetAlarm(mContext, args);
            boolean success = alarm != null;
            if (success) {
                AlarmPreferenceUtils.Companion.getInstance().addAlarmInfo(System.currentTimeMillis(), alarm);
                ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_VOICE_ALARM_ADD_SUCCUSS);
            }
            Log.d(TAG, "METHOD_ADD_ALARM RESULT_ERROR");
            result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS
                    : RESULT_ERROR);
            putExtrasIntoResultBundle(result, alarm);
        }
    }

    /**
     * close_alarm 关闭闹钟
     *
     * @param args:alarm_id:闹钟id//                                   需要增加按照id操作
     * @param args:alarm_hour、alarm_minute按照时间操作
     * @param args:close_type:0只关闭一次、1完全关闭(method为close_alarm时才有该参数)
     * @param result                                                 返回结果
     */
    private void closeAlarm(Bundle args, Bundle result) {
        if (args != null) {
            int hour = args.getInt(EXTRA_TIME_HOUR, -1);
            int min = args.getInt(EXTRA_TIME_MIN, -1);
            int closeType = args.getInt(EXTRA_CLOSE_TYPE, -1);
            if ((hour >= 0) && (min >= 0)) {
                Log.d(TAG, "Get Alarms: Hour: " + hour + ", Min: " + min);
                List<Alarm> list = AiAlarmUtils.getAlarms(mContext, hour, min);
                if (list.isEmpty()) {
                    Log.d(TAG, "METHOD_ENABLE_ALARM RESULT_NO_ALARM_FOUND");
                    result.putInt(EXTRA_RESULT, RESULT_NO_ALARM_FOUND);
                } else if (list.size() == 1) {
                    boolean success = false;
                    Alarm alarm = list.get(0);
                    Alarm preAlarm = null;
                    try {
                        preAlarm = alarm.clone();
                    } catch (CloneNotSupportedException e) {
                        e.printStackTrace();
                    }
                    if (alarm.isRepeatAlarm()) {
                        if (CLOSE_TYPE_ONCE == closeType) {
                            success = AlarmUtils.closeOnce(mContext, alarm.getId());
                        } else {
                            success = AlarmUtils.disableAlarm(mContext, alarm.getId(), false);
                        }
                    } else {
                        success = AlarmUtils.disableAlarm(mContext, alarm.getId(), false);
                    }
                    if (success) {
                        AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(
                                System.currentTimeMillis(), (preAlarm == null) ? alarm : preAlarm, alarm, false);
                        AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.ALARM_CLOSE_USER_VOICE);
                    }
                    Log.d(TAG, "closeAlarm result:" + success);
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                } else {
                    result.putInt(EXTRA_RESULT, RESULT_FOUND_MUL_ALARMS);
                    putExtrasIntoResultBundle(result, list);
                }
            } else {
                long alarmId = args.getLong(EXTRA_ALARM_ID, -1);
                if (alarmId >= 0) {
                    boolean success = false;
                    Alarm alarm = AlarmUtils.getAlarm(mContext, alarmId);
                    if (alarm != null) {
                        if (alarm.isRepeatAlarm()) {
                            if (CLOSE_TYPE_ONCE == closeType) {
                                success = AlarmUtils.closeOnce(mContext, alarm.getId());
                            } else {
                                success = AlarmUtils.disableAlarm(mContext, alarm.getId(), false);
                            }
                        } else {
                            success = AlarmUtils.disableAlarm(mContext, alarmId, false);
                        }
                        if (success) {
                            AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.ALARM_CLOSE_USER_VOICE);
                        }
                    }
                    Log.d(TAG, "closeAlarm of alarmId:" + alarmId + " result:" + success);
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                }
            }
        }
    }

    /**
     * delete_alarm 删除闹钟
     *
     * @param args:alarm_id:闹钟id//               需要增加按照id操作
     * @param args:alarm_hour、alarm_minute按照时间操作
     * @param result
     */
    private void deleteAlarm(Bundle args, Bundle result) {
        if (args != null) {
            int hour = args.getInt(EXTRA_TIME_HOUR, -1);
            int min = args.getInt(EXTRA_TIME_MIN, -1);
            if ((hour >= 0) && (min >= 0)) {
                Log.d(TAG, "Get Alarms: Hour: " + hour + ", Min: " + min);
                List<Alarm> list = AiAlarmUtils.getAlarms(mContext, hour, min);
                if (list.isEmpty()) {
                    Log.d(TAG, "deleteAlarm RESULT_NO_ALARM_FOUND");
                    result.putInt(EXTRA_RESULT, RESULT_NO_ALARM_FOUND);
                } else if (list.size() == 1) {
                    boolean success = false;
                    Alarm alarm = list.get(0);
                    long alarmId = alarm.getId();
                    success = AlarmUtils.deleteAlarm(mContext, alarmId, false);
                    if (success) {
                        AlarmPreferenceUtils.Companion.getInstance().deleteAlarmInfo(System.currentTimeMillis(), alarm);
                        exitAlarmExitMode(alarm.getId());
                        ArraySet<Long> alarmIdSet = new ArraySet<>();
                        alarmIdSet.add(alarmId);
                        PrefUtils.removeNotification(mContext, alarmIdSet, null, false, false);
                    }
                    Log.d(TAG, "deleteAlarm:" + result);
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                } else {
                    result.putInt(EXTRA_RESULT, RESULT_FOUND_MUL_ALARMS);
                    putExtrasIntoResultBundle(result, list);
                    Log.d(TAG, "deleteAlarm RESULT_FOUND_MUL_ALARMS");
                }
            } else {
                long alarmId = args.getLong(EXTRA_ALARM_ID, -1);
                if (alarmId >= 0) {
                    boolean success = false;
                    Alarm alarm = AlarmUtils.getAlarm(mContext, alarmId);
                    success = AlarmUtils.deleteAlarm(mContext, alarmId, false);
                    if (success && (alarm != null)) {
                        AlarmPreferenceUtils.Companion.getInstance().deleteAlarmInfo(System.currentTimeMillis(), alarm);
                        exitAlarmExitMode(alarmId);
                        ArraySet<Long> alarmIdSet = new ArraySet<>();
                        alarmIdSet.add(alarmId);
                        PrefUtils.removeNotification(mContext, alarmIdSet, null, false, false);
                    }
                    Log.d(TAG, "deleteAlarm of alarmId:" + alarmId + " result:" + result);
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                }
            }
        }
    }

    private void enableAlarm(Bundle args, Bundle result) {
        if (args != null) {
            int hour = args.getInt(EXTRA_TIME_HOUR, -1);
            int min = args.getInt(EXTRA_TIME_MIN, -1);
            if ((hour >= 0) && (min >= 0)) {
                Log.d(TAG, "Get Alarms: Hour: " + hour + ", Min: " + min);
                List<Alarm> list = AiAlarmUtils.getAlarms(mContext, hour, min);
                if (list.isEmpty()) {
                    Log.d(TAG, "enableAlarm RESULT_NO_ALARM_FOUND");
                    result.putInt(EXTRA_RESULT, RESULT_NO_ALARM_FOUND);
                } else if (list.size() == 1) {
                    boolean success = false;
                    Alarm alarm = list.get(0);
                    Alarm preAlarm = null;
                    try {
                        preAlarm = alarm.clone();
                    } catch (CloneNotSupportedException e) {
                        e.printStackTrace();
                    }
                    success = AlarmUtils.enableAlarm(getContext(), alarm, true);
                    if (success) {
                        AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(
                                System.currentTimeMillis(), (preAlarm == null) ? alarm : preAlarm, alarm, true);
                        popAlarmSetToast(alarm);
                        AlarmRingOperateUtils.openAlarm(alarm);
                    }
                    Log.d(TAG, "enableAlarm result:" + result);
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                } else {
                    result.putInt(EXTRA_RESULT, RESULT_FOUND_MUL_ALARMS);
                    putExtrasIntoResultBundle(result, list);
                    Log.d(TAG, "enableAlarm RESULT_FOUND_MUL_ALARMS");
                }
            } else {
                long alarmId = args.getLong(EXTRA_ALARM_ID, -1);
                if (alarmId >= 0) {
                    boolean success = false;
                    Alarm alarm = AlarmUtils.getAlarm(mContext, alarmId);
                    if (alarm != null) {
                        Alarm preAlarm = null;
                        try {
                            preAlarm = alarm.clone();
                        } catch (CloneNotSupportedException e) {
                            e.printStackTrace();
                        }
                        success = AlarmUtils.enableAlarm(getContext(), alarm, true);
                        if (success) {
                            AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(System.currentTimeMillis(),
                                    (preAlarm == null) ? alarm : preAlarm, alarm, true);
                            popAlarmSetToast(alarm);
                            try {
                                AlarmRingOperateUtils.openAlarm(alarm);
                            } catch (SQLiteException e) {
                                Log.d(TAG, "openAlarm e: " + e.getMessage());
                            }
                        }
                    } else {
                        success = false;
                    }
                    Log.d(TAG, "METHOD_ENABLE_ALARM ELSE RESULT_ERROR");
                    result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
                }
            }
        }
    }

    private void updateAlarm(Bundle args, Bundle result) {
        if (args != null) {
            int hour = args.getInt(EXTRA_TIME_HOUR, -1);
            int min = args.getInt(EXTRA_TIME_MIN, -1);
            long alarmId = args.getLong(EXTRA_ALARM_ID, -1);

            Log.d(TAG, "Get Alarms: Hour: " + hour + ", Min: " + min);

            boolean success = false;

            if (AiAlarmUtils.isValid(hour, min)) {
                Alarm alarm = AlarmUtils.getAlarm(mContext, alarmId);
                if (alarm != null) {
                    Alarm preAlarm = null;
                    try {
                        preAlarm = alarm.clone();
                    } catch (CloneNotSupportedException e) {
                        e.printStackTrace();
                    }
                    alarm.setEnabled(true);
                    alarm.setHour(hour);
                    alarm.setMinutes(min);

                    AlarmUtils.updateAlarmInfo(AlarmClockApplication.getInstance(), alarm, false);
                    success = AlarmUtils.enableAlarm(AlarmClockApplication.getInstance(), alarm, false);
                    AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(
                            System.currentTimeMillis(), (preAlarm == null) ? alarm : preAlarm, alarm, true);
                }
            }
            result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
        }
    }

    private void closeAllAlarms(Bundle result) {
        if (AiAlarmUtils.getAlarmsCount(mContext) > 0) {
            AiAlarmUtils.disableAllAlarms(mContext);
            Log.d(TAG, "METHOD_CLOSE_ALL_ALARMS RESULT_SUCCESS");
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        } else {
            Log.d(TAG, "METHOD_CLOSE_ALL_ALARMS RESULT_NO_ALARM_FOUND");
            result.putInt(EXTRA_RESULT, RESULT_NO_ALARM_FOUND);
        }
    }

    private void deleteAllAlarms(Bundle result) {
        if (AiAlarmUtils.getAlarmsCount(mContext) > 0) {
            AiAlarmUtils.delAllAlarms(mContext);
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            exitAlarmExitMode(DELETE_ALARM_ALL_ID);
            AlarmUtils.updateAlarmNextTime(mContext);
            Log.d(TAG, "METHOD_DEL_ALL_ALARMS RESULT_SUCCESS");
        } else {
            result.putInt(EXTRA_RESULT, RESULT_NO_ALARM_FOUND);
            Log.d(TAG, "METHOD_DEL_ALL_ALARMS RESULT_NO_ALARM_FOUND");
        }
    }

    private void stopAlarm(Bundle result) {
        if (CurrentAlarmScheduleHolder.getAlarmSchedule() != null) {
            AiAlarmUtils.stopAlarm(mContext, CurrentAlarmScheduleHolder.getAlarmSchedule());
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            Log.d(TAG, "METHOD_STOP_ALARM RESULT_SUCCESS");
            Alarm alarm = CurrentAlarmScheduleHolder.getAlarmSchedule().getAlarm();
            if (alarm != null) {
                AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.ALARM_CLOSE_USER_VOICE);
            }
        }
    }

    private void snoozeAlarm(Bundle args, Bundle result) {
        if (args != null) {
            long alarmId = args.getLong(EXTRA_ALARM_ID, -1);
            long snoozeTime = args.getLong(EXTRA_SNOOZE_TIME, 0);
            if (alarmId >= 0 && snoozeTime > 0) {
                boolean success = false;
                Alarm alarm = AlarmUtils.getAlarm(mContext, alarmId);
                //TBD 推迟闹钟
            }
        } else {
            if (CurrentAlarmScheduleHolder.getAlarmSchedule() != null) {
                AiAlarmUtils.snoozeAlarm(mContext, CurrentAlarmScheduleHolder.getAlarmSchedule().getId());
                result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
                Log.d(TAG, "METHOD_SNOOZE_ALARM RESULT_SUCCESS");
                Alarm alarm = CurrentAlarmScheduleHolder.getAlarmSchedule().getAlarm();
                if (alarm != null) {
                    AlarmRingOperateUtils.snoozeAlarm(alarm, AlarmRingOperateUtils.SNOOZE_ALARM_USER);
                }
            }
        }
    }

    private void handleGetAlarmList(Context context, Bundle args, Bundle result) {
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        putExtrasIntoResultBundle(result, list);
        result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
    }

    /**
     * 灵感组件获取闹钟列表
     *
     * @param context
     * @param result
     */
    private void handleCardGetAlarmList(Context context, Bundle result) {
        if (context != null) {
            ArrayList<Alarm> list = AlarmUtils.getAllAlarms(context);
            Gson gson = new Gson();
            String json = gson.toJson(list);
            Log.d(TAG, "handleCardGetAlarmList:" + json);
            result.putString(CARD_RESULT_DATA, json);
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        } else {
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
        }
    }

    /**
     * 查询是否为节假日
     *
     * @param args
     * @param result
     */
    private void queryWorkdayOrHoliday(Bundle args, Bundle result) {
        if (args != null) {
            String cal = args.getString(QUERY_CALENDAR, "");
            Log.d(TAG, "queryWorkdayOrHoliday:" + cal);
            if (!TextUtils.isEmpty(cal)) {
                try {
                    long dateMillis = Long.parseLong(cal);
                    Calendar dateCalendar = Calendar.getInstance();
                    dateCalendar.setTimeInMillis(dateMillis);
                    int isHoliday = LegalHolidayUtil.queryWorkdayOrHoliday(dateCalendar);
                    result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
                    if (isHoliday == COLOR_TYPE_HOLIDAY) {
                        //节假日
                        result.putString(CARD_RESULT_DATA, CARD_RESULT_DATA_HOLIDAY);
                    } else {
                        result.putString(CARD_RESULT_DATA, CARD_RESULT_DATA_NORMAL);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "queryWorkdayOrHoliday error");
                    result.putInt(EXTRA_RESULT, RESULT_ERROR);
                }
            } else {
                result.putInt(EXTRA_RESULT, RESULT_ERROR);
            }
        }
    }

    /**
     * 查询下次响铃时间和闹钟数量
     *
     * @param result
     */
    private void queryAlarmNextRingTime(Context context, Bundle result) {
        if (context != null) {
            AlarmSchedule nextSchedule = ScheduleUtils.getNextFiringAlarm(context);
            int alarmSize = AlarmUtils.getAlarmsCount(context);
            Log.d(TAG, "queryAlarmNextRingTime:" + "alarmSize:" + alarmSize);
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            if (nextSchedule != null) {
                result.putLong(CARD_RESULT_DATA, nextSchedule.getTime());
                Log.d(TAG, "nextSchedule:" + nextSchedule.getTime());
            }
            result.putInt(CARD_RESULT_ALARM_SIZE, alarmSize);
        } else {
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
        }
    }


    private void handleFilterAlarm(Context context, Bundle args, Bundle result) {
        long startTime = args.getLong(EXTRA_START_TIME, -1);
        long endTime = args.getLong(EXTRA_END_TIME, -1);

        int hour = args.getInt(EXTRA_TIME_HOUR, -1);
        int min = args.getInt(EXTRA_TIME_MIN, -1);

        int endHour = args.getInt(EXTRA_TIME_END_HOUR, -1);
        int endMin = args.getInt(EXTRA_TIME_END_MIN, -1);

        List<Alarm> resultList = AiAlarmUtils.getSectionAlarm(context, startTime, endTime, hour, min, endHour, endMin);

        boolean success = (resultList != null) && !resultList.isEmpty();
        if (success) {
            putExtrasIntoResultBundle(result, resultList);
        }
        result.putInt(EXTRA_RESULT, success ? RESULT_SUCCESS : RESULT_ERROR);
    }

    private void exitAlarmExitMode(long alarmId) {
        Intent intent = new Intent();
        intent.setAction(ACTION_EXIT_ALARM_EDIT_BY_BREENO);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        intent.putExtra(EXTRA_ALARM_ID, alarmId);
        mContext.sendBroadcast(intent);
    }

    private void getMorningSwitchStatus(Context context, Bundle args, Bundle result) {
        boolean nowStatus = PlayMorningTools.isMorningReportEnable(context);
        result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        result.putBoolean(EXTRA_MORNING_SWITCH, nowStatus);
    }

    private void handleChangeMorningSwitch(Context context, Bundle args, Bundle result) {
        if (args != null) {
            boolean morningSwitchStatus = args.getBoolean(EXTRA_MORNING_SWITCH);
            boolean nowStatus = PlayMorningTools.isMorningReportEnable(context);
            Log.d(TAG, "update morning status nowStatus: " + nowStatus);
            if (morningSwitchStatus != nowStatus) {
                Log.d(TAG, "update morning status by voice status: " + morningSwitchStatus);
                PlayMorningTools.setMorningReportEnable(context, morningSwitchStatus);
                Intent notifySetPageIntent = new Intent(AlarmSettingActivity.ACTION_CHANGE_MORNING_STATUS);
                notifySetPageIntent.putExtra(AlarmSettingActivity.EXTRA_MORNING_SWITCH_STATUS, morningSwitchStatus);
                LocalBroadcastManager.getInstance(context).sendBroadcast(notifySetPageIntent);
            }
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        } else {
            Log.d(TAG, "handleChangeMorningSwitch args is null");
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
        }
    }

    private void popAlarmSetToast(final Alarm alarm) {
        AsyncHandler.post(new Runnable() {
            @Override
            public void run() {
                AlarmUtils.popAlarmSetToast(mContext, alarm);
            }
        });
    }

    private TimerService checkAndStartTimerService() {
        TimerService timerService = mTimerService;
        if (timerService == null) {
            startTimerService();
            timerService = mTimerService;
        }
        return timerService;
    }

    private void startTimerService() {
        final long waitTime = 1000;
        try {
            startTimerService(mContext);
            synchronized (mLock) {
                Log.d(TAG, "startTimerService: " + Thread.currentThread().getName());
                Log.i(TAG, "service not bound wait!");
                if (mTimerService == null) {
                    mLock.wait(waitTime); //wait 1s.
                }
            }
        } catch (InterruptedException e) {
            Log.i(TAG, "startTimerService error: " + e.getMessage());
        }
    }

    private void checkTimer(Bundle args, Bundle result) {
        final TimerService timerService = checkAndStartTimerService();
        if (timerService == null) {
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            Log.d(TAG, "METHOD_CHECK_TIMER  RESULT_ERROR");
            return;
        }
        if (timerService.hasTimeObj(DEFAULT_TIMER_INDEX)) {
            boolean isStart = timerService.isStart(DEFAULT_TIMER_INDEX);
            boolean isPause = timerService.isPause(DEFAULT_TIMER_INDEX);
            Log.d(TAG, "checkTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);

            if (!isStart && !isPause) {
                result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            } else {
                TimerService.TimeObj timeObj = timerService.getTimeObj(DEFAULT_TIMER_INDEX);
                if (timeObj != null) {
                    long remainTime = timeObj.getRemainTime();
                    int timerStatus = 0;
                    if (isPause) {
                        timerStatus = 1;
                    }
                    result.putInt(EXTRA_TIMER_ID, timeObj.getTimerId());
                    result.putLong(EXTRA_TIMER_DURATION, timeObj.getTotalTime());
                    result.putLong(EXTRA_TIMER_LEFT_TIME, remainTime / ONE_THOUSAND);
                    result.putLong(EXTRA_TIMER_TIME_STAMP, remainTime);
                    result.putInt(EXTRA_TIMER_TIME_STATUS, timerStatus);
                    result.putString(EXTRA_TIMER_DESCRIPTION, timeObj.getTimerName());
                }
                result.putInt(EXTRA_RESULT, RESULT_TIMER_IS_RUNNING);
            }
        } else {
            Log.d(TAG, "checkTimer no timer 0.");
            timerService.registerTimer0();
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        }
    }

    private void startTimer(Bundle args, Bundle result) {
        if (args == null) {
            Log.e(TAG, "startTimer args is null!");
            return;
        }
        long seconds = args.getInt(AlarmClock.EXTRA_LENGTH, 0);
        boolean override = args.getBoolean(EXTRA_OVERRIDE_CURRENT_TIMER, false);
        boolean startUI = args.getBoolean(EXTRA_START_TIMER_UI, true);
        final TimerService timerService = checkAndStartTimerService();
        if (timerService == null) {
            Log.d(TAG, "METHOD_START_TIMER  RESULT_ERROR");
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            return;
        }
        Log.e(TAG, "startTimer second:" + seconds);
        if (seconds > 0) {
            if (timerService.hasTimeObj(DEFAULT_TIMER_INDEX)) {
                Log.d(TAG, "startTimer seconds: " + seconds + ", override: " + override);
                boolean isStart = timerService.isStart(DEFAULT_TIMER_INDEX);
                boolean isPause = timerService.isPause(DEFAULT_TIMER_INDEX);
                Log.d(TAG, "startTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);

                if (isStart || isPause) {
                    if (override) {
                        timerService.stopTimer(DEFAULT_TIMER_INDEX);
                        timerService.setTotalTime(seconds * TimerController.ONE_SECOND, seconds * TimerController.ONE_SECOND, DEFAULT_TIMER_INDEX);
                        timerService.stopTimerAlert(DEFAULT_TIMER_INDEX);
                    } else {
                        return;
                    }
                }
            } else {
                Log.d(TAG, "startTimer register timer 0.");
                timerService.registerTimer0();
            }
            mContext.stopService(new Intent(mContext, TimerKlaxon.class));
            if (startUI) {
                setAndStartTimer(seconds);
            } else {
                setAndStartTimerWithoutUI(timerService, seconds);
            }
            result.putInt(EXTRA_TIMER_ID, AI_TIMER_ID);
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        }
    }

    private void pauseTimer(Bundle args, Bundle result) {
        final TimerService timerService = checkAndStartTimerService();
        if (timerService == null) {
            Log.d(TAG, "METHOD_PAUSE_TIMER  RESULT_ERROR");
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            return;
        }
        boolean isStart = timerService.isStart(DEFAULT_TIMER_INDEX);
        boolean isPause = timerService.isPause(DEFAULT_TIMER_INDEX);
        Log.d(TAG, "pauseTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);

        if (!isStart && !isPause) {
            result.putInt(EXTRA_RESULT, RESULT_NO_TIMER);
        } else {
            if (isPause) {
                result.putInt(EXTRA_RESULT, RESULT_TIMER_ALREADY_IN_STATE);
            } else {
                timerService.pauseTimer(DEFAULT_TIMER_INDEX);
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(TimerConstant.PAUSE_TIMER_BROADCAST));
                result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            }
        }
    }

    private void resumeTimer(Bundle args, Bundle result) {
        final TimerService timerService = checkAndStartTimerService();
        if (timerService == null) {
            Log.d(TAG, "METHOD_RESUME_TIMER  RESULT_ERROR");
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            return;
        }
        boolean isStart = timerService.isStart(DEFAULT_TIMER_INDEX);
        boolean isPause = timerService.isPause(DEFAULT_TIMER_INDEX);
        Log.d(TAG, "resumeTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);

        if (!isStart && !isPause) {
            result.putInt(EXTRA_RESULT, RESULT_NO_TIMER);
        } else {
            if (isPause) {
                timerService.startTimer(DEFAULT_TIMER_INDEX);
                LocalBroadcastManager.getInstance(mContext).sendBroadcast(new Intent(TimerConstant.RESUME_TIMER_BROADCAST));
                result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
            } else {
                result.putInt(EXTRA_RESULT, RESULT_TIMER_ALREADY_IN_STATE);
            }
        }
    }

    private void cancelTimer(Bundle args, Bundle result) {
        final TimerService timerService = checkAndStartTimerService();
        if (timerService == null) {
            Log.d(TAG, "METHOD_CANCEL_TIMER  RESULT_ERROR");
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            return;
        }
        boolean isStart = timerService.isStart(DEFAULT_TIMER_INDEX);
        boolean isPause = timerService.isPause(DEFAULT_TIMER_INDEX);
        Log.d(TAG, "cancelTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);

        if (!isStart && !isPause) {
            result.putInt(EXTRA_RESULT, RESULT_NO_TIMER);
        } else {
            timerService.stopTimer(DEFAULT_TIMER_INDEX);
            timerService.stopTimerAlert(DEFAULT_TIMER_INDEX);
            timerService.stopTimerRefreshLayout();
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        }
    }

    /**
     * 拉起计时器页面并开启计时
     *
     * @param seconds
     */
    private void setAndStartTimer(long seconds) {
        Log.d(TAG, "setAndStartTimer");
        sAiStartTimerMark = true;
        boolean supportMiniApp = FoldScreenUtils.isSupportMiniApp(mContext);
        Log.d(TAG, "supportMiniApp:" + supportMiniApp);
        ActivityOptions options = ActivityOptions.makeBasic();
        if (FoldScreenUtils.isDragonflySmallScreen(mContext) && supportMiniApp) {
            Intent intent = new Intent(mContext, OplusTimerMiniActivity.class);
            intent.setAction(AlarmClock.ACTION_AI_SET_TIMER);
            intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, seconds);
            intent.putExtra(AlarmClock.EXTRA_TIMER_ID, AI_TIMER_ID);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            options.setLaunchDisplayId(1);
            mContext.startActivity(intent, options.toBundle());
        } else {
            Intent intent = new Intent(mContext, AlarmClock.class);
            intent.setAction(AlarmClock.ACTION_AI_SET_TIMER);
            intent.putExtra(AlarmClock.EXTRA_TIMER_START, true);
            intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, seconds);
            intent.putExtra(AlarmClock.EXTRA_TIMER_ID, AI_TIMER_ID);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            options.setLaunchDisplayId(0);
            mContext.startActivity(intent, options.toBundle());
        }
    }

    /**
     * 开启计时,不启动页面
     *
     * @param seconds
     */
    private void setAndStartTimerWithoutUI(TimerService service, long seconds) {
        Log.d(TAG, "setAndStartTimerWithoutUI");
        service.registerTimer0();
        service.setTimerId(DEFAULT_TIMER_INDEX, AI_TIMER_ID);
        service.setTimerName(AlarmClockApplication.getInstance().getResources().getString(R.string.timer_title), DEFAULT_TIMER_INDEX);
        service.setTimerRingUri(TimerUtils.getDefaultTimerRingtoneUri(mContext).toString(), DEFAULT_TIMER_INDEX);
        service.setTimerRingName(null, DEFAULT_TIMER_INDEX);
        service.setTotalTime(seconds * ONE_THOUSAND, seconds * ONE_THOUSAND, DEFAULT_TIMER_INDEX);
        service.startTimer(DEFAULT_TIMER_INDEX);
        TimerPreUtils.recordTimerNeedToAlarm(true);
        LiteEventBus.Companion.getInstance().send(TimerConstant.TIMER_REFRESH_BY_AI, "");
    }

    private void putExtrasIntoResultBundle(Bundle result, Alarm alarm) {
        if ((result != null) && (alarm != null)) {
            List<Alarm> list = new ArrayList<>();
            list.add(alarm);
            putExtrasIntoResultBundle(result, list);
        }
    }

    private void putExtrasIntoResultBundle(Bundle result, List<Alarm> list) {
        if ((result != null) && (list != null) && !list.isEmpty()) {
            final int size = list.size();
            int[] hours = new int[size];
            int[] mins = new int[size];
            String[] labels = new String[size];
            boolean[] states = new boolean[size];
            long[] ids = new long[size];
            String[] repeat = new String[size];
            int[] enableAssociates = new int[size];
            long[] times = new long[size];
            int[] workday = new int[size];
            int[] holiday = new int[size];
            int[] repeatSets = new int[size];
            int[] snoonzeItems = new int[size];
            int[] snoonzeTimes = new int[size];
            int[] updateTypes = new int[size];
            String[] uuids = new String[size];
            int[] snoozeTime = new int[size];
            int[] workdayType = new int[size];
            long[] workdayUpdateTime = new long[size];
            String[] specialAlarmDays = new String[size];
            int[] defaultAlarm = new int[size];
            int[] ringNum = new int[size];
            int[] loopSwitch = new int[size];
            int[] loopCycleDays = new int[size];
            int[] loopID = new int[size];
            int[] loopWorkDays = new int[size];
            int[] loopAlarmNumbers = new int[size];
            int[] loopDays = new int[size];
            String[] loopResetDays = new String[size];

            fillAlarmsInfo(list, ids, hours, mins, labels, states, repeat, enableAssociates, times,
                    workday, holiday, repeatSets, snoonzeItems, snoonzeTimes, uuids, updateTypes,
                    snoozeTime, workdayType, workdayUpdateTime, specialAlarmDays, defaultAlarm, ringNum,
                    loopSwitch, loopCycleDays, loopID, loopWorkDays, loopAlarmNumbers, loopDays, loopResetDays);
            result.putLongArray(EXTRA_ALARM_ID_LIST, ids);
            result.putIntArray(EXTRA_ALARM_HOUR_LIST, hours);
            result.putIntArray(EXTRA_ALARM_MIN_LIST, mins);
            result.putStringArray(EXTRA_ALARM_LABEL_LIST, labels);
            result.putBooleanArray(EXTRA_ALARM_STATE_LIST, states);
            result.putStringArray(EXTRA_ALARM_REPEAT_LIST, repeat);
            result.putIntArray(EXTRA_ALARM_ENABLE_ASSOCIATE_LIST, enableAssociates);
            result.putLongArray(EXTRA_ALARM_TIME_LIST, times);
            result.putIntArray(EXTRA_WORKDAY_SWITCH_LIST, workday);
            result.putIntArray(EXTRA_HOLIDAY_SWITCH_LIST, holiday);
            result.putIntArray(EXTRA_REPEAT_SET_LIST, repeatSets);
            result.putIntArray(EXTRA_ALARM_SNOONZE_ITEM_LIST, snoonzeItems);
            result.putIntArray(EXTRA_ALARM_SNOONZE_TIME_LIST, snoonzeTimes);
            result.putStringArray(EXTRA_ALARM_UUID_LIST, uuids);
            result.putIntArray(EXTRA_ALARM_SNOOZE_TIME_LIST, snoozeTime);
            result.putIntArray(EXTRA_ALARM_WORKDAY_TYPE_LIST, workday);
            result.putLongArray(EXTRA_ALARM_WORKDAY_UPDATE_TIME_LIST, workdayUpdateTime);
            result.putStringArray(EXTRA_ALARM_SPECIAL_ALARM_DAYS_LIST, specialAlarmDays);
            result.putIntArray(EXTRA_ALARM_DEFAULT_ALARM_LIST, defaultAlarm);
            result.putIntArray(EXTRA_ALARM_RING_NUM_LIST, ringNum);
            result.putIntArray(EXTRA_UPDATE_TYPE, updateTypes);
            result.putIntArray(EXTRA_ALARM_LOOP_SWITCH_LIST, loopSwitch);
            result.putIntArray(EXTRA_ALARM_LOOP_CYCLE_DAYS_LIST, loopCycleDays);
            result.putIntArray(EXTRA_ALARM_LOOP_ID_LIST, loopID);
            result.putIntArray(EXTRA_ALARM_LOOP_WORK_DAYS_LIST, loopWorkDays);
            result.putIntArray(EXTRA_ALARM_LOOP_ALARM_NUMBER_LIST, loopAlarmNumbers);
            result.putIntArray(EXTRA_ALARM_LOOP_DAY_LIST, loopDays);
            result.putStringArray(EXTRA_ALARM_LOOP_RESET_DAYS_LIST, loopResetDays);
            Log.d(TAG, "putExtrasIntoResultBundle updateType: " + PlatformUtils.sUpdateType);
        }
    }

    private void fillAlarmsInfo(List<Alarm> list, long[] ids, int[] hours, int[] mins, String[] labels,
                                boolean[] states, String[] repeats, int[] enableAssociates, long[] time, int[] workday, int[] holiday,
                                int[] repeatSets, int[] snoonzeItems, int[] snoonzeTimes, String[] uuids, int[] updateTypes, int[] snoozeTime,
                                int[] workdayType, long[] workdayUpateTime, String[] specialAlarmDays, int[] defaultAlarm, int[] ringNum,
                                int[] loopSwitch, int[] loopCycleDays, int[] loopID, int[] loopWorkDays, int[] loopAlarmNumbers,
                                int[] loopDays, String[] loopResetDays) {
        final int size = list.size();
        for (int i = 0; i < size; i++) {
            Alarm alarm = list.get(i);
            long id = alarm.getId();
            ids[i] = id;
            hours[i] = alarm.getHour();
            mins[i] = alarm.getMinutes();
            labels[i] = alarm.getLabel();
            states[i] = alarm.isEnabled();
            enableAssociates[i] = alarm.getEnableAssociate();
            workday[i] = alarm.getWorkdaySwitch();
            holiday[i] = alarm.getHolidaySwitch();
            int repeatSet = alarm.getRepeatSet();
            uuids[i] = alarm.getUUID();
            snoozeTime[i] = alarm.getmSnoozeTime();
            workdayType[i] = alarm.getmWorkDayType();
            workdayUpateTime[i] = alarm.getmWorkdayUpdateTime();
            specialAlarmDays[i] = alarm.getmSpecialAlarmDays();
            defaultAlarm[i] = alarm.getmDefaultAlarm();
            ringNum[i] = alarm.getRingNum();
            loopSwitch[i] = alarm.getmLoopSwitch();
            loopCycleDays[i] = alarm.getmLoopCycleDays();
            loopID[i] = alarm.getmLoopID();
            loopWorkDays[i] = alarm.getmLoopWorkDays();
            loopAlarmNumbers[i] = alarm.getmLoopAlarmNumber();
            loopDays[i] = alarm.getmLoopDay();
            loopResetDays[i] = alarm.getmLoopRestDays();

            repeats[i] = RepeatSet.getDescription(mContext, repeatSet, alarm.getWorkdaySwitch(), alarm.getHolidaySwitch(), true, alarm, false);
            repeatSets[i] = repeatSet;
            snoonzeItems[i] = alarm.getSnoonzeItem();
            AlarmSchedule schedule = AiAlarmUtils.getAlarmSchedule(alarm);
            if (schedule != null) {
                time[i] = schedule.getTime();
                snoonzeTimes[i] = schedule.getSnoonzeTime();
            } else {
                time[i] = AlarmUtils.getAlarmNextTime(alarm, null);
                snoonzeTimes[i] = 0;
            }
            Log.d(TAG, "id:" + id + ",mIotAlarmId:" + PlatformUtils.sIotAlarmId + ",updateType:" + PlatformUtils.sUpdateType);
            updateTypes[i] = (id == PlatformUtils.sIotAlarmId) ? PlatformUtils.sUpdateType : PlatformUtils.UPDATE_TYPE_DEFAULT;
        }
    }

    private void handleUpdateCityList(Context context, Bundle args, Bundle result) {
        if (args == null) {
            Log.e(TAG, "handleUpdateCityList, param is Illegal!");
            return;
        }
        int type = args.getInt(EXTRA_CITY_UPDATE_TYPE);
        int cityId = args.getInt(EXTRA_CITY_ID);
        String name = args.getString(EXTRA_CITY_NAME);

        Log.d(TAG, "handleUpdateCityList name:" + name + ",cityId:" + cityId);
        switch (type) {
            case EXTRA_CITY_ADD_LIST:
                boolean addSuccess = addWorldCity(context, cityId);
                result.putInt(EXTRA_RESULT, addSuccess ? 1 : -1);
                break;

            case EXTRA_CITY_DELETE_LIST:
                boolean deleteSuccess = deleteWorldCity(context, cityId);
                result.putInt(EXTRA_RESULT, deleteSuccess ? 1 : -1);
                break;
            default:
                break;
        }

    }


    private boolean addWorldCity(Context context, int cityId) {

        if (!CityUtils.containCity(context, cityId)) {
            Log.w(TAG, "addWorldCity cityId is not exist:" + cityId);
            return false;
        }
        if (CityUtils.getCurrentCitiesCount(context) >= WorldClockBaseFragment.MAX_CITY_NUM) {
            Log.w(TAG, "addWorldCity Failed to add city! The maximum is 10. Please delete some and retry");
            return false;
        }
        if (CityUtils.cityIsSelected(context, cityId)) {
            Log.w(TAG, "addWorldCity The current list already includes the city:" + cityId);
            return false;
        }
        CityUtils.asyncSetCityUserAdd(context, cityId);
        return true;

    }

    private boolean deleteWorldCity(Context context, int cityId) {

        if (!CityUtils.cityIsSelected(context, cityId)) {
            Log.e(TAG, "deleteWorldCity cityId is not exist:" + cityId);
            return false;
        }
        Integer[] selectedIds = new Integer[]{cityId};
        CityUtils.asyncDeleteCitiesWithoutSound(context, selectedIds);
        return true;
    }

    private void handleGetCityList(Context context, Bundle args, Bundle result) {
        if (result == null) {
            result = new Bundle();
        }
        try {
            String locale = Locale.ENGLISH.toString();
            if (args != null) {
                locale = args.getString(EXTRA_LOCALE, Locale.ENGLISH.toString());
            }
            Log.d(TAG, "handleGetCityList locale: " + locale);
            ArrayList<City> cities = CityUtils.getAllCitiesOfLanguage(context, locale);
            if ((cities != null) && (cities.size() > 0)) {
                String[] cityNames = new String[cities.size()];
                String[] timeZones = new String[cities.size()];
                int[] ids = new int[cities.size()];
                int[] sort = new int[cities.size()];
                for (int i = 0; i < cities.size(); i++) {
                    City city = cities.get(i);
                    ids[i] = city.getCityId();
                    sort[i] = i;
                    cityNames[i] = city.getName() + "\uff0c" + city.getCountry();
                    timeZones[i] = city.getTimezone();
                }
                result.putIntArray(EXTRA_CITY_ID_LIST, ids);
                result.putStringArray(EXTRA_CITY_NAME_LIST, cityNames);
                result.putStringArray(EXTRA_CITY_TIMEZONE_LIST, timeZones);
                result.putIntArray(EXTRA_CITY_SORT_LIST, sort);
            }
            result.putInt(EXTRA_RESULT, RESULT_SUCCESS);
        } catch (Exception e) {
            result.putInt(EXTRA_RESULT, RESULT_ERROR);
            Log.d(TAG, "handleGetCityList error: " + e.getMessage());
        }
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] strings, @Nullable String s, @Nullable String[] strings1, @Nullable String s1) {
        return null;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues contentValues) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues contentValues, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }
}
