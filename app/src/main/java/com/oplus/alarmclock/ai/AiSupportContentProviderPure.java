package com.oplus.alarmclock.ai;

import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import com.oplus.clock.common.utils.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class AiSupportContentProviderPure extends AiSupportContentProvider {

    private static final String TAG = "AiSupportContentProviderPure";
    public static final String AUTHORITY_PURE = "com.oplus.alarmclock.ai";

    private Uri replacePureUri(Uri uri) {
        if (uri != null) {
            Uri newUri = Uri.parse(uri.toString().replace(AUTHORITY_PURE, AUTHORITY));
            Log.d(TAG, "query uri : " + uri + " newUri : " + newUri);
            return newUri;
        }
        return null;
    }

    @Override
    public boolean onCreate() {
        return super.onCreate();
    }

    @Override
    public Bundle call(@NonNull String method, @NonNull String name, Bundle args) {
        return super.call(method, name, args);
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] strings, @Nullable String s, @Nullable String[] strings1, @Nullable String s1) {
        return super.query(replacePureUri(uri), strings, s, strings1, s1);
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return super.getType(replacePureUri(uri));
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues contentValues) {
        return super.insert(replacePureUri(uri), contentValues);
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String s, @Nullable String[] strings) {
        return super.delete(replacePureUri(uri), s, strings);
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues contentValues, @Nullable String s, @Nullable String[] strings) {
        return super.update(replacePureUri(uri), contentValues, s, strings);
    }
}
