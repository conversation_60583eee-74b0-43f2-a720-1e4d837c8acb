/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : Clock Backup and Restore constants
 * History :( ID, Date, Author, Description)
 * v1.0, 2017-06-26, xuan.zhou, create
 ***********************************************************/
package com.oplus.alarmclock.backup;

public class BackUpConstant {
    public final static String CLOCK_DATA_CHANGE = "com.oplus.alarmclock.recover";

    static final String CLOCK_ROOT = "CLOCK_ROOT";

    static final String WORLD_CLOCK_ROOT = "worldclock_root";
    static final String ENNAME = "enName";



    static final String STOPWATCH_ROOT = "stopwatch_root";

    static final String STOPWATCH_STATUS_PREFERENCE = "stopwatch_status_preference";
    static final String STOPWATCH_LAST_RECORD_TIME_PREFERENCE = "stopwatch_last_record_time_preference";

    static final String STOPWATCH_DATA_PREFERENCE = "stopwatch_data";
    static final String STOPWATCH_NOW_PREFERENCE = "stopwatch_now";
    static final String STOPWATCH_SERVICE_STATUS_PREFERENCE = "stopwatch_status";
    static final String STOPWATCH_RECORDS_PREFERENCE = "stopwatch_records";

    static final String TIMER_ROOT = "timer_root";
    static final String TIMER_NEED_TO_ALARM_PREFERENCE = "timer_need_to_alarm";
    static final String TIMER_SET_TIME_PREFERENCE = "timer_set_time";

    static final String ALARM_CLOSE_MODEL_AND_RED = "close_model_and_red";
    /**
     * 默认铃声/默认振动
     */
    static final String ALARM_DEFAULT_RING_URI = "alarm_default_ring_uri_and_vibrate";
    static final String TIMER_DATA_PREFERENCE = "timer_data";
    static final String TIMER_DATA_START_PREFERENCE = "timer_start";
    static final String TIMER_DATA_TOTAL_TIME_PREFERENCE = "timer_total_time";
    static final String TIMER_STATUS_PREFERENCE = "timer_status";

    static final String ALARM_REPEAT_ROOT = "alarm_repeat_root";
    static final String MORNING_SETTING_STATUS = "morning_setting_status";
	static final String SETTING_SHOW_NEXT_ALARM_NOTICES = "setting_show_next_alarm_notices";
    static final String SETTING_GRADUALLY_RINGS = "setting_gradually_rings";

    static final String SETTING_SET_WORKDAY_ROOT = "setting_set_workday_root";
    static final String SETTING_SET_WORKDAY_TYPE = "setting_set_workday_type";
    static final String SETTING_SET_WORKDAY_TYPE_TIME = "setting_set_workday_type_time";

    /* 负一屏时钟小卡城市数据 */
    static final String DIAL_CLOCK_CITY_DATA = "dial_clock_city_data_root";
    static final String DIAL_CLOCK_ATTR_WIDGET_CODE = "dial_clock_attr_widget_code";
    static final String DIAL_CLOCK_ATTR_CITY_ID = "dial_clock_attr_city_id";
}
