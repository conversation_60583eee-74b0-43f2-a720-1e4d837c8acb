/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : Clock Backup Data and compose
 * History :( ID, Date, Author, Description)
 * v1.0, 2017-06-26, xuan.zhou, create
 ***********************************************************/
package com.oplus.alarmclock.backup;

import static com.oplus.alarmclock.backup.ClockBackupPlugin.DEFAULT_ALARM_SETTING_URI;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Xml;

import com.coloros.refusedesktop.Constants;
import com.coloros.refusedesktop.viewmodel.DialClockViewModel;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.LoopAlarmUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.WorkDayTypeUtils;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TimerConstant;

import org.xmlpull.v1.XmlSerializer;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

class ClockXmlComposer {
    private static final String CLOCK_RECORD_TAG = "clockrecord";
    private static final String TAG = "ClockXmlComposer";

    private XmlSerializer mSerializer = null;
    private StringWriter mStringWriter = null;
    private XmlSerializer mTempSerializer = null;

    boolean startCompose() {
        boolean result = false;
        mSerializer = Xml.newSerializer();
        mStringWriter = new StringWriter();
        mTempSerializer = Xml.newSerializer();
        StringWriter tempStringWriter = new StringWriter();
        try {
            mSerializer.setOutput(mStringWriter);
            // serializer.startDocument("UTF-8", null);
            mSerializer.startDocument(null, false);
            mSerializer.startTag("", CLOCK_RECORD_TAG);
            mTempSerializer.setOutput(tempStringWriter);
            // serializer.startDocument("UTF-8", null);
            mTempSerializer.startDocument(null, false);
            mTempSerializer.startTag("", CLOCK_RECORD_TAG);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean endCompose() {
        boolean result = false;
        try {
            mSerializer.endTag("", CLOCK_RECORD_TAG);
            mSerializer.endDocument();
            mTempSerializer.endTag("", CLOCK_RECORD_TAG);
            mTempSerializer.endDocument();
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean addOneAlarm(Context context, Alarm alarm) {
        boolean result = false;
        try {
            mSerializer.startTag("", BackUpConstant.CLOCK_ROOT);
            mSerializer.attribute("", ClockContract.Alarm.ID, Long.toString(alarm.getId()));
            mSerializer.attribute("", ClockContract.Alarm.HOUR, Integer.toString(alarm.getHour()));
            mSerializer.attribute("", ClockContract.Alarm.MINUTES, Integer.toString(alarm.getMinutes()));
            mSerializer.attribute("", ClockContract.Alarm.DAYS_OF_WEEK,
                    Integer.toString(alarm.getRepeatSet()));
            mSerializer.attribute("", ClockContract.Alarm.ENABLED, Boolean.toString(alarm.isEnabled()));
            mSerializer.attribute("", ClockContract.Alarm.ALERTTYPE,
                    Integer.toString(alarm.getAlertType()));
            try {
                mTempSerializer.attribute("", ClockContract.Alarm.MESSAGE, alarm.getLabel());
            } catch (Exception e) {
                e.printStackTrace();
                alarm.setLabel("");
            }
            if (!TextUtils.isEmpty(alarm.getUUID())) {
                mSerializer.attribute("", ClockContract.Alarm.ALARM_UUID, alarm.getUUID());
            }
            Uri alert = alarm.getAlert();
            mSerializer.attribute("", ClockContract.Alarm.MESSAGE, alarm.getLabel());
            mSerializer.attribute("", ClockContract.Alarm.SNOOZE, Integer.toString(alarm.getSnoonzeItem()));
            mSerializer.attribute("", ClockContract.Alarm.ALERT, (alert != null) ? alert.toString() : "");
            mSerializer.attribute("", ClockContract.Alarm.VOLUME, Integer.toString(alarm.getVolume()));
            mSerializer.attribute("", ClockContract.Alarm.BACKGROUND, "");
            mSerializer.attribute("", ClockContract.Alarm.VIBRATE, Integer.toString(alarm.getVibrate()));
            mSerializer.attribute("", ClockContract.Alarm.WORKDAY_SWITCH, Integer.toString(alarm.getWorkdaySwitch()));
            mSerializer.attribute("", ClockContract.Alarm.HOLIDAY_SWITCH, Integer.toString(alarm.getHolidaySwitch()));
            //Spotify新增
            if ((alert != null) && AlarmSpotifyUtils.INSTANCE.isSpotifyRing(alert.toString())) {
                mSerializer.attribute("", ClockContract.Alarm.ALERT_RINGNAME, alarm.getRingName());
            }
            //13.0新增
            mSerializer.attribute("", ClockContract.Alarm.SNOOZE_TIME, Integer.toString(alarm.getmSnoozeTime()));
            mSerializer.attribute("", ClockContract.Alarm.WORKDAY_TYPE, Integer.toString(alarm.getmWorkDayType()));
            mSerializer.attribute("", ClockContract.Alarm.WORKDAY_UPDATE_TIME, String.valueOf(alarm.getmWorkdayUpdateTime()));
            mSerializer.attribute("", ClockContract.Alarm.SPECIAL_ALARM_DAYS, alarm.getmSpecialAlarmDays());
            mSerializer.attribute("", ClockContract.Alarm.RING_NUMBER, Integer.toString(alarm.getRingNum()));
            mSerializer.attribute("", ClockContract.Alarm.DEFAULT_ALARM, Integer.toString(alarm.getmDefaultAlarm()));
            //轮班闹钟新增
            mSerializer.attribute("", ClockContract.Alarm.LOOP_SWITCH, Integer.toString(alarm.getmLoopSwitch()));
            mSerializer.attribute("", ClockContract.Alarm.LOOP_CYCLE_DAYS, Integer.toString(alarm.getmLoopCycleDays()));
            mSerializer.attribute("", ClockContract.Alarm.LOOP_ID, Integer.toString(alarm.getmLoopID()));
            mSerializer.attribute("", ClockContract.Alarm.LOOP_WORK_DAYS, Integer.toString(alarm.getmLoopWorkDays()));
            mSerializer.attribute("", ClockContract.Alarm.LOOP_DAY, Integer.toString(alarm.getmLoopDay()));
            mSerializer.attribute("", ClockContract.Alarm.LOOP_ALARM_NUMBER, Integer.toString(alarm.getmLoopAlarmNumber()));
            mSerializer.attribute("", ClockContract.Alarm.GARB_ALARM_SWITCH, Integer.toString(alarm.getmGarbSwitch()));
            if (alarm.getmLoopSwitch() == 1) {
                //包装轮班闹钟子闹钟数据
                String loopAlarmStr = LoopAlarmUtils.buildLoopAlarmBackupStr((int) alarm.getId(), alarm.getmLoopRestDays(), context);
                mSerializer.attribute("", ClockContract.Alarm.LOOP_RESET_DAYS, loopAlarmStr);
            }
            if (alarm.getmGarbSwitch() == 1) {
                mSerializer.attribute("", ClockContract.Alarm.SPECIAL_ALARM_DAYS,
                        DatePickerUtils.SPLIT);
                mSerializer.attribute("", ClockContract.Alarm.LOOP_RESET_DAYS, alarm.getmLoopRestDays());
                mSerializer.attribute("", ClockContract.Alarm.GARB_ALARM_DATE, alarm.getmSpecialAlarmDays());
            }

            mSerializer.endTag("", BackUpConstant.CLOCK_ROOT);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    String getXmlInfo() {
        if (mStringWriter != null) {
            return mStringWriter.toString();
        }

        return null;
    }

    boolean addOneCity(City city) {
        boolean result = false;
        try {
            mSerializer.startTag("", BackUpConstant.WORLD_CLOCK_ROOT);
            mSerializer.attribute("", ClockContract.City.CITY_ID, Integer.toString(city.getCityId()));
            mSerializer.attribute("", BackUpConstant.ENNAME, city.getName());
            mSerializer.attribute("", ClockContract.City.TIMEZONE_ID, city.getTimezone());
            mSerializer.attribute("", ClockContract.City.SORT_ORDER,
                    Integer.toString(city.getSortPos()));
            mSerializer.attribute("", ClockContract.City.FLAG, Integer.toString(city.getFlag()));
            mSerializer.attribute("", ClockContract.City.FLAG2, Integer.toString(city.getFlag2()));
            mSerializer.endTag("", BackUpConstant.WORLD_CLOCK_ROOT);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean addOneTimerRecord(OplusTimer timer) {
        boolean result = false;
        try {
            Uri alert = Uri.parse(timer.getRing());
            mSerializer.startTag("", OplusTimer.ROOT);
            mSerializer.attribute("", ClockContract.TimerTableColumns._ID, Integer.toString(timer.getTimerIndex()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.DURATION, Long.toString(timer.getDuration()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.DESCRIPTION, (timer.getDescription() != null) ? timer.getDescription() : "");
            mSerializer.attribute("", ClockContract.TimerTableColumns.FLAG, Integer.toString(timer.getFlag()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.SELECTED, Integer.toString(timer.getSelected()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.RING, (alert != null) ? alert.toString() : "");
            mSerializer.attribute("", ClockContract.TimerTableColumns.RINGNAME, (timer.getRingName() != null) ? timer.getRingName() : "");
            mSerializer.endTag("", OplusTimer.ROOT);

            mSerializer.startTag("", OplusTimer.LOW_TIMER_ROOT);
            mSerializer.attribute("", ClockContract.TimerTableColumns._ID, Integer.toString(timer.getTimerIndex()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.DURATION, Long.toString(timer.getDuration()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.DESCRIPTION, (timer.getDescription() != null) ? timer.getDescription() : "");
            mSerializer.attribute("", ClockContract.TimerTableColumns.FLAG, Integer.toString(timer.getFlag()));
            mSerializer.attribute("", ClockContract.TimerTableColumns.SELECTED, Integer.toString(timer.getSelected()));
            mSerializer.endTag("", OplusTimer.LOW_TIMER_ROOT);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean addTimerRecord(Context context) {
        boolean result = false;
        try {
            mSerializer.startTag("", BackUpConstant.TIMER_ROOT);
            mSerializer.attribute("", TimerConstant.TIMER_STATUS_START_PREFERENCE,
                    Boolean.toString(PrefUtils.getBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            TimerConstant.TIMER_STATUS_START_PREFERENCE, false)));
            mSerializer.attribute("", TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE,
                    Boolean.toString(PrefUtils.getBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false)));
            mSerializer.attribute("", BackUpConstant.TIMER_NEED_TO_ALARM_PREFERENCE,
                    Boolean.toString(PrefUtils.getBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_NEED_TO_ALARM_PREFERENCE, false)));
            mSerializer.attribute("", BackUpConstant.TIMER_SET_TIME_PREFERENCE,
                    Long.toString(PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_SET_TIME_PREFERENCE, 0)));
            mSerializer.attribute("", BackUpConstant.TIMER_DATA_PREFERENCE,
                    Long.toString(PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_DATA_PREFERENCE, 0)));
            mSerializer.attribute("", BackUpConstant.TIMER_DATA_START_PREFERENCE,
                    Long.toString(PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_DATA_START_PREFERENCE, 0)));
            mSerializer.attribute("", BackUpConstant.TIMER_DATA_TOTAL_TIME_PREFERENCE,
                    Long.toString(PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_DATA_TOTAL_TIME_PREFERENCE, 0)));
            mSerializer.attribute("", BackUpConstant.TIMER_STATUS_PREFERENCE,
                    Integer.toString(PrefUtils.getInt(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            BackUpConstant.TIMER_STATUS_PREFERENCE, 0)));

            mSerializer.endTag("", BackUpConstant.TIMER_ROOT);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean addAlarmCloseModelAndRed(Context context) {
        boolean result = false;
        try {
            mSerializer.startTag("", BackUpConstant.ALARM_CLOSE_MODEL_AND_RED);
            //响铃方式与红点处理
            int closeModel = AlarmCloseModelUtils.Companion.getSInstance().getAlertCloseModel(context);
            boolean closeModeRed = PrefUtils.getBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    AlarmCloseModelUtils.SETTING_ALARM_CLOSE_HIDE_RED_DOT, false);
            if (closeModel == -1) {
                closeModel = AlarmCloseModelUtils.CLOSE_MODEL_BUTTON;
            } else {
                closeModeRed = true;
            }
            mSerializer.attribute("", AlarmCloseModelUtils.SETTING_ALARM_CLOSE_MODEL, Integer.toString(closeModel));
            mSerializer.attribute("", AlarmCloseModelUtils.SETTING_ALARM_CLOSE_HIDE_RED_DOT, Boolean.toString(closeModeRed));
            mSerializer.endTag("", BackUpConstant.ALARM_CLOSE_MODEL_AND_RED);
            result = true;
        } catch (Exception e) {
            Log.e(TAG, "addAlarmCloseModelAndRed error: " + e.getMessage());
        }

        return result;
    }

    /**
     * 添加默认闹钟铃声/振动类型
     *
     * @param context
     * @return
     */
    boolean addAlarmDefaultRingAndVibrate(Context context) {
        boolean result = false;
        try {
            mSerializer.startTag("", BackUpConstant.ALARM_DEFAULT_RING_URI);
            Uri uri = AlarmRingUtils.getDefaultRingtoneUri(context, true);
            String ringPath = DEFAULT_ALARM_SETTING_URI;
            if (uri != null) {
                //转化为铃声地址
                ringPath = AlarmRingUtils.getRightAlertUri(uri.toString(), context);
            }
            if (TextUtils.isEmpty(ringPath)) {
                ringPath = DEFAULT_ALARM_SETTING_URI;
            }
            mSerializer.attribute("", PrefUtils.ALARM_DEFAULT_RING_URI, ringPath);
            mSerializer.attribute("", PrefUtils.ALARM_DEFAULT_VIBRATE_TYPE,
                    Integer.toString(AlarmRingUtils.getDefaultVibrate(context)));
            mSerializer.endTag("", BackUpConstant.ALARM_DEFAULT_RING_URI);
            result = true;
        } catch (Exception e) {
            Log.e(TAG, "addAlarmDefaultRingAndVibrate error: " + e.getMessage());
        }
        return result;
    }

    boolean addOneAlarmRepeat(AlarmRepeat repeat) {
        boolean result = false;
        try {
            Log.d(TAG, "addOneAlarmRepeat : " + repeat);
            mSerializer.startTag("", BackUpConstant.ALARM_REPEAT_ROOT);
            mSerializer.attribute("", ClockContract.AlarmsRepeat._ID, Long.toString(repeat.getmId()));
            mSerializer.attribute("", ClockContract.AlarmsRepeat.ALARM_DURATION, Integer.toString(repeat.getmAlarmDuration()));
            //响铃间隔
            mSerializer.attribute("", ClockContract.AlarmsRepeat.ALARM_INTERVAL, Integer.toString(ClockConstant.SNOOZE_AFTER_MIN));
            //响铃次数
            mSerializer.attribute("", ClockContract.AlarmsRepeat.ALARM_NUM, Integer.toString(ClockConstant.SNOOZE_RING_NUM));
            mSerializer.attribute("", ClockContract.AlarmsRepeat.ALARM_PROMPT, Integer.toString(repeat.getmAlarmPrompt()));
            mSerializer.endTag("", BackUpConstant.ALARM_REPEAT_ROOT);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    boolean addShowNextAlarmNotices(Context context) {
        boolean result = false;
        try {
            boolean isNeedShowNextAlarm = AlarmUtils.isOpenNextAlarmNotices(context);
            Log.d(TAG, "addShowNextAlarmNotices : " + isNeedShowNextAlarm);
            mSerializer.startTag("", BackUpConstant.SETTING_SHOW_NEXT_ALARM_NOTICES);
            mSerializer.attribute("", AlarmUtils.KEY_NEXT_ALARM_NOTICES, Boolean
                    .toString(isNeedShowNextAlarm));
            mSerializer.endTag("", BackUpConstant.SETTING_SHOW_NEXT_ALARM_NOTICES);
            result = true;
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(TAG, "addShowNextAlarmNotices error: " + e.getMessage());
        }

        return result;
    }

    /**
     * 添加铃声渐强数据
     * @param context
     * @return
     */
    boolean addBellGraduallyRings(Context context) {
        boolean result = false;
        try {
            boolean isBellGraduallyRings = AlarmUtils.isOpenBellGraduallyRings(context);
            Log.d(TAG, "addBellGraduallyRings : " + isBellGraduallyRings);
            mSerializer.startTag("", BackUpConstant.SETTING_GRADUALLY_RINGS);
            mSerializer.attribute("", AlarmUtils.KEY_BELL_GRADUALLY_RINGS, Boolean.toString(isBellGraduallyRings));
            mSerializer.endTag("", BackUpConstant.SETTING_GRADUALLY_RINGS);
            result = true;
        } catch (IOException e) {
            Log.e(TAG, "addBellGraduallyRings error: " + e.getMessage());
        }
        return result;
    }


    boolean addMorningData(Context context) {
        boolean result = false;
        Log.d(TAG, "addMorningData : ");
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance()) && (context != null) && (mSerializer != null)) {
            boolean morningState = PlayMorningTools.isMorningReportEnable(context);
            boolean isShow = PrefUtils.getBoolean(context, MorningAlarmClock.MORNING_PREFERENCE, MorningAlarmClock.MORNING_IS_SHOWN, false);
            try {
                Log.d(TAG, "morning_state : " + morningState);
                mSerializer.startTag("", BackUpConstant.MORNING_SETTING_STATUS);
                mSerializer.attribute("", MorningAlarmClock.MORNING_STATE, Boolean.toString(morningState));
                mSerializer.attribute("", MorningAlarmClock.MORNING_IS_SHOWN, Boolean.toString(isShow));
                mSerializer.endTag("", BackUpConstant.MORNING_SETTING_STATUS);
                result = true;
            } catch (Exception e) {
                Log.e(TAG, "addMorningData " + e.getMessage());
            }
        }
        return result;
    }

    /**
     * 添加负一屏世界时钟小卡的城市数据，只保留cityId 和 widgetCode
     */
    boolean addDialClockCityData(Context context) {
        boolean result = false;
        if (context == null) {
            return false;
        }
        try {
            Map<String, String> allPrefs = (Map<String, String>) PrefUtils.getAll(context, Constants.DIAL_CLOCK_PREF_FILE_NAME);
            if (allPrefs == null || allPrefs.isEmpty()) {
                return false;
            }
            for (Map.Entry<String, String> entry : allPrefs.entrySet()) {
                String key = entry.getKey();
                if (key.contains(Constants.EXTRA_DIAL_CLOCK_CITY_ID)) {
                    String cityId = entry.getValue();
                    String widgetCode = DialClockViewModel.INSTANCE.splitClockKey(Constants.EXTRA_DIAL_CLOCK_CITY_ID, key);
                    mSerializer.startTag("", BackUpConstant.DIAL_CLOCK_CITY_DATA);
                    mSerializer.attribute("", BackUpConstant.DIAL_CLOCK_ATTR_WIDGET_CODE, widgetCode);
                    mSerializer.attribute("", BackUpConstant.DIAL_CLOCK_ATTR_CITY_ID, cityId);
                    mSerializer.endTag("", BackUpConstant.DIAL_CLOCK_CITY_DATA);
                    Log.d(TAG, "addDialClockCityData cityId " + cityId + " widgetCode " + widgetCode);
                }
            }
            result = true;
        } catch (ClassCastException | IOException e) {
            Log.d(TAG, "addDialClockCityData e: " + e.getMessage());
        }
        return result;
    }


    boolean addSetWorkday(Context context) {
        boolean result = false;
        if (context != null) {
            try {
                long setWorkDayTypeTime = WorkDayTypeUtils.getSetWorkDayTypeTime(context);
                if (setWorkDayTypeTime > 0) {
                    int setWorkDayType = WorkDayTypeUtils.getSetWorkDayType(context);
                    Log.d(TAG, "addSetWorkday  setWorkDayType = " + setWorkDayType + "  setWorkDayTypeTime = " + setWorkDayTypeTime);
                    mSerializer.startTag("", BackUpConstant.SETTING_SET_WORKDAY_ROOT);
                    mSerializer.attribute("", BackUpConstant.SETTING_SET_WORKDAY_TYPE, String.valueOf(setWorkDayType));
                    mSerializer.attribute("", BackUpConstant.SETTING_SET_WORKDAY_TYPE_TIME, String.valueOf(setWorkDayTypeTime));
                    mSerializer.endTag("", BackUpConstant.SETTING_SET_WORKDAY_ROOT);
                    result = true;
                }
            } catch (IOException e) {
                e.printStackTrace();
                Log.e(TAG, "addSetWorkday error: " + e.getMessage());
            }
        }
        return result;
    }
}