/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : Clock Restore Data
 * History :( ID, Date, Author, Description)
 * v1.0, 2017-06-26, xuan.zhou, create
 ***********************************************************/
//OPLUS Java File Skip Rule:NestedBranchDepth
package com.oplus.alarmclock.backup;

import static com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper.isSupportGarbAlarm;

import android.annotation.SuppressLint;
import android.content.ContentProviderOperation;
import android.content.ContentProviderOperation.Builder;
import android.content.ContentProviderResult;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.ParcelFileDescriptor;
import android.provider.Settings;
import android.text.TextUtils;

import com.oplus.alarmclock.ai.AiAlarmUtils;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.appfunctions.ClockAppSearchManager;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.CityUtils;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.timer.Adapter.TimerAdapter;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.alarmclock.utils.IBaseChannel;
import com.oplus.alarmclock.utils.LoopAlarmUtils;
import com.oplus.clock.common.osdk.IntentNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.backup.sdk.common.host.BREngineConfig;
import com.oplus.backup.sdk.compat.DataSizeUtils;
import com.oplus.backup.sdk.component.BRPluginHandler;
import com.oplus.backup.sdk.component.plugin.RestorePlugin;
import com.oplus.backup.sdk.host.listener.ProgressHelper;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClockRestorePlugin extends RestorePlugin {
    private static final String TAG = "ClockRestorePlugin";
    private static final boolean DEBUG = true;

    private static final int TYPE_CLOCK = 288;
    private static final int BUFFER_BYTE = 512;

    private final Object mLock = new Object();

    private int mClockIndex = 0;
    private int mTimerIndex = 0;

    private ArrayList<Alarm> mAlarmRecordList;
    private ArrayList<City> mWorldClockRecordList;
    private ArrayList<OplusTimer> mTimerRecordList;
    private ArrayList<AlarmRepeat> mAlarmRepeatList;
    private ArrayList<ContentProviderOperation> mDbOps;

    /**
     * 子闹钟列表
     */
    private Map<Integer, List<Alarm>> mLoopAlarm = new HashMap<>();

    private int mCompletedCount = 0;
    private int mMaxCount = -1;
    private boolean mIsCancel;
    private boolean mIsPause;
    private String mDefaultAlarmMediaUri;
    private BRPluginHandler mBRPluginHandler;

    private Context mContext;

    @Override
    public void onCreate(Context context, BRPluginHandler brPluginHandler, BREngineConfig config) {
        mContext = context;
        if (mContext != null) {
            mDefaultAlarmMediaUri = Settings.System.getString(mContext.getContentResolver(),
                    Settings.System.DEFAULT_ALARM_ALERT_URI.toString());
        }
        super.onCreate(context, brPluginHandler, config);
        mBRPluginHandler = brPluginHandler;
        Log.i(TAG, "onCreate: mDefaultAlarmMediaUri: " + mDefaultAlarmMediaUri);
    }

    @Override
    public Bundle onPrepare(Bundle bundle) {
        onStart();
        if (mMaxCount < 0) {
            mMaxCount = getMaxCount();
        }
        Bundle preview = new Bundle();
        ProgressHelper.putMaxCount(preview, mMaxCount);
        Log.i(TAG, "onPrepare mMaxCount: " + mMaxCount);
        return preview;
    }

    @Override
    public Bundle onPreview(Bundle arg0) {
        if (mMaxCount < 0) {
            mMaxCount = getMaxCount();
        }
        Bundle preview = new Bundle();
        ProgressHelper.putMaxCount(preview, mMaxCount);
        long size = DataSizeUtils.estimateSize(TYPE_CLOCK, mMaxCount);
        ProgressHelper.putPreviewDataSize(preview, size);
        Log.i(TAG, "onPreview estimateSize: " + size);
        return preview;
    }

    @SuppressLint("WrongConstant")
    @Override
    public void onRestore(Bundle arg0) {
        if (DEBUG) {
            Log.i(TAG, "onRestore");
        }
        if (mMaxCount > 0) {
            int worldClockIndex = 0;
            while ((!mIsCancel) && (mMaxCount > 0) && (mCompletedCount < mMaxCount)) {
                synchronized (mLock) {
                    while (mIsPause) {
                        try {
                            Log.i(TAG, "on pause wait lock here");
                            mLock.wait();
                        } catch (InterruptedException e) {
                            Log.e(TAG, "onRestore error: " + e.getMessage());
                        }
                    }
                }
                if ((mAlarmRecordList != null) && (mClockIndex < mAlarmRecordList.size())) {
                    Alarm alarm = mAlarmRecordList.get(mClockIndex++);
                    if (alarm.getmLoopSwitch() == 1) {
                        //轮班闹钟
                        List<Alarm> loopAlarmList = LoopAlarmUtils.INSTANCE.parserLoopAlarmBackupStr(alarm.getmLoopRestDays(),
                                (int) alarm.getId(), alarm);
                        mLoopAlarm.put(mDbOps.size(), loopAlarmList);
                    }
                    parserGarbAlarm(alarm);
                    if (DEBUG) {
                        Log.d(TAG, "mClockIndex=" + mClockIndex);
                    }
                    Builder builder = insertAlarm(alarm);
                    mDbOps.add(builder.build());
                } else if ((mWorldClockRecordList != null)
                        && (worldClockIndex < mWorldClockRecordList.size())) {
                    if (worldClockIndex == 0) {
                        ContentValues values = new ContentValues();
                        values.put(ClockContract.City.FLAG, 0);
                        if (mContext != null) {
                            mContext.getContentResolver().update(ClockContract.City.NEW_CITY_CONTENT_URI,
                                    values, null, null);
                        }
                    }
                    City city = mWorldClockRecordList.get(worldClockIndex++);
                    if (DEBUG) {
                        Log.d(TAG, "mWorldClockIndex=" + worldClockIndex);
                    }
                    insertWorldClock(city);
                } else if ((mTimerRecordList != null) && (mTimerIndex < mTimerRecordList.size())) {
                    OplusTimer timer = mTimerRecordList.get(mTimerIndex++);
                    Log.d(TAG, "onRestore timer:" + timer.toString() + " mTimerIndex:" + mTimerIndex);
                    Builder builder = insertTimer(timer);
                    mDbOps.add(builder.build());
                } else if (mAlarmRepeatList != null) {

                    if (mAlarmRepeatList.size() > 0) {
                        Log.d(TAG, "mAlarmRepeatList.size()  >0 ");
                        AlarmRepeat alarmRepeat = mAlarmRepeatList.get(0);
                        Log.d(TAG, "onRestore alarmRepeat:" + alarmRepeat.toString());
                        Builder builder = insertAlarmRepeat(alarmRepeat);
                        mDbOps.add(builder.build());
                    } else {
                        Log.e(TAG, "mAlarmRepeatList.size()  == 0 ");
                    }

                } else {
                    Log.e(TAG, "onRestore error!");
                }

                if (mCompletedCount < mMaxCount) {
                    mCompletedCount++;
                    if ((mCompletedCount == mMaxCount) && (!mIsCancel)) {
                        try {
                            if (mContext != null) {
                                ContentProviderResult[] result = mContext.getContentResolver().applyBatch(ClockContract.AUTHORITY, mDbOps);
                                //处理轮班闹钟子闹钟
                                for (int next : mLoopAlarm.keySet()) {
                                    if (result.length > next) {
                                        Uri reset = result[next].uri;
                                        if (reset != null) {
                                            long alarmId = ContentUris.parseId(reset);
                                            //添加子闹钟
                                            List<Alarm> loopList = mLoopAlarm.get(next);
                                            LoopAlarmUtils.saveSubLoopAlarm(mContext, loopList, (int) alarmId);
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "batch insert Clock failed.", new Throwable(e.toString()));
                            mCompletedCount = -1;
                        } finally {
                            try {
                                if (mContext != null) {
                                    mContext.getContentResolver().notifyChange(ClockContract.ALARM_CONTENT_URI, null, AlarmUtils.NOTIFY_NO_DELAY);
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "notifyChange error", new Throwable(e.toString()));
                            }
                        }
                    }
                }
                ClockAppSearchManager.initAlarms(mContext, false);
                Bundle progress = new Bundle();
                ProgressHelper.putMaxCount(progress, mMaxCount);
                ProgressHelper.putCompletedCount(progress, mCompletedCount);
                if (mBRPluginHandler != null) {
                    mBRPluginHandler.updateProgress(progress);
                }
            }
        }
        Log.d(TAG, "mCompletedCount:" + mCompletedCount);
    }

    private void parserGarbAlarm(Alarm alarm) {
        if (alarm.getmGarbSwitch() == 1) {
            //秒抢闹钟
            if (isSupportGarbAlarm()) {
                String[] arr = alarm.getmLoopRestDays().split(DatePickerUtils.SPLIT);
                List<Alarm> garbAlarmList = new ArrayList<Alarm>();
                for (String s : arr) {
                    Calendar ringTime = GarbAlarmUtils.buildSubAlarmTime(
                            Integer.parseInt(s),
                            alarm.getMinutes(),
                            alarm.getHour()
                    );
                    Alarm item = GarbAlarmUtils.buildItemAlarm(0, 0, ringTime);
                    garbAlarmList.add(item);
                }
                mLoopAlarm.put(mDbOps.size(), garbAlarmList);
            } else {
                //不支持秒抢版本
                alarm.setmSpecialAlarmDays(DatePickerUtils.SPLIT);
                alarm.setmLoopRestDays(DatePickerUtils.SPLIT);
                alarm.setmGarbSwitch(0);
            }
        }
    }

    private void onStart() {
        BREngineConfig config = getBREngineConfig();
        if (config == null) {
            Log.d(TAG, "onStart config is null");
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ClockAppSearchManager.deleteAllAlarms(mContext);
        }
        String path = config.getRestoreRootPath() + File.separator + "Clock" + File.separator
                + ClockBackupPlugin.CLOCK_XML;
        Log.d(TAG, "onStart(): " + path);
        String content = getXmlInfo(path);

        Log.d(TAG, "onStart() content = " + content);

        mDbOps = new ArrayList<>();
        if (content != null) {
            HashMap<Integer, ArrayList> map = ClockXmlParser.parse(content, mContext);
            mAlarmRecordList = map.get(0);
            Log.v(TAG, "onStart() mAlarmRecordList = " + mAlarmRecordList);
            mWorldClockRecordList = map.get(1);
            Log.v(TAG, "onStart() mWorldClockRecordList = " + mWorldClockRecordList);
            mTimerRecordList = map.get(2);
            Log.d(TAG, "onStart() mTimerRecordList = " + mTimerRecordList);
            final int index3 = 3;
            if (null != map.get(index3)) {
                mAlarmRepeatList = map.get(index3);
                Log.d(TAG, "onStart() mAlarmRepeatList = " + mAlarmRepeatList);
            }
            modifyTimerList(mTimerRecordList);
        }
        AiAlarmUtils.delAllAlarms(mContext);
    }

    /**
     * Used to check restored data and the timers in new phone, prevent from timers out of bounds, because
     * we only hold #TimerViewPagerAdapter.TIMER_PAGE_ITEM_MAX_NUM timers.
     *
     * @param list The timers which needs to be restored.
     */
    private void modifyTimerList(ArrayList<OplusTimer> list) {
        if (list != null) {
            ArrayList<OplusTimer> timersInDB = TimerDataHelper.getTimersInDB(mContext);
            while ((timersInDB.size() + list.size()) > TimerAdapter.TIMER_PAGE_ITEM_MAX_NUM) {
                int index = list.size() - 1;
                if (index >= 0) {
                    list.remove(index);
                } else {
                    Log.e(TAG, "modifyTimerList data out of bounds, remove data in new "
                            + "phone to keep correct");
                    if (timersInDB.size() > 0) {
                        timersInDB.remove(timersInDB.size() - 1);
                    }
                }
            }
        }
    }

    @Override
    public void onPause(Bundle arg0) {
        if (DEBUG) {
            Log.v(TAG, "onPause()");
        }
        mIsPause = true;
    }

    @Override
    public void onCancel(Bundle arg0) {
        mIsCancel = true;
        mIsPause = false;
        synchronized (mLock) {
            mLock.notifyAll();
            Log.i(TAG, "onCancel mLock.notifyAll()");
        }
    }

    @Override
    public void onContinue(Bundle arg0) {
        mIsPause = false;
        synchronized (mLock) {
            mLock.notifyAll();
            Log.i(TAG, "onContinue mLock.notifyAll()");
        }
    }

    @Override
    public Bundle onDestroy(Bundle arg0) {
        onEnd();
        Bundle result = new Bundle();
        ProgressHelper.putBRResult(result, mIsCancel ? ProgressHelper.BR_RESULT_CANCEL : ProgressHelper.BR_RESULT_SUCCESS);
        ProgressHelper.putMaxCount(result, mMaxCount);
        ProgressHelper.putCompletedCount(result, mCompletedCount);
        Log.i(TAG, "onDestroy =" + result);
        return result;
    }

    @SuppressLint("WrongConstant")
    private void onEnd() {
        if (mAlarmRecordList != null) {
            mAlarmRecordList.clear();
        }

        if (mWorldClockRecordList != null) {
            mWorldClockRecordList.clear();
        }

        if (mTimerRecordList != null) {
            mTimerRecordList.clear();
        }

        if (mAlarmRecordList != null) {
            mAlarmRecordList.clear();
        }

        if (mDbOps != null) {
            mDbOps = null;
        }

        if (DEBUG) {
            Log.v(TAG, "onEnd()");
        }

        if (mContext != null) {
            Intent intent = new Intent();
            intent.setAction(BackUpConstant.CLOCK_DATA_CHANGE);
            intent.setPackage(mContext.getPackageName());
            intent.addFlags(IntentNativeUtils.getFLAG_RECEIVER_INCLUDE_BACKGROUND());
            mContext.sendBroadcast(intent);
        }
    }

    private String getXmlInfo(String fileName) {
        InputStream is = null;
        try {
            is = new FileInputStream(
                    getFileDescriptor(fileName, ParcelFileDescriptor.MODE_READ_WRITE));
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int len = -1;
            byte[] buffer = new byte[BUFFER_BYTE];
            while ((len = is.read(buffer, 0, BUFFER_BYTE)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toString(StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    private void insertWorldClock(City city) {
        if (DEBUG) {
            Log.v(TAG, "zhName=" + city.getName() + " sorted = " + city.getSortPos());
        }
        String selection = ClockContract.City.CITY_ID + " = ?";
        int cityID = CityUtils.changeToShowDomesticOrExp(mContext, city.getCityId());
        if (cityID != -1) {
            ContentValues values1 = new ContentValues();
            ContentValues values2 = new ContentValues();
            values1.put(ClockContract.City.FLAG, 0);
            values1.put(ClockContract.City.SORT_ORDER, 0);
            values2.put(ClockContract.City.FLAG, city.getFlag());
            values2.put(ClockContract.City.SORT_ORDER, city.getSortPos());
            if (mContext != null) {
                mContext.getContentResolver().update(ClockContract.City.NEW_CITY_CONTENT_URI,
                        values1, selection, new String[]{String.valueOf(city.getCityId())});
                mContext.getContentResolver().update(ClockContract.City.NEW_CITY_CONTENT_URI,
                        values2, selection, new String[]{String.valueOf(cityID)});
            }
        } else {
            ContentValues values = new ContentValues();
            values.put(ClockContract.City.FLAG, city.getFlag());
            values.put(ClockContract.City.SORT_ORDER, city.getSortPos());
            if (mContext != null) {
                mContext.getContentResolver().update(ClockContract.City.NEW_CITY_CONTENT_URI,
                        values, selection, new String[]{String.valueOf(city.getCityId())});
            }
        }
    }

    private Builder insertAlarm(Alarm alarm) {
        ContentValues values = new ContentValues();
        values.put(ClockContract.Alarm.HOUR, alarm.getHour());
        values.put(ClockContract.Alarm.MINUTES, alarm.getMinutes());
        values.put(ClockContract.Alarm.ENABLED, alarm.isEnabled() ? 1 : 0);
        values.put(ClockContract.Alarm.ALERTTYPE, alarm.getAlertType());
        values.put(ClockContract.Alarm.MESSAGE, alarm.getLabel());
        values.put(ClockContract.Alarm.SNOOZE, alarm.getSnoonzeItem());
        values.put(ClockContract.Alarm.VIBRATE, alarm.getVibrate());
        values.put(ClockContract.Alarm.VOLUME, alarm.getVolume());
        if (!TextUtils.isEmpty(alarm.getUUID())) {
            values.put(ClockContract.Alarm.ALARM_UUID, alarm.getUUID());
        }
        if (Utils.isAboveQ()) {
            values.put(ClockContract.Alarm.OWNER_USER_ID, AppPlatformUtils.getCurrentUser());
        }
        String ringUri = (alarm.getAlert() != null) ? alarm.getAlert().toString() : null;
        if ((ringUri != null) && AlarmSpotifyUtils.INSTANCE.isSpotifyRing(ringUri)) {
            values.put(ClockContract.Alarm.ALERT_RINGNAME, alarm.getRingName());
        }
        String ringUriStr = AlarmRingUtils.checkRingUri(ringUri, mContext);
        Log.d(TAG, "insertAlarm: ringUri: " + ringUriStr);
        values.put(ClockContract.Alarm.ALERT, ringUriStr);
        if (DeviceUtils.isExpVersion(mContext)) {
            if (alarm.getWorkdaySwitch() == 1) {
                alarm.setWorkdaySwitch(0);
                alarm.setHolidaySwitch(0);
                alarm.setRepeat(RepeatSet.REPEAT_DAILY);
            } else if (alarm.getHolidaySwitch() == 1) {
                alarm.setHolidaySwitch(0);
            }
        }
        values.put(ClockContract.Alarm.DAYS_OF_WEEK, alarm.getRepeatSet());
        values.put(ClockContract.Alarm.WORKDAY_SWITCH, alarm.getWorkdaySwitch());
        values.put(ClockContract.Alarm.HOLIDAY_SWITCH, alarm.getHolidaySwitch());
        //13.0新增
        if ((alarm.getWorkdaySwitch() == 1) && (alarm.getmWorkdayUpdateTime() == 0)) {
            values.put(ClockContract.Alarm.WORKDAY_UPDATE_TIME, System.currentTimeMillis());
        } else {
            values.put(ClockContract.Alarm.WORKDAY_UPDATE_TIME, alarm.getmWorkdayUpdateTime());
        }
        values.put(ClockContract.Alarm.WORKDAY_TYPE, alarm.getmWorkDayType());
        if ((mAlarmRepeatList != null) && (mAlarmRepeatList.size() > 0) && (alarm.getSnoonzeItem() == ClockConstant.SNOOZE_SWITCH_ON_5_MIN)) {
            //为-1表示从S搬家
            if ((mAlarmRepeatList.get(0).getmAlarmInterval() > 0) && (alarm.getmSnoozeTime() == -1)) {
                //12的响铃间隔数据迁移到13设置到每个闹钟上
                alarm.setmSnoozeTime(mAlarmRepeatList.get(0).getmAlarmInterval());
            }
            if ((mAlarmRepeatList.get(0).getmAlarmNum() > 0) && (alarm.getRingNum() == -1)) {
                //迁移响铃次数
                int ringNum = mAlarmRepeatList.get(0).getmAlarmNum();
                if (ringNum == 1) {
                    ringNum = 2;
                }
                alarm.setRingNum(ringNum);
            }
        }
        if (alarm.getRingNum() == -1) {
            alarm.setRingNum(ClockConstant.SNOOZE_RING_NUM);
        }
        if (alarm.getmSnoozeTime() == -1) {
            alarm.setmSnoozeTime(ClockConstant.SNOOZE_AFTER_MIN);
        }
        values.put(ClockContract.Alarm.GARB_ALARM_SWITCH, alarm.getmGarbSwitch());
        values.put(ClockContract.Alarm.SNOOZE_TIME, alarm.getmSnoozeTime());
        values.put(ClockContract.Alarm.RING_NUMBER, alarm.getRingNum());
        if ((DeviceUtils.isExpVersion(mContext) && (ChannelManager.INSTANCE.getChannelUtils().getChannel() == IBaseChannel.CHANNEL_WPLUS))
                || alarm.getmGarbSwitch() == 1) {
            values.put(ClockContract.Alarm.SPECIAL_ALARM_DAYS, alarm.getmSpecialAlarmDays());
        } else {
            if (!alarm.getmSpecialAlarmDays().equals(DatePickerUtils.SPLIT) && alarm.getRepeatSet() == 0) {
                //指定日期闹钟，无重复规则改为响一次，默认关闭
                values.put(ClockContract.Alarm.ENABLED, 0);
            }
            values.put(ClockContract.Alarm.SPECIAL_ALARM_DAYS, DatePickerUtils.SPLIT);
        }
        values.put(ClockContract.Alarm.DEFAULT_ALARM, alarm.getmDefaultAlarm());
        values.put(ClockContract.Alarm.LOOP_SWITCH, alarm.getmLoopSwitch());
        values.put(ClockContract.Alarm.LOOP_CYCLE_DAYS, alarm.getmLoopCycleDays());
        values.put(ClockContract.Alarm.LOOP_ID, alarm.getmLoopID());
        values.put(ClockContract.Alarm.LOOP_WORK_DAYS, alarm.getmLoopWorkDays());
        values.put(ClockContract.Alarm.LOOP_DAY, alarm.getmLoopDay());
        values.put(ClockContract.Alarm.LOOP_ALARM_NUMBER, alarm.getmLoopAlarmNumber());
        values.put(ClockContract.Alarm.LOOP_RESET_DAYS, alarm.getmLoopRestDays());

        Uri alarmAlertUri = null;
        try {
            alarmAlertUri = Uri.parse(ringUriStr);
        } catch (Exception e) {
            Log.e(TAG, "insertAlarm parse uri error:" + e.getMessage());
        }
        values.put(ClockContract.Alarm.RING_ABSOLUTE_PATH, AlarmRingUtils.getRingAbsolutePath(mContext, alarmAlertUri));
        Builder builder = ContentProviderOperation
                .newInsert(ClockContract.ALARM_CONTENT_URI);
        builder.withValues(values);
        return builder;
    }

    private Builder insertTimer(OplusTimer timer) {
        Builder builder = ContentProviderOperation
                .newInsert(ClockContract.TIMER_CONTENT_URI);
        ContentValues values = new ContentValues();
        values.put(ClockContract.TimerTableColumns.DESCRIPTION, timer.getDescription());
        values.put(ClockContract.TimerTableColumns.DURATION, timer.getDuration());
        values.put(ClockContract.TimerTableColumns.FLAG, timer.getFlag());
        values.put(ClockContract.TimerTableColumns.SELECTED, timer.getSelected());
        String ringUri = (timer.getRing() != null) ? timer.getRing() : null;
        String ringUriStr = AlarmRingUtils.checkRingUri(ringUri, mContext);
        values.put(ClockContract.TimerTableColumns.RING, ringUriStr);
        values.put(ClockContract.TimerTableColumns.RINGNAME, timer.getRingName());
        builder.withValues(values);
        return builder;
    }

    private Builder insertAlarmRepeat(AlarmRepeat repeat) {
        Log.d(TAG, "insertAlarmRepeat = " + repeat);
        Builder builder = ContentProviderOperation
                .newInsert(ClockContract.ALARMS_REPEAT_URI);
        ContentValues values = new ContentValues();
        values.put(ClockContract.AlarmsRepeat.ALARM_DURATION, repeat.getmAlarmDuration());
        values.put(ClockContract.AlarmsRepeat.ALARM_PROMPT, repeat.getmAlarmPrompt());
        values.put(ClockContract.AlarmsRepeat.ALARM_INTERVAL, repeat.getmAlarmInterval());
        values.put(ClockContract.AlarmsRepeat.ALARM_NUM, repeat.getmAlarmNum());
        builder.withValues(values);
        return builder;
    }

    private int getMaxCount() {
        if (mMaxCount == -1) {
            mMaxCount = 0;
            if (mAlarmRecordList != null) {
                Log.d(TAG, "getMaxCount mAlarmRecordList size = " + mAlarmRecordList.size());
                mMaxCount += mAlarmRecordList.size();
            }

            if (mWorldClockRecordList != null) {
                Log.d(TAG, "getMaxCount mWorldClockRecordList size = " + mWorldClockRecordList.size());
                mMaxCount += mWorldClockRecordList.size();
            }

            if (mTimerRecordList != null) {
                Log.d(TAG, "getMaxCount mTimerRecordList size = " + mTimerRecordList.size());
                mMaxCount += mTimerRecordList.size();
            }

            if (mAlarmRepeatList != null) {
                Log.d(TAG, "getMaxCount mAlarmRecordList size = " + mAlarmRepeatList.size());
                mMaxCount += mAlarmRepeatList.size();
            }

            if (DEBUG) {
                Log.v(TAG, "getMaxCount():" + mMaxCount);
            }
        }
        return mMaxCount;
    }

}
