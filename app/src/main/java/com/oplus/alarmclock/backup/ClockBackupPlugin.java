/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : Clock Backup Data
 * History :( ID, Date, Author, Description)
 * v1.0, 2017-06-26, xuan.zhou, create
 ***********************************************************/
package com.oplus.alarmclock.backup;

import android.annotation.SuppressLint;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;

import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.provider.AlarmContract;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.AlarmWeatherUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.backup.sdk.compat.DataSizeUtils;
import com.oplus.backup.sdk.component.BRPluginHandler;
import com.oplus.backup.sdk.component.plugin.BackupPlugin;
import com.oplus.backup.sdk.common.host.BREngineConfig;
import com.oplus.backup.sdk.host.listener.ProgressHelper;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

public class ClockBackupPlugin extends BackupPlugin {
    public static final String CLOCK_XML = "clock_backup.xml";
    private static final String TAG = "ClockBackupPlugin";
    public final static String DEFAULT_ALARM_SETTING_URI = Settings.System.DEFAULT_ALARM_ALERT_URI
            .toString();
    private static final boolean DEBUG = true;
    private static final int TYPE_CLOCK = 288;

    private static final int ALARM_CLOCK = 0;
    private static final int WORLD_CLOCK = 1;
    private static final int TIMER = 2;
    private static final int REPEAT_INFO = 3;

    private static final String[] WORLD_CLOCK_PROJECTION = {
            ClockContract.City.CITY_ID,
            ClockContract.City.CITY_NAME,
            ClockContract.City.TIMEZONE_ID,
            ClockContract.City.FLAG,
            ClockContract.City.FLAG2,
            ClockContract.City.SORT_ORDER,
            ClockContract.City.CITY_COUNTRY
    };

    private static final int CITY_ID_INDEX = 0;
    private static final int CITY_NAME_INDEX = 1;
    private static final int ID_CITY_INDEX = 2;
    private static final int FLAG_INDEX = 3;
    private static final int FLAG2_INDEX = 4;
    private static final int SORTORDER2_INDEX = 5;
    private static final int CITY_COUNTRY_INDEX = 6;


    private final Object mLock = new Object();
    private String mDefaultAlarmMediaUriStr = null;
    private ClockXmlComposer mClockXml = null;
    private ArrayList<Alarm> mClockList = null;
    private ArrayList<City> mWorldClockList = null;
    private ArrayList<OplusTimer> mTimerList = null;
    private ArrayList<AlarmRepeat> mAlarmRepeatList = null;

    private Cursor[] mCursorArray = {null, null, null, null};
    private Context mContext;
    private String mTargetPath;
    private int mCompletedCount = 0;
    private int mMaxCount = -1;
    private boolean mIsCancel;
    private boolean mIsPause;
    private BRPluginHandler mBRPluginHandler;

    @Override
    public void onCreate(Context context, BRPluginHandler brPluginHandler, BREngineConfig config) {
        Log.i(TAG, "onCreate");
        mContext = context;
        try {
            mCursorArray[ALARM_CLOCK] = mContext.getContentResolver()
                    .query(ClockContract.ALARM_CONTENT_URI, AlarmContract.INSTANCE.getQUERY_COLUMNS(), null, null, null);
            mCursorArray[WORLD_CLOCK] = mContext.getContentResolver().query(
                    ClockContract.City.NEW_CITY_CONTENT_URI,
                    WORLD_CLOCK_PROJECTION,
                    ClockContract.City.FLAG + "=1 AND " + ClockContract.City.LOCALE_LAN
                            + "='" + ClockContract.City.EN_NAME + "'",
                    null, null);
            mCursorArray[TIMER] = context.getContentResolver().query(ClockContract.TIMER_CONTENT_URI, null, null, null, null);
            mCursorArray[REPEAT_INFO] = context.getContentResolver().query(ClockContract.ALARMS_REPEAT_URI, null, null, null, ClockContract.ALARMS_REPEAT_ORDER_LIMIT);
        } catch (Exception e) {
            Log.e(TAG, "Exception: " + e);
        }

        if (mCursorArray[ALARM_CLOCK] != null) {
            mCursorArray[ALARM_CLOCK].moveToFirst();
        }
        if (mCursorArray[WORLD_CLOCK] != null) {
            mCursorArray[WORLD_CLOCK].moveToFirst();
        }
        if (mCursorArray[TIMER] != null) {
            mCursorArray[TIMER].moveToFirst();
        }

        if (mCursorArray[REPEAT_INFO] != null) {
            mCursorArray[REPEAT_INFO].moveToFirst();
        }

        mClockList = new ArrayList<>();
        mWorldClockList = new ArrayList<>();
        mTimerList = new ArrayList<>();
        mAlarmRepeatList = new ArrayList<>();

        mDefaultAlarmMediaUriStr = Settings.System.getString(mContext.getContentResolver(),
                DEFAULT_ALARM_SETTING_URI);
        super.onCreate(context, brPluginHandler, config);
        mBRPluginHandler = brPluginHandler;
    }

    private void onStart() {
        if (DEBUG) {
            Log.i(TAG, "onStart(): " + getMaxCount());
        }
        mClockXml = new ClockXmlComposer();
        mClockXml.startCompose();

        BREngineConfig config = getBREngineConfig();
        if (config == null) {
            return;
        }
        mTargetPath = config.getBackupRootPath() + File.separator + "Clock";

        File file = new File(mTargetPath);
        if (!file.exists()) {
            boolean succeed = file.mkdirs();
            if (!succeed) {
                Log.e(TAG, "mkdir failed!");
            }
        } else if (file.isFile()) {
            long time = file.lastModified();
            String newPath = mTargetPath + time;
            File newFile = new File(newPath);
            if (file.renameTo(newFile)) {
                boolean succeed = file.mkdirs();
                if (!succeed) {
                    Log.e(TAG, "mkdir failed!");
                }
            }
        }

        File file2 = new File(file.getAbsolutePath() + File.separator + CLOCK_XML);
        Log.i(TAG, "onStart(), targetPath: " + mTargetPath + ", file2: " + file2);
        if (!file2.exists()) {
            try {
                boolean result = file2.createNewFile();
                if (!result) {
                    Log.e(TAG, "Failed to create new file.");
                }
            } catch (Exception e) {
                Log.e(TAG, "onStart():file failed:" + file2.getAbsolutePath());
            }
        }
    }

    private void onEnd() {
        if (DEBUG) {
            Log.i(TAG, "onEnd");
        }

        for (Cursor cur : mCursorArray) {
            if (cur != null) {
                cur.close();
            }
        }

        for (int i = 0; i < mCursorArray.length; i++) {
            mCursorArray[i] = null;
        }

        if ((mClockList != null) && (mClockList.size() > 0)) {
            mClockList.clear();
        }
        if ((mWorldClockList != null) && (mWorldClockList.size() > 0)) {
            mWorldClockList.clear();
        }

        if ((mAlarmRepeatList != null) && (mAlarmRepeatList.size() > 0)) {
            mAlarmRepeatList.clear();
        }
    }

    @Override
    public void onBackup(Bundle arg0) {
        if (DEBUG) {
            Log.i(TAG, "onBackup");
        }
        if (mMaxCount > 0) {
            while (!mIsCancel && (mMaxCount > 0) && (mCompletedCount < mMaxCount)) {
                synchronized (mLock) {
                    while (mIsPause) {
                        try {
                            Log.i(TAG, "on pause wait lock here");
                            mLock.wait();
                        } catch (InterruptedException e) {
                            Log.e(TAG, "InterruptedException.");
                        }
                    }
                }
                if ((mCursorArray[ALARM_CLOCK] != null)
                        && (!mCursorArray[ALARM_CLOCK].isAfterLast())) {
                    Alarm alarm = parseCursor(mCursorArray[ALARM_CLOCK]);
                    if (alarm != null) {
                        mClockList.add(alarm);
                    }
                    mCursorArray[ALARM_CLOCK].moveToNext();

                } else if ((mCursorArray[WORLD_CLOCK] != null)
                        && (!mCursorArray[WORLD_CLOCK].isAfterLast())) {
                    City city = parseWorldCursor(mCursorArray[WORLD_CLOCK]);
                    if (city != null) {
                        mWorldClockList.add(city);
                    }
                    mCursorArray[WORLD_CLOCK].moveToNext();

                } else if ((mCursorArray[TIMER] != null)
                        && (!mCursorArray[TIMER].isAfterLast())) {
                    Log.d(TAG, "onBackup load data:");
                    OplusTimer timer = parseTimerCursor(mCursorArray[TIMER]);
                    if (timer != null) {
                        mTimerList.add(timer);
                    }
                    mCursorArray[TIMER].moveToNext();

                } else if ((mCursorArray[REPEAT_INFO] != null)
                        && (!mCursorArray[REPEAT_INFO].isAfterLast())) {
                    Log.d(TAG, "onBackup load data:");
                    AlarmRepeat repeat = parseAlarmRepeatCursor(mCursorArray[REPEAT_INFO]);
                    if (repeat != null) {
                        mAlarmRepeatList.add(repeat);
                    }
                    mCursorArray[REPEAT_INFO].moveToNext();

                }
                mCompletedCount++;
                Bundle progress = new Bundle();
                ProgressHelper.putMaxCount(progress, mMaxCount);
                ProgressHelper.putCompletedCount(progress, mCompletedCount);
                mBRPluginHandler.updateProgress(progress);
            }
        }
        if (!mIsCancel) {
            writeDataToXml();
        }
        Log.d(TAG, "mCompletedCount:" + mCompletedCount);
    }

    @Override
    public void onCancel(Bundle arg0) {
        mIsCancel = true;
        mIsPause = false;
        synchronized (mLock) {
            mLock.notifyAll();
            Log.i(TAG, "onCancel mLock.notifyAll()");
        }
    }

    @Override
    public void onContinue(Bundle arg0) {
        mIsPause = false;
        synchronized (mLock) {
            mLock.notifyAll();
            Log.i(TAG, "onContinue mLock.notifyAll()");
        }
    }

    @Override
    public Bundle onDestroy(Bundle arg0) {
        if (DEBUG) {
            Log.v(TAG, "onDestroy() ");
        }
        onEnd();
        Bundle result = new Bundle();
        ProgressHelper.putBRResult(result, mIsCancel ? ProgressHelper.BR_RESULT_CANCEL : ProgressHelper.BR_RESULT_SUCCESS);
        ProgressHelper.putMaxCount(result, mMaxCount);
        ProgressHelper.putCompletedCount(result, mCompletedCount);
        Log.i(TAG, "onDestroy =" + result);
        return result;
    }

    private void writeDataToXml() {
        if ((mCompletedCount == getMaxCount()) && (mClockXml != null)) {
            for (int i = 0; i < mClockList.size(); ++i) {
                //排除轮班闹钟子闹钟
                if (mClockList.get(i).getmLoopID() == -1) {
                    mClockXml.addOneAlarm(mContext, mClockList.get(i));
                }
            }

            for (int i = 0; i < mWorldClockList.size(); ++i) {
                mClockXml.addOneCity(mWorldClockList.get(i));
            }

            Log.d(TAG, "writeDataToXml mTimerList.size():" + mTimerList.size());
            for (int i = 0; i < mTimerList.size(); ++i) {
                mClockXml.addOneTimerRecord(mTimerList.get(i));
            }
            mClockXml.addTimerRecord(mContext);
            for (int i = 0; i < mAlarmRepeatList.size(); ++i) {
                mClockXml.addOneAlarmRepeat(mAlarmRepeatList.get(i));
            }
            mClockXml.addMorningData(mContext);
            mClockXml.addShowNextAlarmNotices(mContext);
            mClockXml.addBellGraduallyRings(mContext);

            mClockXml.addAlarmCloseModelAndRed(mContext);
            mClockXml.addAlarmDefaultRingAndVibrate(mContext);
            //add set workday type 2020-03-11 Confirm with product manager that the set workday type does not support moving
//            mClockXml.addSetWorkday(mContext);
            mClockXml.addDialClockCityData(mContext);
            mClockXml.endCompose();
            String recordXmlInfo = mClockXml.getXmlInfo();
            if (recordXmlInfo != null) {
                writeToFile(mTargetPath + File.separator + CLOCK_XML,
                        recordXmlInfo.getBytes(StandardCharsets.UTF_8));
            }
        }
    }

    @Override
    public void onPause(Bundle arg0) {
        if (DEBUG) {
            Log.v(TAG, "onPause() ");
        }
        mIsPause = true;
    }

    @Override
    public Bundle onPrepare(Bundle bundle) {
        Log.i(TAG, "onPrepare");
        if (mMaxCount < 0) {
            mMaxCount = getMaxCount();
        }
        onStart();
        Bundle preview = new Bundle();
        ProgressHelper.putMaxCount(preview, mMaxCount);
        Log.i(TAG, "onPrepare end=" + preview);
        return preview;
    }

    @Override
    public Bundle onPreview(Bundle arg0) {
        Log.i(TAG, "onPreview");
        if (mMaxCount < 0) {
            mMaxCount = getMaxCount();
        }
        Bundle preview = new Bundle();
        ProgressHelper.putMaxCount(preview, mMaxCount);
        long size = DataSizeUtils.estimateSize(TYPE_CLOCK, mMaxCount);
        ProgressHelper.putPreviewDataSize(preview, size);
        Log.i(TAG, "onPreview end=" + preview);
        return preview;
    }

    private int getMaxCount() {
        if (mMaxCount == -1) {
            mMaxCount = 0;
            for (Cursor cur : mCursorArray) {
                if (cur != null) {
                    Log.v(TAG, "cur : mCount" + cur.getCount());
                }
                mMaxCount += ((cur == null) || cur.isClosed()) ? 0 : cur.getCount();
            }
        }

        if (DEBUG) {
            Log.v(TAG, "getMaxCount():" + mMaxCount);
        }

        return mMaxCount;
    }


    private Alarm parseCursor(Cursor cursor) {
        Alarm alarm = null;
        if (cursor != null) {
            long id = cursor.getLong(ClockContract.Alarm.ALARM_ID_INDEX);
            int hour = cursor.getInt(ClockContract.Alarm.ALARM_HOUR_INDEX);
            int minutes = cursor.getInt(ClockContract.Alarm.ALARM_MINUTES_INDEX);
            int daysOfWeek = cursor.getInt(ClockContract.Alarm.ALARM_DAYS_OF_WEEK_INDEX);
            boolean enabled = cursor.getInt(ClockContract.Alarm.ALARM_ENABLED_INDEX) == 1;
            int alertType = cursor.getInt(ClockContract.Alarm.ALARM_ALERTTYPE_INDEX);
            String message = cursor.getString(ClockContract.Alarm.ALARM_MESSAGE_INDEX);
            int snooze = cursor.getInt(ClockContract.Alarm.ALARM_SOONZE_INDEX);
            String ringUri = cursor.getString(ClockContract.Alarm.ALARM_ALERT_INDEX);
            int volume = cursor.getInt(ClockContract.Alarm.ALARM_VOLUME_INDEX);
            String ringName = cursor.getString(ClockContract.Alarm.ALARM_ALERT_RINGNAME_INDEX);
            int deleteAfterUse = cursor.getInt(ClockContract.Alarm.ALARM_DELETE_AFTER_USE_INDEX);
            int vibrate = cursor.getInt(ClockContract.Alarm.ALARM_VIBRATE_INDEX);
            int workdaySwitch = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_WORKDAY_SWITCH_INDEX)) {
                workdaySwitch = cursor.getInt(ClockContract.Alarm.ALARM_WORKDAY_SWITCH_INDEX);
            }
            int holidaySwitch = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_HOLIDAY_SWITCH_INDEX)) {
                holidaySwitch = cursor.getInt(ClockContract.Alarm.ALARM_HOLIDAY_SWITCH_INDEX);
            }
            int workType = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_WORKDAY_TYPE_INDEX)) {
                workType = cursor.getInt(ClockContract.Alarm.ALARM_WORKDAY_TYPE_INDEX);
            }
            long workdayUpdateTime = -1;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_WORKDAY_UPDATE_TIME_INDEX)) {
                workdayUpdateTime = cursor.getLong(ClockContract.Alarm.ALARM_WORKDAY_UPDATE_TIME_INDEX);
            }
            int snoozeTime = ClockConstant.SNOOZE_AFTER_MIN;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_SNOOZE_TIME_INDEX)) {
                snoozeTime = cursor.getInt(ClockContract.Alarm.ALARM_SNOOZE_TIME_INDEX);
            }
            String special = DatePickerUtils.SPLIT;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_SPECIAL_ALARM_DAYS_INDEX)) {
                special = cursor.getString(ClockContract.Alarm.ALARM_SPECIAL_ALARM_DAYS_INDEX);
            }
            int ringNum = ClockConstant.SNOOZE_RING_NUM;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_RING_NUMBER_INDEX)) {
                ringNum = cursor.getInt(ClockContract.Alarm.ALARM_RING_NUMBER_INDEX);
            }
            int defaultAlarm = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_DEFAULT_ALARM_INDEX)) {
                defaultAlarm = cursor.getInt(ClockContract.Alarm.ALARM_DEFAULT_ALARM_INDEX);
            }
            //轮班闹钟新增字段
            int loopSwitch = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_SWITCH_INDEX)) {
                loopSwitch = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_SWITCH_INDEX);
            }
            int loopCycleDays = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_CYCLE_DAYS_INDEX)) {
                loopCycleDays = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_CYCLE_DAYS_INDEX);
            }
            int loopID = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_ID_INDEX)) {
                loopID = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_ID_INDEX);
            }
            int loopWorkDays = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_WORK_DAYS_INDEX)) {
                loopWorkDays = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_WORK_DAYS_INDEX);
            }
            int loopDay = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_DAY_INDEX)) {
                loopDay = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_DAY_INDEX);
            }
            int loopAlarmNumber = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_ALARM_NUMBER_INDEX)) {
                loopAlarmNumber = cursor.getInt(ClockContract.Alarm.ALARM_LOOP_ALARM_NUMBER_INDEX);
            }
            String loopResetDays = DatePickerUtils.SPLIT;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_LOOP_RESET_DAYS_INDEX)) {
                loopResetDays = cursor.getString(ClockContract.Alarm.ALARM_LOOP_RESET_DAYS_INDEX);
            }
            int garbSwitch = 0;
            if (!cursor.isNull(ClockContract.Alarm.ALARM_GARB_SWITCH_INDEX)) {
                garbSwitch = cursor.getInt(ClockContract.Alarm.ALARM_GARB_SWITCH_INDEX);
            }

            String alarmUuid = cursor.getString(ClockContract.Alarm.ALARM_UUID_INDEX);

            //TODO: Check this.
            String ringPath = getRightAlertUri(ringUri);
            Uri alertUri = null;
            if (!TextUtils.isEmpty(ringPath)) {
                alertUri = Uri.parse(ringPath);
            }
            alarm = Alarm.build(enabled, hour, minutes, daysOfWeek, alertType, message, alertUri,
                    ringName, volume, deleteAfterUse, vibrate, workdaySwitch, holidaySwitch);
            alarm.setId(id);
            alarm.setSnoonzeItem(snooze);
            alarm.setmWorkDayType(workType);
            alarm.setmWorkdayUpdateTime(workdayUpdateTime);
            alarm.setmSnoozeTime(snoozeTime);
            alarm.setmSpecialAlarmDays(special);
            alarm.setRingNum(ringNum);
            alarm.setmDefaultAlarm(defaultAlarm);
            alarm.setmLoopSwitch(loopSwitch);
            alarm.setmLoopCycleDays(loopCycleDays);
            alarm.setmLoopID(loopID);
            alarm.setmLoopWorkDays(loopWorkDays);
            alarm.setmLoopDay(loopDay);
            alarm.setmLoopAlarmNumber(loopAlarmNumber);
            alarm.setmLoopRestDays(loopResetDays);
            alarm.setmGarbSwitch(garbSwitch);
            if (!TextUtils.isEmpty(alarmUuid)) {
                alarm.setUUID(alarmUuid);
            }
        }

        return alarm;
    }

    private String getRightAlertUri(String ringUri) {
        if (ringUri != null) {
            if (ringUri.equals(DEFAULT_ALARM_SETTING_URI) && !TextUtils.isEmpty(mDefaultAlarmMediaUriStr)) {
                ringUri = mDefaultAlarmMediaUriStr;
            }
        }
        String ringPath = AlarmRingUtils.getMusicPathFromUriString(mContext, ringUri);

        if (ringPath != null) {
            File ringFile = new File(ringPath);
            if (!ringFile.exists()) {
                ringPath = null;
            }
        } else {
            Log.e(TAG, "ring path is null");
        }
        if (ClockConstant.ALARM_ALERT_SILENT.equals(ringUri)) {
            ringPath = ringUri;
        } else if (AlarmWeatherUtils.isDynamicWeatherAlert(ringUri)) {
            ringPath = ringUri;
        } else if (AlarmSpotifyUtils.INSTANCE.isSpotifyRing(ringUri)) {
            ringPath = ringUri;
        }
        return ringPath;
    }

    private City parseWorldCursor(Cursor cursor) {
        City city = null;
        if (cursor != null) {
            city = new City();
            city.setCityId(cursor.getInt(CITY_ID_INDEX));
            city.setName(cursor.getString(CITY_NAME_INDEX));
            city.setCountry(cursor.getString(CITY_COUNTRY_INDEX));
            city.setTimezone(cursor.getString(ID_CITY_INDEX));
            city.setSortPos(cursor.getInt(SORTORDER2_INDEX));
            city.setFlag(cursor.getInt(FLAG_INDEX));
            city.setFlag2(cursor.getInt(FLAG2_INDEX));
            if (DEBUG) {
                Log.v(TAG, "city=" + city.getName() + "," + city.getCountry() + " sort= " + city.getSortPos());
            }
        }
        return city;
    }

    @SuppressLint("Range")
    private OplusTimer parseTimerCursor(Cursor cursor) {
        if (DEBUG) {
            Log.v(TAG, "parseTimerCursor");
        }

        OplusTimer data = null;
        if (cursor != null) {
            data = new OplusTimer();
            data.setTimerIndex(cursor.getInt(cursor.getColumnIndex(ClockContract.TimerTableColumns._ID)));
            data.setDuration(cursor.getLong(cursor.getColumnIndex(ClockContract.TimerTableColumns.DURATION)));
            data.setDescription(cursor.getString(cursor.getColumnIndex(ClockContract.TimerTableColumns.DESCRIPTION)));
            data.setFlag(cursor.getInt(cursor.getColumnIndex(ClockContract.TimerTableColumns.FLAG)));
            data.setRingName(cursor.getString(cursor.getColumnIndex(ClockContract.TimerTableColumns.RINGNAME)));

            if (data.getFlag() == OplusTimer.FLAG_SYSYTEM_TIMER_ITEM) {
                return null;
            }
            data.setSelected(cursor.getInt(cursor.getColumnIndex(ClockContract.TimerTableColumns.SELECTED)));
            String ringUri = cursor.getString(cursor.getColumnIndex(ClockContract.TimerTableColumns.RING));
            String ringPath = getRightAlertUri(ringUri);
            Uri alertUri = null;
            if (!TextUtils.isEmpty(ringPath)) {
                alertUri = Uri.parse(ringPath);
            }
            data.setRing(String.valueOf(alertUri));
        }

        return data;
    }


    @SuppressLint("Range")
    private AlarmRepeat parseAlarmRepeatCursor(Cursor cursor) {
        if (DEBUG) {
            Log.v(TAG, "parseTimerCursor");
        }

        AlarmRepeat data = null;
        if (cursor != null) {
            data = new AlarmRepeat();
            data.setmId(cursor.getInt(cursor.getColumnIndex(ClockContract.AlarmsRepeat._ID)));
            data.setmAlarmDuration(cursor.getInt(cursor.getColumnIndex(ClockContract.AlarmsRepeat.ALARM_DURATION)));
            data.setmAlarmInterval(cursor.getInt(cursor.getColumnIndex(ClockContract.AlarmsRepeat.ALARM_INTERVAL)));
            data.setmAlarmNum(cursor.getInt(cursor.getColumnIndex(ClockContract.AlarmsRepeat.ALARM_NUM)));
            data.setmAlarmPrompt(cursor.getInt(cursor.getColumnIndex(ClockContract.AlarmsRepeat.ALARM_PROMPT)));
        }

        Log.v(TAG, "Parse alarm repeat info :" + data);

        return data;
    }


    private void writeToFile(String fileName, byte[] buf) {
        Log.d(TAG, "writeToFile" + fileName);
        FileOutputStream outStream = new FileOutputStream(getFileDescriptor(fileName));
        try {
            outStream.write(buf, 0, buf.length);
            outStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                outStream.close();
            } catch (IOException e) {
                Log.e(TAG, "writeToFile IOException.");
            }
        }
    }

}
