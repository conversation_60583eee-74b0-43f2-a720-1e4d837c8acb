/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :settings for statusBar.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowInsets;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.base.FoldScreenConfig;
import com.oplus.alarmclock.base.IUIConfig;
import com.oplus.alarmclock.base.UIConfigMonitor;
import com.oplus.alarmclock.utils.ColorStatusBarResponseUtil;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.VersionUtils;
import com.oplus.utils.ActivityUtils;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.Collection;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

public abstract class BaseActivity extends AppCompatActivity implements UIConfigMonitor.OnUIConfigChangeListener,
        ColorStatusBarResponseUtil.StatusBarClickListener, View.OnApplyWindowInsetsListener {
    public WeakReference<BaseActivity> mActivityWeak = new WeakReference<>(this);
    protected UiMode mUiMode = null;
    private ColorStatusBarResponseUtil mColorStatusBarResponseUtil;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Utils.setWindowStyle(this);
        Utils.setAnmiToolbarActivityWindowStyle(this);
        registerUIConfigMonitor();
        if (shouldApplyThemeOverlays()) {
            COUIThemeOverlay.getInstance().applyThemeOverlays(this);
        }
        if (isRegisterStatusBar()) {
            mColorStatusBarResponseUtil = new ColorStatusBarResponseUtil(this);
            mColorStatusBarResponseUtil.setStatusBarClickListener(this);
            mColorStatusBarResponseUtil.register();
        }
        //非手势导航添加底部padding值
        if (VersionUtils.isOsVersion15() && getWindow() != null && !Utils.isGestureNavMode(this)) {
            getWindow().getDecorView().setOnApplyWindowInsetsListener(this);
        }
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Utils.setDefaultDisplay(newBase);
        super.attachBaseContext(newBase);
    }

    protected boolean shouldApplyThemeOverlays() {
        return true;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        UIConfigMonitor.Companion.getInstance().onActivityConfigChanged(newConfig);
        dealOsloOrientation();
    }

    /**
     * 处理平板横竖屏边距padding
     * isRealOslo 获取的是物理平板，包括分屏模式，所以需要处理平板横竖屏，和其他情况（其他情况padding设置为默认）
     */
    private void dealOsloOrientation() {
        mUiMode = FoldScreenUtils.uiMode(this, isInMultiWindowMode());
        if (UiMode.LARGE_HORIZONTAL == mUiMode) {
            onOsloLandOrientation();
        } else if (UiMode.LARGE_VERTICAL == mUiMode) {
            onOsloPortOrientation();
        } else {
            onOsloOtherOrientation();
        }
        //处理设置相关页面左右间距
        onScreenFold();
    }

    protected void onScreenFold() {
    }

    protected void onOsloLandOrientation() {
    }

    protected void onOsloPortOrientation() {
    }

    protected void onOsloOtherOrientation() {
    }

    protected void registerUIConfigMonitor() {
        UIConfigMonitor.Companion.getInstance().attachActivity(this);
        FoldScreenUtils.updateUIOrientation(this, false);
    }

    @Override
    public void onUIConfigChanged(@NotNull Collection<IUIConfig> configList) {
        configList.forEach(iuiConfig -> {
            if (iuiConfig instanceof FoldScreenConfig) {
                FoldScreenUtils.updateUIOrientation(this, true);
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mColorStatusBarResponseUtil != null) {
            mColorStatusBarResponseUtil.unRegister();
        }
        mUiMode = null;
        if (getWindow() != null && getWindow().getDecorView() != null) {
            getWindow().getDecorView().setOnApplyWindowInsetsListener(null);
        }
    }


    @NonNull
    @Override
    public WindowInsets onApplyWindowInsets(@NonNull View view, @NonNull WindowInsets windowInsets) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            int bottom = windowInsets.getInsets(WindowInsets.Type.systemBars()).bottom;
            if (getWindow() != null && getWindow().getDecorView() != null) {
                getWindow().getDecorView().setPadding(0, 0, 0, bottom);
            }
        }
        return windowInsets;
    }

    protected boolean isRegisterStatusBar() {
        return false;
    }

    public UiMode obtainUiMode() {
        if (mUiMode == null) {
            mUiMode = FoldScreenUtils.uiMode(this, isInMultiWindowMode());
        }
        return mUiMode;
    }

    public boolean isTabletMode() {
        if (mUiMode == null) {
            mUiMode = FoldScreenUtils.uiMode(this, isInMultiWindowMode());
        }
        return UiMode.LARGE_HORIZONTAL == mUiMode || UiMode.LARGE_VERTICAL == mUiMode;
    }

    public boolean isMidMode() {
        return UiMode.MIDDLE == obtainUiMode();
    }

    @Override
    public void onStatusBarClicked() {
        Log.d("BaseActivity", "onStatusBarClicked: ");
    }

    /**
     * 孔雀常规屏设置左右间距
     */
    public void setLayoutPadding(View view, AppBarLayout mAppBarLayout) {
        if (view == null || (mAppBarLayout == null)) {
            return;
        }
        if (!FlexibleWindowUtils.isSupportFlexibleActivity()) {
            int padding = 0;
            switch (obtainUiMode()) {
                case LARGE_HORIZONTAL:
                    padding = getResources().getDimensionPixelSize(R.dimen.settings_oslo_land_padding);
                    view.setPadding(padding, 0, padding, 0);
                    break;
                case LARGE_VERTICAL:
                    padding = getResources().getDimensionPixelSize(R.dimen.settings_oslo_port_padding);
                    view.setPadding(padding, 0, padding, 0);
                    break;
                case MIDDLE:
                    padding = getResources().getDimensionPixelSize(R.dimen.layout_dp_84);
                    view.setPadding(padding, 0, padding, 0);
                    break;
                case NORMAL:
                case SMALL:
                default:
                    view.setPadding(0, mAppBarLayout.getMeasuredHeight(), 0, 0);
                    break;
            }
        } else {
            view.setPadding(0, mAppBarLayout.getMeasuredHeight(), 0, 0);
        }
    }

    /**
     * 跟手面板背景色适配
     */
    public void setFlexibleWindowBg() {
        if (FlexibleWindowUtils.isSupportFlexibleActivity()) {
            boolean isFlexibleActivity = FlexibleWindowUtils.isFlexibleActivity(getResources().getConfiguration());
            boolean isFlexibleActivitySuitable = FlexibleWindowUtils.isFlexibleActivitySuitable(getResources().getConfiguration());
            if (isFlexibleActivity) {
                setFlexibleWindowBgColor(COUIContextUtil.getAttrColor(this, R.attr.couiColorSurfaceWithCard));
            } else if (isFlexibleActivitySuitable) {
                setFlexibleWindowBgColor(COUIContextUtil.getAttrColor(this, R.attr.couiColorSurfaceWithCard));
            }
        }
    }

    /**
     * 设置面板view颜色
     *
     * @param color
     */
    private void setFlexibleWindowBgColor(int color) {
        if (getWindow() != null && getWindow().getDecorView() != null) {
            getWindow().getDecorView().setBackgroundColor(color);
        }
        COUIToolbar toolbar = findViewById(R.id.toolbar);
        if (toolbar != null) {
            toolbar.setBackgroundColor(color);
        }
        AppBarLayout appBarLayout = findViewById(R.id.app_bar);
        if (appBarLayout != null) {
            appBarLayout.setBackgroundColor(color);
        }
    }

    /**
     * 销毁所有页面
     */
    public void finishSettingActivity() {
        if (getResources() != null && FlexibleWindowUtils.isOpenInFlexibleActivity(getResources().getConfiguration())) {
            for (WeakReference<BaseActivity> act : ActivityUtils.INSTANCE.getSSettingActivity()) {
                if (act.get() != null && !act.get().isFinishing()) {
                    act.get().finish();
                }
            }
            ActivityUtils.INSTANCE.getSSettingActivity().clear();
        }
    }

    /**
     * 添加activity到集合设置事件
     */
    public void addActivityWeakAndSetOnTouch() {
        ActivityUtils.INSTANCE.getSSettingActivity().add(mActivityWeak);
        getWindow().getDecorView().setOnTouchListener((view, motionEvent) -> {
            boolean isOutOf = Utils.isOutOfBounds(BaseActivity.this, motionEvent);
            if (motionEvent.getAction() == MotionEvent.ACTION_UP
                    || motionEvent.getAction() == MotionEvent.ACTION_CANCEL) {
                if (isOutOf) {
                    finishSettingActivity();
                }
            }
            return false;
        });
    }
}
