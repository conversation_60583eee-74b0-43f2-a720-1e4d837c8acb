/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : AlarmInitReceiver is used to Reschedule every alarm state when specified things
 * happened.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 * v1.1, 2018-8-20, <PERSON><PERSON><PERSON>, clean code.
 ************************************************************/

package com.oplus.alarmclock;

import static com.oplus.alarmclock.appfunctions.ClockAppSearchManager.initAlarms;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Message;

import com.coloros.widget.smallweather.ClockWidgetManager;
import com.oplus.alarmclock.alarmclock.AlarmAlertWakeLock;
import com.oplus.alarmclock.alarmclock.AlarmNotify;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmFixUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmNotRingCheckUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.alert.AlarmKlaxon;
import com.oplus.alarmclock.alert.AlarmService;
import com.oplus.alarmclock.backup.BackUpConstant;
import com.oplus.alarmclock.globalclock.DefaultCityInitService;
import com.oplus.alarmclock.migration.WPlusUtils;
import com.oplus.alarmclock.stopwatch.StopWatchService;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.DialClockUtil;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.ProcessGuard;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;
import java.util.List;
import java.util.TimeZone;

public class AlarmInitReceiver extends BroadcastReceiver {

    private static final String TAG = "AlarmInitReceiver";
    private static final String SET_NEXT_ALERT_OLD = "com.oppo.alarmclock.alarmclock.SET_NEXT_ALERT";
    private static final String SET_NEXT_ALERT = "com.oplus.alarmclock.alarmclock.SET_NEXT_ALERT";
    private static final String ACTION_LOCKED_BOOT_COMPLETED = Intent.ACTION_LOCKED_BOOT_COMPLETED;

    private static final int KILL_SELF = 101;
    private static final int KILL_SEIF_ALLOWED_TIME = 60 * 1000;
    private static final int OTHER_ACTION_RECEIVED = 102;
    private static final int OTHER_ACTION_COMPLETED = 103;
    private Context mContext;
    private AlarmInitHandler mHandler;

    /**
     * Sets alarm on ACTION_BOOT_COMPLETED. Resets alarm on TIME_SET, TIMEZONE_CHANGED
     */
    @Override
    public void onReceive(final Context context, final Intent intent) {
        mContext = context;
        ProcessGuard.startProtect(context, "AlarmInitReceiver");
        final String action = intent.getAction();
        Log.v(TAG, "AlarmInitReceiver receive: " + action + ";" + this.hashCode());

        final PendingResult result = goAsync();
        AlarmAlertWakeLock.acquirePartialWakeLock(context);

        mHandler = new AlarmInitHandler(this);
        // We need to increment the global id out of the async task to prevent
        // race conditions
        if (!Intent.ACTION_BOOT_COMPLETED.equals(action)
                && !ACTION_LOCKED_BOOT_COMPLETED.equals(action)) {
            mHandler.removeMessages(KILL_SELF);
            mHandler.sendEmptyMessage(OTHER_ACTION_RECEIVED);
        }

        AsyncHandler.post(new Runnable() {
            @Override
            public void run() {
                AlarmStateManager.updateGloablIntentId(context);
                // Remove the snooze alarm after a boot.
                if (Intent.ACTION_BOOT_COMPLETED.equals(action)
                        || ACTION_LOCKED_BOOT_COMPLETED.equals(action)
                        || Intent.ACTION_TIME_CHANGED.equals(action)
                        || Intent.ACTION_TIMEZONE_CHANGED.equals(action)
                        || Intent.ACTION_LOCALE_CHANGED.equals(action)) {
                    final boolean isLockBootComplete = ACTION_LOCKED_BOOT_COMPLETED.equals(action);
                    final boolean isBootComplete = Intent.ACTION_BOOT_COMPLETED.equals(action)
                            || isLockBootComplete;
                    if (isBootComplete) {
                        // Clear  timers data
                        TimerService.resetTimerStatus(context);

                        startDfltCityInitService(context);

                        StopWatchService.resetAllStopWatchStatus(context);

                        AlarmRingStatisticUtils.statisticsPowerOn(context);


                    }
                    //update ClockShortcut
                    if (Intent.ACTION_LOCALE_CHANGED.equals(action)) {
                        ClockShortcutManager.dynamicInitShortcuts(context);
                    }

                    if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
                        // wplus db migration task
                        WPlusUtils.startDBMigration(WPlusUtils.FROM_BOOT_COMPLETE);
                        AlarmUtils.registedMediaScannerFinishedReceiver();
                        AlarmUtils.startUpdateRingUri(context);
                    }

                    if (Intent.ACTION_TIMEZONE_CHANGED.equals(action)) {
                        String tz = intent.getStringExtra(Intent.EXTRA_TIMEZONE);
                        TimeZone.setDefault(TimeZone.getTimeZone(tz));
                        Log.d(TAG, "timezone change : " + tz);
                        DialClockUtil.postDialClockData(context);
                    }

                    AlarmNotRingCheckUtils.INSTANCE.checkAlarmNotRing(true);

                    // Update all the alarm instances on time change event
                    if (isBootComplete) {
                        AlarmFixUtils.fixAlarmInTimeCheck(context, action, true);
                        if (!isLockBootComplete) {
                            initAlarms(context, true);
                            mHandler.sendEmptyMessage(KILL_SELF);
                        } else {
                            PrefUtils.removeNotification(context,null,null,true,true);
                            AlarmNotify alarmNotify = new AlarmNotify();
                            alarmNotify.updateNextWorkdayNotice(context, true);
                        }
                    } else {
                        AlarmStateManager.fixAlarmInstances(context, action);
                        mHandler.sendEmptyMessage(OTHER_ACTION_COMPLETED);
                        if (!Intent.ACTION_LOCALE_CHANGED.equals(action)) {
                            AlarmNotify alarmNotify = new AlarmNotify();
                            alarmNotify.updateNextWorkdayNotice(context, true);
                        }
                    }
                } else if (SET_NEXT_ALERT_OLD.equals(action) || SET_NEXT_ALERT.equals(action)
                        || BackUpConstant.CLOCK_DATA_CHANGE.equals(action)) {
                    AlarmStateManager.fixAlarmInstancesForSpeechAssistant(context);
                    //Backup and recovery
                    if (BackUpConstant.CLOCK_DATA_CHANGE.equals(action)) {
                        AlarmStateManager.showNextAlarmNotices(context);
                    }
                } else if (Intent.ACTION_SHUTDOWN.equals(action)) {
                    AlarmKlaxon.stop(context);
                    Log.d(TAG, "setLauncherStateReceiverEnabled onReceive false");
                    PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                            StopWatchService.LAST_TIME_MILLIS_PREFERENCE, System.currentTimeMillis());
                    Log.d(TAG, "run: save timemillis");
                    AlarmRingStatisticUtils.statisticsPowerOff(context);
                }

                result.finish();
                AlarmAlertWakeLock.releaseCpuLockCpu(TAG);
                ProcessGuard.endProtect(context, "AlarmInitReceiver");
            }
        });
        Log.d(TAG, "onReceive end");
    }

    private void startDfltCityInitService(Context context) {
        if (!DeviceUtils.isProvisioned(context)) {
            context.startService(new Intent(context, DefaultCityInitService.class));
        }
    }

    private void killSelf() {
        boolean isKillable = true;

        if ((AlarmService.sIsServiceAlive) || (AlarmClockApplication.getActivityCount() > 0)) {
            isKillable = false;
        }

        if (hasAlarmInOneMinute(mContext)) {
            isKillable = false;
        }

        if (!DeviceUtils.isProvisioned(mContext)) {
            isKillable = false;
        }

        if (!ClockWidgetManager.getInstance().haveNoWidget(mContext)) {
            isKillable = false;
        }

        if (DialClockUtil.hasDialClock(mContext)) {
            isKillable = false;
        }

        Log.d(TAG, "killSelf ? " + (isKillable ? "YES!" : "NO!"));
        if (isKillable) {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

    private static boolean hasAlarmInOneMinute(Context context) {
        boolean hasAlarmInOneMinute = false;
        List<AlarmSchedule> list = ScheduleUtils.getAllSchedules(context);
        if ((list != null) && (!list.isEmpty())) {
            final long currentTimeMills = Calendar.getInstance().getTimeInMillis();
            for (AlarmSchedule schedule : list) {
                long alarmTimeMills = ScheduleUtils.getAlarmTimeInMills(schedule);
                //TODO: Shoud it be bigger than 0?
                if (alarmTimeMills - currentTimeMills <= KILL_SEIF_ALLOWED_TIME) {
                    hasAlarmInOneMinute = true;
                    break;
                }
            }
        }
        return hasAlarmInOneMinute;
    }

    private static class AlarmInitHandler extends StaticHandler<AlarmInitReceiver> {

        private boolean mIsHandleOtherAction;

        AlarmInitHandler(AlarmInitReceiver t) {
            super(t);
        }

        @Override
        public void handleMessage(Message msg, AlarmInitReceiver t) {
            switch (msg.what) {
                case KILL_SELF:
                    if (!mIsHandleOtherAction) {
                        t.killSelf();
                    }
                    break;
                case OTHER_ACTION_RECEIVED:
                    mIsHandleOtherAction = true;
                    break;
                case OTHER_ACTION_COMPLETED:
                    mIsHandleOtherAction = false;
                    break;
                default:
                    break;
            }
        }
    }

}
