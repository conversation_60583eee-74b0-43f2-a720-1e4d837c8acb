/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
@file:Suppress("SpreadOperator")

package com.oplus.alarmclock.stopwatch

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.oplus.alarmclock.R

class StopWatchMinutePointer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : StopWatchPointer(context, attrs, defStyleAttr) {
    companion object {
        private const val ONE_CIRCLE_MINUTE_TIME = 30 * 60 * 1000
        private const val ONE_MILLISECOND_MINUTE_ANGLE = 0.0002F//一毫秒分针转动0.0002°
    }

    private var mSmallCircleToTopDistance = 0F
    private var mSmallCircleRadius = 0F
    private var mSmallMinuteWidth = 0F
    private var mSmallMinuteLength = 0F
    private var mSmallMinuteOffsetLength = 0F
    private var mSmallMinuteCircleRadius = 0F
    private var mMinuteColor = 0
    private var mSmallCenterY = 0F
    private var mCurrentMinuteAngle = 0F
    private var mPauseMinuteAngle = 0F
    private var mRestoreMinuteAngle = 0F
    private val mDrawAble by lazy {
        val resourceId = if (mIsDark) {
            R.drawable.ic_dial_stopwatch_minute_pointer_dark
        } else {
            R.drawable.ic_dial_stopwatch_minute_pointer
        }
        AppCompatResources.getDrawable(getContext(), resourceId)
    }

    private val mScaleMinuteCirclePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG)
    }

    init {
        initColor(context)
        initSize(context, attrs)
    }

    override fun onResume() {
        super.onResume()
        mCurrentMinuteAngle = mTime % ONE_CIRCLE_MINUTE_TIME * ONE_MILLISECOND_MINUTE_ANGLE
        if (mPauseMinuteAngle == 0f || mCurrentMinuteAngle == mPauseMinuteAngle) {
            mInterceptInvalidate = false
            mPauseMinuteAngle = 0f
            return
        }
        val time = getRestoreTime()
        val targetMinuteAngle =
            (mTime + time) % ONE_CIRCLE_MINUTE_TIME * ONE_MILLISECOND_MINUTE_ANGLE
        mAnimator =
            ValueAnimator.ofFloat(*getAnimatorParams(mPauseMinuteAngle, targetMinuteAngle)).apply {
                duration = time
                interpolator = mRestoreInterpolator
                addUpdateListener {
                    mCurrentMinuteAngle = it.animatedValue as Float % ONE_CIRCLE_DEGREE
                    invalidate()
                }
                doOnCancel {
                    resetResumeProperties()
                    postInvalidate()
                }
                doOnEnd { resetResumeProperties() }
            }
        mAnimator?.start()
    }

    override fun onPause() {
        super.onPause()
        mPauseMinuteAngle = mCurrentMinuteAngle
    }

    override fun onStop() {
        super.onStop()
        mRestoreMinuteAngle = mCurrentMinuteAngle
        mAnimator = ValueAnimator.ofFloat(mCurrentMinuteAngle, 0F).apply {
            duration = mTotalRestoreTime
            interpolator = mRestoreInterpolator
            addUpdateListener {
                mCurrentMinuteAngle = it.animatedValue as Float
                invalidate()
            }
            doOnCancel {
                resetStopProperties()
                postInvalidate()
            }
            doOnEnd { resetStopProperties() }
        }
        mAnimator?.start()
    }

    override fun onRelease() {
        super.onRelease()
        mRestoreMinuteAngle = 0F
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mSmallCenterY = mSmallCircleToTopDistance + mSmallCircleRadius
        mBitmap = drawableToBitmap(mDrawAble)
    }

    override fun Canvas.onStart() {
        drawMinute(mCurrentMinuteAngle)
    }

    override fun Canvas.onStop() {
        drawMinute(mCurrentMinuteAngle)
    }

    private fun Canvas.drawMinute(angle: Float) {
        save()
        rotate(angle, mCenterX, mSmallCenterY)
        mBitmap?.runCatching {
            if (!isRecycled) {
                drawBitmap(
                    this,
                    mCenterX - width / 2,
                    mSmallCenterY + mSmallMinuteOffsetLength - mSmallMinuteLength,
                    mScaleMinuteCirclePaint
                )
            }
        }
        restore()
    }

    override fun initColor(context: Context) {
        super.initColor(context)
        context.resources?.run {
            mMinuteColor = getColor(R.color.app_dial_minute_pointer_color, null)
            mShadowColor = getColor(R.color.hour_minute_shadow_color, null)
        }
    }

    override fun initSize(context: Context, attrs: AttributeSet?) {
        super.initSize(context, attrs)
        context.resources?.run {
            mSmallCircleToTopDistance =
                getDimension(R.dimen.app_dial_small_ring_distance_edge_offset) * mMultiple
            mSmallCircleRadius = getDimension(R.dimen.app_dial_small_circle_radius) * mMultiple
            mSmallMinuteWidth = getDimension(R.dimen.layout_dp_10) * mMultiple
            mSmallMinuteLength = getDimension(R.dimen.layout_dp_39) * mMultiple
            mSmallMinuteOffsetLength = getDimension(R.dimen.layout_dp_5) * mMultiple
            mSmallMinuteCircleRadius = getDimension(R.dimen.layout_dp_5) * mMultiple

            mShadowRadius = getDimension(R.dimen.app_dial_pointer_shadow_radius) * mMultiple
            mShadowY = getDimension(R.dimen.app_dial_pointer_shadow_dy) * mMultiple
        }
    }

    override fun calculateAngle() {
        super.calculateAngle()
        mCurrentMinuteAngle = mTime % ONE_CIRCLE_MINUTE_TIME * ONE_MILLISECOND_MINUTE_ANGLE
    }

    override fun resetResumeProperties() {
        super.resetResumeProperties()
        mPauseMinuteAngle = 0F
    }

    override fun resetStopProperties() {
        super.resetStopProperties()
        mRestoreTime = 0
        mRestoreMinuteAngle = 0F
        mCurrentMinuteAngle = 0F
        mPauseMinuteAngle = 0F
        mTotalRestoreTime = 0
    }

    /**
     * 复原的时间
     * 时长：
     * T=500ms+10t
     * t：当前停止时间
     * T：转动总时长
     * */
    override fun getTotalRestoreTime(): Long {
        return RESTORE_TIME + (mTime / THOUSAND / SIXTY % THIRTY) * TEN
    }

    override fun getRestoreTime(): Long {
        return RESTORE_TIME + ((mTime - mPauseTime) / THOUSAND / SIXTY % THIRTY) * TEN
    }
}