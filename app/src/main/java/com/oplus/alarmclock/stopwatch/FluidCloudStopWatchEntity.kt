/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - FluidCloudStopWatchEntity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  W9002127  2024/11/22     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

data class FluidCloudStopWatchEntity(
    val timerMsg: String,
    val day: String,
    val hour: String,
    val minute: String,
    val second: String,
    val includeDay: Boolean,
    val includeHours: <PERSON>olean,
    val thanOneMinute: <PERSON>olean,
    val voiceStr: String
)