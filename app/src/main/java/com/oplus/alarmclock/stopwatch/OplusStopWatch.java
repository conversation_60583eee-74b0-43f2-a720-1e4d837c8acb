/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.stopwatch;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.FontMetrics;
import android.graphics.Point;
import android.util.AttributeSet;
import android.view.View;

import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;

import java.text.NumberFormat;
import java.util.Locale;

public class OplusStopWatch extends View {
    public static final String TAG = "OplusStopWatch";
    public static final int EXTRA_LONG_FORMAT = 101;
    public static final int LONG_FORMAT = 102;
    public static final int SCOPE_OF_WORTH = 20;
    public static final int SHORT_FORMAT = 103;
    public static final int HOURS_OF_WHOLE_DAY = 24;
    public static final int HOUR_IN_MILLISEC = 60 * 60 * 1000;
    public static final int MINUTE_IN_MILLISEC =  60 * 1000;

    private static final int DEFAULT_TIME_FORMAT_LENGTH = 8;
    private final String mDefaultLanguage;
    private int mViewTextSize = 0;
    private int mViewTextColorNormal;
    private int mViewTextColorHighLight;
    private float mTimeStandardLength;
    //非阿拉伯数字，显示最长时的宽度
    private float mTimeStandardLengthForNonArabicNum;
    private Paint mViewTextPaint;
    private FontMetrics mFontMetrics;
    private Point mCenter;
    private NumberFormat mNumberFormat;
    //非阿拉伯数字
    private boolean mIsNonArabicNum = false;
    private Context mContext;
    //判断是否是秒表
    private boolean mIsOplusStopWatch = true;

    public OplusStopWatch(Context context) {
        this(context, null);
    }

    public OplusStopWatch(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OplusStopWatch(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.OplusStopWatchView);
        mIsOplusStopWatch = array.getBoolean(R.styleable.OplusStopWatchView_isOplusStopWatch, true);
        array.recycle();
        Resources r = context.getResources();
        mNumberFormat = NumberFormat.getInstance();
        OplusStopWatchWrapper.INSTANCE.setNumberFormat(mNumberFormat);
        mContext = context;
        mViewTextSize = mIsOplusStopWatch ? r.getDimensionPixelSize(R.dimen.view_stopwatch_textsize) : r.getDimensionPixelSize(R.dimen.view_stopwatch_interval_textsize);
        final float scale = r.getConfiguration().fontScale;
        mViewTextSize = (int) COUIChangeTextUtil.getSuitableFontSize(mViewTextSize, scale, COUIChangeTextUtil.G2);
        if (DeviceUtils.isStickingSupport(context) && !COUIDarkModeUtil.isNightMode(getContext())) {
            mViewTextColorNormal = mIsOplusStopWatch ? r.getColor(R.color.black_text_color_of_sticking_support) : r.getColor(R.color.oplus_interval_black_text_color);
        } else {
            mViewTextColorNormal = mIsOplusStopWatch ? r.getColor(R.color.default_text_color_black) : r.getColor(R.color.oplus_interval_black_text_color);
        }
        mViewTextColorHighLight = mIsOplusStopWatch ? COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary) : r.getColor(R.color.oplus_interval_black_text_color);
        mViewTextPaint = new Paint();
        mViewTextPaint.setAntiAlias(true);
        mViewTextPaint.setTextSize(mViewTextSize);
        mViewTextPaint.setColor(mViewTextColorNormal);
        mViewTextPaint.setTypeface(Utils.getSansEnTypeface(false));
        mDefaultLanguage = Locale.getDefault().getLanguage();
        mTimeStandardLength = initStandardLength(mViewTextPaint, SHORT_FORMAT);
        mTimeStandardLengthForNonArabicNum = initStandardLength(mViewTextPaint, EXTRA_LONG_FORMAT);
    }

    /**
     * 初始化字符串的标准长度  07:40.35
     */
    private float initStandardLength(Paint paint, int formatType) {
        //Arabic number is shorter than other language. the length of the painted String is 8.
        Log.i(TAG, "OplusStopWatch: Language: " + mDefaultLanguage);
        if (("ar".equals(mDefaultLanguage)) || "my".equals(mDefaultLanguage)) {
            mIsNonArabicNum = true;
        }
        float maxWidth = paint.measureText(mNumberFormat.format(0));
        int widthMaxNum = 0;
        int numberCount = 10;
        for (int i = 1; i < numberCount; i++) {
            float width = paint.measureText(mNumberFormat.format(i));
            maxWidth = Math.max(maxWidth, width);
            if (maxWidth == width) {
                widthMaxNum = i;
            }
        }
        return measureTextWidthForDifferentFormatType(paint, mNumberFormat.format(widthMaxNum), formatType);
    }

    /**
     * 初始化不同显示情况下，字符串的标准长度
     * EXTRA_LONG_FORMAT  05:22:33:22
     * LONG_FORMAT     05:44:32
     * SHORT_FORMAT    07:40.35
     */
    private float measureTextWidthForDifferentFormatType(Paint paint, String number, int formatter) {
        StringBuilder sb = new StringBuilder();
        if (formatter == LONG_FORMAT) {
            //05:44:32
            sb.append(number).append(number).append(getTimeSeparator())
                    .append(number).append(number).append(getTimeSeparator())
                    .append(number).append(number);
        } else if (formatter == EXTRA_LONG_FORMAT) {
            //05:22:33:22
            sb.append(number).append(number).append(getTimeSeparator())
                    .append(number).append(number).append(getTimeSeparator())
                    .append(number).append(number).append(getTimeSeparator()).append(number).append(number);
            ;
        } else {
            // 07:40.35
            sb.append(number).append(number)
                    .append(getTimeSeparator())
                    .append(number).append(number)
                    .append(OplusStopWatchWrapper.INSTANCE.getDecimalSeparator())
                    .append(number).append(number);
        }
        return paint.measureText(sb.toString());
    }

    public void setStopwatchTextSize(float textSize) {
        final float scale = mContext.getResources().getConfiguration().fontScale;
        mViewTextSize = (int) COUIChangeTextUtil.getSuitableFontSize(textSize, scale, COUIChangeTextUtil.G2);
    }


    public int[] getTimeMeasureTextLengths(int startSize, int endSize) {

        int[] timeLengths = new int[2];
        Paint paint = new Paint();
        paint.setTypeface(Utils.getSansEnTypeface(false));

        final float scale = mContext.getResources().getConfiguration().fontScale;

        timeLengths[0] = measureText(paint, startSize, scale);
        timeLengths[1] = measureText(paint, endSize, scale);

        return timeLengths;
    }

    public int measureText(Paint paint, int textSize, float scale) {
        int tmpTextSize = (int) COUIChangeTextUtil.getSuitableFontSize(textSize, scale, COUIChangeTextUtil.G2);
        paint.setTextSize(tmpTextSize);
        float result = initStandardLength(paint, SHORT_FORMAT);
        return (int) result;
    }

    private String getTimeSeparator() {
        if (mContext != null) {
            return mContext.getResources().getString(R.string.time_separator);
        }
        return ":";
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawDisplayTime(canvas, getFormatType());
    }

    private int getFormatType() {
        int hour = OplusStopWatchWrapper.INSTANCE.getHour();
        if (hour > (HOURS_OF_WHOLE_DAY - 1)) {
            // 05:22:33:22
            return EXTRA_LONG_FORMAT;
        } else if (hour > 0) {
            // 05:44:32
            return LONG_FORMAT;
        } else {
            // 07:40.35
            return SHORT_FORMAT;
        }
    }

    private void drawDisplayTime(Canvas canvas, int formatter) {
        canvas.save();
        canvas.translate((float) (getCenter().x), (float) (getCenter().y));

        mFontMetrics = getFontMetrics();
        String displayTime = OplusStopWatchWrapper.INSTANCE.getTimeStr(formatter, mIsOplusStopWatch);
        float baseYPoint = -(mFontMetrics.bottom + mFontMetrics.top) / 2.0f;

        float timeStandardMaxLength = 0f;
        if (mIsNonArabicNum && (formatter == EXTRA_LONG_FORMAT)) {
            timeStandardMaxLength = (mTimeStandardLengthForNonArabicNum + mTimeStandardLength) / 2;
        } else {
            timeStandardMaxLength = mTimeStandardLength;
        }
        float startXPoint = -timeStandardMaxLength / 2.0f;

        mViewTextPaint.setTextSize(mViewTextSize);
        float hmsLength = mViewTextPaint.measureText(displayTime);
        if ((!mIsNonArabicNum) && ((displayTime.length() > DEFAULT_TIME_FORMAT_LENGTH)
                || (hmsLength > timeStandardMaxLength + SCOPE_OF_WORTH))) {
            int tempSmallSize = (int) ((float) mViewTextSize * timeStandardMaxLength / hmsLength);
            mViewTextPaint.setTextSize(tempSmallSize);
        } else {
            mViewTextPaint.setTextSize(mViewTextSize);
        }

        if (formatter == OplusStopWatch.SHORT_FORMAT) {
            char decimalSeparator = OplusStopWatchWrapper.INSTANCE.getDecimalSeparator();
            mViewTextPaint.setColor(mViewTextColorNormal);
            String timeStr = OplusStopWatchWrapper.INSTANCE.getTimeStr(formatter, mIsOplusStopWatch);
            String[] time = timeStr.split("[" + decimalSeparator + "]");
            if (time.length > 1) {
                if ((time.length > 2) && "si".equals(Locale.getDefault().getLanguage())) {
                    //Deal with Sinhala language which separator is "." not ":"
                    String firstStr = time[0] + decimalSeparator + time[1];
                    canvas.drawText(firstStr, startXPoint, baseYPoint, mViewTextPaint);
                    mViewTextPaint.setColor(mViewTextColorHighLight);
                    canvas.drawText(decimalSeparator + time[2],
                            startXPoint + mViewTextPaint.measureText(firstStr), baseYPoint,
                            mViewTextPaint);
                } else {
                    canvas.drawText(time[0], startXPoint, baseYPoint, mViewTextPaint);
                    mViewTextPaint.setColor(mViewTextColorHighLight);
                    canvas.drawText(decimalSeparator + time[1],
                            startXPoint + mViewTextPaint.measureText(time[0]), baseYPoint,
                            mViewTextPaint);
                }
            } else {
                canvas.drawText(timeStr, startXPoint, baseYPoint, mViewTextPaint);
            }
        } else {
            mViewTextPaint.setColor(mViewTextColorNormal);
            canvas.drawText((OplusStopWatchWrapper.INSTANCE.getTimeStr(formatter, mIsOplusStopWatch)),
                    startXPoint, baseYPoint, mViewTextPaint);
        }
        canvas.restore();
    }

    private FontMetrics getFontMetrics() {
        if (mFontMetrics == null) {
            mFontMetrics = mViewTextPaint.getFontMetrics();
        }
        return mFontMetrics;
    }

    public String getTalkBackTime() {
        long elapseTime = mIsOplusStopWatch ? getElapseTime() : getIntervalElapseTime();
        if (elapseTime == 0) {
            return "0 " + mContext.getString(R.string.timer_sec);
        }
        int day = (int) (elapseTime / (60 * 60 * 1000 * 24));
        int hour = (int) (elapseTime / (60 * 60 * 1000) % 24);
        int minute = (int) ((elapseTime / (60 * 1000)) % 60);
        int second = (int) ((elapseTime / (1000)) % 60);
        int millisecond = (int) ((elapseTime % 1000) / 10);// only show First two digits
        String dayStr = "";
        String hourStr = "";
        String minuteStr = "";
        String secondStr = "";
        if (day > 0) {
            dayStr = getQuantityString(R.plurals.days_short, day);
        }
        if (hour > 0) {
            hourStr = getQuantityString(R.plurals.hours_short, hour);
        }
        if (minute > 0) {
            minuteStr = getQuantityString(R.plurals.minutes_plurals, minute);
        }
        if (second > 0) {
            secondStr = getQuantityString(R.plurals.timer_sec_plurals, second);
            if (millisecond > 0) {
                //bug 2286382, if millisecond < 10, result is 0.x, but result should be 0.0x
                final int millSecondMax = 10;
                if (millisecond >= millSecondMax) {
                    secondStr = mNumberFormat.format(second) + "."
                            + mNumberFormat.format(millisecond)
                            + mContext.getString(R.string.timer_sec);
                } else {
                    secondStr = mNumberFormat.format(second) + ".0"
                            + mNumberFormat.format(millisecond)
                            + mContext.getString(R.string.timer_sec);
                }
            }
        }
        return dayStr + hourStr + minuteStr + secondStr;
    }


    public String getQuantityString(int id, int quantity) {
        final String localizedQuantity = mNumberFormat.format(quantity);
        return mContext.getResources().getQuantityString(id, quantity, localizedQuantity);
    }


    private Point getCenter() {
        if (mCenter == null) {
            mCenter = new Point(getWidth() / 2, getHeight() / 2);
        }
        return mCenter;
    }


    public long getElapseTime() {
        return OplusStopWatchWrapper.INSTANCE.getElapseTime();
    }

    public void setElapseTime(long time) {
        OplusStopWatchWrapper.INSTANCE.setElapseTime(time);
        postInvalidate();
    }

    public long getIntervalElapseTime() {
        return OplusStopWatchWrapper.INSTANCE.getIntervalElapseTime();
    }

    public void setIntervalElapseTime(long time) {
        OplusStopWatchWrapper.INSTANCE.setIntervalElapseTime(time);
        postInvalidate();
    }
}
