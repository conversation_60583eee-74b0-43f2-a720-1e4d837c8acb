/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.TextWeightUtils
import com.oplus.alarmclock.utils.TextWeightUtils.WEIGHT_NINE
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightNoChange

class StopWatchDialView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    companion object {
        private const val TWO = 2
        private const val FIVE = 5
        private const val TEN = 10
        private const val THIRTY = 30
        private const val ONE_HUNDRED_TWENTY = 120
        private const val ANGLE_INTERVAL = 3.0F//120个点，每个点间隔3°
        private const val ANGLE_INTERVAL_SMALL = 12.0F//30个点，每个点间隔12°
        private val mScaleTextList = listOf("60", "05", "10", "15", "20", "25", "30", "35", "40", "45", "50", "55") //表盘显示的数字
        private val mScaleTextListSmall = listOf("30", "05", "10", "15", "20", "25") //表盘显示的数字
    }

    private var mScaleToTopDistance = 0F
    private var mTextToTopDistance = 0F
    private var mScaleWidth = 0F
    private var mScaleLength = 0F
    private var mScaleTextSize = 0F

    private var mSmallCircleToTopDistance = 0F
    private var mSmallScaleToTopDistance = 0F
    private var mSmallTextToTopDistance = 0F
    private var mSmallLongScaleWidth = 0F
    private var mSmallLongScaleLength = 0F
    private var mSmallScaleWidth = 0F
    private var mSmallScaleLength = 0F
    private var mSmallScaleTextSize = 0F
    private var mSmallCircleRadius = 0F
    private var mSmallCircleBorder = 0F

    private var mScaleBoostColor = 0
    private var mScaleColor = 0
    private var mSmallBoostScaleColor = 0
    private var mSmallScaleColor = 0
    private var mScaleTextColor = 0
    private var mSmallCircleBorderStartColor = 0
    private var mSmallCircleBorderEndColor = 0

    private var mCenterX = 0F
    private var mCenterY = 0F
    private var mSmallCenterY = 0F

    //数字刻度画笔
    private val mScaleTextPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getTextPaint(mScaleTextColor, mScaleTextSize)
        }
    }

    //小数字刻度画笔
    private val mScaleSmallTextPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getTextPaint(mScaleTextColor, mSmallScaleTextSize)
        }
    }

    //长刻度线画笔
    private val mScaleLongPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getScalePaint(mScaleBoostColor, mScaleWidth)
        }
    }

    //刻度线画笔
    private val mScalePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getScalePaint(mScaleColor, mScaleWidth)
        }
    }

    private val mScaleSmallBorderPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getBorderPaint(mSmallCircleBorder, mSmallCircleBorderStartColor, mSmallCircleBorderEndColor)
        }
    }

    //小表盘长刻度线画笔
    private val mScaleSmallLongPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getScalePaint(mSmallBoostScaleColor, mSmallLongScaleWidth)
        }
    }

    //小表盘刻度线画笔
    private val mScaleSmallPaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            getScalePaint(mSmallScaleColor, mSmallScaleWidth)
        }
    }

    init {
        initColor(context)
        initSize(context, attrs)
    }

    private fun initColor(context: Context) {
        context.resources?.run {
            mScaleBoostColor = getColor(R.color.app_dial_scale_boost_color, null)
            mScaleColor = getColor(R.color.app_dial_scale_normal_color, null)
            mSmallBoostScaleColor = getColor(R.color.app_dial_small_scale_boost_color, null)
            mSmallScaleColor = getColor(R.color.app_dial_small_scale_normal_color, null)
            mScaleTextColor = getColor(R.color.app_dial_scale_text_color, null)

            mSmallCircleBorderStartColor = getColor(R.color.app_dial_small_circle_border_start_color, null)
            mSmallCircleBorderEndColor = getColor(R.color.app_dial_small_circle_border_end_color, null)
        }
    }

    private fun initSize(context: Context, attrs: AttributeSet?) {
        context.resources?.run {
            val multiple = getMultiple(context, attrs)
            mScaleToTopDistance = getDimension(R.dimen.app_dial_scale_distance_edge_offset) * multiple
            mTextToTopDistance = getDimension(R.dimen.app_dial_scale_txt_distance_edge_offset) * multiple
            mScaleWidth = getDimension(R.dimen.app_dial_scale_width) * multiple
            mScaleLength = getDimension(R.dimen.app_dial_scale_length) * multiple
            mScaleTextSize = getDimension(R.dimen.app_dial_scale_text_size) * multiple

            mSmallCircleToTopDistance = getDimension(R.dimen.app_dial_small_ring_distance_edge_offset) * multiple
            mSmallScaleToTopDistance = getDimension(R.dimen.app_dial_small_scale_distance_edge_offset) * multiple
            mSmallTextToTopDistance = getDimension(R.dimen.app_dial_small_scale_txt_distance_edge_offset) * multiple
            mSmallLongScaleWidth = getDimension(R.dimen.app_dial_small_long_scale_width) * multiple
            mSmallLongScaleLength = getDimension(R.dimen.app_dial_small_long_scale_length) * multiple
            mSmallScaleWidth = getDimension(R.dimen.app_dial_small_short_scale_width) * multiple
            mSmallScaleLength = getDimension(R.dimen.app_dial_small_short_scale_length) * multiple
            mSmallScaleTextSize = getDimension(R.dimen.layout_dp_7_11) * multiple
            mSmallCircleRadius = getDimension(R.dimen.app_dial_small_circle_radius) * multiple
            mSmallCircleBorder = getDimension(R.dimen.app_dial_small_circle_border) * multiple
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mCenterX = w.toFloat() / TWO
        mCenterY = h.toFloat() / TWO
        mSmallCenterY = mSmallCircleToTopDistance + mSmallCircleRadius
    }

    override fun onDraw(canvas: Canvas) {
        drawScale(canvas)
        drawSmallScale(canvas)
    }

    private fun drawScale(canvas: Canvas) {
        for (i in 0 until ONE_HUNDRED_TWENTY) {
            canvas.save()
            canvas.rotate(i * ANGLE_INTERVAL, mCenterX, mCenterY)
            val isNumberScale = (i % TEN) == 0
            if (isNumberScale) {
                canvas.drawLine(
                    mCenterX,
                    mScaleToTopDistance,
                    mCenterX,
                    mScaleToTopDistance + mScaleLength,
                    mScaleLongPaint
                )
                //画数字  是数字刻度的时候，一定是15的倍数，获取这个倍数，从集合里取当前数值
                val numberIndex = i / TEN
                //小时对应的象限，两个数字要高亮
                val text = mScaleTextList[numberIndex]
                val textBound = Rect().apply {
                    mScaleTextPaint.getTextBounds(text, 0, text.length, this)
                }
                canvas.rotate(
                    -i * (ANGLE_INTERVAL),
                    mCenterX,
                    mTextToTopDistance + textBound.height() / TWO
                )
                canvas.drawText(
                    text,
                    mCenterX - textBound.width() / TWO,
                    mTextToTopDistance + textBound.height(),
                    mScaleTextPaint
                )
            } else {
                canvas.drawLine(
                    mCenterX,
                    mScaleToTopDistance,
                    mCenterX,
                    mScaleToTopDistance + mScaleLength,
                    mScalePaint
                )
            }
            canvas.restore()
        }
    }

    private fun drawSmallScale(canvas: Canvas) {
        canvas.drawCircle(mCenterX, mSmallCenterY, mSmallCircleRadius, mScaleSmallBorderPaint)
        for (i in 0 until THIRTY) {
            canvas.save()
            canvas.rotate(i * ANGLE_INTERVAL_SMALL, mCenterX, mSmallCenterY)
            val isNumberScale = (i % FIVE) == 0
            if (isNumberScale) {
                canvas.drawLine(
                    mCenterX,
                    mSmallScaleToTopDistance,
                    mCenterX,
                    mSmallScaleToTopDistance + mSmallLongScaleLength,
                    mScaleSmallLongPaint
                )
                val numberIndex = i / FIVE
                val text = mScaleTextListSmall[numberIndex]
                val textBound = Rect().apply {
                    mScaleSmallTextPaint.getTextBounds(text, 0, text.length, this)
                }
                canvas.rotate(
                    -i * (ANGLE_INTERVAL_SMALL),
                    mCenterX,
                    mSmallTextToTopDistance + textBound.height() / TWO
                )
                canvas.drawText(
                    text,
                    mCenterX - textBound.width() / TWO,
                    mSmallTextToTopDistance + textBound.height(),
                    mScaleSmallTextPaint
                )
            } else {
                canvas.drawLine(
                    mCenterX,
                    mSmallScaleToTopDistance,
                    mCenterX,
                    mSmallScaleToTopDistance + mSmallScaleLength,
                    mScaleSmallPaint
                )
            }
            canvas.restore()
        }
    }

    private fun getMultiple(context: Context, attrs: AttributeSet?): Float {
        var multiple = 1F
        context.resources?.run {
            attrs?.let {
                val obtainAttributes = obtainAttributes(it, R.styleable.StopWatchDialView)
                multiple =
                    obtainAttributes.getFloat(R.styleable.StopWatchDialView_dial_multiple, 1F)
                obtainAttributes.recycle()
            }
        }
        return multiple
    }

    private fun Paint.getScalePaint(scaleColor: Int, scaleWidth: Float): Paint {
        return apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            strokeCap = Paint.Cap.ROUND
            color = scaleColor
            strokeWidth = scaleWidth
        }
    }

    private fun Paint.getBorderPaint(scaleWidth: Float, startColor: Int, endColor: Int): Paint {
        return apply {
            isAntiAlias = true
            style = Paint.Style.STROKE
            strokeWidth = scaleWidth
            shader = LinearGradient(
                mCenterX, mSmallCenterY - mSmallCircleRadius,
                mCenterX, mSmallCenterY + mSmallCircleRadius,
                startColor, endColor, Shader.TileMode.CLAMP
            )
        }
    }

    private fun Paint.getTextPaint(textColor: Int, textSize: Float, textWeight: Int = TextWeightUtils.WEIGHT_MEDIUM): Paint {
        return apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = textColor
            this.textSize = textSize
            setTextWeightNoChange(textWeight)
        }
    }
}