/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchSeedlingHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/18
 ** Author: ********
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ********   2024/11/18     1.0           add file
 ****************************************************************/
@file:Suppress(
    "MaximumLineLength", "WhenExpressionFormattingRule", "LongParameterList", "LongMethod"
)

package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper
import com.oplus.alarmclock.stopwatch.StopWatchService.STATUS_2
import com.oplus.alarmclock.timer.TimerSeedlingHelper
import com.oplus.alarmclock.timer.TimerSeedlingHelper.isSupportFluidCloud
import com.oplus.alarmclock.utils.ChannelManager
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_DEFAULT
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_FAIL
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_LOADING
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_SUCCESS
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FluidWakeLockUtils
import com.oplus.alarmclock.utils.IBaseChannel
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.clock.common.utils.Log.d
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.pantanal.seedling.bean.CancelPanelActionConfigEnum
import com.oplus.pantanal.seedling.bean.PanelActionEnum
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingHostEnum
import com.oplus.pantanal.seedling.bean.SeedlingIntent
import com.oplus.pantanal.seedling.bean.SeedlingIntentFlagEnum
import com.oplus.pantanal.seedling.intent.IIntentResultCallBack
import com.oplus.pantanal.seedling.update.SeedlingCardOptions
import com.oplus.pantanal.seedling.util.SeedlingTool
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicInteger

object StopWatchSeedlingHelper {
    const val METHOD_CONTROL_OPERATE = "controlOperate"
    const val METHOD_FUNCTION_OPERATE = "functionOperate"
    private const val TAG = "StopWatchSeedlingHelper"
    private const val SEEDLING_STOPWATCH_ACTION = "pantanal.intent.business.app.system.STOPWATCH"
    private const val SERVICE_ID = "268451930"
    private const val SERVICE_ID_EX = "268451950"
    private const val PARAMS_STRING_PAGES_ID = "pages/index"
    private const val PARAMS_STRING_INIT_DATA = "initData"
    private const val PARAMS_STRING_STOPWATCH_CAPSULE_PLAY = "stopwatch_capsule_play"
    private const val PARAMS_STRING_STOPWATCH_RUNNING = "stopwatch_running"
    private const val PARAMS_STRING_STOPWATCH_TIME = "stopwatch_time"
    private const val PARAMS_STRING_STOPWATCH_DAY = "stopwatch_day"
    private const val PARAMS_STRING_STOPWATCH_HOUR = "stopwatch_hour"
    private const val PARAMS_STRING_STOPWATCH_MINUTE = "stopwatch_minute"
    private const val PARAMS_STRING_STOPWATCH_SECOND = "stopwatch_second"
    private const val PARAMS_STRING_STOPWATCH_SPLIT_DAY = "stopwatch_split_day"
    private const val PARAMS_STRING_STOPWATCH_SPLIT_HOUR = "stopwatch_split_hour"
    private const val PARAMS_STRING_STOPWATCH_SPLIT_MINUTE = "stopwatch_split_minute"
    private const val PARAMS_STRING_STOPWATCH_FONT_SIZE = "stopwatch_font_size"
    private const val PARAMS_STRING_STOPWATCH_DESCRIPTION = "stopwatch_description"
    private const val PARAMS_STRING_STOPWATCH_VOICE = "stopwatch_voice"

    private const val VALUE_STRING_STOPWATCH_TIME_SPLIT = ":"
    private const val VALUE_STRING_STOPWATCH_FONT_SM = "sm-font"
    private const val VALUE_STRING_STOPWATCH_FONT_MD = "md-font"
    private const val VALUE_STRING_STOPWATCH_FONT_LG = "lg-font"
    private const val VALUE_STRING_STOPWATCH_DESCRIPTION_START = "\$t('strings.start_description')"
    private const val VALUE_STRING_STOPWATCH_DESCRIPTION_PAUSE = "\$t('strings.pause_description')"

    private const val DELAY_CHECK = 3 * 1000L
    private const val DEFAULT_EMPTY_STRING = ""
    private var mCurrentStatus: Int = 0
    private var mCurrentRecordNum: String? = null
    private var mCurrentEntity: FluidCloudStopWatchEntity? = null

    /**
     * 加载卡片状态
     * 0:default
     * 1:loading
     * 2:fail
     * 3:success
     */
    private var mSeedlingCardStatus: AtomicInteger = AtomicInteger(STATUS_LOADCARD_DEFAULT)


    /**
     * 全景息屏监听
     */
    var stopwatchFluidWakeLock: FluidWakeLockUtils? = null
    /**
     * 卡片是不是正在加载状态
     */
    @JvmStatic
    fun isSeedlingCardLoading(): Boolean {
        return mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING
    }

    /**
     * 卡片是不是加载失败(用于推送卡片/更新卡片数据)
     * 初始状态和成功状态是可以推送数据的
     * 加载中/加载失败不能推送数据
     */
    @JvmStatic
    fun isSeedlingCardLoadFail(): Boolean {
        return mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL
    }


    /**
     * 确保显示计时卡
     * @param callBack 加载卡片是否成功的回调
     */
    @JvmStatic
    fun showStopWatchCardSecure(
        context: Context,
        recordNum: String?,
        status: Int,
        entity: FluidCloudStopWatchEntity,
        autoUpdate: Boolean,
        callBack: SeedCallback? = null
    ) {
        if (!VersionUtils.isOsVersion15()
            || !isSupportFluidCloud()
            || DeviceUtils.isSuperPowerSaveMode(context)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
        ) {
            callBack?.onLoadCardResult(false)
            return
        }
        if (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING) {
            d(TAG, "mSeedlingCardStatus is loading")
            return
        }
        mCurrentStatus = status
        mCurrentRecordNum = recordNum
        val pair = getStateAndDescription(recordNum, status)
        if (mSeedlingCardStatus.get() == STATUS_LOADCARD_SUCCESS) {
            updateStopWatchCard(context, pair, entity, autoUpdate)
        } else {
            showStopWatchCard(context, pair, entity, callBack)
        }
    }

    /**
     *  关闭流体云卡
     *  隐藏卡的时候要反注册callback,因多处调用隐藏方法,context来源不一样,所以这里使用applicationcontext
     *  @param context
     */
    @JvmStatic
    fun closeSeedlingCard(context: Context) {
        if (!TimerSeedlingHelper.isSupportFluidCloud() || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL) || (mSeedlingCardStatus.get() == STATUS_LOADCARD_DEFAULT)) {
            return
        }
        d(TAG, "closeSeedlingCard")
        resetHelper()
        SeedlingTool.sendSeedling(
            context,
            SeedlingIntent(action = SEEDLING_STOPWATCH_ACTION, flag = SeedlingIntentFlagEnum.END)
        )
        stopwatchFluidWakeLock?.apply {
            releasePartialWakeLock()
            unRegisterPanoramicAOD()
        }
    }

    /**
     * 用于卡片被回收后，重置状态使用
     * 回收卡片不需要清除数据（如果卡片回收后重建了，需要用到数据）
     */
    @JvmStatic
    fun resetSeedlingCardByDestroy() {
        if ((mSeedlingCardStatus.get() == STATUS_LOADCARD_DEFAULT)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
        ) {
            return
        }
        d(TAG, "resetSeedlingCardByDestroy")
        resetHelper(false)
    }

    /**
     * 重置helper
     * @param clearData 是否需要清除数据（正常关闭流体云需要清除数据，卡片回收重建，则需要保留数据）
     */
    @JvmStatic
    private fun resetHelper(clearData: Boolean = true) {
        d(TAG, "resetHelper")
        if (clearData) {
            mCurrentStatus = 0
            mCurrentRecordNum = null
            mCurrentEntity = null
        }
        mSeedlingCardStatus.set(STATUS_LOADCARD_DEFAULT)
    }

    /**
     * 显示秒表卡
     * @param context
     * @param pair 状态和描述
     * @param entity 数据
     * @param callBack 回调
     */
    @JvmStatic
    private fun showStopWatchCard(
        context: Context,
        pair: Pair<Boolean, String>,
        entity: FluidCloudStopWatchEntity?,
        callBack: SeedCallback?
    ) {
        d(TAG, "showStopWatchCard entity:$entity")
        mCurrentEntity = entity
        val businessData = buildBusinessData(pair, entity)
        val businessInitData = JSONObject().apply {
            put(PARAMS_STRING_INIT_DATA, businessData)
        }
        val options = getSeedlingCardOptions()
        SeedlingTool.sendSeedling(context, SeedlingIntent(
            action = SEEDLING_STOPWATCH_ACTION,
            flag = SeedlingIntentFlagEnum.START,
            data = businessData,
            options = businessInitData,
            cardOptions = options
        ), callBack = object : IIntentResultCallBack {
            override fun onIntentResultCodeCallBack(
                action: String,
                flag: Int,
                resultCode: Int
            ) {
                if (SEEDLING_STOPWATCH_ACTION == action) {
                    mSeedlingCardStatus.updateAndGet {
                        d(TAG, "onIntentResult resultCode:$resultCode")
                        val isSuccess = (resultCode == SeedlingTool.DECISION_RESULT_SUCCEED) || (resultCode == SeedlingTool.DECISION_RESULT_REPEATED_ACTION)
                        callBack?.onLoadCardResult(isSuccess)
                        if (isSuccess) {
                            STATUS_LOADCARD_SUCCESS
                        } else {
                            STATUS_LOADCARD_FAIL
                        }
                    }
                }
            }
        })
        mSeedlingCardStatus.set(STATUS_LOADCARD_LOADING)
        CoroutineScope(Dispatchers.IO).launch {
            d(TAG, "DELAY_CHECK")
            delay(DELAY_CHECK)
            withContext(Dispatchers.Main) {
                d(TAG, "mSeedlingCardStatus.get() :" + mSeedlingCardStatus.get())
                if (mSeedlingCardStatus.get() == STATUS_LOADCARD_SUCCESS) {
                    return@withContext
                }
                mSeedlingCardStatus.getAndUpdate {
                    if (it == STATUS_LOADCARD_LOADING) {
                        callBack?.onLoadCardResult(false)
                        STATUS_LOADCARD_FAIL
                    } else {
                        it
                    }
                }
            }
        }
        stopwatchFluidWakeLock = FluidWakeLockUtils(AlarmSnoozeSeedingHelper.TAG, context)
        stopwatchFluidWakeLock?.registerPanoramicAOD(context)
    }

    private fun buildBusinessData(
        pair: Pair<Boolean, String>,
        entity: FluidCloudStopWatchEntity?
    ): JSONObject {
        return JSONObject().apply {
            val capsulePlay = (entity?.thanOneMinute == false) && pair.first
            put(PARAMS_STRING_STOPWATCH_CAPSULE_PLAY, capsulePlay)
            put(PARAMS_STRING_STOPWATCH_RUNNING, pair.first)
            put(PARAMS_STRING_STOPWATCH_TIME, entity?.timerMsg)
            val includeDay = entity?.includeDay == true
            put(PARAMS_STRING_STOPWATCH_DAY, if (includeDay) entity?.day else DEFAULT_EMPTY_STRING)
            put(
                PARAMS_STRING_STOPWATCH_SPLIT_DAY,
                if (includeDay) VALUE_STRING_STOPWATCH_TIME_SPLIT else DEFAULT_EMPTY_STRING
            )
            val includeHour = entity?.includeHours == true
            put(
                PARAMS_STRING_STOPWATCH_HOUR,
                if (includeHour) entity?.hour else DEFAULT_EMPTY_STRING
            )
            put(
                PARAMS_STRING_STOPWATCH_SPLIT_HOUR,
                if (includeHour) VALUE_STRING_STOPWATCH_TIME_SPLIT else DEFAULT_EMPTY_STRING
            )
            put(PARAMS_STRING_STOPWATCH_MINUTE, entity?.minute)
            put(PARAMS_STRING_STOPWATCH_SPLIT_MINUTE, VALUE_STRING_STOPWATCH_TIME_SPLIT)
            put(PARAMS_STRING_STOPWATCH_SECOND, entity?.second)
            if (includeDay) {
                put(PARAMS_STRING_STOPWATCH_FONT_SIZE, VALUE_STRING_STOPWATCH_FONT_SM)
            } else if (includeHour) {
                put(PARAMS_STRING_STOPWATCH_FONT_SIZE, VALUE_STRING_STOPWATCH_FONT_MD)
            } else {
                put(PARAMS_STRING_STOPWATCH_FONT_SIZE, VALUE_STRING_STOPWATCH_FONT_LG)
            }
            put(PARAMS_STRING_STOPWATCH_DESCRIPTION, pair.second)
            put(PARAMS_STRING_STOPWATCH_VOICE, entity?.voiceStr)
        }
    }

    /**
     * 发送流体云数据
     * @param context 上下文
     * @param pair 状态和描述
     * @param entity 数据
     */
    @JvmStatic
    private fun updateStopWatchCard(
        context: Context,
        pair: Pair<Boolean, String>,
        entity: FluidCloudStopWatchEntity?,
        autoUpdate: Boolean = true
    ) {
        if (isInvalidSend(entity, autoUpdate)) {
            return
        }
        mCurrentEntity = entity
        val businessData = buildBusinessData(pair, entity)
        sendDataToSeedlingCard(context, businessData)
    }

    /**
     * 获取cardOptions
     */
    @JvmStatic
    private fun getSeedlingCardOptions(): SeedlingCardOptions {
        return SeedlingCardOptions().apply {
            pageId = PARAMS_STRING_PAGES_ID
            isMilestone = true
            grade = SeedlingCardOptions.GRADE_4
            lockScreenShowHostMap = mapOf(SeedlingHostEnum.StatusBar to false)
            showHostMap = mapOf(SeedlingHostEnum.StatusBar to true)
            remindType = SeedlingCardOptions.REMIND_TYPE_NORMAL
            panelActionConfigMap = mapOf(
                PanelActionEnum.PANEL_SLIDE to CancelPanelActionConfigEnum.Retract,
                PanelActionEnum.OUTSIDE_CLICK to CancelPanelActionConfigEnum.Retract
            )
        }
    }

    private fun getStateAndDescription(recordNum: String?, status: Int): Pair<Boolean, String> {
        return if (status == STATUS_2) {
            Pair(false, VALUE_STRING_STOPWATCH_DESCRIPTION_PAUSE)
        } else {
            if (TextUtils.isEmpty(recordNum)) {
                Pair(true, VALUE_STRING_STOPWATCH_DESCRIPTION_START)
            } else {
                Pair(
                    true,
                    String.format(
                        AlarmClockApplication.getInstance().resources.getString(R.string.count_nums),
                        recordNum
                    )
                )
            }
        }
    }

    /**
     * 发送数据到流体云卡
     * @param businessData 卡片数据
     */
    @JvmStatic
    private fun sendDataToSeedlingCard(
        context: Context,
        businessData: JSONObject
    ) {
        val seedlingCardId = PrefUtils.getString(
            context,
            ClockConstant.SEEDLING_PREF_FILE_NAME,
            ClockConstant.KEY_SEEDLING_STOPWATCH_CARD_ID,
            DEFAULT_EMPTY_STRING
        )
        if (!TextUtils.isEmpty(seedlingCardId)) {
            SeedlingTool.updateAllCardData(
                SeedlingCard.build(seedlingCardId), businessData, getSeedlingCardOptions()
            )
        }
    }

    /**
     * 是否是非法推送(过滤毫秒级别推送)
     * @param entity 数据
     * @param intercept 是否拦截验证
     */
    @JvmStatic
    private fun isInvalidSend(
        entity: FluidCloudStopWatchEntity?,
        intercept: Boolean = true
    ): Boolean {
        if (!intercept) {
            return false
        }
        return mCurrentEntity?.timerMsg?.equals(entity?.timerMsg) ?: true
    }

    /**
     * 系统关卡恢复后,更新数据
     * 用于切换系统分身后,卡片恢复数据用
     */
    @JvmStatic
    fun asynDataByReCreateCard() {
        if (!TimerSeedlingHelper.isSupportFluidCloud()
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
            ) {
            return
        }
        d(TAG, "asynDataByReCreateCard")
        CoroutineScope(Dispatchers.IO).launch {
            mSeedlingCardStatus.set(STATUS_LOADCARD_SUCCESS)
            val pair = getStateAndDescription(mCurrentRecordNum, mCurrentStatus)
            val jsonObject = buildBusinessData(pair, mCurrentEntity)
            sendDataToSeedlingCard(AlarmClockApplication.getInstance(), jsonObject)
        }
    }

    /**
     * 查询计时器流体云serviceId
     */
    @JvmStatic
    fun queryStopWatchFluidServiceId(context: Context): String {
        val serviceId: String =
            if (DeviceUtils.isExpVersion(context) && ChannelManager.getChannelUtils()
                    .getChannel() == IBaseChannel.CHANNEL_WPLUS
            ) {
                SERVICE_ID_EX
            } else {
                SERVICE_ID
            }
        return serviceId
    }

    private fun operateStopWatchByAction(context: Context, type: Int) {
        val intent = Intent(context, StopWatchService::class.java)
        intent.putExtra(StopwatchNotificationManager.STOPWATCH_ACTION_TYPE, type)
        intent.action = StopwatchNotificationManager.STOPWATCH_ACTION + type
        context.startService(intent)
    }

    fun interface SeedCallback {
        fun onLoadCardResult(isSuccess: Boolean)
    }

    /**
     * 处理方法回调
     * @param context
     * @param method
     * @param extras
     */
    @JvmStatic
    fun handleMethodCalls(context: Context, method: String, extras: Bundle?) {
        when (method) {
            METHOD_CONTROL_OPERATE -> {
                operateStopWatchByAction(
                    context, StopwatchNotificationManager.STOPWATCH_NOTIFICATION_TYPE_PAUSE_CONTINUE
                )
            }

            METHOD_FUNCTION_OPERATE -> {
                operateStopWatchByAction(
                    context, StopwatchNotificationManager.STOPWATCH_NOTIFICATION_TYPE_COUNT_CANCEL
                )
            }
        }
    }
}