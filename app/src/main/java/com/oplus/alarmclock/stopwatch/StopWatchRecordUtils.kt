/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StopWatchRecordUtils.kt
 ** Description:处理时间字符串转换时间
 ** Version: 1.0
 ** Date : 2022/10/28
 ** Author: <PERSON><PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoJun    2022/10/28     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log
import android.content.Context
import android.text.TextUtils
import java.io.IOException
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.text.ParseException
import java.util.regex.Pattern

object StopWatchRecordUtils {

    private const val TAG = "StopWatchRecordUtils"
    private const val TWO_DIGIT = 10
    private const val SEC_IN_MILLISEC = 1000

    private const val SYMBOL_THREE = 3
    private const val SYMBOL_TWO = 2
    private const val SYMBOL_ONE = 1
    private const val SYMBOL_ZERO = 0
    private const val DEFAULT_STR = "0"
    private const val TYPE_SPLITE = "#"
    private const val TYPE_COLUMN = ":"
    private val TYPE_PATTERN = Pattern.compile(TYPE_SPLITE)

    /**通过判断当前时间字符串中分隔符的个数，得到是哪个时间格式*/
    @JvmStatic
    private fun patternTime(patternText: String): Int {
        val matcher = TYPE_PATTERN.matcher(patternText)
        var count = 0
        while (matcher.find()) {
            count++
        }
        return count
    }

    /**时间字符串转换回时间long*/
    @JvmStatic
    fun getLastIntervalTime(context: Context, timeStr: String): Long {
        return if (!TextUtils.isEmpty(timeStr)) {
            try {
                val splitStr: String = context.resources.getString(R.string.time_separator)
                val intervalTime =
                    timeStr.replace(TYPE_COLUMN, TYPE_SPLITE).replace(splitStr, TYPE_SPLITE)
                val symbolNum = patternTime(intervalTime)
                var dayStr = DEFAULT_STR
                var hourStr = DEFAULT_STR
                var minuteStr = DEFAULT_STR
                var secondStr = DEFAULT_STR
                var millisecondStr = DEFAULT_STR
                val split = intervalTime.split(TYPE_SPLITE).toTypedArray()
                when (symbolNum) {
                    SYMBOL_ONE -> {
                        minuteStr = split[SYMBOL_ZERO]
                        secondStr = split[SYMBOL_ONE].substring(SYMBOL_ZERO, SYMBOL_TWO)
                        millisecondStr = split[SYMBOL_ONE].substring(SYMBOL_THREE)
                    }
                    SYMBOL_TWO -> {
                        hourStr = split[SYMBOL_ZERO]
                        minuteStr = split[SYMBOL_ONE]
                        secondStr = split[SYMBOL_TWO]
                    }
                    SYMBOL_THREE -> {
                        dayStr = split[SYMBOL_ZERO]
                        hourStr = split[SYMBOL_ONE]
                        minuteStr = split[SYMBOL_TWO]
                        secondStr = split[SYMBOL_THREE]
                    }
                }
                Log.d(
                    TAG,
                    "$dayStr=hourStr=$hourStr=minuteStr=$minuteStr=secondStr=$secondStr=millisecondStr=$millisecondStr"
                )
                val numberFormat = NumberFormat.getInstance()
                val day =
                    numberFormat.parse(dayStr)
                        .toInt() * OplusStopWatch.HOURS_OF_WHOLE_DAY * OplusStopWatch.HOUR_IN_MILLISEC
                val hour = numberFormat.parse(hourStr).toInt() * OplusStopWatch.HOUR_IN_MILLISEC
                val minute =
                    numberFormat.parse(minuteStr).toInt() * OplusStopWatch.MINUTE_IN_MILLISEC
                val second = numberFormat.parse(secondStr).toInt() * SEC_IN_MILLISEC
                val millisecond = numberFormat.parse(millisecondStr).toInt() * TWO_DIGIT
                Log.d(TAG, "$day=hour=$hour=minute=$minute=second=$second=millisecond=$millisecond")

                (day + hour + minute + second + millisecond).toLong()
            } catch (e: IOException) {
                Log.e(TAG, "IOException: " + e.message)
                0L
            } catch (e: ParseException) {
                Log.e(TAG, "ParseException: " + e.message)
                0L
            }
        } else {
            0L
        }
    }
}
