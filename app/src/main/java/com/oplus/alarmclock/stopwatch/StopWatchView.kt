/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.util.AttributeSet
import android.widget.RelativeLayout
import com.coui.appcompat.orientationutil.COUIOrientationUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.FoldScreenUtils

open class StopWatchView : RelativeLayout {
    private var mStopWatchMinutePointer: StopWatchMinutePointer? = null
    private var mStopWatchSecondPointer: StopWatchSecondPointer? = null

    constructor(context: Context?) : this(context, null)
    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        context?.let {
            val uiMode = FoldScreenUtils.uiMode(it, COUIOrientationUtil.isInMultiWindowMode(it))
            if (FoldScreenUtils.UiMode.LARGE_VERTICAL == uiMode || FoldScreenUtils.UiMode.LARGE_HORIZONTAL == uiMode) {
                inflate(it, R.layout.stopwatch_pointer_large, this)
            } else if (FoldScreenUtils.UiMode.MIDDLE == uiMode) {
                inflate(it, R.layout.stopwatch_pointer_mid, this)
            } else {
                inflate(it, R.layout.stopwatch_pointer_normal, this)
            }
            mStopWatchMinutePointer = findViewById(R.id.stop_watch_pointer_minute)
            mStopWatchSecondPointer = findViewById(R.id.stop_watch_pointer_second)
        }
    }

    fun update(time: Long) {
        mStopWatchMinutePointer?.update(time)
        mStopWatchSecondPointer?.update(time)
    }

    fun onResume() {
        mStopWatchMinutePointer?.onResume()
        mStopWatchSecondPointer?.onResume()
    }

    fun onPause() {
        mStopWatchMinutePointer?.onPause()
        mStopWatchSecondPointer?.onPause()
    }

    fun onStop() {
        mStopWatchMinutePointer?.onStop()
        mStopWatchSecondPointer?.onStop()
    }

    fun record() {
//        mStopWatchSecondPointer?.record()
    }
}