/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchListManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.animation.ValueAnimator
import android.content.Context
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.math.MathUtils
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopwatchMainIncludeBinding
import com.oplus.alarmclock.databinding.StopwatchMidIncludeBinding
import com.oplus.alarmclock.utils.FoldScreenUtils
import kotlin.math.abs
import kotlin.math.roundToInt

class StopWatchListManager(val mContext: Context) {
    companion object {
        private const val DIAL_ALPHA_SCALE = 0.2F
        private const val SCROLL_REBOUND_SCALE = 0.23F
        private const val ZERO_F = 0F
        private const val LOCATION_NONE = -1
        private const val TWO = 2
        private const val ONE_F = 1F
        private const val DURATION = 400L
        private const val DURATION_OPEN = 100L
        private const val PATH_X1 = 0.25F
        private const val PATH_Y1 = 0.10F
        private const val PATH_X2 = 0.20F
        private const val PATH_Y2 = 1.00F
        private const val ZERO_073 = 0.73F
        private const val ZERO_08 = 0.8F
    }

    @VisibleForTesting
    val mOnScrollListener by lazy { initScrollListener() }
    var mScrollDistance = 0
    var mCurrentRatio = ZERO_F
    private lateinit var mRecyclerView: RecyclerView
    private lateinit var mLayoutManager: LinearLayoutManager
    private lateinit var mDialClock: RelativeLayout
    private lateinit var mStopWatchTv: StopWatchTextView
    private lateinit var mStopWatchTitleTv: FrameLayout
    private lateinit var mDivider: View
    private lateinit var mStopWatchScale: StopWatchView

    private var mTempScrollY = 0
    private var mHeaderHeight = 0
    private var mEndValue = 0
    private var mOffsetTop = 0
    private var mIsDown = false
    private var mPosition = 0

    /**
     * 标题距离顶部的距离
     */
    private var mMsgDistance = 0
    private var mNormalDistance = 0

    /**
     * 表盘是否已经缩放
     */
    private var mStopWatchZoom = false
    private var mAlphaTop = ZERO_F
    private var mAlphaStart = ZERO_F
    private var mAlphaBottom = ZERO_F
    private val mAnimator by lazy { initAnimator() }
    private var mEnable = true
    private var mTitleCompensate = 0
    private var mAutoScroll = true
    private var mUiMode: FoldScreenUtils.UiMode = FoldScreenUtils.UiMode.NORMAL
    private var mRecordStatus = false

    /**
     * 是否浮窗模式
     */
    private var mIsFloatingWindow: Boolean = false

    init {
        mContext.resources.run {
            mTitleCompensate = getDimensionPixelSize(R.dimen.layout_dp_14)
        }
    }

    /**
     * 初始化
     * */
    fun initManager(
        stopWatchInclude: StopwatchMainIncludeBinding?,
        stopWatchMidInclude: StopwatchMidIncludeBinding?,
        triple: Triple<Int, Int, Int>,
        uiMode: FoldScreenUtils.UiMode,
        stopWatchCl: ConstraintLayout,
        stopWatchList: RecyclerView,
        stopWatchListTitle: FrameLayout,
        isFloatingWindow: Boolean
    ) {

        stopWatchMidInclude?.run {
            mOffsetTop = triple.third
            mHeaderHeight = triple.first + (stopWatchCl.paddingTop ?: 0)
            mScrollDistance = triple.second
            mAlphaBottom = triple.second * DIAL_ALPHA_SCALE
            mAlphaTop = triple.second - mAlphaBottom * ZERO_073
            mAlphaStart = triple.second - mAlphaTop
            mRecyclerView = stopWatchList
            mLayoutManager = stopWatchList.layoutManager as LinearLayoutManager
            mDialClock = stopWatchRl
            mStopWatchTitleTv = stopWatchListTitle
            mStopWatchTv = stopWatch
            mDivider = stopWatchDivider
            mStopWatchScale = stopWatchScale
            if (mMsgDistance == 0 || !isFloatingWindow) {
                mMsgDistance = getMsgLineDistance()
            }
        }

        stopWatchInclude?.run {
            mOffsetTop = triple.third
            mHeaderHeight = triple.first + parent.paddingTop + (stopWatchCl.paddingTop ?: 0)
            mScrollDistance = triple.second
            mAlphaBottom = triple.second * DIAL_ALPHA_SCALE
            mAlphaTop = triple.second - mAlphaBottom * ZERO_073
            mAlphaStart = triple.second - mAlphaTop
            mRecyclerView = stopWatchList
            mLayoutManager = stopWatchList.layoutManager as LinearLayoutManager
            mDialClock = stopWatchRl
            mStopWatchTitleTv = stopWatchListTitle
            mStopWatchTv = stopWatch
            mDivider = stopWatchDivider
            mStopWatchScale = stopWatchScale
            if (mMsgDistance == 0 || !isFloatingWindow) {
                mMsgDistance = getMsgLineDistance()
            }
        }

        if (mNormalDistance != 0 && !isFloatingWindow) {
            mMsgDistance = mNormalDistance
        }
        //浮窗模式下且未缩放或者上次为非浮窗状态，需要对距离进行缩放
        if (isFloatingWindow && (!mStopWatchZoom || !mIsFloatingWindow)) {
            mStopWatchZoom = true
            mNormalDistance = mMsgDistance
            //浮窗内需要对距离进行缩放
            mMsgDistance = (mMsgDistance * FoldScreenUtils.FLOATING_WINDOW_STOP_WATCH_TITLE_SCALE).toInt()
        }
        mIsFloatingWindow = isFloatingWindow
        //恢复默认值
        setEnable(true)
        setStatus(false)
        setOnScrollListener()
        //触发滑动，更新视图
        listScroll(0)
        mUiMode = uiMode
    }

    fun stop(isHover: Boolean) {
        reset()
        mCurrentRatio = 0F
        updateView(0F, 0, isHover)
    }

    /**
     * 中屏无计次无需重置view
     */
    fun stop() {
        mCurrentRatio = 0F
        reset()
    }

    fun reset() {
        stopScroll()
        mRecyclerView.run {
            if (childCount > 0) {
                smoothScrollToPosition(0)
            }
            startNestedScroll(ViewCompat.SCROLL_AXIS_VERTICAL)
        }
    }

    fun setEnable(enable: Boolean) {
        mEnable = enable
    }

    fun setStatus(isRecord: Boolean) {
        mRecordStatus = isRecord
    }

    /**
     * 更新uiMode
     */
    fun setUiMode(uiMode: FoldScreenUtils.UiMode) {
        mUiMode = uiMode
    }

    fun scrollToBottom() {
        if (mCurrentRatio >= ZERO_08) {
            mLayoutManager.scrollToPositionWithOffset(0, -mScrollDistance)
        } else {
            mLayoutManager.scrollToPosition(0)
        }
    }

    private fun getMsgLineDistance(): Int {
        val location = IntArray(2)
        mDivider.getLocationInWindow(location)
        val top = location[1]
        mStopWatchTitleTv.getLocationInWindow(location)
        val bottom = location[1]
        return abs(bottom - top - mTitleCompensate)
    }

    /**
     * 联动原理，获取列表滑动监听获取列表侵入Header的百分比，控制当前Header的左右上下距离和透明度的变化
     * */
    private fun onListScroll() {
        //置顶后再向上滑动，不做处理
        if (mCurrentRatio == ONE_F && !mIsDown) {
            return
        }
        if (mEnable) {
            mRecyclerView.run {
                val diff = getDiff()
                if (diff >= 0) {
                    val tempRatio = getRatio(diff)
                    if (tempRatio == mCurrentRatio) {
                        return
                    }
                    mCurrentRatio = tempRatio
                    if (mEnable && mAutoScroll) {
                        updateView(mCurrentRatio, diff, false)
                    }
                }
            }
        }
    }

    /**
     * 更新表盘、数字等View
     * */
    private fun updateView(currentRatio: Float, currentOffset: Int, isHover: Boolean) {
        mContext.resources.run {
            updateDialClock(currentOffset, currentRatio)
            updateDialClockTv(currentRatio, isHover)
        }
    }

    fun updateViewToOpen() {
        reset()
        ValueAnimator.ofFloat(1F, 0F).apply {
            duration = DURATION_OPEN
            addUpdateListener { animation ->
                val d = animation.animatedValue as Float
                updateDialClock((mHeaderHeight * d).roundToInt(), d)
                updateDialClockTv(d, false)
            }
        }.start()
    }


    /**
     * 更新表盘
     * */
    private fun updateDialClock(currentOffset: Int, currentRatio: Float) {
        val mAlpha = MathUtils.clamp(abs(currentOffset / mAlphaBottom), ZERO_F, ONE_F)
        val scale = ONE_F - DIAL_ALPHA_SCALE * currentRatio
        mStopWatchScale.run {
            scaleX = scale
            scaleY = scale
            setAlpha(this, ONE_F - mAlpha)
        }
        mDialClock.run {
            scaleX = scale
            scaleY = scale
            setAlpha(this, ONE_F - mAlpha)
        }
    }

    /**
     * 更新数字时间、时区描述、横线
     * */
    private fun updateDialClockTv(currentRatio: Float, isHover: Boolean) {
        mStopWatchTv.run {
            val currentMarginTop =
                    ((getMarginTop() - mOffsetTop) * (ONE_F - currentRatio)).toInt() + mOffsetTop
            val param = (layoutParams as ConstraintLayout.LayoutParams).apply {
                topMargin = currentMarginTop
            }
            if (!isHover) {
                layoutParams = param
                updateDialClockMsgTv(currentRatio)
            }
            //updateLine(currentRatio)
        }
    }

    private fun updateDialClockMsgTv(currentRatio: Float) {
        var compensation = 0
        if (mUiMode == FoldScreenUtils.UiMode.MIDDLE) {
            //中屏下跳转标题间距
            compensation = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_40)
        }
        mStopWatchTitleTv.translationY = -(currentRatio * (mMsgDistance - compensation))
    }

    private fun updateLine(currentRatio: Float) {
        mDivider.run {
            val alpha = if (currentRatio >= ZERO_08) 1F else 0F
            setAlpha(this, alpha)
        }
    }

    private fun setAlpha(view: View, mAlpha: Float) {
        view.run {
            alpha = mAlpha
        }
    }

    /**
     * 获取列表顶部侵入和Header的距离
     * */
    private fun getDiff(): Int {
        mPosition = getFirstChildLocationY(mRecyclerView)
        if (mPosition < 0) {
            return LOCATION_NONE
        }
        val diff = mHeaderHeight - mPosition
        return MathUtils.clamp(diff, 0, mHeaderHeight)
    }

    /**
     * 获取列表顶部的Y轴位置
     * */
    private fun getFirstChildLocationY(viewGroup: ViewGroup): Int {
        val size = viewGroup.childCount
        if (size <= 0) {
            return LOCATION_NONE
        } else {
            for (i in 0 until size) {
                val child = viewGroup.getChildAt(i) ?: return LOCATION_NONE
                if (child.visibility == View.VISIBLE) {
                    val location = IntArray(TWO)
                    child.getLocationInWindow(location)
                    return location[1]
                }
            }
        }
        return LOCATION_NONE
    }

    private fun setOnScrollListener() {
        mRecyclerView.run {
            addOnScrollListener(mOnScrollListener)
            setOnScrollChangeListener { _, _, _, _, _ ->
                onListScroll()
            }
        }
    }

    /**
     * 滚动列表，展开表盘
     */
    fun listScroll(diff: Int) {
        if (diff >= 0) {
            val tempRatio = getRatio(diff)
            if (tempRatio == mCurrentRatio) {
                return
            }
            mCurrentRatio = tempRatio
            updateView(mCurrentRatio, diff, false)
        }
    }

    fun setAutoScroll(autoScroll: Boolean) {
        mAutoScroll = autoScroll
    }

    /**
     * 列表状态变化监听，用于列表停止滑动后，自动上下滑动的逻辑
     * */
    private fun initScrollListener(): RecyclerView.OnScrollListener {
        return object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (!mAutoScroll) {
                    return
                }
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (mRecordStatus) {
                        //状态切换
                        if (mCurrentRatio != ZERO_F && mCurrentRatio != ONE_F) {
                            if (mCurrentRatio > ZERO_08) {
                                //数字模式
                                listScroll(mScrollDistance)
                            } else {
                                //展开表盘
                                listScroll(0)
                            }
                        }
                    } else {
                        //动画回弹处理
                        val diff = getDiff()
                        if (diff == 0) {
                            //展开表盘
                            listScroll(0)
                            return
                        }
                        if (diff >= mScrollDistance) {
                            listScroll(mScrollDistance)
                            return
                        }
                        if (diff <= 1) {
                            return
                        }
                        val isRebound = (diff.toFloat() / mScrollDistance) > SCROLL_REBOUND_SCALE
                        mEndValue = if (mIsDown) {
                            if (isRebound) {
                                -diff
                            } else {
                                mScrollDistance - diff
                            }
                        } else {
                            if (isRebound) {
                                mScrollDistance - diff
                            } else {
                                -diff
                            }
                        }
                        rebound()
                    }
                } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    mEnable = true
                    mRecordStatus = false
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mIsDown = dy < 0
            }
        }
    }

    private fun initAnimator(): ValueAnimator {
        return ValueAnimator.ofFloat(ZERO_F, ONE_F).apply {
            duration = DURATION
            interpolator = PathInterpolator(PATH_X1, PATH_Y1, PATH_X2, PATH_Y2)
            addUpdateListener { animation ->
                val range = animation.animatedValue as Float
                val currentY = (range * mEndValue).toInt()
                val scroll = currentY - mTempScrollY
                mRecyclerView.scrollBy(0, scroll)
                mTempScrollY = currentY
            }
        }
    }

    private fun rebound() {
        mTempScrollY = 0
        mAnimator.start()
    }

    /**
     * 获取列表侵入部分占可滑动部分的百分比
     * */
    private fun getRatio(diff: Int): Float {
        val ratio = diff.toFloat() / mScrollDistance
        return MathUtils.clamp(ratio, ZERO_F, ONE_F)
    }

    private fun stopScroll() {
        val time = SystemClock.uptimeMillis()
        val event =
                MotionEvent.obtain(time, time, MotionEvent.ACTION_CANCEL, ZERO_F, ZERO_F, 0)
        mRecyclerView.dispatchTouchEvent(event)
        event.recycle()
    }
}