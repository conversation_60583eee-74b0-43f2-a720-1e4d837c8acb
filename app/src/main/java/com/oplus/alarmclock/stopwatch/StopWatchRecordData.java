/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.stopwatch;

import android.content.Context;

import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;

import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;

public class StopWatchRecordData {
    public static final String TAG = "StopWatchRecordView";
    private static final int TWO_DIGIT = 10;
    private static final int MIN_IN_HOUR = 60;
    private static final int SEC_IN_MILLISEC = 1000;

    private long mStopWatchTime;
    private NumberFormat mNumberFormat;
    private Context mContext;

    StopWatchRecordData(long time, Context context) {
        mNumberFormat = NumberFormat.getInstance();
        mStopWatchTime = time;
        mContext = context;
    }

    private String getText() {
        long mElapseTime = mStopWatchTime;
        int hour = (int) (mElapseTime / (OplusStopWatch.HOUR_IN_MILLISEC));
        if (hour > (OplusStopWatch.HOURS_OF_WHOLE_DAY - 1)) {
            return timeFormat(mElapseTime, OplusStopWatch.EXTRA_LONG_FORMAT);
        } else if (hour > 0) {
            return timeFormat(mElapseTime, OplusStopWatch.LONG_FORMAT);
        } else {
            return timeFormat(mElapseTime, OplusStopWatch.SHORT_FORMAT);
        }
    }

    private String timeFormat(long time, int formatter) {
        int day = (int) (time
                / (OplusStopWatch.HOUR_IN_MILLISEC * OplusStopWatch.HOURS_OF_WHOLE_DAY));
        int hour = (int) (time / (OplusStopWatch.HOUR_IN_MILLISEC)
                % OplusStopWatch.HOURS_OF_WHOLE_DAY);
        int minute = (int) ((time / (MIN_IN_HOUR * SEC_IN_MILLISEC)) % MIN_IN_HOUR);
        int second = (int) ((time / (SEC_IN_MILLISEC)) % MIN_IN_HOUR);
        int millisecond = (int) ((time % SEC_IN_MILLISEC) / TWO_DIGIT);// only show First two digits
        String newStr = "";
        String dayStr = mNumberFormat.format(day);
        String hourStrTemp = mNumberFormat.format(0) + mNumberFormat.format(hour);
        String hourStr = (hour < TWO_DIGIT) ? hourStrTemp : mNumberFormat.format(hour);
        String minStrTemp = mNumberFormat.format(0) + mNumberFormat.format(minute);
        String minuteStr = (minute < TWO_DIGIT) ? minStrTemp
                : mNumberFormat.format(minute);
        String secStrTemp = mNumberFormat.format(0) + mNumberFormat.format(second);
        String secondStr = (second < TWO_DIGIT) ? secStrTemp
                : mNumberFormat.format(second);
        String millsecStrTemp = mNumberFormat.format(0)
                + mNumberFormat.format(millisecond);
        String millisecondStr = (millisecond < TWO_DIGIT) ? millsecStrTemp
                : mNumberFormat.format(millisecond);

        String timeSeparator = ":";
        if (mContext != null) {
            timeSeparator = mContext.getResources().getString(R.string.time_separator);
        } else {
            Log.e(TAG, "mContext null");
        }

        switch (formatter) {
            case OplusStopWatch.EXTRA_LONG_FORMAT:
                newStr = dayStr + timeSeparator + hourStr + timeSeparator + minuteStr + timeSeparator + secondStr;
                break;
            case OplusStopWatch.LONG_FORMAT:
                newStr = hourStr + timeSeparator + minuteStr + timeSeparator + secondStr;
                break;
            case OplusStopWatch.SHORT_FORMAT:
                newStr = minuteStr + timeSeparator + secondStr
                        + DecimalFormatSymbols.getInstance().getDecimalSeparator() + millisecondStr;
                break;
            default:
                break;
        }
        return newStr;
    }

    public String getTimeRecord() {
        return getText();
    }

    public String getTimeDifference(long mLastElapseTime) {
        return caculateTimeDifference(mLastElapseTime);
    }

    private String caculateTimeDifference(long mLastElapseTime) {
        long mElapseTime = mStopWatchTime;
        if (mElapseTime == 0 || mElapseTime < mLastElapseTime) {
            return getText();
        }

        int hour = (int) ((mElapseTime - mLastElapseTime) / (OplusStopWatch.HOUR_IN_MILLISEC));
        if (hour < 0) {
            Log.i(TAG, "hour <0 , hour= " + hour);
            return timeFormat(mElapseTime, OplusStopWatch.SHORT_FORMAT);
        }

        if (hour > (OplusStopWatch.HOURS_OF_WHOLE_DAY - 1)) {
            return timeFormat(mElapseTime - mLastElapseTime, OplusStopWatch.EXTRA_LONG_FORMAT);
        } else if (hour > 0) {
            return timeFormat(mElapseTime - mLastElapseTime, OplusStopWatch.LONG_FORMAT);
        } else {
            return timeFormat((mElapseTime / 10 - mLastElapseTime / 10) * 10, OplusStopWatch.SHORT_FORMAT);
        }
    }

    public Long getTimeDuration(long mLastElapseTime) {
        return (mStopWatchTime - mLastElapseTime);
    }
}
