/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchAnimationManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/5/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/5/28     1.0            add file
 ****************************************************************/
@file:Suppress("FuncSingleCommentRule", "ParameterStyleBracesRule", "LongMethod", "WorldClockAnimationManager", "EmptyIfBlock",
        "NoBlankLineBeforeRbrace", "ParameterListWrapping", "SpacingAroundParens", "LongParameterList", "MaximumLineLength")

package com.oplus.alarmclock.stopwatch

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup
import android.view.animation.*
import androidx.core.animation.addListener
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.NestedScrollableHost
import com.oplus.alarmclock.view.dial.SimpleAnimationListener
import java.lang.ref.WeakReference

class StopWatchAnimationManager(private var uiMode: UiMode? = UiMode.NORMAL) {
    companion object {
        private const val ZERO_POINT_FIVE = 0.85F
        private const val ZERO_F = 0F
        private const val ONE_F = 1.0F
        private const val HALF = 0.5F
        private const val COMPENSATE = 0.5F
        private const val TWO = 2
        private const val DURATION_RECYCLER = 200L
        private const val DURATION_DIAL = 400L
        private const val DURATION_ALPHA = 200L
        private const val DURATION_TRANSLATION_X = 500L
        private const val ANIMATOR_ALPHA = "alpha"
        private const val ANIMATOR_TRANSLATION_X = "translationX"
    }

    var mCurrentIsCenter = uiMode != UiMode.MIDDLE

    /**
     * 按钮动画是否在执行中，执行按钮动画不能点击开启秒表
     */
    var buttonAnimation = false
    private var misHover = false
    fun setIsHover(isHover: Boolean) {
        misHover = isHover
    }

    fun setUiMode(uiMode: UiMode?) {
        this.uiMode = uiMode
    }

    /**
     * 孔雀、平板表盘平移放大动画
     * @param dial 表盘
     * @param stopWatchScale 表盘指针
     * @param rv 列表
     * @param isToCenter 是否居中
     * @param isConfidential
     *
     */
    fun startDialMarginAnimation(stopWatch: View, stopWatchScale: View?, dial: View, parent: NestedScrollableHost, rv: View, isToCenter: Boolean, isConfidential: Boolean, isInit: Boolean) {
        if (!isInit) {
            return
        }
        if (isToCenter != mCurrentIsCenter) {
            mCurrentIsCenter = isToCenter
            if (uiMode == UiMode.LARGE_HORIZONTAL || uiMode == UiMode.LARGE_VERTICAL) {
                //大屏执行列表平移，表盘动画
                startRvAnimation(rv, isToCenter)
                startDialAnimation(dial, isToCenter, isConfidential)
            }
            if (UiMode.MIDDLE == uiMode) {
                parent.clipChildren = !isToCenter
                //中屏执行表盘放大缩小动画
                moveScale(dial, isToCenter, false)
                stopWatchScale?.run {
                    moveScale(this, isToCenter, false)
                    stopwatchScaleMargin(this, isToCenter)
                }
                moveScale(stopWatch, isToCenter, true)
                stopwatchTimeMargin(stopWatch, isToCenter)
            }
        }
    }

    /**
     * 秒表时间位置设置无动画
     */
    fun stopwatchTimeMarginNoAnimator(view: View?, isCenter: Boolean) {
        view?.apply {
            val layoutPar = layoutParams as ViewGroup.MarginLayoutParams
            if (isCenter) {
                layoutPar.topMargin = layoutPar.topMargin + FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
            } else {
                layoutPar.topMargin = layoutPar.topMargin - FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
            }
            layoutParams = layoutPar
        }
    }

    /**
     *  秒表时间动画
     */
    private fun stopwatchTimeMargin(view: View?, isCenter: Boolean) {
        view?.apply {
            val layoutPar = layoutParams as ViewGroup.MarginLayoutParams
            val start = layoutPar.topMargin
            val end = if (isCenter) {
                layoutPar.topMargin + FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
            } else {
                layoutPar.topMargin - FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
            }
            val valueAnimator = ValueAnimator.ofInt(start, end).setDuration(DURATION_DIAL)
            valueAnimator.interpolator = COUIMoveEaseInterpolator()
            valueAnimator.addUpdateListener { animation: ValueAnimator? ->
                if (animation != null) {
                    val animValue = animation.animatedValue
                    if (animValue != null && animValue is Int) {
                        layoutPar.topMargin = animValue
                        layoutParams = layoutPar
                    }
                }
            }
            valueAnimator.start()
        }
    }

    /**
     *  秒表指针动画
     *  加COMPENSATE是为了Float计算结果补偿，防止数字的乘除越来越小
     */
    fun stopwatchScaleMargin(view: View?, isCenter: Boolean) {
        view?.run {
            val layoutPar = layoutParams as ViewGroup.MarginLayoutParams
            val start = layoutPar.topMargin
            val end = if (isCenter) {
                start * FoldScreenUtils.DIAL_SCALE_MOVE + COMPENSATE
            } else {
                start / FoldScreenUtils.DIAL_SCALE_MOVE + COMPENSATE
            }
            ValueAnimator.ofInt(start, end.toInt()).apply {
                duration = DURATION_DIAL
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animation ->
                    val margin = animation.animatedValue
                    layoutPar.topMargin = margin as Int
                    layoutParams = layoutPar
                }
                addListener(onEnd = {
                    layoutPar.topMargin = end.toInt()
                    layoutParams = layoutPar
                }, onCancel = {
                    layoutPar.topMargin = end.toInt()
                    layoutParams = layoutPar
                })
            }.start()
        }
    }

    /**
     * 秒表指针位置计算
     */
    fun stopwatchScaleMarginNoAnimator(view: View?, isCenter: Boolean) {
        view?.run {
            val layoutPar = layoutParams as ViewGroup.MarginLayoutParams
            val start = layoutPar.topMargin
            val end = if (isCenter) {
                start * FoldScreenUtils.DIAL_SCALE_MOVE + COMPENSATE
            } else {
                start / FoldScreenUtils.DIAL_SCALE_MOVE + COMPENSATE
            }
            layoutPar.topMargin = end.toInt()
            layoutParams = layoutPar
        }
    }

    fun upDateCenter(isToCenter: Boolean) {
        if ((isToCenter != mCurrentIsCenter)) {
            mCurrentIsCenter = isToCenter
        }
    }

    fun setDialLayout(view: View, isToCenter: Boolean, dialDistance: Pair<Int, Int>?) {
        val mDialDistance = dialDistance ?: getDialDistance(view)
        val distance = mDialDistance.first
        val move = (if (isToCenter || misHover) 0 else distance)
        (view.layoutParams as ViewGroup.MarginLayoutParams).run {
            marginEnd = move
            view.layoutParams = this
        }
    }

    /**
     * 获取表盘的间距
     */
    private fun getDialDistance(view: View): Pair<Int, Int> {
        view.let {
            it.resources.run {
                val all = displayMetrics.widthPixels
                val distance = (all / TWO)
                return Pair(distance, 0)
            }
        }
    }

    /**
     * 获取表盘的间距
     */
    private fun getDialDistance12(): Pair<Int, Int> {
        AlarmClockApplication.getInstance().let {
            it.resources.run {
                val all = displayMetrics.widthPixels / TWO
                val isOslo = (UiMode.LARGE_HORIZONTAL == uiMode) || (UiMode.LARGE_VERTICAL == uiMode)
                val dial = if (isOslo) {
                    getDimensionPixelOffset(R.dimen.layout_dp_335) / TWO
                } else {
                    getDimensionPixelOffset(R.dimen.layout_dp_280) / TWO
                }
                val start = if (isOslo) {
                    getDimensionPixelOffset(R.dimen.layout_dp_103)
                } else {
                    getDimensionPixelOffset(R.dimen.layout_dp_45)
                }
                val distance = all - dial - start
                return Pair(distance, start)
            }
        }
    }

    /**
     * 孔雀、平板RecyclerView动画
     * */
    private fun startRvAnimation(view: View, isToCenter: Boolean) {
        val fromAlpha = if (isToCenter) 1F else 0F
        val toAlpha = if (isToCenter) 0F else 1F
        val alpha = AlphaAnimation(fromAlpha, toAlpha).apply {
            duration = DURATION_RECYCLER
            startOffset = if (isToCenter) 0 else DURATION_RECYCLER
            fillAfter = true
            isFillEnabled = true
        }
        val scaleFrom = if (isToCenter) 1F else ZERO_POINT_FIVE
        val scaleTo = if (isToCenter) ZERO_POINT_FIVE else 1F
        val scale =
                ScaleAnimation(
                        scaleFrom,
                        scaleTo,
                        scaleFrom,
                        scaleTo,
                        Animation.RELATIVE_TO_SELF,
                        HALF,
                        Animation.RELATIVE_TO_SELF,
                        HALF
                ).apply {
                    duration = DURATION_RECYCLER
                }
        val animationSet = AnimationSet(false).apply {
            addAnimation(alpha)
            addAnimation(scale)
        }
        view.startAnimation(animationSet)
    }

    /**
     * 孔雀、平板表盘整体动画
     * */
    fun startDialAnimation(view: View, isToCenter: Boolean, isConfidential: Boolean) {
        val dialDistance = if (isConfidential) getDialDistance12() else getDialDistance(view)
        val distance = dialDistance.first
        var move = (if (isToCenter) distance else -distance).toFloat()
        if (Utils.isRtl()) {
            move = -move
        }
        val animation: Animation = TranslateAnimation(0F, move / TWO, 0F, 0F).apply {
            duration = DURATION_DIAL
            fillAfter = true
            isFillEnabled = true
            interpolator = COUIMoveEaseInterpolator()
            setAnimationListener(object : SimpleAnimationListener() {
                override fun onAnimationEnd(animation: Animation) {
                    view.clearAnimation()
                    setDialLayout(view, isToCenter, dialDistance)
                }
            })
        }
        view.startAnimation(animation)
    }

    /**
     * 表盘缩放（无动画）
     * @param view
     * @param isCenter 是否居中，居中则放大表盘
     */
    fun toScale(view: View, isCenter: Boolean, isText: Boolean) {
        val toScale = if (isCenter || misHover) FoldScreenUtils.DIAL_SCALE_MOVE else 1.0F
        view.run {
            if (!isText) {
                pivotX = width / FoldScreenUtils.NUMBER_TWO
                pivotY = 0f
            }
            scaleX = toScale
            scaleY = toScale
        }
    }

    /**
     * 浮窗缩放
     */
    fun toFloatingWindowScale(view: View, scale: Float) {
        view.post(kotlinx.coroutines.Runnable {
            view.run {
                pivotX = width / FoldScreenUtils.NUMBER_TWO
                pivotY = 0f
                scaleX = scale
                scaleY = scale
            }
        })
    }

    /**
     * 表盘缩放（动画）
     * @param view
     * @param isCenter 是否居中
     */
    private fun moveScale(view: View?, isCenter: Boolean, isText: Boolean) {
        val toScale = if (isCenter || misHover) FoldScreenUtils.DIAL_SCALE_MOVE else 1.0F
        view?.run {
            if (!isText) {
                pivotX = width / FoldScreenUtils.NUMBER_TWO
                pivotY = 0f
            }
            val fromScale = view.scaleX
            ValueAnimator.ofFloat(fromScale, toScale).apply {
                duration = DURATION_DIAL
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animation ->
                    val d = animation.animatedValue as Float
                    scaleX = d
                    scaleY = d
                }
                addListener(onEnd = {
                    scaleX = toScale
                    scaleY = toScale
                }, onCancel = {
                    scaleX = toScale
                    scaleY = toScale
                })
            }.start()
        }
    }


    /**
     * 底部按钮显示平移动画
     */
    fun displayButtonAnimation(button: View, isLeft: Boolean) {
        val buttonSide = WeakReference(button).get()
        buttonSide?.run {
            var start = if (isLeft) marginEnd.toFloat() else -marginStart.toFloat()
            if (start == ZERO_F) {
                alpha = ONE_F
                return
            }
            if (Utils.isRtl()) {
                start = -start
            }
            translationX = start
            alpha = ZERO_F
            val alpha = ObjectAnimator.ofFloat(this, ANIMATOR_ALPHA, ZERO_F, ONE_F).apply {
                duration = DURATION_ALPHA
                interpolator = COUIEaseInterpolator()

            }
            val translateX = ObjectAnimator.ofFloat(this, ANIMATOR_TRANSLATION_X, start, ZERO_F).apply {
                duration = DURATION_TRANSLATION_X
                interpolator = COUIMoveEaseInterpolator()
            }
            AnimatorSet().apply {
                playTogether(alpha, translateX)
                start()
                doOnCancel {
                    resetButton(buttonSide, true)
                }
                doOnEnd {
                    cancel()
                }
            }
        }
    }

    /**
     * 底部按钮隐藏动画
     */
    fun hideButtonAnimation(button: View, isLeft: Boolean, centerButton: View) {
        val buttonSide = WeakReference(button).get()
        val buttonStart = WeakReference(centerButton).get()
        buttonSide?.run {
            var start = if (isLeft) marginEnd.toFloat() else -marginStart.toFloat()
            if (start == ZERO_F) {
                visibility = View.INVISIBLE
                return
            }
            if (Utils.isRtl()) {
                start = -start
            }
            val alpha = ObjectAnimator.ofFloat(this, ANIMATOR_ALPHA, ONE_F, ZERO_F).apply {
                duration = DURATION_ALPHA
                interpolator = COUIEaseInterpolator()
                doOnCancel {
                    resetButton(buttonSide, false)
                }
                doOnEnd {
                    visibility = View.INVISIBLE
                }
            }
            val translateX = ObjectAnimator.ofFloat(this, ANIMATOR_TRANSLATION_X, ZERO_F, start).apply {
                duration = DURATION_TRANSLATION_X
                interpolator = COUIMoveEaseInterpolator()
                doOnStart {
                    if (isLeft) buttonAnimation = true
                }
                doOnCancel {
                    if (isLeft) buttonAnimation = false
                    resetButton(buttonSide, false)
                }
                doOnEnd {
                    if (isLeft) buttonAnimation = false
                    buttonStart?.isEnabled = true
                    buttonSide.alpha = ONE_F
                }
            }
            AnimatorSet().apply {
                playTogether(alpha, translateX)
                start()
                doOnEnd {
                    cancel()
                }
            }
        }
    }

    /**
     * 重置按钮状态
     */
    private fun resetButton(button: View, isDisplay: Boolean) {
        button.visibility = if (isDisplay) View.VISIBLE else View.INVISIBLE
        button.alpha = ONE_F
    }

    /**
     * 秒表悬停表盘动画
     */
    fun postureAnimator(start: Float, end: Float, view: View) {
        ValueAnimator.ofFloat(start, end).apply {
            duration = DURATION_RECYCLER
            addUpdateListener { animation ->
                val d = animation.animatedValue as Float
                view.translationY = d
            }
        }.start()
    }


    /**
     * 悬停模式计次列表动画
     */
    fun listAnimator(
            start: Int, end: Int, view: View,
            animatorListener: Animator.AnimatorListener?, dur: Long,
    ) {
        ValueAnimator.ofInt(start, end).apply {
            duration = dur
            addUpdateListener { animation ->
                val d = animation.animatedValue as Int
                view.translationY = d.toFloat()
            }
            if (animatorListener != null) {
                addListener(animatorListener)
            }
        }.start()
    }
}