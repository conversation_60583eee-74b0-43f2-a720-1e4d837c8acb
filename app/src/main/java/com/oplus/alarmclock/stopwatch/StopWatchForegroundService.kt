/****************************************************************
 ** Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchForegroundService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: TimerForegroundService
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  W9002127  2024/12/11     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.lifecycle.LifecycleService
import com.oplus.alarmclock.stopwatch.StopwatchNotificationManager.STOPWATCH_NOTIFICATION_ID
import com.oplus.alarmclock.utils.NotificationUtils
import com.oplus.clock.common.event.LiteEventBus.Companion.instance
import com.oplus.clock.common.utils.Log

class StopWatchForegroundService : LifecycleService() {
    companion object {
        const val TAG = "StopWatchForegroundService"
    }

    private var mBinder: IBinder? = StopwatchForegroundBinder()
    override fun onCreate() {
        liteEventBusState()
        val notificationUtils = NotificationUtils()
        notificationUtils.sendForegroundNotificationByOld(
            this,
            this,
            STOPWATCH_NOTIFICATION_ID,
            NotificationUtils.STOPWATCH
        )
        super.onCreate()
        Log.d(TAG, "onCreate")
    }
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_NOT_STICKY
    }
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        stopForeground(STOP_FOREGROUND_REMOVE)
        instance.releaseObserver(hashCode().toString())
    }

    inner class StopwatchForegroundBinder : Binder() {
        fun getService(): StopWatchForegroundService = this@StopWatchForegroundService
    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        return mBinder
    }

    private fun liteEventBusState() {
        instance.with(NotificationUtils.STOP_STOPWATCH_FOREGROUND_SERVICE, hashCode().toString())
            .observe(this) {
                Log.d(TAG, "stopSelf: ")
                stopSelf()
            }
    }
}