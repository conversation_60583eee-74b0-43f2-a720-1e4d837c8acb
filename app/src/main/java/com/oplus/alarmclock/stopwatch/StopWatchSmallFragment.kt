/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StopWatchSmallFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopwatchMainViewSplitBinding
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.LocalColorRecyclerView

class StopWatchSmallFragment : StopWatchFragment<StopwatchMainViewSplitBinding>() {

    companion object {
        private const val TAG = "StopWatchSmallFragment"
    }

    override fun layoutId(): Int {
        return R.layout.stopwatch_main_view_split
    }

    override fun initListener() {
        super.initListener()
        mViewBinding?.clickListener = this
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout,
                    null, R.menu.action_menu_icon_stop_watch)
        }
    }

    override fun initTitle() {
        mViewBinding?.apply {
            val fontScale = resources.configuration.fontScale
            val size = resources.getDimension(R.dimen.text_size_sp_12)
            Utils.setSuitableFontSize(titleStartTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleMiddleTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleEndTv, size, fontScale, COUIChangeTextUtil.G2)
        }
    }

    override fun stopWatchInterval(): StopWatchTextSmallView? {
        return mViewBinding?.stopWatchDotTv
    }

    override fun stopWatchCl(): ConstraintLayout? {
        return mViewBinding?.stopWatchCl
    }

    override fun stopWatch(): StopWatchTextView? {
        return mViewBinding?.stopWatch
    }

    override fun stopWatchView(): StopWatchView? {
        return null
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.nextComponent
    }

    override fun buttonCount(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun listView(): LocalColorRecyclerView? {
        return mViewBinding?.stopWatchList
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun listTitle(): FrameLayout? {
        return mViewBinding?.stopWatchListTitle
    }

    override fun clockSize(): Int {
        return 0
    }
}