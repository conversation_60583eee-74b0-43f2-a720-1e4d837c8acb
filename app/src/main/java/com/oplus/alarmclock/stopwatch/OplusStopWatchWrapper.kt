/*
 *
 *  * ***************************************************************
 *  * Copyright (C), 2010-2021, OPLUS Mobile Comm Corp., Ltd.
 *  * VENDOR_EDIT
 *  * File:  - OplusStopWatchWrapper.kt
 *  * Version: 1.0
 *  * Date : 2021/03/18
 *  * Author: hewei
 *  * ---------------------Revision History: -----------------------
 *  * <author>    <data>                        <version >     <desc>
 *  * hewei       2021/03/18   1.0            OplusStopWatchWrapper.kt
 *  * ***************************************************************
 *
 *
 */
@file:Suppress("MaximumLineLength")
package com.oplus.alarmclock.stopwatch

import android.text.TextUtils
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.util.*

/**
 *  stopwatch 包装类，以解决进程killed后stopwatch对象被回收，无法获取当前秒表时间的问题
 */
object OplusStopWatchWrapper {

    private const val TAG = "OplusStopWatchUtil"
    private const val DEFAULT_MILLISECONDS: Long = 999
    private var mNumberFormat: NumberFormat? = null
    private var mElapseTime: Long = 0
    private var mHour = 0
    private var mIntervalElapseTime: Long = 0

    fun setIntervalElapseTime(time: Long) {
        mIntervalElapseTime = time
    }

    fun getIntervalElapseTime(): Long {
        return mIntervalElapseTime
    }

    fun setNumberFormat(numberFormat: NumberFormat) {
        mNumberFormat = numberFormat;
    }

    fun setElapseTime(time: Long) {
        mElapseTime = time
        mHour = (time / OplusStopWatch.HOUR_IN_MILLISEC).toInt()
    }

    fun getElapseTime(): Long {
        return mElapseTime
    }

    fun getHour(): Int {
        return mHour
    }

    fun getDisplayTimeStr(): String {
        val hour = mHour
        when {
            (hour > OplusStopWatch.HOURS_OF_WHOLE_DAY - 1) -> {
                return getFlashBackTimeStr(OplusStopWatch.EXTRA_LONG_FORMAT)
            }
            (hour > 0) -> {
                return getFlashBackTimeStr(OplusStopWatch.LONG_FORMAT)
            }
            else -> {
                val string = getFlashBackTimeStr(OplusStopWatch.SHORT_FORMAT)
                val timeSeparator = AlarmClockApplication.getInstance()?.resources?.getString(R.string.time_separator)

                //00:10:60 通知显示最小单位为毫秒，所以需要截取显示
                val endIndex = 3
                return try {
                    timeSeparator?.let {
                        string.substring(0, string.indexOf(it).plus(endIndex))
                    } ?: string
                } catch (e: Exception) {
                    Log.e(TAG, "getDisplayTimeStr error:$e")
                    string
                }

            }
        }

    }

    fun getStopWatchEntity(): FluidCloudStopWatchEntity {
        val mElapseStopWatchTime = mElapseTime
        val resources = AlarmClockApplication.getInstance().resources
        val timerMsg: String
        val voiceStr: String
        val day = (mElapseStopWatchTime / (60 * 60 * 1000 * 24)).toInt()
        val hour = (mElapseStopWatchTime / (60 * 60 * 1000) % 24).toInt()
        val minute = (mElapseStopWatchTime / (60 * 1000) % 60).toInt()
        val second = (mElapseStopWatchTime / 1000 % 60).toInt()
        val numberFormat = android.icu.text.NumberFormat.getInstance()
        val dayStrTemp = numberFormat.format(0) + numberFormat.format(day.toLong())
        val dayStr = if (day < 10) dayStrTemp else numberFormat.format(day.toLong())
        val hourStrTmp = numberFormat.format(0) + numberFormat.format(hour.toLong())
        val hourStr = if (hour < 10) hourStrTmp else numberFormat.format(hour.toLong())
        val minStrTemp = numberFormat.format(0) + numberFormat.format(minute.toLong())
        val minuteStr = if (minute < 10) minStrTemp else numberFormat.format(minute.toLong())
        val secStrTemp = numberFormat.format(0) + numberFormat.format(second.toLong())
        val secondStr = if (second < 10) secStrTemp else numberFormat.format(second.toLong())
        val timeSeparator = resources.getString(R.string.time_separator)
        if (day > 0) {
            timerMsg = dayStr + timeSeparator + hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
            voiceStr = resources.getQuantityString(R.plurals.days_short, day, day) + resources.getQuantityString(R.plurals.hours_short, hour, hour) + resources.getQuantityString(R.plurals.minutes_plurals, minute, minute) + resources.getQuantityString(R.plurals.timer_sec_plurals, second, second)
            return FluidCloudStopWatchEntity(timerMsg, dayStr, hourStr, minuteStr, secondStr,
                includeDay = true,
                includeHours = true,
                thanOneMinute = true,
                voiceStr = voiceStr
            )
        }
        if (hour > 0) {
            timerMsg = hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
            voiceStr = resources.getQuantityString(R.plurals.hours_short, hour, hour) + resources.getQuantityString(R.plurals.minutes_plurals, minute, minute) + resources.getQuantityString(R.plurals.timer_sec_plurals, second, second)
            return FluidCloudStopWatchEntity(timerMsg, dayStr, hourStr, minuteStr, secondStr,
                includeDay = false,
                includeHours = true,
                thanOneMinute = true,
                voiceStr = voiceStr
            )
        }

        timerMsg = minuteStr + timeSeparator + secondStr
        voiceStr = resources.getQuantityString(R.plurals.minutes_plurals, minute, minute) + resources.getQuantityString(R.plurals.timer_sec_plurals, second, second)
        return FluidCloudStopWatchEntity(timerMsg, dayStr, hourStr, minuteStr, secondStr,
            includeDay = false,
            includeHours = false,
            thanOneMinute = minute > 0,
            voiceStr = voiceStr
        )
    }

    fun getDecimalSeparator(): Char {
        return if (TextUtils.equals(Locale.getDefault().toString(), "de_DE")) {
            '.'
        } else DecimalFormatSymbols.getInstance().decimalSeparator
    }

    fun getTimeStr(formatter: Int, isStopWatch: Boolean): String {
        val mElapseStopWatchTime = if (isStopWatch) mElapseTime else mIntervalElapseTime
        val day = (mElapseStopWatchTime / (60 * 60 * 1000 * 24)).toInt()
        val hour = (mElapseStopWatchTime / (60 * 60 * 1000) % 24).toInt()
        val minute = (mElapseStopWatchTime / (60 * 1000) % 60).toInt()
        val second = (mElapseStopWatchTime / 1000 % 60).toInt()
        val millisecond = (mElapseStopWatchTime % 1000 / 10).toInt() // only show First two digits
        var newStr = ""
        try {
            if (mNumberFormat == null) {
                mNumberFormat = NumberFormat.getInstance()
            }
            val dayStr = mNumberFormat?.format(day.toLong())
            val formatZero = mNumberFormat?.format(0)
            val hourStr =
                if (hour < 10) formatZero + mNumberFormat?.format(hour.toLong()) else mNumberFormat?.format(
                    hour.toLong()
                )
            val minuteStr =
                if (minute < 10) formatZero + mNumberFormat?.format(minute.toLong()) else mNumberFormat?.format(
                    minute.toLong()
                )
            val secondStr =
                if (second < 10) formatZero + mNumberFormat?.format(second.toLong()) else mNumberFormat?.format(
                    second.toLong()
                )
            val minStrTemp = formatZero + mNumberFormat?.format(millisecond.toLong())
            val millisecondStr =
                if (millisecond < 10) minStrTemp else mNumberFormat?.format(millisecond.toLong())
            val timeSeparator =
                AlarmClockApplication.getInstance().resources.getString(R.string.time_separator)

            when (formatter) {
                OplusStopWatch.EXTRA_LONG_FORMAT -> newStr =
                    dayStr + timeSeparator + hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
                OplusStopWatch.LONG_FORMAT -> newStr =
                    hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
                OplusStopWatch.SHORT_FORMAT -> newStr = (minuteStr + timeSeparator + secondStr
                        + getDecimalSeparator() + millisecondStr)
                else -> {
                }
            }
        } catch (e: ArithmeticException) {
            Log.e(TAG, "getTimeStr e:${e.message}")
        }
        return newStr
    }

    fun getFlashBackTimeStr(formatter: Int): String {
        var mElapseStopWatchTime = mElapseTime
        val day = (mElapseStopWatchTime / (60 * 60 * 1000 * 24)).toInt()
        val hour = (mElapseStopWatchTime / (60 * 60 * 1000) % 24).toInt()
        val minute = (mElapseStopWatchTime / (60 * 1000) % 60).toInt()
        val second = (mElapseStopWatchTime / 1000 % 60).toInt()
        val millisecond = (mElapseStopWatchTime % 1000 / 10).toInt() // only show First two digits
        var newStr = ""
        try {
            val numberFormat = android.icu.text.NumberFormat.getInstance()
            val dayStr = numberFormat.format(day.toLong())
            val hourStrTmp = numberFormat.format(0) + numberFormat.format(hour.toLong())
            val hourStr = if (hour < 10) hourStrTmp else numberFormat.format(hour.toLong())
            val minStrTemp = numberFormat.format(0) + numberFormat.format(minute.toLong())
            val minuteStr = if (minute < 10) minStrTemp else numberFormat.format(minute.toLong())
            val secStrTemp = numberFormat.format(0) + numberFormat.format(second.toLong())
            val secondStr = if (second < 10) secStrTemp else numberFormat.format(second.toLong())
            val millisecondStrTemp =
                numberFormat.format(0) + numberFormat.format(millisecond.toLong())
            val millisecondStr =
                if (millisecond < 10) millisecondStrTemp else numberFormat.format(millisecond.toLong())
            val timeSeparator =
                AlarmClockApplication.getInstance().resources.getString(R.string.time_separator)

            when (formatter) {
                OplusStopWatch.EXTRA_LONG_FORMAT -> newStr =
                    dayStr + timeSeparator + hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
                OplusStopWatch.LONG_FORMAT -> newStr =
                    hourStr + timeSeparator + minuteStr + timeSeparator + secondStr
                OplusStopWatch.SHORT_FORMAT -> newStr = (minuteStr + timeSeparator + secondStr
                        + getDecimalSeparator() + millisecondStr)
                else -> {
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getFlashBackTimeStr e:${e.message}")
        }
        return newStr
    }
}