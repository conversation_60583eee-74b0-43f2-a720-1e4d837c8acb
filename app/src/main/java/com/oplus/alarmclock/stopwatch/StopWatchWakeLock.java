/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

/**
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: StopWacth.java
 * ModuleName: StopWacth Author: MaCong Create Date: Description: the main wake lock manager
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2010-9-24 MaCong CheckList
 */
package com.oplus.alarmclock.stopwatch;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.PowerManager;

import com.oplus.clock.common.utils.Log;

/**
 * Hold a wakelock that can be acquired in the AlarmReceiver and released in the AlarmAlert activity
 */
public class StopWatchWakeLock {
    private static final String TAG = "StopWatchWakeLock";
    private static PowerManager.WakeLock sWakeLock;

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuWakeLock(Context context) {
        if (sWakeLock != null) {
            return;
        }
        sWakeLock = ((PowerManager) context.getApplicationContext().getSystemService(Context.POWER_SERVICE)).
                newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ON_AFTER_RELEASE,
                        "StopWatchWakeLock");

        sWakeLock.acquire();
    }

    public static void releaseCpuLock() {
        try {
            if ((sWakeLock != null) && (sWakeLock.isHeld())) {
                sWakeLock.release();
                sWakeLock = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "releaseCpuLock " + e.getMessage());
        }
    }
}
