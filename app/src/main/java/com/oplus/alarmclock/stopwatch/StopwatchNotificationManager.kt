/*
 *
 *  * ***************************************************************
 *  * Copyright (C), 2010-2021, OPLUS Mobile Comm Corp., Ltd.
 *  * VENDOR_EDIT
 *  * File:  - StopwatchNotificationManager.kt
 *  * Version: 1.0
 *  * Date : 2021/03/17
 *  * Author: hewei
 *  * ---------------------Revision History: -----------------------
 *  * <author>    <data>                        <version >     <desc>
 *  * hewei       2021/03/17   1.0            StopwatchNotificationManager.kt
 *  * ***************************************************************
 *
 *
 */

package com.oplus.alarmclock.stopwatch

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.stopwatch.StopWatchSeedlingHelper.queryStopWatchFluidServiceId
import com.oplus.alarmclock.utils.NotificationUtils
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.VersionUtils

object StopwatchNotificationManager {

    const val STOPWATCH_ACTION_TYPE = "stopwatch_action_type"
    const val STOPWATCH_ACTION = "stopwatch_notification_action_"
    const val STOPWATCH_NOTIFICATION_TYPE_PAUSE_CONTINUE = 1
    const val STOPWATCH_NOTIFICATION_TYPE_COUNT_CANCEL = 2
    const val STOPWATCH_NOTIFICATION_TYPE_INTERCEPT_SEND = 3
    const val STOPWATCH_NOTIFICATION_ID = -2000
    const val STOPWATCH__NOTIFICATION_CHANNEL_ID = "stopwatch_notification_channel"

    private const val TAG = "StopwatchNotificationManager"

    private var mLeftPendingIntent: PendingIntent? = null
    private var mRightPendingIntent: PendingIntent? = null
    private var mDeletePendingIntent: PendingIntent? = null

    fun showStopwatchNotification(
        context: Context,
        recordNum: String?,
        time: String,
        status: Int
    ) {

        var leftBtnString: String? = null
        var rightBtnString: String? = null

        if (status == STOPWATCH_NOTIFICATION_TYPE_PAUSE_CONTINUE) {
            leftBtnString = context.getString(R.string.record)
            rightBtnString = context.getString(R.string.text_timer_btn_pause)
        } else if (status == STOPWATCH_NOTIFICATION_TYPE_COUNT_CANCEL) {
            leftBtnString = context.getString(R.string.RePostion)
            rightBtnString = context.getString(R.string.text_timer_btn_continue)
        }

        //1.获取通知对象
        val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val b = Notification.Builder(context, STOPWATCH__NOTIFICATION_CHANNEL_ID)

        //2.设置Notification.Builder属性
        b.apply {
            setAutoCancel(false)
            setSmallIcon(R.drawable.ic_launcher_clock)
            setContentTitle(context.getString(R.string.StopWatch_Title))
            //3.设置计次描述

            if (status == STOPWATCH_NOTIFICATION_TYPE_COUNT_CANCEL) {
                b.setContentText(time + "  " + context.getString(R.string.paused))
            } else {
                val num = if (recordNum == null) "" else "  " + String.format(
                    context.getString(R.string.count_nums),
                    recordNum
                )
                b.setContentText(time + num)
            }

            b.setShowWhen(false)
            setOngoing(true)
            setContentIntent(getEnterAppPendingIntent(context))
            setDeleteIntent(getDeletePendingIntent(context))

            //4.设置左侧按钮属性
            addAction(
                Notification.Action.Builder(
                    R.drawable.ic_launcher_clock,
                    leftBtnString,
                    getLeftPendingIntent(context)
                ).build()
            )
            //5.设置右侧按钮属性
            addAction(
                Notification.Action.Builder(
                    R.drawable.ic_launcher_clock,
                    rightBtnString,
                    getRightPendingIntent(context)
                ).build()
            )
        }

        //6.设置NotificationChannel
        val name: CharSequence = context.getString(R.string.stopwatch_notification_label)
        val channel = NotificationChannel(
            STOPWATCH__NOTIFICATION_CHANNEL_ID,
            name,
            NotificationManager.IMPORTANCE_DEFAULT
        )
        channel.setSound(null, null)
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        nm.createNotificationChannel(channel)


        val notification = b.build()
        notification.flags = Notification.FLAG_NO_CLEAR
        if (VersionUtils.isOSVersion1501()) {
            val serviceID = queryStopWatchFluidServiceId(context)
            notification.extras.putString(NotificationUtils.FLUID_SERVICE_ID, serviceID)
        }
        //7.发送notification
        nm.notify(STOPWATCH_NOTIFICATION_ID, notification)
    }

    fun cancelTimerNotification(context: Context) {
        val nm = context
            .getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        nm.cancel(STOPWATCH_NOTIFICATION_ID)
        mLeftPendingIntent = null
        mRightPendingIntent = null
        mDeletePendingIntent = null
    }

    private fun getLeftPendingIntent(context: Context): PendingIntent {
        if (mLeftPendingIntent == null) {
            mLeftPendingIntent = getStopwatchPendingIntent(context, STOPWATCH_NOTIFICATION_TYPE_COUNT_CANCEL)
        }
        return mLeftPendingIntent!!
    }

    private fun getRightPendingIntent(context: Context): PendingIntent {
        if (mRightPendingIntent == null) {
            mRightPendingIntent = getStopwatchPendingIntent(context, STOPWATCH_NOTIFICATION_TYPE_PAUSE_CONTINUE)
        }
        return mRightPendingIntent!!
    }

    private fun getDeletePendingIntent(context: Context): PendingIntent {
        if (mDeletePendingIntent == null) {
            mDeletePendingIntent = getStopwatchPendingIntent(context, STOPWATCH_NOTIFICATION_TYPE_INTERCEPT_SEND)
        }
        return mDeletePendingIntent!!
    }

    private fun getStopwatchPendingIntent(context: Context, type: Int): PendingIntent {
        Log.i(TAG, "getStopwatchPendingIntent type$type")
        val intent = Intent(context, com.oplus.alarmclock.stopwatch.StopWatchService::class.java)
        intent.putExtra(STOPWATCH_ACTION_TYPE, type)
        intent.action = STOPWATCH_ACTION + type
        return PendingIntent.getService(context, 0, intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT))
    }

    private fun getEnterAppPendingIntent(context: Context) : PendingIntent {
        val intent = Intent(context, AlarmClock::class.java)
        intent.action = AlarmClock.ACTION_START_BY_NOTIFICATION
        intent.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_STOPWATCH)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        return PendingIntent.getActivity(context, 0, intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT))
    }

}