/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - StopWatchRecordAdapter.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/03/30
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2020/03/30     1.0            StopWatchRecordAdapter.java
 *  ***************************************************************
 */

package com.oplus.alarmclock.stopwatch;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.animation.PathInterpolator;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.view.TalkBackListener;
import com.oplus.alarmclock.utils.ColorLinearGradientUtil;
import com.oplus.alarmclock.utils.DisplayUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.LocalColorRecyclerView;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class StopWatchRecordAdapter extends COUIRecyclerView.Adapter<RecyclerView.ViewHolder> implements TalkBackListener.Listener {

    public static final String ITEM_TILE = "item_title";
    private static final String TAG = "StopWatchRecordAdapter";
    private static final String ITEM_DATA = "item_data";
    private static final String ITEM_TIME_DIFF = "item_time_diff";
    private static final String SP_NAME = AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP;
    private static final int ANIMATOR_DURATION = 250;
    private static final float CONTROL_X1 = 0.33f;
    private static final float CONTROL_Y1 = 0f;
    private static final float CONTROL_X2 = 0.67f;
    private static final float CONTROL_Y2 = 1.0f;
    private static final int ITEM_FOOTER = -1;
    private static final int ITEM_NON_FOOTER = -2;
    private static final int ITEM_DIVIDER = -3;
    private static final int ITEM_MIN_SIZE = 3;
    private static final String TIME_DURATION_MAX = "stopwatch_max_duration";
    private static final String TIME_DURATION_MIN = "stopwatch_min_duration";
    private float mFontScale;
    private List<Map<String, String>> mData;
    private int mColorHighLight;
    private int mColorRed;
    private int mColorDefault;
    private Context mContext;
    private ColorLinearGradientUtil mColorGradientUtil;
    private int mDefaultTextSize;
    private LocalColorRecyclerView mRecyclerView;
    private int mFooterCount = 1;
    private int mItemHeightDefault;
    private View mItemView;
    private String mMaxTime;
    private String mMinTime;
    private boolean mIsSplit = false;
    private int mRecyclerHeight = 0;
    private TalkBackListener mTalkBackListener;
    private StopWatchListManager mListManager;
    private UiMode mUiMode;

    public StopWatchRecordAdapter(
            LocalColorRecyclerView recyclerView,
            Context context,
            UiMode uiMode,
            List<Map<String, String>> data
    ) {
        mRecyclerView = recyclerView;
        this.mContext = context;
        this.mUiMode = uiMode;
        this.mData = data;
        Resources resources = context.getResources();
        mFontScale = resources.getConfiguration().fontScale;
        mColorDefault = COUIContextUtil.getAttrColor(this.mContext, R.attr.couiColorPrimaryNeutral);
        mColorHighLight = resources.getColor(R.color.stop_watch_max_item_color, null);
        mColorRed = resources.getColor(R.color.stop_watch_min_item_color, null);
        if (isTabletMode()) {
            mDefaultTextSize = resources.getDimensionPixelOffset(R.dimen.text_size_sp_18);
        } else {
            mDefaultTextSize = resources.getDimensionPixelOffset(R.dimen.text_size_sp_16);
        }
        mColorGradientUtil = new ColorLinearGradientUtil(mColorHighLight, mColorDefault);
        mItemHeightDefault = resources.getDimensionPixelOffset(R.dimen.layout_dp_52);

        mTalkBackListener = new TalkBackListener();
        mTalkBackListener.setListener(this);
    }

    public void setUiMode(UiMode uiMode) {
        mUiMode = uiMode;
    }

    public void setListManager(StopWatchListManager mListManager) {
        this.mListManager = mListManager;
    }

    public void update(List<Map<String, String>> data) {
        mData = filterEmptyItem(data);
        getStopWatchTime();
        notifyDataSetChanged();
    }

    public List<Map<String, String>> getList() {
        if (mData == null) {
            return new ArrayList<>();
        }
        return mData;
    }

    private List<Map<String, String>> filterEmptyItem(List<Map<String, String>> data) {
        List<Map<String, String>> tmp = new ArrayList<>();
        if ((data == null) || data.isEmpty()) {
            return tmp;
        }

        for (Map<String, String> item : data) {
            String title = item.get(ITEM_TILE);
            if ((title != null) && (!title.equals(" "))) {
                tmp.add(item);
            }
        }
        return tmp;
    }

    public void notifyItemRangeChanged(Map<String, String> item) {
        if (mData == null) {
            mData = new ArrayList<>();
        }
        getStopWatchTime();
        mData.add(0, item);
        notifyItemRangeInserted(0, 2);
    }

    public void setIsSplit(boolean isSplit) {
        mIsSplit = isSplit;
    }

    @SuppressLint("ResourceType")
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == ITEM_FOOTER) {
            View footerView = new View(mContext);
            footerView.setLayoutParams(new RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, RecyclerView.LayoutParams.WRAP_CONTENT));
            return new FooterViewHolder(footerView);
        } else if (viewType == ITEM_DIVIDER) {
            View dividerView = new View(mContext);
            int height = mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_033);
            int marginStart = mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_4);
            RecyclerView.LayoutParams params = new RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, height);
            params.setMarginStart(marginStart);
            dividerView.setLayoutParams(params);
            COUIDarkModeUtil.setForceDarkAllow(dividerView, false);
            dividerView.setBackgroundColor(COUIContextUtil.getAttrColor(mContext, R.attr.couiColorDivider));
            return new DividerViewHolder(dividerView);
        } else {
            boolean isSpilt = !FoldScreenUtils.isRealOsloPortrait() && mIsSplit;
            int id = isSpilt ? R.layout.stopwatch_rcd_item_split : R.layout.stopwatch_rcd_item;
            return new RecordHolder(LayoutInflater.from(mContext).inflate(id, parent, false));
        }
    }


    public void updateFooterHeight(boolean isRefresh, boolean isHover) {
        if (isHover) {
            mRecyclerHeight = 1;
            notifyItemChanged(getItemCount() - mFooterCount);
        } else {
            mFooterCount = ((getFooterHeight(isRefresh) <= 0) || (mData == null) || mData.isEmpty()) ? 0 : 1;
            notifyDataSetChanged();
        }
    }

    private int getFooterHeight(boolean isRefresh) {
        if ((mRecyclerView == null) || (mData == null) || mData.isEmpty()) {
            return 0;
        } else {
            int itemHeight = (mItemView == null) ? mItemHeightDefault : mItemView.getMeasuredHeight();
            int height = mRecyclerView.getHeight() - (itemHeight * mData.size());
            return Math.max(height, 0);
        }
    }


    @Override
    public int getItemViewType(int position) {
        if (position >= getItemCount() - mFooterCount) {
            return ITEM_FOOTER;
        } else if (position % 2 != 0) {
            return ITEM_DIVIDER;
        } else {
            return ITEM_NON_FOOTER;
        }
    }


    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        if (mData == null || mData.isEmpty()) {
            return;
        }
        if (viewHolder.getItemViewType() == ITEM_FOOTER) {
            FooterViewHolder empty = (FooterViewHolder) viewHolder;
            int footerHeight = getFooterHeight(false);
            if (footerHeight >= 0) {
                empty.itemView.setLayoutParams(new RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, footerHeight));
            }
            return;
        }
        if (viewHolder.getItemViewType() == ITEM_DIVIDER) {
            viewHolder.itemView.setVisibility(View.VISIBLE);
            return;
        }
        RecordHolder holder = (RecordHolder) viewHolder;
        if (mItemView == null) {
            mItemView = holder.itemView;
        }

        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) holder.itemView.getLayoutParams();
        params.height = mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_52);
        holder.itemView.setLayoutParams(params);
        setTalkBackListener(holder.itemView);
        Utils.setSuitableFontSize(holder.mTitle, mDefaultTextSize, mFontScale, COUIChangeTextUtil.G2);
        Utils.setSuitableFontSize(holder.mTimes, mDefaultTextSize, mFontScale, COUIChangeTextUtil.G2);
        Utils.setSuitableFontSize(holder.mTimeDifference, mDefaultTextSize, mFontScale, COUIChangeTextUtil.G2);
        Map<String, String> item = mData.get(position / 2);
        String times = item.get(ITEM_DATA);
        String title = item.get(ITEM_TILE);
        String timeDiff = item.get(ITEM_TIME_DIFF);
        if ((title != null) && (!title.equals(" "))) {
            holder.mTitle.setText(title);
            holder.mTimes.setText(times);
            holder.mTimeDifference.setText("+" + timeDiff);
            holder.itemView.setContentDescription(title + ", +" + timeDiff + ", " + times + ",");
            if (mData.size() >= ITEM_MIN_SIZE) {
                if (TextUtils.equals(String.valueOf(mMaxTime), timeDiff)) {
                    holder.mTitle.setTextColor(mColorHighLight);
                    holder.mTimeDifference.setTextColor(mColorHighLight);
                } else if (TextUtils.equals(String.valueOf(mMinTime), timeDiff)) {
                    holder.mTitle.setTextColor(mColorRed);
                    holder.mTimeDifference.setTextColor(mColorRed);
                } else {
                    holder.mTitle.setTextColor(mColorDefault);
                    holder.mTimeDifference.setTextColor(mColorDefault);
                }
            } else {
                holder.mTitle.setTextColor(mColorDefault);
                holder.mTimeDifference.setTextColor(mColorDefault);
            }
        }
    }

    @Override
    public int getItemCount() {
        if (mData == null || mData.isEmpty()) {
            return 0;
        }
        //position为奇数时为分割线
        return (mData.size() * 2 - 1) + mFooterCount;
    }

    private void handleColorAnimator(final TextView text) {

        ValueAnimator colorAnimator = ValueAnimator.ofFloat(0, 1);
        colorAnimator.setDuration(ANIMATOR_DURATION);
        colorAnimator.setInterpolator(new PathInterpolator(CONTROL_X1, CONTROL_Y1, CONTROL_X2, CONTROL_Y2));
        colorAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if ((animation != null) && (animation.getAnimatedValue() != null)) {
                    float range = (float) animation.getAnimatedValue();
                    text.setTextColor(mColorGradientUtil.getColor(range));
                }
            }
        });
        colorAnimator.start();
    }

    class RecordHolder extends COUIRecyclerView.ViewHolder {

        TextView mTitle;
        TextView mTimes;
        TextView mTimeDifference;

        RecordHolder(@NonNull View v) {
            super(v);
            mTitle = v.findViewById(R.id.record_seq);
            mTimes = v.findViewById(R.id.record_time);
            mTimeDifference = v.findViewById(R.id.record_time_diference);
            TextWeightUtils.setTextBold(mTitle);
            TextWeightUtils.setTextBold(mTimes);
            TextWeightUtils.setTextBold(mTimeDifference);
        }
    }

    private static class FooterViewHolder extends COUIRecyclerView.ViewHolder {
        public FooterViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    private static class DividerViewHolder extends COUIRecyclerView.ViewHolder {
        public DividerViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    private void getStopWatchTime() {
        mMaxTime = PrefUtils.getString(mContext, SP_NAME, TIME_DURATION_MAX, "");
        mMinTime = PrefUtils.getString(mContext, SP_NAME, TIME_DURATION_MIN, "");
        if ((mData.size() >= ITEM_MIN_SIZE) && (TextUtils.isEmpty(mMaxTime))) {
            for (Map<String, String> item : mData) {
                String timeDiff = item.get(ITEM_TIME_DIFF);
                if (TextUtils.isEmpty(timeDiff)) {
                    continue;
                }
                checkTimeDiff(timeDiff);
            }
            PrefUtils.putString(mContext, SP_NAME, TIME_DURATION_MAX, mMaxTime);
            PrefUtils.putString(mContext, SP_NAME, TIME_DURATION_MIN, mMinTime);
        }
    }

    /**
     * 通过减去其他部分来获取Recycler的高度
     * temp 列表下方的按钮和选项卡总高度
     * getWindowSize 不包含状态栏和导航栏的屏幕高度
     */
    private int getRecyclerHeight(boolean isRefresh) {
        if (UiMode.LARGE_HORIZONTAL == mUiMode
                || UiMode.LARGE_VERTICAL == mUiMode
                || UiMode.MIDDLE == mUiMode) {
            return 0;
        }
        // isRefresh 蜻蜓切换悬停模式强制刷新footer高度
        if (mRecyclerHeight == 0 || isRefresh) {
            int screenHeight = DisplayUtils.getWindowHeight(mContext);
            int recyclerBottom = mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_167);
            int topMargin = ((ViewGroup.MarginLayoutParams) mRecyclerView.getLayoutParams()).topMargin;
            mRecyclerHeight = screenHeight - recyclerBottom - topMargin;
        }
        return mRecyclerHeight;
    }

    private void setTalkBackListener(View view) {
        if (mListManager != null) {
            ViewCompat.setAccessibilityDelegate(view, mTalkBackListener);
        }
    }

    @Override
    public void onTalkBackEvent(@NonNull View view, @NonNull AccessibilityEvent event) {
        boolean isFocused = event.getEventType() == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED;
        if ((mListManager != null) && Utils.isTalkBackOpen() && isFocused) {
            mListManager.setEnable(true);
        }
    }

    /**
     * 检测时间计次间隔
     */
    private void checkTimeDiff(String timeDiff) {
        if (TextUtils.isEmpty(mMinTime)) {
            mMinTime = timeDiff;
        }
        if (TextUtils.isEmpty(mMaxTime)) {
            mMaxTime = timeDiff;
        }
        Log.d(TAG, "checkTimeDiff timeDiff==" + timeDiff + "mMaxTime=" + mMaxTime + "mMinTime=" + mMinTime);
        long time = StopWatchRecordUtils.getLastIntervalTime(mContext, timeDiff);
        long maxTime = StopWatchRecordUtils.getLastIntervalTime(mContext, mMaxTime);
        long minTime = StopWatchRecordUtils.getLastIntervalTime(mContext, mMinTime);
        Log.d(TAG, "checkTimeDiff22== time==" + time + "maxTime=" + maxTime + "minTime=" + minTime);
        if (time > maxTime) {
            mMaxTime = timeDiff;
        }
        if (time < minTime) {
            mMinTime = timeDiff;
        }
        Log.d(TAG, "checkTimeDiff33== timeDiff==" + timeDiff + "mMaxTime=" + mMaxTime + "mMinTime=" + mMinTime);
    }

    public final boolean isTabletMode() {
        return UiMode.LARGE_HORIZONTAL == mUiMode || UiMode.LARGE_VERTICAL == mUiMode;
    }
}