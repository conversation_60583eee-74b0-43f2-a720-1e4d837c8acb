/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
@file:Suppress("SpreadOperator")

package com.oplus.alarmclock.stopwatch

import android.animation.AnimatorSet
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.PathInterpolator
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.graphics.ColorUtils
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ChannelManager
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.clock.common.utils.Log

class StopWatchSecondPointer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : StopWatchPointer(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "StopWatchSecondPointer"
        private const val ONE_CIRCLE_SECOND_TIME = 60 * 1000
        private const val ONE_MILLISECOND_SECOND_ANGLE = 0.006F//一毫秒秒针转动0.006°
        private const val RECORD_PATH_ONE = 0.3F
        private const val RECORD_PATH_TWO = 0F
        private const val RECORD_PATH_THREE = 0.83F
        private const val RECORD_PATH_FOUR = 1F
        private const val GRADIENT_POSITIONS_ONE = 0f
        private const val GRADIENT_POSITIONS_TWO = 0.5f
        private const val GRADIENT_POSITIONS_THREE = 1f
        private const val DURATION_150 = 150L
        private const val DURATION_200 = 200L
        private const val DURATION_203 = 203L
        private const val START_DELAY_180 = 180L
        private const val START_DELAY_200 = 200L
        private const val MIX_COLOR_RATIO = 0.5f
        private const val RECORD_OFFSET_RATIO = 0.5f
    }

    private var mSecondWidth = 0F
    private var mSecondLength = 0F
    private var mSecondOffsetLength = 0F
    private var mSecondCircleRadius = 0F
    private var mAxisRadius = 0F
    private var mSecondColor = 0
    private var mRecordColor = 0
    private var mMixColor = 0
    private var mAxisColor = 0
    private var mCurrentSecondAngle = 0F
    private var mPauseSecondAngle = 0F
    private var mRestoreSecondAngle = 0F
    private val mDrawAble by lazy {
        val resourceId = if (mIsDark) {
            R.drawable.ic_dial_stopwatch_second_pointer_dark
        } else {
            R.drawable.ic_dial_stopwatch_second_pointer
        }
        AppCompatResources.getDrawable(getContext(), resourceId)?.apply {
            setTint(mSecondColor)
        }
    }
    private var mAnimatorSet: AnimatorSet? = null
    private var mIsRecord = false
    private var mFadePointerProgress = 0f
    private var mOffsetPointerProgress = 0f
    private lateinit var mGradientColors: IntArray
    private val mGradientPositions =
        floatArrayOf(GRADIENT_POSITIONS_ONE, GRADIENT_POSITIONS_TWO, GRADIENT_POSITIONS_THREE)
    private val mColorEvaluator = ArgbEvaluator()
    private val mRecordInterpolator by lazy {
        PathInterpolator(
            RECORD_PATH_ONE,
            RECORD_PATH_TWO,
            RECORD_PATH_THREE,
            RECORD_PATH_FOUR
        )
    }

    private val mScaleSecondCirclePaint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG)
    }

    init {
        initColor(context)
        initSize(context, attrs)
    }

    fun record() {
        if (ChannelManager.getLightOSUtils().isLightOS()) {
            return
        }
        mIsRecord = true
        mAnimatorSet?.run {
            if (isRunning) {
                cancel()
            }
        }
        mFadePointerProgress = 0f
        val fadeInPointerAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = DURATION_200
            interpolator = COUIMoveEaseInterpolator()
            addUpdateListener { animation ->
                mFadePointerProgress = animation.animatedValue as Float
                invalidate()
            }
        }

        val offsetAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = DURATION_150
            startDelay = START_DELAY_180
            interpolator = AccelerateDecelerateInterpolator()
            addUpdateListener { animation ->
                mOffsetPointerProgress = animation.animatedValue as Float
                invalidate()
            }
        }

        val fadeOutPointerAnimator = ValueAnimator.ofFloat(1f, 0f).apply {
            duration = DURATION_203
            startDelay = START_DELAY_200
            interpolator = mRecordInterpolator
            addUpdateListener { animation ->
                mFadePointerProgress = animation.animatedValue as Float
                invalidate()
            }
        }

        mAnimatorSet = AnimatorSet().apply {
            playTogether(fadeInPointerAnimator, offsetAnimator, fadeOutPointerAnimator)
            doOnCancel {
                mFadePointerProgress = 0f
                mOffsetPointerProgress = 0f
                mIsRecord = false
                mAnimatorSet = null
            }
            doOnEnd {
                mFadePointerProgress = 0f
                mOffsetPointerProgress = 0f
                mIsRecord = false
                mAnimatorSet = null
            }
            start()
        }
    }

    override fun onResume() {
        super.onResume()
        mCurrentSecondAngle = mTime % ONE_CIRCLE_SECOND_TIME * ONE_MILLISECOND_SECOND_ANGLE
        Log.d(
            TAG,
            "onResume mCurrentSecondAngle:$mCurrentSecondAngle, mPauseSecondAngle:$mPauseSecondAngle"
        )
        if (mPauseSecondAngle == 0f || mCurrentSecondAngle == mPauseSecondAngle) {
            mInterceptInvalidate = false
            mPauseSecondAngle = 0f
            return
        }
        val time = getRestoreTime()
        val targetSecondAngle =
            (mTime + time) % ONE_CIRCLE_SECOND_TIME * ONE_MILLISECOND_SECOND_ANGLE
        mAnimator =
            ValueAnimator.ofFloat(*getAnimatorParams(mPauseSecondAngle, targetSecondAngle)).apply {
                duration = time
                interpolator = mRestoreInterpolator
                addUpdateListener {
                    mCurrentSecondAngle = it.animatedValue as Float % ONE_CIRCLE_DEGREE
                    invalidate()
                }

                doOnCancel {
                    resetResumeProperties()
                    postInvalidate()
                }

                doOnEnd { resetResumeProperties() }
            }
        mAnimator?.start()
    }

    override fun onPause() {
        super.onPause()
        mAnimatorSet?.run {
            if (isRunning) {
                cancel()
            }
        }
        mPauseSecondAngle = mCurrentSecondAngle
    }

    override fun onStop() {
        super.onStop()
        mRestoreSecondAngle = mCurrentSecondAngle
        mAnimator = ValueAnimator.ofFloat(mCurrentSecondAngle, 0F).apply {
            duration = mTotalRestoreTime
            interpolator = mRestoreInterpolator
            addUpdateListener {
                mCurrentSecondAngle = it.animatedValue as Float
                invalidate()
            }
            doOnCancel {
                resetStopProperties()
                postInvalidate()
            }
            doOnEnd { resetStopProperties() }
        }
        mAnimator?.start()
    }

    override fun onRelease() {
        super.onRelease()
        mRestoreSecondAngle = 0F
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mBitmap = drawableToBitmap(mDrawAble)
    }

    override fun Canvas.onStart() {
        drawSecond(mCurrentSecondAngle)
    }

    override fun Canvas.onStop() {
        drawSecond(mCurrentSecondAngle)
    }

    private fun Canvas.drawSecond(angle: Float) {
        save()
        rotate(angle, mCenterX, mCenterY)
        /*val paint = if (mIsRecord) {
            val color =
                mColorEvaluator.evaluate(mFadePointerProgress, mSecondColor, mMixColor) as Int
            mGradientColors[1] = color
            val linearGradientShader = LinearGradient(
                mCenterX,
                mCenterY - mSecondLength * (mOffsetPointerProgress - RECORD_OFFSET_RATIO),
                mCenterX,
                mCenterY - mSecondLength * (mOffsetPointerProgress + RECORD_OFFSET_RATIO),
                mGradientColors,
                mGradientPositions,
                Shader.TileMode.CLAMP
            )
            mPaint.getSecondPaint(mSecondColor, mSecondWidth).apply {
                shader = linearGradientShader
            }
        } else {
            mPaint.getSecondPaint(mSecondColor, mSecondWidth)
        }*/
        mBitmap?.runCatching {
            if (!isRecycled) {
                drawBitmap(
                    this,
                    mCenterX - width / 2,
                    mCenterY + mSecondOffsetLength - mSecondLength,
                    mScaleSecondCirclePaint
                )
            }
        }
        drawCircle(mCenterX, mCenterY, mAxisRadius, mPaint.getAxisPaint(mAxisColor))
        restore()
    }

    override fun initColor(context: Context) {
        super.initColor(context)
        context.resources?.run {
            mSecondColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary)
            if (!DeviceUtils.isWPlusPhone()) {
                mAxisColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorLabelSecondary)
            } else {
                mAxisColor = getColor(R.color.oos_second_pointer_center_color, null)
            }
            mRecordColor = getColor(R.color.app_dial_second_pointer_record_color, null)
            mMixColor = ColorUtils.blendARGB(mSecondColor, mRecordColor, MIX_COLOR_RATIO)
            mGradientColors = intArrayOf(mSecondColor, mMixColor, mSecondColor)
            mShadowColor = getColor(R.color.second_shadow_color, null)
        }
    }

    override fun initSize(context: Context, attrs: AttributeSet?) {
        super.initSize(context, attrs)
        context.resources?.run {
            mSecondWidth = getDimension(R.dimen.app_dial_pointer_stopwatch_second_width) * mMultiple
            mSecondLength =
                getDimension(R.dimen.app_dial_pointer_stopwatch_second_length) * mMultiple
            mSecondOffsetLength =
                getDimension(R.dimen.app_dial_pointer_stopwatch_second_offset) * mMultiple
            mSecondCircleRadius = getDimension(R.dimen.app_dial_axis_second_radius) * mMultiple
            mAxisRadius = getDimension(R.dimen.app_dial_axis_radius) * mMultiple

            mShadowRadius = getDimension(R.dimen.app_dial_pointer_shadow_radius) * mMultiple
            mShadowY = getDimension(R.dimen.app_dial_pointer_shadow_dy) * mMultiple
        }
    }

    override fun calculateAngle() {
        super.calculateAngle()
        mCurrentSecondAngle = mTime % ONE_CIRCLE_SECOND_TIME * ONE_MILLISECOND_SECOND_ANGLE
    }

    override fun resetResumeProperties() {
        super.resetResumeProperties()
        mPauseSecondAngle = 0F
    }

    override fun resetStopProperties() {
        super.resetStopProperties()
        mRestoreTime = 0
        mRestoreSecondAngle = 0F
        mCurrentSecondAngle = 0F
        mPauseSecondAngle = 0F
        mTotalRestoreTime = 0
        mTime = 0
    }

    /**
     * 复原的时间
     * 时长：
     * T=500ms+10t
     * t：当前停止时间
     * T：转动总时长
     * */
    override fun getTotalRestoreTime(): Long {
        return RESTORE_TIME + (mTime / THOUSAND % SIXTY) * TEN
    }

    override fun getRestoreTime(): Long {
        return RESTORE_TIME + ((mTime - mPauseTime) / THOUSAND % SIXTY) * TEN
    }


    private fun Paint.getAxisPaint(axisColor: Int): Paint {
        return apply {
            reset()
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = axisColor
        }
    }
}