/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StopWatchNormalFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.animation.Animator
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopwatchMainViewBinding
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.DRAGONFLY_CLOCK_DIAL_LOCATION_Y
import com.oplus.alarmclock.utils.FoldScreenUtils.DRAGONFLY_CLOCK_DIAL_SMALL_LOCATION_Y
import com.oplus.alarmclock.utils.FoldScreenUtils.DRAGONFLY_STOPWATCH_LARGE_LIST_LOCATION_Y
import com.oplus.alarmclock.utils.FoldScreenUtils.DRAGONFLY_STOPWATCH_SMALL_LIST_LOCATION_Y
import com.oplus.alarmclock.utils.FoldScreenUtils.DRAGONFLY_STOPWATCH_STAND_LIST_LOCATION_Y
import com.oplus.alarmclock.utils.FoldScreenUtils.isDragonfly
import com.oplus.alarmclock.utils.FoldScreenUtils.screenDisplayModel
import com.oplus.alarmclock.utils.TextWeightUtils
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightNoChange
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.LocalColorRecyclerView

class StopWatchNormalFragment : StopWatchFragment<StopwatchMainViewBinding>() {

    companion object {
        private const val HOVER_ANIM_DURATION = 200L
    }

    private var listHeight = 0

    /**
     * 秒表悬停表盘向上偏移量
     */
    private var stopWatchLocationY = 0

    /**
     * 列表悬停偏移量
     */
    private var stopWatchListLocationY = 0

    private var isHover = false

    /**
     * 计次列表高度
     */
    private var paddingHeight = 0

    /**
     * 计次列表距离顶部的高度
     */
    private var topMargin = 0
    /**
     * 是否浮窗模式
     */
    private var mIsFloatingWindow = false

    private var mTioDistance = 0
    private var isInit = false
    override fun layoutId(): Int {
        return R.layout.stopwatch_main_view
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout,
                    null, R.menu.action_menu_icon_stop_watch)
        }
    }

    override fun initDialClockAnimManager() {
        super.initDialClockAnimManager()
        mViewBinding?.stopWatchInclude?.apply {
            mShadowManager?.init(
                    null,
                    null,
                    null,
                    null,
                    null,
                    stopWatchBg,
                    null
            )
            mShadowAnimationManager?.init(
                    stopWatchScale,
                    stopWatch,
                    stopWatchDotTv,
                    stopWatchBg,
                    stopWatchRl
            )
        }
    }

    override fun initHoverIfNeed() {
        super.initHoverIfNeed()
        calculationLocationY()
        if (isDragonfly()) {
            mViewBinding?.stopWatchList?.let {
                it.post {
                    listHeight = it.height
                }
            }
        }

        /*悬停模式下切换至悬停状态*/
        (activity as? AlarmClock)?.apply {
            if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                mViewBinding?.stopWatchList?.post { onHoverPostureChanged(true) }
            }
        }
    }

    private fun calculationLocationY() {
        val screenDisplay = screenDisplayModel()
        stopWatchLocationY = DRAGONFLY_CLOCK_DIAL_LOCATION_Y
        when (screenDisplay) {
            FoldScreenUtils.SCREEN_DISPLAY_SMALL -> {
                //较小
                stopWatchLocationY = DRAGONFLY_CLOCK_DIAL_SMALL_LOCATION_Y
                stopWatchListLocationY = DRAGONFLY_STOPWATCH_SMALL_LIST_LOCATION_Y
            }

            FoldScreenUtils.SCREEN_DISPLAY_LARGE -> {
                //较大
                stopWatchListLocationY = DRAGONFLY_STOPWATCH_LARGE_LIST_LOCATION_Y
            }

            else -> {
                //标准
                stopWatchListLocationY = DRAGONFLY_STOPWATCH_STAND_LIST_LOCATION_Y
            }
        }
    }

    override fun initTitle() {
        mViewBinding?.apply {
            val fontScale = resources.configuration.fontScale
            val size = resources.getDimension(R.dimen.text_size_sp_14)
            Utils.setSuitableFontSize(titleStartTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleMiddleTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleEndTv, size, fontScale, COUIChangeTextUtil.G2)
            titleStartTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleMiddleTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleEndTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
        }
    }

    override fun initListener() {
        super.initListener()
        mViewBinding?.clickListener = this
        mViewBinding?.stopWatchInclude?.clickListener = this
        initManager()
    }


    /**
     * 浮窗模式
     */
    override fun flexibleScenario() {
        super.flexibleScenario()
        context?.let {
            if (FoldScreenUtils.isFlexibleScenario(it)) {
                mIsFloatingWindow = true
                if (isHover) {
                    handleNormal()
                }
                resetViewData(isFloatingWindow = true)
                mStopWatchListManager?.reset()
            } else {
                if (mIsFloatingWindow) {
                    if (isHover) {
                        handleHover()
                    }
                    mStopWatchListManager?.reset()
                    resetViewData(isFloatingWindow = false)
                }
                mIsFloatingWindow = false
            }
        }
    }

    override fun updateList() {
        super.updateList()
        mViewBinding?.apply {
            if (((stopWatchList.paddingTop > 0)
                            && ((stopWatchList.layoutParams as MarginLayoutParams).topMargin > 0))
                    || isHover) {
                return
            }
            initManager()
        }
    }


    private fun initManager() {
        mViewBinding?.apply {
            initTopMargin(false)
            stopWatchInclude.stopWatch.update()
            mIsFloatingWindow = FoldScreenUtils.isFlexibleScenario(mContext)
            stopWatchCl.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    stopWatchCl.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    if (!isInit) {
                        resetViewData(FoldScreenUtils.isFlexibleScenario(mContext))
                        isInit = true
                    }
                }
            })
        }
    }


    /**
     * 配置view位置数据
     */
    private fun resetViewData(isFloatingWindow: Boolean) {
        mViewBinding?.apply {
            var zoomOffset = 1f
            var marginOffset = 1f
            var titleBottom = 0
            if (isFloatingWindow) {
                titleBottom = resources.getDimensionPixelSize(R.dimen.layout_dp_106)
                zoomOffset = FoldScreenUtils.FLOATING_WINDOW_SCALE
                marginOffset = FoldScreenUtils.FLOATING_WINDOW_STOP_WATCH_SCALE
            }
            stopWatchListTitle.apply {
                val params = layoutParams as MarginLayoutParams
                params.bottomMargin = titleBottom
                layoutParams = params
            }
            mAnimationManager.toFloatingWindowScale(stopWatchInclude.parent, zoomOffset)
            if (mTioDistance == 0) {
                mTioDistance = getTopDistance()
            }
            val topMargin = (mTioDistance * zoomOffset).toInt()
            val height = (resources.getDimensionPixelSize(R.dimen.layout_dp_400) * marginOffset).toInt()
            val margin = (resources.getDimensionPixelSize(R.dimen.layout_dp_52) * marginOffset).toInt()
            val topPadding = stopWatchInclude.parent.paddingTop ?: 0
            val compensate = (resources.getDimensionPixelSize(R.dimen.layout_dp_25) * marginOffset).toInt()
            val paddingHeight = height + topPadding - topMargin - compensate
            if (topMargin > 0 && paddingHeight > 0) {
                <EMAIL> = paddingHeight
                val params = stopWatchList.layoutParams as MarginLayoutParams
                params.topMargin = topMargin + compensate
                stopWatchList.layoutParams = params
                stopWatchList.setPadding(0, paddingHeight, 0, 0)
                stopWatchList.scrollBy(0, -paddingHeight)
                setManager(height, paddingHeight, margin, isFloatingWindow)
                if (!isInit) {
                    stopWatchInclude.stopWatch.center()
                }
                val layoutParams = stopWatchInclude.stopWatchScale.layoutParams as MarginLayoutParams
                layoutParams.topMargin = ((stopWatchInclude.stopWatchRl.height - stopWatchInclude.stopWatchScale.height) / TWO).toInt()
                stopWatchInclude.stopWatchScale.layoutParams = layoutParams
                stopWatchList.viewTreeObserver.addOnGlobalLayoutListener(object :
                    OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        stopWatchList.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        mListAdapter?.setListManager(mStopWatchListManager)
                        mListAdapter?.updateFooterHeight(false, false)
                    }
                })
            }
        }
    }


    private fun setManager(height: Int, scrollDistance: Int, topMargin: Int, isFloatingWindow: Boolean) {
        if (mStopWatchListManager == null) {
            mStopWatchListManager = context?.let { StopWatchListManager(it) }
        }
        mViewBinding?.let {
            mStopWatchListManager.initManager(it.stopWatchInclude, null, Triple(height, scrollDistance, topMargin),
                    uiMode, it.stopWatchCl, it.stopWatchList, it.stopWatchListTitle, isFloatingWindow)
        }
    }

    /**
     * 获取顶部的高度
     */
    private fun getTopDistance(): Int {
        mViewBinding?.apply {
            val location = IntArray(2)
            stopWatchInclude.stopWatchDivider.getLocationInWindow(location)
            val top: Int = stopWatchCl.paddingTop
            val compensation = resources.getDimensionPixelSize(R.dimen.layout_dp_20)
            return location[1] + stopWatchInclude.stopWatchDivider.height - top + compensation
        }
        return 0
    }

    override fun onHoverPostureChanged(hover: Boolean) {
        super.onHoverPostureChanged(hover)
        if (mViewBinding == null || mAnimationManager == null || isHover == hover) {
            return
        }
        isHover = hover
        mAnimationManager.setIsHover(hover)
        if (hover) {
            handleHover()
        } else {
            handleNormal()
        }
    }

    private fun handleHover() {
        mViewBinding?.apply {
            mAnimationManager?.apply {
                postureAnimator(0f, stopWatchLocationY.toFloat(), stopWatchInclude.stopWatchRl)
                postureAnimator(0f, stopWatchLocationY.toFloat(), stopWatchInclude.stopWatch)
                postureAnimator(0f, stopWatchLocationY.toFloat(), stopWatchInclude.stopWatchDotTv)
                postureAnimator(0f, stopWatchLocationY.toFloat(), stopWatchInclude.stopWatchScale)
                postureAnimator(0f, stopWatchListLocationY.toFloat(), stopWatchListTitle)
            }
            if (mStopWatchListManager.mCurrentRatio > 0) {
                mStopWatchListManager.setAutoScroll(false)
                mStopWatchListManager.updateViewToOpen()
            }
            stopWatchList.setPadding(0, 0, 0, 0)
            val cityLayout = stopWatchList.layoutParams as MarginLayoutParams
            topMargin = paddingHeight + cityLayout.topMargin + stopWatchListLocationY
            setListTopMargin(paddingHeight + cityLayout.topMargin)
            mAnimationManager.listAnimator(
                    0,
                    stopWatchListLocationY,
                    stopWatchList,
                    object : Animator.AnimatorListener {
                        override fun onAnimationStart(animation: Animator) {}
                        override fun onAnimationEnd(animation: Animator) {
                            dragonflyToHoverAnimationEnd()
                        }

                        override fun onAnimationCancel(animation: Animator) {
                            dragonflyToHoverAnimationEnd()
                        }

                        override fun onAnimationRepeat(animation: Animator) {}
                    }, HOVER_ANIM_DURATION
            )
        }
    }

    private fun dragonflyToHoverAnimationEnd() {
        mViewBinding?.apply {
            mStopWatchListManager?.setAutoScroll(false)
            stopWatchList.smoothScrollToPosition(0)
            stopWatchList.postDelayed({
                mListAdapter.updateFooterHeight(true, true)
                setListHeight(listHeight - stopWatchListLocationY - paddingHeight)
                if (!isHover) {
                    handleNormal()
                }
            }, FoldScreenUtils.STOP_WATCH_DELAY_CHANGE_HEIGHT.toLong())
        }
    }

    private fun handleNormal() {
        mViewBinding?.apply {
            setListHeight(listHeight)
            mAnimationManager?.apply {
                postureAnimator(stopWatchLocationY.toFloat(), 0f, stopWatchInclude.stopWatchRl)
                postureAnimator(stopWatchLocationY.toFloat(), 0f, stopWatchInclude.stopWatch)
                postureAnimator(stopWatchLocationY.toFloat(), 0f, stopWatchInclude.stopWatchDotTv)
                postureAnimator(stopWatchLocationY.toFloat(), 0f, stopWatchInclude.stopWatchScale)
                postureAnimator(stopWatchListLocationY.toFloat(), 0f, stopWatchList)
                postureAnimator(stopWatchListLocationY.toFloat(), 0f, stopWatchListTitle)
            }
            stopWatchList.setPadding(0, paddingHeight, 0, 0)
            setListTopMargin(topMargin - paddingHeight - stopWatchListLocationY)
            scrollToBottom()
            mStopWatchListManager?.setAutoScroll(true)
            stopWatchList.postDelayed({
                mListAdapter?.updateFooterHeight(true, false)
            }, HOVER_DEFAULT_ANIMATOR_DURATION)
        }
    }

    /**
     * 设置列表间距
     *
     * @param topMargin
     */
    private fun setListTopMargin(topMargin: Int) {
        mViewBinding?.stopWatchList?.let {
            val cityLayout = it.layoutParams as MarginLayoutParams
            cityLayout.topMargin = topMargin
            it.layoutParams = cityLayout
        }
    }

    /**
     * 设置列表高度
     *
     * @param height
     */
    private fun setListHeight(height: Int) {
        mViewBinding?.stopWatchList?.let {
            val cityLayout = it.layoutParams as MarginLayoutParams
            cityLayout.height = height
            it.layoutParams = cityLayout
        }
    }

    override fun isHover(): Boolean {
        return isHover
    }

    override fun stopWatchInterval(): StopWatchTextSmallView? {
        return mViewBinding?.stopWatchInclude?.stopWatchDotTv
    }

    override fun stopWatchCl(): ConstraintLayout? {
        return mViewBinding?.stopWatchCl
    }

    override fun stopWatch(): StopWatchTextView? {
        return mViewBinding?.stopWatchInclude?.stopWatch
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun stopWatchView(): StopWatchView? {
        return mViewBinding?.stopWatchInclude?.stopWatchScale
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.nextComponent
    }

    override fun buttonCount(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun listView(): LocalColorRecyclerView? {
        return mViewBinding?.stopWatchList
    }

    override fun listTitle(): FrameLayout? {
        return mViewBinding?.stopWatchListTitle
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.app_dial_stand_width)
    }
}