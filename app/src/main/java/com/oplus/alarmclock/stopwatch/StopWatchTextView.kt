/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
@file:Suppress("ParameterListWrapping")

package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopWatchExtraLongBinding
import com.oplus.alarmclock.databinding.StopWatchLongBinding
import com.oplus.alarmclock.databinding.StopWatchShortBinding
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.TextWeightUtils.setTextBold
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightMedium
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.ViewExt
import com.oplus.alarmclock.view.ViewExt.setColon
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.abs

class StopWatchTextView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {
    companion object {
        const val STATUS_SHORT = 1
        const val STATUS_LONG = 2
        const val STATUS_EXTRA_LONG = 3
        private const val ZERO = 0
        private const val ONE = 1
        private const val TWO = 2
        private const val THREE = 3
        private const val FOUR = 4
        private const val FIVE = 5
        private const val SIX = 6
        private const val SEVEN = 7
        private const val EIGHT = 8
        private const val NINE = 9
        private const val TEN = 10
        private const val TWENTY_FOUR = 24
        private const val SIXTY = 60
        private const val SECOND_OR_MILLI = 1000L
        private const val MINUTE = SIXTY * SECOND_OR_MILLI
        private const val HOUR = SIXTY * MINUTE
        private const val DAY = TWENTY_FOUR * HOUR
    }

    private lateinit var mNumberFormat: NumberFormat
    private lateinit var mInflater: LayoutInflater
    private var mMaxSize = 0F
    private var mMinSize = 0F
    private var mHoverSize = 0F
    private var mFormatZero = ""
    private var mTime = 0L
    private var mParentHeight = 0F
    private var mIsCenter = false
    private var mTextLength = 0F
    private var mTextHeight = 0
    private var mTextSize = 0F
    private var mMarginTop = 0
    private var mMaxNum: String? = null
    private var mDayStartTv: TextView? = null
    private var mDayEndTv: TextView? = null
    private var mColonStartTv: TextView? = null
    private var mHourStartTv: TextView? = null
    private var mHourEndTv: TextView? = null
    private var mColonMiddleTv: TextView? = null
    private var mMinuteStartTv: TextView? = null
    private var mMinuteEndTv: TextView? = null
    private var mColonEndTv: TextView? = null
    private var mSecondStartTv: TextView? = null
    private var mSecondEndTv: TextView? = null
    private var mPointTv: TextView? = null
    private var mMillisecondStartTv: TextView? = null
    private var mMillisecondEndTv: TextView? = null
    private var mCurrentStatus = 0
    private var mThemeColor = 0
    private var mIsHover = false
    private var mUiMode = FoldScreenUtils.UiMode.NORMAL
    private var mInterceptInvalidate = false

    init {
        context.let {
            initColor(it)
            intParam(it)
            initSize(it, attrs)
        }
    }

    /**
     * 当前是否悬停
     * @param isHover 是否悬停
     */
    fun setIsHover(isHover: Boolean) {
        mIsHover = isHover
    }

    fun setUiMode(uiMode: FoldScreenUtils.UiMode) {
        mUiMode = uiMode
    }

    private fun initColor(context: Context) {
        mThemeColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimaryText)
    }

    fun center() {
        center(false)
    }

    fun center(isHover: Boolean) {
        if (isHover) {
            return
        }
        if (layoutParams != null) {
            layoutParams = (layoutParams as MarginLayoutParams).run {
                if (!mIsCenter || (topMargin != mMarginTop)) {
                    mIsCenter = true
                    topMargin += mMarginTop
                    mMarginTop = topMargin
                }
                this
            }
        }
    }

    fun getTime(): Long {
        return mTime
    }

    fun update(time: Long) {
        mTime = time
        if (mInterceptInvalidate) {
            return
        }
        update()
    }

    fun onResume() {
        mInterceptInvalidate = false
    }

    fun onPause() {
        mInterceptInvalidate = true
    }

    fun update() {
        setView()
        val minute = mTime / MINUTE % SIXTY
        val second = mTime / SECOND_OR_MILLI % SIXTY
        setData(mMinuteStartTv, mMinuteEndTv, minute)
        setData(mSecondStartTv, mSecondEndTv, second)
        if (mCurrentStatus == STATUS_SHORT) {
            val millisecond = mTime % SECOND_OR_MILLI / TEN
            setData(mMillisecondStartTv, mMillisecondEndTv, millisecond)
        } else {
            val hour = mTime / HOUR % TWENTY_FOUR
            setData(mHourStartTv, mHourEndTv, hour)
            if (mCurrentStatus == STATUS_EXTRA_LONG) {
                val day = mTime / DAY
                mDayStartTv?.visibility = if (day < TEN) View.GONE else View.VISIBLE
                setData(mDayStartTv, mDayEndTv, day)
            }
        }
    }

    fun setTextSize(textSize: Float) {
        if (mCurrentStatus == STATUS_EXTRA_LONG) {
            context.resources.run {
                if (mIsHover) {
                    measureMaxLength(getDimension(R.dimen.text_size_sp_58))
                } else {
                    measureMaxLength(mMinSize)
                }
            }
        } else {
            measureMaxLength(textSize)
        }
        setTextSize()
    }

    fun getMarginTop(): Int {
        return mMarginTop
    }

    private fun setView() {
        val status = when (mTime) {
            in 0 until HOUR -> STATUS_SHORT
            in HOUR until DAY -> STATUS_LONG
            else -> STATUS_EXTRA_LONG
        }
        if (status != mCurrentStatus) {
            mCurrentStatus = status
            removeAllViews()
            initTextLength()
            initViews(status)
        }
    }

    /**
     * 适配多语言使用了DateFormat.format，而非全部使用阿拉伯数字
     * */
    private fun setData(startTv: TextView?, endTv: TextView?, time: Long) {
        val timeStr = mNumberFormat.format(time)
        if (timeStr.length >= TWO) {
            startTv?.text = timeStr.substring(ZERO, ONE)
            endTv?.text = timeStr.substring(ONE, TWO)
        } else {
            startTv?.text = mFormatZero
            endTv?.text = timeStr
        }
    }

    private fun initSize(context: Context, attrs: AttributeSet?) {
        attrs?.let {
            context.resources?.run {
                val height = getDimension(R.dimen.layout_dp_252)
                val maxSize = getDimension(R.dimen.text_size_sp_28)
                val minSize = getDimension(R.dimen.text_size_sp_23)
                mHoverSize = getDimension(R.dimen.text_size_sp_74)
                val obtainAttributes = obtainAttributes(it, R.styleable.StopWatchTextView)
                mParentHeight =
                   obtainAttributes.getDimension(
                        R.styleable.StopWatchTextView_parent_height,
                        height
                    )
                mMaxSize =
                    obtainAttributes.getDimension(R.styleable.StopWatchTextView_max_size, maxSize)
                mMinSize =
                    obtainAttributes.getDimension(R.styleable.StopWatchTextView_min_size, minSize)
                obtainAttributes.recycle()
            }
        }
        initTextHeight()
        initTextLength()
    }

    private fun intParam(context: Context) {
        mInflater = LayoutInflater.from(context)
        mIsCenter = false
        mNumberFormat = NumberFormat.getInstance()
        mFormatZero = mNumberFormat.format(0)
        layoutDirection = LAYOUT_DIRECTION_LTR
    }

    private fun initTextHeight() {
        val size = mMaxSize
        val paint = Paint().apply { this.textSize = Utils.getTextSize(size) }
        mTextHeight = paint.fontMetrics.run { abs(bottom - top).toInt() }
        mMarginTop = if ("my" == Locale.getDefault().language) {
            (mParentHeight / FOUR + mTextHeight / TWO).toInt() +
                    resources.getDimensionPixelOffset(R.dimen.layout_dp_7)
        } else {
            (mParentHeight / FOUR + mTextHeight / TWO).toInt()
        }
    }

    /**
     * 测量宽度最大的数字，Sans字体不等宽
     * */
    private fun initTextLength() {
        context.resources.run {
            var size = if (mCurrentStatus == STATUS_EXTRA_LONG) mMinSize else mMaxSize
            if (mIsHover) {
                size = mHoverSize
            }
            measureMaxLength(size)
        }
    }

    private fun measureMaxLength(textSize: Float) {
        val paint = Paint().apply {
            this.textSize = Utils.getTextSize(textSize)
            setTextWeightMedium()
        }
        mTextLength = if (mMaxNum == null) {
            mNumberFormat.run {
                val numArr =
                        arrayOf(
                                format(ZERO),
                                format(ONE),
                                format(TWO),
                                format(THREE),
                                format(FOUR),
                                format(FIVE),
                                format(SIX),
                                format(SEVEN),
                                format(EIGHT),
                                format(NINE)
                        )
                var maxWidth = 0F
                numArr.forEach {
                    val width = paint.measureText(it)
                    maxWidth = maxWidth.coerceAtLeast(width)
                    if (maxWidth == width) {
                        mMaxNum = it
                    }
                }
                maxWidth
            }
        } else {
            paint.measureText(mMaxNum)
        }
        mTextSize = paint.textSize
    }

    private fun setTextSize() {
        mTextLength.toInt().run {
            mTextSize.let {
                mDayStartTv?.setSize(this, it)
                mDayEndTv?.setSize(this, it)
                mHourStartTv?.setSize(this, it)
                mHourEndTv?.setSize(this, it)
                mMinuteStartTv?.setSize(this, it)
                mMinuteEndTv?.setSize(this, it)
                mSecondStartTv?.setSize(this, it)
                mSecondEndTv?.setSize(this, it)
                mMillisecondStartTv?.setSize(this, it)
                mMillisecondEndTv?.setSize(this, it)
                mColonStartTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
                mColonMiddleTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
                mColonEndTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
                mPointTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
            }
        }
    }

    private fun TextView.setSize(length: Int, size: Float) {
        layoutParams.run {
            width = length
        }
        this.setTextSize(TypedValue.COMPLEX_UNIT_PX, size)
    }

    private fun initViews(status: Int) {
        setTvNull()
        when (status) {
            STATUS_SHORT -> initShort()
            STATUS_LONG -> initLong()
            STATUS_EXTRA_LONG -> initExtraLong()
        }
        setTextSize()
    }

    private fun setTvNull() {
        mDayStartTv = null
        mDayEndTv = null
        mColonStartTv = null
        mHourStartTv = null
        mHourEndTv = null
        mColonMiddleTv = null
        mMinuteStartTv = null
        mMinuteEndTv = null
        mColonEndTv = null
        mSecondStartTv = null
        mSecondEndTv = null
        mPointTv = null
        mMillisecondStartTv = null
        mMillisecondEndTv = null
    }

    private fun initShort() {
        val viewBinding = StopWatchShortBinding.inflate(mInflater, this, true)
        viewBinding.run {
            mMinuteStartTv = minuteStartTv
            mMinuteEndTv = minuteEndTv
            mColonEndTv = colonEndTv
            mSecondStartTv = secondStartTv
            mSecondEndTv = secondEndTv
            mPointTv = pointTv
            mMillisecondStartTv = millisecondStartTv
            mMillisecondEndTv = millisecondEndTv
            mThemeColor.let {
                secondStartTv.setTextColor(it)
                secondEndTv.setTextColor(it)
                pointTv.setTextColor(it)
                millisecondStartTv.setTextColor(it)
                millisecondEndTv.setTextColor(it)
            }
            ViewExt.getColonStr().let {
                colonEndTv.setColon(it, getColonOffset(), getColonDefaultOffset())
            }
            pointTv.text = DecimalFormatSymbols.getInstance().decimalSeparator.toString()
        }
        setTypeface()
    }

    private fun initLong() {
        val viewBinding = StopWatchLongBinding.inflate(mInflater, this, true)
        viewBinding.run {
            mHourStartTv = hourStartTv
            mHourEndTv = hourEndTv
            mColonMiddleTv = colonMiddleTv
            mMinuteStartTv = minuteStartTv
            mMinuteEndTv = minuteEndTv
            mColonEndTv = colonEndTv
            mSecondStartTv = secondStartTv
            mSecondEndTv = secondEndTv
            mThemeColor.let {
                secondStartTv.setTextColor(it)
                secondEndTv.setTextColor(it)
            }
            ViewExt.getColonStr().let {
                colonMiddleTv.setColon(it, getColonOffset(), getColonDefaultOffset())
                colonEndTv.setColon(it, getColonOffset(), getColonDefaultOffset())
            }
        }
        setTypeface()
    }

    private fun initExtraLong() {
        val viewBinding = StopWatchExtraLongBinding.inflate(mInflater, this, true)
        viewBinding.run {
            mDayStartTv = dayStartTv
            mDayEndTv = dayEndTv
            mColonStartTv = colonStartTv
            mHourStartTv = hourStartTv
            mHourEndTv = hourEndTv
            mColonMiddleTv = colonMiddleTv
            mMinuteStartTv = minuteStartTv
            mMinuteEndTv = minuteEndTv
            mColonEndTv = colonEndTv
            mSecondStartTv = secondStartTv
            mSecondEndTv = secondEndTv
            mThemeColor.let {
                secondStartTv.setTextColor(it)
                secondEndTv.setTextColor(it)
            }
            ViewExt.getColonStr().let {
                colonStartTv.setColon(it, getColonOffset(), getColonDefaultOffset())
                colonMiddleTv.setColon(it, getColonOffset(), getColonDefaultOffset())
                colonEndTv.setColon(it, getColonOffset(), getColonDefaultOffset())
            }
        }
        setTypeface()
    }

    private fun getColonOffset(): Int {
        return when (mUiMode) {
            FoldScreenUtils.UiMode.LARGE_VERTICAL -> R.dimen.stopwatch_dial_colon_offset_max
            FoldScreenUtils.UiMode.LARGE_HORIZONTAL -> R.dimen.stopwatch_dial_colon_offset_max
            FoldScreenUtils.UiMode.MIDDLE -> R.dimen.stopwatch_dial_colon_offset_mid
            else -> R.dimen.stopwatch_dial_colon_offset
        }
    }

    private fun getColonDefaultOffset(): Int {
        return when (mUiMode) {
            FoldScreenUtils.UiMode.LARGE_VERTICAL -> R.dimen.stopwatch_dial_colon_offset_default
            FoldScreenUtils.UiMode.LARGE_HORIZONTAL -> R.dimen.stopwatch_dial_colon_offset_default
            FoldScreenUtils.UiMode.MIDDLE -> R.dimen.stopwatch_dial_colon_offset_default_mid
            else -> R.dimen.stopwatch_dial_colon_offset_default
        }
    }

    private fun setTypeface() {
        if (Utils.isAboveS()) {
            mDayStartTv?.setTextWeightMedium()
            mDayEndTv?.setTextWeightMedium()
            mHourStartTv?.setTextWeightMedium()
            mHourEndTv?.setTextWeightMedium()
            mMinuteStartTv?.setTextWeightMedium()
            mMinuteEndTv?.setTextWeightMedium()
            mSecondStartTv?.setTextWeightMedium()
            mSecondEndTv?.setTextWeightMedium()
            mMillisecondStartTv?.setTextWeightMedium()
            mMillisecondEndTv?.setTextWeightMedium()
            mColonStartTv?.setTextWeightMedium()
            mColonMiddleTv?.setTextWeightMedium()
            mColonEndTv?.setTextWeightMedium()
            mPointTv?.setTextWeightMedium()
        } else {
            //R版本不支字体粗细不支持900，改为默认加粗
            mDayStartTv?.setTextBold()
            mDayEndTv?.setTextBold()
            mHourStartTv?.setTextBold()
            mHourEndTv?.setTextBold()
            mMinuteStartTv?.setTextBold()
            mMinuteEndTv?.setTextBold()
            mSecondStartTv?.setTextBold()
            mSecondEndTv?.setTextBold()
            mMillisecondStartTv?.setTextBold()
            mMillisecondEndTv?.setTextBold()
            mColonStartTv?.setTextBold()
            mColonMiddleTv?.setTextBold()
            mColonEndTv?.setTextBold()
            mPointTv?.setTextBold()
        }
    }
}