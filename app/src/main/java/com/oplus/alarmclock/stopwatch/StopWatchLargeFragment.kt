/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StopWatchLargeFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopwatchMainLargeViewBinding
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.TextWeightUtils
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightNoChange
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.LocalColorRecyclerView
import com.oplus.clock.common.utils.Log

class StopWatchLargeFragment : StopWatchFragment<StopwatchMainLargeViewBinding>() {

    override fun layoutId(): Int {
        return R.layout.stopwatch_main_large_view
    }

    override fun initListener() {
        super.initListener()
        mViewBinding?.clickListener = this
    }

    override fun initTitle() {
        mViewBinding?.apply {
            val fontScale = resources.configuration.fontScale
            val size = resources.getDimension(R.dimen.text_size_sp_16)
            Utils.setSuitableFontSize(titleStartTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleMiddleTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleEndTv, size, fontScale, COUIChangeTextUtil.G2)
            titleStartTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleMiddleTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleEndTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
        }
    }

    override fun initDialClockAnimManager() {
        loadOsloView()
        super.initDialClockAnimManager()
        mViewBinding?.apply {
            mShadowManager.init(
                    null,
                    null,
                    null,
                    null,
                    null,
                    stopWatchBg,
                    null
            )
            mShadowAnimationManager?.init(
                    stopWatchScale,
                    stopWatch,
                    stopWatchDotTv,
                    stopWatchBg,
                    stopWatchRl
            )
        }
    }

    override fun resetStopWatch() {
        super.resetStopWatch()
        mViewBinding?.stopWatch?.center()
    }

    private fun loadOsloView() {
        mViewBinding?.apply {
            val set = ConstraintSet()
            Log.d(TAG, "loadOsloView: uiMode:$uiMode ${isLandscapeScreen()}")
            if (isLandscapeScreen()) {
                set.clone(context, R.layout.stopwatch_main_large_land_view)
            } else {
                set.clone(context, R.layout.stopwatch_main_large_land_content)
            }
            set.applyTo(stopWatchContent)
            stopWatch.center()
        }
    }

    private fun isLandscapeScreen(): Boolean {
        return FoldScreenUtils.UiMode.LARGE_HORIZONTAL == uiMode
    }

    private fun isPortraitScreen(): Boolean {
        return FoldScreenUtils.UiMode.LARGE_VERTICAL == uiMode
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        setTitleLineVisibility()
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout,
                    null, R.menu.action_menu_icon_stop_watch)
        }
    }

    override fun onScreenOrientationChanged(orientation: Int) {
        super.onScreenOrientationChanged(orientation)
        mViewBinding?.apply {
            mListAdapter?.setUiMode(uiMode)
            loadOsloView()
            val isEmpty = mListAdapter?.list.isNullOrEmpty()
            stopWatchListTitle.visibility = if (isEmpty) {
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
            if (mAnimationManager != null) {
                mAnimationManager.setUiMode(uiMode)
                if (isLandscapeScreen() && !isEmpty) {
                    mAnimationManager.mCurrentIsCenter = false
                    mAnimationManager.setDialLayout(stopWatchRl, false, null)
                }
            }
        }
        setTitleLineVisibility()
    }


    /**
     *  设置ListTitle的底部横线是否显示
     */
    private fun setTitleLineVisibility() {
        mViewBinding?.run {
            val isEmpty = mListAdapter?.list.isNullOrEmpty()
            if (isLandscapeScreen() || isEmpty) {
                dividerLine.visibility = View.INVISIBLE
                dividerLineLand.visibility = if (isEmpty) View.INVISIBLE else View.VISIBLE
            } else {
                dividerLine.visibility = View.VISIBLE
                dividerLineLand.visibility = View.INVISIBLE
            }
        }
    }

    /**
     * 表盘移动动画，横屏
     */
    override fun startDialTranslateAnimal(isCenter: Boolean) {
        super.startDialTranslateAnimal(isCenter)
        if (isLandscapeScreen()) {
            mViewBinding?.apply {
                mAnimationManager?.startDialMarginAnimation(
                        stopWatch,
                        null,
                        stopWatchRl,
                        stopWatchCl,
                        stopWatchList,
                        isCenter,
                        false,
                        isInit = true
                )
            }
        }
    }

    override fun stopWatchInterval(): StopWatchTextSmallView? {
        return mViewBinding?.stopWatchDotTv
    }

    override fun stopWatchCl(): ConstraintLayout? {
        return mViewBinding?.stopWatchContent
    }

    override fun stopWatch(): StopWatchTextView? {
        return mViewBinding?.stopWatch
    }

    override fun stopWatchView(): StopWatchView? {
        return mViewBinding?.stopWatchScale
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.nextComponent
    }

    override fun buttonCount(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun listView(): LocalColorRecyclerView? {
        return mViewBinding?.stopWatchList
    }

    override fun listTitle(): FrameLayout? {
        return mViewBinding?.stopWatchListTitle
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun setButtonImage(buttonCancel: COUITintImageView, buttonCount: COUITintImageView, isCancel: Boolean) {
        if (isCancel) {
            buttonCancel.isEnabled = false
            buttonCount.isEnabled = true
        } else {
            buttonCancel.isEnabled = true
            buttonCount.isEnabled = false
        }
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_333)
    }

    override fun getMainFabDrawable(context: Context, isStart: Boolean): Drawable? {
        return if (isStart) {
            context.getDrawable(R.drawable.button_start_mid)
        } else {
            context.getDrawable(R.drawable.button_pause_mid)
        }
    }
}