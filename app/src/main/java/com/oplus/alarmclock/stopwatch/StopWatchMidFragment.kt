/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StopWatchMidFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.content.Context
import android.graphics.drawable.Drawable
import android.transition.AutoTransition
import android.transition.TransitionManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopwatchMainMidViewBinding
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.TextWeightUtils
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightNoChange
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.LocalColorRecyclerView
import java.util.Locale

class StopWatchMidFragment : StopWatchFragment<StopwatchMainMidViewBinding>() {

    /**
     * 是否悬停
     */
    private var isHover = false

    /**
     * 计次列表高度
     */
    private var paddingHeight = 0

    private var mTimerTopMargin = 0

    /**
     * 控件是否加载完成
     */
    private var mIsInit = false

    private val mHoverRunAble = Runnable {
        mListAdapter?.updateFooterHeight(false, false)
    }

    var mIsTimer = false

    /**
     * 列表margin
     */
    var mCityListTopMargin = 0

    /**
     * 列表padding
     */
    var mPaddingHeight = 0

    /**
     * 是否分屏
     */
    private var mIsInMultiWindowMode = false

    override fun layoutId(): Int {
        return R.layout.stopwatch_main_mid_view
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            stopWatchInclude.stopWatch.apply {
                if (("my" == Locale.getDefault().language)) {
                    setTextSize(resources.getDimension(R.dimen.text_size_sp_18))
                } else {
                    setTextSize(resources.getDimension(R.dimen.text_size_sp_25))
                }
                if (!FoldScreenUtils.isRealOslo()) {
                    setPadding(resources.getDimensionPixelSize(R.dimen.layout_dp_033), 0, 0, 0)
                }
            }
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout,
                    null, R.menu.action_menu_icon_stop_watch)
        }
    }

    override fun initListener() {
        super.initListener()
        mViewBinding?.clickListener = this
        mViewBinding?.stopWatchInclude?.clickListener = this
        initManager()
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_235)
    }

    override fun getMainFabDrawable(context: Context, isStart: Boolean): Drawable? {
        return if (isStart) {
            context.getDrawable(R.drawable.button_start_mid)
        } else {
            context.getDrawable(R.drawable.button_pause_mid)
        }
    }

    override fun updateList() {
        super.updateList()
        mViewBinding?.apply {
            if (((stopWatchList.paddingTop > 0)
                            && ((stopWatchList.layoutParams as ViewGroup.MarginLayoutParams).topMargin > 0))
                    || isHover) {
                return
            }
            initManager()
        }
    }

    override fun initTopMargin(isHover: Boolean) {
        if (stopWatchCl() != null) {
            if (isHover) {
                stopWatchCl()!!.setPadding(0, 0, 0, 0)
            } else {
                val paddingTop = Utils.getStatusBarHeight(mContext)
                val bottomMargin = resources.getDimensionPixelSize(R.dimen.clock_dial_top_margin)
                stopWatchCl()!!.setPadding(0, paddingTop - bottomMargin, 0, 0)
            }
        }
    }

    override fun flexibleScenario() {
        super.flexibleScenario()
        context?.let {
            if (FoldScreenUtils.isFlexibleScenario(it) && isHover) {
                handleNormal()
            }
        }
    }

    /**
     * 初始化管理器
     */
    private fun initManager() {
        mViewBinding?.apply {
            initTopMargin(false)
            stopWatchInclude.stopWatch.update()
            stopWatchCl.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    mIsInit = true
                    stopWatchCl.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val topMargin = getTopDistance()
                    val height = resources.getDimensionPixelSize(R.dimen.layout_dp_330)
                    val margin = resources.getDimensionPixelSize(R.dimen.layout_dp_52)
                    val compensate = resources.getDimensionPixelSize(R.dimen.layout_dp_25)
                    val compensateT = resources.getDimensionPixelSize(R.dimen.layout_dp_40)
                    val paddingHeight = height - topMargin - compensate - compensateT
                    if (topMargin > 0 && paddingHeight > 0) {
                        <EMAIL> = paddingHeight
                        val params = stopWatchList.layoutParams as ViewGroup.MarginLayoutParams
                        params.topMargin = topMargin + compensate + compensateT
                        stopWatchList.layoutParams = params
                        stopWatchList.setPadding(0, paddingHeight, 0, 0)
                        stopWatchList.scrollBy(0, -paddingHeight)
                        mCityListTopMargin = topMargin + compensate + compensateT
                        mPaddingHeight = paddingHeight
                        setManager(height, paddingHeight, margin)
                        stopWatchInclude.stopWatch.center()
                        val layoutParams = stopWatchInclude.stopWatchScale.layoutParams as ViewGroup.MarginLayoutParams
                        layoutParams.topMargin = ((stopWatchInclude.stopWatchRl.height - stopWatchInclude.stopWatchScale.height) / TWO).toInt()
                        stopWatchInclude.stopWatchScale.layoutParams = layoutParams
                        setMidLayoutMargin()
                        mListAdapter?.setListManager(mStopWatchListManager)
                        mListAdapter?.updateFooterHeight(false, false)
                    }
                }
            })
        }
    }

    /**
     * 获取顶部的高度
     */
    private fun getTopDistance(): Int {
        mViewBinding?.apply {
            val location = IntArray(2)
            stopWatchInclude.stopWatchDivider.getLocationInWindow(location)
            val top: Int = stopWatchCl.paddingTop
            val compensation = resources.getDimensionPixelSize(R.dimen.layout_dp_20)
            return location[1] + stopWatchInclude.stopWatchDivider.height - top + compensation
        }
        return 0
    }

    private fun setManager(height: Int, scrollDistance: Int, topMargin: Int) {
        if (mStopWatchListManager == null) {
            mStopWatchListManager = context?.let { StopWatchListManager(it) }
        }
        mViewBinding?.let {
            mStopWatchListManager.initManager(null, it.stopWatchInclude, Triple(height, scrollDistance, topMargin),
                    uiMode, it.stopWatchCl, it.stopWatchList, it.stopWatchListTitle, false)
        }
    }

    override fun initDialClockAnimManager() {
        super.initDialClockAnimManager()
        mViewBinding?.stopWatchInclude?.apply {
            mShadowManager?.init(
                    null,
                    null,
                    null,
                    null,
                    null,
                    stopWatchBg,
                    null
            )
            mShadowAnimationManager?.init(
                    stopWatchScale,
                    stopWatch,
                    stopWatchDotTv,
                    stopWatchBg,
                    stopWatchRl
            )
        }
    }

    override fun initTitle() {
        mViewBinding?.apply {
            val fontScale = resources.configuration.fontScale
            val size = resources.getDimension(R.dimen.text_size_sp_16)
            Utils.setSuitableFontSize(titleStartTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleMiddleTv, size, fontScale, COUIChangeTextUtil.G2)
            Utils.setSuitableFontSize(titleEndTv, size, fontScale, COUIChangeTextUtil.G2)
            titleStartTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleMiddleTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
            titleEndTv.setTextWeightNoChange(TextWeightUtils.WEIGHT_BOLD)
        }
    }

    override fun resetStopWatch() {
        super.resetStopWatch()
    }

    override fun initHoverIfNeed() {
        super.initHoverIfNeed()
        /*悬停模式下切换至悬停状态*/
        (activity as? AlarmClock)?.apply {
            if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                mViewBinding?.stopWatchInclude?.stopWatchScale?.post {
                    val isListEmpty = mListAdapter?.list.isNullOrEmpty()
                    if (isListEmpty) {
                        //无计次情况下需要计算指针位置
                        mAnimationManager.stopwatchScaleMarginNoAnimator(mViewBinding?.stopWatchInclude?.stopWatchScale, true)
                    }
                }
                mViewBinding?.stopWatchList?.post {
                    mViewBinding?.stopWatchCl?.clipChildren = false
                    mAnimationManager.stopwatchTimeMarginNoAnimator(mViewBinding?.stopWatchInclude?.stopWatch, true)
                    onHoverPostureChanged(true)
                }
            }
        }
    }

    override fun startShadowAnimation(center: Boolean) {
        if (mShadowAnimationManager != null) {
            mShadowAnimationManager.startAnimation(center, true)
        }
    }

    override fun startDialTranslateAnimal(isCenter: Boolean) {
        super.startDialTranslateAnimal(isCenter)
        mViewBinding?.apply {
            mAnimationManager?.startDialMarginAnimation(
                    stopWatchInclude.stopWatch,
                    stopWatchInclude.stopWatchScale,
                    stopWatchInclude.stopWatchRl,
                    stopWatchCl,
                    stopWatchList,
                    isToCenter = isCenter,
                    isConfidential = false,
                    mIsInit
            )
        }
    }

    override fun onHoverPostureChanged(hover: Boolean) {
        super.onHoverPostureChanged(hover)
        if (mViewBinding == null || mAnimationManager == null || isHover == hover) {
            return
        }
        isHover = hover
        mAnimationManager.setIsHover(hover)
        if (hover) {
            handleHover()
        } else {
            handleNormal()
        }
    }

    override fun onScreenOrientationChanged(orientation: Int) {
        super.onScreenOrientationChanged(orientation)
        if (FoldScreenUtils.isRealOslo()) {
            setMidLayoutMargin()
        }
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        super.onMultiWindowModeChanged(isInMultiWindowMode)
        mIsInMultiWindowMode = isInMultiWindowMode
    }

    /**
     * 调整平板分屏后显示中屏布局的调整
     * && mIsInMultiWindowMode 避免影响中屏显示效果
     * */
    private fun setMidLayoutMargin() {
        mViewBinding?.apply {
            val titleLayoutParams =
                stopWatchTitleLayout.layoutParams as ViewGroup.MarginLayoutParams
            val params = stopWatchList.layoutParams as ViewGroup.MarginLayoutParams
            if (DeviceUtils.isLandscapeScreen() && mIsInMultiWindowMode) {
                params.marginStart = resources.getDimensionPixelSize(R.dimen.layout_dp_50)
                params.marginEnd = resources.getDimensionPixelSize(R.dimen.layout_dp_50)

                titleLayoutParams.marginStart =
                    resources.getDimensionPixelSize(R.dimen.layout_dp_50)
                titleLayoutParams.marginEnd =
                    resources.getDimensionPixelSize(R.dimen.layout_dp_50)
            } else {
                params.marginStart = resources.getDimensionPixelSize(R.dimen.layout_dp_158)
                params.marginEnd = resources.getDimensionPixelSize(R.dimen.layout_dp_158)

                titleLayoutParams.marginStart =
                    resources.getDimensionPixelSize(R.dimen.layout_dp_158)
                titleLayoutParams.marginEnd =
                    resources.getDimensionPixelSize(R.dimen.layout_dp_158)
            }
            stopWatchTitleLayout.layoutParams = titleLayoutParams
            stopWatchList.layoutParams = params
            val layoutParams = stopWatchInclude.stopWatchScale.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.topMargin = ((stopWatchInclude.stopWatchRl.height - stopWatchInclude.stopWatchScale.height) / TWO).toInt()
            stopWatchInclude.stopWatchScale.layoutParams = layoutParams
        }
    }

    /**
     * 悬停
     */
    private fun handleHover() {
        mViewBinding?.apply {
            //停止未执行完的悬停过渡动画
            TransitionManager.endTransitions(stopWatchCl)
            stopWatchInclude.stopWatch.visibility = View.INVISIBLE
            mViewBinding?.stopWatchList?.visibility = View.INVISIBLE
            val ac = AutoTransition()
            ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
            ac.interpolator = COUIEaseInterpolator()
            val set = ConstraintSet()
            set.clone(mContext, R.layout.stopwatch_main_view_hover)
            TransitionManager.beginDelayedTransition(stopWatchCl, ac)
            set.applyTo(stopWatchCl)
            //滚动到列表顶部
            mStopWatchListManager.listScroll(0)
            //取消联动
            mStopWatchListManager.setEnable(false)
            //取消自动滚动
            mStopWatchListManager.setAutoScroll(false)
            stopWatchInclude.stopWatch.visibility = View.VISIBLE
            //隐藏表盘
            stopWatchInclude.stopWatchBg.visibility = View.INVISIBLE
            stopWatchInclude.stopWatchDialScale.visibility = View.INVISIBLE
            stopWatchInclude.stopWatchScale.visibility = View.INVISIBLE
            mViewBinding?.stopWatchList?.visibility = View.VISIBLE
            //恢复列表间距
            mViewBinding?.stopWatchList?.apply {
                //清除view延迟消息
                handler?.removeCallbacks(mHoverRunAble)
                setPadding(0, 0, 0, 0)
            }
            //隐藏/显示标题
            if (mListAdapter?.list.isNullOrEmpty()) {
                stopWatchListTitle.visibility = View.INVISIBLE
            } else {
                stopWatchListTitle.visibility = View.VISIBLE
            }
            mIsTimer = mListAdapter?.list.isNullOrEmpty()
            mAnimationManager.toScale(stopWatchInclude.stopWatch, isCenter = true, isText = true)
            stopWatchCl.clipChildren = true
            //设置文字内容
            stopWatchInclude.stopWatch.apply {
                setIsHover(true)
                setTextSize(resources.getDimension(R.dimen.text_size_sp_58))
                val llp = layoutParams as ViewGroup.MarginLayoutParams
                mTimerTopMargin = llp.topMargin
                llp.topMargin = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_142)
                layoutParams = llp
            }
            initTopMargin(true)
        }
    }

    private fun handleNormal() {
        mViewBinding?.apply {
            stopWatchInclude.apply {
                //停止未执行完的悬停过渡动画
                TransitionManager.endTransitions(stopWatchCl)
                stopWatchRl.visibility = View.INVISIBLE
                stopWatch.visibility = View.INVISIBLE
                mViewBinding?.stopWatchList?.visibility = View.INVISIBLE
                val ac = AutoTransition()
                ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
                ac.interpolator = COUIEaseInterpolator()
                val set = ConstraintSet()
                set.clone(mContext, R.layout.stopwatch_main_mid_view_content)
                TransitionManager.beginDelayedTransition(stopWatchCl, ac)
                set.applyTo(stopWatchCl)
                val isListEmpty = mListAdapter?.list.isNullOrEmpty()
                stopWatchBg.visibility = View.VISIBLE
                stopWatchDialScale.visibility = View.VISIBLE
                stopWatchScale.visibility = View.VISIBLE
                if (!isListEmpty) {
                    stopWatchListTitle.visibility = View.VISIBLE
                } else {
                    stopWatchListTitle.visibility = View.INVISIBLE
                }
                mViewBinding?.stopWatchList?.visibility = View.VISIBLE
                mViewBinding?.stopWatchList?.apply {
                    scrollToPosition(0)
                    val params = layoutParams as ViewGroup.MarginLayoutParams
                    params.topMargin = mCityListTopMargin
                    layoutParams = params
                    setPadding(0, mPaddingHeight, 0, 0)
                    scrollBy(0, -mPaddingHeight)
                    postDelayed(mHoverRunAble, HOVER_DEFAULT_ANIMATOR_DURATION)
                }
                stopWatchCl.clipChildren = !isListEmpty
                mAnimationManager.toScale(stopWatch, isListEmpty, true)
                mAnimationManager.toScale(stopWatchScale, isListEmpty, false)
                mAnimationManager.toScale(stopWatchRl, isListEmpty, false)
                stopWatch.apply {
                    setIsHover(false)
                    setTextSize(resources.getDimension(R.dimen.text_size_sp_25))
                    val llp = layoutParams as ViewGroup.MarginLayoutParams
                    if (mIsTimer != isListEmpty) {
                        if (mIsTimer) {
                            llp.topMargin = mTimerTopMargin - FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
                        } else {
                            llp.topMargin = mTimerTopMargin + FoldScreenUtils.STOP_WATCH_TIME_TOP_MARGIN
                        }
                    } else {
                        llp.topMargin = mTimerTopMargin
                    }
                    layoutParams = llp
                }
                stopWatch.visibility = View.VISIBLE
                stopWatchRl.visibility = View.VISIBLE
                mStopWatchListManager.setEnable(true)
                mStopWatchListManager.setAutoScroll(true)
                initTopMargin(false)
                if (mIsTimer != isListEmpty) {
                    mAnimationManager.stopwatchScaleMargin(stopWatchScale, isListEmpty)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mViewBinding?.stopWatchList?.run {
            if (handler != null) {
                handler.removeCallbacksAndMessages(null)
            }
        }
    }

    override fun isHover(): Boolean {
        return isHover
    }

    override fun stopWatchInterval(): StopWatchTextSmallView? {
        return mViewBinding?.stopWatchInclude?.stopWatchDotTv
    }

    override fun stopWatchCl(): ConstraintLayout? {
        return mViewBinding?.stopWatchCl
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun stopWatch(): StopWatchTextView? {
        return mViewBinding?.stopWatchInclude?.stopWatch
    }

    override fun stopWatchView(): StopWatchView? {
        return mViewBinding?.stopWatchInclude?.stopWatchScale
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.nextComponent
    }

    override fun buttonCount(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun listView(): LocalColorRecyclerView? {
        return mViewBinding?.stopWatchList
    }

    override fun listTitle(): FrameLayout? {
        return mViewBinding?.stopWatchListTitle
    }

    override fun stopPosition(isHover: Boolean) {
        if (mListAdapter.list.size == 0) {
            mStopWatchListManager.stop()
        } else {
            mStopWatchListManager.stop(isHover)
        }
    }

    override fun setButtonImage(buttonCancel: COUITintImageView, buttonCount: COUITintImageView, isCancel: Boolean) {
        if (isCancel) {
            buttonCancel.isEnabled = false
            buttonCount.isEnabled = true
        } else {
            buttonCancel.isEnabled = true
            buttonCount.isEnabled = false
        }
    }
}