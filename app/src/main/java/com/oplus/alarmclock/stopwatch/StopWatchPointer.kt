/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PaintFlagsDrawFilter
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.animation.PathInterpolator
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.alarmclock.R

open class StopWatchPointer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    companion object {
        private const val TWO = 2
        const val TEN = 10
        const val PATH_ONE = 0.3F
        const val PATH_TWO = 0F
        const val PATH_THREE = 0.2F
        const val PATH_FOUR = 1F
        const val THIRTY = 60L
        const val SIXTY = 60L
        const val ONE_CIRCLE_DEGREE = 360F
        const val RESTORE_TIME = 500L
        const val THOUSAND = 1000L
    }

    var mCenterX = 0F
    var mCenterY = 0F
    lateinit var mPaint: Paint
    var mTime = 0L

    var mTotalRestoreTime = 0L
    var mRestoreTime = 0L
    var mMultiple = 1F
    var mAnimator: ValueAnimator? = null
    val mRestoreInterpolator by lazy { PathInterpolator(PATH_ONE, PATH_TWO, PATH_THREE, PATH_FOUR) }
    private var mIsRestore = false

    /**
     * 拦截刷新
     * 默认不拦截
     * pause的时候拦截
     * resume的时候放开
     */
    protected var mInterceptInvalidate = false
    protected var mPauseTime = 0L
    protected var mShadowX = 0F
    protected var mShadowY = 0F
    protected var mShadowRadius = 0F
    protected var mShadowColor = 0
    protected var mIsDark = false //是否是亮色还是夜色
    protected var mBitmap: Bitmap? = null
    private val mDrawFilter = PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG)

    init {
        mIsDark = COUIDarkModeUtil.isNightMode(context)
    }

    fun update(time: Long) {
        mTime = time
        if (mInterceptInvalidate) {
            return
        }
        mIsRestore = false
        calculateAngle()
        postInvalidate()
    }

    open fun onResume() {
        cancelAnimator()
    }

    open fun onPause() {
        cancelAnimator()
        mPauseTime = mTime
        mInterceptInvalidate = true
    }

    open fun onStop() {
        if ((mTime <= 0) || mIsRestore) {
            return
        }
        mIsRestore = true
        mRestoreTime = 0
        mPauseTime = 0
        mTotalRestoreTime = getTotalRestoreTime()
    }

    open fun onRelease() {
        mRestoreTime = 0
        cancelAnimator()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        kotlin.runCatching { mBitmap?.recycle() }
        onRelease()
    }

    open fun initColor(context: Context) {
    }

    open fun initSize(context: Context, attrs: AttributeSet?) {
        context.resources?.run {
            mMultiple = getMultiple(context, attrs)
            initPaint()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mCenterX = w.toFloat() / TWO
        mCenterY = h.toFloat() / TWO
    }

    override fun onDraw(canvas: Canvas) {
        if (mIsRestore) {
            canvas.onStop()
        } else {
            canvas.onStart()
        }
    }

    open fun Canvas.onStart() {
    }

    open fun Canvas.onStop() {
    }

    private fun initPaint() {
        mPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            strokeCap = Paint.Cap.ROUND
        }
    }

    private fun getMultiple(context: Context, attrs: AttributeSet?): Float {
        var multiple = 1F
        context.resources?.run {
            attrs?.let {
                val obtainAttributes = obtainAttributes(it, R.styleable.StopWatchPointer)
                multiple =
                    obtainAttributes.getFloat(R.styleable.StopWatchPointer_pointer_multiple, 1F)
                obtainAttributes.recycle()
            }
        }
        return multiple
    }

    /**
     * 复原的时间
     * 时长：
     * T=500ms+10t
     * t：当前停止时间
     * T：转动总时长
     * */
    open fun getTotalRestoreTime(): Long {
        return 0L
    }

    open fun getRestoreTime(): Long {
        return 0L
    }

    open fun calculateAngle() {
    }

    open fun getAnimatorParams(first: Float, second: Float): FloatArray {
        return if (first > second) {
            floatArrayOf(first, ONE_CIRCLE_DEGREE, second + ONE_CIRCLE_DEGREE)
        } else {
            floatArrayOf(first, second)
        }
    }

    /**
     * 重置Resume动画执行后的属性
     */
    protected open fun resetResumeProperties() {
        mAnimator = null
        mInterceptInvalidate = false
    }

    /**
     * 重置onStop动画执行后的属性
     */
    protected open fun resetStopProperties() {
        mAnimator = null
        mInterceptInvalidate = false
    }

    /**
     * 取消动画
     */
    protected open fun cancelAnimator() {
        mAnimator?.run {
            if (isRunning) {
                cancel()
            }
        }
    }

    protected fun drawableToBitmap(drawable: Drawable?): Bitmap? {
        if (drawable == null) {
            return null
        }

        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight

        if (width <= 0 || height <= 0) {
            return null
        }
        val targetWidth = (width * mMultiple).toInt()
        val targetHeight = (height * mMultiple).toInt()

        // Create a Bitmap with the same dimensions as the Drawable
        val bitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // Enable anti-aliasing using PaintFlagsDrawFilter
        canvas.drawFilter = mDrawFilter

        // Set the bounds of the Drawable to match the Bitmap
        drawable.setBounds(0, 0, targetWidth, targetHeight)

        // Draw the Drawable onto the Canvas
        drawable.draw(canvas)

        return bitmap
    }

    fun Paint.getCirclePaint(circleColor: Int): Paint {
        return apply {
            style = Paint.Style.FILL_AND_STROKE
            color = circleColor
            setShadowLayer(
                mShadowRadius,
                mShadowX,
                mShadowY,
                mShadowColor
            )
        }
    }

    fun Paint.getSecondPaint(secondColor: Int, secondStroke: Float): Paint {
        return apply {
            reset()
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            color = secondColor
            strokeWidth = secondStroke
            strokeCap = Paint.Cap.ROUND
            setShadowLayer(
                mShadowRadius,
                mShadowX,
                mShadowY,
                mShadowColor
            )
        }
    }
}