/***
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: StopWacth.java
 * ModuleName: StopWacth Author: MaCong Create Date: Description: the main activity of StopWatchUI
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2010-9-24 MaCong CheckList
 */
package com.oplus.alarmclock.stopwatch;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.SystemClock;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.statelistutil.COUIStateListUtil;
import com.coui.appcompat.tintimageview.COUITintImageView;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.RuntimePermissionAlert;
import com.oplus.alarmclock.base.BaseUiModeFragment;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.FlashBackHelper;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.LinearMotorHelper;
import com.oplus.alarmclock.view.LocalColorRecyclerView;
import com.oplus.alarmclock.view.stopwatch.ShadowAnimationManager;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.dial.ShadowManager;
import com.oplus.statistics.OplusTrack;
import com.coloros.widget.commondata.Constants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.databinding.ViewDataBinding;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;

public abstract class StopWatchFragment<T extends ViewDataBinding> extends BaseUiModeFragment<T>
        implements AlarmClock.IFragmentFocused,
        OnClickListener {
    public static final long TWO = 2;
    public static final long HOVER_DEFAULT_ANIMATOR_DURATION = 800;
    public static final String REFRESH_STOPWATCH = "STOPWATCH_REFRESH";
    private static final String SP_NAME = AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP;
    private static final String TAG = "StopWatchFragment";
    private static final long[] sVibratePattern = new long[]{10, 50};
    private static final String STOPWATCH_STATUS_PREFERENCE = "stopwatch_status_preference";
    private static final String STOPWATCH_LAST_RECORD_TIME_PREFERENCE = "stopwatch_last_record_time_preference";
    private static final int STATUS_0 = 0;
    private static final int STATUS_1 = 1;
    private static final int STATUS_2 = 2;
    private static final int SHOW_BTN = 0x01;
    private static final int DEFAULT_ANIMATOR_DURATION = 250;
    private static final long NOTIFY_DATA_CHANGE_DELAY = 600;
    private static final long mFrequency = 4000L;
    private final DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();
    private final Handler mHandler = new Handler();
    private final FlashBackHelper.OnClickListener mFlashBackListener = new FlashBackHelper.OnClickListener() {
        @Override
        public void onLeftButtonClick() {
            mainActionSelected();
        }

        @Override
        public void onRightButtonClick() {
            countAndCancelStopwatch();
        }
    };

    /**
     * Called when the activity is first created.
     */
    private final ServiceConnection mServiceConnect = new ServiceConnection() {
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "onServiceConnected Bind successful");
            if ((getActivity() == null) || (getActivity().isFinishing())) {
                return;
            }
            if (!(service instanceof StopWatchService.StopWatchBinder)) {
                Log.d(TAG, "service is not instanceof StopWatchBinder");
                return;
            }
            StopWatchService.StopWatchBinder mIBinder = (StopWatchService.StopWatchBinder) service;
            mService = mIBinder.getService();
            onServiceConnectedToUpdateLayout();
        }

        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected unbind!");
            if (mService != null) {
                mService.setDisplayTime(null);
                mService.setDisplayIntervalTime(null);
            }
        }
    };
    private final BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {

        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "stopwatch onReceive: " + action);
            if (TextUtils.isEmpty(action)) {
                return;
            }
            if (REFRESH_STOPWATCH.equals(action)) {
                int type = intent.getIntExtra(StopwatchNotificationManager.STOPWATCH_ACTION_TYPE, -1);
                Log.i(TAG, "REFRESH_STOPWATCH type:" + type);
                if (type != -1) {
                    if (type == STATUS_0) {
                        startStopwatch();
                    } else if (type == STATUS_1) {
                        pauseAndContinueStopwatch();
                    } else if (type == STATUS_2) {
                        countAndCancelStopwatch();
                    }
                }
            }
        }
    };
    private final DisplayTime mDisplayTime = this::displayTime;
    private final DisplayIntervalTime mDisplayIntervalTime = this::displayIntervalTime;
    public ShadowManager mShadowManager;
    protected int mAnimationNum = 0;
    protected ShadowAnimationManager mShadowAnimationManager = null;
    protected StopWatchRecordAdapter mListAdapter;
    protected StopWatchListManager mStopWatchListManager;
    protected StopWatchAnimationManager mAnimationManager;


    private long mCount = 0;
    private Vibrator mVibrator;
    private List<Map<String, String>> mData = new ArrayList<>();
    private StopWatchService mService;
    private boolean mServiceUnbind = false;
    private int mStatus = STATUS_0;
    private VibrationEffect mVibrationEffectMiddle;
    private boolean mFocused = false;
    private LinearLayoutManager mLinearLayoutManager;
    private HandlerAni mHandlerAni;
    private LinearMotorHelper mLinearMotorHelper;
    private RuntimePermissionAlert mRuntimePermissionAlert;
    private boolean mIsFirstEnter = true;
    private boolean mIsSelected = false;
    private boolean mNotInitEffect;


    private void onServiceConnectedToUpdateLayout() {
        Log.i(TAG, "onServiceConnectedToUpdateLayout");
        mService.setLastElapseTime(getLastElapseTime());
        mService.setDisplayTime(mDisplayTime);
        mService.setDisplayIntervalTime(mDisplayIntervalTime);
        mStatus = mService.mStatus;
        recordStatus(mStatus);
        initViews(mStatus);
        mData.clear();
        mData.addAll(mService.getStopWatchRecordsSP());
        updateList(mData);
        if (mService.shouldResentNotify()) {
            mService.showStopwatchNotification(mService.getLastRecordTitle(), mStatus, false);
        } else {
            mService.cancelStopwatchNotification();
        }
    }

    public void displayIntervalTime(long time) {
        if (stopWatchInterval() != null) {
            stopWatchInterval().update(time);
        }
    }


    public void redDotSetting() {
        if (couiToolbar() != null) {
            redDotSetting(couiToolbar());
        }
    }

    private static class HandlerAni extends StaticHandler<StopWatchFragment> {

        HandlerAni(StopWatchFragment t) {
            super(t);
        }

        @Override
        public void handleMessage(Message msg, StopWatchFragment t) {
            if (msg.what == SHOW_BTN) {
                t.refreshBottomLayout();
            }
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
        onCreateInit();
    }

    @Override
    protected void initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup group) {
        super.initView(inflater, group);
        if (group != null) {
            mContext = group.getContext();
        } else {
            mContext = inflater.getContext();
        }
        initTitle();
        initDialClock();
        initTopMargin(false);
        initButton();
        initRecyclerView();
        initListener();
        bindStopWatchService();
        initHoverIfNeed();
        initClockEffect();
    }

    /**
     * 初始化光追时钟
     */
    protected void initClockEffect() {
        if (mShadowManager != null) {
            if (Utils.isAboveOS14()) {
                if (mNotInitEffect && mIsFirstEnter) {
                    clockEffectViewEnter();
                    mNotInitEffect = false;
                }
            }
        }
    }

    @Override
    protected void initData() {
        super.initData();
        mVibrator = (Vibrator) mContext.getSystemService(Context.VIBRATOR_SERVICE);
    }

    protected void initDialClock() {
        mAnimationManager = new StopWatchAnimationManager(getUiMode());
        stopWatch().setUiMode(getUiMode());
        resetStopWatch();
        initDialClockAnimManager();
        setDialBg(false);
    }

    protected void initDialClockAnimManager() {
        mShadowManager = new ShadowManager();
        mShadowAnimationManager = new ShadowAnimationManager();
    }

    private void mainActionSelected() {
        if (!mAnimationManager.getButtonAnimation()) {
            //底部按钮动画非执行状态可点击
            if ((mStatus == STATUS_1) || (mStatus == STATUS_2)) {
                pauseAndContinueStopwatch();
            } else {
                startStopwatch();
            }
        }
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.stop_watch || id == R.id.stop_watch_dot_tv) {
            if (mStopWatchListManager != null) {
                mStopWatchListManager.reset();
            }
        } else if (id == R.id.first_component) {
            mStatus = STATUS_1;
            countAndCancelStopwatch();
        } else if (id == R.id.next_component) {
            mStatus = STATUS_2;
            countAndCancelStopwatch();
        }
    }

    private void startStopwatch() {
        if (mService == null) {
            return;
        }
        if (mStatus == STATUS_0) {
            FlashBackHelper.getInstance().sendData(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY,
                    OplusStopWatchWrapper.INSTANCE.getFlashBackTimeStr(OplusStopWatch.LONG_FORMAT),
                    getResources().getString(R.string.StopWatch_Title),
                    getResources().getString(R.string.text_timer_btn_pause),
                    getResources().getColor(R.color.flash_back_bottom_left_red_color),
                    getResources().getColor(R.color.flash_back_bottom_content_white_color),
                    getResources().getString(R.string.record),
                    getResources().getColor(R.color.flash_back_bottom_right_green_color),
                    getResources().getColor(R.color.flash_back_bottom_content_white_color),
                    mFlashBackListener);
            start();
            mStatus = STATUS_1;
            recordStatus(mStatus);
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_STOPWATCH_START_MENU);
        }
        refreshBottomLayout();
        if (mService != null) {
            mService.saveStopWatchStatus(true, true);
        }
    }

    private void countAndCancelStopwatch() {
        if (mService == null) {
            return;
        }
        handleCountButton();
    }

    private void pauseAndContinueStopwatch() {
        if (mService == null) {
            return;
        }
        if (mStatus == STATUS_1) {
            pause();
            mStatus = STATUS_2;
            recordStatus(mStatus);
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_STOPWATCH_PAUSE_MENU);
            if (mService != null) {
                mService.saveStopWatchStatus(false, true);
            }
        } else if (mStatus == STATUS_2) {
            toContinue();
            mStatus = STATUS_1;
            recordStatus(mStatus);
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_STOPWATCH_CONTINUE_MENU);
            if (mService != null) {
                mService.saveStopWatchStatus(true, true);
            }
        }
        refreshBottomLayout();
    }

    private void registerBroadcast() {
        IntentFilter filterLocal = new IntentFilter();
        filterLocal.addAction(REFRESH_STOPWATCH);
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalReceiver, filterLocal);
    }

    private void initRecyclerView() {
        LocalColorRecyclerView listView = listView();
        if (listView != null) {
            boolean hasData = mData != null && !mData.isEmpty();
            mListAdapter = new StopWatchRecordAdapter(listView, mContext, getUiMode(), mData);
            mListAdapter.setIsSplit(FoldScreenUtils.UiMode.SMALL == getUiMode());
            mLinearLayoutManager = new LinearLayoutManager(mContext);
            listView.setLayoutManager(mLinearLayoutManager);
            DefaultItemAnimator animator = new DefaultItemAnimator();
            animator.setAddDuration(DEFAULT_ANIMATOR_DURATION);
            animator.setMoveDuration(DEFAULT_ANIMATOR_DURATION);
            listView.setItemAnimator(animator);
            listView.setAdapter(mListAdapter);
            if (!hasData) {
                listView.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
            } else {
                listView.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            }
        }
    }


    @Override
    public void onScreenOrientationChanged(int orientation) {
        super.onScreenOrientationChanged(orientation);
        if (mService != null) {
            mData.clear();
            mData.addAll(mService.getStopWatchRecordsSP());
            updateList(mData);
        }
    }


    private void bindStopWatchService() {
        mServiceUnbind = false;
        Context context = getActivity();
        if (context != null) {
            Intent intent = new Intent(context, StopWatchService.class);
            context.bindService(intent, mServiceConnect, Service.BIND_AUTO_CREATE);
            context.startService(intent);
        }
    }

    private void handleCountButton() {
        Log.d(TAG, "handleCountButton: mStatus: " + mStatus);
        mCount = 0;
        if (mStatus == STATUS_0) {

        } else if (mStatus == STATUS_1) {
            record();
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_STOPWATCH_RECORD_MENU);
            if (mService != null) {
                mService.saveStopWatchStatus(false, true);
            }
        } else if (mStatus == STATUS_2) {
            FlashBackHelper.getInstance().releaseManager(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY);
            mStatus = STATUS_0;
            recordStatus(mStatus);
            if (stopWatch() != null) {
                stopWatch().update(0);
            }
            if (stopWatchView() != null) {
                stopWatchView().onStop();
            }
            rePosition();
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_STOPWATCH_RESET_MENU);
            refreshBottomLayout(true);
        }
    }

    public void refreshBottomLayout() {
        refreshBottomLayout(false);
    }

    public void refreshBottomLayout(boolean isClick) {
        COUIFloatingButton buttonStart = buttonStart();
        COUITintImageView buttonCancel = buttonCancel();
        COUITintImageView buttonCount = buttonCount();
        if ((buttonStart != null) && (buttonCancel != null) && (buttonCount != null) && (mContext != null)) {
            buttonStart.setMainFloatingButtonBackgroundColor(COUIStateListUtil.createColorStateList(
                    COUIContextUtil.getAttrColor(mContext, R.attr.couiColorPrimary),
                    COUIContextUtil.getAttrColor(mContext, R.attr.couiColorPrimary)));
            AppCompatImageView mainButton = buttonStart.getMainFloatingButton();
            FlashBackHelper helper = FlashBackHelper.getInstance();
            if (mStatus == STATUS_0) {
                buttonStart.setMainFabDrawable(getMainFabDrawable(mContext, true));
                //执行按钮隐藏动画
                if (buttonCancel.getVisibility() == View.VISIBLE && isClick) {
                    mAnimationManager.hideButtonAnimation(buttonCount, true, buttonStart);
                    mAnimationManager.hideButtonAnimation(buttonCancel, false, buttonStart);
                } else {
                    buttonCount.setVisibility(View.INVISIBLE);
                    buttonCancel.setVisibility(View.INVISIBLE);
                }
                if (mainButton != null) {
                    mainButton.setContentDescription(mContext.getString(R.string.text_timer_btn_start));
                }
            }
            if (mStatus == STATUS_1) {
                helper.setStopWatchPlay(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY);
                buttonStart.setMainFabDrawable(getMainFabDrawable(mContext, false));
                //执行按钮显示动画
                if (buttonCancel.getVisibility() == View.INVISIBLE) {
                    mAnimationManager.displayButtonAnimation(buttonCount, true);
                    mAnimationManager.displayButtonAnimation(buttonCancel, false);
                }
                buttonCancel.setVisibility(View.VISIBLE);
                buttonCancel.setEnabled(false);
                setButtonImage(buttonCancel, buttonCount, true);
                buttonCount.setVisibility(View.VISIBLE);
                buttonCount.setEnabled(true);
                if (mainButton != null) {
                    mainButton.setContentDescription(mContext.getString(R.string.text_timer_btn_pause));
                }
            }
            if (mStatus == STATUS_2) {
                helper.setStopWatchPause(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY);
                buttonStart.setMainFabDrawable(getMainFabDrawable(mContext, true));
                buttonCancel.setVisibility(View.VISIBLE);
                setButtonImage(buttonCancel, buttonCount, false);
                buttonCancel.setEnabled(true);
                buttonCount.setVisibility(View.VISIBLE);
                buttonCount.setEnabled(false);
                if (mainButton != null) {
                    mainButton.setContentDescription(mContext.getString(R.string.text_timer_btn_start));
                }
            }
        }
    }

    private void initViews(int status) {
        FlashBackHelper.getInstance().setListener(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY, mFlashBackListener);
        if (status == STATUS_0) {
            mData.clear();
            updateList(mData);
        } else if (status == STATUS_2) {
            if (mService != null) {
                displayTime(mService.mCountTime);
//                mStopWatch.setContentDescription(mStopWatch.getTalkBackTime());
                displayIntervalTime(mService.mCountIntervalTime);
            }
        }
        refreshBottomLayout();
    }

    protected void resetStopWatch() {
        if (stopWatch() != null) {
            stopWatch().update(0);
        }
    }

    protected void initButton() {
        COUIFloatingButton buttonStart = buttonStart();
        COUITintImageView buttonCancel = buttonCancel();
        COUITintImageView buttonCount = buttonCount();
        if (buttonCancel == null || buttonStart == null || buttonCount == null) {
            return;
        }
        buttonCancel.setOnClickListener(this);
        buttonCount.setOnClickListener(this);
        Utils.initPressFeedback(buttonCancel, buttonCancel);
        Utils.initPressFeedback(buttonCount, buttonCount);
        buttonCancel.setContentDescription(mContext.getResources().getString(R.string.RePostion));
        buttonCount.setContentDescription(mContext.getResources().getString(R.string.record));
        buttonStart.setMainFloatingButtonBackgroundColor(ColorStateList.valueOf(COUIContextUtil.getAttrColor(mContext, R.attr.couiColorPrimary)));
    }

    public boolean onMenuItemClick(MenuItem item) {
        if (item.getItemId() == R.id.settings) {
            if (mDoubleClickHelper.canClick()) {
                startToSetting();
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.SETTING_FROM_STOPWATCH);
            }
        }
        return true;
    }

    protected void initListener() {
        mRuntimePermissionAlert = new RuntimePermissionAlert(this.getActivity(), new RuntimePermissionAlert.RuntimePermissionCallBack() {
            @Override
            public void doAfterGranted(boolean isNeedOpenAddView) {
                mainActionSelected();
            }

            @Override
            public void doAfterDenieD() {
                Log.d(TAG, "doAfterDenieD");
            }

            @Override
            public void onExitClick() {

            }

            @Override
            public void onClickOutside() {
                Log.d(TAG, "onClickOutside");
            }
        });
        if (buttonStart() != null) {
            buttonStart().setOnChangeListener(new COUIFloatingButton.OnChangeListener() {
                @Override
                public boolean onMainActionSelected() {
                    if ((mRuntimePermissionAlert != null)
                            && Utils.isAboveT()
                            && !mRuntimePermissionAlert.hasNotificationPermission()
                            && !Utils.hasRequestNotifyPermission(ClockConstant.KEY_STOPWATCH_HAS_REQUEST_NOTIFY)) {
                        Utils.saveRequestNotifyPermission(ClockConstant.KEY_STOPWATCH_HAS_REQUEST_NOTIFY, true);
                        mRuntimePermissionAlert.requestNotificationPermission();
                    } else {
                        mainActionSelected();
                    }
                    return false;
                }

                @Override
                public void onToggleChanged(boolean b) {
                }
            });
        }
        if (couiToolbar() != null) {
            couiToolbar().setOnMenuItemClickListener(menuItem -> {
                onMenuItemClick(menuItem);
                return false;
            });
        }
    }

    @Override
    public ViewGroup getBlurView() {
        return null;
    }

    @Override
    public void onFocused(boolean focused) {
        mFocused = focused;
        if (mFocused) {
            if (mStatus == STATUS_1) {
                StopWatchWakeLock.acquireCpuWakeLock(getActivity());
            }
        } else {
            StopWatchWakeLock.releaseCpuLock();
        }
    }

    @Override
    public void onPreChangeTab() {
    }

    @Override
    public void onPause() {
        Log.i(TAG, "onPause");
        super.onPause();
/*        if (stopWatchView() != null) {
            stopWatchView().onPause();
        }
        if (stopWatch() != null) {
            stopWatch().onPause();
        }
        if (stopWatchInterval() != null) {
            stopWatchInterval().onPause();
        }*/
        if (mService != null) {
            mService.recordStopWatchSP(mService.mCountTime);
            mService.recordStopWatchIntervalSP(mService.mCountIntervalTime);
        }
        mIsSelected = false;
        mHandler.removeCallbacksAndMessages(null);
        OplusTrack.onPause(getActivity());
    }

    @Override
    public void onResume() {
        Log.i(TAG, "onResume: mServiceUnbind: " + mServiceUnbind);
        super.onResume();
        /*if (stopWatchView() != null) {
            stopWatchView().onResume();
        }
        if (stopWatch() != null) {
            stopWatch().onResume();
        }
        if (stopWatchInterval() != null) {
            stopWatchInterval().onResume();
        }*/
        if (mService != null) {
            mData.clear();
            mData.addAll(mService.mData);
            updateList(mData);
            mStatus = mService.mStatus;
            recordStatus(mStatus);
        } else {
            if (mServiceUnbind) {
                bindStopWatchService();
            }
        }
        initViews(mStatus);
        if ((mStatus == STATUS_1) && mFocused) {
            StopWatchWakeLock.acquireCpuWakeLock(getActivity());
        }
        OplusTrack.onResume(getActivity());
        updateList();
    }

    private void updateList(List<Map<String, String>> data) {
        if ((mListAdapter != null) && (listView() != null)) {
            mListAdapter.update(data);
            mListAdapter.updateFooterHeight(false, false);
            boolean isVisible = mListAdapter.getList().size() > 0;
            if (stopWatchInterval() != null) {
                stopWatchInterval().setVisibility(isVisible ? View.VISIBLE : View.INVISIBLE);
            }
            setListTitleVisibility(isVisible);
            //平板分屏下，不触发表盘指针动画
            if (FoldScreenUtils.isRealOslo() && isInMultiWindowMode()) {
                return;
            }
            if (isHover()) {
                mAnimationManager.upDateCenter(!isVisible);
            } else {
                startDialTranslateAnimal(!isVisible);
            }
        }
    }

    @Override
    @SuppressLint("InlinedApi")
    public void onStop() {
        Log.i(TAG, "onStop");
        super.onStop();
        if (listView() != null) {
            listView().fling(0, 0);
        }
        StopWatchWakeLock.releaseCpuLock();
    }

    @Override
    public void onDestroy() {
        StopWatchWakeLock.releaseCpuLock();
        Log.v(TAG, "onDestroy: status = " + mStatus);
        if (mService != null) {
            mService.setDisplayTime(null);
            mService.setDisplayIntervalTime(null);
        }
        getActivity().unbindService(mServiceConnect);
        if (mStatus == STATUS_0) {
            boolean stop = getActivity()
                    .stopService(new Intent(getActivity(), StopWatchService.class));
            Log.i(TAG, "stopService()" + stop);
        }
        mService = null;
        mServiceUnbind = true;
        if (mHandlerAni != null) {
            mHandlerAni.removeCallbacksAndMessages(null);
        }
        unRegisterBroadcast();
        if (listView() != null && listView().getHandler() != null) {
            listView().getHandler().removeCallbacksAndMessages(null);
        }
        super.onDestroy();
    }

    private void unRegisterBroadcast() {
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLocalReceiver);
    }

    private void recordStatus(int status) {
        PrefUtils.putInt(mContext, SP_NAME, STOPWATCH_STATUS_PREFERENCE, status);
        Log.d(TAG, "recordStatus: " + status);
    }

    public int getStatus() {
        return PrefUtils.getInt(mContext, SP_NAME, STOPWATCH_STATUS_PREFERENCE, 0);
    }

    private void recordLastElapseTime(long time) {
        PrefUtils.putLong(mContext, SP_NAME, STOPWATCH_LAST_RECORD_TIME_PREFERENCE, time);
    }

    public long getLastElapseTime() {
        return PrefUtils.getLong(mContext, SP_NAME, STOPWATCH_LAST_RECORD_TIME_PREFERENCE, 0L);
    }

    public void start() {
        Log.i(TAG, "timer start");
        if (mData == null) {
            mData = new ArrayList<>();
        }
        mData.clear();
        StopWatchWakeLock.acquireCpuWakeLock(getActivity());
        if (mService != null) {
            mService.start();
        }
    }

    private void stopListScroll() {
        LocalColorRecyclerView listView = listView();
        if (listView != null) {
            long uptime = SystemClock.uptimeMillis();
            listView.dispatchTouchEvent(MotionEvent.obtain(uptime, uptime, MotionEvent.ACTION_CANCEL, 0, 0, 0));
        }
    }

    protected void scrollToBottom() {
        if (mStopWatchListManager == null) {
            if (mLinearLayoutManager != null) {
                mLinearLayoutManager.scrollToPosition(0);
            }
        } else {
            mStopWatchListManager.scrollToBottom();
        }
    }

    /**
     * 计次
     */
    @SuppressLint("NewApi")
    public void record() {
        if (mStopWatchListManager != null) {
            mStopWatchListManager.setEnable(false);
            mStopWatchListManager.setStatus(true);
        }
        stopListScroll();
        if ((mLinearMotorHelper != null) && mLinearMotorHelper.isSupportLinearmoto()) {
            mLinearMotorHelper.vibrateWeakShortOnce(false);
        } else {
            Utils.setVibrateRepeatNone(mVibrator, getContext(), mVibrationEffectMiddle, sVibratePattern);
        }
        if (stopWatchView() != null) {
            stopWatchView().record();
        }
        if (stopWatch() != null) {
            long elapseTime = stopWatch().getTime();
            Log.i(TAG, "timer record: elapseTime: " + elapseTime);
            recordLastElapseTime(elapseTime);
            if (mService != null) {
                Map<String, String> item = mService.record(elapseTime);
                mListAdapter.notifyItemRangeChanged(item);
                mService.repositionInterval();
                mService.startInterval();
            }
        }
        if (stopWatchInterval() != null) {
            stopWatchInterval().setVisibility(View.VISIBLE);
        }
        setListTitleVisibility(true);
        scrollToBottom();

        if (isHover()) {
            mAnimationManager.upDateCenter(false);
        } else {
            startDialTranslateAnimal(false);
        }
        mHandler.removeCallbacksAndMessages(null);
        mHandler.postDelayed(() -> mListAdapter.updateFooterHeight(false, false), NOTIFY_DATA_CHANGE_DELAY);
    }

    public void pause() {
        Log.i(TAG, "timer pause");
        StopWatchWakeLock.releaseCpuLock();
        if (mService != null) {
            mService.pause();
        }
    }

    public void toContinue() {
        StopWatchWakeLock.acquireCpuWakeLock(getActivity());
        if (mService != null) {
            mService.toContinue();
        }
    }

    public void rePosition() {
        AlarmClock alarmClock = (AlarmClock) getActivity();
        if (mStopWatchListManager != null && mListAdapter != null && mListAdapter.getList() != null) {
            stopPosition(alarmClock != null && alarmClock.mLayoutInfo == ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE);
        }
        StopWatchWakeLock.releaseCpuLock();
        if (mService != null) {
            mService.reposition();
        }
        mData.clear();
        if (stopWatchInterval() != null) {
            stopWatchInterval().setVisibility(View.INVISIBLE);
        }
        setListTitleVisibility(false);
        updateList(mData);
        recordLastElapseTime(0L);
        if (mService != null) {
            mService.resetElapseTime();
        }
    }

    public void displayTime(long time) {
        if (stopWatch() != null) {
            stopWatch().update(time);
        }
        if (stopWatchView() != null) {
            stopWatchView().update(time);
        }
        FlashBackHelper.getInstance().sendData(Constants.CLOCK_STOPWATCH_FLASHBACK_KEY,
                OplusStopWatchWrapper.INSTANCE.getFlashBackTimeStr(OplusStopWatch.LONG_FORMAT),
                getFlashBackContent(),
                (mStatus == STATUS_1) ? getResources().getString(R.string.text_timer_btn_pause) : getResources().getString(R.string.text_timer_btn_continue),
                (mStatus == STATUS_1) ? getResources().getColor(R.color.flash_back_bottom_left_red_color) : getResources().getColor(R.color.flash_back_bottom_left_green_color),
                getResources().getColor(R.color.flash_back_bottom_content_white_color),
                (mStatus == STATUS_1) ? getResources().getString(R.string.record) : getResources().getString(R.string.RePostion),
                (mStatus == STATUS_1) ? getResources().getColor(R.color.flash_back_bottom_right_green_color) : getResources().getColor(R.color.flash_back_bottom_right_gray_color),
                (mStatus == STATUS_1) ? getResources().getColor(R.color.flash_back_bottom_content_white_color) : getResources().getColor(R.color.flash_back_bottom_content_black_color),
                mFlashBackListener);

        if (((time - mCount) > mFrequency) && ((Utils.isTalkBackOpen()))) {
            mCount = time;
        }
    }

    public String getFlashBackContent() {

        if ((mService != null) && !TextUtils.isEmpty(mService.getLastRecordTitle())) {
            return getResources().getString(R.string.StopWatch_Title) + " | " + String.format(getString(R.string.count_nums), mService.getLastRecordTitle());
        }
        return getResources().getString(R.string.StopWatch_Title);
    }

    public void onStatusBarClicked() {
        if (listView() != null) {
            long time = SystemClock.uptimeMillis();
            MotionEvent event = MotionEvent.obtain(time, time, MotionEvent.ACTION_CANCEL, 0, 0, 0);
            listView().dispatchTouchEvent(event);
            event.recycle();
            if (listView().getChildCount() > 0) {
                listView().smoothScrollToPosition(0);
            }
            listView().startNestedScroll(ViewCompat.SCROLL_AXIS_VERTICAL);
        }
    }

    private void onCreateInit() {
        mContext = getContext();
        mStatus = getStatus();
        Log.i(TAG, "onCreate: " + mStatus);
        mHandlerAni = new HandlerAni(this);
        if (Utils.isHardwareLinermotorSupport(mContext)) {
            mVibrationEffectMiddle = (VibrationEffect) VibrationEffect.createOneShot(
                    ClockConstant.RAPID_MIDDLE_ONESHOT_TIME, ClockConstant.MIDDLE_AMPLITUDE);
        }
        mLinearMotorHelper = new LinearMotorHelper(mContext);
        registerBroadcast();
    }

    private void setListTitleVisibility(boolean isVisible) {
        int visibility = isVisible ? View.VISIBLE : View.INVISIBLE;
        if (listTitle() != null) {
            listTitle().setVisibility(visibility);
        }
        if (getRootView() != null) {
            View dividerLinde = getRootView().findViewById(R.id.divider_line);
            View dividerLindeLand = getRootView().findViewById(R.id.divider_line_land);
            if (getUiMode() == FoldScreenUtils.UiMode.LARGE_HORIZONTAL) {
                if (dividerLinde != null) {
                    dividerLinde.setVisibility(View.INVISIBLE);
                }
                if (dividerLindeLand != null) {
                    dividerLindeLand.setVisibility(visibility);
                }
            } else {
                if (dividerLinde != null) {
                    dividerLinde.setVisibility(visibility);
                }
                if (dividerLindeLand != null) {
                    dividerLindeLand.setVisibility(View.INVISIBLE);
                }
            }
        }
    }

    protected void initTopMargin(boolean isHover) {
        if (stopWatchCl() != null) {
            int paddingTop = Utils.getStatusBarHeight(mContext);
            stopWatchCl().setPadding(0, paddingTop, 0, 0);
        }
    }

    public void selectEd() {
        boolean isToCenter = false;
        mIsSelected = true;
        if (mListAdapter != null) {
            isToCenter = mListAdapter.getList().size() == 0;
        }
        setDialBg(true, isToCenter);
        if (Utils.isAboveOS14()) {
            if (mShadowManager == null) {
                mNotInitEffect = true;
            } else {
                clockEffectViewEnter();
            }
        }
    }

    private void clockEffectViewEnter() {
        if (mIsFirstEnter) {
            mIsFirstEnter = false;
        }
    }

    /**
     * 表盘平移动画
     *
     * @param isCenter 是否居中
     */
    protected void startDialTranslateAnimal(boolean isCenter) {
    }

    private void setDialBg(boolean selected) {
        setDialBg(selected, false);
    }

    private void setDialBg(boolean selected, boolean center) {
        if (selected && mAnimationNum <= 0) {
            mAnimationNum++;
            startShadowAnimation(center);
        }
    }

    protected void startShadowAnimation(boolean center) {
        if (mShadowAnimationManager != null) {
            mShadowAnimationManager.startAnimation(center, false);
        }
    }

    protected void initHoverIfNeed() {
    }

    /**
     * 更新列表
     */
    protected void updateList() {
    }

    protected void setButtonImage(COUITintImageView buttonCancel, COUITintImageView buttonCount, Boolean isCancel) {
        if (isCancel) {
            buttonCancel.setEnabled(false);
            buttonCount.setEnabled(true);
        } else {
            buttonCancel.setEnabled(true);
            buttonCount.setEnabled(false);
        }
    }

    /**
     * stop
     */
    protected void stopPosition(Boolean isHover) {
        mStopWatchListManager.stop(isHover);
    }

    /**
     * 是否处于悬停模式中
     *
     * @return 默认false，处于悬停中
     */
    protected boolean isHover() {
        return false;
    }

    /**
     * 初始化title字体大小以边距
     */
    protected abstract void initTitle();

    /**
     * 工具栏
     *
     * @return COUIToolbar
     */
    abstract protected COUIToolbar couiToolbar();

    /**
     * 小计View对象
     *
     * @return StopWatchTextSmallView
     */
    protected abstract StopWatchTextSmallView stopWatchInterval();

    /**
     * 秒表整体View对象
     *
     * @return ConstraintLayout
     */
    protected abstract ConstraintLayout stopWatchCl();

    /**
     * 秒表View对象
     *
     * @return StopWatchTextSmallView
     */
    protected abstract StopWatchTextView stopWatch();

    /**
     * stopWatchView对象
     *
     * @return StopWatchView
     */
    protected abstract StopWatchView stopWatchView();

    /**
     * 秒表开始按钮对象
     *
     * @return COUIFloatingButton
     */
    protected abstract COUIFloatingButton buttonStart();

    /**
     * 秒表结束按钮对象
     *
     * @return COUITintImageView
     */
    protected abstract COUITintImageView buttonCancel();

    /**
     * 秒表计次按钮对象
     *
     * @return COUITintImageView
     */
    protected abstract COUITintImageView buttonCount();

    /**
     * 秒表列表对象
     *
     * @return LocalColorRecyclerView
     */
    protected abstract LocalColorRecyclerView listView();

    /**
     * 秒表列表表头
     *
     * @return LocalColorRecyclerView
     */
    protected abstract FrameLayout listTitle();

    /**
     * 表盘大小
     *
     * @return 大小
     */
    abstract protected int clockSize();

    /**
     * 设置中间按钮图片
     *
     * @param context
     * @param isStart
     * @return
     */
    protected Drawable getMainFabDrawable(Context context, boolean isStart) {
        if (isStart) {
            return context.getDrawable(R.drawable.button_start);
        } else {
            return context.getDrawable(R.drawable.button_pause);
        }
    }
}
