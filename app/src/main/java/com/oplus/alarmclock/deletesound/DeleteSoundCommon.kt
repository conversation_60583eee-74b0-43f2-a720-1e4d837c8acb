/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - DeleteSoundCommon.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/07/13
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  HeWei  2020/07/13      1.0            DeleteSoundCommon.kt
 *  ***************************************************************
 */


package com.oplus.alarmclock.deletesound

import android.content.Context
import android.provider.Settings
import com.oplus.alarmclock.AlarmClockApplication

abstract class DeleteSoundCommon {

    companion object {
        const val DELETE_SOUND_NAME = "global_delete.ogg"
        const val DELETE_SOUND_FILE_PATH = "/system/media/audio/ui/global_delete.ogg"

        private const val IS_OPEN_SOUND_EFFECT = "global_delete_sound"
        private const val TURN = 1
        private const val OFF = 0
    }

    fun supportSoundEffect(): Boolean {
        val context: Context = com.oplus.alarmclock.AlarmClockApplication.getInstance()
        return Settings.Secure.getInt(context.contentResolver, IS_OPEN_SOUND_EFFECT, TURN) != OFF
    }


}