/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AlarmSoundPool.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/07/13
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  HeWei  2020/07/13      1.0            AlarmSoundPool.kt
 *  ***************************************************************
 */

package com.oplus.alarmclock.deletesound

import android.media.AudioAttributes
import android.media.SoundPool
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log

class AlarmSoundPool : DeleteSoundHandlerBase() {

    companion object {
        private const val TAG = "AlarmSoundPool"
    }

    override fun getPlayer(): IDeleteSoundPlayer {
        return AlarmSoundPoolDelegate()
    }

    private class AlarmSoundPoolDelegate : DeleteSoundCommon(), IDeleteSoundPlayer {

        private var mSoundPool: SoundPool? = null
        private var mPrepared = false
        private var mSoundId = 0
        private val mPriority = 1
        private val mVolume = 1f
        private val mLoop = 0
        private val mRate = 1f

        override fun initPlayer() {
            val poolBuilder = SoundPool.Builder()
            val attr = AudioAttributes.Builder().setLegacyStreamType(1).build()
            poolBuilder.setMaxStreams(1)
            poolBuilder.setAudioAttributes(attr)
            mSoundPool = poolBuilder.build()
            mSoundPool!!.setOnLoadCompleteListener { soundPool: SoundPool?, i: Int, i1: Int ->
                mPrepared = true
                Log.i(TAG, "onLoadComplete:sampleId:$i,status:$i1")
            }
            Log.i(TAG, "initPlayer")
        }

        override fun play() {
            if (!supportSoundEffect()) {
                Log.i(TAG, "system switch is off in setting")
                return
            }
            try {
                Log.i(
                    TAG,
                    "play with soundPool mSoundId:$mSoundId,mPrepared:$mPrepared"
                )
                if (prepared()) {
                    mSoundPool!!.play(mSoundId, mVolume, mVolume, mPriority, mLoop, mRate)
                }
            } catch (e: Exception) {
                Log.e(TAG, "play delete sound error:$e")
            }
        }

        override fun release() {
            mSoundPool?.release()
            mSoundPool = null
            mPrepared = false
            Log.i(TAG, "release")
        }

        override fun prepared(): Boolean {
            return (mSoundId != 0) && mPrepared
        }

        override fun loadResource() {
            if (mSoundPool == null) {
                initPlayer()
            }

            try {
                if (!prepared()) {
                    mSoundId = if (Utils.isAboveT()) {
                        mSoundPool!!.load(
                            AlarmClockApplication.getInstance(),
                            R.raw.global_delete,
                            mPriority
                        )
                    } else {
                        mSoundPool!!.load(DELETE_SOUND_FILE_PATH, mPriority)
                    }
                }
                Log.i(TAG, "loadResource,mSoundId:$mSoundId")
            } catch (e: Exception) {
                Log.e(TAG, "loadResource e:$e")
            }
        }
    }

}