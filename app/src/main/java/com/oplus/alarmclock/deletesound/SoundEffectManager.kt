/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - SoundEffectManager.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/07/13
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  HeWei  2020/07/13      1.0            SoundEffectManager.kt
 *  ***************************************************************
 */


package com.oplus.alarmclock.deletesound

import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.Utils

class SoundEffectManager {
    private var mPlayer: IDeleteSoundPlayer? = null

    companion object {
        private const val TAG = "SoundEffectManager"
        private var sInstance: SoundEffectManager = SoundEffectManager()

        @JvmStatic
        val instance: SoundEffectManager
            get() {
                return sInstance
            }
    }

    init {
        getPlayer()
    }

    private fun getPlayer() {
        if (mPlayer == null) {
            if (Utils.isAboveT()) {
                mPlayer = AlarmSoundPool()
                Log.i(TAG, "get player above T")
            } else {
                if (Utils.isAboveR()) {
                    mPlayer = AlarmMediaPlayer()
                    Log.i(TAG, "get player above R")
                } else {
                    mPlayer = AlarmSoundPool()
                    Log.i(TAG, "get player below Q")
                }
            }
        }
    }


    fun loadFile() {
        mPlayer!!.loadResource()
    }

    fun playSoundEffect() {
        mPlayer!!.loadResource()
        mPlayer!!.play()
    }

    fun release() {
        mPlayer!!.release()
    }


}