/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DeleteSoundHandlerBase.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/1/27
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/1/27     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.deletesound

import android.os.Handler
import android.os.HandlerThread
import android.os.Message

abstract class DeleteSoundHandlerBase : IDeleteSoundPlayer{

    companion object {
        private const val MSG_INIT_PLAYER = 1000;
        private const val MSG_PLAY = 1001;
        private const val MSG_LOAD_RESOURCE = 1002;
        private const val MSG_RELEASE = 1003;
    }
    //跑在 HandlerThread 线程
    private val mHandler : Handler
    private val mPlayerDelegate : IDeleteSoundPlayer

    init {
        mHandler = getNewHandler()
        mPlayerDelegate = getPlayer()
    }

    abstract fun getPlayer() : IDeleteSoundPlayer

    override fun initPlayer() {
        mHandler.sendEmptyMessage(MSG_INIT_PLAYER)
    }

    override fun play() {
        mHandler.sendEmptyMessage(MSG_PLAY)
    }

    override fun release() {
        mHandler.sendEmptyMessage(MSG_RELEASE)
    }

    override fun prepared(): Boolean {
        return mPlayerDelegate.prepared()
    }

    override fun loadResource() {
        mHandler.sendEmptyMessage(MSG_LOAD_RESOURCE)
    }

    private fun getNewHandler() : Handler {
        val handlerThread = HandlerThread("alarm_delete_sound_player")
        handlerThread.start()
        return object : Handler(handlerThread.looper) {
            override fun handleMessage(msg: Message) {
                when(msg.what) {
                    MSG_INIT_PLAYER -> mPlayerDelegate.initPlayer()
                    MSG_PLAY -> mPlayerDelegate.play()
                    MSG_LOAD_RESOURCE -> mPlayerDelegate.loadResource()
                    MSG_RELEASE -> mPlayerDelegate.release()
                }
            }
        }
    }


}