/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AlarmMediaPlayer.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/07/13
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  HeWei  2020/07/13      1.0            AlarmMediaPlayer.kt
 *  ***************************************************************
 */


package com.oplus.alarmclock.deletesound

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.media.MediaPlayer.OnPreparedListener
import android.net.Uri
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.PermissionUtils
import com.oplus.alarmclock.utils.SoundUriUtils

class AlarmMediaPlayer : DeleteSoundHandlerBase() {

    companion object {
        private const val TAG = "AlarmMediaPlayer"

    }
    override fun getPlayer(): IDeleteSoundPlayer {
        return AlarmMediaPlayerDelegate()
    }

    private class AlarmMediaPlayerDelegate : DeleteSoundCommon(), IDeleteSoundPlayer {

        private var mPrepare = false
        private var mPlayer: MediaPlayer? = null
        private var mCallback: PlayerActionListener? = null
        private var mContext: Context? = null

        override fun initPlayer() {
            mContext = AlarmClockApplication.getInstance()
            mPlayer = MediaPlayer()
            mCallback = PlayerActionListener()
            mPlayer?.apply {
                setOnCompletionListener(mCallback)
                setOnErrorListener(mCallback)
                setOnPreparedListener(mCallback)
                setAudioStreamType(AudioManager.STREAM_SYSTEM)
            }
        }

        override fun play() {
            if (!supportSoundEffect()) {
                Log.d(TAG, "system switch is off in setting and system sound is silence")
                return
            }
            if (prepared()) {
                mPlayer?.start()
            }
        }

        override fun release() {
            mPlayer?.release()
            mCallback = null
            mPlayer = null
        }

        override fun prepared(): Boolean {
            return mPrepare && PermissionUtils.hasStoragePermission()
        }

        override fun loadResource() {
            if (mPlayer == null) {
                initPlayer()
            }
            try {
                mPlayer?.reset()
                val uri = getSoundPath()
                Log.d(TAG, "loadResource uri:$uri")
                if (uri != null) {
                    mPlayer?.setDataSource(mContext!!, uri)
                    mPlayer?.prepare()
                    mPrepare = true
                }

            } catch (e: Exception) {
                Log.e(TAG, "loadResource e:$e")
            }
        }

        private fun getSoundPath(): Uri? {
            return SoundUriUtils.getSoundPath(DELETE_SOUND_NAME)
        }

        class PlayerActionListener : OnCompletionListener, MediaPlayer.OnErrorListener, OnPreparedListener {
            override fun onCompletion(mediaPlayer: MediaPlayer) {
                Log.i(TAG, "PlayerActionListener onCompletion")
            }

            override fun onError(mediaPlayer: MediaPlayer, i: Int, i1: Int): Boolean {
                Log.i(TAG, "PlayerActionListener onError:")
                return false
            }

            override fun onPrepared(mediaPlayer: MediaPlayer) {
                Log.i(TAG, "PlayerActionListener onPrepared:")
            }
        }
    }

}