/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : The City Object for the AlarmClock application.
 * <p>
 * History :( ID, Date, Author,
 * <p>
 * Description) v1.0, 2016-5-10, <PERSON>, create
 ************************************************************/

package com.oplus.alarmclock.globalclock;

public final class City {

    private String mName;   // the localized name to be display.
    private String mCountry;
    private String mTimezone;
    private int mRawId;     // _id in table for one record.
    private int mSortPos;   // sort position.
    private int mCityId;    // Id for one city with different locale names.

    //For backup and restore use.
    private int mFlag;
    private int mFlag2;
    //End.

    //TODO: Move these to subclass.
    private boolean mSelected;
    private int mDisplayPosition;
    //end.

    public String getName() {
        return mName;
    }

    public void setName(String name) {
        mName = name;
    }

    public String getCountry() {
        return mCountry;
    }

    public void setCountry(String country) {
        this.mCountry = country;
    }

    public String getTimezone() {
        return mTimezone;
    }

    public void setTimezone(String zoneId) {
        mTimezone = zoneId;
    }

    public int getRawId() {
        return mRawId;
    }

    public void setRawId(int rawId) {
        mRawId = rawId;
    }

    public int getCityId() {
        return mCityId;
    }

    public void setCityId(int cityId) {
        mCityId = cityId;
    }

    public int getSortPos() {
        return mSortPos;
    }

    public void setSortPos(int sortPos) {
        mSortPos = sortPos;
    }

    public int getFlag() {
        return mFlag;
    }

    public void setFlag(int flag) {
        mFlag = flag;
    }

    public int getFlag2() {
        return mFlag2;
    }

    public void setFlag2(int flag2) {
        mFlag2 = flag2;
    }

    public boolean isSelected() {
        return mSelected;
    }

    public void setSelected(boolean selected) {
        mSelected = selected;
    }

    @Override
    public boolean equals(Object other) {
        return (other != null) && (other instanceof City) && (mRawId == ((City) other).mRawId);
    }

    @Override
    public int hashCode() {
        // 根据Effectiv Java里,int类型生成hashcode的方法得到此方法。
        int result = 17;
        int random = 31;
        result = random * result + mRawId;
        return result;
    }

    @Override
    public String toString() {
        return "City: [" + mRawId + "]: CityId: " + mCityId + ", CityName: " + mName
                + ", timezone: " + mTimezone + ", sortpos: " + mSortPos;
    }

    public int getDisplayPosition() {
        return mDisplayPosition;
    }

    public void setDisplayPosition(int displayPosition) {
        this.mDisplayPosition = displayPosition;
    }
}
