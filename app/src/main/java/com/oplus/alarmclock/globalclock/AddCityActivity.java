/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUSOS_EDIT
 ** File: - ColorSearchViewBelowToolbarActivity.java
 ** Description: Implemented searchView below toolbar
 ** Version: 1.0
 ** Date: 2019/1/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>         <date>       <version>    <desc>
 ** <EMAIL>  2019/2/22    1.1          Fix the bug of shaking toolbar title
 **                                            when search animator begins
 ***************************************************************/
package com.oplus.alarmclock.globalclock;

import static com.oplus.alarmclock.alarmclock.AlarmSettingActivity.TO_SETTING_CODE;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;

public class AddCityActivity extends BaseActivity {

    public static final String IS_SHOW_PANEL = "is_show_panel";
    public static final String IS_FROM_WORD_TIME_PLUG = "is_from_word_time_plug";
    public static final String IS_FROM_WORD_CLOCK_OR_WIDGET = "is_from_word_clock_or_widget";
    private static final String TAG = "AddCityActivity";
    public static boolean sIsShowing;
    private COUIBottomSheetDialogFragment mColorBottomSheetDialogFragment;
    private AddCityFragment mAddCityFragment;
    private AddCityPanelFragment mAddCityPanelFragment;
    private boolean mIsFromScreen;
    private boolean mIsShowPanel;
    private boolean mIsFromDialClock;
    private AddCityBottomSheetDialog mAddCityDialog;
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                Log.i(TAG, "Receiver ACTION_SCREEN_OFF.");
                getWindow().setFlags(0, WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Intent data = getIntent();
        if (data != null) {
            mIsShowPanel = data.getBooleanExtra(IS_SHOW_PANEL, false);
            mIsFromDialClock = data.getBooleanExtra(AddCityFragment.FROM_DIAL_CLOCK_KEY, false);
        }
        if (savedInstanceState != null) {
            mIsShowPanel = savedInstanceState.getBoolean(IS_SHOW_PANEL, false);
            Log.d(TAG, "savedInstanceState mIsShowPanel=" + mIsShowPanel);
        }
        if (!mIsShowPanel && FlexibleWindowUtils.isSupportFlexibleActivity()
                && FlexibleWindowUtils.isFlexibleActivitySuitable(getResources().getConfiguration())) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                setTranslucent(false);
            }
        }
        setActivityTheme(mIsShowPanel);
        super.onCreate(savedInstanceState);
        if (mIsShowPanel) {
            if (isTabletMode() || isMidMode()) {
                getWindow().setNavigationBarColor(Color.parseColor("#01ffffff"));
            } else {
                getWindow().setNavigationBarColor(Color.parseColor("#01FAFAFA"));
            }
        } else {
            if (COUIDarkModeUtil.isNightMode(this)) {
                getWindow().setNavigationBarColor(Color.parseColor("#E6ffffff"));
            } else {
                getWindow().setNavigationBarColor(Color.parseColor("#F5F5F5"));
            }
        }
        setSupportActionBar(null);
        registerScreenOffReceiver();


        if (mIsShowPanel) {
            Log.i(TAG, "showPanel,mIsFromDialClock:" + mIsFromDialClock);
            if (mIsFromDialClock) {
                showPanel();
            } else {
                showDialog();
            }
        } else {
            showFullPage();
        }
        //设置跳转城市选择页面如果是跟手面板内需监听点击
        if (getResources() != null && FlexibleWindowUtils.isOpenInFlexibleActivity(getResources().getConfiguration())) {
            setFinishOnTouchOutside(false);
            getWindow().getDecorView().setOnTouchListener((view, motionEvent) -> {
                boolean isOutOf = Utils.isOutOfBounds(AddCityActivity.this, motionEvent);
                if (motionEvent.getAction() == MotionEvent.ACTION_UP
                        || motionEvent.getAction() == MotionEvent.ACTION_CANCEL) {
                    if (isOutOf) {
                        Intent intent = new Intent();
                        setResult(TO_SETTING_CODE, intent);
                        finish();
                    }
                }
                return false;
            });
        }
    }

    private void registerScreenOffReceiver() {
        Intent intent = getIntent();
        if (intent != null) {
            mIsFromScreen = intent.getBooleanExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, false);
            if (mIsFromScreen) {
                final IntentFilter intentFilter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
                registerReceiver(mReceiver, intentFilter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
                getWindow().setFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED,
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
    }

    private void unregisterScreenOffReceiver() {
        if (mIsFromScreen) {
            unregisterReceiver(mReceiver);
        }
    }

    void setActivityTheme(boolean showPanel) {
        Log.i(TAG, "setActivityTheme,showPanel:" + showPanel);
        setTheme(showPanel ? R.style.AppNoTitleThemeTranslucent : R.style.AppNoTitleTheme2);
    }

    void showFullPage() {
        Log.i(TAG, "showFullPage");
        setContentView(R.layout.fragment_add_city_panel);
        mAddCityFragment = new AddCityFragment();
        getSupportFragmentManager().beginTransaction().replace(R.id.container, mAddCityFragment).commit();
    }

    void showPanel() {
        getWindow().getDecorView().post(() -> {
            mColorBottomSheetDialogFragment = new AddCityBottomSheetFragment();
            mAddCityPanelFragment = new AddCityPanelFragment();
            mColorBottomSheetDialogFragment.setMainPanelFragment(mAddCityPanelFragment);
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
            transaction.remove(mColorBottomSheetDialogFragment);
            transaction.add(mColorBottomSheetDialogFragment, "add_city_panel").commitNowAllowingStateLoss();
        });
    }

    void showDialog() {
        if (mAddCityDialog == null) {
            mAddCityDialog = new AddCityBottomSheetDialog(this, obtainUiMode(), 0, AlarmClock.sStartFromScreen);
            mAddCityDialog.setOnShowListener(dialog ->
                    mAddCityDialog.setNavColor(ContextCompat.getColor(AddCityActivity.this, R.color.coui_color_background_elevatedWithCard))
            );
            mAddCityDialog.setOnDismissListener(dialog -> {
                finish();
            });
        }
        mAddCityDialog.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Intent data = getIntent();
        if (data != null) {
            sIsShowing = data.getBooleanExtra(IS_FROM_WORD_TIME_PLUG, false);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        sIsShowing = false;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }

    @Override
    public void finish() {
        super.finish();
        if (mIsShowPanel) {
            overridePendingTransition(0, 0);
        } else {
            overridePendingTransition(R.anim.coui_zoom_fade_enter,
                    R.anim.coui_push_down_exit);
        }
    }

    public void finish(boolean isFinishPanel) {
        if (isFinishPanel && (mColorBottomSheetDialogFragment != null)) {
            mColorBottomSheetDialogFragment.dismiss();
        } else {
            finish();
        }
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        Log.d(TAG, "onSaveInstanceState mIsShowPanel=" + mIsShowPanel);
        outState.putBoolean(IS_SHOW_PANEL, mIsShowPanel);
    }

    @Override
    protected void onDestroy() {
        if (mAddCityDialog != null && mAddCityDialog.isShowing()) {
            mAddCityDialog.dismiss();
            mAddCityDialog = null;
        }
        super.onDestroy();
        Log.d(TAG, "onDestroy showPanel");
        unregisterScreenOffReceiver();
        if (mColorBottomSheetDialogFragment != null) {
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
            transaction.remove(mColorBottomSheetDialogFragment).commitNowAllowingStateLoss();
            mColorBottomSheetDialogFragment = null;
        }
    }

    @Override
    public void onStatusBarClicked() {
        if (mAddCityFragment != null) {
            mAddCityFragment.onStatusBarClicked();
        } else if (mAddCityPanelFragment != null) {
            mAddCityPanelFragment.onStatusBarClicked();
        }
    }

    @Override
    protected boolean isRegisterStatusBar() {
        return true;
    }
}
