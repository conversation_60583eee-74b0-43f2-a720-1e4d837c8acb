/*
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: GlobalTime2.java
 * ModuleName: GlobalTime Author: MaCong Create Date: Description: the main activity
 * <p>
 * History: <version > <time> <author> <desc>
 *     1.0 2010-9-24 MaCong CheckList
 *     1.1 2018-8-22 <PERSON><PERSON><PERSON> recode
 */
// OPLUS Java File Skip Rule:LineLength
package com.oplus.alarmclock.globalclock;

import static android.content.Context.RECEIVER_EXPORTED;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.databinding.ViewDataBinding;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.base.BaseUiModeFragment;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.EditMenuClickUtils;
import com.oplus.alarmclock.view.modelview.ISaveInstanceState;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.ToastManager;
import com.coloros.alarmclock.widget.WidgetUtils;
import com.oplus.statistics.OplusTrack;
import com.oplus.utils.ActivityUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import androidx.annotation.Nullable;

public abstract class WorldClockBaseFragment<T extends ViewDataBinding> extends BaseUiModeFragment<T> implements AlarmClock.IFragmentFocused, ISaveInstanceState {


    public static final int MAX_CITY_NUM = 30; // the max cities numbers
    public static final String KEY_HAVE_ADD_CITY_PANEL = "have_add_city_panel";
    protected static final String TRANSITION_ANIMATION = "transition";
    protected boolean mTimeZoneChange = false;
    protected AddCityBottomSheetDialog mAddCityDialog;
    private static final String TAG = "WorldClockBaseFragment";
    private ContentObserver mCitiesContentObserver;
    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();

    private final BroadcastReceiver mIntentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "Receive Action: " + action);
            mTimeZoneChange = Intent.ACTION_TIMEZONE_CHANGED.equals(action) || Intent.ACTION_TIME_CHANGED.equals(action);
            onSystemTimeChanged();
        }
    };

    private BroadcastReceiver mLocalBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "LocalBroadcastReceiver Action: " + action);
            if (WidgetUtils.ACTION_DELETE_WIDGET.equals(action) || WidgetUtils.ACTION_DELETE_WIDGET_OLD.equals(action)) {
                updateMenu();
            } else if (ClockConstant.WORLD_CLOCK_DATA_CHANGED.equals(action)) {
                final Activity activity = getActivity();
                if ((activity != null) && (!activity.isFinishing())) {
                    boolean isNotNeedUpdateCityList = intent.getBooleanExtra(ClockConstant.IS_NOT_NEED_UPDATE_CITY_LIST, false);
                    asyncLoadCities(WorldClockBaseFragment.this, isNotNeedUpdateCityList);
                }
            }
        }
    };


    private static class CitiesContentObserver extends ContentObserver {

        WeakReference<WorldClockBaseFragment> mFragmentWeakReference;

        public CitiesContentObserver(Handler handler, WorldClockBaseFragment fragment) {
            super(handler);
            mFragmentWeakReference = new WeakReference<>(fragment);
        }

        @Override
        public void onChange(boolean b) {
            super.onChange(b);
            Log.d(TAG, "---onChange: " + b);
            WorldClockBaseFragment fragment = mFragmentWeakReference.get();
            if ((fragment != null) && (fragment.getActivity() != null)
                    && (!fragment.getActivity().isFinishing())) {
                int cityListSize = CityUtils.getCurrentCitiesCount(fragment.mContext);
                Log.d(TAG, "onChange: citiesDBSize = " + cityListSize + " CurrentCitiesCount = " + fragment.getCurrentCitiesCount());
                if (cityListSize != -1 && cityListSize != fragment.getCurrentCitiesCount()) {
                    asyncLoadCities(fragment, false);
                }
            }
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mCitiesContentObserver = new CitiesContentObserver(new Handler(), this);
        setHasOptionsMenu(true);
        addDefaultCityIfNeeded();
        registerReceiver();
        registerContentObserver();
        registerLocalBroadcastReceiver();
    }

    private void addDefaultCityIfNeeded() {
        final Context context = getActivity();
        final boolean hasInitDefaultCity = PrefUtils.hasInitDefaultCity(context);
        Log.d(TAG, "addDefaultCityIfNeeded: hasInitDefaultCity: " + hasInitDefaultCity);
        if (!hasInitDefaultCity && (CityUtils.getAllCities(context).size() < MAX_CITY_NUM)) {
            CityUtils.addDefaultCity(context);
            CityUtils.decideShowChina(context);
            boolean hasAddWorldClockWidget = WidgetUtils.hasAlreadyAddedWorldClockWidget(context);
            Log.d(TAG, "hasAddWorldClockWidget: " + hasAddWorldClockWidget);
            if (hasAddWorldClockWidget) {
                WidgetUtils.notifyUpdateWidgetCityList(context);
            }
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        filter.addAction(Intent.ACTION_TIME_TICK);
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);

        if (getActivity() != null) {
            getActivity().registerReceiver(mIntentReceiver, filter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
        }
    }

    protected boolean refreshUiImmediatelyForSort() {
        return false;
    }

    private void registerLocalBroadcastReceiver() {
        final IntentFilter filter = new IntentFilter();
        filter.addAction(WidgetUtils.ACTION_DELETE_WIDGET);
        filter.addAction(WidgetUtils.ACTION_DELETE_WIDGET_OLD);
        if (refreshUiImmediatelyForSort()) {
            filter.addAction(ClockConstant.WORLD_CLOCK_DATA_CHANGED);
        }
        if (getActivity() != null) {
            LocalBroadcastManager.getInstance(this.getActivity()).registerReceiver(
                    mLocalBroadcastReceiver, filter);
        }
    }

    private void registerContentObserver() {
        ContentResolver cr = getContext().getContentResolver();
        cr.registerContentObserver(ClockContract.City.NEW_CITY_CONTENT_URI, true, mCitiesContentObserver);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (isListAnimating()) {
            Log.d(TAG, "onOptionsItemSelected list is animating");
            return true;
        }
        int itemId = item.getItemId();
        if (itemId == R.id.edit) {
            Log.d(TAG, "onOptionsItemSelected clicked edit");
            if (mDoubleClickHelper.canClick() && EditMenuClickUtils.Companion.canClickAfterTabSwitch()) {
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_GLOBAL_DELETE_MENU);
                onEditMenuSelected();
            }
        } else if (itemId == R.id.settings) {
            Log.d(TAG, "onOptionsItemSelected clicked settings");
            if (mDoubleClickHelper.canClick()) {
                startToSetting();
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.SETTING_FROM_GLOBAL_CLOCK);
            }
        } else if (itemId == android.R.id.home) {
            Log.d(TAG, "onOptionsItemSelected clicked cancel");
            onEditCancel();
        } else if (itemId == R.id.cancel_select) {
            onEditCancel();
        } else if (itemId == R.id.select_all_clock) {
            if (mDoubleClickHelper.canClick()) {
                onEditSelectedAll();
            }
        }

        return true;
    }

    @Override
    public void initData() {
        super.initData();
        asyncLoadCities(this, false);
    }

    //Subclasss
    public abstract int getCurrentCitiesCount();

    //Subclasss
    protected void onCitiesLoadFinished(Context context, ArrayList<City> list) {

    }

    //Subclass
    protected void onEditMenuSelected() {

    }

    //Subclass
    protected void onAddMenuSelected() {

    }

    //Subclass
    protected void onSystemTimeChanged() {

    }

    public void onEditSelectedAll() {

    }

    protected void onEditCancel() {

    }

    @Override
    public void onResume() {
        super.onResume();
        OplusTrack.onResume(getActivity());
        updateMenu();
    }

    @Override
    public void onPause() {
        super.onPause();
        OplusTrack.onPause(getActivity());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAddCityDialog != null && mAddCityDialog.isShowing()) {
            mAddCityDialog.dismiss();
            mAddCityDialog = null;
        }
        unregisterReceiver();
        unregisterContentObserver();
        unregisterLocalBroadcastReceiver();
    }

    private void unregisterReceiver() {
        if (getActivity() != null) {
            getActivity().unregisterReceiver(mIntentReceiver);
        }
    }

    private void unregisterContentObserver() {
        if (getContext() != null) {
            ContentResolver cr = getContext().getContentResolver();
            cr.unregisterContentObserver(mCitiesContentObserver);
        }
    }

    private void unregisterLocalBroadcastReceiver() {
        if (getActivity() != null) {
            LocalBroadcastManager.getInstance(this.getActivity()).unregisterReceiver(
                    mLocalBroadcastReceiver);
            mLocalBroadcastReceiver = null;
        }
    }

    public void onFABClicked() {
        final int count = getCurrentCitiesCount();
        if (count < MAX_CITY_NUM) {
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_GLOBAL_ADD_MENU);
            addCity();
        } else {
            Log.d(TAG, "onOptionsItemSelected  add world city reach upper limmit, toast tips user");
            ToastManager.showToast(getString(R.string.add_world_clock_limit), Toast.LENGTH_SHORT);
        }
    }

    public void addCity() {
        if (getActivity() == null) {
            return;
        }
        ActivityUtils.increaseFrequency();
        if (mAddCityDialog == null) {
            mAddCityDialog = new AddCityBottomSheetDialog(new WeakReference<>(getActivity()).get(), getUiMode(), getCurrentCitiesCount(), AlarmClock.sStartFromScreen);
        }
        mAddCityDialog.show();
        mAddCityDialog.setOnDismissListener(dialog -> {
            mAddCityDialog = null;
        });
    }

    @Nullable
    @Override
    public Bundle getBundleWhenSaveInstanceState() {
        Bundle bundle = null;
        if ((mAddCityDialog != null) && mAddCityDialog.isShowing()) {
            bundle = new Bundle();
            bundle.putBoolean(KEY_HAVE_ADD_CITY_PANEL, true);
        }
        return bundle;
    }

    void updateMenu() {
        AlarmClock.correctAllMenuItemFromFragment(getActivity());
    }

    //----------------------------------
    private static class AsyncLoadTask extends AsyncTask<Void, Void, ArrayList<City>> {
        private WeakReference<WorldClockBaseFragment> mFragment;
        private boolean mIsNotNeedUpdateCityList;

        AsyncLoadTask(WorldClockBaseFragment fragment, boolean isNotNeedUpdateCityList) {
            super();
            mIsNotNeedUpdateCityList = isNotNeedUpdateCityList;
            mFragment = new WeakReference<>(fragment);
        }

        @Override
        protected ArrayList<City> doInBackground(Void... parameters) {
            WorldClockBaseFragment fragment = mFragment.get();
            if ((fragment != null) && (fragment.isAdded())) {
                return CityUtils.getAllCities(fragment.getActivity().getApplicationContext());
            }
            return null;
        }

        @Override
        protected void onPostExecute(ArrayList<City> list) {
            super.onPostExecute(list);
            WorldClockBaseFragment fragment = mFragment.get();
            if (fragment != null) {
                Log.d(TAG, "AsyncLoadTask: isAdded: " + fragment.isAdded()
                        + ", isRemoving: " + fragment.isRemoving()
                        + ", isDetached: " + fragment.isDetached());
                if ((fragment.isAdded()) && (!fragment.isRemoving()) && (!fragment.isDetached())) {
                    final Context context = fragment.getActivity().getApplicationContext();
                    int prevCount = fragment.getCurrentCitiesCount();
                    int curCount = (list != null) ? list.size() : 0;
                    WidgetUtils.asyncRecordCitiesState(context, prevCount, curCount);
                    if (!mIsNotNeedUpdateCityList) {
                        fragment.onCitiesLoadFinished(context, list);
                    }
                }
            }
        }
    }

    static void asyncLoadCities(WorldClockBaseFragment fragment, boolean isNotNeedUpdateCityList) {
        final AsyncLoadTask loadTask = new AsyncLoadTask(fragment, isNotNeedUpdateCityList);
        loadTask.execute();
    }

    public static float getDesiredWidth(String text, float size) {
        if (TextUtils.isEmpty(text)) {
            return 0f;
        }
        TextPaint textPaint = new TextPaint();
        textPaint.setTextSize(size);
        return StaticLayout.getDesiredWidth(text, textPaint);
    }

    protected boolean isListAnimating() {
        return false;
    }
}
