/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - WorldClockMidFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.content.Context
import android.content.res.Configuration
import android.transition.AutoTransition
import android.transition.TransitionManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.WorldClockViewMidLayoutBinding
import com.oplus.alarmclock.globalclock.view.ScrollGridLayoutManager
import com.oplus.alarmclock.utils.CityAdapterSpaceItemDecoration
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.dial.ShadowManager
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager
import com.oplus.clock.common.utils.Log

class WorldClockMidFragment : WorldClockViewFragment<WorldClockViewMidLayoutBinding>() {

    companion object {
        private const val TAG = "WorldClockMidFragment"
    }

    private var cityListItemDec: CityAdapterSpaceItemDecoration? = null
    private var isHover = false
    private var mTimerTopMargin = 0


    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        initManager()
        mViewBinding?.apply {
            ViewCompat.setAccessibilityDelegate(clickView, mTalkBackListener)
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
            worldClockToolbarInclude.toolbar.setPopupWindowOnDismissListener {
                popupWindowOnDismiss()
            }
        }
    }

    override fun layoutId(): Int {
        return R.layout.world_clock_view_mid_layout
    }

    override fun initTopMargin(isHover: Boolean) {
        val worldClockCl = worldClockCl()
        if (context != null && worldClockCl != null) {
            if (isHover) {
                worldClockCl.setPadding(0, 0, 0, 0)
            } else {
                val bottomMargin = resources.getDimensionPixelSize(R.dimen.clock_dial_top_margin)
                worldClockCl.setPadding(0, (Utils.getStatusBarHeight(context)) - bottomMargin, 0, 0)
            }
        }
    }

    private fun initManager() {
        initTimeSize(true)
        mViewBinding?.worldClockInclude?.dialClockCl?.let {
            it.viewTreeObserver.addOnGlobalLayoutListener(
                    object : ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            it.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            val topMargin: Int = getTopDistance()
                            val height = resources.getDimensionPixelSize(R.dimen.layout_dp_330)
                            val margin = resources.getDimensionPixelSize(R.dimen.layout_dp_52)
                            val marginOffset = resources.getDimensionPixelSize(R.dimen.layout_dp_12)

                            mViewBinding?.clickView?.apply {
                                val params = layoutParams as ViewGroup.MarginLayoutParams
                                params.height = height - margin
                                layoutParams = params
                            }

                            val paddingHeight = height + marginOffset - topMargin
                            if ((topMargin > 0) && (paddingHeight > 0)) {
                                mViewBinding?.worldClockList?.apply {
                                    val params = layoutParams as ViewGroup.MarginLayoutParams
                                    params.topMargin = topMargin
                                    layoutParams = params
                                    setPadding(0, paddingHeight, 0, 0)
                                    scrollBy(0, -paddingHeight)
                                    mCityListTopMargin = topMargin
                                    mPaddingHeight = paddingHeight
                                    Log.d(TAG, "mCityList topMargin:$topMargin,PaddingTop:$paddingHeight")
                                    initListManager(height + marginOffset, paddingHeight, margin)
                                    initTimeSize(false)
                                    mViewBinding?.worldClockInclude?.dialWordTimeTv?.center()
                                    mListAdapter?.notifyDataSetChanged()
                                }
                            }
                        }
                    })
        }
    }

    private fun initListManager(height: Int, scrollDistance: Int, topMargin: Int) {
        if (mWorldClockListManager == null) {
            context?.let { mWorldClockListManager = WorldClockListManager(it) }
        }
        mViewBinding?.run {
            mWorldClockListManager.initManager(
                    null,
                    mViewBinding?.worldClockInclude,
                    mClockManager,
                    Triple(height, scrollDistance, topMargin),
                    worldClockCl,
                    worldClockList,
                    clickView, clickView, false)
        }
    }

    /**
     * 获取顶部的高度
     */
    private fun getTopDistance(): Int {
        return mViewBinding?.worldClockInclude?.worldClockDivider?.let {
            val location = IntArray(2)
            it.getLocationInWindow(location)
            location[1] + it.height - mViewBinding!!.worldClockCl.paddingTop
        } ?: 0
    }

    override fun onAfterMove() {
        super.onAfterMove()
        listViewBacktoTop()
    }

    override fun updateCityListMargin() {
        super.updateCityListMargin()
        mViewBinding?.worldClockList?.apply {
            val paddingTop = paddingTop
            val params = layoutParams as ViewGroup.MarginLayoutParams
            val topMargin = params.topMargin
            if (paddingTop > 0 && topMargin > 0) {
                Log.d(TAG, "updateList mCityList paddingTop:$paddingTop,topMargin:$topMargin")
                return
            }
            (activity as? AlarmClock)?.apply {
                if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE != mLayoutInfo) {
                    initManager()
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged")
        if (FoldScreenUtils.isRealOslo()) {
            initData()
        }
    }
    /**
     * 初始化表盘
     */
    override fun initDialClock() {
        super.initDialClock()
        mViewBinding?.clickListener = this@WorldClockMidFragment
        mClockManager = WorldClockAnimationManager()
        mShadowManager = ShadowManager()
        mViewBinding?.worldClockInclude?.run {
            dialWordTimeTv.setUiMode(uiMode)
            clickListener = this@WorldClockMidFragment
            mClockManager.initDialClock(
                    dialClockRl,
                    dialMsgTv,
                    dialWordTimeTv,
                    dialWordMsgTv,
                    mViewBinding!!.worldClockList
            )
            context?.let {
                val fontScale: Float = it.resources.configuration.fontScale
                Utils.setSuitableFontSize(dialWordMsgTv, fontScale, COUIChangeTextUtil.G3)
                Utils.setSuitableFontSize(dialMsgTv, fontScale, COUIChangeTextUtil.G3)
            }
            mShadowManager.init(dialClockBigTable, dialClockBigGlowTable, dialClockHour, dialClockMinute, dialClockSecond, dialClockBg, mClockManager)
            mAlarmDialClockManager.init(dialClockBigGlowTable, dialClockSecond, dialClockHour, dialClockMinute, dialWordTimeTv)
        }
    }


    private fun initTimeSize(isTop: Boolean) {
        mViewBinding?.worldClockInclude?.dialWordTimeTv?.apply {
            val id = if (isTop) R.dimen.text_size_sp_40 else R.dimen.text_size_sp_58
            val size = resources.getDimension(id)
            setTextSize(size)
        }
    }

    /**
     * 直接进入悬停模式
     */
    override fun initHoverIfNeed() {
        (activity as? AlarmClock)?.apply {
            if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                mViewBinding?.worldClockList?.postDelayed({
                    onHoverPostureChanged(true)
                }, FoldScreenUtils.HOVER_DELAY)
            }
        }
    }

    override fun setListAdapterProp() {
        mListAdapter?.apply {
            isNeedAddFooter(true)
            setIsGeneralScreen(true)
            setIsInMultiWindowMode(isInMultiWindowMode)
        }
    }

    /**
     * 更新表盘位置
     */
    override fun updateTimeDialView(list: ArrayList<City>?) {
        super.updateTimeDialView(list)
        mViewBinding?.apply {
            val isToCenter = list.isNullOrEmpty()
            mShadowManager?.startMoveScale(worldClockInclude.dialClockCl, isToCenter)
        }
    }

    override fun flexibleScenario() {
        super.flexibleScenario()
        context?.let {
            if (FoldScreenUtils.isFlexibleScenario(it) && isHover) {
                handleNormal()
            }
        }
    }

    override fun changeList(mode: Int) {
        mViewBinding?.apply {
            if (mode == MODEL_EDIT) {
                worldClockInclude.dialClockCl.visibility = View.GONE
                worldClockList.visibility = View.GONE
                worldClockListEdit.visibility = View.VISIBLE
                setListCanScroll(false)
                mWorldClockListManager?.setEnable(false)
                mWorldClockListManager?.setModeEdit(true)
                val manager = worldClockListEdit.layoutManager as LinearLayoutManager
                mListAdapter?.let { manager.scrollToPosition(it.longClickPosition) }
            } else {
                worldClockInclude.dialClockCl.visibility = View.VISIBLE
                worldClockList.visibility = View.VISIBLE
                worldClockListEdit.visibility = View.GONE
                mWorldClockListManager?.setEnable(true)
                setListCanScroll(true)
                (activity as? AlarmClock)?.apply {
                    if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                        worldClockList.postDelayed({
                            onHoverPostureChanged(true)
                        }, HOVER_DELAY)
                    } else {
                        worldClockList.postDelayed({
                            onHoverPostureChanged(false)
                        }, HOVER_DELAY)
                    }
                }
                if (isHover) {
                    handleHover()
                } else {
                    if (mTimerTopMargin != 0) {
                        handleNormal()
                    }
                    mWorldClockListManager?.setModeEdit(false)
                }
            }
        }
    }

    override fun onScreenOrientationChanged(orientation: Int) {
        super.onScreenOrientationChanged(orientation)
        if (FoldScreenUtils.isRealOslo()) {
            initData()
        }
    }
    private fun setListCanScroll(isCanScroll: Boolean) {
        mLayoutManager?.setCanScrollVertically(isCanScroll)
        mGridLayoutManager?.setCanScrollVertically(isCanScroll)
    }

    override fun onHoverPostureChanged(hover: Boolean) {
        super.onHoverPostureChanged(hover)
        Log.d(TAG, "onHoverPostureChanged:$isHover; after:$hover")
        if (mViewBinding == null || mClockManager == null || isHover == hover) {
            Log.e(TAG, "onHoverPostureChanged failed or hover is same!")
            return
        }
        if (activity != null && !requireActivity().isFinishing) {
            isHover = hover
            mClockManager?.setIsHover(hover)
            if (hover) {
                handleHover()
            } else {
                handleNormal()
            }
        }
    }

    /**
     * 设置布局管理器
     */
    override fun setRecyclerViewLayoutManager(listView: COUIRecyclerView?, isEdit: Boolean, context: Context?) {
        val gridLayoutManager = ScrollGridLayoutManager(mContext,
                2, LinearLayoutManager.VERTICAL, false)
        mViewBinding?.worldClockList?.layoutManager = gridLayoutManager
    }

    /**
     * 设置编辑模式列表布局管理器
     */
    override fun setEditRecyclerViewLayoutManager(context: Context?) {
        val gridLayoutManager = ScrollGridLayoutManager(mContext,
                2, LinearLayoutManager.VERTICAL, false)
        mViewBinding?.worldClockListEdit?.layoutManager = gridLayoutManager
    }

    private fun handleHover() {
        if (isEditMode) {
            return
        }
        mViewBinding?.apply {
            //停止未执行完的悬停过渡动画
            TransitionManager.endTransitions(worldClockCl)
            val listSize = mListAdapter?.list?.size ?: 0
            worldClockInclude.dialWordTimeTv.visibility = View.INVISIBLE
            val ac = AutoTransition()
            ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
            //差值器
            ac.interpolator = COUIEaseInterpolator()
            //切换布局
            val set = ConstraintSet()
            set.clone(mContext, R.layout.world_clock_view_layout_hover)
            TransitionManager.beginDelayedTransition(worldClockCl, ac)
            set.applyTo(worldClockCl)
            //滚动到顶部
            mWorldClockListManager.listScroll(0)
            //取消联动
            mWorldClockListManager.setEnable(false)
            //取消自动滚动
            mWorldClockListManager.setAutoScroll(false)
            //切换为文字模式
            mClockManager?.changeTextModel()
            //显示数字
            worldClockInclude.dialWordTimeTv.visibility = View.VISIBLE
            //设置列表间距
            mViewBinding?.worldClockList?.apply {
                setPadding(0, 0, 0, 0)
                adapter?.notifyDataSetChanged()
            }
            //设置表盘大小
            if (listSize == 0 || mClockManager?.mIsText == true) {
                mShadowManager?.moveToScale(worldClockInclude.dialClockCl, true)
            }
            if (mClockManager?.mIsText != true) {
                worldClockList.postDelayed({
                    mShadowManager.moveToScale(
                            worldClockInclude.dialClockCl,
                            true
                    )
                }, FoldScreenUtils.DIAL_SCALE_DELAY.toLong())
            }
            /*悬停模式切换为数字模式，数字放大*/
            val size = mContext.resources.getDimension(R.dimen.text_size_sp_58)
            /*12小时制上午下午字体大小*/
            val amSize = mContext.resources.getDimension(R.dimen.text_size_sp_30)
            worldClockInclude.dialWordTimeTv.apply {
                setIsHover(true)
                alpha = 1f
                visibility = View.VISIBLE
                setTextSize(size, amSize)
                val llp = layoutParams as MarginLayoutParams
                mTimerTopMargin = llp.topMargin
                llp.topMargin = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_109)
                layoutParams = llp
            }
            initTopMargin(true)
        }
    }

    private fun handleNormal() {
        if (isEditMode) {
            return
        }
        mViewBinding?.apply {
            //停止未执行完的悬停过渡动画
            TransitionManager.endTransitions(worldClockCl)
            mShadowManager?.moveToScale(worldClockInclude.dialClockCl, true)
            worldClockInclude.dialWordTimeTv.visibility = View.INVISIBLE
            worldClockInclude.dialWordMsgTv.visibility = View.INVISIBLE
            val ac = AutoTransition()
            ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
            ac.interpolator = COUIEaseInterpolator()
            val set = ConstraintSet()
            set.clone(mContext, R.layout.world_clock_view_mid_layout_content)
            TransitionManager.beginDelayedTransition(worldClockCl, ac)
            set.applyTo(worldClockCl)
            mClockManager?.changeToNormal()
            //设置表盘大小
            val listSize = mListAdapter?.list?.size ?: 0
            if (listSize > 0) {
                mShadowManager?.moveToScale(worldClockInclude.dialClockCl, false)
            } else {
                mShadowManager?.moveToScale(worldClockInclude.dialClockCl, true)
            }
            val size = mContext.resources.getDimension(R.dimen.text_size_sp_58)
            val amSize = mContext.resources.getDimension(R.dimen.text_size_sp_20)
            worldClockInclude.dialWordTimeTv.apply {
                setIsHover(false)
                setTextSize(size, amSize)
                val llp = layoutParams as MarginLayoutParams
                llp.topMargin = mTimerTopMargin
                layoutParams = llp
            }
            if (mClockManager?.mIsText == true) {
                worldClockInclude.dialWordMsgTv.visibility = View.VISIBLE
                worldClockInclude.dialWordTimeTv.visibility = View.VISIBLE
            }
            mViewBinding?.worldClockList?.apply {
                scrollToPosition(0)
                val params = layoutParams as ViewGroup.MarginLayoutParams
                params.topMargin = mCityListTopMargin
                layoutParams = params
                setPadding(0, mPaddingHeight, 0, 0)
                scrollBy(0, -mPaddingHeight)
                adapter?.notifyDataSetChanged()
            }
            mViewBinding?.clickView?.apply {
                val height = resources.getDimensionPixelSize(R.dimen.layout_dp_330)
                val margin = resources.getDimensionPixelSize(R.dimen.layout_dp_52)
                val params = layoutParams as ViewGroup.MarginLayoutParams
                params.height = height - margin
                layoutParams = params
            }
            mWorldClockListManager.setEnable(true)
            mWorldClockListManager.setAutoScroll(true)
            initTopMargin(false)
        }
    }

    override fun isHover(): Boolean {
        return isHover
    }

    override fun getTalkBackMsg(): String? {
        return mViewBinding?.let {
            val time = it.worldClockInclude.dialWordTimeTv.getTalkBackMsg()
            val zone = it.worldClockInclude.dialWordMsgTv.text
            time + zone
        }
    }

    override fun getBlurView(): ViewGroup? {
        return mViewBinding?.worldClockList
    }

    override fun floatingButton(): COUIFloatingButton? {
        return mViewBinding?.button
    }

    override fun worldClockCl(): ConstraintLayout? {
        return mViewBinding?.worldClockCl
    }

    override fun cityListView(): COUIRecyclerView? {
        return mViewBinding?.worldClockList
    }


    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun cityListEditView(): COUIRecyclerView? {
        return mViewBinding?.worldClockListEdit
    }

    override fun setTimeInfo(timeInfo: String?) {
        timeInfo?.let { mViewBinding?.worldClockInclude?.timeInfo = it }
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_235)
    }
}