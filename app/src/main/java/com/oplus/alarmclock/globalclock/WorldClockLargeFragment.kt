/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - WorldClockLargeFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.WorldClockViewLargeLayoutBinding
import com.oplus.alarmclock.globalclock.view.ScrollGridLayoutManager
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.alarmclock.view.dial.ShadowManager
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager


class WorldClockLargeFragment : WorldClockViewFragment<WorldClockViewLargeLayoutBinding>() {

    override fun layoutId(): Int {
        return R.layout.world_clock_view_large_layout
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
            worldClockToolbarInclude.toolbar.setPopupWindowOnDismissListener {
                popupWindowOnDismiss()
            }
        }
    }

    override fun initDialClock() {
        super.initDialClock()
        loadOsloView()
        mClockManager = WorldClockAnimationManager()
        mShadowManager = ShadowManager()
        mViewBinding?.run {
            dialWordTimeBigTv.setUiMode(uiMode)
            clickListener = this@WorldClockLargeFragment
            mClockManager.initDialClock(
                    dialClockBigRl,
                    dialMsgBigTv,
                    dialWordTimeBigTv,
                    dialWordMsgBigTv,
                    mViewBinding!!.worldClockBigList
            )
            mShadowManager.init(
                dialClockBigTable,
                dialClockBigGlowTable,
                dialClockBigHour,
                dialClockBigMinute,
                dialClockBigSecond,
                dialClockBgBig,
                    mClockManager
            )
            mAlarmDialClockManager.init(
                dialClockBigGlowTable,
                dialClockBigSecond,
                dialClockBigHour,
                dialClockBigMinute,
                dialWordTimeBigTv
            )
        }
    }

    override fun updateTimeDialView(list: ArrayList<City>?) {
        super.updateTimeDialView(list)
        mViewBinding?.apply {
            val isToCenter = list.isNullOrEmpty()
            if (dialClockCl.visibility != View.VISIBLE) {
                dialClockCl.visibility = if (isLandscapeScreen() && !isToCenter) View.INVISIBLE else View.VISIBLE
            }
            if (isLandscapeScreen()) {
                mClockManager?.startDialMarginAnimation(
                        dialClockCl,
                        worldClockBigList,
                        isToCenter
                )
            }
        }
    }

    override fun setRecyclerViewLayoutManager(
        listView: COUIRecyclerView?,
        isEdit: Boolean,
        context: Context?
    ) {
        context?.apply {
            mGridLayoutManager =
                    ScrollGridLayoutManager(this, 2, GridLayoutManager.VERTICAL, false)
            listView?.layoutManager = mGridLayoutManager
        }
    }

    override fun setListAdapterProp() {
        mListAdapter?.apply {
            isNeedAddFooter(false)
            setIsGeneralScreen(false)
            setIsInMultiWindowMode(false)
        }
    }

    override fun resetCityAdapter(list: ArrayList<City>?) {
        super.resetCityAdapter(list)
        if (isPortraitScreen() && list?.size == 1) {
            mViewBinding?.worldClockBigList?.adapter = mListAdapter
        }
    }

    override fun onScreenOrientationChanged(orientation: Int) {
        super.onScreenOrientationChanged(orientation)
        mViewBinding?.apply {
            mListAdapter?.setUiMode(uiMode)
            dialWordTimeBigTv.setUiMode(uiMode)
            loadOsloView()
            dialClockCl.visibility = View.VISIBLE
            if (isLandscapeScreen()) {
                mClockManager?.apply {
                    mCurrentIsCenter = true
                    if (currentCount > 0) {
                        mCurrentIsCenter = false
                        startDialAnimation(dialClockCl, false)
                    }
                }
            } else {
                mClockManager?.mCurrentIsCenter = false
            }
            mListAdapter?.isNeedAddFooter(false)
            mListAdapter?.notifyDataSetChanged()
        }
    }

    /**
     * 切换平板横竖屏View
     * ConstraintSet 的 clone方法加载的布局必须为 ConstraintLayout
     */
    private fun loadOsloView() {
        mViewBinding?.apply {
            val set = ConstraintSet()
            if (isLandscapeScreen()) {
                set.clone(context, R.layout.world_clock_view_large_land_layout)
            } else {
                set.clone(context, R.layout.world_clock_view_large_land_content)
            }
            set.applyTo(worldClockContent)
        }
    }

    private fun isLandscapeScreen(): Boolean {
        return UiMode.LARGE_HORIZONTAL == uiMode
    }

    private fun isPortraitScreen(): Boolean {
        return UiMode.LARGE_VERTICAL == uiMode
    }

    override fun getTalkBackMsg(): String? {
        return mViewBinding?.let {
            val time = it.dialWordTimeBigTv.getTalkBackMsg()
            val zone = it.dialWordMsgBigTv.text
            time + zone
        }
    }

    override fun getBlurView(): ViewGroup? {
        return mViewBinding?.worldClockBigList
    }

    override fun floatingButton(): COUIFloatingButton? {
        return mViewBinding?.button
    }


    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun worldClockCl(): ConstraintLayout? {
        return mViewBinding?.worldClockContent
    }

    override fun cityListView(): COUIRecyclerView? {
        return mViewBinding?.worldClockBigList
    }

    override fun cityListEditView(): COUIRecyclerView? {
        return null
    }

    override fun setTimeInfo(timeInfo: String?) {
        timeInfo?.let { mViewBinding?.timeInfo = it }
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_333)
    }
}