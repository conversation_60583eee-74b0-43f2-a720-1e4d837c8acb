/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - WorldClockNormalFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.WorldClockViewLayoutBinding
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.isDragonfly
import com.oplus.alarmclock.utils.FoldScreenUtils.screenDisplayModel
import com.oplus.alarmclock.view.dial.ShadowManager
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager
import com.oplus.clock.common.utils.Log

class WorldClockNormalFragment : WorldClockViewFragment<WorldClockViewLayoutBinding>() {

    companion object {
        private const val TIME_DURATION = 200L
        private const val TAG = "WorldClockNormalFragment"
        private const val DIAC_LIST_LOCATION_Y = -100
    }

    private var mCityListDiff: Int = 0

    /**
     * 常规状态下的topMargin
     */
    private var topMargin = 0

    /**
     * 是否悬停
     */
    private var isHover = false

    /**
     * 悬停模式下页面整体向上的偏移量
     */
    private var diacClockclLocationY = FoldScreenUtils.TIMER_LAYOUT_LOCATION_Y
    private var diacListLocationY = DIAC_LIST_LOCATION_Y

    /**
     * 分割线距离顶部距离
     */
    private var mDividerDis = 0

    /**
     * 是否浮窗模式
     */
    private var mIsFloatingWindow = false
    private var isInit = false

    override fun layoutId(): Int {
        return R.layout.world_clock_view_layout
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        initManager()
        mViewBinding?.apply {
            ViewCompat.setAccessibilityDelegate(clickView, mTalkBackListener)
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout,
                    null, R.menu.action_menu_icon_all)
            worldClockToolbarInclude.toolbar.setPopupWindowOnDismissListener {
                popupWindowOnDismiss()
            }
        }
    }

    override fun onEditMenuSelected() {
        super.onEditMenuSelected()
        mViewBinding?.clickView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
    }

    override fun onEditCancel() {
        super.onEditCancel()
        mViewBinding?.clickView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
    }

    override fun delete() {
        super.delete()
        mViewBinding?.clickView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
    }

    override fun initDialClock() {
        super.initDialClock()
        mViewBinding?.clickListener = this@WorldClockNormalFragment
        mClockManager = WorldClockAnimationManager()
        mShadowManager = ShadowManager()
        mViewBinding?.worldClockInclude?.run {
            dialWordTimeTv.setUiMode(uiMode)
            clickListener = this@WorldClockNormalFragment
            mClockManager.initDialClock(
                    dialClockRl,
                    dialMsgTv,
                    dialWordTimeTv,
                    dialWordMsgTv,
                    mViewBinding!!.worldClockList
            )
            dialWordMsgTv.textSize = resources.getDimension(R.dimen.text_size_sp_14)
            dialMsgTv.textSize = resources.getDimension(R.dimen.text_size_sp_14)
            mShadowManager.init(dialClockBigTable, dialClockBigGlowTable, dialClockHour, dialClockMinute, dialClockSecond, dialClockBg, mClockManager)
            mAlarmDialClockManager.init(dialClockBigGlowTable, dialClockSecond, dialClockHour, dialClockMinute, dialWordTimeTv)
        }
    }

    private fun initListManager(height: Int, scrollDistance: Int, topMargin: Int, isFloatingWindow: Boolean) {
        if (mWorldClockListManager == null) {
            context?.let { mWorldClockListManager = WorldClockListManager(it) }
        }
        mViewBinding?.run {
            mWorldClockListManager.initManager(
                    mViewBinding?.worldClockInclude,
                    null,
                    mClockManager,
                    Triple(height, scrollDistance, topMargin),
                    worldClockCl,
                    worldClockList,
                    clickView,
                    floatingDivider,
                    isFloatingWindow)
        }
    }

    private fun initManager() {
        initTimeSize(true)
        mIsFloatingWindow = FoldScreenUtils.isFlexibleScenario(mContext)
        mViewBinding?.worldClockInclude?.dialClockCl?.let {
            it.viewTreeObserver.addOnGlobalLayoutListener(
                    object : OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            it.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            resetViewData(FoldScreenUtils.isFlexibleScenario(mContext))
                            if (mIsFloatingWindow && mClockManager != null) {
                                mClockManager.updateTextScale(true)
                            }
                        }
                    })
        }
    }

    /**
     * 配置联动参数
     */
    private fun resetViewData(isFloatingWindow: Boolean) {
        var zoomOffset = 1f
        if (isFloatingWindow) {
            zoomOffset = FoldScreenUtils.FLOATING_WINDOW_SCALE
        }
        //浮窗模式，缩小表盘
        mViewBinding?.worldClockInclude?.run {
            dialClockCl.post {
                mShadowManager?.moveToScaleTemp(dialClockCl, zoomOffset)
            }
        }
        if (mDividerDis == 0) {
            mDividerDis = getTopDistance()
        }
        val topMargin: Int = (mDividerDis * zoomOffset).toInt()
        val height = resources.getDimensionPixelSize(R.dimen.layout_dp_360) * zoomOffset
        val margin = resources.getDimensionPixelSize(R.dimen.layout_dp_52)
        val topPadding = mViewBinding?.worldClockInclude?.dialClockCl?.paddingTop ?: 0

        mViewBinding?.clickView?.apply {
            val params = layoutParams as MarginLayoutParams
            params.height = (height + topPadding - margin).toInt()
            layoutParams = params
        }

        val paddingHeight = (height + topPadding - topMargin).toInt()
        if ((topMargin > 0) && (paddingHeight > 0)) {
            mViewBinding?.worldClockList?.apply {
                val params = layoutParams as MarginLayoutParams
                params.topMargin = (topMargin)
                layoutParams = params
                setPadding(0, paddingHeight, 0, 0)
                scrollBy(0, -paddingHeight)
                mCityListTopMargin = topMargin
                mPaddingHeight = paddingHeight
                Log.d(TAG, "mCityList topMargin:$topMargin,PaddingTop:$paddingHeight,topMargin:$mDividerDis")
                initListManager(height.toInt(), paddingHeight, margin, isFloatingWindow)
                initTimeSize(false)
                if (!isInit) {
                    mViewBinding?.worldClockInclude?.dialWordTimeTv?.center()
                }
                mListAdapter?.notifyDataSetChanged()
                isInit = true
            }
        }
    }

    override fun initHoverIfNeed() {
        calculationLocationY()
        (activity as? AlarmClock)?.apply {
            if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                mViewBinding?.worldClockList?.postDelayed({
                    onHoverPostureChanged(true)
                }, FoldScreenUtils.HOVER_DELAY)
            }
        }
    }

    override fun setListAdapterProp() {
        mListAdapter?.apply {
            isNeedAddFooter(true)
            setIsGeneralScreen(true)
            setIsInMultiWindowMode(isInMultiWindowMode)
        }
    }

    override fun changeList(mode: Int) {
        mViewBinding?.apply {
            if (mode == MODEL_EDIT) {
                worldClockInclude.dialClockCl.visibility = View.GONE
                worldClockList.visibility = View.GONE
                worldClockListEdit.visibility = View.VISIBLE
                mWorldClockListManager?.setEnable(false)
                mWorldClockListManager?.setModeEdit(true)
                val manager = worldClockListEdit.layoutManager as LinearLayoutManager
                mListAdapter?.let { manager.scrollToPosition(it.longClickPosition) }
                mCityListDiff = mWorldClockListManager?.getDiff() ?: 0
            } else {
                worldClockInclude.dialClockCl.visibility = View.VISIBLE
                worldClockList.visibility = View.VISIBLE
                worldClockListEdit.visibility = View.GONE
                if (!isHover) {
                    mWorldClockListManager?.setEnable(true)
                    mWorldClockListManager?.setModeEdit(false)
                }
                (activity as? AlarmClock)?.apply {
                    if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE == mLayoutInfo) {
                        worldClockList.postDelayed({
                            onHoverPostureChanged(true)
                        }, HOVER_DELAY)
                    } else {
                        worldClockList.postDelayed({
                            onHoverPostureChanged(false)
                        }, HOVER_DELAY)
                    }
                }
                if (mCityListDiff > 0 && !isHover) {
                    //表盘收起状态重新设置列表位置，以免界面异常
                    worldClockList.scrollBy(0, mCityListDiff)
                    mCityListDiff = 0
                }
            }
        }
    }

    override fun updateCityListMargin() {
        super.updateCityListMargin()
        mViewBinding?.worldClockList?.apply {
            val paddingTop = paddingTop
            val params = layoutParams as MarginLayoutParams
            val topMargin = params.topMargin
            if (paddingTop > 0 && topMargin > 0) {
                Log.d(TAG, "updateList mCityList paddingTop:$paddingTop,topMargin:$topMargin")
                return
            }
            (activity as? AlarmClock)?.apply {
                if (ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE != mLayoutInfo) {
                    initManager()
                }
            }
        }
    }

    override fun onAfterMove() {
        super.onAfterMove()
        listViewBacktoTop()
    }

    override fun flexibleScenario() {
        super.flexibleScenario()
        context?.let {
            if (FoldScreenUtils.isFlexibleScenario(it)) {
                mIsFloatingWindow = true
                if (isHover) {
                    handleNormal()
                }
                resetViewData(true)
                mWorldClockListManager?.reset()
            } else {
                if (mIsFloatingWindow) {
                    if (isHover) {
                        handleHover()
                    }
                    resetViewData(false)
                    mWorldClockListManager?.reset()
                }
                mIsFloatingWindow = false
            }
            mClockManager.updateTextScale(mIsFloatingWindow)
        }
    }

    @Suppress("WhenExpressionFormattingRule")
    private fun calculationLocationY() {
        val screenDisplay = screenDisplayModel()
        diacClockclLocationY = FoldScreenUtils.DRAGONFLY_CLOCK_DIAL_LOCATION_Y
        when (screenDisplay) {
            FoldScreenUtils.SCREEN_DISPLAY_SMALL -> {
                diacClockclLocationY = FoldScreenUtils.DRAGONFLY_CLOCK_DIAL_SMALL_LOCATION_Y
                diacListLocationY = FoldScreenUtils.DRAGONFLY_WORLDCLOCK_SMALL_LIST_LOCATION_Y
            }

            FoldScreenUtils.SCREEN_DISPLAY_LARGE -> {
                diacListLocationY = FoldScreenUtils.DRAGONFLY_WORLDCLOCK_LARGE_LIST_LOCATION_Y
            }

            else -> {
                diacListLocationY = FoldScreenUtils.DRAGONFLY_WORLDCLOCK_STAND_LIST_LOCATION_Y
            }
        }
    }

    private fun initTimeSize(isTop: Boolean) {
        mViewBinding?.worldClockInclude?.dialWordTimeTv?.apply {
            val id = if (isTop) R.dimen.text_size_sp_40 else R.dimen.text_size_sp_48
            val size = resources.getDimension(id)
            setTextSize(size)
        }
    }

    /**
     * 获取顶部的高度
     */
    private fun getTopDistance(): Int {
        return mViewBinding?.worldClockInclude?.worldClockDivider?.let {
            val location = IntArray(2)
            it.getLocationInWindow(location)
            location[1] + it.height - mViewBinding!!.worldClockCl.paddingTop
        } ?: 0
    }

    override fun onHoverPostureChanged(hover: Boolean) {
        super.onHoverPostureChanged(hover)
        if (mViewBinding == null
                || mClockManager == null
                || isHover == hover
                || !isDragonfly()
        ) {
            return
        }
        if (activity != null && !requireActivity().isFinishing) {
            mClockManager?.setIsHover(hover)
            if (hover) {
                handleHover()
            } else {
                handleNormal()
            }
            isHover = hover
        }
    }

    private fun handleHover() {
        mViewBinding?.apply {
            Log.d(TAG, "handleHover dialClockCl:" + worldClockInclude.dialClockCl.visibility
                    + " Y:" + worldClockInclude.dialClockCl.translationY)
            if (isHover) {
                return
            }
            /*已经是悬停模式*/
            if (worldClockList.paddingTop == 0) {
                mWorldClockListManager?.setAutoScroll(false)
                mWorldClockListManager?.setEnable(false)
                mListAdapter?.clearItemView()
                mListAdapter?.notifyDataSetChanged()
                return
            }
            worldClockList.setPadding(0, 0, 0, 0)
            val cityLayout = worldClockList.layoutParams as MarginLayoutParams
            topMargin = mPaddingHeight + cityLayout.topMargin + diacListLocationY
            cityLayout.topMargin = mPaddingHeight + cityLayout.topMargin
            worldClockList.layoutParams = cityLayout

            /**列表上移动画*/
            worldClockList.cityListAnimator(topMargin - diacListLocationY, topMargin, null)

            /**取消自动滚动*/
            mWorldClockListManager?.setAutoScroll(false)

            /**取消联动*/
            mWorldClockListManager?.setEnable(false)
            worldClockInclude.dialClockCl.clockAnimator(0f, diacClockclLocationY.toFloat())

            val headerPadding = mWorldClockListManager?.getDiff() ?: 0
            if (headerPadding != 0) {
                /*列表活动到顶部*/
                mWorldClockListManager?.reset()
                val valueAnimator = ValueAnimator.ofInt(headerPadding, 0).setDuration(TIME_DURATION)
                valueAnimator.addUpdateListener { animation: ValueAnimator? ->
                    if (animation != null) {
                        val animValue = animation.animatedValue
                        if (animValue != null && animValue is Int) {
                            mWorldClockListManager?.listScroll(animValue)
                            if (animValue == 0) {
                                //刷新列表，刷新列表footer
                                mListAdapter?.clearItemView()
                                mListAdapter?.notifyDataSetChanged()
                            }
                        }
                    }
                }
                valueAnimator.start()
            }
            if (mWorldClockListManager?.getDiff() == 0) {
                /*刷新列表，刷新列表footer*/
                mListAdapter?.clearItemView()
                mListAdapter?.notifyDataSetChanged()
            }
        }
    }

    private fun handleNormal() {
        mViewBinding?.apply {
            if (worldClockInclude.dialClockCl.translationY == 0f || !isHover) {
                mWorldClockListManager?.setEnable(true)
                mWorldClockListManager?.setAutoScroll(true)
                return
            }
            worldClockList.scrollToPosition(0)
            /*表盘下移动画*/
            worldClockInclude.dialClockCl.let {
                it.clockAnimator(it.translationY, 0f)
            }
            /*恢复padding*/
            worldClockList.setPadding(0, mPaddingHeight, 0, 0)
            val cityLayout = worldClockList.layoutParams as MarginLayoutParams
            cityLayout.topMargin = topMargin - mPaddingHeight

            /*恢复topMargin*/
            worldClockList.layoutParams = cityLayout

            /*列表下移动画*/
            worldClockList.cityListAnimator(
                    topMargin - mPaddingHeight,
                    topMargin - mPaddingHeight - diacListLocationY,
                    mAnimatorListener
            )
            worldClockList.scrollBy(0, -mPaddingHeight)
        }
    }

    private fun setListCanScroll(isCanScroll: Boolean) {
        mLayoutManager?.setCanScrollVertically(isCanScroll)
        mGridLayoutManager?.setCanScrollVertically(isCanScroll)
    }

    /**
     * 悬停模式城市列表动画
     */
    private fun COUIRecyclerView.cityListAnimator(
        start: Int,
        end: Int,
        animatorListener: Animator.AnimatorListener?
    ) {
        val cityLayout = layoutParams as MarginLayoutParams
        ValueAnimator.ofInt(start, end).apply {
            duration = TIME_DURATION
            addUpdateListener { animation ->
                val d = animation.animatedValue as Int
                cityLayout.topMargin = d
                layoutParams = cityLayout
            }
            if (animatorListener != null) {
                addListener(animatorListener)
            }
        }.start()
    }

    /**
     * 悬停模式表盘动画
     */
    private fun ConstraintLayout.clockAnimator(start: Float, end: Float) {
        ValueAnimator.ofFloat(start, end).apply {
            duration = TIME_DURATION
            addUpdateListener { animation ->
                val d = animation.animatedValue as Float
                translationY = d
            }
        }.start()
    }

    private val mAnimatorListener: Animator.AnimatorListener = object : Animator.AnimatorListener {
        override fun onAnimationStart(animator: Animator) {}
        override fun onAnimationEnd(animator: Animator) {
            mListAdapter?.clearItemView()
            mListAdapter?.notifyDataSetChanged()
            mWorldClockListManager?.setEnable(true)
            mWorldClockListManager?.setAutoScroll(true)
        }

        override fun onAnimationCancel(animator: Animator) {
            mListAdapter?.clearItemView()
            mListAdapter?.notifyDataSetChanged()
            mWorldClockListManager?.setEnable(true)
            mWorldClockListManager?.setAutoScroll(true)
        }

        override fun onAnimationRepeat(animator: Animator) {}
    }

    override fun isHover(): Boolean {
        return isHover
    }

    override fun isNormalFragment(): Boolean {
        return true
    }

    override fun getTalkBackMsg(): String? {
        return mViewBinding?.worldClockInclude?.let {
            val time = it.dialWordTimeTv.getTalkBackMsg()
            val zone = it.dialWordMsgTv.text.toString()
            time + zone
        }
    }

    override fun getBlurView(): ViewGroup? {
        return mViewBinding?.worldClockList
    }

    override fun floatingButton(): COUIFloatingButton? {
        return mViewBinding?.button
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun worldClockCl(): ConstraintLayout? {
        return mViewBinding?.worldClockCl
    }

    override fun cityListView(): COUIRecyclerView? {
        return mViewBinding?.worldClockList
    }

    override fun cityListEditView(): COUIRecyclerView? {
        return mViewBinding?.worldClockListEdit
    }

    override fun setTimeInfo(timeInfo: String?) {
        timeInfo?.let {
            mViewBinding?.worldClockInclude?.timeInfo = it
            mViewBinding?.clickView?.contentDescription = it
        }
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.app_dial_stand_width)
    }
}