/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorldClockCSUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.content.Context
import android.text.BidiFormatter
import android.text.TextDirectionHeuristics
import android.text.TextUtils
import android.view.View
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import java.text.SimpleDateFormat
import java.util.*

object WorldClockCSUtils {
    /**
     * 时区变化埋点
     */
    @JvmStatic
    fun buriedPointTimeZoneChange(context: Context) {
        val values = HashMap<String, String>()
        val calendar = Calendar.getInstance()
        val timeZone = calendar.timeZone
        values[ClockOplusCSUtils.KEY_CURRENT_TIME_ZONE] = timeZone.displayName
        values[ClockOplusCSUtils.KEY_CURRENT_OFF_SET] = getOffSet(timeZone)
        values[ClockOplusCSUtils.KEY_CURRENT_TIME_STAMP] = calendar.time.time.toString()
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.EVENT_TIME_ZONE_CHANGE, values)
    }

    @JvmStatic
    private fun getOffSet(tz: TimeZone): String {
        val calendar = Calendar.getInstance()
        val mGmtFormatter = SimpleDateFormat("ZZZZ", Locale.getDefault())
        mGmtFormatter.timeZone = tz
        var gmtString = mGmtFormatter.format(calendar.time)
        val l = Locale.getDefault()
        val isRtl = TextUtils.getLayoutDirectionFromLocale(l) == View.LAYOUT_DIRECTION_RTL
        gmtString = BidiFormatter.getInstance().unicodeWrap(
            gmtString,
            if (isRtl) TextDirectionHeuristics.RTL else TextDirectionHeuristics.LTR
        )
        return gmtString ?: ""
    }
}