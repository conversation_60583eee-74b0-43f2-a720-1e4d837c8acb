/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AddCityBottomSheetDialog.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/2/19
 ** Author: ********
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ********  2024/2/19     1.0            build this module
 ****************************************************************/
@file:Suppress("MaximumLineLength")
package com.oplus.alarmclock.globalclock

import android.app.Activity
import android.app.LoaderManager
import android.content.Context
import android.content.Loader
import android.database.Cursor
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.asynclayoutinflater.view.AsyncLayoutInflater.OnInflateFinishedListener
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.touchsearchview.COUITouchSearchView
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.BaseFragment
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.BackgroundUtils.setEmptyBackground
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.alarmclock.view.modelview.PanelSaveRestoreHelper.setBundle
import com.oplus.anim.EffectiveAnimationView
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.Log.d

class AddCityBottomSheetDialog(
    private val mActivity: Activity,
    private val mUiMode: UiMode,
    private val mCityCount: Int,
    private val mIsFromScreen: Boolean
) : COUIBottomSheetDialog(mActivity, R.style.DefaultBottomSheetDialog), COUISearchBar.OnStateChangeListener,
    COUITouchSearchView.TouchSearchActionListener, OnInflateFinishedListener, OnItemClickListener, LoaderManager.LoaderCallbacks<Cursor?> {

    companion object {
        private const val TAG = "AddCityBottomSheetDialog"
    }

    private val mContext: Context = AlarmClockApplication.getInstance().applicationContext
    private var mViewHolder: AddCityViewHolder = AddCityViewHolder()
    private var mAddCityManager: AddCityManager? = null
    private var mContainerView: FrameLayout = FrameLayout(mContext)

    init {
        contentView = mContainerView
        initDialog()
        mViewHolder.inflate(mActivity, null, null, this)
    }

    override fun onInflateFinished(view: View, resid: Int, parent: ViewGroup?) {
        mContainerView.removeAllViews()
        mContainerView.addView(view)
        mViewHolder.initView(view)
        initParams()
        mAddCityManager = AddCityManager(mActivity, this, mViewHolder)
        mAddCityManager?.initData()
        initListener()
        mAddCityManager?.registerConfigChangeReceiver()
        setBundle(null)
    }

    private fun initParams() {
        mActivity.intent.putExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, mIsFromScreen)
        mActivity.intent.putExtra(AddCityFragment.CURRENT_COUNT, mCityCount)
        mActivity.intent.putExtra(AddCityActivity.IS_SHOW_PANEL, false)
        mActivity.intent.putExtra(AddCityActivity.IS_FROM_WORD_CLOCK_OR_WIDGET, true)
    }

    private fun initDialog() {
        hideDragView()
        setPanelBackgroundTintColor(ContextCompat.getColor(mContext, R.color.coui_color_background_elevatedWithCard))
        window?.let {
            if (isTabletMode() || isMidMode()) {
                it.navigationBarColor = Color.parseColor("#01ffffff")
            } else {
                it.navigationBarColor = Color.parseColor("#01FAFAFA")
            }
        }
        if (isTabletMode()) {
            setHeight(mActivity.resources.getDimensionPixelOffset(R.dimen.layout_dp_800))
        } else {
            setIsShowInMaxHeight(true)
        }
        //禁用拖动关闭和点击空白关闭
        setCanceledOnTouchOutside(false)
        setDraggable(false)
        setAnimationListener(object : OnAnimationListener {
            override fun onShowAnimationEnd() {
                super.onShowAnimationEnd()
                //动画执行完成后启动拖动和点击空白关闭
                setDraggable(true)
                setCanceledOnTouchOutside(true)
            }
        })
        setOnShowListener {
            if (COUIDarkModeUtil.isNightMode(context)) {
                setPanelBackgroundTintColor(
                    ContextCompat.getColor(
                        mActivity,
                        R.color.coui_color_background_elevatedWithCard
                    )
                )
            }
        }
    }

    private fun initListener() {
        mViewHolder.mListAdapter?.setOnItemClickListener(this)
        mViewHolder.mTouchSearchView?.touchSearchActionListener = this
        mViewHolder.mGlobalSearchView?.addOnStateChangeListener(this)
        mViewHolder.mBehavior?.setOnScrollStateChangedListener { _, scrollState ->
            if (COUIRecyclerView.SCROLL_STATE_DRAGGING == scrollState) {
                mAddCityManager?.hideSoftInput()
            }
        }
    }

    fun handleQuery(id: Int, param: Bundle) {
        Log.d(TAG, "")
        mActivity.loaderManager?.restartLoader<Cursor>(id, param, this)
    }

    fun isTabletMode(): Boolean {
        return UiMode.LARGE_HORIZONTAL == mUiMode || UiMode.LARGE_VERTICAL == mUiMode
    }

    fun isMidMode(): Boolean {
        return UiMode.MIDDLE == mUiMode
    }

    fun onStatusBarClicked() {
        mViewHolder.apply {
            mTouchSearchView?.closing()
            backtoTop()
        }
    }

    override fun dismiss() {
        super.dismiss()
        mAddCityManager?.onDestroy()
    }

    fun playEmptyAnimOrShowEmptyIcon(animationView: EffectiveAnimationView?, mEmptyTextView: TextView?, tabPage: Int) {
        if (animationView != null && animationView.visibility == View.VISIBLE) {
            d("TAG", "playEmptyAnim0 ")
            if (mEmptyTextView != null) {
                mEmptyTextView.visibility = View.VISIBLE
            }
            val isDark = COUIDarkModeUtil.isNightMode(mContext)
            /**此处需修改为1f，否在在暗色模式下动画有异常，参见bug6525295 */
            animationView.alpha = BaseFragment.ALPHA_ZERO
            setEmptyBackground(isDark, animationView, tabPage)
        }
    }

    override fun onCreateLoader(id: Int, bundle: Bundle?): Loader<Cursor?>? {
        return mAddCityManager?.onCreateLoader(id, bundle)
    }

    override fun onLoaderReset(loader: Loader<Cursor?>?) {
        loader?.let { mAddCityManager?.onLoaderReset(it) }
    }

    override fun onLoadFinished(loader: Loader<Cursor?>?, cursor: Cursor?) {
        loader?.let { mAddCityManager?.onLoadFinished(it, cursor) }
    }

    override fun onStateChange(p0: Int, p1: Int) {
        mAddCityManager?.onStateChange(p0, p1)
    }

    override fun onKey(key: CharSequence?) {
        key?.let { mAddCityManager?.onKey(it) }
    }

    override fun onLongKey(longKey: CharSequence?) {
        longKey?.let { mAddCityManager?.onLongKey(it) }
    }

    override fun onNameClick(charSequence: CharSequence?) {
        charSequence?.let { mAddCityManager?.onNameClick(it) }
    }

    override fun onItemClock(position: Int, cursor: Cursor?) {
        cursor?.let { mAddCityManager?.onItemClock(position, it) }
    }
}