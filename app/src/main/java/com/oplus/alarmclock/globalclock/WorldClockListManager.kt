/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorldClockListManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.animation.ValueAnimator
import android.content.Context
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.view.animation.PathInterpolator
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.math.MathUtils.clamp
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.WorldDialClockBinding
import com.oplus.alarmclock.databinding.WorldDialClockMidBinding
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter
import com.oplus.alarmclock.view.NestedScrollableHost
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager
import com.oplus.alarmclock.view.dial.AlarmDialClockMsgTextView
import com.oplus.alarmclock.view.dial.AlarmDialClockTextView
import com.oplus.clock.common.utils.Log
import kotlin.math.abs

class WorldClockListManager(val mContext: Context) {
    companion object {
        private const val DIAL_ALPHA_SCALE = 0.2F
        private const val SCROLL_REBOUND_SCALE = 0.23F
        private const val ZERO_F = 0F
        private const val LOCATION_NONE = -1
        private const val TWO = 2
        private const val ONE_F = 1F
        private const val DURATION = 400L
        private const val PATH_X1 = 0.25F
        private const val PATH_Y1 = 0.10F
        private const val PATH_X2 = 0.20F
        private const val PATH_Y2 = 1.00F
        private const val ZERO_073 = 0.73F
        private const val ZERO_095 = 0.95F
    }

    var mScrollDistance = 0

    @VisibleForTesting
    val mOnScrollListener by lazy { initScrollListener() }
    private var mAlarmDialClockManager: WorldClockAnimationManager? = null
    private lateinit var mRecyclerView: RecyclerView
    private lateinit var mDialClock: RelativeLayout
    private lateinit var mDialClockMsgTv: AlarmDialClockMsgTextView
    private lateinit var mDialClockTv: AlarmDialClockTextView
    private lateinit var mDialClockTvMsgTv: AlarmDialClockMsgTextView
    private lateinit var mDivider: View
    private lateinit var mClickView: View
    private var mFloatDivider: View? = null
    private var mCurrentRatio = ZERO_F
    private var mTempScrollY = 0
    private var mHeaderHeight = 0
    private var mEndValue = 0
    private var mOffsetTop = 0
    private var mIsDown = false
    private var mPosition = 0
    private var mMsgDistance = 0
    private var mViewTextSize = ZERO_F
    private var mTextSizeDiff = ZERO_F
    private var mAmPmTextSizeSize = ZERO_F
    private var mAmPmTextSizeDiff = ZERO_F
    private var mAlphaTop = ZERO_F
    private var mAlphaStart = ZERO_F
    private var mAlphaBottom = ZERO_F
    private val mAnimator by lazy { initAnimator() }
    private var mEnable = true
    private var mAutoScroll = true

    /**
     * 是否浮窗模式
     */
    private var mIsFloatingWindow: Boolean = false

    /**
     * 表盘和列表联动需要计算列表侵入表盘距离
     * 计算距离时需要减去列表item根布局的marginTop值
     */
    private var mListItemMarginTop = 0

    init {
        mContext.resources.run {
            //item根布局的marginTop值
            mListItemMarginTop = getDimensionPixelSize(R.dimen.layout_dp_12)
            mViewTextSize = getDimension(R.dimen.text_size_sp_48)
            mTextSizeDiff = mViewTextSize - getDimension(R.dimen.text_size_sp_40)
            mAmPmTextSizeSize = getDimension(R.dimen.text_size_sp_20)
            mAmPmTextSizeDiff = mAmPmTextSizeSize - getDimension(R.dimen.text_size_sp_18)
        }
    }

    /**
     * 初始化
     */
    fun initManager(
        dialCockView: WorldDialClockBinding?,
        dialCockViewMid: WorldDialClockMidBinding?,
        manager: WorldClockAnimationManager,
        triple: Triple<Int, Int, Int>,
        worldClockCl: NestedScrollableHost,
        worldClockList: RecyclerView,
        clickView: View,
        floatDivider: View,
        isFloatingWindow: Boolean
    ) {
        dialCockView?.run {
            mOffsetTop = triple.third
            mHeaderHeight = triple.first + dialClockCl.paddingTop + worldClockCl.paddingTop
            mScrollDistance = triple.second
            mAlphaBottom = triple.second * DIAL_ALPHA_SCALE
            mAlphaTop = triple.second - mAlphaBottom * ZERO_073
            mAlphaStart = triple.second - mAlphaTop
            mRecyclerView = worldClockList
            mDialClock = dialClockRl
            mDialClockMsgTv = dialMsgTv
            mDialClockTv = dialWordTimeTv
            mDialClockTvMsgTv = dialWordMsgTv
            mDivider = worldClockDivider
            mClickView = clickView
            if (mMsgDistance == 0) {
                mMsgDistance = getMsgDistance()
            }
        }
        mFloatDivider = floatDivider
        mIsFloatingWindow = isFloatingWindow
        dialCockViewMid?.run {
            mOffsetTop = triple.third
            mHeaderHeight = triple.first + worldClockCl.paddingTop
            mScrollDistance = triple.second
            mAlphaBottom = triple.second * DIAL_ALPHA_SCALE
            mAlphaTop = triple.second - mAlphaBottom * ZERO_073
            mAlphaStart = triple.second - mAlphaTop
            mRecyclerView = worldClockList
            mDialClock = dialClockRl
            mDialClockMsgTv = dialMsgTv
            mDialClockTv = dialWordTimeTv
            mDialClockTvMsgTv = dialWordMsgTv
            mDivider = worldClockDivider
            mClickView = clickView
            if (mMsgDistance == 0) {
                mMsgDistance = getMsgDistance()
            }
        }
        mAlarmDialClockManager = manager
        setOnScrollListener()
        mFloatDivider?.let {
            if (!mIsFloatingWindow) {
                it.alpha = 0F
            }
        }
        mDivider.let {
            if (mIsFloatingWindow) {
                it.alpha = 0F
            }
        }
    }

    /**
     * 用于列表隐藏时表盘还原
     */
    fun resetNoList() {
        listScroll(0)
    }

    /**
     * 列表回到原位
     */
    fun reset() {
        stopScroll()
        mRecyclerView.run {
            if (childCount > 0) {
                smoothScrollToPosition(0)
            }
            startNestedScroll(ViewCompat.SCROLL_AXIS_VERTICAL)
        }
    }

    fun setEnable(enable: Boolean) {
        mEnable = enable
    }

    fun setAutoScroll(autoScroll: Boolean) {
        mAutoScroll = autoScroll
    }

    /**
     * 设置分割线隐藏/显示
     * */
    fun setModeEdit(isEditMode: Boolean) {
        if (isEditMode) {
            setAlpha(mFloatDivider, ZERO_F)
        } else {
            updateLine(mCurrentRatio)
        }
    }
    /**
     * 获取上下两个时区描述的间距
     */
    private fun getMsgDistance(): Int {
        val location = IntArray(2)
        mDialClockTvMsgTv.getLocationInWindow(location)
        val top = location[1]
        mDialClockMsgTv.getLocationInWindow(location)
        val bottom = location[1]
        return abs(bottom - top)
    }

    /**
     * 联动原理，获取列表滑动监听获取列表侵入Header的百分比，控制当前Header的左右上下距离和透明度的变化
     */
    private fun onListScroll() {
        if (mEnable) {
            mRecyclerView.run {
                val diff = getDiff()
                if (diff >= 0) {
                    val tempRatio = getRatio(diff)
                    if (tempRatio == mCurrentRatio) {
                        return
                    }
                    mCurrentRatio = tempRatio
                    updateView(mCurrentRatio, diff)
                    updateClickView()
                }
            }
        }
    }

    /**
     * 滚动列表，展开表盘
     */
    fun listScroll(diff: Int) {
        if (diff >= 0) {
            val tempRatio = getRatio(diff)
            if (tempRatio == mCurrentRatio) {
                return
            }
            mCurrentRatio = tempRatio
            updateView(mCurrentRatio, diff)
            updateClickView()
        }
    }

    /**
     * 更新表盘、数字等View
     */
    private fun updateView(currentRatio: Float, currentOffset: Int) {
        mContext.resources.run {
            val isText = mAlarmDialClockManager?.mIsText ?: false
            updateDialClock(isText, currentOffset, currentRatio)
            updateDialClockTv(isText, currentOffset, currentRatio)
        }
    }

    /**
     * 更新表盘
     */
    private fun updateDialClock(isText: Boolean, currentOffset: Int, currentRatio: Float) {
        if (isText) {
            return
        }
        val mAlpha = clamp(abs(currentOffset / mAlphaBottom), ZERO_F, ONE_F)
        mDialClock.run {
            scaleX = ONE_F - DIAL_ALPHA_SCALE * currentRatio
            scaleY = scaleX
            setAlpha(this, ONE_F - mAlpha)
        }
    }

    /**
     * 更新数字时间、时区描述、横线
     */
    private fun updateDialClockTv(isText: Boolean, currentOffset: Int, currentRatio: Float) {
        mDialClockTv.run {
            val currentMarginTop =
                    ((mMarginTop - mOffsetTop) * (ONE_F - currentRatio)).toInt() + mOffsetTop
            val param = (layoutParams as ConstraintLayout.LayoutParams).apply {
                topMargin = currentMarginTop
            }
            layoutParams = param
            val timeSize = mViewTextSize - currentRatio * mTextSizeDiff
            val amPmSize = mAmPmTextSizeSize - currentRatio * mAmPmTextSizeDiff
            setTextSize(timeSize, amPmSize)
            val currentAlpha = if (isText) {
                ONE_F
            } else {
                if (currentOffset >= mAlphaStart) {
                    (currentOffset - mAlphaStart) / mAlphaTop
                } else {
                    ZERO_F
                }
            }
            setAlpha(this, currentAlpha)
            updateDialClockMsgTv(isText, currentRatio)
            updateLine(currentRatio)
        }
    }

    private fun updateDialClockMsgTv(isText: Boolean, currentRatio: Float) {
        if (isText) {
            return
        }
        mDialClockMsgTv.translationY = -(currentRatio * mMsgDistance)
    }

    private fun updateLine(currentRatio: Float) {
        mDivider.run {
            val alpha = if (currentRatio >= 1F) 1F else 0F
            if (mIsFloatingWindow) {
                setAlpha(mFloatDivider, alpha)
            } else {
                setAlpha(this, alpha)
            }
        }
    }

    /**
     * 更新最上层的点击区域
     */
    private fun updateClickView() {
        if (mCurrentRatio > ZERO_F) {
            if (mClickView.visibility != View.GONE) {
                mClickView.visibility = View.GONE
            }
        } else {
            if (mClickView.visibility != View.VISIBLE) {
                mClickView.visibility = View.VISIBLE
            }
        }
    }

    private fun setAlpha(view: View?, mAlpha: Float) {
        view?.run {
            alpha = mAlpha
            visibility = if ((mAlpha == ZERO_F)) {
                View.INVISIBLE
            } else {
                View.VISIBLE
            }
        }
    }

    /**
     * 获取列表顶部侵入和Header的距离
     */
    fun getDiff(): Int {
        mPosition = getFirstChildLocationY(mRecyclerView)
        if (mPosition < 0) {
            return LOCATION_NONE
        }
        val diff = mHeaderHeight - mPosition
        return clamp(diff, 0, mHeaderHeight)
    }

    /**
     * 获取列表顶部的Y轴位置
     */
    private fun getFirstChildLocationY(viewGroup: RecyclerView): Int {
        if (viewGroup.adapter == null) {
            return LOCATION_NONE
        }
        val ada = viewGroup.adapter as CityListAdapter
        val size = ada.list.size
        if (size <= 0) {
            return LOCATION_NONE
        } else {
            for (i in 0 until size) {
                val child = viewGroup.getChildAt(i) ?: return LOCATION_NONE
                if (child.visibility == View.VISIBLE) {
                    val location = IntArray(TWO)
                    child.getLocationInWindow(location)
                    return location[1] - mListItemMarginTop
                }
            }
        }
        return LOCATION_NONE
    }

    private fun setOnScrollListener() {
        mRecyclerView.run {
            addOnScrollListener(mOnScrollListener)
            setOnScrollChangeListener { _, _, _, _, _ ->
                onListScroll()
            }
        }
    }

    /**
     * 列表状态变化监听，用于列表停止滑动后，自动上下滑动的逻辑
     */
    private fun initScrollListener(): RecyclerView.OnScrollListener {
        return object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE && mAutoScroll) {
                    val diff = getDiff()
                    if ((diff <= 1) || (diff >= mScrollDistance)) {
                        return
                    }
                    val isRebound = (diff.toFloat() / mScrollDistance) > SCROLL_REBOUND_SCALE
                    mEndValue = if (mIsDown) {
                        if (isRebound) {
                            -diff
                        } else {
                            mScrollDistance - diff
                        }
                    } else {
                        if (isRebound) {
                            mScrollDistance - diff
                        } else {
                            -diff
                        }
                    }
                    rebound()
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mIsDown = dy < 0
            }
        }
    }

    private fun initAnimator(): ValueAnimator {
        return ValueAnimator.ofFloat(ZERO_F, ONE_F).apply {
            duration = DURATION
            interpolator = PathInterpolator(PATH_X1, PATH_Y1, PATH_X2, PATH_Y2)
            addUpdateListener { animation ->
                val range = animation.animatedValue as Float
                val currentY = (range * mEndValue).toInt()
                val scroll = currentY - mTempScrollY
                mRecyclerView.scrollBy(0, scroll)
                mTempScrollY = currentY
            }
        }
    }

    private fun rebound() {
        mTempScrollY = 0
        mAnimator.start()
    }

    /**
     * 获取列表侵入部分占可滑动部分的百分比
     */
    private fun getRatio(diff: Int): Float {
        val ratio = diff.toFloat() / mScrollDistance
        return clamp(ratio, ZERO_F, ONE_F)
    }

    private fun stopScroll() {
        val time = SystemClock.uptimeMillis()
        val event =
                MotionEvent.obtain(time, time, MotionEvent.ACTION_CANCEL, ZERO_F, ZERO_F, 0)
        mRecyclerView.dispatchTouchEvent(event)
        event.recycle()
    }
}