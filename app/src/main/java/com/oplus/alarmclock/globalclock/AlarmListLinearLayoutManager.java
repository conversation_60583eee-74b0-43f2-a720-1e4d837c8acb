/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:AlarmListLinearLayoutManager
 ** Description: Set the scroll time of RecyclerView to 400ms
 ** Version: V 1.0
 ** Date : 2024/2/28
 ** Author: helin
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.globalclock;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashMap;
import java.util.Map;

public class AlarmListLinearLayoutManager extends LinearLayoutManager {


    public AlarmListLinearLayoutManager(Context context) {
        super(context);
    }

    public AlarmListLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public AlarmListLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    public int computeVerticalScrollOffset(RecyclerView.State state) {
        return 0;
    }

}
