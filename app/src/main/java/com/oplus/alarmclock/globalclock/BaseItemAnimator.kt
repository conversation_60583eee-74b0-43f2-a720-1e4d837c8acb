/*********************************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd
 ** OPLUSOS_EDIT, All rights reserved.
 **
 ** File: - BaseItemAnimator.kt
 ** Description: Basic ItemAnimator for ColorRecyclerView. Each custom ItemAnimator
 **              should extends this class and override animateRemoveImpl or
 **              animateAddImpl method bases on demand
 ** Version: 1.0
 ** Date: 2020/03/26
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2020.03.26   1.0         Create this module
 ********************************************************************************/

package com.oplus.alarmclock.globalclock

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.TimeInterpolator
import android.animation.ValueAnimator
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.oplus.clock.common.utils.Log
import java.util.*
import kotlin.math.max

open class BaseItemAnimator : SimpleItemAnimator() {
    companion object {
        const val DEBUG = false
        const val LOG = "TestAnimator"
        const val TYPE_ADD = 1
        const val TYPE_REMOVE = 2
        const val TYPE_MOVE = 3
        const val TYPE_CHANGE = 4
    }

    private val mPendingRemovals = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingAdditions = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingMoves = ArrayList<MoveInfo>()
    private val mPendingChanges = ArrayList<ChangeInfo>()

    private var mAdditionsList = ArrayList<ArrayList<RecyclerView.ViewHolder>>()
    private var mMovesList = ArrayList<ArrayList<MoveInfo>>()
    private var mChangesList = ArrayList<ArrayList<ChangeInfo>>()

    private var mInterpolator: TimeInterpolator? = null
    private var mAddInterpolator: TimeInterpolator? = null
    private var mRemoveInterpolator: TimeInterpolator? = null
    private var mMoveInterpolator: TimeInterpolator? = null
    private var mChangeInterpolator: TimeInterpolator? = null

    internal var mAddAnimations = ArrayList<RecyclerView.ViewHolder?>()
    internal var mMoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    internal var mRemoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    internal var mChangeAnimations = ArrayList<RecyclerView.ViewHolder?>()

    /**
     * Store info of item that is to move
     */
    class MoveInfo internal constructor(
        var holder: RecyclerView.ViewHolder?,
        var fromX: Int,
        var fromY: Int,
        var toX: Int,
        var toY: Int
    )

    /**
     * Store info of item that is to change
     */
    class ChangeInfo private constructor(
        var oldHolder: RecyclerView.ViewHolder?,
        var newHolder: RecyclerView.ViewHolder?
    ) {
        var fromX = 0
        var fromY = 0
        var toX = 0
        var toY = 0

        internal constructor(
            oldHolder: RecyclerView.ViewHolder?, newHolder: RecyclerView.ViewHolder?,
            fromX: Int, fromY: Int, toX: Int, toY: Int
        ) : this(oldHolder, newHolder) {
            this.fromX = fromX
            this.fromY = fromY
            this.toX = toX
            this.toY = toY
        }

        override fun toString(): String {
            return ("ChangeInfo{"
                + "oldHolder=" + oldHolder
                + ", newHolder=" + newHolder
                + ", fromX=" + fromX
                + ", fromY=" + fromY
                + ", toX=" + toX
                + ", toY=" + toY
                + '}')
        }
    }

    /**
     * Run all pending animations. Order is remove, move, change and add.
     */
    override fun runPendingAnimations() {
        val removalsPending = mPendingRemovals.isNotEmpty()
        val movesPending = mPendingMoves.isNotEmpty()
        val changesPending = mPendingChanges.isNotEmpty()
        val additionsPending = mPendingAdditions.isNotEmpty()

        if (!removalsPending && !movesPending && !additionsPending && !changesPending) {
            // nothing to animate
            return
        }
        // First, remove stuff
        for (holder in mPendingRemovals) {
            animateRemoveImpl(holder)
        }
        mPendingRemovals.clear()
        // Next, move stuff
        if (movesPending) {
            val moves = ArrayList<MoveInfo>()
            moves.addAll(mPendingMoves)
            mMovesList.add(moves)
            mPendingMoves.clear()
            val mover = Runnable {
                for (moveInfo in moves) {
                    animateMoveImpl(
                        moveInfo.holder, moveInfo.fromX, moveInfo.fromY,
                        moveInfo.toX, moveInfo.toY
                    )
                }
                moves.clear()
                mMovesList.remove(moves)
            }
            if (removalsPending) {
                val view = moves[0].holder?.itemView
                view?.let {
                    moveAnimationPost(it, mover, removeDuration)
                }
            } else {
                mover.run()
            }
        }
        // Next, change stuff, to run in parallel with move animations
        if (changesPending) {
            val changes = ArrayList<ChangeInfo>()
            changes.addAll(mPendingChanges)
            mChangesList.add(changes)
            mPendingChanges.clear()
            val changer = Runnable {
                for (change in changes) {
                    animateChangeImpl(change)
                }
                changes.clear()
                mChangesList.remove(changes)
            }
            if (removalsPending) {
                val holder = changes[0].oldHolder
                holder?.itemView?.let {
                    changeAnimationPost(it, changer, removeDuration)
                }
            } else {
                changer.run()
            }
        }
        // Next, add stuff
        if (additionsPending) {
            val additions = ArrayList<RecyclerView.ViewHolder>()
            additions.addAll(mPendingAdditions)
            mAdditionsList.add(additions)
            mPendingAdditions.clear()
            val adder = Runnable {
                for (holder in additions) {
                    animateAddImpl(holder)
                }
                additions.clear()
                mAdditionsList.remove(additions)
            }
            if (removalsPending || movesPending || changesPending) {
                val removeDuration = if (removalsPending) removeDuration else 0
                val moveDuration = if (movesPending) moveDuration else 0
                val changeDuration = if (changesPending) changeDuration else 0
                val totalDelay = removeDuration + max(moveDuration, changeDuration)
                val view = additions[0].itemView
                addAnimationPost(view, adder, totalDelay)
            } else {
                adder.run()
            }
        }
    }

    /**
     * Pre-set info of viewHolder which will be removed from the list
     */
    override fun animateRemove(holder: RecyclerView.ViewHolder?): Boolean {
        resetAnimation(holder, TYPE_REMOVE)
        holder?.let {
            mPendingRemovals.add(it)
        }
        return true
    }

    /**
     * Implementation of remove animation
     */
    open fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        mRemoveAnimations.add(holder)
        animation.setDuration(removeDuration)
            .alpha(0f)
            .setListener(
                object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animator: Animator) {
                        dispatchRemoveStarting(holder)
                    }

                    override fun onAnimationCancel(animator: Animator) {
                        ViewHelperUtil.clear(view)
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        ViewHelperUtil.clear(view)
                        dispatchRemoveFinished(holder)
                        mRemoveAnimations.remove(holder)
                        dispatchFinishedWhenDone()
                    }
                }
            )
            .start()
    }

    /**
     * Pre-set info of viewHolder which will be added into the list
     */
    override fun animateAdd(holder: RecyclerView.ViewHolder?): Boolean {
        resetAnimation(holder, TYPE_ADD)
        holder?.apply {
            itemView.alpha = 0f
        }?.let {
            mPendingAdditions.add(it)
        }
        return true
    }

    /**
     * Implementation of add animation
     */
    open fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        mAddAnimations.add(holder)
        animation.alpha(1f).setDuration(addDuration)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchAddStarting(holder)
                }

                override fun onAnimationCancel(animator: Animator) {
                    ViewHelperUtil.clear(view)
                }

                override fun onAnimationEnd(animator: Animator) {
                    ViewHelperUtil.clear(view)
                    dispatchAddFinished(holder)
                    mAddAnimations.remove(holder)
                    dispatchFinishedWhenDone()
                }
            }).start()
    }

    /**
     * Pre-set info of viewHolder which is to move
     */
    override fun animateMove(
        holder: RecyclerView.ViewHolder?,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        var fromX = fromX
        var fromY = fromY
        val view: View? = holder?.itemView
        view?.let {
            fromX += it.translationX.toInt()
            fromY += it.translationY.toInt()
        }

        resetAnimation(holder, TYPE_MOVE)
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX == 0 && deltaY == 0) {
            dispatchMoveFinished(holder)
            return false
        }
        if (deltaX != 0) {
            view?.translationX = -deltaX.toFloat()
        }
        if (deltaY != 0) {
            view?.translationY = -deltaY.toFloat()
        }
        mPendingMoves.add(
            MoveInfo(
                holder,
                fromX,
                fromY,
                toX,
                toY
            )
        )
        return true
    }

    /**
     * Implementation of move animation
     */
    open fun animateMoveImpl(
        holder: RecyclerView.ViewHolder?,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ) {
        val view = holder?.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX != 0) {
            view?.animate()?.translationX(0f)
        }
        if (deltaY != 0) {
            view?.animate()?.translationY(0f)
        }
        // TODO: make EndActions end listeners instead, since end actions aren't called when
        // vpas are canceled (and can't end them. why?)
        // need listener functionality in VPACompat for this. Ick.
        val animation = view?.animate()
        mMoveAnimations.add(holder)
        animation?.setDuration(moveDuration)?.setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                dispatchMoveStarting(holder)
            }

            override fun onAnimationCancel(animator: Animator) {
                if (deltaX != 0) {
                    view.translationX = 0f
                }
                if (deltaY != 0) {
                    view.translationY = 0f
                }
            }

            override fun onAnimationEnd(animator: Animator) {
                animation.setListener(null)
                dispatchMoveFinished(holder)
                mMoveAnimations.remove(holder)
                dispatchFinishedWhenDone()
            }
        })?.start()
    }

    /**
     * Pre-set info of viewHolder which is to change
     */
    override fun animateChange(
        oldHolder: RecyclerView.ViewHolder?,
        newHolder: RecyclerView.ViewHolder?,
        fromLeft: Int,
        fromTop: Int,
        toLeft: Int,
        toTop: Int
    ): Boolean {
        if (oldHolder === newHolder) {
            // Don't know how to run change animations when the same view holder is re-used.
            // run a move animation to handle position changes.
            return animateMove(oldHolder, fromLeft, fromTop, toLeft, toTop)
        }
        oldHolder?.let {
            val prevTranslationX = oldHolder.itemView.translationX
            val prevTranslationY = oldHolder.itemView.translationY
            val prevAlpha = oldHolder.itemView.alpha
            resetAnimation(oldHolder, TYPE_CHANGE)
            val deltaX = (toLeft - fromLeft - prevTranslationX).toInt()
            val deltaY = (toTop - fromTop - prevTranslationY).toInt()
            // recover prev translation state after ending animation
            oldHolder.itemView.apply {
                translationX = prevTranslationX
                translationY = prevTranslationY
                alpha = prevAlpha
            }
            newHolder?.let {
                // carry over translation values
                resetAnimation(newHolder, TYPE_CHANGE)
                newHolder.itemView.apply {
                    translationX = -deltaX.toFloat()
                    translationY = -deltaY.toFloat()
                    alpha = 0f
                }
            }
        }

        mPendingChanges.add(ChangeInfo(oldHolder, newHolder, fromLeft, fromTop, toLeft, toTop))
        return true
    }

    /**
     * Implementation of change animation
     */
    open fun animateChangeImpl(changeInfo: ChangeInfo) {
        val holder = changeInfo.oldHolder
        val view = holder?.itemView
        val newHolder = changeInfo.newHolder
        val newView = newHolder?.itemView
        if (view != null) {
            val oldViewAnim = view.animate().setDuration(changeDuration)
            mChangeAnimations.add(changeInfo.oldHolder)
            oldViewAnim.translationX(changeInfo.toX - changeInfo.fromX.toFloat())
            oldViewAnim.translationY(changeInfo.toY - changeInfo.fromY.toFloat())
            oldViewAnim.alpha(0f).setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchChangeStarting(changeInfo.oldHolder, true)
                }

                override fun onAnimationEnd(animator: Animator) {
                    oldViewAnim.setListener(null)
                    view.alpha = 1f
                    view.translationX = 0f
                    view.translationY = 0f
                    dispatchChangeFinished(changeInfo.oldHolder, true)
                    mChangeAnimations.remove(changeInfo.oldHolder)
                    dispatchFinishedWhenDone()
                }
            }).start()
        }
        if (newView != null) {
            val newViewAnimation = newView.animate()
            mChangeAnimations.add(changeInfo.newHolder)
            newViewAnimation.translationX(0f).translationY(0f).setDuration(changeDuration)
                .alpha(1f).setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animator: Animator) {
                        dispatchChangeStarting(changeInfo.newHolder, false)
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        newViewAnimation.setListener(null)
                        newView.alpha = 1f
                        newView.translationX = 0f
                        newView.translationY = 0f
                        dispatchChangeFinished(changeInfo.newHolder, false)
                        mChangeAnimations.remove(changeInfo.newHolder)
                        dispatchFinishedWhenDone()
                    }
                }).start()
        }
    }

    override fun isRunning(): Boolean {
        return (mPendingAdditions.isNotEmpty()
            || mPendingChanges.isNotEmpty()
            || mPendingMoves.isNotEmpty()
            || mPendingRemovals.isNotEmpty()
            || mMoveAnimations.isNotEmpty()
            || mRemoveAnimations.isNotEmpty()
            || mAddAnimations.isNotEmpty()
            || mChangeAnimations.isNotEmpty()
            || mMovesList.isNotEmpty()
            || mAdditionsList.isNotEmpty()
            || mChangesList.isNotEmpty())
    }

    private fun endChangeAnimation(
        infoList: MutableList<ChangeInfo>,
        item: RecyclerView.ViewHolder
    ) {
        for (i in infoList.indices.reversed()) {
            val changeInfo = infoList[i]
            if (endChangeAnimationIfNecessary(changeInfo, item)) {
                if (changeInfo.oldHolder == null && changeInfo.newHolder == null) {
                    infoList.remove(changeInfo)
                }
            }
        }
    }

    private fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo) {
        changeInfo.oldHolder?.let {
            endChangeAnimationIfNecessary(changeInfo, it)
        }
        changeInfo.newHolder?.let {
            endChangeAnimationIfNecessary(changeInfo, it)
        }
    }

    private fun endChangeAnimationIfNecessary(
        changeInfo: ChangeInfo,
        item: RecyclerView.ViewHolder
    ): Boolean {
        var oldItem = false
        when {
            changeInfo.newHolder === item -> {
                changeInfo.newHolder = null
            }
            changeInfo.oldHolder === item -> {
                changeInfo.oldHolder = null
                oldItem = true
            }
            else -> {
                return false
            }
        }
        item.itemView.apply {
            alpha = 1f
            translationX = 0f
            translationY = 0f
        }
        dispatchChangeFinished(item, oldItem)
        return true
    }

    override fun endAnimation(item: RecyclerView.ViewHolder) {
        val view = item.itemView
        // this will trigger end callback which should set properties to their target values.
        view.animate().cancel()
        // TODO if some other animations are chained to end, how do we cancel them as well?
        for (i in mPendingMoves.indices.reversed()) {
            val moveInfo: MoveInfo = mPendingMoves[i]
            if (moveInfo.holder === item) {
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(item)
                mPendingMoves.removeAt(i)
            }
        }
        endChangeAnimation(mPendingChanges, item)
        if (mPendingRemovals.remove(item)) {
            ViewHelperUtil.clear(view)
            dispatchRemoveFinished(item)
        }
        if (mPendingAdditions.remove(item)) {
            ViewHelperUtil.clear(view)
            dispatchAddFinished(item)
        }

        for (i in mChangesList.indices.reversed()) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            endChangeAnimation(changes, item)
            if (changes.isEmpty()) {
                mChangesList.removeAt(i)
            }
        }
        for (i in mMovesList.indices.reversed()) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            for (j in moves.indices.reversed()) {
                val moveInfo = moves[j]
                if (moveInfo.holder === item) {
                    view.translationY = 0f
                    view.translationX = 0f
                    dispatchMoveFinished(item)
                    moves.removeAt(j)
                    if (moves.isEmpty()) {
                        mMovesList.removeAt(i)
                    }
                    break
                }
            }
        }
        for (i in mAdditionsList.indices.reversed()) {
            val additions = mAdditionsList[i]
            if (additions.remove(item)) {
                ViewHelperUtil.clear(view)
                dispatchAddFinished(item)
                if (additions.isEmpty()) {
                    mAdditionsList.removeAt(i)
                }
            }
        }

        // animations should be ended by the cancel above.
        // noinspection PointlessBooleanExpression,ConstantConditions
        check(!(mRemoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                + "mRemoveAnimations list")
        }

        // noinspection PointlessBooleanExpression,ConstantConditions
        check(!(mAddAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                + "mAddAnimations list")
        }

        // noinspection PointlessBooleanExpression,ConstantConditions
        check(!(mChangeAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                + "mChangeAnimations list")
        }

        // noinspection PointlessBooleanExpression,ConstantConditions
        check(!(mMoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                + "mMoveAnimations list")
        }
        dispatchFinishedWhenDone()
    }

    override fun endAnimations() {
        var count = mPendingMoves.size
        for (i in count - 1 downTo 0) {
            val item: MoveInfo = mPendingMoves[i]
            item.holder?.itemView?.let {
                it.translationY = 0f
                it.translationX = 0f
            }
            dispatchMoveFinished(item.holder)
            mPendingMoves.removeAt(i)
        }

        count = mPendingRemovals.size
        for (i in count - 1 downTo 0) {
            val item = mPendingRemovals[i]
            dispatchRemoveFinished(item)
            mPendingRemovals.removeAt(i)
        }

        count = mPendingAdditions.size
        for (i in count - 1 downTo 0) {
            val item = mPendingAdditions[i]
            ViewHelperUtil.clear(item.itemView)
            dispatchAddFinished(item)
            mPendingAdditions.removeAt(i)
        }

        count = mPendingChanges.size
        for (i in count - 1 downTo 0) {
            endChangeAnimationIfNecessary(mPendingChanges[i])
        }
        mPendingChanges.clear()
        if (!isRunning) {
            return
        }

        var listCount = mMovesList.size
        for (i in listCount - 1 downTo 0) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            count = moves.size
            for (j in count - 1 downTo 0) {
                val moveInfo = moves[j]
                val item = moveInfo.holder
                item?.itemView?.let {
                    it.translationY = 0f
                    it.translationX = 0f
                }
                dispatchMoveFinished(moveInfo.holder)
                moves.removeAt(j)
                if (moves.isEmpty()) {
                    mMovesList.remove(moves)
                }
            }
        }

        listCount = mAdditionsList.size
        for (i in listCount - 1 downTo 0) {
            val additions = mAdditionsList[i]
            count = additions.size
            for (j in count - 1 downTo 0) {
                val item = additions[j]
                val view = item.itemView
                view.alpha = 1f
                dispatchAddFinished(item)
                additions.removeAt(j)
                if (additions.isEmpty()) {
                    mAdditionsList.remove(additions)
                }
            }
        }

        listCount = mChangesList.size
        for (i in listCount - 1 downTo 0) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            count = changes.size
            for (j in count - 1 downTo 0) {
                endChangeAnimationIfNecessary(changes[j])
                if (changes.isEmpty()) {
                    mChangesList.remove(changes)
                }
            }
        }

        cancelAll(mRemoveAnimations)
        cancelAll(mMoveAnimations)
        cancelAll(mAddAnimations)
        cancelAll(mChangeAnimations)

        dispatchAnimationsFinished()
    }

    /**
     * Check the state of currently pending and running animations. If there are none
     * pending/running, call [.dispatchAnimationsFinished] to notify any
     * listeners.
     */
    fun dispatchFinishedWhenDone() {
        if (!isRunning) {
            dispatchAnimationsFinished()
        }
    }

    private fun resetAnimation(holder: RecyclerView.ViewHolder?, type: Int) {
        if (mInterpolator == null) {
            mInterpolator = ValueAnimator().interpolator
        }

        holder?.itemView?.animate()?.interpolator = when (type) {
            TYPE_ADD -> if (mAddInterpolator == null) mInterpolator else mAddInterpolator
            TYPE_REMOVE -> if (mRemoveInterpolator == null) mInterpolator else mRemoveInterpolator
            TYPE_MOVE -> if (mMoveInterpolator == null) mInterpolator else mMoveInterpolator
            TYPE_CHANGE -> if (mChangeInterpolator == null) mInterpolator else mInterpolator
            else -> mInterpolator
        }
        holder?.let {
            endAnimation(it)
        }
    }

    /**
     * Cancel each viewHolder's ViewPropertyAnimator's animation
     */
    fun cancelAll(viewHolders: List<RecyclerView.ViewHolder?>) {
        for (i in viewHolders.indices.reversed()) {
            viewHolders[i]?.itemView?.animate()?.cancel()
        }
    }

    /**
     * Post move animation. Override this method can change animation delay
     */
    open fun moveAnimationPost(itemView: View, runnable: Runnable, delay: Long) {
        ViewCompat.postOnAnimationDelayed(itemView, runnable, delay)
    }

    /**
     * Post change animation. Override this method can change animation delay
     */
    open fun changeAnimationPost(itemView: View, runnable: Runnable, delay: Long) {
        ViewCompat.postOnAnimationDelayed(itemView, runnable, delay)
    }

    /**
     * Post add animation. Override this method can change animation delay
     */
    open fun addAnimationPost(itemView: View, runnable: Runnable, delay: Long) {
        ViewCompat.postOnAnimationDelayed(itemView, runnable, delay)
    }

    /**
     * Set a default interpolator for this ItemAnimator
     *
     * @param interpolator Default interpolator
     */
    fun setInterpolator(interpolator: TimeInterpolator) {
        mInterpolator = interpolator
    }

    /**
     * Set specific interpolator for specific animation process
     *
     * @param interpolator Specific interpolator
     * @param type         Specific animation process
     */
    fun setInterpolator(interpolator: TimeInterpolator, type: Int) {
        when (type) {
            TYPE_ADD -> mAddInterpolator = interpolator
            TYPE_REMOVE -> mRemoveInterpolator = interpolator
            TYPE_MOVE -> mMoveInterpolator = interpolator
            TYPE_CHANGE -> mChangeInterpolator = interpolator
        }
    }
}