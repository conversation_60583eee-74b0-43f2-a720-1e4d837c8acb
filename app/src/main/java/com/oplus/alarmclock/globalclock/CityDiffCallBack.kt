/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CityDiffCallBack.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/3/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/3/25     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.text.TextUtils
import androidx.recyclerview.widget.DiffUtil

class CityDiffCallBack(private val mOldData: List<com.oplus.alarmclock.globalclock.City>?, private val mNewData: List<com.oplus.alarmclock.globalclock.City>?) : DiffUtil.Callback() {


    override fun getOldListSize(): Int {
        return mOldData?.size ?: 0
    }

    override fun getNewListSize(): Int {
        return mNewData?.size ?: 0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return mOldData?.get(oldItemPosition)?.cityId == mNewData?.get(newItemPosition)?.cityId
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {

        val oldCity = mOldData?.get(oldItemPosition)
        val newCity = mNewData?.get(newItemPosition)
        return ((oldCity?.cityId == newCity?.cityId)
                && TextUtils.equals(oldCity?.name, newCity?.name)
                && TextUtils.equals(oldCity?.country, newCity?.country)
                && TextUtils.equals(oldCity?.timezone, newCity?.timezone)
                && (oldCity?.rawId == newCity?.rawId)
                && (oldCity?.sortPos == newCity?.sortPos)
                && (oldCity?.flag == newCity?.flag)
                && (oldCity?.flag2 == newCity?.flag2)
                && (oldCity?.isSelected == newCity?.isSelected)
                && (oldCity?.displayPosition == newCity?.displayPosition))
    }
}