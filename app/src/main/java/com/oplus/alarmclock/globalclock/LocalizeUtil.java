/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.globalclock;

import android.text.TextUtils;

import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.clock.common.utils.Log;

import java.util.Locale;

public class LocalizeUtil {

    private static final String TAG = "LocalizeUtil";

    private static Locale sCurrentLocale;
    private static boolean sIsCurrentLangTranslated;

    private static Locale sCurrentSpecialLocale;
    private static String sCurrentSpecialLocalString;

    public static boolean isTranslated(Locale locale) {
        if ((locale != null) && !locale.equals(sCurrentLocale)) {
            sCurrentLocale = locale;
            String lang = locale.toString();
            sIsCurrentLangTranslated = false;
            String[] translations = getTranslations();
            for (String temp : translations) {
                if (temp.equals(lang)) {
                    sIsCurrentLangTranslated = true;
                    break;
                }
            }
        }
        return sIsCurrentLangTranslated;
    }

    /**
     * 当 Locale.getDefault 返回的是 zh_TW_#Hant 这种值时，之前的判断失效
     * 需要将 locale 中的 language 和 country 提取出来再进行一次判断
     * 并将正确的 locale string 返回
     */
    public static String isTranslatedForSpecialLocale(Locale locale) {
        if ((locale != null) && !locale.equals(sCurrentSpecialLocale)) {
            sCurrentSpecialLocale = locale;
            String lang = locale.getLanguage() + "_" + locale.getCountry();
            String languageTag = locale.toLanguageTag();
            sCurrentSpecialLocalString = null;
            Log.d(TAG, "deal special locale " + locale + " after " + lang + " languageTag: " + languageTag);
            String[] translations = getTranslations();
            for (String lanAndRegion : translations) {
                if (lanAndRegion.equals(lang)) {
                    sCurrentSpecialLocalString = lanAndRegion;
                    break;
                }

            }

            if ((!TextUtils.isEmpty(sCurrentSpecialLocalString)) && (ClockContract.City.ZH_NAME.equals(sCurrentSpecialLocalString))) {
                if (isSimplifiedChinese(locale)) {
                    sCurrentSpecialLocalString = ClockContract.City.ZH_NAME;
                } else if (isTraditionalChinese(locale)) {
                    if (languageTag.contains("HK")) {
                        sCurrentSpecialLocalString = ClockContract.City.HK_NAME;
                    } else {
                        sCurrentSpecialLocalString = ClockContract.City.TW_NAME;
                    }
                }
            }

            if (TextUtils.isEmpty(sCurrentSpecialLocalString)) {
                for (String lanAndRegion : translations) {
                    //适配选择语言回归原生, 语言和区域可以任意组合，用 Hans 和 Hant 区域是简体还是繁体中文。只用语言判断，再特殊判断简体和繁体中文
                    String[] temp = TextUtils.split(lanAndRegion, "_");
                    String language = temp[0];
                    if (language.equals(locale.getLanguage())) {
                        if (isSimplifiedChinese(locale)) {
                            sCurrentSpecialLocalString = ClockContract.City.ZH_NAME;
                        } else if (isTraditionalChinese(locale)) {
                            if (languageTag.contains("HK")) {
                                sCurrentSpecialLocalString = ClockContract.City.HK_NAME;
                            } else {
                                sCurrentSpecialLocalString = ClockContract.City.TW_NAME;
                            }
                        } else {
                            sCurrentSpecialLocalString = lanAndRegion;
                        }
                        break;
                    }
                }
            }
        }
        return sCurrentSpecialLocalString;
    }

    // 是否为简体中文
    public static boolean isSimplifiedChinese(Locale locale) {
        String languageTag = locale.toLanguageTag();
        if (languageTag.contains("zh-Hans")
                || languageTag.equalsIgnoreCase("zh-CN") // 简体中文 大陆
                || languageTag.equalsIgnoreCase("zh-SG") // 简体中文 新加坡
                || languageTag.equalsIgnoreCase("zh-MY")) { // 简体中文 马来西亚
            return true;
        }
        return false;
    }

    // 是否为繁体中文
    public static boolean isTraditionalChinese(Locale locale) {
        String languageTag = locale.toLanguageTag();
        if (languageTag.contains("zh-Hant")
                || languageTag.equalsIgnoreCase("zh-HK")
                || languageTag.equalsIgnoreCase("zh-TW")
                || languageTag.equalsIgnoreCase("zh-MO")) {
            return true;
        }
        return false;
    }

    /**
     * 城市数据库中已支持的语言
     * my es de pt, zh-hant 这几种语言，在区域任意选择的时候，需要有个优先级避免查询城市的语言和本地语言不一致。亲测
     * 繁体中文 zh-hant 默认 zh_TW
     * 缅甸语 my_ZG ,my_MM 默认 my_MM
     * 西班牙语 es_ES, es_MX 默认 es_ES
     * 德语 de_DE de_CH 默认 de_DE
     * 葡萄牙语 pt_PT, pt_BR 默认 pt_PT
     * 在这个列表中，把默认的放前面，就会先取到
     */
    public static String[] getTranslations() {
        return new String[]{
                "ar_EG", "as_IN", "bn_BD", "bo_CN", "fa_IR",
                "fr_FR", "gu_IN", "hi_IN", "in_ID", "kk_KZ",
                "km_KH", "kn_IN", "lo_LA", "ml_IN", "mr_IN",
                "ms_MY", "my_MM", "my_ZG", "ne_NP", "or_IN",
                "pa_IN", "ru_RU", "si_LK", "sw_KE", "ta_IN",
                "te_IN", "th_TH", "fil_PH", "ur_PK", "vi_VN",
                "ko_KR", "ja_JP", "cs_CZ", "ro_RO", "es_ES",
                "es_MX", "de_DE", "it_IT", "nb_NO", "sv_SE",
                "nl_NL", "el_GR", "hu_HU", "pl_PL", "pt_PT",
                "pt_BR", "tr_TR", "ug_CN", "uk_UA", "uz_UZ",
                "zh_TW", "iw_IL", "sr_RS_#Latn", "de_CH",
                "zh_CN", "zh_HK", "en_US", "ca_ES", "gl_ES",
                "da_DK", "eu_ES", "sk_SK", "bg_BG", "fi_FI",
                "hr_HR", "sl_SI"
        };
    }
}
