/*
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: GlobalTime2.java
 * ModuleName: GlobalTime Author: MaCong Create Date: Description: the main activity
 * <p>
 * History: <version > <time> <author> <desc>
 *     1.0 2010-9-24 MaCong CheckList
 *     1.1 2018-8-22 <PERSON><PERSON><PERSON> recode
 */
package com.oplus.alarmclock.globalclock;

import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentProviderOperation;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.AsyncTask;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.MeasureSpec;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.widget.CompoundButton;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.COUICollapsingToolbarLayout;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;
import com.oplus.alarmclock.globalclock.view.ScrollGridLayoutManager;
import com.oplus.alarmclock.globalclock.view.ScrollLinearLayoutManager;
import com.oplus.alarmclock.globalclock.view.TalkBackListener;
import com.oplus.alarmclock.mvvm.worldclock.WorldClockViewModel;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.ClockProvider;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.CityAdapterSpaceItemDecoration;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FloatingButtonTool;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.GlobalDigitalClock;
import com.oplus.alarmclock.view.dial.AlarmDialClockManager;
import com.oplus.alarmclock.view.dial.ShadowManager;
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public abstract class WorldClockViewFragment<T extends ViewDataBinding>
        extends WorldClockBaseFragment<T>
        implements DragListener,
        CompoundButton.OnCheckedChangeListener,
        View.OnClickListener,
        View.OnLongClickListener,
        CityListAdapter.OnCityItemClickListener,
        AlarmClock.NavigationDelete,
        IAfterMove,
        TalkBackListener.Listener {

    public static final int TAG_ITEM_CITY = R.id.city_name;

    public static final int ITEM_VIEW_HOLDER_ID = R.id.select;

    public static final float ALPHA_10 = 1.0f;
    public static final float ALPHA_03 = 0.3f;

    public static final int MODEL_EDIT = 0x1;
    public static final Long HOVER_DELAY = 200L;
    public static final int MODEL_NOML = 0x2;

    /**
     * 表盘下字体大小
     */
    protected static final float TIME_VIEW_TEXT_SIZE_LARGE = 14;
    protected static final float TIME_VIEW_TEXT_SIZE = 12;

    private static final int TIME_DURATION = 200;
    private static final int EMPTY_SELESCT_ID = -1;

    private static final String TAG = "WorldClockViewFragment";
    public final Set<Integer> mSelectedIds = new HashSet<>();
    public CityListAdapter mListAdapter;
    public WorldClockViewModel mViewModel;
    public int mMode = MODEL_NOML;
    public ItemTouchHelper mItemTouchHelper;
    public ScrollLinearLayoutManager mLayoutManager;
    public ValueAnimator mSmoothScrollAnimator;
    public FloatingButtonTool mFloatingButtonTool;
    public WorldClockAnimationManager mClockManager;
    public WorldClockListManager mWorldClockListManager;
    public ShadowManager mShadowManager;
    public AlarmDialClockManager mAlarmDialClockManager;
    public int mItemHeight = 0;
    public TalkBackListener mTalkBackListener;
    public ScrollGridLayoutManager mGridLayoutManager;
    public int mCityListTopMargin;
    public int mPaddingHeight;
    private boolean mIsFirstEnter = true;
    private int mClickMenuId;
    private boolean mNotInitEffect;

    private int startEditModel(View view) {
        if (mMode == MODEL_EDIT) {
            return 0;
        }
        Object obj = view.getTag(TAG_ITEM_CITY);
        if ((obj != null) && (obj instanceof City)) {
            int id = ((City) obj).getCityId();
            changeMode(MODEL_EDIT, ((City) obj).getCityId());
            return id;
        }
        return 0;
    }

    @Override
    protected boolean refreshUiImmediatelyForSort() {
        return true;
    }

    @Override
    protected void onEditMenuSelected() {
        mClickMenuId = R.id.edit;
    }

    @Override
    protected void initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup group) {
        super.initView(inflater, group);
        if (group != null) {
            mContext = group.getContext();
        } else {
            mContext = inflater.getContext();
        }
        initDialClock();
        initViewModel();
        initTalkBack();
        initTopMargin(false);
        initListView();
        initializeFloatingButton();
        initHoverIfNeed();
        initClockEffect();
        if (couiToolbar() != null) {
            couiToolbar().setOnMenuItemClickListener(menuItem -> {
                onOptionsItemSelected(menuItem);
                return false;
            });
        }
    }

    public void popupWindowOnDismiss() {
        if (mClickMenuId == R.id.edit) {
            changeMode(MODEL_EDIT, EMPTY_SELESCT_ID);
        }
        mClickMenuId = 1;
    }

    public void redDotSetting() {
        if (couiToolbar() != null) {
            redDotSetting(couiToolbar());
        }
    }

    /**
     * 初始化光追时钟
     */
    protected void initClockEffect() {
        if (mShadowManager != null) {
            if (Utils.isAboveOS14()) {
                if (mIsFirstEnter && mNotInitEffect) {
                    mShadowManager.startAnimation(mAlarmDialClockManager);
                    mIsFirstEnter = !mIsFirstEnter;
                    mNotInitEffect = false;
                }
            }
        }
    }


    @Override
    public void initData() {
        super.initData();
        loadZoneMsg();
    }

    private void initViewModel() {
        mViewModel = new ViewModelProvider(this).get(WorldClockViewModel.class);
        mViewModel.getMZoneData().observe(this.getViewLifecycleOwner(), this::setTimeInfo);
    }

    private void initTalkBack() {
        if (mTalkBackListener == null) {
            mTalkBackListener = new TalkBackListener();
            mTalkBackListener.setListener(this);
        }
    }

    protected void initTopMargin(boolean isHover) {
        ConstraintLayout worldClockCl = worldClockCl();
        if (getContext() != null && worldClockCl != null) {
            worldClockCl.setPadding(0, Utils.getStatusBarHeight(getContext()), 0, 0);
        }
    }

    protected void initDialClock() {
        mAlarmDialClockManager = new AlarmDialClockManager();
    }

    protected void initListView() {
        COUIRecyclerView cityList = cityListView();
        Context context = this.getContext();
        if (cityList != null && context != null) {
            setRecyclerViewLayoutManager(cityList, false, context);
            cityList.addItemDecoration(new CityAdapterSpaceItemDecoration(false));
            mListAdapter = new CityListAdapter(context, this, getUiMode());
            mListAdapter.setRecyclerView(cityList);
            mListAdapter.setItemLongClickListener(this);
            mListAdapter.setMultiSelect(false);
            setListAdapterProp();
            cityList.setOnLongClickListener(null);
            cityList.setItemViewCacheSize(0);
            cityList.setAdapter(mListAdapter);
            cityList.setNestedScrollingEnabled(true);
            cityList.setLongClickable(true);
            cityList.setClickable(true);
            cityList.setItemAnimator(new ListItemRemovedAnimator());
            mItemTouchHelper = new ItemTouchHelper(
                    new SimpleItemTouchHelperCallBack(context, mListAdapter, this));

            COUIRecyclerView cityListEdit = cityListEditView();
            if (cityListEdit == null) {
                mItemTouchHelper.attachToRecyclerView(cityList);
            } else {
                mItemTouchHelper.attachToRecyclerView(cityListEdit);
                setEditRecyclerViewLayoutManager(mContext);
                cityListEdit.setItemViewCacheSize(0);
                cityListEdit.setAdapter(mListAdapter);
                cityListEdit.addItemDecoration(
                        new CityAdapterSpaceItemDecoration(false));
            }
        }
    }

    /**
     * 初始化悬浮按钮
     **/
    private void initializeFloatingButton() {
        if (mFloatingButtonTool == null) {
            mFloatingButtonTool = new FloatingButtonTool();
        }
        if (getContext() != null) {
            mFloatingButtonTool.setFloatingButton(
                    AlarmClock.TAB_INDEX_GLOBALCITY,
                    getContext(),
                    floatingButton(),
                    getString(R.string.global_add_city));

            if ((mListAdapter != null) && (mListAdapter.getList() != null)) {
                mFloatingButtonTool.updateFloatingButton(
                        getContext(),
                        floatingButton(),
                        mMode != WorldClockViewFragment.MODEL_EDIT,
                        mListAdapter.getList().size() < WorldClockBaseFragment.MAX_CITY_NUM);
            }
        }
    }

    /**
     * 当悬停模式进入时，初始化悬停UI，折叠屏生效(UiMode=Normal, Middle)
     */
    protected void initHoverIfNeed() {
    }

    @Override
    protected String getTitle() {
        return AlarmClockApplication.getInstance().getResources().getString(R.string.global_time_title);
    }

    @Override
    public void onFocused(boolean focused) {
        if (focused) {
            updateClockRefresh(cityListView(), true);
        }
    }

    @Override
    public void onPreChangeTab() {
    }

    private void loadZoneMsg() {
        if (mViewModel != null) {
            mViewModel.loadZoneData();
        }
    }

    private void updateClockRefresh(ViewGroup viewGroup, boolean refresh) {
        if (viewGroup != null) {
            final int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View view = viewGroup.getChildAt(i).findViewById(R.id.global_addcity_time);
                if ((view != null) && (view instanceof GlobalDigitalClock)) {
                    GlobalDigitalClock clock = (GlobalDigitalClock) view;
                    clock.setTimeZoneID(clock.getTimeZoneID());
                }
            }
        }
    }

    public void onStatusBarClicked() {
        COUIRecyclerView cityList = cityListView();
        COUIRecyclerView cityListEdit = cityListEditView();
        if ((cityList != null) && (cityList.getVisibility() == View.VISIBLE)) {
            scrollToTop(cityList);
        } else if ((cityListEdit != null) && (cityListEdit.getVisibility() == View.VISIBLE)) {
            scrollToTop(cityListEdit);
        }
        if (mAddCityDialog != null && mAddCityDialog.isShowing()) {
            mAddCityDialog.onStatusBarClicked();
        }
    }

    /**
     * 关闭添加闹钟弹窗
     */
    public void dismissAddCityDialog() {
        if (mAddCityDialog != null && mAddCityDialog.isShowing()) {
            mAddCityDialog.dismiss();
        }
    }

    @Override
    public void onEditSelectedAll() {
        if (isListAnimating()) {
            Log.d(TAG, "doQuickSelect list is animating");
            return;
        }
        Log.i(TAG, "mSelectCheckBox select all by menu");
        doQuickSelect();
    }

    @Override
    protected void onEditCancel() {
        changeMode(MODEL_NOML, EMPTY_SELESCT_ID);
    }

    private void changeMode(int mode, int cityId) {
        changeMode(mode, cityId, true);
    }

    private void changeMode(int mode, int cityId, boolean isNotify) {
        if (mListAdapter == null) {
            Log.e(TAG, "change model list adapter is null!");
            return;
        }
        mMode = mode;
        mSelectedIds.clear();
        if (cityId != EMPTY_SELESCT_ID) {
            mSelectedIds.add(cityId);
        }
        Activity activity = getActivity();
        if (couiToolbar() != null) {
            couiToolbar().getMenu().close();
            couiToolbar().getMenu().clear();
        }

        if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)) {
            mListAdapter.clearEditCitySelect(cityId);
            if (mode == MODEL_EDIT) {
                mListAdapter.changeMode(MODEL_EDIT, isNotify);
                mListAdapter.setEditCheckedChangeListener(this);
                mListAdapter.setOnItemClickListener(this);
                mListAdapter.setIsEdit(true);
                ((AlarmClock) activity).showNavigation();
                if (couiToolbar() != null) {
                    couiToolbar().setIsTitleCenterStyle(true);
                    couiToolbar().inflateMenu(R.menu.menu_edit_mode);
                }
                updateMenuText();
            } else {
                mListAdapter.changeMode(MODEL_NOML, isNotify);
                mListAdapter.setIsEdit(false);
                mListAdapter.setItemLongClickListener(this);
                ((AlarmClock) activity).dismissNavigation();
                if (couiToolbar() != null) {
                    couiToolbar().setIsTitleCenterStyle(false);
                    couiToolbar().setTitle(" ");
                    couiToolbar().inflateMenu(R.menu.action_menu_icon_all);
                }
            }
            AlarmClock.correctAllMenuItemFromFragment(activity);
        }
        changeMenu(mode);
        if ((mListAdapter.getList() != null) && (mFloatingButtonTool != null)) {
            mFloatingButtonTool.updateFloatingButton(
                    mContext,
                    floatingButton(),
                    !(mMode == MODEL_EDIT),
                    mListAdapter.getList().size() < WorldClockBaseFragment.MAX_CITY_NUM);
        }
        changeList(mode);
    }


    private void deleteWorldClock() {
        Integer[] cityIds = mSelectedIds.toArray(new Integer[0]);
        CityUtils.asyncDeleteCities(AlarmClockApplication.getInstance().getApplicationContext(), cityIds, true/*play effect sound*/);
    }

    public void doQuickSelect() {
        if (mListAdapter == null) {
            Log.e(TAG, "change model list adapter is null!");
            return;
        }
        int listCount = mListAdapter.getList().size();
        final boolean hasSelecteAll = mSelectedIds.size() == listCount;
        final boolean select = !hasSelecteAll;
        doQuickSelect(mListAdapter.getList(), select);
        mListAdapter.notifyItemRangeChanged(0, mListAdapter.getList().size(), ClockConstant.CHECKBOX_STATE);
        updateMenuText();
    }

    /**
     * 更新顶部菜单文字
     */
    private void updateMenuText() {
        if (couiToolbar() != null) {
            MenuItem menuItem = couiToolbar().getMenu().findItem(R.id.select_all_clock);
            int listCount = mListAdapter.getList().size();
            if (menuItem != null) {
                if ((mSelectedIds.size() == listCount)) {
                    menuItem.setTitle(getString(R.string.unselect_all_text));
                } else {
                    menuItem.setTitle(getString(R.string.select_all));
                }
            }
        }
    }

    private void doQuickSelect(List<City> list, boolean select) {
        mSelectedIds.clear();
        if (list != null) {
            for (City city : list) {
                city.setSelected(select);
                if (select) {
                    mSelectedIds.add(city.getCityId());
                }
            }
        }
        udpateEditMenuAndTitle();
    }

    protected void listViewBacktoTop() {
        COUIRecyclerView cityList = cityListView();
        if ((cityList != null) && (cityList.getChildCount() > 0)) {
            int distance = computeScrollDistance();
            if (distance == 0) {
                return;
            }
            if (mSmoothScrollAnimator != null) {
                mSmoothScrollAnimator.cancel();
                mSmoothScrollAnimator = null;
            }
            mSmoothScrollAnimator = ValueAnimator.ofInt(0, -distance).setDuration(TIME_DURATION);
            mSmoothScrollAnimator.addUpdateListener(new AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    if (animation != null) {
                        Object object = animation.getAnimatedValue();
                        if ((object != null) && (object instanceof Integer)) {
                            int d = (Integer) object;
                            cityList.scrollBy(0, d);
                        }
                    }
                }
            });
            mSmoothScrollAnimator.start();
        }
    }

    private int computeScrollDistance() {
        if ((getActivity() == null) || (getActivity().isFinishing()) || (mLayoutManager == null)) {
            return 0;
        }
        if (cityListView() == null) {
            return 0;
        }
        int firstVisiblePosition = mLayoutManager.findFirstCompletelyVisibleItemPosition();
        View firstVisiView = cityListView().getChildAt(0);
        if (firstVisiblePosition == 0) {
            return 0;
        }
        int position = 0;
        int distance = 0;
        do {
            distance += getItemHeight();
            position++;
            // if start position not specified, set distance approaching the
            // height of list view.
        } while ((distance < cityListView().getHeight()) && (position < firstVisiblePosition - 1));

        int firstViewTop = firstVisiView.getTop();
        return cityListView().getPaddingTop() + distance - firstViewTop;
    }

    private int getItemHeight() {
        if (cityListView() == null) {
            return 0;
        }
        View itemView = getLayoutInflater().inflate(R.layout.global_city_list_item_view, cityListView(), false);
        int heightSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
        int widthSpec = MeasureSpec.makeMeasureSpec(
                cityListView().getWidth() - cityListView().getPaddingStart() - cityListView().getPaddingEnd(),
                MeasureSpec.AT_MOST);
        itemView.measure(widthSpec, heightSpec);
        return itemView.getMeasuredHeight();
    }

    @Override
    protected void onSystemTimeChanged() {
        loadZoneMsg();
        if (mTimeZoneChange) {
            WorldClockCSUtils.buriedPointTimeZoneChange(mContext);
        }
        if ((mListAdapter == null)) {
            return;
        }
        int itemSize = mListAdapter.getList().size();
        for (int i = 0; i < itemSize; i++) {
            View view = cityListView().getChildAt(i);
            if (view != null) {
                COUIRecyclerView.ViewHolder viewHolder = cityListView().getChildViewHolder(view);
                if (viewHolder instanceof CityListAdapter.ItemTouchViewHolder) {
                    CityListAdapter.ItemTouchViewHolder holder = (CityListAdapter.ItemTouchViewHolder) viewHolder;
                    String zoneId = mListAdapter.getList().get(i).getTimezone();
                    if (TextUtils.isEmpty(zoneId)) {
                        Log.d(TAG, "get zoneId from list is null");
                        zoneId = holder.mDigitalTime.getTimeZoneID();
                    }
                    holder.mDigitalTime.setTimeZoneID(zoneId);
                }
            }
        }
        mListAdapter.notifyDataSetChanged();
    }

    public void setModeToNormal() {
        if (mMode == MODEL_EDIT) {
            Log.i(TAG, "setModeToNormal");
            changeMode(MODEL_NOML, EMPTY_SELESCT_ID);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mListAdapter != null) {
            mListAdapter.clearResource();
        }
        if (cityListView() != null && cityListView().getHandler() != null) {
            cityListView().getHandler().removeCallbacksAndMessages(null);
        }
    }

    private void updateMenuAndEmptyView() {
        Activity activity = getActivity();
        if ((activity == null) || (activity.isFinishing()) || (!isAdded())) {
            return;
        }
        if (cityListView() == null) {
            return;
        }
        if (getCurrentCitiesCount() == 0) {
            cityListView().post(() -> cityListView().setVisibility(View.GONE));
        } else {
            updateLayout();
        }
        updateMenu();
    }

    private void updateLayout() {
        if (cityListView() != null) {
            cityListView().setVisibility(View.VISIBLE);
            cityListView().setAlpha(ALPHA_10);
        }
    }

    protected City getCityById(int cityId) {
        if (mListAdapter == null) {
            return null;
        }
        City target = null;
        List<City> list = mListAdapter.getList();
        if (list != null) {
            for (City city : list) {
                if (city.getCityId() == cityId) {
                    target = city;
                    break;
                }
            }
        }
        return target;
    }

    public void clearEdit() {
        changeMode(MODEL_NOML, EMPTY_SELESCT_ID);
    }

    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean checked) {
        Object object = compoundButton.getTag();
        if ((object != null) && (object instanceof Integer)) {
            int id = (Integer) object;
            setCitySelected(id, checked);
        } else {
            Log.e(TAG, "onCheckedChanged error: No alarm id found in view's tag!");
        }
    }

    @Override
    public boolean onLongClick(View view) {
        startEditModel(view);
        return true;
    }

    @Override
    public void onItemClick(View view, int position) {
        if (mListAdapter == null) {
            return;
        }
        Object object = view.getTag(ITEM_VIEW_HOLDER_ID);
        if ((object != null) && (object instanceof Integer)) {
            int id = (Integer) object;
            City city = getCityById(id);
            if (city != null) {
                setCitySelected(id, !city.isSelected());
                mListAdapter.notifyItemChanged(position, ClockConstant.CHECKBOX_STATE);
            }
        } else {
            Log.e(TAG, "onCheckedChanged error: No alarm id found in view's tag!");
        }
    }

    private void setCitySelected(int cityId, boolean checked) {
        City city = getCityById(cityId);
        if (city != null) {
            city.setSelected(checked);
        }

        if (checked) {
            mSelectedIds.add(cityId);
        } else {
            mSelectedIds.remove(cityId);
        }
        udpateEditMenuAndTitle();
        updateMenuText();
    }

    private void udpateEditMenuAndTitle() {
        AlarmClock.correctAllMenuItemFromFragment(getActivity());
        if (mMode != MODEL_EDIT) {
            return;
        }
        final int count = mSelectedIds.size();
        Activity activity = getActivity();
        if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)) {
            setToolbarTitle((AlarmClock) activity, count);
            if (couiToolbar() != null) {
                couiToolbar().setTitle(getToolbarTitle(count));
            }
            ((AlarmClock) activity).setNavigationItemEnable(count > 0, R.id.navigation_delete);
        }
    }

    public boolean onBackPressed() {
        if (isListAnimating()) {
            Log.d(TAG, "onBackPressed list is animating");
            return true;
        }
        if (mMode == MODEL_EDIT) {
            changeMode(MODEL_NOML, EMPTY_SELESCT_ID);
            return true;
        }
        return false;
    }

    private void changeMenu(int mMode) {
        if (mMode == MODEL_EDIT) {
            udpateEditMenuAndTitle();
        }
    }

    @Override
    public int getCurrentCitiesCount() {
        if (mListAdapter != null) {
            return mListAdapter.getList().size();
        }
        return 0;
    }

    /**
     * 是否展示编辑按钮
     */
    public void isShowEditMenu() {
        if (couiToolbar() != null) {
            MenuItem menuItem = couiToolbar().getMenu().findItem(R.id.edit);
            if (menuItem != null) {
                menuItem.setVisible(mListAdapter.getList().size() > 0);
            }
        }
    }

    @Override
    protected void onCitiesLoadFinished(Context context, ArrayList<City> list) {
        if (mListAdapter == null) {
            Log.e(TAG, "onCitiesLoadFinished list adapter is not init!");
            return;
        }
        if (!isHover()) {
            updateTimeDialView(list);
        } else {
            if (mClockManager != null) {
                boolean isCenter = list == null || list.size() <= 0;
                mClockManager.setIsToCenter(isCenter);
            }
        }
        updateCityListMargin();
        if ((mWorldClockListManager != null) && (list != null) && (list.isEmpty())) {
            mWorldClockListManager.resetNoList();
        }
        if ((mMode == MODEL_EDIT) && (list != null)) {
            if (mSelectedIds.isEmpty()) {
                for (City city : list) {
                    city.setSelected(false);
                }
            } else {
                Set<Integer> set = new HashSet<>();
                boolean selected = false;
                for (City city : list) {
                    int cityId = city.getCityId();
                    selected = mSelectedIds.contains(cityId);
                    Log.d(TAG, "onCitiesLoadFinished, contains: " + cityId + ": " + selected);
                    city.setSelected(selected);
                    if (selected) {
                        set.add(cityId);
                    }
                }
                mSelectedIds.clear();
                mSelectedIds.addAll(set);
            }
        }

        if ((list != null) && (!list.isEmpty())) {
            for (int i = 0; i < list.size(); i++) {
                City city = list.get(i);
                city.setDisplayPosition(i);
            }
        }

        if (mMarkedNeedQuitEditMode) {
            mMarkedNeedQuitEditMode = false;
            changeMode(MODEL_NOML, EMPTY_SELESCT_ID, false);
        }

        resetCityAdapter(list);

        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new CityDiffCallBack(mListAdapter.getList(), list), true);
        diffResult.dispatchUpdatesTo(mListAdapter);
        mListAdapter.updateData(list);
        mListAdapter.notifyDataSetChanged();
        expandTitleWhenItemLessThenCityList(list);
        initializeFloatingButton();
        updateMenuAndEmptyView();
        udpateEditMenuAndTitle();
    }

    private void expandTitleWhenItemLessThenCityList(ArrayList<City> list) {
        if (mItemHeight == 0) {
            mItemHeight = getItemHeight();
        }
    }

    @Override
    public void onAfterMove() {
    }

    @Override
    public void onPause() {
        super.onPause();
        setResources(false);
    }

    private void asyncUpdatePos(List<City> list) {
        if ((list == null) || (list.isEmpty())) {
            return;
        }

        final List<CityPos> posList = new ArrayList<>();
        for (City city : list) {
            posList.add(new CityPos(city.getCityId(), city.getSortPos()));
        }

        new UpdatePosTask(posList).execute();
    }

    @Override
    public void delete() {
        deleteWorldClock();
    }

    @Override
    public String getDeleteTitle() {
        final int count = mSelectedIds.size();
        return getDeleteTitle(count, getCurrentCount());
    }

    public int getCurrentCount() {
        return (mListAdapter == null) ? 0 : mListAdapter.getList().size();
    }

    @Override
    public void onResume() {
        super.onResume();
        setResources(true);
    }

    @Override
    public void onStop() {
        super.onStop();
//        setResources(false);
    }

    @Override
    @SuppressLint("NonConstantResourceId")
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.dial_word_time_tv) {
            if ((mWorldClockListManager != null) && (isNormalFragment() || !isHover())) {
                mWorldClockListManager.reset();
            }
        } else if (id == R.id.click_view || id == R.id.dial_clock_cl || id == R.id.dial_clock_big_cl) {
            if (mClockManager != null) {
                if (isNormalFragment() || !isHover()) {
                    mClockManager.startDialChangeAnimation(getCurrentCitiesCount());
                }
            }
        } else {
            Log.d(TAG, "onClick: " + v.getId());
        }
    }

    @Override
    public void onTalkBackEvent(@NotNull View view, @NotNull AccessibilityEvent event) {
        boolean isFocused = event.getEventType() == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED;
        if (Utils.isTalkBackOpen() && isFocused) {
            String description = getTalkBackMsg();
            if (description != null) {
                view.setContentDescription(description);
            } else {
                view.setContentDescription("");
            }
        }
    }

    private void setResources(boolean isOnResume) {
        if (mAlarmDialClockManager != null) {
            if (isOnResume) {
                mAlarmDialClockManager.start();
            } else {
                mAlarmDialClockManager.stop();
            }
        }
    }

    private static class UpdatePosTask extends AsyncTask<Void, Void, Void> {

        private List<CityPos> mPosList;

        private UpdatePosTask(List<CityPos> posList) {
            mPosList = posList;
        }

        @Override
        protected Void doInBackground(Void... voids) {
            updatePos(AlarmClockApplication.getInstance().getApplicationContext(), mPosList);
            return null;
        }
    }

    private static void updatePos(Context context, List<CityPos> list) {
        if (!list.isEmpty()) {
            ArrayList<ContentProviderOperation> ops = new ArrayList<>();
            for (CityPos cityPos : list) {
                int cityId = cityPos.mCityId;
                int pos = cityPos.mPos;
                ContentProviderOperation op = ContentProviderOperation.newUpdate(
                                ClockProvider.buildUnNotifyUri(ClockContract.City.NEW_CITY_CONTENT_URI))
                        .withSelection(ClockContract.City.CITY_ID + "=" + cityId, null)
                        .withValue(ClockContract.City.SORT_ORDER, pos).build();
                ops.add(op);
                Log.d(TAG, "city: " + cityPos.mCityId + ", sort: " + pos);
            }

            boolean success = Utils.applyDbOperations(context, ops);
            Log.d(TAG, "updatePos: " + success);
            if (success) {
                sendLocalBroadcast(context);
            }
        }
    }

    private static void sendLocalBroadcast(Context context) {
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(context);
        Intent intent = new Intent(ClockConstant.WORLD_CLOCK_DATA_CHANGED);
        intent.putExtra(ClockConstant.IS_NOT_NEED_UPDATE_CITY_LIST, true);
        manager.sendBroadcast(intent);
    }

    @Override
    public void onStartDrag(COUIRecyclerView.ViewHolder viewHolder) {
        if (mItemTouchHelper != null) {
            mItemTouchHelper.startDrag(viewHolder);
        }
    }

    @Override
    public void onEndDrag() {
        if (mListAdapter != null) {
            List<City> cityList = mListAdapter.getList();
            for (int i = 0; i < cityList.size(); i++) {
                (mListAdapter.getList().get(i)).setSortPos(i);
            }

            asyncUpdatePos(mListAdapter.getList());
        }
    }

    public static final class CityPos {
        int mCityId;
        int mPos;

        public CityPos(int cityId, int pos) {
            mCityId = cityId;
            mPos = pos;
        }
    }

    public boolean isEditMode() {
        return mMode == MODEL_EDIT;
    }

    public int getClockSelectCount() {
        return mSelectedIds.size();
    }

    public boolean isListAnimating() {
        return (mListAdapter != null) && mListAdapter.isAnimating();
    }

    /**
     * 首次进入世界时钟执行表盘动画
     */
    public void selectEd() {
        if ((mShadowManager != null) && (mAlarmDialClockManager != null)) {
            mShadowManager.startAnimation(mAlarmDialClockManager);
            if (mIsFirstEnter) {
                mIsFirstEnter = false;
            }
        } else {
            mNotInitEffect = true;
        }
    }

    private void scrollToTop(RecyclerView list) {
        if (list != null) {
            long time = SystemClock.uptimeMillis();
            MotionEvent event = MotionEvent.obtain(time, time, MotionEvent.ACTION_CANCEL, 0, 0, 0);
            list.dispatchTouchEvent(event);
            event.recycle();
            if (list.getChildCount() > 0) {
                list.smoothScrollToPosition(0);
            }
            list.startNestedScroll(ViewCompat.SCROLL_AXIS_VERTICAL);
        }
    }

    protected void updateCityListMargin() {
    }

    /**
     * 切换编辑模式时，切换列表
     *
     * @param mode
     */
    protected void changeList(int mode) {
    }

    /**
     * 是否小屏
     *
     * @return
     */
    protected boolean isNormalFragment() {
        return false;
    }

    /**
     * 列表加载完成后，更新表盘位置
     *
     * @param list 城市列表
     */
    protected void updateTimeDialView(ArrayList<City> list) {
    }

    /**
     * 平板竖屏下，列表数据变为1时，需要重设adapter
     *
     * @param list 城市列表
     */
    protected void resetCityAdapter(ArrayList<City> list) {
    }

    /**
     * 设置城市列表的LayoutManager
     *
     * @param listView
     * @param context
     */
    protected void setRecyclerViewLayoutManager(COUIRecyclerView listView, boolean isEdit, Context context) {
        if (isEdit) {
            setEditRecyclerViewLayoutManager(context);
        } else {
            mLayoutManager = new ScrollLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false);
            listView.setLayoutManager(mLayoutManager);
        }
    }

    /**
     * 设置城市列表的LayoutManager
     *
     * @param context
     */
    protected void setEditRecyclerViewLayoutManager(Context context) {
        COUIRecyclerView editList = cityListEditView();
        if (editList != null) {
            editList.setLayoutManager(new ScrollLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        }
    }

    /**
     * 根据展示的页面不同，配置城市列表adapter的差异属性
     */
    protected void setListAdapterProp() {
        mListAdapter.isNeedAddFooter(true);
        mListAdapter.setIsGeneralScreen(true);
        mListAdapter.setIsInMultiWindowMode(false);
    }

    /**
     * 获取talkBackMsg
     *
     * @return String
     */
    protected String getTalkBackMsg() {
        return null;
    }

    /**
     * 是否处于悬停状态
     *
     * @return Boolean
     */
    protected Boolean isHover() {
        return false;
    }

    /**
     * 获取悬浮按钮
     *
     * @return COUIFloatingButton
     */
    abstract protected COUIFloatingButton floatingButton();

    /**
     * 工具栏
     *
     * @return COUIToolbar
     */
    abstract protected COUIToolbar couiToolbar();

    /**
     * 获取世界时钟容器对象
     *
     * @return ConstraintLayout
     */
    abstract protected ConstraintLayout worldClockCl();

    /**
     * 获取城市列表
     *
     * @return COUIRecyclerView
     */
    abstract protected COUIRecyclerView cityListView();

    /**
     * 城市编辑列表，常规屏下，存在城市编辑列表
     *
     * @return COUIRecyclerView
     */
    abstract protected COUIRecyclerView cityListEditView();

    /**
     * 设置时间到View中
     *
     * @param timeInfo 时间信息
     */
    abstract protected void setTimeInfo(String timeInfo);

    /**
     * 表盘大小
     *
     * @return 大小
     */
    abstract protected int clockSize();
}