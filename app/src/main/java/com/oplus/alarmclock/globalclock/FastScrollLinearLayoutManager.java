/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description: Set the scroll time of RecyclerView to 400ms
 ** Version: V 1.0
 ** Date : 2019/10/18
 ** Author: yu xiaolong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.globalclock;

import android.content.Context;
import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashMap;
import java.util.Map;

public class FastScrollLinearLayoutManager extends LinearLayoutManager {


    private static final int DEFAULT_DURATION = 400;
    private Map<Integer, Integer> mHeightMap = new HashMap<>();

    public FastScrollLinearLayoutManager(Context context) {
        super(context);
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        LinearSmoothScroller linearSmoothScroller = new LinearSmoothScroller(recyclerView.getContext()) {

            @Override
            public PointF computeScrollVectorForPosition(int targetPosition) {
                return FastScrollLinearLayoutManager.this.computeScrollVectorForPosition(targetPosition);
            }

            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                return super.calculateSpeedPerPixel(displayMetrics);
            }

            //Calculates the time it should take to scroll the given distance (in pixels)
            @Override
            protected int calculateTimeForScrolling(int dx) {
                if (dx > DEFAULT_DURATION) {
                    dx = DEFAULT_DURATION;
                }
                return super.calculateTimeForScrolling(dx);
            }
        };

        linearSmoothScroller.setTargetPosition(position);
        startSmoothScroll(linearSmoothScroller);
    }


    @Override
    public void onLayoutCompleted(RecyclerView.State state) {
        super.onLayoutCompleted(state);
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View view = getChildAt(i);
            if (view != null) {
                mHeightMap.put(i, view.getHeight());
            }
        }
    }

    @Override
    public int computeVerticalScrollOffset(RecyclerView.State state) {
        if (getChildCount() == 0) {
            return 0;
        }
        int firstVisiblePosition = findFirstVisibleItemPosition();
        View firstVisibleView = findViewByPosition(firstVisiblePosition);
        if (firstVisibleView == null) {
            return 0;
        }
        int offsetY = -(int) (firstVisibleView.getY());
        for (int i = 0; i < firstVisiblePosition; i++) {
            offsetY += (mHeightMap.get(i) == null) ? 0 : mHeightMap.get(i);
        }
        return offsetY;
    }

}
