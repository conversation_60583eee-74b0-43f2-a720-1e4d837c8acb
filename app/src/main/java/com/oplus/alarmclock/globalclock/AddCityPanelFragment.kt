/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AddCityPanelFragment.kt
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/10/22
 *  ** Author: <EMAIL>
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  HeWei  2020/10/22      1.0            AddCityPanelFragment.kt
 *  ***************************************************************
 */
package com.oplus.alarmclock.globalclock

import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.alarmclock.utils.ToastManager
import com.oplus.clock.common.utils.Log

class AddCityPanelFragment : COUIPanelFragment() {

    companion object {
        const val TAG = "AddCityPanelFragment"
        const val DOUBLE_ACTION_INTERVAL = 2000
    }

    private var mLastClickTime = 0L
    private var mLastDragTime = 0L

    private val mAddCityFragment = com.oplus.alarmclock.globalclock.AddCityFragment()
    private val mContext = AlarmClockApplication.getInstance()
    private var mDialog: COUIBottomSheetDialog? = null

    override fun initView(panelView: View) {
        initToolbar()
        initPage()
        initListener()
    }

    fun onStatusBarClicked() {
        mAddCityFragment.onStatusBarClicked()
    }

    private fun initListener() {
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {

                if (mAddCityFragment.isEditing() && (System.currentTimeMillis() - mLastClickTime > DOUBLE_ACTION_INTERVAL)) {
                    setCancelable(false)
                    ToastManager.showToast(mContext, getString(R.string.panel_back_toast))
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    mLastClickTime = System.currentTimeMillis()
                } else {
                    setCancelable(true)
                }
            }
            false
        }

        setPanelDragListener {
            if (mAddCityFragment.isEditing() && (System.currentTimeMillis() - mLastDragTime > DOUBLE_ACTION_INTERVAL)) {
                ToastManager.showToast(mContext, getString(R.string.panel_pull_down_toast))
                mLastDragTime = System.currentTimeMillis()
                true
            } else {
                false
            }
        }

        mDialog =
                ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
        mDialog?.apply {
            (activity as? BaseActivity)?.obtainUiMode()?.apply {
                Log.d(TAG, "initListener uiMode:$this")
                if (UiMode.LARGE_VERTICAL == this || UiMode.LARGE_HORIZONTAL == this) {
                    setHeight(resources.getDimensionPixelOffset(R.dimen.layout_dp_800))
                } else {
                    setIsShowInMaxHeight(true)
                }
            } ?: setIsShowInMaxHeight(true)
            setOnDismissListener {
                activity?.finish()
            }
            setOnShowListener {
                if (COUIDarkModeUtil.isNightMode(context)) {
                    setPanelBackgroundTintColor(
                        ContextCompat.getColor(
                            mContext,
                            R.color.coui_color_background_elevatedWithCard
                        )
                    )
                }
            }
        }
    }

    private fun initToolbar() {
        hideDragView()
        titleView?.visibility = View.GONE
        toolbar?.visibility = View.GONE
        view?.findViewById<ViewGroup>(R.id.title_view_container)?.visibility = View.GONE
    }

    private fun initPage() {
        childFragmentManager.beginTransaction().replace(contentResId, mAddCityFragment).commit()
    }


    override fun onDestroy() {
        super.onDestroy()
        childFragmentManager.beginTransaction().remove(mAddCityFragment)
                .commitAllowingStateLoss()

        outSideViewOnTouchListener = null
        setPanelDragListener(null)
        dialogOnKeyListener = null
        mDialog?.setOnDismissListener(null)
    }

    private fun setCancelable(cancelable: Boolean) {
        (parentFragment as? COUIBottomSheetDialogFragment)?.isCancelable = cancelable
    }
}