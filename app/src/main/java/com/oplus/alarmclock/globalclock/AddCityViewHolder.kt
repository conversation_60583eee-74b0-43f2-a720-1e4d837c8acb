/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AddCityViewHolder.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/2/19
 ** Author: ********
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ********  2024/2/19     1.0            build this module
 ****************************************************************/
@file:Suppress("DEPRECATION")

package com.oplus.alarmclock.globalclock

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.EditText
import android.widget.LinearLayout
import androidx.asynclayoutinflater.view.AsyncLayoutInflater
import androidx.asynclayoutinflater.view.AsyncLayoutInflater.OnInflateFinishedListener
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.orientationutil.COUIOrientationUtil
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchBar.STATE_EDIT
import com.coui.appcompat.searchview.COUISearchBar.STATE_NORMAL
import com.coui.appcompat.searchview.COUISearchBar.TYPE_INSTANT_SEARCH
import com.coui.appcompat.searchview.COUISearchView
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.appcompat.touchsearchview.COUITouchSearchView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.behavior.HeadScaleWithSearchBhv
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter
import com.oplus.alarmclock.globalclock.view.HeightView
import com.oplus.alarmclock.globalclock.view.TopMarginView
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils.getStatusBarHeight
import com.oplus.anim.EffectiveAnimationView
import com.oplus.clock.common.utils.Log.d

class AddCityViewHolder {

    companion object {
        private const val TAG: String = "AddCityViewHolder"
        private const val FACTOR = 0.45
    }
    lateinit var mRoot: View
    private var mContext: Context = AlarmClockApplication.getInstance().applicationContext
    private var mResources: Resources = mContext.resources
    var mColorAppBarLayout: AppBarLayout? = null
    private var mViewBg: View? = null
    var mGlobalSearchView: COUISearchBar? = null
    var mToolBar: COUIToolbar? = null
    var mHeaderView: View? = null
    var mDividerLine: View? = null
    var mBehavior: HeadScaleWithSearchBhv? = null
    var mHeaderHeightView: HeightView? = null
    var mSearchHeightView: HeightView? = null
    var mTopMarginView: TopMarginView? = null
    var mCityList: COUIRecyclerView? = null
    var mListAdapter: AddGlobalCityListAdapter? = null
    var mSearchBar: EditText? = null
    var mMissMatch: LinearLayout? = null
    var mViewEmpty: EffectiveAnimationView? = null
    var mTvEmpty: android.widget.TextView? = null
    var mTouchSearchView: COUITouchSearchView? = null
    private var mLayoutParams: CoordinatorLayout.LayoutParams? = null

    var mStatusBarHeight = 0


    fun inflate(activity: Activity, container: ViewGroup?, savedInstanceState: Bundle?, onInflateFinishedListener: OnInflateFinishedListener) {
        AsyncLayoutInflater(activity).inflate(R.layout.world_clock_add_city_list, container, onInflateFinishedListener)
    }

    fun initView(root: View) {
        mRoot = root
        mToolBar = mRoot.findViewById(R.id.toolbar_options)
        mDividerLine = mRoot.findViewById(R.id.divider_line)
        mHeaderView = View(mContext)
        mHeaderView?.layoutParams = RecyclerView.LayoutParams(
            RecyclerView.LayoutParams.MATCH_PARENT, RecyclerView.LayoutParams.WRAP_CONTENT
        )
        mHeaderView?.visibility = View.INVISIBLE
    }

    fun initViewBg(isFromSetting: Boolean) {
        mViewBg = mRoot.findViewById(R.id.view_bg)
        if (isFromSetting) {
            mViewBg?.setBackgroundResource(R.color.main_pager_background)
        }
    }

    fun initAppBarLayout(isShowPanel: Boolean) {
        mColorAppBarLayout = mRoot.findViewById(R.id.abl_toolbar_options)
        mLayoutParams = mColorAppBarLayout?.layoutParams as CoordinatorLayout.LayoutParams
        mBehavior = mLayoutParams?.behavior as? HeadScaleWithSearchBhv
        if (COUIDarkModeUtil.isNightMode(mContext)) {
            mColorAppBarLayout?.setBackgroundResource(R.color.coui_color_background_elevatedWithCard)
        } else {
            if (isShowPanel) {
                mColorAppBarLayout?.setBackgroundResource(R.color.coui_color_surface)
            } else {
                mColorAppBarLayout?.setBackgroundResource(R.color.add_alarm_panel_bg)
            }
        }
        mColorAppBarLayout?.setPadding(0, mStatusBarHeight, 0, 0)
        mColorAppBarLayout?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                mColorAppBarLayout!!.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val topPadding = mColorAppBarLayout!!.measuredHeight
                val lp = mHeaderView?.layoutParams as RecyclerView.LayoutParams
                lp.height = topPadding + mResources.getDimensionPixelSize(R.dimen.category_top_padding)
                d(TAG, "onGlobalLayout topPadding = " + (topPadding + mResources.getDimensionPixelSize(
                        R.dimen.category_top_padding
                    )) + ",lp.height = " + lp.height)
                mHeaderView?.layoutParams = lp
                val layoutParams = mMissMatch?.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin =
                    (mRoot.height * FACTOR).toInt() - (mMissMatch?.height?.div(2) ?: 0)
                mMissMatch?.layoutParams = layoutParams
                val touchSearchViewLayoutParams =
                    mTouchSearchView?.layoutParams as ViewGroup.MarginLayoutParams
                touchSearchViewLayoutParams.topMargin = topPadding
                mTouchSearchView?.layoutParams = touchSearchViewLayoutParams
            }
        })
    }

    fun initSearchView(
        isFromSetting: Boolean,
        searchBarActionListener: AddCityManager.SearchBarActionListener
    ) {
        mGlobalSearchView = mRoot.findViewById(R.id.globalSearchView)
        mGlobalSearchView?.apply {
            setSearchAnimateType(TYPE_INSTANT_SEARCH)
            val queryHint: String = if (isFromSetting) {
                mResources.getString(R.string.search_city_for_setting)
            } else {
                mResources.getString(R.string.search_city_country)
            }
            mGlobalSearchView?.searchEditText?.setHint(queryHint)
            setOnClickListener {
                if (mGlobalSearchView?.searchState == STATE_NORMAL) {
                    //normal状态切换edit状态 清除动画状态
                    mGlobalSearchView?.changeStateWithAnimation(STATE_EDIT)
                    if (queryHint == mResources.getString(R.string.search_city_for_setting)) {
                        val marginTop =
                            getStatusBarHeight(mContext) + mResources.getDimensionPixelOffset(R.dimen.toolbar_margin_top)
                        mGlobalSearchView?.setExtraActivateMarginTop(marginTop)
                    }
                }
                return@setOnClickListener
            }
            functionalButton.setOnClickListener {
                if (mGlobalSearchView?.searchState == STATE_EDIT) {
                    mGlobalSearchView?.changeStateWithAnimation(STATE_NORMAL)
                }
            }
        }
        mSearchBar = mGlobalSearchView?.searchEditText
        mSearchBar?.clearFocus()
        mSearchBar?.doOnTextChanged { char, _, _, _ ->
            searchBarActionListener.onQueryTextChange(char.toString())
        }
        mSearchBar?.setOnLongClickListener {
            searchBarActionListener.onLongClick(it)
        }
        mSearchBar?.setOnClickListener {
            searchBarActionListener.onClick(it)
        }
    }

    fun initListView(isShowPanel: Boolean) {
        mCityList = mRoot.findViewById(R.id.allCities)
        mBehavior?.mInterceptTouchEventRecipient = mCityList
        mCityList?.layoutManager = FastScrollLinearLayoutManager(mContext)
        ViewCompat.setNestedScrollingEnabled(mCityList!!, true)
        mListAdapter = AddGlobalCityListAdapter(mHeaderView, null)
        mCityList?.adapter = mListAdapter
        mCityList?.isMotionEventSplittingEnabled = false
        backtoTop()
        if (!isShowPanel) {
            mCityList?.setBackgroundResource(R.color.coui_color_background_elevatedWithCard)
        }
        mCityList?.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                mTouchSearchView?.closing()
            }
            false
        }
    }

    fun initSearchBar() {
        mTouchSearchView = mRoot.findViewById<View>(R.id.touch_search_bar) as COUITouchSearchView
        mMissMatch = mRoot.findViewById(R.id.miss_match)
        mViewEmpty = mRoot.findViewById(R.id.view_empty)
        mTvEmpty = mRoot.findViewById(R.id.tv_empty)
        if (FoldScreenUtils.isInDealMultiWindowMode(COUIOrientationUtil.isInMultiWindowMode(mContext))) {
            mTouchSearchView?.visibility = View.GONE
            mListAdapter?.setIndexShowOrHide(true)
        }
    }

    fun onStatusBarClicked() {
        mTouchSearchView?.closing()
        backtoTop()
    }

    fun backtoTop() {
        if (mCityList != null) {
            mCityList!!.smoothScrollToPosition(0)
        }
    }
}