/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - WorldClockSmallFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.WorldClockViewSplitLayoutBinding

class WorldClockSmallFragment : WorldClockViewFragment<WorldClockViewSplitLayoutBinding>() {

    override fun layoutId(): Int {
        return R.layout.world_clock_view_split_layout
    }

    override fun initDialClock() {
        super.initDialClock()
        mClockManager = null
        mShadowManager = null
        mViewBinding?.apply {
            dialWordTimeTv.setUiMode(uiMode)
            mAlarmDialClockManager?.init(null, null, null, null, dialWordTimeTv)
        }
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
            worldClockToolbarInclude.toolbar.setPopupWindowOnDismissListener {
                popupWindowOnDismiss()
            }
        }
    }

    override fun setListAdapterProp() {
        mListAdapter?.apply {
            isNeedAddFooter(true)
            setIsGeneralScreen(true)
            setIsInMultiWindowMode(true)
        }
    }

    override fun getTalkBackMsg(): String? {
        return mViewBinding?.let {
            val time = it.dialWordTimeTv.getTalkBackMsg()
            val zone = it.dialWordMsgTv.text
            time + zone
        }
    }

    override fun getBlurView(): ViewGroup? {
        return mViewBinding?.worldClockList
    }

    override fun floatingButton(): COUIFloatingButton? {
        return mViewBinding?.button
    }

    override fun worldClockCl(): ConstraintLayout? {
        return mViewBinding?.worldClockCl
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun cityListView(): COUIRecyclerView? {
        return mViewBinding?.worldClockList
    }

    override fun cityListEditView(): COUIRecyclerView? {
        return null
    }

    override fun setTimeInfo(timeInfo: String?) {
        timeInfo?.let { mViewBinding?.timeInfo = it }
    }

    override fun clockSize(): Int {
        return 0
    }
}