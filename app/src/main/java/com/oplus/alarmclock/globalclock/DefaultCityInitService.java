/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.globalclock;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.provider.Settings;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;

public class DefaultCityInitService extends Service {

    private final static String TAG = "DefaultCityInitService";

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int arg1, int arg2) {
        ContentResolver contentResolver = getContentResolver();
        if (contentResolver == null) {
            Log.e(TAG, "Unable to get content resolver.");
            stopSelf();
        } else {
            initDefaultData(contentResolver);
        }
        return START_NOT_STICKY;
    }

    private void initDefaultData(ContentResolver contentResolver) {
        addDefaultCity(this, contentResolver);
        CityUtils.decideShowChina(this);
    }

    private void addDefaultCity(final Context context, final ContentResolver contentResolver) {
        ContentObserver deviceProvisionedObserver = new ContentObserver(new Handler()) {
            @Override
            public void onChange(boolean selfChange) {
                super.onChange(selfChange);
                Log.d(TAG, "addDefaultCity onchange " + selfChange);
                if (!DeviceUtils.isProvisioned(context)) {
                    return;
                }

                final boolean hasInitDefaultCity = PrefUtils.hasInitDefaultCity(context);
                Log.d(TAG, "addDefaultCity hasInitDefaultCity:" + hasInitDefaultCity);

                if (!hasInitDefaultCity) {
                    CityUtils.addDefaultCity(context);
                }

                contentResolver.unregisterContentObserver(this);
                stopSelf();
            }
        };
        contentResolver.registerContentObserver(
                Settings.Global.getUriFor(Settings.Global.DEVICE_PROVISIONED), false,
                deviceProvisionedObserver);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
