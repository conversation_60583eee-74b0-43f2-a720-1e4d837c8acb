/*********************************************************************************
 ** Copyright (C), 2008-2016, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUSOS_EDIT, All rights reserved.
 **
 ** File: - ViewHelperUtil.kt
 ** Description: Util object for reset view's property after animation end or cancel
 **
 ** Version: 1.0
 ** Date: 2020-03-26
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2020-03-26   1.0         Create this module
 ********************************************************************************/

package com.oplus.alarmclock.globalclock

import android.view.View

object ViewHelperUtil {
    fun clear(view : View) {
        view.apply {
            alpha = 1f
            scaleX = 1f
            scaleY = 1f
            translationX = 0f
            translationY = 0f
            rotation = 0f
            rotationX = 0f
            rotationY = 0f
            pivotX = measuredWidth/2f
            pivotY = measuredHeight/2f
            animate().apply {
                interpolator = null
                startDelay = 0
            }
        }
    }
}