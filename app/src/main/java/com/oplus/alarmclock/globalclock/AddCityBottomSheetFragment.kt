/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUSOS_EDIT
 ** File: - AddCityBottomSheetFragment.java
 ** Description: Add City panel
 ** Version: 1.0
 ** Date: 2021/02/05
 ** Author: qidingquan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>         <date>       <version>    <desc>
 ** qidingqaun      2021/02/05    1.1          Add City panel
 ***************************************************************/
package com.oplus.alarmclock.globalclock

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Build
import android.os.Bundle
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUINavigationBarUtil
import com.oplus.alarmclock.R


class AddCityBottomSheetFragment : COUIBottomSheetDialogFragment() {
    companion object {
        const val FROM_DIAL_CLOCK_SETTING = "from_dial_clock_setting"
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            view?.setPadding(0, 0, 0, 0)
            context?.let {
                val ds = dialog as? COUIBottomSheetDialog
                ds?.setNavColor(ContextCompat.getColor(it, R.color.add_city_nav_color))
                if (COUINavigationBarUtil.isNavigationBarShow(it)) {
                    view?.run {
                        resources?.let {
                            background = ResourcesCompat.getDrawable(
                                it,
                                R.drawable.coui_panel_bg_without_shadow,
                                null
                            )
                        }
                    }
                }
            }
        }
        return dialog
    }

    @SuppressLint("MissingSuperCall")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        dialog?.setOnCancelListener(null)
    }
}