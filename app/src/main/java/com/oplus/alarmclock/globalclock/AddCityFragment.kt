/***********************************************************
 * Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 * OPLUSOS_EDIT
 * File: - ColorSearchViewBelowToolbarActivity.java
 * Description: Implemented searchView below toolbar
 * Version: 1.0
 * Date: 2019/1/24
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>         <date>       <version>    <desc>
 * <EMAIL>  2019/2/22    1.1          Fix the bug of shaking toolbar title
 * when search animator begins
</desc></version></date></author> */
package com.oplus.alarmclock.globalclock

import android.app.LoaderManager
import android.content.Loader
import android.database.Cursor
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.touchsearchview.COUITouchSearchView
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.BaseFragment
import com.oplus.alarmclock.R
import com.oplus.clock.common.utils.Log.d

open class AddCityFragment : BaseFragment(), COUISearchBar.OnStateChangeListener,
    COUITouchSearchView.TouchSearchActionListener, OnItemClickListener,
    LoaderManager.LoaderCallbacks<Cursor?> {

    var mViewHolder: AddCityViewHolder = AddCityViewHolder()
    private var mAddCityManager: AddCityManager? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        d(TAG, "onCreate")
        mContext = AlarmClockApplication.getInstance()
        val containerView = FrameLayout(mContext)
        activity?.let {
            mViewHolder.inflate(it, containerView, savedInstanceState) { view: View, _: Int, _: ViewGroup? ->
                containerView.removeAllViews()
                containerView.addView(view)
                mViewHolder.initView(view)
                mAddCityManager = AddCityManager(this, mViewHolder)
                mAddCityManager?.initData()
                initListener()
                mAddCityManager?.registerConfigChangeReceiver()
            }
        }
        return containerView
    }

    private fun initListener() {
        mViewHolder.let {
            it.mListAdapter?.setOnItemClickListener(this)
            it.mTouchSearchView?.touchSearchActionListener = this
            it.mGlobalSearchView?.addOnStateChangeListener(this)
            it.mBehavior?.setOnScrollStateChangedListener { _, scrollState ->
                if (COUIRecyclerView.SCROLL_STATE_DRAGGING == scrollState) {
                    mAddCityManager?.hideSoftInput()
                }
            }
        }
    }

    fun handleQuery(id: Int, param: Bundle) {
        activity?.loaderManager?.restartLoader<Cursor>(id, param, this)
    }

    fun isEditing(): Boolean {
        return !TextUtils.isEmpty(mViewHolder.mSearchBar?.text)
    }

    override fun onOptionsItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home, R.id.cancel -> finish()
            else -> {}
        }
        return true
    }

    override fun onStateChange(i: Int, i1: Int) {
        d(TAG, "onStateChange# mTouchSearchView change state is: $i1")
        mAddCityManager?.onStateChange(i, i1)
    }

    override fun onItemClock(position: Int, cursor: Cursor?) {
        d(TAG, "onItemClick : $position")
        cursor?.let { mAddCityManager?.onItemClock(position, it) }
    }

    fun finish() {
        mViewHolder.mTouchSearchView?.popupWindow?.dismiss()
        val activity = activity as? AddCityActivity
        activity?.run {
            if (!isFinishing) {
                finish(mAddCityManager?.mIsShowPanel ?: false)
            }
        }
    }

    override fun onCreateLoader(id: Int, bundle: Bundle?): Loader<Cursor?>? {
        return mAddCityManager?.onCreateLoader(id, bundle)
    }

    override fun onLoadFinished(loader: Loader<Cursor?>?, cursor: Cursor?) {
        loader?.let { mAddCityManager?.onLoadFinished(it, cursor) }
    }

    override fun onLoaderReset(loader: Loader<Cursor?>?) {
        loader?.let { mAddCityManager?.onLoaderReset(it) }
    }

    fun onStatusBarClicked() {
        mViewHolder.apply {
            mTouchSearchView?.closing()
            backtoTop()
        }
    }

    override fun onKey(key: CharSequence?) {
        key?.let { mAddCityManager?.onKey(it) }
    }

    override fun onLongKey(longKey: CharSequence?) {
        longKey?.let { mAddCityManager?.onLongKey(it) }
    }

    override fun onNameClick(charSequence: CharSequence?) {
        charSequence?.let { mAddCityManager?.onNameClick(it) }
    }

    override fun onDestroy() {
        super.onDestroy()
        mAddCityManager?.onDestroy()
    }

    companion object {
        const val ACTION_ADD_WORLD_CLOCK = "com.oplus.alarmclock.ADD_WORLD_CLOCK"
        const val ACTION_ADD_WORLD_CLOCK_OLD = "com.coloros.alarmclock.ADD_WORLD_CLOCK"
        const val ADD_CITY_FRAGMENT = 4
        const val CURRENT_COUNT = "current_count"
        const val FROM_DIAL_CLOCK_KEY = "isFromDialClock"
        private const val TAG = "AddCityFragment"
    }
}