/***********************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:AlarmListGridLayoutManager
 ** Description: Set the scroll time of RecyclerView to 400ms
 ** Version: V 1.0
 ** Date : 2024/2/28
 ** Author: helin
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.globalclock;

import android.content.Context;
import android.util.AttributeSet;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


public class AlarmListGridLayoutManager extends GridLayoutManager {


    public AlarmListGridLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public AlarmListGridLayoutManager(Context context, int spanCount) {
        super(context, spanCount);
    }

    public AlarmListGridLayoutManager(Context context, int spanCount, int orientation, boolean reverseLayout) {
        super(context, spanCount, orientation, reverseLayout);
    }

    @Override
    public int computeVerticalScrollOffset(RecyclerView.State state) {
        return 0;
    }

}
