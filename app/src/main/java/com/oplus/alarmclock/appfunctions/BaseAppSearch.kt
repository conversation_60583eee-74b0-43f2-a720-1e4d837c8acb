/****************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseAppSearch.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/26
 ** Author: <PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  <PERSON><PERSON>uang  2025/3/26   1.0            appfunctions : BaseAppSearch
 ****************************************************************/
@file:Suppress("ParameterListWrapping")
package com.oplus.alarmclock.appfunctions

import android.content.Context
import android.os.Build
import androidx.appsearch.app.AppSearchSession
import androidx.appsearch.app.SetSchemaRequest
import androidx.appsearch.app.SetSchemaRequest.READ_ASSISTANT_APP_SEARCH_DATA
import androidx.appsearch.builtintypes.Alarm
import androidx.appsearch.builtintypes.Timer
import androidx.appsearch.platformstorage.PlatformStorage
import com.google.common.util.concurrent.FutureCallback
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.MoreExecutors
import com.oplus.clock.common.utils.Log.d
import java.util.concurrent.Executor
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

open class BaseAppSearch {

     companion object {
          private const val TIMER_DATABASE_NAME = "TimerDataBase"
          private const val ALARM_DATABASE_NAME = "AlarmDataBase"
          private const val TAG = "BaseAppSearch"
          var sessionTimer: AppSearchSession? = null
          var sessionAlarm: AppSearchSession? = null
          var executor: ExecutorService? = null
          var NAMESPACE: String? = null
     }

     fun initNameSpace(context: Context) {
          if (NAMESPACE == null) {
               NAMESPACE = context.packageName
          }
     }

     @Synchronized
     fun initSession(context: Context, isAlarmSession: Boolean): AppSearchSession? {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
               kotlin.runCatching {
                    val session = if (isAlarmSession) sessionAlarm else sessionTimer
                    if (session == null) {
                         initNameSpace(context)
                         restartExecutor()
                         val databaseName = if (isAlarmSession) ALARM_DATABASE_NAME else TIMER_DATABASE_NAME
                         val documentClass = if (isAlarmSession) Alarm::class.java else Timer::class.java
                         val schemaTypeName = if (isAlarmSession) "builtin:Alarm" else "builtin:Timer"
                         val appSearchSession = PlatformStorage.createSearchSessionAsync(
                              PlatformStorage.SearchContext.Builder(context, databaseName).build()
                         )
                         val setSchemaRequest = SetSchemaRequest.Builder().apply {
                              addRequiredPermissionsForDocumentClassVisibility(documentClass,
                                   HashSet<Int>().apply { add(READ_ASSISTANT_APP_SEARCH_DATA) })
                              addRequiredPermissionsForSchemaTypeVisibility(
                                   schemaTypeName,
                                   HashSet<Int>().apply { add(READ_ASSISTANT_APP_SEARCH_DATA) })
                              addDocumentClasses(documentClass)
                         }.build()
                         if (isAlarmSession) {
                              sessionAlarm = appSearchSession.get()
                              sessionAlarm?.setSchemaAsync(setSchemaRequest)
                              return sessionAlarm
                         } else {
                              sessionTimer = appSearchSession.get()
                              sessionTimer?.setSchemaAsync(setSchemaRequest)
                              return sessionTimer
                         }
                    }
               }.onFailure { e ->
                    close(isAlarmSession)
                    d(TAG, "initSession: Failure ${e.message}")
               }
          }
          return if (isAlarmSession) sessionAlarm else sessionTimer
     }

     private fun restartExecutor() {
          if (executor == null || executor?.isShutdown == true || executor?.isTerminated == true) {
               executor = Executors.newSingleThreadExecutor()
          }
     }

     fun <T> handleFutureCallback(
          future: ListenableFuture<T>,
          executor: Executor,
          onSuccess: (T) -> Unit,
          onFailure: (Throwable) -> Unit
     ) {
          Futures.addCallback(future, object : FutureCallback<T> {
               override fun onSuccess(result: T) {
                    onSuccess(result)
               }

               override fun onFailure(t: Throwable) {
                    d(TAG, "Error: " + t.message)
                    onFailure(t)
               }
          }, executor)
     }

     fun close(isAlarmSession: Boolean) {
          if (isAlarmSession) {
               sessionAlarm?.close()
               sessionAlarm = null
          } else {
               sessionTimer?.close()
               sessionTimer = null
          }
          executor?.shutdown()
          executor = null
     }

     fun saveSession(isAlarmSession: Boolean) {
          val future: ListenableFuture<Void>? = if (isAlarmSession) {
               sessionAlarm?.requestFlushAsync()
          } else {
               sessionTimer?.requestFlushAsync()
          }
          if (future != null) {
               Futures.addCallback(future, object : FutureCallback<Void> {
                    override fun onSuccess(result: Void?) {
                         d(TAG, "onSuccess: ")
                    }

                    override fun onFailure(t: Throwable) {
                         d(TAG, "Flush request failed: ${t.message}")
                    }
               }, MoreExecutors.directExecutor())
          }
     }
}