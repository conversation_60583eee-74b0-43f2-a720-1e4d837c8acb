/****************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerAppSearchManger.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/19
 ** Author: <PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  Yangchenguang  2025/3/26   1.0            appfunctions : TimerAppSearchManger
 ****************************************************************/
package com.oplus.alarmclock.appfunctions

import android.content.Context
import androidx.appsearch.app.PutDocumentsRequest
import androidx.appsearch.app.RemoveByDocumentIdRequest
import androidx.appsearch.builtintypes.Timer
import com.heytap.addon.os.WaveformEffect
import com.oplus.alarmclock.ai.AiSupportContentProvider.DEFAULT_TIMER_INDEX
import com.oplus.alarmclock.appfunctions.AppFunctionsUtils.isSupportAppFunction
import com.oplus.alarmclock.appfunctions.TimerFunctions.Companion.ONE_THOUSAND
import com.oplus.alarmclock.timer.TimerService
import com.oplus.alarmclock.utils.AlarmRingUtils
import com.oplus.clock.common.utils.Log

object TimerAppSearchManger : BaseAppSearch() {

    private const val TAG: String = "TimerAppSearchManger"

    @Synchronized
    @JvmStatic
    fun addTimer(context: Context, timer: Timer, isInitSession: Boolean) {
        Log.d(TAG, "addTimer: ${timer.name}")
        if (isSupportAppFunction(context)) {
            if (isInitSession) {
                initSession(context, false)
            }
            kotlin.runCatching {
                val putRequest = PutDocumentsRequest.Builder()
                    .addDocuments(timer)
                    .build()
                val putAsyncFuture = sessionTimer?.putAsync(putRequest)
                executor?.let { executor ->
                        putAsyncFuture?.let {
                            handleFutureCallback(it,
                                executor,
                                onSuccess = { result ->
                                    if (result.isSuccess) {
                                        Log.d(TAG, "addTimer: success $result")
                                    }
                                    close(false)
                                },
                                onFailure = { t ->
                                    Log.d(TAG, "putRequest failed: ${t.message}")
                                    close(false)
                                })
                        }
                } ?: close(false)
            }.onFailure { e ->
                Log.d(TAG, "deleteAllAlarms Error: ${e.message}")
                close(false)
            }
        }
    }

    @Synchronized
    @JvmStatic
    fun deleteTimer(context: Context, timerID: String, isInitSession: Boolean) {
        if (isSupportAppFunction(context)) {
            if (isInitSession) {
                initSession(context, false)
            }
            kotlin.runCatching {
                NAMESPACE?.let { namespace ->
                    val removeRequest = RemoveByDocumentIdRequest.Builder(namespace).addIds(timerID).build()
                    val removeAsyncFuture = sessionTimer?.removeAsync(removeRequest)
                    executor?.let { executor ->
                        removeAsyncFuture?.let {
                            handleFutureCallback(it,
                                executor,
                                onSuccess = { result ->
                                    if (result.isSuccess) {
                                        Log.d(TAG, "deleteAlarms result.successes: $result")
                                    } else {
                                        Log.d(TAG, "deleteAlarms: failed")
                                    }
                                    close(false)
                                },
                                onFailure = { t ->
                                    Log.d(TAG, "RemoveByDocumentIdRequest failed: ${t.message}")
                                    close(false)
                                })
                        } ?: close(false)
                    } ?: close(false)
                } ?: close(false)
            }.onFailure { e ->
                Log.d(TAG, "deleteTimer Error: ${e.message}")
                close(false)
            }
        }
    }

    @Synchronized
    @JvmStatic
    fun getTimer(context: Context, timer: TimerService.TimeObj, elapsedRealtime: Long, firstStarTime: Long): Timer {
        return Timer.Builder(context.packageName, DEFAULT_TIMER_INDEX.toString()).apply {
            Log.d(TAG, "getTimer: ${timer.mTimerName}")
            setName(timer.mTimerName)
            setDurationMillis(timer.totalTime)
            setBaseTimeMillis(context, System.currentTimeMillis(), elapsedRealtime)
            if (timer.mIsStart) {
                if (timer.remainTime > ONE_THOUSAND) {
                    setRemainingDurationMillis(timer.remainTime - ONE_THOUSAND - 1)
                } else {
                    setRemainingDurationMillis(timer.remainTime)
                }
            } else {
                setRemainingDurationMillis(timer.remainTime + ONE_THOUSAND - 1)
            }
            //firstStartTimer
            setStartTimeMillis(firstStarTime)
            setOriginalDurationMillis(timer.totalTime)
            if (!timer.mIsStart && !timer.mIsPause) {
                setStatus(Timer.STATUS_EXPIRED)
            } else if (timer.mIsStart) {
                setStatus(Timer.STATUS_STARTED)
            } else if (timer.mIsPause) {
                setStatus(Timer.STATUS_PAUSED)
            }
            setShouldVibrate(
                AlarmRingUtils.getDefaultVibrate(context) != WaveformEffect.EFFECT_RINGTONE_NOVIBRATE
            )
        }.build()
    }
}

