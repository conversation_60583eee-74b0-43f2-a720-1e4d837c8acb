/****************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AppFunctionsUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: <PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  Yang<PERSON>uang  2025/3/10     1.0            appfunctions : AppsearchAlarmUtils
 ****************************************************************/
package com.oplus.alarmclock.appfunctions

import android.content.Context
import android.os.SystemClock
import com.google.android.appfunctions.AppFunctionException
import com.google.android.appfunctions.schema.common.v1.clock.Alarm
import com.google.android.appfunctions.schema.common.v1.clock.AlarmStatus
import com.google.android.appfunctions.schema.common.v1.clock.DayPattern
import com.google.android.appfunctions.schema.common.v1.types.Date
import com.google.android.appfunctions.schema.common.v1.types.TimeOfDay
import com.oplus.alarmclock.R
import com.oplus.alarmclock.ai.AiAlarmUtils
import com.oplus.alarmclock.alarmclock.AlarmSchedule
import com.oplus.alarmclock.alarmclock.AlarmUtils
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.DeviceUtils.isEnableAppfunction
import com.oplus.clock.common.utils.Log.d
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Calendar
import com.oplus.alarmclock.alarmclock.Alarm as OplusAlarm

object AppFunctionsUtils {
    private const val REPEAT_DAILY = "RRULE:FREQ=DAILY"
    const val REPEAT_WEEKLY = "RRULE:FREQ=WEEKLY;BYDAY="
    private const val TAG = "AppFunctionUtils"
    private const val ONE_THOUSAND = 1000
    private const val TWO = 2
    private const val THREE = 3
    private const val FOUR = 4
    private const val FIVE = 5
    private const val SIX = 6
    private const val SEVEN = 7
    private var lastcalltime = 0L
    private const val THROTTLE_INTERVAL = 150L
    const val ERROR_ACTION_NOT_SUPPORTED = "action_not_support"
    private val daysToSting =
        mapOf(TWO to "MO", THREE to "TU", FOUR to "WE", FIVE to "TH", SIX to "FR", SEVEN to "SA", 1 to "SU")

    @JvmStatic
    fun getNextAlertTime(nextAlarmTime: Long): String {
        val scheduleTime = LocalDateTime.ofInstant(
            Instant.ofEpochSecond(
                nextAlarmTime / ONE_THOUSAND
            ),
            ZoneId.systemDefault()
        ).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        return scheduleTime
    }

    @JvmStatic
    fun getAlertTime(alarm: OplusAlarm): String? {
        val alertTime = AlarmUtils.getAlarmTime(alarm, "createScheduleForAlarm").timeInMillis
        val scheduleTime = LocalDateTime.ofInstant(
            Instant.ofEpochSecond(alertTime / ONE_THOUSAND),
            ZoneId.systemDefault()
        ).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        return scheduleTime
    }

    @JvmStatic
    fun getAlarmLabel(context: Context, alarm: OplusAlarm): String {
        return if (alarm.getmLoopSwitch() == 1) {
            context.getString(R.string.loop_alarm_title)
        } else if (alarm.getmGarbSwitch() == 1) {
            context.getString(R.string.grab_alarm_title)
        } else {
            alarm.label
        }
    }

    /**
     * repeatSet to List
     * @param repeatSet alarm.repeatSet
     */
    @JvmStatic
    fun getDaysFromRepeatSet(repeatSet: Int): ArrayList<Int> {
        val days = ArrayList<Int>()
        for (i in 0 until AiAlarmUtils.getWeekAddNum().size) {
            if (repeatSet and AiAlarmUtils.getWeekAddNum()[i] != 0) {
                days.add(if (i < SIX) i + TWO else 1)
            }
        }
        return days
    }

    @JvmStatic
    fun isSupportAppFunction(context: Context): Boolean {
        return isEnableAppfunction(context) && DeviceUtils.isExpVersion(context)
    }

    @JvmStatic
    fun failedMessage(errorCode: String): AppFunctionException {
        return AppFunctionException(
            AppFunctionException.ErrorCode.ERROR_ACTION_NOT_SUPPORTED,
            errorCode
        )
    }

    @JvmStatic
    fun multipleCalls(): Boolean {
        val currentTime = SystemClock.elapsedRealtime()
        if (currentTime - lastcalltime < THROTTLE_INTERVAL) {
            return true
        }
        lastcalltime = currentTime
        return false
    }

    @JvmStatic
    fun getAppFunctionAlarm(context: Context, alarm: OplusAlarm, alarmSchedule: AlarmSchedule?, isSnoozeAvailable: Boolean): Alarm {
        d(TAG, "getAppFunctionAlarm: ${alarm.isEnabled} + $isSnoozeAvailable")
        return Alarm(
            namespace = context.packageName,
            id = alarm.id.toString(),
            label = getAlarmLabel(context, alarm),
            time = if (alarm.getmGarbSwitch() == 1) {
                val alarmTime = AlarmUtils.getAlarmTime(alarm, "getAlarmNextTime")
                TimeOfDay(context.packageName, alarm.id.toString(),
                    alarmTime.get(Calendar.HOUR_OF_DAY), alarmTime.get(Calendar.MINUTE), 0, 0)
            } else {
                TimeOfDay(context.packageName, alarm.id.toString(), alarm.hour, alarm.minutes, 0, 0)
            },
            alarmStatus = if (alarm.isEnabled) {
                if (isSnoozeAvailable) {
                    AlarmStatus.FIRING
                } else {
                    AlarmStatus.SCHEDULED
                }
            } else {
                AlarmStatus.DISMISSED
            },
            dayPattern = getDayPattern(context.packageName, alarm, alarmSchedule)
        )
    }

    /**
     * getDayPattern from alarm
     */
    @JvmStatic
    fun getDayPattern(
        packageName: String,
        alarm: OplusAlarm,
        alarmSchedule: AlarmSchedule?
    ): DayPattern {
        return if (alarm.repeatSet == 0 || alarm.getmLoopSwitch() == 1 ||
            alarm.getmGarbSwitch() == 1
        ) {
            DayPattern(
                date = if (alarmSchedule != null) {
                    Date(packageName, alarm.id.toString(), alarmSchedule.year, alarmSchedule.month, alarmSchedule.day)
                } else {
                    val alarmTime = AlarmUtils.getAlarmTime(alarm, "getAlarmNextTime")
                    Date(packageName, alarm.id.toString(), alarmTime.get(Calendar.YEAR),
                        alarmTime.get(Calendar.MONTH) + 1, alarmTime.get(Calendar.DAY_OF_MONTH))
                }
            )
        } else {
            DayPattern(recurrencePattern = getRrulFromDays(alarm.repeatSet))
        }
    }

    /**
     * repeatSet to google Rrul
     * @param repeatSet alarm.repeatSet
     */
    @JvmStatic
    private fun getRrulFromDays(repeatSet: Int): String {
        val days = getDaysFromRepeatSet(repeatSet)
        val dayStrings = ArrayList<String>()
        for (day in days) {
            daysToSting[day]?.let {
                dayStrings.add(it)
            }
        }
        return REPEAT_WEEKLY + dayStrings.joinToString(",")
    }
}