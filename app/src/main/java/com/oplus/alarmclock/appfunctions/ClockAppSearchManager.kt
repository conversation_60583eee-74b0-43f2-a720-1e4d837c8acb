/****************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockAppSearchManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: <PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  Yangchenguang  2025/3/10     1.0            appfunctions : ClockAppSearchManager
 ****************************************************************/
package com.oplus.alarmclock.appfunctions

import android.content.Context
import androidx.appsearch.app.GetByDocumentIdRequest
import androidx.appsearch.app.PutDocumentsRequest
import androidx.appsearch.app.RemoveByDocumentIdRequest
import androidx.appsearch.app.SearchSpec
import androidx.appsearch.builtintypes.Alarm
import androidx.appsearch.builtintypes.AlarmInstance
import com.heytap.addon.os.WaveformEffect
import com.oplus.alarmclock.alarmclock.AlarmUtils
import com.oplus.alarmclock.appfunctions.AppFunctionsUtils.getAlarmLabel
import com.oplus.alarmclock.appfunctions.AppFunctionsUtils.getAlertTime
import com.oplus.alarmclock.appfunctions.AppFunctionsUtils.getDaysFromRepeatSet
import com.oplus.alarmclock.appfunctions.AppFunctionsUtils.isSupportAppFunction
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.alarmclock.Alarm as OplusAlarm

object ClockAppSearchManager : BaseAppSearch() {

    private const val TAG: String = "ClockAppSearchManager"
    private const val MIN: Long = 60 * 1000

    /**
     * 开机、搬家、数据库更新
     */
    @JvmStatic
    @Synchronized
    fun initAlarms(context: Context?, isBootComplete: Boolean) {
        if (context == null) {
            Log.d(TAG, "initAlarms: context is Null")
        } else {
            if (isSupportAppFunction(context)) {
                val allAlarms = AlarmUtils.getAllAlarms(context)
                Log.d(TAG, "initAlarms: ${allAlarms.size} , isBootComplete: $isBootComplete")
                val appSearchAlarms = ArrayList<Alarm>()
                if (!allAlarms.isNullOrEmpty()) {
                    for (opAlarm in allAlarms) {
                        if (isBootComplete && opAlarm.isEnabled) {
                            // fixAlarm 会执行打开的alarm
                            continue
                        }
                        getAppSearchAlarm(
                            context,
                            getDaysFromRepeatSet(opAlarm.repeatSet),
                            opAlarm,
                            false
                        )?.let {
                            appSearchAlarms.add(it)
                        }
                    }
                    if (allAlarms.isNotEmpty()) {
                        addAlarms(context, appSearchAlarms, isInitSession = true)
                    }
                }
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun addAlarms(context: Context, alarms: ArrayList<Alarm>, isInitSession: Boolean) {
        if (isSupportAppFunction(context)) {
            if (alarms.isEmpty()) {
                return
            }
            if (isInitSession) {
                initSession(context, true)
            }
            kotlin.runCatching {
                val putRequest = PutDocumentsRequest.Builder()
                    .addDocuments(alarms)
                    .build()
                val putAsyncFuture = sessionAlarm?.putAsync(putRequest)
                executor?.let { executor ->
                        putAsyncFuture?.let {
                            handleFutureCallback(it, executor,
                                onSuccess = { result ->
                                    if (result.isSuccess) {
                                        Log.d(TAG, "addAlarms: success $result")
                                    } else {
                                        Log.d(TAG, "addAlarms: failed")
                                    }
                                    close(true)
                                },
                                onFailure = { t ->
                                    Log.d(TAG, "putRequest failed: ${t.message}")
                                    close(true)
                                }
                            )
                        }
                } ?: close(true)
            }.onFailure { e ->
                Log.d(TAG, "addAlarms Error: ${e.message}")
                close(true)
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun deleteAlarms(context: Context, ids: ArrayList<String>, isInitSession: Boolean) {
        kotlin.runCatching {
            if (isInitSession) {
                initSession(context, true)
            }
            NAMESPACE?.let { namespace ->
                val removeRequest =
                    RemoveByDocumentIdRequest.Builder(namespace).addIds(ids).build()
                val removeAsyncFuture = sessionAlarm?.removeAsync(removeRequest)
                executor?.let { executor ->
                    removeAsyncFuture?.let {
                        handleFutureCallback(it, executor,
                            onSuccess = { result ->
                                if (result.isSuccess) {
                                    Log.d(TAG, "deleteAlarms result.successes: $result")
                                } else {
                                    Log.d(TAG, "deleteAlarms: failed")
                                }
                                close(true)
                            },
                            onFailure = { t ->
                                Log.d(TAG, "RemoveByDocumentIdRequest failed: ${t.message}")
                                close(true)
                            }
                        )
                    }
                } ?: close(true)
            } ?: close(true)
        }.onFailure { e ->
            Log.d(TAG, "deleteAlarms Error: ${e.message}")
            close(true)
        }
    }

    @JvmStatic
    @Synchronized
    fun deleteAllAlarms(context: Context) {
        if (DeviceUtils.isExpVersion(context)) {
            initSession(context, true)
            kotlin.runCatching {
                NAMESPACE?.let { namespace ->
                    val searchRequest = SearchSpec.Builder()
                        .addFilterNamespaces(namespace)
                        .addFilterDocumentClasses(Alarm::class.java)
                        .build()
                    val searchFuture = sessionAlarm?.search("", searchRequest)?.nextPageAsync
                    executor?.let { executor ->
                        searchFuture?.let {
                                handleFutureCallback(it, executor,
                                    onSuccess = { result ->
                                        if (result.isNotEmpty()) {
                                            Log.d(TAG, "deleteAllAlarms search data success: $result")
                                            val alarmIds = ArrayList<String>()
                                            for (res in result) {
                                                alarmIds.add(res.genericDocument.toDocumentClass(Alarm::class.java).id)
                                            }
                                            deleteAlarms(
                                                context,
                                                alarmIds,
                                                isInitSession = false
                                            )
                                        }
                                    },
                                    onFailure = { t ->
                                        Log.d(TAG, "deleteAllAlarms search Error: " + t.message)
                                        close(true)
                                    })
                            } ?: close(true)
                    } ?: close(true)
                } ?: close(true)
            }.onFailure { e ->
                Log.d(TAG, "deleteAllAlarms Error: ${e.message}")
                close(true)
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun updateAlarms(context: Context, alarmId: String, isCloseOnce: Boolean, nextAlertTime: Long) {
        if (isSupportAppFunction(context)) {
            Log.d(TAG, "updateAlarms: alarmId = $alarmId,isCloseOnce = $isCloseOnce, nextAlertTime = $nextAlertTime")
            initSession(context, true)
            kotlin.runCatching {
                NAMESPACE?.let { namespace ->
                    val getByDocumentIdRequest = GetByDocumentIdRequest.Builder(namespace).addIds(alarmId).build()
                    val searchFuture = sessionAlarm?.getByDocumentIdAsync(getByDocumentIdRequest)
                    executor?.let { executor ->
                        searchFuture?.let {
                                handleFutureCallback(it, executor,
                                    onSuccess = { result ->
                                        if (result.isSuccess) {
                                            val apAlarm =
                                                result.successes[alarmId]?.toDocumentClass(Alarm::class.java)
                                            apAlarm?.let {
                                                Alarm.Builder(apAlarm).apply {
                                                    if (isCloseOnce) {
                                                        setNextInstance(
                                                            AlarmInstance.Builder(namespace, alarmId,
                                                                AppFunctionsUtils.getNextAlertTime(nextAlertTime)
                                                            ).apply {
                                                                apAlarm.nextInstance?.snoozeDurationMillis?.let { snoozeDuration ->
                                                                    setSnoozeDurationMillis(snoozeDuration)
                                                                }
                                                                setStatus(AlarmInstance.STATUS_SCHEDULED)
                                                            }.build()
                                                        )
                                                    } else {
                                                        setEnabled(false)
                                                        it.nextInstance?.let { mNextInstance ->
                                                            setNextInstance(AlarmInstance.Builder(mNextInstance).apply {
                                                                setStatus(AlarmInstance.STATUS_SCHEDULED)
                                                            }.build())
                                                        }
                                                    }
                                                }.build().let { putAlarm ->
                                                    val arrayList = ArrayList<Alarm>()
                                                    arrayList.add(putAlarm)
                                                    addAlarms(context, arrayList, isInitSession = false)
                                                }
                                            }
                                        }
                                    },
                                    onFailure = { t ->
                                        Log.d(TAG, "search Error: " + t.message)
                                        close(true)
                                    })
                            } ?: close(true)
                    } ?: close(true)
                } ?: close(true)
            }.onFailure { e ->
                Log.d(TAG, "updateAlarms Error: ${e.message}")
                close(true)
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun updateAlarmByAF(context: Context, alarm: OplusAlarm) {
        if (isSupportAppFunction(context)) {
            initSession(context, true)
            kotlin.runCatching {
                NAMESPACE?.let { namespace ->
                    val getByDocumentIdRequest =
                        GetByDocumentIdRequest.Builder(namespace).addIds(alarm.id.toString())
                            .build()
                    val searchFuture = sessionAlarm?.getByDocumentIdAsync(getByDocumentIdRequest)
                    executor?.let { executor ->
                        searchFuture?.let {
                                handleFutureCallback(it, executor,
                                    onSuccess = { result ->
                                        if (result.isSuccess) {
                                            getAppSearchAlarm(
                                                context,
                                                getDaysFromRepeatSet(alarm.repeatSet),
                                                alarm,
                                                false
                                            )?.let { appSearchAlarm ->
                                                val arrayList = ArrayList<Alarm>()
                                                arrayList.add(appSearchAlarm)
                                                addAlarms(
                                                    context,
                                                    arrayList,
                                                    isInitSession = false
                                                )
                                            }
                                        }
                                    },
                                    onFailure = { t ->
                                        Log.d(TAG, "search Error: " + t.message)
                                        close(true)
                                    })
                            } ?: close(true)
                    } ?: close(true)
                } ?: close(true)
            }.onFailure { e ->
                Log.d(TAG, "updateAlarmByAF Error: ${e.message}")
                close(true)
            }
        }
    }

    @JvmStatic
    @Synchronized
    fun updateAlarmState(context: Context, alarmID: Long, alarmState: Int) {
        if (isSupportAppFunction(context)) {
            initSession(context, true)
            kotlin.runCatching {
                Log.d(TAG, "updateAlarmState: $alarmState")
                NAMESPACE?.let { namespace ->
                    val getByDocumentIdRequest =
                        GetByDocumentIdRequest.Builder(namespace).addIds(alarmID.toString())
                            .build()
                    val searchFuture = sessionAlarm?.getByDocumentIdAsync(getByDocumentIdRequest)
                    executor?.let { executor ->
                        searchFuture?.let {
                                handleFutureCallback(it, executor,
                                    onSuccess = { result ->
                                        if (result.isSuccess) {
                                            val alarm = result.successes[alarmID.toString()]?.toDocumentClass(Alarm::class.java)
                                            val updatedAlarm =
                                                alarm?.run {
                                                    Alarm.Builder(alarm)
                                                        .setNextInstance(
                                                            alarm.nextInstance?.let { it1 ->
                                                                AlarmInstance.Builder(it1)
                                                                    .setStatus(alarmState)
                                                                    .build()
                                                            }).build()
                                                }
                                            updatedAlarm?.let {
                                                val arrayList = ArrayList<Alarm>()
                                                arrayList.add(updatedAlarm)
                                                addAlarms(
                                                    context,
                                                    arrayList,
                                                    isInitSession = false
                                                )
                                            }
                                        }
                                    },
                                    onFailure = { t ->
                                        Log.d(TAG, "search Error: " + t.message)
                                        close(true)
                                    })
                            } ?: close(true)
                    } ?: close(true)
                } ?: close(true)
            }.onFailure { e ->
                Log.d(TAG, "updateAlarmState Error: ${e.message}")
                close(true)
            }
        }
    }

    @JvmStatic
    @Suppress("SpreadOperator")
    fun getAppSearchAlarm(
        context: Context,
        repeatDays: ArrayList<Int>?,
        alarm: OplusAlarm,
        isNeedEnable: Boolean
    ): Alarm? {
        return if (isSupportAppFunction(context)) {
            initNameSpace(context)
            NAMESPACE?.let { namespace ->
                Alarm.Builder(namespace, alarm.id.toString()).apply {
                    setName(getAlarmLabel(context, alarm))
                    setHour(alarm.hour)
                    setMinute(alarm.minutes)
                    if (isNeedEnable) {
                        setEnabled(true)
                    } else {
                        setEnabled(alarm.isEnabled)
                    }
                    setShouldVibrate(alarm.vibrate != WaveformEffect.EFFECT_RINGTONE_NOVIBRATE)
                    if (repeatDays != null && alarm.getmLoopSwitch() != 1 && !isGarbAlarm(alarm)) {
                        setDaysOfWeek(*repeatDays.toIntArray())
                    }
                    try {
                        getAlertTime(alarm)?.let {
                            setNextInstance(
                                AlarmInstance.Builder(
                                    namespace,
                                    alarm.id.toString(),
                                    it
                                ).apply {
                                    setSnoozeDurationMillis(
                                        if (alarm.snoonzeItem == 1) {
                                            alarm.getmSnoozeTime() * MIN
                                        } else {
                                            -1
                                        }
                                    )
                                    setStatus(AlarmInstance.STATUS_SCHEDULED)
                                }.build()
                            )
                        }
                    } catch (e: IllegalArgumentException) {
                        Log.d(TAG, "getAppSearchAlarm: error $e")
                    }
                    setOriginatingDevice(Alarm.ORIGINATING_DEVICE_SMART_PHONE)
                }.build()
            }
        } else {
            null
        }
    }

    private fun isGarbAlarm(alarm: OplusAlarm): Boolean {
        return alarm.getmGarbSwitch() == 1
    }
}