/*****************************************************************
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: BaseFragment.java
 ** Description:
 ** Version: V 1.0
 ** Date : 2019-09-05
 ** Author: Yuxiaolong
 **
 ****************************************************************/
package com.oplus.alarmclock;

import android.app.ActivityOptions;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.COUICollapsableAppBarLayout;
import com.google.android.material.appbar.COUICollapsingToolbarLayout;
import com.google.android.material.appbar.COUIDividerAppBarLayout;
import com.oapm.perftest.PerfTest;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.oplus.alarmclock.alarmclock.AlarmSettingActivity;
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils;
import com.oplus.alarmclock.globalclock.AddCityFragment;
import com.oplus.alarmclock.utils.BackgroundUtils;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.IBaseChannel;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;
import com.oplus.clock.common.utils.Log;
import com.oplus.anim.EffectiveAnimationView;
import com.oplus.flexiblewindow.FlexibleWindowManager;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;


public class BaseFragment extends Fragment {

    public static final float ALPHA_ZERO = 1.0f;
    private static final String TAG = "BaseFragment";
    private static final float ALPHA_HALF = 0.5f;
    public View mDividerLine;
    protected Context mContext;
    protected boolean mMarkedNeedQuitEditMode = false;


    public void markNeedClearEdit() {
        mMarkedNeedQuitEditMode = true;
    }


    protected int getCurrentCount() {
        return -1;
    }

    public void setToolbarTitle(AlarmClock main, int selectCount) {

        String content = null;
        if (selectCount == getCurrentCount()) {
            content = getString(R.string.selected_all_item);
        } else {
            content = (selectCount == 0) ? getString(R.string.select_zero_text)
                    : getString(R.string.select_count_text, selectCount);
        }
    }

    public String getToolbarTitle(int selectCount) {

        String content = null;
        if (selectCount == getCurrentCount()) {
            content = getString(R.string.selected_all_item);
        } else {
            content = (selectCount == 0) ? getString(R.string.select_zero_text)
                    : getString(R.string.select_count_text, selectCount);
        }
        return content;
    }

    protected void redDotSetting(COUIToolbar mToolbar) {
        if ((mToolbar == null) || mToolbar.getMenu() == null) {
            return;
        }
        MenuItem settingMenu = mToolbar.getMenu().findItem(R.id.settings);
        if (settingMenu == null) {
            return;
        }
        try {
            if (AlarmCloseModelUtils.Companion.getSInstance().getMCloseModelHideRed()) {
                mToolbar.setRedDot(R.id.settings, -1);
            } else {
                mToolbar.setRedDot(R.id.settings, 0);
            }
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "redDotSetting exception:" + e.getMessage());
        }
    }

    protected void initToolbar(CoordinatorLayout coor, COUIToolbar mToolbar, COUIDividerAppBarLayout mAppBarLayout,
                               COUICollapsingToolbarLayout couiCollapsingToolbarLayout, int menuId) {
        if (mToolbar == null || coor == null || mAppBarLayout == null) {
            return;
        }
        coor.setPadding(coor.getPaddingLeft(), Utils.getStatusBarHeight(mToolbar.getContext()), coor.getPaddingRight(), coor.getPaddingBottom());
        mToolbar.inflateMenu(menuId);
        mToolbar.setTitleMarginStart(0);
        mToolbar.setTitle("");
        mToolbar.setIsTitleCenterStyle(false);
        if (couiCollapsingToolbarLayout != null) {
            couiCollapsingToolbarLayout.setTitle(" ");
        }
        if (mAppBarLayout instanceof COUICollapsableAppBarLayout) {
            ((COUICollapsableAppBarLayout) mAppBarLayout).setSubtitleHideEnable(true);
            ((COUICollapsableAppBarLayout) mAppBarLayout).setStartPaddingBottom(getResources().getDimensionPixelOffset(R.dimen.layout_dp_8));
        }
        redDotSetting(mToolbar);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (getContext() != null) {
            flexibleScenario();
        }
    }

    protected boolean isEditMode() {
        return false;
    }

    /**
     * 浮窗模式下处理逻辑，子类可以重写此方法来处理浮窗模式下的逻辑，默认不处理
     */
    protected void flexibleScenario() {
        Log.i(TAG, "flexibleScenario");
    }

    protected String getTitle() {
        return "";
    }

    /*是否需要显示副标题*/
    protected boolean shouldDisplaySubTitle() {
        return false;
    }


    public String getDeleteTitle(int selectCount, int totalCount) {
        if ((selectCount == 0)) {
            return getString(R.string.oplus_delete);
        } else if (selectCount == totalCount) {
            return getString(R.string.delete_all_item);
        } else {
            return getString(R.string.oplus_delete_multi_item, selectCount);
        }
    }

    public void playEmptyAnimOrShowEmptyIcon(EffectiveAnimationView animationView, TextView mEmptyTextView, int tabPage) {
        if ((animationView != null) && (animationView.getVisibility() == View.VISIBLE)) {
            if (FoldScreenUtils.isInDealMultiWindowMode(isInMultiWindowMode()) && (tabPage != AddCityFragment.ADD_CITY_FRAGMENT)) {
                Log.d("TAG", "animationView INVISIBLE");
                animationView.setVisibility(View.INVISIBLE);
                if (mEmptyTextView != null) {
                    mEmptyTextView.setVisibility(View.INVISIBLE);
                }
                return;
            }
            Log.d("TAG", "playEmptyAnim0 ");
            if (mEmptyTextView != null) {
                mEmptyTextView.setVisibility(View.VISIBLE);
            }
            boolean isDark = COUIDarkModeUtil.isNightMode(mContext);
            /**此处需修改为1f，否在在暗色模式下动画有异常，参见bug6525295*/
            animationView.setAlpha(ALPHA_ZERO);
            BackgroundUtils.setEmptyBackground(isDark, animationView, tabPage);
        }
    }

    public void resetEmptyAnimToBegin(EffectiveAnimationView animationView) {
        if ((animationView != null) && (animationView.getVisibility() == View.VISIBLE)
                && (!animationView.isAnimating())) {
            if (!(ChannelManager.INSTANCE.getChannelUtils().getChannel() == IBaseChannel.CHANNEL_WPLUS)) {
                Log.d(TAG, "resetEmptyAnimToBegin0");
                animationView.setFrame(0);
                animationView.setProgress(0);
                /**重置动画效果时透明度设置需同上方一致，否则自检有闪烁现象*/
                animationView.setAlpha(ALPHA_ZERO);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        PerfTest.leakWatch(this);
    }

    public boolean isInMultiWindowMode() {
        boolean isInMultiWindowMode = false;
        FragmentActivity activity = getActivity();
        if (activity != null) {
            isInMultiWindowMode = activity.isInMultiWindowMode();
        }
        return isInMultiWindowMode;
    }

    /**
     * 跳转到设置页面
     */
    public void startToSetting() {
        //跳转设置默认铃声设置会读取音频文件，需要检查是否同意音频须知
        buildToSetting();
    }

    /**
     * 跳转设置
     */
    private void buildToSetting() {
        FlexibleWindowManager flexibleM = FlexibleWindowUtils.getFlexibleWindowManager();
        if (FlexibleWindowUtils.isSupportFlexibleActivity() && flexibleM != null) {
            try {
                ActivityOptions options = ActivityOptions.makeBasic();
                Bundle exBundle = new Bundle();
                exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true);
                exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT, false);
                int direction = FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_RIGHT;
                if (Utils.isRtl()) {
                    direction = FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT;
                }
                exBundle.putInt(FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION, direction);
                Intent intent = new Intent(getActivity(), AlarmSettingActivity.class);
                intent.putExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, AlarmClock.sStartFromScreen);
                startActivity(intent, flexibleM.setExtraBundle(options, exBundle));
            } catch (NoSuchMethodError error) {
                Log.e(TAG, "FlexibleWindowManager NoSuchMethodError");
                toStartSetting();
            } catch (NoSuchFieldError error) {
                Log.e(TAG, "FlexibleWindowManager NoSuchFieldError");
                toStartSetting();
            }
        } else {
            toStartSetting();
        }
    }


    /**
     * 跳转至全屏设置页面
     */
    private void toStartSetting() {
        Intent intent = new Intent(getActivity(), AlarmSettingActivity.class);
        intent.putExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, AlarmClock.sStartFromScreen);
        startActivity(intent);
    }

}
