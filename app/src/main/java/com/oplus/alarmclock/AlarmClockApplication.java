/**
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: AlarmClockApplication
 * ModuleName: Clock Author: <PERSON> Create Date: Description: The Clock Application.
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2016-5-24 Amy Created
 */
package com.oplus.alarmclock;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.StrictMode;

import androidx.annotation.NonNull;

import com.coloros.widget.smallweather.ClockWidgetState;
import com.google.android.appfunctions.AppFunctionAndroidApp;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.oplus.alarmclock.alarmclock.holiday.LegalHolidayParser;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.globalclock.romupdate.LanguageUtil;
import com.oplus.alarmclock.provider.ClockProviderOPlus;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.AppFeatureUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.ProcessGuard;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.osdk.AdapterHelperUtils;
import com.oplus.clock.common.osdk.CompatUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.clock.common.utils.VersionUtils;
import com.oplus.statistics.OplusTrack;
import com.oplus.stdid.sdk.StdIDSDK;
import com.oplus.wrapper.graphics.Canvas;

import java.lang.reflect.Method;

@AppFunctionAndroidApp
public class AlarmClockApplication extends Application {

    public static final String SHARED_PREFS_ALARM_CLOCK_APP = "shared_prefs_alarm_app";
    public static final String SHARED_PREFS_ALARM_CLOCK_APP_NOTIFY = "shared_prefs_alarm_app_notify";

    private static final boolean PERFORMANCE_DEBUG = false;

    private static final String TAG = "AlarmClockApplication";

    private static final int FIVE_SECONDS = 5 * 1000; // 5s

    private static int sActivityCount;
    private static AlarmClockApplication sInstance;

    public static AlarmClockApplication getInstance() {
        return sInstance;
    }

    private static void setApplicationInstance(AlarmClockApplication instance) {
        sInstance = instance;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        AdapterHelperUtils.init(this);
        setApplicationInstance(this);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        LanguageUtil.setAppLanguage(getApplicationContext());
    }

    @Override
    public void onCreate() {
        Log.i(TAG, "onCreate:" + getProcessName());
        super.onCreate();
        if (PERFORMANCE_DEBUG) {
            setStrictMode();
        }
        final Context context = this;
        //Protect the process in 5s.
        ProcessGuard.startProtect(context, "AlarmClockApplication", FIVE_SECONDS);
        registerActivityListener();
        if (!DeviceUtils.isExpVersion(this) && Utils.isMainProcess(this)) {
            StdIDSDK.init(context);
            LegalHolidayParser.initializeHoliday();
        }
        enableOPlusProvider();
        OplusTrack.init(this);
        if (Utils.isMainProcess(this)) {
            try {
                TimerSeedlingHelper.asynSupportFluidCloud(this);
            } catch (NoClassDefFoundError e) {
                Log.e(TAG, "NoClassDefFoundError TimerSeedlingHelper");
            }
            AlarmCloseModelUtils.Companion.getSInstance().init(this);
            AlarmPreferenceUtils.Companion.getInstance().refreshSyncTime();
            AppFeatureUtils.asyncSupportLightOS(this);
        } else {
            ClockWidgetState.readWidgetConfig(this);
        }
    }

    private void enableOPlusProvider() {
        if (VersionUtils.isOsVersion11_3()) {
            Log.d(TAG, "enableOPlusProvider");
            ComponentName componentName = new ComponentName(this, ClockProviderOPlus.class);
            PackageManager packageManager = getPackageManager();
            packageManager.setComponentEnabledSetting(componentName, PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }
    }

    public static int getActivityCount() {
        return sActivityCount;
    }

    private void registerActivityListener() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {

            @Override
            public void onActivityStopped(Activity arg0) {
                // do nothing
            }

            @Override
            public void onActivityStarted(Activity arg0) {
                // do nothing
            }

            @Override
            public void onActivitySaveInstanceState(Activity arg0, Bundle arg1) {
                // do nothing
            }

            @Override
            public void onActivityResumed(Activity arg0) {
                // do nothing
            }

            @Override
            public void onActivityPaused(Activity arg0) {
                // do nothing
            }

            @Override
            public void onActivityDestroyed(Activity arg0) {
                sActivityCount--;
            }

            @Override
            public void onActivityCreated(Activity arg0, Bundle arg1) {
                sActivityCount++;
            }
        });
    }

    private void setStrictMode() {
        Log.d("Enabling StrictMode policy in debug mode.");
        StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder().detectAll()
                .penaltyLog()
                .penaltyDeath()
                .build());
        StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder().detectAll()
                .penaltyLog()
                .build());
    }

    @SuppressLint("SoonBlockedPrivateApi")
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.d(TAG, "onTrimMemory: " + level);
        if (level >= TRIM_MEMORY_COMPLETE) {
            SQLiteDatabase.releaseMemory();
            if (CompatUtils.supportSysApi()) {
                try {
                    Canvas.freeCaches();
                    Canvas.freeTextLayoutCaches();
                } catch (NoClassDefFoundError | NoSuchMethodError | NoSuchFieldError | Exception e) {
                    Log.e(TAG, "onTrimMemory exception:" + e.getMessage());
                    freeCaches();
                }
            } else {
                freeCaches();
            }
        }
    }

    private void freeCaches() {
        Class<?> clazz = null;
        Method method = null;
        try {
            clazz = Class.forName("android.graphics.Canvas");
            method = clazz.getDeclaredMethod("freeCaches");
            method.invoke(null);
            method = clazz.getDeclaredMethod("freeTextLayoutCaches");
            method.invoke(null);
        } catch (Exception e) {
            Log.d(TAG, "freeCaches Exception: " + e.getMessage());
        }
    }
}
