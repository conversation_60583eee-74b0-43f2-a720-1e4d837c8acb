/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Views of the Alarms
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2018-2-1, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.drawable.Icon;
import android.os.AsyncTask;
import android.os.Build;
import android.text.TextUtils;

import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

@TargetApi(Build.VERSION_CODES.N_MR1)
public class ClockShortcutManager {

    private static final String TAG = "ShortcutManager";

    private static final String SHORTCUT_NEW_ALARM_BUILD = "set_alarm";
    private static final String SHORTCUT_NEW_ALARM_SPEECH = "voice_alarm";

    private static class InitShortcutTask extends AsyncTask<Void, Void, Void> {
        private final WeakReference<Context> mContext;

        private InitShortcutTask(Context context) {
            mContext = new WeakReference<>(context);
        }

        @Override
        protected Void doInBackground(Void... voids) {
            Context context = (mContext == null) ? null : mContext.get();
            if (context != null) {
                initShortcuts(context);
            }
            return null;
        }
    }

    public static void dynamicInitShortcuts(Context context) {
        new InitShortcutTask(context).execute();
    }

    @TargetApi(Build.VERSION_CODES.N_MR1)
    private static void initShortcuts(Context context) {
        if ((null == context) || (!Utils.isAboveO())) {
            Log.e(TAG, "Shortcuts NOT supportted!");
            return;
        }

        if (!isAppAvailable(context)) {
            Log.e(TAG, "Privacy or permission not agreed, do not show shorcuts!");
            return;
        }

        List<ShortcutInfo> infos = new ArrayList<>();

        String label = null;
        /* initialize new alarm action **/
        label = context.getResources().getString(R.string.create_alarm);
        Intent intent = new Intent(AlarmClock.ACTION_ALARM_CLOCK_SET_ALARM);
        intent.setPackage(context.getPackageName());
        ShortcutInfo newAlarm = buildShortcutInfo(context,
                SHORTCUT_NEW_ALARM_BUILD, label, label,
                R.drawable.desktop_menu_add, intent);
        if (newAlarm != null) {
            infos.add(newAlarm);
        }

        /* initialize new arlam action by speech **/
        if (Utils.isSpeechAiAvailable(context)) {
            label = context.getResources().getString(R.string.oplus_voice);
            Intent intentSpeech = new Intent(AlarmClock.ACTION_ALARM_CLOCK_VOICE_SET_ALARM);
            intentSpeech.setPackage(context.getPackageName());
            ShortcutInfo speechNewAlarm = buildShortcutInfo(context,
                    SHORTCUT_NEW_ALARM_SPEECH, label, label,
                    R.drawable.desktop_menu_voice, intentSpeech);
            if (speechNewAlarm != null) {
                infos.add(speechNewAlarm);
            }
        }

        ShortcutManager shortcutManager = context.getSystemService(ShortcutManager.class);
        if (shortcutManager != null) {
            try {
                shortcutManager.setDynamicShortcuts(infos);
                Log.d(TAG, "initShortcuts success");
            } catch (Exception e) {
                Log.e(TAG, "initShortcuts fail : " + e.getMessage());
            }
        } else {
            Log.w(TAG, "ShortcutManager is null!");
        }
    }

    @TargetApi(Build.VERSION_CODES.N_MR1)
    private static ShortcutInfo buildShortcutInfo(Context context, String id, String shortLabel,
                                                  String longLabel, int iconId, Intent intent) {
        if ((null == context) || TextUtils.isEmpty(id) || TextUtils.isEmpty(shortLabel)
                || TextUtils.isEmpty(longLabel) || (null == intent)) {
            Log.e(TAG, "BuildShortcutInfo: param error!");
            return null;
        }
        return new ShortcutInfo.Builder(context, id).setShortLabel(shortLabel)
                .setLongLabel(longLabel).setIcon(Icon.createWithResource(context, iconId))
                .setIntent(intent).build();
    }

    private static boolean isAppAvailable(Context activity) {
        if (!DeviceUtils.isExpVersion(activity)) {
            if (PrivacyPolicyAlert.isFirstEntry(activity)) {
                return false;
            }
        }

        return true;
    }
}