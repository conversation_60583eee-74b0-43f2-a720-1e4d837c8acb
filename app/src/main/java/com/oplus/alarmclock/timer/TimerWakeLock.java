/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.PowerManager;

import com.oplus.clock.common.utils.Log;

public class TimerWakeLock {
    private static final String TAG = "TimerWakeLock";
    private static PowerManager.WakeLock sCpuWakeLockPartial;
    private static PowerManager.WakeLock sCpuWakeLockFull;

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuWakeLockPartial(Context context) {
        Log.v(TAG, "acquireCpuWakeLockPartial sCpuWakeLockPartial = " + sCpuWakeLockPartial);

        if (sCpuWakeLockPartial != null) {
            return;
        }

        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        if (pm != null) {
            sCpuWakeLockPartial = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "TimerWakeLock");
            sCpuWakeLockPartial.acquire(60 * 1000);
        }
    }

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuWakeLockFull(Context context) {
        Log.v(TAG, "acquireCpuWakeLockFull sCpuWakeLockFull = " + sCpuWakeLockFull);
        if (sCpuWakeLockFull != null) {
            return;
        }

        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        if (pm != null) {
            sCpuWakeLockFull = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK
                            | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE,
                    "TimerWakeLock");
            sCpuWakeLockFull.setReferenceCounted(false);
            sCpuWakeLockFull.acquire(60 * 1000);
        }
    }

    public static void releaseCpuLockFull() {
        Log.v(TAG, "releaseCpuLockFull sCpuWakeLockFull = " + sCpuWakeLockFull);
        if (sCpuWakeLockFull != null) {
            sCpuWakeLockFull.release();
            sCpuWakeLockFull = null;
        }
    }

    public static void releaseCpuLockPartial() {
        Log.v(TAG, "releaseCpuLockPartial sCpuWakeLockPartial = " + sCpuWakeLockPartial);
        if (sCpuWakeLockPartial != null) {
            sCpuWakeLockPartial.release();
            sCpuWakeLockPartial = null;
        }
    }

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuLockTimer(Context context,long timeout) {
        Log.v(TAG, "acquireCpuLockTimer");
        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        PowerManager.WakeLock wakeLock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "acquireCpuLockTimer");
        wakeLock.acquire(timeout + 200);
    }
}
