/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */
/**
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: TimerKlaxon.java
 * ModuleName: Timer Author: MaCong Create Date: Description: the background serivice to play music
 * and vibrate
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2010-9-24 MaCong CheckList
 */

package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.provider.Settings;
import android.provider.Settings.SettingNotFoundException;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.text.TextUtils;

import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alert.player.RingtonePlayerApi;
import com.oplus.alarmclock.utils.AlarmWeatherUtils;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;

/**
 * Manages alarms and vibe. Runs as a service so that it can continue to play if another activity
 * overrides the AlarmAlert dialog.
 */
public class TimerKlaxon extends Service {

    private static final String TAG = "TimerKlaxon";
    private static final boolean DEBUG = true;

    /**
     * Play alarm up to 1 minutes before silencing
     */
    private static final int ALARM_TIMEOUT_SECONDS = 1 * 60 * 1000;

    private static final long[] sVibratePattern = new long[]{500, 500};
    private static final long[] RAPID_STRONG_WAVEFORM_TIME = new long[]{500, 500, 500, 500};

    // Internal messages
    private static final int KILLER = 1000;
    private static final int RECYCLE = 1222;
    private static AlarmWeatherUtils sAlarmWeatherUtils = null;
    private boolean mPlaying = false;
    private VibrationEffect mVibrationEffectStrong;
    private RingtonePlayerApi mRingtonePlayerApi;
    private int mAlertType;

    private float mVelocity;
    private Context mContext;
    private Handler mHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case KILLER:
                    Intent intent = new Intent(TimerConstant.STOP_ALERT);
                    LocalBroadcastManager localBroadcastManager = LocalBroadcastManager
                            .getInstance(mContext);
                    localBroadcastManager.sendBroadcast(intent);
                    Log.d(TAG, "send STOP_ALERT");
                    stopSelf();
                    break;
                default:
                    break;
            }
        }
    };

    @Override
    public void onCreate() {
        if (DEBUG) {
            Log.i(TAG, "onCreate");
        }
        super.onCreate();
        mContext = this;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stop();
        mHandler.removeMessages(RECYCLE);
        if (DEBUG) {
            Log.i(TAG, "onDestroy()");
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    @SuppressLint("InlinedApi,NewApi")
    public int onStartCommand(Intent intent, int flags, int startId) {
        // No intent, tell the system not to restart us.
        if (intent == null) {
            stopSelf();
            return START_NOT_STICKY;
        }

        String uri = intent.getStringExtra(TimerService.TIMER_RINGTONE_URI);
        mAlertType = intent.getIntExtra(TimerService.TIMER_ALERT_TYPE, 2);
        if ((TextUtils.isEmpty(uri)) || ("null".equalsIgnoreCase(uri))) {
            uri = TimerUtils.getDefaultTimerRingtoneUri(mContext).toString();
        }
        int ringerMode = getRingtonePlayer(this).ringMode();
        if (uri == null) {
            stopSelf();
            return START_NOT_STICKY;
        } else {
            if (DeviceUtils.isCmccVersion(this) && DeviceUtils.isPowerSaveEnabled(this)) {
                mRingtonePlayerApi.stopVibrator();
                Log.d(TAG, " PowerSave mode, mVibrator.cancel();");
            } else if (!(AlarmWeatherUtils.isDynamicWeatherAlert(uri)
                    && ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()
                    && DeviceUtils.isChimesWithSound(this))) {

                if (ringerMode == AudioManager.RINGER_MODE_NORMAL) {
                    int isVirbrateWhenRing = 0;
                    try {
                        isVirbrateWhenRing = Settings.System.getInt(this.getContentResolver(),
                                Settings.System.VIBRATE_WHEN_RINGING);
                    } catch (SettingNotFoundException e) {
                        Log.e(TAG, "get isVirbrateWhenRing error!");
                    }
                    if (isVirbrateWhenRing != 0) {
                        mRingtonePlayerApi.doVibrator();
                    } else {
                        mRingtonePlayerApi.stopVibrator();
                    }
                } else if (ringerMode == AudioManager.RINGER_MODE_VIBRATE) {
                    mRingtonePlayerApi.doVibrator();
                } else if (ringerMode == AudioManager.RINGER_MODE_SILENT) {
                    mRingtonePlayerApi.stopVibrator();
                }
            }
            if (AlarmWeatherUtils.isDynamicWeatherAlert(uri) && ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
                if (sAlarmWeatherUtils == null) {
                    String finalUri = uri;
                    sAlarmWeatherUtils = new AlarmWeatherUtils(new AlarmWeatherUtils.LoadWeatherTypeListener() {
                        @Override
                        public void onLoadComplete(boolean success, Object weatherAlertResOrName) {
                            play(Uri.parse(finalUri), weatherAlertResOrName, true);
                        }
                    });
                }
                sAlarmWeatherUtils.getWeatherInfo(mContext);
            } else {
                play(Uri.parse(uri), null, false);
            }
        }

        // Record the initial call state here so that the new alarm has the
        // newest state.
        return START_STICKY;
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private void play(Uri ringtoneUri, Object weatherAlertResOrName, boolean useWeatherAlert) {
        // stop() checks to see if we are already playing.
        stop();

        if (mAlertType != 1) {
            //判断不是静音
            if (!TextUtils.equals(ClockConstant.ALARM_ALERT_SILENT, ringtoneUri.toString())) {
                RingtonePlayerApi player = getRingtonePlayer(this);
                player.play(ringtoneUri, useWeatherAlert, weatherAlertResOrName, true, null);
                player.setVolume(this, AlarmUtils.isOpenBellGraduallyRings(mContext));
            }
        }

        enableKiller();
        mPlaying = true;
    }

    /**
     * Stops alarm audio and disables alarm if it not snoozed and not repeating
     */
    public void stop() {
        if (DEBUG) {
            Log.d(TAG, "TimerKlaxon.stop() mPlaying = " + mPlaying);
        }
        try {
            if (sAlarmWeatherUtils != null) {
                sAlarmWeatherUtils.freed();
                sAlarmWeatherUtils = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "stop error: " + e.getMessage());
        }
        if (mPlaying) {
            mPlaying = false;
            // Stop audio playing
            if (mRingtonePlayerApi != null) {
                if (DEBUG) {
                    Log.d(TAG, "stop play...");
                }
                mRingtonePlayerApi.stop();
                // Stop vibrator
                mRingtonePlayerApi.stopVibrator();
            }
        }
        disableKiller();
    }

    /**
     * Kills alarm audio after ALARM_TIMEOUT_SECONDS, so the alarm won't run all day. This just
     * cancels the audio, but leaves the notification popped, so the user will know that the alarm
     * tripped.
     */
    private void enableKiller() {
        mHandler.sendEmptyMessageDelayed(KILLER, ALARM_TIMEOUT_SECONDS);
    }

    private void disableKiller() {
        mHandler.removeMessages(KILLER);
    }

    private  RingtonePlayerApi getRingtonePlayer(Context context) {
        if (mRingtonePlayerApi == null) {
            mRingtonePlayerApi = new RingtonePlayerApi(
                    context.getApplicationContext(),
                    RingtonePlayerApi.TYPE_TIMER
            );
        }
        return mRingtonePlayerApi;
    }
}
