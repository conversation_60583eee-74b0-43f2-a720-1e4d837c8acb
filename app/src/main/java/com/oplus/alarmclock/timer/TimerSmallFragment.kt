/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - TimerSmallFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.TimerMainViewBinding
import com.oplus.alarmclock.timer.ui.TimerController
import com.oplus.alarmclock.view.TimerRecyclerView
import com.oplus.alarmclock.view.water.WaterClockView

class TimerSmallFragment : OplusTimerFragment<TimerMainViewBinding>() {
    override fun layoutId(): Int {
        return R.layout.timer_main_view
    }

    override fun createTimerController(index: Int): TimerController {
        return TimerController(index, this)
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
        }
    }
    override fun updateSplitParams() {
        super.updateSplitParams()
        setOplusTimerPickerMargin(
                CONTROLLER_MARGIN,
                CONTROLLER_MARGIN_TOP,
                CONTROLLER_MARGIN,
                CONTROLLER_MARGIN
        )
        mTimerController?.apply {
            mOplusTimerPicker?.updateSpinnerSplitParams(
                false,
                resources.configuration.orientation
            )
            mTimerView?.visibility = View.GONE
            val offset = resources.getDimensionPixelOffset(R.dimen.layout_dp_102)
            mTitleName?.translationY = offset.toFloat()
            mAddTimerLayout?.apply {
                val lpAddTimerLayout = layoutParams as ViewGroup.MarginLayoutParams
                lpAddTimerLayout.topMargin = -CONTROLLER_MARGIN_TOP
                layoutParams = lpAddTimerLayout
            }
            mShadowBg?.visibility = View.GONE
        }
    }

    override fun setComponentBtn() {
        mViewBinding?.apply {
            buttonStart()?.translationX = TimerAnimationManager.mButtonStartTransitionX
        }
    }
    override fun setRecyclerViewLayoutManager() {
        timerRecyclerView()?.let {
            val linearLayoutManager = LinearLayoutManager(mContext)
            it.layoutManager = linearLayoutManager
        }
    }

    fun setOplusTimerPickerMargin(left: Int, top: Int, right: Int, bottom: Int) {
        oplusTimerPicker()?.let {
            val lp = it.layoutParams as ViewGroup.MarginLayoutParams
            lp.setMargins(left, top, right, bottom)
            it.layoutParams = lp
        }
    }

    override fun timerAdd(): TextView? {
        return mViewBinding?.timerAdd
    }

    override fun oplusTimerPicker(): OplusTimePickerCustomClock? {
        return mViewBinding?.oplusTimerPicker
    }

    override fun timerView(): TimerView? {
        return mViewBinding?.timerView
    }

    override fun timerTextView(): TimerTextView? {
        return mViewBinding?.timerText
    }

    override fun titleName(): TextView? {
        return mViewBinding?.what
    }

    override fun addTimerLayout(): RelativeLayout? {
        return mViewBinding?.addTimerLayout
    }

    override fun timerProgressViewLayout(): View? {
        return mViewBinding?.timerProgressViewLayout
    }

    override fun shadowBg(): WaterClockView? {
        return mViewBinding?.timerBg
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun timerLayout(): ConstraintLayout? {
        return mViewBinding?.timerRootPort
    }

    override fun timerRecyclerView(): TimerRecyclerView? {
        return mViewBinding?.situationTimerViewList
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }

    override fun clockSize(): Int {
        return 0
    }
}