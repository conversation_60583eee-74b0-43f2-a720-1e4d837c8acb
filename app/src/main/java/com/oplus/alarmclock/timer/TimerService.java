/**
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: TimerService.java
 * ModuleName: Timer Author: <PERSON>yk Create Date: Description: the service support Timer.
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2011-8-25 <PERSON><PERSON> the service support the Timer
 * running in background
 */
// OPLUS Java File Skip Rule:LineLength
package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;

import com.coloros.widget.commondata.Constants;
import com.google.gson.JsonObject;
import com.oplus.airview.IAirViewProxy;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ai.AiSupportContentProvider;
import com.oplus.alarmclock.ai.AiSupportContentProviderPure;
import com.oplus.alarmclock.appfunctions.TimerAppSearchManger;
import com.oplus.alarmclock.stopwatch.StopWatchService;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FlashBackHelper;
import com.oplus.alarmclock.utils.FlashbackUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.NotificationUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.osdk.IntentNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.clock.common.utils.VersionUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static com.oplus.alarmclock.AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP;
import static com.oplus.alarmclock.timer.TimerSeedlingHelper.showTimerCardSecure;
import static com.oplus.alarmclock.utils.DeviceUtils.registerSuperPowerObserver;
import static com.oplus.alarmclock.utils.DeviceUtils.unRegisterSuperPowerObserver;

public class TimerService extends Service implements FlashbackUtils.FlashbackStatusListener {
    public static final String REFRESH_TIMERS = "OPLUS_TIMER_REFRESH_TIMERS";
    public static final String TIMER_STATUS_PREFERENCE = "timer_status";
    public static final String TIMER0_REMAIN_TIME = "timer0_remain";
    public static final String TIMER0_FILT_CLOCK = "filtClock";
    public static final String TIMER0_FIRST_START_TIME = "firstStartTIME";
    public static final String STOP_TIMER = "stop_timer";
    public static final String TIME_FLASHBACK_DATA_JSON = "time_flashback_data_json";
    public static final String REALTIME = "realtime";
    public static final String TIME_STATUS = "time_status";
    public static final String STOP_ALARM_ACTION = "com.oplus.alarmclock.STOP_ALARM";
    public static final String TIMER_STOP_TIMER = "com.oplus.alarmclock.Timer.STOP_TIMER";
    public static final String TIMER_ALERT_ACTION = "oplus.intent.action.TIMER.ALERT";

    public static final String TIMER_RINGTONE_URI = "timer_ringtone_uri";
    public static final String TIMER_ALERT_TYPE = "timer_Alert_Type";
    public static final String DEFAULT_TIMER_INDEX = "0";
    public static final String DEFAULT_TIMER_NAME = "Timer";
    public static final String TIMER_NAME = "timer_name";
    public static final String TIMER_INDEX = "timer_index";
    public static final String TIMER_OWNER_USER_ID = "timer_owner_user_id";
    public static final String KEY_IS_FROM_NOTIFICATION = "is_from_notification";
    public static final String TIMER_RING_URI = "timer_ring_uri";
    public static final String TIMER_RING_NAME = "timer_ring_name";
    public static final String TIMER_DATA_TOTAL_TIME_PREFERENCE = "timer_total_time";
    public static final String SELECTED_TIMER_DES = "selected_timer_des";
    public static final long ONE_MINUTE_IN_MILLS = 120 * 1000;
    public static final int INTERVAL_PERIOD_30 = 30;
    private static final int FLASHBACK_ICON_TYPE_TIMER = 2;
    private static final String TAG = "TimerService";
    private static final String STOP_ALERT = "OPLUS_TIMER_STOP_ALERT";
    private static final String TIMER_DATA_PREFERENCE = "timer_data";
    private static final String TIMER_DATA_START_PREFERENCE = "timer_start";
    private static final String TIMER_TIME_ID_PREFERENCE = "timer_id";
    private static final String TIMER_TIME_NAME_PREFERENCE = "timer_name";
    private static final String TIMER_TIME_RING_PREFERENCE = "timer_ring";
    private static final String TIMER_TIME_RING_NAME_PREFERENCE = "timer_ring_name";
    private static final String TIMER_DESCRIPTION_PREFERENCE = "timer_description";
    private static final int STATUS_PREPARE = 0;
    private static final int STATUS_START = 1;
    private static final int STATUS_PAUSE = 2;
    private static final int STATUS_OVER = 4;
    private static final long DEFAULT_ANIMAL_VALUE = 1000;
    private static final int INTERVAL_PERIOD = 100;
    private static final int INVALIDATE = 1111;
    private static final int TIMER_OVER = 1112;
    private static final int ALARM_TIME_COME = 1113;
    private static final boolean DEBUG = true;
    private static final long ALARM_TIME = 5000;
    private static final long THOUSAND = 1000;
    private static final long ELAPSED_TIME = 900;
    private static final long SHORT_TIME = 30 * 1000;
    private static final long MIDDLE_TIME = 4 * SHORT_TIME;
    private static final int SHORT_REFRESH_TIME = 10;
    private static final int MIDDLE_REFRESH_TIME = 30;
    private static final int DEFAULT_REFRESH_TIME = 100;
    //public static final int FLAG_RECEIVER_INCLUDE_BACKGROUND = 0x01000000;
    private static final int INTENT_FLAG_RECEIVER_INCLUDE_BACKGROUND = 0x01000000;
    private final static Object TIMER_LOCK = new Object();
    private static boolean sTimerRunning = false;
    private long mRefreshTime = 0;
    private long mTempRemainTime;
    private OplusTimer mSelectedTimer;
    private AlarmManager mAlarmManager;
    private IBinder mBinder;
    private HashMap<Integer, TimeObj> mTimeObjMap = new HashMap<Integer, TimeObj>();
    private LocalBroadcastManager mLocalBroadcastManager;
    /**
     * Flashback Function Module
     */
    private FlashbackUtils mFlashbackUtils;
    private IAirViewProxy mIAirViewProxy;
    private boolean mFlashbackStatus;
    private boolean mIsInterceptSend = false;
    private boolean mIsSelectedTimer = false;
    private BroadcastReceiver mReceiver = new TimerBroadCastReceiver();
    private BroadcastReceiver mTimerAlertReceiver = new TimerAlertReceiver();
    private boolean mHasStartService = false;
    private ContentObserver mSuperPowerSaveObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            TimeObj obj = mTimeObjMap.get(0);
            if (obj != null) {
                if (!obj.mIsStart && !obj.mIsPause) {
                    return;
                }
                boolean isSuperPowerSaveMode = DeviceUtils.isSuperPowerSaveMode(TimerService.this);
                Log.d(TAG, "onChange isSuperPowerSaveMode:" + isSuperPowerSaveMode + " selfChange:" + selfChange);
                if (isSuperPowerSaveMode) {
                    TimerSeedlingHelper.closeSeedlingCard(TimerService.this);
                } else {
                    TimerNotificationManager.cancelTimerNotification(TimerService.this);
                }
                obj.showTimerNotification(obj.mIsStart);
            }
        }
    };
    private BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {

        public void onReceive(Context context, Intent intent) {

            Log.i(TAG, "receive intent = " + intent.getAction());
            String action = intent.getAction();
            if (!TextUtils.isEmpty(action)) {
                if (TIMER_STOP_TIMER.endsWith(action)) {
                    stopAllTimer();
                } else if (STOP_ALERT.equals(action)) {
                    stopAllTimer();
                }
            }
        }
    };

    @SuppressLint("CommitPrefEdits")
    public static void resetTimerStatus(Context context) {
        if (context == null) {
            context = AlarmClockApplication.getInstance();
        }
        synchronized (TIMER_LOCK) {
            if (sTimerRunning) {
                Log.i(TAG, "Timer is running");
                return;
            }
        }
        String name = PrefUtils.getString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_NAME_PREFERENCE, "");
        if (!TextUtils.isEmpty(name)) {
            //清除AlarmManager
            Log.i(TAG, "cancel timer ring");
            String timerRingUri = PrefUtils.getString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_PREFERENCE, "");
            String timerRingName = PrefUtils.getString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_NAME_PREFERENCE, "");
            String timeDes = PrefUtils.getString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DESCRIPTION_PREFERENCE, "");
            Intent intent = createTimerIntent(context, name, timerRingUri, timerRingName, timeDes, "0");
            PendingIntent alertIntent = PendingIntent.getBroadcast(context, 0, intent,
                    Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            alarmManager.cancel(alertIntent);
        }
        PrefUtils.putLong(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_PREFERENCE, 0);
        PrefUtils.putLong(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_START_PREFERENCE, 0);
        PrefUtils.putLong(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_TOTAL_TIME_PREFERENCE, 0);
        PrefUtils.putInt(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_STATUS_PREFERENCE, STATUS_PREPARE);
        PrefUtils.putInt(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_ID_PREFERENCE, 0);
        PrefUtils.putString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_NAME_PREFERENCE, "");
        PrefUtils.putString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_PREFERENCE, "");
        PrefUtils.putLong(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FIRST_START_TIME, 0);
        PrefUtils.putString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_NAME_PREFERENCE, "");
        PrefUtils.putString(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DESCRIPTION_PREFERENCE, "");
        PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FILT_CLOCK, 0);
        PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_REMAIN_TIME, 0);
        // Save to preference immediately.
        Log.d(TAG, "resetTimerStatus end.");
    }

    /**
     * 创建timer响铃intent
     *
     * @param context
     * @param timerName
     * @param timerRingUri
     * @param timerRingName
     * @param timeDes
     * @param index
     * @return
     */
    private static Intent createTimerIntent(Context context, String timerName, String timerRingUri,
                                            String timerRingName, String timeDes, String index) {
        Intent intent = new Intent(TIMER_ALERT_ACTION);
        intent.putExtra(TIMER_NAME, timerName);
        intent.putExtra(TIMER_RING_URI, timerRingUri);
        intent.putExtra(TIMER_RING_NAME, timerRingName);
        intent.putExtra(TIMER_INDEX, index);
        if (!TextUtils.isEmpty(timeDes)) {
            intent.putExtra(SELECTED_TIMER_DES, timeDes);
        }
        if (Utils.isAboveQ()) {
            intent.putExtra(TIMER_OWNER_USER_ID, AppPlatformUtils.getCurrentUser());
            intent.addFlags(IntentNativeUtils.getFLAG_RECEIVER_INCLUDE_BACKGROUND());
        } else {
            intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
        }
        intent.setPackage(context.getPackageName());
        return intent;
    }

    @Override
    public IBinder onBind(Intent intent) {
        if (DEBUG) {
            Log.i(TAG, "onBind called!");
        }
        return mBinder;
    }

    @SuppressLint("WrongConstant")
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        flags = START_REDELIVER_INTENT;
        if (intent != null) {
            int type = intent.getIntExtra(TimerNotificationManager.TIMER_ACTION_TYPE, -1);
            if (type == TimerNotificationManager.TIMER_NOTIFICATION_TYPE_INTERCEPT_SEND) {
                mIsInterceptSend = true;
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    /*
     * Remove by Gaowenbo, this is RTC time method, change system time can effect the timer. public
     * void startTimerAlert() { long time = System.currentTimeMillis(); long alertTime = time +
     * mCountTime; Intent intent = new Intent(TimerAlarm.TIMER_ALERT_ACTION); mAlertIntent =
     * PendingIntent.getBroadcast(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
     * mAlarmManager.set(AlarmManager.RTC_WAKEUP, alertTime, mAlertIntent); }
     */

    @Override
    public void onCreate() {
        super.onCreate();

        if (!DeviceUtils.isExpVersion(this)) {
            Log.d(TAG, "onCreate not exp version");
            mFlashbackUtils = new FlashbackUtils(this);
            mFlashbackUtils.bindAirViewService(getApplicationContext());
        }
        Log.i(TAG, "onCreate called!");
        mAlarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        mBinder = new TimerBinder();

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        registerReceiver(mReceiver, filter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);

        IntentFilter filterAlert = new IntentFilter();
        filterAlert.addAction(TimerAlertReceiver.ACTION_STOP_TIMER_RING);
        filterAlert.addAction(Intent.ACTION_SHUTDOWN);
        registerReceiver(mTimerAlertReceiver, filterAlert, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_NOT_EXPORTED);

        IntentFilter filterLocal = new IntentFilter();
        filterLocal.addAction(STOP_ALERT);
        filterLocal.addAction(TIMER_STOP_TIMER);
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(this);
        mLocalBroadcastManager.registerReceiver(mLocalReceiver, filterLocal);
        registerSuperPowerObserver(this, mSuperPowerSaveObserver);
        registerTimer0();


        long recordTime = PrefUtils.getLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_PREFERENCE, 0);
        long recordStartTime = PrefUtils.getLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_START_PREFERENCE, 0);
        long recordTotalTime = PrefUtils.getLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_TOTAL_TIME_PREFERENCE, 0);
        int timerId = PrefUtils.getInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_ID_PREFERENCE, 0);
        String timerName = PrefUtils.getString(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_NAME_PREFERENCE, "");
        String timerRing = PrefUtils.getString(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_PREFERENCE, "");
        setTimerId(0, timerId);
        Log.d(TAG, "recordTime:" + recordTime + " recordStartTime = " + recordStartTime + " timerName = " + timerName + " timerRing = " + timerRing);
        if (!TextUtils.isEmpty(timerName)) {
            setTimerName(timerName, 0);
        }
        if (recordTime != 0) {
            long timeroFiltClock = PrefUtils.getLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FILT_CLOCK, 0);
            long recordTimeBycal = TimerPreUtils.getTimer0RemainTime(0);
            Log.d(TAG, "timeroFiltClock:" + timeroFiltClock + " recordTimeBycal = " + recordTimeBycal);
            /* timeroFiltClock 首次开始计时的时间戳
             * recordTimeBycal 暂停时还剩余的时长
             * recordStartTime 每次开始计时的时间戳 */
            if (recordTimeBycal == 0) {
                recordTimeBycal = recordTotalTime - (SystemClock.elapsedRealtime() - timeroFiltClock);
            } else {
                recordTimeBycal = recordTimeBycal - (SystemClock.elapsedRealtime() - recordStartTime);
            }
            Log.d(TAG, "recordTotalTime:" + recordTotalTime + " recordTimeBycal = " + recordTimeBycal);
            int status = getTimerStatus();
            if (!TextUtils.isEmpty(timerRing)) {
                setTimerRingUri(timerRing, 0);
            }
            if (status == STATUS_START) {
                Log.d(TAG, "status == STATUS_START");
                if (recordTimeBycal < 0) {
                    return;
                }
                setTotalTime(recordTotalTime, recordTimeBycal, 0);

                startTimer(0);
            } else if (status == STATUS_PAUSE) {
                Log.d(TAG, "status == STATUS_PAUSE");
                setTotalTime(recordTotalTime, recordTime, 0);
                pauseTimer(0);
            }
        } else {
            Log.i(TAG, " updateFlashbackElapsedRealtime");
            saveTimeStatus(false, false);
            // update StopWatchUI
            updateStopWatchFlashbackElapsedRealtime();
            //update Time
            updateTimeFlashbackElapsedRealtime();
        }
    }

    @Override
    public void success(IAirViewProxy iAirViewProxy, boolean status) {
        mIAirViewProxy = iAirViewProxy;
        mFlashbackStatus = status;
    }

    @Override
    public void failure(IAirViewProxy iAirViewProxy, boolean status) {
        mFlashbackStatus = status;
    }

    public void updateStopWatchFlashbackElapsedRealtime() {
        try {
            JsonObject jsonObject = new JsonObject();
            String stopWatchJson = PrefUtils.getString(this,
                    FlashbackUtils.FLASHBACK_STATE_STORE, StopWatchService.STOPWATCH_FLASHBACK_DATA_JSON, "");
            if ((stopWatchJson == null) || stopWatchJson.equals("")) {
                return;
            }
            JSONObject stopWatchJsonObject = new JSONObject(stopWatchJson);
            long realtime = SystemClock.elapsedRealtime();
            jsonObject.addProperty(StopWatchService.REALTIME, realtime);
            jsonObject.addProperty(StopWatchService.STOPWATCH_STATUS, stopWatchJsonObject.getBoolean(StopWatchService.STOPWATCH_STATUS));
            //Update stopwatch start time and current status
            PrefUtils.getString(this, FlashbackUtils.FLASHBACK_STATE_STORE, StopWatchService.STOPWATCH_FLASHBACK_DATA_JSON, jsonObject.toString());
        } catch (Exception e) {
            Log.e(TAG, "updateStopWatchFlashbackElapsedRealtime error: " + e.getMessage());
        }
    }

    public void updateTimeFlashbackElapsedRealtime() {
        try {
            JsonObject jsonObject = new JsonObject();
            String timeJson = PrefUtils.getString(this, FlashbackUtils.FLASHBACK_STATE_STORE, TIME_FLASHBACK_DATA_JSON, "");
            if ((timeJson == null) || timeJson.equals("")) {
                return;
            }
            JSONObject timeJsonObject = new JSONObject(timeJson);
            long realtime = SystemClock.elapsedRealtime();
            jsonObject.addProperty(REALTIME, realtime);
            jsonObject.addProperty(TIME_STATUS, timeJsonObject.getBoolean(TIME_STATUS));
            //更新启动时间和当前状态
            PrefUtils.putString(this, FlashbackUtils.FLASHBACK_STATE_STORE, TIME_FLASHBACK_DATA_JSON, jsonObject.toString());
        } catch (Exception e) {
            Log.e(TAG, "updateTimeFlashbackElapsedRealtime error: " + e.getMessage());
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        FlashBackHelper.getInstance().releaseManager(Constants.CLOCK_TIMER_FLASHBACK_KEY);
        Log.i(TAG, "onDestroy()");
        mLocalBroadcastManager.unregisterReceiver(mLocalReceiver);
        mLocalReceiver = null;
        unregisterReceiver(mReceiver);
        mReceiver = null;
        unregisterReceiver(mTimerAlertReceiver);
        mTimerAlertReceiver = null;
        unRegisterSuperPowerObserver(this, mSuperPowerSaveObserver);
        if (mBinder != null) {
            mBinder = null;
        }
    }

    public synchronized int registerTimer() {
        if (mTimeObjMap == null) {
            return -1;
        } else {
            int index = -1;
            int size = mTimeObjMap.size();

            if (size == 1) {
                Log.d(TAG, "registerTimer():index = " + 1 + ", size = " + size);
                TimeObj obj = new TimeObj(1);
                mTimeObjMap.put(1, obj);
                return 1;
            }

            // i=1, because 0 is the default.
            for (int i = 1; i < size + 1; i++) {
                Iterator<Integer> iterator = mTimeObjMap.keySet().iterator();
                boolean hasSame = false;
                while (iterator.hasNext()) {
                    if (i == iterator.next()) {
                        // if has same, no need to search next
                        hasSame = true;
                        break;
                    }
                }
                if (!hasSame) {
                    // if no same exist, yes! this is the one and the minimum
                    // one.
                    index = i;
                    break;
                }
            }

            Log.d(TAG, "registerTimer():index = " + index + ", size = " + size);
            Iterator<Integer> iterator = mTimeObjMap.keySet().iterator();
            while (iterator.hasNext()) {
                int i = iterator.next();
                Log.d(TAG, "registerTimer():iterator = " + i);
            }

            TimeObj obj = new TimeObj(index);
            mTimeObjMap.put(index, obj);
            return index;
        }
    }

    public synchronized void registerTimer0() {
        if (mTimeObjMap != null) {
            int size = mTimeObjMap.size();
            if (size == 0) {
                Log.d(TAG, "registerTimer0():index = " + 0 + ", size = " + size);
                TimeObj obj = new TimeObj(0);
                obj.setTimerName("Timer 0");
                mTimeObjMap.put(0, obj);
            }
        }
    }

    public synchronized void removeTimer(int index) {
        if (mTimeObjMap != null) {
            if (index == 0) {
                return;
            }
            Log.d(TAG, "removeTimer():index = " + index);
            mTimeObjMap.remove(index);
        }
    }

    public synchronized Set<Integer> getTimersKey() {
        if (mTimeObjMap != null) {
            return mTimeObjMap.keySet();
        }
        return null;
    }

    public boolean hasTimeObj(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        return obj != null;
    }

    public void setStart(int index, boolean isStart) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.mIsStart = isStart;
        }
    }

    public void setPause(int index, boolean isPause) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.mIsPause = isPause;
        }
    }

    public boolean isStart(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        return (obj != null) && obj.mIsStart;
    }

    public boolean isPause(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        return (obj != null) && obj.mIsPause;
    }

    public int getTimerId(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getTimerId();
        }
        return 0;
    }

    public void setTimerId(int index, int timerId) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setTimerId(timerId);
        }
    }

    public long getTotalTime(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getTotalTime();
        }
        return -1;
    }

    public long getLastTotalTime(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getLastTotalTime();
        }
        return -1;
    }

    public void setTotalTime(long totalTime, long remainTime, int index) {
        Log.d(TAG, "totalTime:" + totalTime + ",remainTime: " + remainTime);
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setTotalTime(totalTime);
            obj.setRemainTime(remainTime);
            obj.setPauseRemainTime(remainTime);
        }
        recordTotalTimer(totalTime, index);
        if (totalTime != 0) {
            recordTimerSetTime(totalTime / THOUSAND);
        }
    }

    public long getRemainTime(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getRemainTime();
        }
        return -1;
    }

    public void startTimerByDragonfly(boolean shouldAddListener) {
        setFlashBackListener(shouldAddListener);
        startTimer(0);
    }

    public void pauseTimerByDragonfly(boolean shouldAddListener) {
        setFlashBackListener(shouldAddListener);
        pauseTimer(0);
    }

    public void startTimer(int index) {
        Log.d(TAG, "startTimer:" + index);
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.startTimer();
        }
    }

    public void pauseTimer(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.pauseTimer();
        }
    }

    public void stopTimer(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            Log.d(TAG, "stopTimer: " + index);
            obj.stopTimer(false);
        }
    }

    public void stopTimerAlert(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.stopTimerAlert();
        }
    }

    public void stopTimerRefreshLayout() {
        if (mLocalBroadcastManager != null) {
            mLocalBroadcastManager.sendBroadcast(new Intent(STOP_TIMER));
        }
    }

    public void setTimerName(String name, int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setTimerName(name);
            obj.setIndex(index);
        }
    }

    public void setCtsTimerName(String name, int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setmCtsName(name);
        }
    }

    public String getCtsTimerName(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getCtsName();
        }
        return null;
    }

    public String getTimerName(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getTimerName();
        }
        return null;
    }

    public void setTimerRingUri(String ringUri, int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setTimerRingUri(ringUri);
        }
    }

    public String getTimerRingUri(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getTimerRingUri();
        }
        return null;
    }

    public void setTimerRingName(String name, int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            obj.setTimerRingName(name);
        }
    }

    public String getTimerRingName(int index) {
        TimeObj obj = mTimeObjMap.get(index);
        if (obj != null) {
            return obj.getTimerRingName();
        }
        return null;
    }

    public void stopAllTimer() {
        Iterator<Integer> iterator = mTimeObjMap.keySet().iterator();
        while (iterator.hasNext()) {
            Log.d(TAG, "stopAllTimer: ");
            int item = iterator.next();
            mTimeObjMap.get(item).stopTimer(false);
            mTimeObjMap.get(item).stopTimerAlert();
            stopTimerRefreshLayout();
        }
    }

    private void restartAllTimer() {
        Iterator<Integer> iterator = mTimeObjMap.keySet().iterator();
        while (iterator.hasNext()) {
            mTimeObjMap.get(iterator.next()).restartTimer();
        }
    }

    private void recordTimer(long time, int index) {
        if (index == 0) {
            PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_PREFERENCE, time);
        }
    }

    private void recordTimerId(int timerId) {
        PrefUtils.putInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_ID_PREFERENCE, timerId);
    }

    private void recordStartTime(long time, int index) {
        if (index == 0) {
            PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_START_PREFERENCE, time);
        }
    }

    private void recordTotalTimer(long time, int index) {
        if (index == 0) {
            PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_TOTAL_TIME_PREFERENCE, time);
        }
    }

    private void resetRecord(int index) {
        if (index == 0) {
            PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DATA_PREFERENCE, 0);
        }
    }

    public void recordTimerSetTime(long time) {
        PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_SET_TIME_PREFERENCE, time);
    }

    private void resetTimerStatus() {
        Log.d(TAG, "resetTimerStatus");
        PrefUtils.putBoolean(this, SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false);
        PrefUtils.putBoolean(this, SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_START_PREFERENCE, false);
    }

    private void recordTimerStatus(boolean isStart, boolean isPause, int index) {
        if (index == 0) {
            Log.d(TAG, "recordTimerStatus: index-" + index + ", isStart-" + isStart + ", isPause-"
                    + isPause);
            if (isStart) {
                PrefUtils.putInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_STATUS_PREFERENCE, STATUS_START);
            } else if (isPause) {
                PrefUtils.putInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_STATUS_PREFERENCE, STATUS_PAUSE);
            } else {
                PrefUtils.putInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_STATUS_PREFERENCE, STATUS_PREPARE);
            }
        }
    }

    private int getTimerStatus() {
        int status = PrefUtils.getInt(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_STATUS_PREFERENCE, 0);
        Log.d(TAG, "getTimerStatus:" + status);
        return status;
    }

    private void recordTimerName(String timerName) {
        if (!TextUtils.isEmpty(timerName)) {
            PrefUtils.putString(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_NAME_PREFERENCE, timerName);
        }
    }

    private void recordTimerRing(String timerRing) {
        if (!TextUtils.isEmpty(timerRing)) {
            PrefUtils.putString(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_PREFERENCE, timerRing);
        }
    }

    public void loadSeedlingCardResult(boolean show) {
        TimeObj obj = mTimeObjMap.get(0);
        if (obj != null) {
            obj.loadSeedlingCardResult(show);
        }
    }

    private static class StopWatchHandler extends StaticHandler<TimeObj> {

        public StopWatchHandler(TimeObj timeObj) {
            super(timeObj);
        }

        public void handleMessage(Message msg, TimeObj t) {
            t.handleMessage(msg);
        }
    }

    public void recordStartClockTime(long time) {
        PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FILT_CLOCK, time);
    }

    public void recordFirstStartClockTime(long time) {
        PrefUtils.putLong(this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FIRST_START_TIME, time);
    }

    public TimeObj getTimeObj(int index) {
        if (mTimeObjMap != null && mTimeObjMap.containsKey(index)) {
            return mTimeObjMap.get(index);
        }
        return null;
    }

    public class TimeObj {
        private int mTimerId = 0;
        public boolean mIsStart = false;
        public boolean mIsPause = false;
        public String mTimerName;
        public String mCtsName;
        public int mIndex;
        private long mCountTime;
        private long mTotalTime;
        /**
         * 上次计时器时间
         */
        private long mLastTotalTime;
        private long mPauseRemainTime;
        private Timer mTimer;
        private PendingIntent mAlertIntent;
        private long mStartTime;
        private long mRemainTime;
        private Handler mStopWatchHandler = new StopWatchHandler(this);
        private long mStartTimerTag = 0;
        private String mTimerRingUri;
        private String mTimerRingName;

        public void handleMessage(Message msg) {
            if (INVALIDATE == msg.what) {
                if (mIsStart) {
                    if (mRemainTime < 0) {
                    } else {
                        long second = mRemainTime / THOUSAND;
                        if (mTempRemainTime != second) {
                            mTempRemainTime = second;
                            recordTimer(mRemainTime, mIndex);
                        }
                        if ((SystemClock.elapsedRealtime() - mStartTimerTag) > ELAPSED_TIME) {
                            showTimerNotification(true);
                            mStartTimerTag = SystemClock.elapsedRealtime();
                        }

                    }
                }
            } else if (msg.what == TIMER_OVER) {
                removeTimer(mIndex);
                Log.d(TAG, "handleMessage: " + TIMER_OVER);
                stopTimer(true);
                mLocalBroadcastManager.sendBroadcast(new Intent(REFRESH_TIMERS));
                checkAndStopSelf();
            } else if (msg.what == ALARM_TIME_COME) {
                Intent intent = (Intent) msg.obj;
                intent.setPackage(getPackageName());
                sendBroadcast(intent);
            }
        }

        TimeObj(int index) {
            this.mIndex = index;
        }

        void pauseTimer() {
            notificationTimingStatus(false);
            mIsStart = false;
            mIsPause = true;
            if (mTimer != null) {
                mTimer.cancel();
            }
            mPauseRemainTime = mRemainTime;
            if ((mAlarmManager != null) && (mAlertIntent != null)) {
                mAlarmManager.cancel(mAlertIntent);
            }
            if (mStopWatchHandler.hasMessages(ALARM_TIME_COME)) {
                mStopWatchHandler.removeMessages(ALARM_TIME_COME);
            }
            Log.d(TAG, "pauseTimer, mPauseRemainTime: " + mPauseRemainTime);
            recordTimerStatus(mIsStart, mIsPause, mIndex);
            saveTimer0RemainTime(TimerService.this, mRemainTime);
            showTimerNotification(false);
            notifyTimerChanged(STATUS_START);
            long recordFirstStartTime = PrefUtils.getLong(TimerService.this,
                    SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FIRST_START_TIME, 0);
            TimerAppSearchManger.addTimer(TimerService.this,
                    TimerAppSearchManger.getTimer(TimerService.this, this,
                            SystemClock.elapsedRealtime(), recordFirstStartTime), true);
        }

        void saveTimer0RemainTime(Context context, long remain) {
            Log.d(TAG, "saveTimer0RemainTime:" + remain + " mIndex:" + mIndex);
            if (mIndex == 0) {
                PrefUtils.putLong(context, SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_REMAIN_TIME, remain);
            }
        }

        void startTimer() {
            notificationTimingStatus(true);
            mIsStart = true;
            mIsPause = false;
            mStartTimerTag = 0;
            mIsInterceptSend = false;
            synchronized (TIMER_LOCK) {
                sTimerRunning = true;
            }
            if (mTimer != null) {
                mTimer.cancel();
                mTimer = null;
            }
            mTimer = new Timer(true);

            mStartTime = SystemClock.elapsedRealtime();
            long recordStartClockTime = PrefUtils.getLong(TimerService.this,
                    SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FILT_CLOCK, 0);
            if (recordStartClockTime == 0) {
                recordStartClockTime(mStartTime);
            }
            mTimer.schedule(new StopWatchHolder(mStopWatchHandler), 0, INTERVAL_PERIOD);

            int period = getRefreshTime(getTotalTime());
            mTimer.schedule(new StopWatchHolder(mStopWatchHandler), 0, period);
            startTimerAlert();
            startService();
            recordStartTime(mStartTime, mIndex);
            recordTimerStatus(mIsStart, mIsPause, mIndex);
            notifyTimerChanged(STATUS_PREPARE);
            recordTimerName(mTimerName);
            recordTimerRing(mTimerRingUri);
            long recordFisrstStartTime = PrefUtils.getLong(TimerService.this,
                    SHARED_PREFS_ALARM_CLOCK_APP, TIMER0_FIRST_START_TIME, 0);
            if (recordFisrstStartTime == 0) {
                recordFisrstStartTime = System.currentTimeMillis();
                recordFirstStartClockTime(recordFisrstStartTime);
            }
            TimerAppSearchManger.addTimer(TimerService.this,
                    TimerAppSearchManger.getTimer(TimerService.this, this,
                            mStartTime, recordFisrstStartTime), true);
        }


        private void timeOver() {
            mIsStart = false;
            mIsPause = false;
            if (mTimer != null) {
                mTimer.cancel();
            }
            mStopWatchHandler.sendEmptyMessage(TIMER_OVER);
            if (DEBUG) {
                Log.d(TAG, "timeover()");
            }
        }

        public long getRemainTime() {
            return mRemainTime;
        }

        void setRemainTime(long time) {
            mRemainTime = time;
        }

        void setPauseRemainTime(long pauseRemainTime) {
            mPauseRemainTime = pauseRemainTime;
        }

        public long getTotalTime() {
            return mTotalTime;
        }

        public long getLastTotalTime() {
            return mLastTotalTime;
        }

        void setTotalTime(long time) {
            mTotalTime = time;
            mLastTotalTime = time;
            mCountTime = time;
            mPauseRemainTime = time;
        }

        public void setIndex(int index) {
            this.mIndex = index;
        }

        public int getTimerId() {
            return mTimerId;
        }

        public void setTimerId(int timerId) {
            this.mTimerId = timerId;
            recordTimerId(timerId);
        }

        public String getTimerName() {
            return mTimerName;
        }

        void setTimerName(String name) {
            mTimerName = name;
        }

        public String getCtsName() {
            return mCtsName;
        }

        public void setmCtsName(String ctsName) {
            Log.e(TAG, "setmCtsName " + ctsName);
            this.mCtsName = ctsName;
        }

        @SuppressLint({"InlinedApi", "WrongConstant"})
        void startTimerAlert() {
            Log.d(TAG, "startTimerAlert mCtsName:" + mCtsName);
            if (!TextUtils.isEmpty(mCtsName)) {
                mTimerName = mCtsName;
            }
            stopTimerAlert();
            long time = SystemClock.elapsedRealtime();
            long alertTime = time + mPauseRemainTime;
            String timerDes = "";
            if (mSelectedTimer != null && isSelectedTimer() && mSelectedTimer.getFlag() == 0 && mTimerId != AiSupportContentProvider.AI_TIMER_ID) {
                timerDes = mSelectedTimer.getDescription();
                PrefUtils.putString(TimerService.this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_DESCRIPTION_PREFERENCE, mSelectedTimer.getDescription());
            }
            Intent intent = createTimerIntent(TimerService.this, mTimerName, mTimerRingUri, mTimerRingName, timerDes, String.valueOf(mIndex));
            mAlertIntent = PendingIntent.getBroadcast(TimerService.this, mIndex, intent,
                    Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            PrefUtils.putBoolean(TimerService.this, SHARED_PREFS_ALARM_CLOCK_APP, ClockConstant.KEY_TIMER_DATA_CLEAR, false);
            PrefUtils.putString(TimerService.this, SHARED_PREFS_ALARM_CLOCK_APP, TIMER_TIME_RING_NAME_PREFERENCE, mTimerRingName);

            if (mCountTime > 0) {
                if (mCountTime <= ALARM_TIME) {
                    TimerWakeLock.acquireCpuLockTimer(TimerService.this, mCountTime);
                    mStopWatchHandler.sendMessageDelayed(mStopWatchHandler.obtainMessage(ALARM_TIME_COME, intent), mCountTime);
                } else {
                    if (mAlarmManager != null) {
                        mAlarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, alertTime,
                                mAlertIntent);
                    }
                }
                showTimerNotification(true);
            }
            Log.d(TAG, "startTimerAlert" + mCountTime + " mTimerName:" + mTimerName + ", index:" + mIndex);
        }

        private void loadSeedlingCardResult(boolean isSuccess) {
            if (isSuccess) {
                TimerNotificationManager.cancelTimerNotification(TimerService.this);
            } else {
                showNativeNotification(mIsStart);
            }
        }

        void stopTimer(boolean isAutoStop) {
            notificationTimingStatus(false);
            Log.d(TAG, "stopTimer");
            TimerService.this.setTimerId(0, 0);
            resetTimerStatus();
            if (mTimer != null) {
                mTimer.cancel();
            }
            mIsSelectedTimer = false;
            mRemainTime = 0;
            mTotalTime = 0;
            mIsStart = false;
            mIsPause = false;
            synchronized (TIMER_LOCK) {
                sTimerRunning = false;
            }
            setCtsTimerName(null, mIndex);
            resetRecord(mIndex);
            recordStartClockTime(0);
            saveTimer0RemainTime(TimerService.this, 0);
            recordTimerStatus(mIsStart, mIsPause, mIndex);
            TimerNotificationManager.cancelTimerNotification(TimerService.this);
            if (isAutoStop) {
                TimerSeedlingHelper.closeSeedlingCardByVersionControl(TimerService.this);
                notifyTimerChanged(STATUS_OVER);
            } else {
                TimerSeedlingHelper.closeSeedlingCard(TimerService.this);
                notifyTimerChanged(STATUS_PAUSE);
            }
            FlashBackHelper.getInstance().releaseManager(Constants.CLOCK_TIMER_FLASHBACK_KEY);
            stopService();
            recordFirstStartClockTime(0);
            TimerAppSearchManger.deleteTimer(TimerService.this, DEFAULT_TIMER_INDEX, true);
        }

        void restartTimer() {
            if (!mIsStart) {
                return;
            }
            mIsInterceptSend = false;
            if (mTimer != null) {
                mTimer.cancel();
                mTimer = null;
                mTimer = new Timer(true);
                mTimer.schedule(new StopWatchHolder(mStopWatchHandler), 0, INTERVAL_PERIOD);
            }
            showTimerNotification(true);
            saveTimer0RemainTime(TimerService.this, 0);
        }

        void stopTimerAlert() {
            Log.d(TAG, "stopTimerAlert:" + mTotalTime + " " + mCountTime);
            if ((mAlarmManager != null) && (mAlertIntent != null)) {
                mAlarmManager.cancel(mAlertIntent);
            }
            if (mStopWatchHandler.hasMessages(ALARM_TIME_COME)) {
                mStopWatchHandler.removeMessages(ALARM_TIME_COME);
            }
        }

        void showTimerNotification(Boolean isStart) {
            if (mSelectedTimer != null && mSelectedTimer.getFlag() == 0 && mTimerId != AiSupportContentProvider.AI_TIMER_ID) {
                //默认计时器翻译计时器名称
                try {
                    String[] defaultTimerArray = getResources().getStringArray(R.array.default_timer_description);
                    int index = Integer.parseInt(mSelectedTimer.getDescription());
                    mTimerName = String.valueOf(defaultTimerArray[index]);
                } catch (Exception e) {
                    Log.e(TAG, "showTimerNotification e:" + e.getMessage());
                }
            }
            String timerName = mTimerName;
            if (!TextUtils.isEmpty(mCtsName)) {
                timerName = mCtsName;
            }
            boolean isOSVersion1501 = VersionUtils.isOSVersion1501();
            showTimerCardSecure(TimerService.this, mIndex, timerName,
                    Formatter.getTimerEntity(mRemainTime, mTotalTime, getApplicationContext()),
                    isStart, isOSVersion1501 ? null : this::loadSeedlingCardResult);
            if (TimerSeedlingHelper.isSupportCapsuleFeature() || isOSVersion1501) {
                showNativeNotification(isStart);
                if (!TimerUtils.isForegroundServiceStarted()) {
                    mHasStartService = false;
                    startService();
                }
            }
        }

        /**
         * 再次封装，用于6974756屏蔽原生通知需求
         *
         * @param isStart
         */
        private void showNativeNotification(Boolean isStart) {
            if (mIsInterceptSend) {
                return;
            }
            TimerNotificationManager.showTimerNotification(TimerService.this, mCountTime / THOUSAND, isStart, mIndex, mRemainTime);
        }

        public String getTimerRingUri() {
            return mTimerRingUri;
        }

        public void setTimerRingUri(String mTimerRingUri) {
            this.mTimerRingUri = mTimerRingUri;
        }

        public String getTimerRingName() {
            return mTimerRingName;
        }

        public void setTimerRingName(String mTimerRingName) {
            this.mTimerRingName = mTimerRingName;
        }

        class StopWatchHolder extends TimerTask {
            protected Handler mHandler;
            boolean mCanceled;

            StopWatchHolder(Handler handler) {
                mHandler = handler;
                mCanceled = false;
            }

            public boolean cancel() {
                mCanceled = true;
                return super.cancel();
            }

            public void run() {
                if ((mCanceled && (mCountTime == 0)) || (mRemainTime <= 0)) {
                    mCountTime = 0;
                    timeOver();
                    return;
                }

                mRemainTime = mPauseRemainTime - (SystemClock.elapsedRealtime() - mStartTime);
                mCountTime = mRemainTime;
                FlashBackHelper.getInstance().sendData(Constants.CLOCK_TIMER_FLASHBACK_KEY, Formatter.getTimerStr(mRemainTime, getApplicationContext()));
                mHandler.sendEmptyMessage(INVALIDATE);
            }
        }
    }

    public class TimerBinder extends Binder {
        public TimerService getService() {
            return TimerService.this;
        }
    }

    class TimerBroadCastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (Intent.ACTION_TIME_CHANGED.equals(intent.getAction())) {
                restartAllTimer();
            }
        }
    }

    public void notificationTimingStatus(boolean status) {
        saveTimeStatus(status, false);
        if (!status) {
            cancel(status);
        }
    }

    /*
     * Save the state of the stopwatch
     * */
    public void saveTimeStatus(boolean status, boolean ifUpdataRealtime) {
        if (!DeviceUtils.isExpVersion(this)) {
            try {
                long realtime = SystemClock.elapsedRealtime();
                if (ifUpdataRealtime) {
                    save(realtime, status);
                } else {
                    String timeJson = PrefUtils.getString(this, FlashbackUtils.FLASHBACK_STATE_STORE, TIME_FLASHBACK_DATA_JSON, "");
                    JSONObject timeJsonObject = new JSONObject(timeJson);
                    save(timeJsonObject.getLong(REALTIME), status);
                }
            } catch (Exception e) {
                Log.e(TAG, "saveTimeStatus error: " + e.getMessage());
            }
        }
    }

    private void save(long realtime, boolean status) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty(REALTIME, realtime);
        jsonObject.addProperty(TIME_STATUS, status);
        PrefUtils.putString(this, FlashbackUtils.FLASHBACK_STATE_STORE, TIME_FLASHBACK_DATA_JSON, jsonObject.toString());
    }

    public void cancel(boolean status) {
        try {
            //Close flashback icon
            if (!status && (mFlashbackUtils != null) && (mIAirViewProxy != null)) {
                String stopWatchJson = PrefUtils.getString(this,
                        FlashbackUtils.FLASHBACK_STATE_STORE, StopWatchService.STOPWATCH_FLASHBACK_DATA_JSON, "");
                if ((stopWatchJson == null) || stopWatchJson.equals("")) {
                    mFlashbackUtils.cancel(getApplicationContext(), mIAirViewProxy);
                    return;
                }
                //Determine if the stopwatch is running
                JSONObject stopWatchJsonObject = new JSONObject(stopWatchJson);
                boolean stopWatchStatus = stopWatchJsonObject.getBoolean(StopWatchService.STOPWATCH_STATUS);
                if (!stopWatchStatus) {
                    mFlashbackUtils.cancel(getApplicationContext(), mIAirViewProxy);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "cancel" + e.getMessage());
        }
    }

    private void checkAndStopSelf() {
        if (!AlarmClock.sMainActivityRunning) {
            stopSelf();
        }
    }

    public int getRefreshTime(long time) {
        if (time <= SHORT_TIME) {
            return SHORT_REFRESH_TIME;
        } else if (time <= MIDDLE_TIME) {
            return MIDDLE_REFRESH_TIME;
        } else {
            return DEFAULT_REFRESH_TIME;
        }
    }

    public boolean isSelectedTimer() {
        return mIsSelectedTimer;
    }

    public void isSelectedTimer(boolean mIsSelectedTimer) {
        this.mIsSelectedTimer = mIsSelectedTimer;
    }

    public void setSelectedTimer(OplusTimer timer) {
        mSelectedTimer = timer;
    }

    public OplusTimer getSelectedTimer() {
        return mSelectedTimer;
    }

    private void setFlashBackListener(boolean shouldAddListener) {
        FlashBackHelper instance = FlashBackHelper.getInstance();
        boolean haveListener = instance.haveListener(Constants.CLOCK_TIMER_FLASHBACK_KEY);
        Log.d(TAG, "shouldAddListener: " + shouldAddListener + ",haveListener:" + haveListener);
        if (shouldAddListener && !haveListener) {
            instance.setListener(Constants.CLOCK_TIMER_FLASHBACK_KEY, new FlashBackHelper.OnClickListener() {
                @Override
                public void onLeftButtonClick() {
                    boolean start = isStart(0);
                    boolean pause = isPause(0);
                    Log.d(TAG, "onLeftButtonClick: start:" + start + ",pause:" + pause);
                    if (start) {
                        pauseTimer(0);
                        instance.setTimerPause(Constants.CLOCK_TIMER_FLASHBACK_KEY);
                    } else if (pause) {
                        startTimer(0);
                        instance.setTimerPlay(Constants.CLOCK_TIMER_FLASHBACK_KEY);
                    }
                }

                @Override
                public void onRightButtonClick() {
                    Log.d(TAG, "onRightButtonClick: ");
                    stopTimer(0);
                    stopTimerAlert(0);
                    instance.setTimerReset(Constants.CLOCK_TIMER_FLASHBACK_KEY);
                    instance.releaseManager(Constants.CLOCK_TIMER_FLASHBACK_KEY);
                }
            });
        }
    }

    private void notifyTimerChanged(int state) {
        Log.d(TAG, "notify timer state changed:" + getTimerId(0) + ";" + state);
        getContentResolver().notifyChange(getNotifyUri(AiSupportContentProvider.AUTHORITY, state), null, ContentResolver.NOTIFY_UPDATE);
        getContentResolver().notifyChange(getNotifyUri(AiSupportContentProviderPure.AUTHORITY_PURE, state), null, ContentResolver.NOTIFY_UPDATE);
    }

    private Uri getNotifyUri(String authority, int state) {
        String uriPath = new StringBuilder(AiSupportContentProvider.CONTENT)
                .append(authority)
                .append(AiSupportContentProvider.TIMER_EXTENDS)
                .append("/")
                .append(getTimerId(0))
                .append("/")
                .append(state)
                .toString();
        Log.d(TAG, "notify timer state changed:" + uriPath);
        return Uri.parse(uriPath);
    }

    public void startService() {
        if (VersionUtils.isOSVersion1501() && !mHasStartService) {
            Intent intent = new Intent(this, TimerForegroundService.class);
            if (Utils.isAboveS()) {
                Log.d(TAG, "start TimerForegroundService");
                this.startForegroundService(intent);
            } else {
                this.startService(intent);
            }
            mHasStartService = true;
            TimerUtils.setForegroundServiceStarted(true);
        }
    }

    public void stopService() {
        if (VersionUtils.isOSVersion1501() && mHasStartService) {
            Log.d(TAG, "stop TimerForegroundService");
            LiteEventBus.Companion.getInstance().send(NotificationUtils.STOP_TIMER_FOREGROUND_SERVICE, true);
            mHasStartService = false;
        }
    }
}
