/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerAnimationCountDown.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/20
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2024/08/20     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.os.CountDownTimer

/**
 * 计时器动画执行倒计时
 */
abstract class TimerAnimationCountDown() {
    companion object {
        const val TAG = "TimerAnimationCountDown"
        const val SECOND = 1000L
        const val MINUTE = 60L
    }

    /**
     * CountDownTimer
     */
    var mTimerAnimationTimer: CountDownTimer? = null

    /**
     * 开启倒计时任务
     * @return 是否开启倒计时成功
     */
    fun startTimer(): Boolean {
        kotlin.runCatching {
            mTimerAnimationTimer = object : CountDownTimer(MINUTE * SECOND, SECOND) {
                override fun onTick(millisUntilFinished: Long) {
                    onTimerTick()
                }

                override fun onFinish() {
                    onTimerFinish()
                }
            }.start()
            return true
        }
        return false
    }

    fun cancel() {
        mTimerAnimationTimer?.cancel()
        mTimerAnimationTimer = null
    }

    /**
     * 倒计时任务更新
     */
    abstract fun onTimerTick()

    /**
     * 倒计时任务结束
     */
    abstract fun onTimerFinish()
}