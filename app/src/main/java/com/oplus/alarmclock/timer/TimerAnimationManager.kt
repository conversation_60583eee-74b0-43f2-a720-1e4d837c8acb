/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerAnimationManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/6
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin   2023/3/6     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.animation.PathInterpolator
import androidx.annotation.VisibleForTesting
import androidx.core.animation.addListener
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.marginStart
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.animation.COUIOutEaseInterpolator
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DisplayUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import java.lang.ref.WeakReference
import kotlin.math.abs

object TimerAnimationManager {

    private const val TAG = "TimerAnimationManager"

    private const val ZERO_F = 0F
    private const val ONE_F = 1.0F
    private const val HALF_ALPHA = 0.5F
    private const val CONTROL_0_3 = 0.3f
    private const val CONTROL_0_15 = 0.15f
    private const val CONTROL_1 = 1f
    private const val MOVE_OUT_RATIO = 5.0f
    private const val MAX_COLOR = 255
    private const val DURATION_RECYCLER = 200L
    private const val DURATION_DIAL = 400L
    private const val DURATION_ALPHA = 200L
    private const val DURATION_TRANSLATION_X = 500L
    private const val DURATION_TRANSITION = 350L
    private const val DURATION_FADE_SHORT = 180L
    private const val DURATION_FADE_SHORT_NEW = 200L
    private const val DURATION_FADE_LONG = 300L
    private const val DURATION_FADE_LONG_NEW = 500L
    private const val ANIMATOR_ALPHA = "alpha"
    private var mStopAnimatorSet: AnimatorSet? = null
    private var mDisplayAnimatorSet: AnimatorSet? = null

    @VisibleForTesting
    var miniAppTransitionAnimator: AnimatorSet? = null
    var mButtonStartTransitionX = 0F

    /**
     * 底部按钮显示平移动画
     * @param button 按钮
     * @param isLeft 是否是左边按钮
     */
    fun displayButtonAnimation(buttonCancel: View, buttonStart: View, translationSpace: Float, isAutoRefresh: Boolean) {
        val buttonSide = WeakReference(buttonCancel).get()
        val buttonMid = WeakReference(buttonStart).get()
        if (mDisplayAnimatorSet?.isRunning == true) {
            return
        }
        buttonSide?.run {
            val end = if (Utils.isRtl()) {
                translationSpace + width / 2
            } else {
                -(translationSpace + width / 2)
            }
            if (end == translationX) {
                resetButton(this, true)
                return
            }
            if (isAutoRefresh) {
                alpha = ONE_F
                translationX = end
                buttonMid?.apply {
                    mButtonStartTransitionX = -end
                    translationX = -end
                }
                return
            }
            isClickable = false
            translationX = ZERO_F
            alpha = ZERO_F
            val alpha = ObjectAnimator.ofFloat(this, ANIMATOR_ALPHA, ZERO_F, ONE_F).apply {
                duration = DURATION_ALPHA
                interpolator = COUIEaseInterpolator()
            }
            val translateX = ValueAnimator.ofFloat(ZERO_F, end).apply {
                duration = DURATION_TRANSLATION_X
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener {
                    translationX = it.animatedValue as Float
                    buttonMid?.run {
                        buttonStartAnim(end, this, true, it)
                    }
                }
            }
            if (mDisplayAnimatorSet == null) {
                mDisplayAnimatorSet = AnimatorSet()
            }
            mDisplayAnimatorSet?.apply {
                playTogether(alpha, translateX)
                start()
                doOnCancel {
                    resetButton(buttonSide, true)
                }
                doOnEnd {
                    isClickable = true
                    removeAllListeners()
                    cancel()
                    mDisplayAnimatorSet = null
                }
            }
        }
    }

    /**
     * 重置按钮位置，无动画
     */
    fun hideButtonNoAnimation(buttonCancel: View, buttonStart: View, translationSpace: Float) {
        buttonCancel.run {
            val start = if (Utils.isRtl()) {
                translationSpace + width / 2
            } else {
                -(translationSpace + width / 2)
            }
            resetButton(buttonCancel, false)
            translationX = start
            buttonStart.apply {
                mButtonStartTransitionX = 0f
                translationX = 0f
            }
        }
    }

    /**
     * 底部按钮隐藏动画
     * @param button 按钮
     * @param isLeft 是否是左边按钮
     */
    fun hideButtonAnimation(buttonCancel: View, buttonStart: View, translationSpace: Float) {
        val buttonSide = WeakReference(buttonCancel).get()
        val buttonMid = WeakReference(buttonStart).get()
        if (mStopAnimatorSet?.isRunning == true) {
            return
        }
        buttonSide?.run {
            val start = if (Utils.isRtl()) {
                translationSpace + width / 2
            } else {
                -(translationSpace + width / 2)
            }
            if (start == ZERO_F) {
                resetButton(buttonSide, false)
                return
            }
            val alpha = ObjectAnimator.ofFloat(this, ANIMATOR_ALPHA, ONE_F, ZERO_F).apply {
                duration = DURATION_ALPHA
                interpolator = COUIEaseInterpolator()
                doOnCancel {
                    resetButton(buttonSide, false)
                }
                doOnEnd {
                    visibility = View.INVISIBLE
                }
            }
            val translateX = ValueAnimator.ofFloat(start, ZERO_F).apply {
                duration = DURATION_TRANSLATION_X
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener {
                    translationX = it.animatedValue as Float
                    buttonMid?.run {
                        buttonStartAnim(start, this, false, it)
                    }
                }
                doOnCancel {
                    resetButton(buttonSide, false)
                }
                doOnEnd {
                    buttonSide.alpha = ONE_F
                }
            }
            if (mStopAnimatorSet == null) {
                mStopAnimatorSet = AnimatorSet()
            }
            mStopAnimatorSet?.apply {
                playTogether(alpha, translateX)
                start()
                doOnEnd {
                    removeAllListeners()
                    cancel()
                    mStopAnimatorSet = null
                }
            }
        }
    }

    private fun buttonStartAnim(
        start: Float,
        buttonStart: View,
        isDisplay: Boolean,
        anim: ValueAnimator
    ) {
        val total = abs(start)
        var tx = total * anim.animatedFraction
        if (!isDisplay) {
            tx = total - tx
        }
        if (Utils.isRtl()) {
            tx = -tx
        }
        mButtonStartTransitionX = tx
        buttonStart.translationX = tx
    }

    /**
     * 重置按钮状态
     */
    private fun resetButton(button: View, isDisplay: Boolean) {
        button.visibility = if (isDisplay) View.VISIBLE else View.INVISIBLE
        button.alpha = ONE_F
    }

    /**
     * miniApp入场动画列表->自定义计时器&列表到计时页
     */
    fun miniAppEnterAnimation(
        sourceView: View,
        targetView: View
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        val start = getStart(sourceView)
        val moveIn = getEnterMoveInAnimator(targetView, start)
        val moveOut = getEnterMoveOutAnimator(sourceView, start)
        val alpha = getEnterAlphaAnimator(sourceView)
        startPlayMiniAppEnterAnimation(null, moveOut, moveIn, alpha, targetView, sourceView)
    }

    /**
     * miniApp入场动画列表->自定义计时器&列表到计时页-带计时按钮的平移动画
     */
    fun miniAppEnterTranslationAni(
        sourceView: View,
        targetView: View,
        mResetBtn: View,
        mMiniStart: View
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        val start = getStart(sourceView)
        val moveIn = getEnterMoveInAnimator(targetView, start)
        val moveOut = getEnterMoveOutAnimator(sourceView, start)
        val alpha = getEnterAlphaAnimator(sourceView)

        /**按钮平移进入动画*/
        val resetFadeIn: ValueAnimator? = getMiniResetEnterAnimator(mResetBtn, mMiniStart)
        startPlayMiniAppEnterAnimation(resetFadeIn, moveOut, moveIn, alpha, targetView, sourceView)
    }

    private fun startPlayMiniAppEnterAnimation(
        resetFadeIn: ValueAnimator?,
        moveOut: ValueAnimator,
        moveIn: ValueAnimator?,
        alpha: ValueAnimator?,
        targetView: View,
        sourceView: View
    ) {
        miniAppTransitionAnimator = AnimatorSet().apply {
            if (resetFadeIn != null) {
                play(moveOut).with(moveIn).with(alpha).with(resetFadeIn)
            } else {
                play(moveOut).with(moveIn).with(alpha)
            }
            duration = DURATION_TRANSITION
            doOnStart { targetView.visibility = View.VISIBLE }
            doOnEnd {
                sourceView.translationX = 0f
                sourceView.visibility = View.INVISIBLE
                resetMiniAnim()
            }
            doOnCancel {
                sourceView.translationX = 0f
                sourceView.visibility = View.INVISIBLE
                miniAppTransitionAnimator = null
            }
            interpolator = PathInterpolator(CONTROL_0_3, CONTROL_0_15, CONTROL_0_3, CONTROL_1)
        }.also { it.start() }
    }


    private fun getEnterAlphaAnimator(sourceView: View): ValueAnimator? {
        val alpha = ValueAnimator.ofFloat(0f, HALF_ALPHA).apply {
            addUpdateListener {
                val drawable = ColorDrawable(Color.BLACK)
                drawable.alpha = ((it.animatedValue as Float) * MAX_COLOR).toInt()
                sourceView.foreground = drawable
            }
            doOnEnd { sourceView.foreground = ColorDrawable(Color.TRANSPARENT) }
            doOnCancel { sourceView.foreground = ColorDrawable(Color.TRANSPARENT) }
        }
        return alpha
    }

    private fun getEnterMoveOutAnimator(sourceView: View, start: Int): ValueAnimator {
        val moveOut = ObjectAnimator.ofFloat(
            sourceView,
            "translationX",
            0f,
            -start.toFloat() / MOVE_OUT_RATIO
        )
        return moveOut
    }

    private fun getEnterMoveInAnimator(targetView: View, start: Int): ValueAnimator? {
        val moveIn = ObjectAnimator.ofFloat(
            targetView,
            "translationX",
            start.toFloat(),
            0f
        )
        return moveIn
    }

    private fun getStart(sourceView: View): Int {
        resetMiniAnim()
        val start = if (Utils.isRtl()) {
            -sourceView.width
        } else {
            sourceView.width
        }
        return start
    }

    private fun getMiniResetEnterAnimator(mResetBtn: View, mMiniStart: View): ValueAnimator? {
        val resetFadeIn = mResetBtn.let {
            var start = it.marginStart.toFloat()
            if (start == 0F) {
                it.alpha = ONE_F
                return null
            }
            it.isClickable = false
            start = abs(
                DisplayUtils.getRealWindowSize(it.context).width.toFloat() / 2 - abs(start) - it.resources.getDimensionPixelSize(
                        R.dimen.layout_dp_56
                ).toFloat() / 2
            )
            if (Utils.isRtl()) {
                start = -start
            }
            it.translationX = start
            it.alpha = 0f
            val alphaTimeRate = DURATION_FADE_SHORT_NEW.toFloat() / DURATION_FADE_LONG_NEW.toFloat()
            val alphaRate = DURATION_FADE_LONG_NEW.toFloat() / DURATION_FADE_SHORT_NEW.toFloat()
            ValueAnimator.ofFloat(start, 0f).apply {
                duration = DURATION_FADE_LONG_NEW
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animValue ->
                    val value = animValue.animatedValue as Float
                    it.translationX = value
                    miniStartAnimation(mMiniStart, start, this, true)
                    /**应设计要求，透明度需要200ms就刷新到1F，此处根据动画的执行百分比来进行计算*/
                    if (animatedFraction <= alphaTimeRate) {
                        it.alpha = animatedFraction * alphaRate
                    } else if (it.alpha == ONE_F) {
                        /**doNothing 避免重复刷新*/
                    } else {
                        it.alpha = ONE_F
                    }
                }
                addListener(onEnd = { _ ->
                    it.isClickable = true
                    it.translationX = 0f
                }, onCancel = { _ ->
                    it.isClickable = true
                    it.translationX = 0f
                })
            }
        }
        return resetFadeIn
    }

    /**
     * miniApp出场动画自定义计时器&列表到计时页 -> 列表
     */
    fun miniAppExitAnimation(
        sourceView: View,
        targetView: View
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        val start = getStart(sourceView)
        val moveIn = getExitMoveInAnimator(targetView, start)
        val moveOut = getExitMoveOutAnimator(sourceView, start)
        val alpha = getExitAlphaAnimator(targetView, sourceView)
        startPlayMiniAppExitAnimation(null, moveOut, moveIn, alpha, targetView, sourceView)
    }

    /**
     * miniApp出场动画自定义计时器&列表到计时页 -> 列表-带计时按钮平移动画
     */
    fun miniAppExitTranslationAni(
        sourceView: View,
        targetView: View,
        mResetBtn: View,
        mMiniStart: View
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        val start = getStart(sourceView)
        val moveIn = getExitMoveInAnimator(targetView, start)
        val moveOut = getExitMoveOutAnimator(sourceView, start)
        val alpha = getExitAlphaAnimator(targetView, sourceView)

        /**按钮平移退出动画*/
        val resetFadeOut = getMiniResetExitAnimator(mResetBtn, mMiniStart)
        startPlayMiniAppExitAnimation(resetFadeOut, moveOut, moveIn, alpha, targetView, sourceView)
    }

    private fun startPlayMiniAppExitAnimation(
        resetFadeOut: ValueAnimator?,
        moveOut: ValueAnimator,
        moveIn: ValueAnimator,
        alpha: ValueAnimator?,
        targetView: View,
        sourceView: View
    ) {
        miniAppTransitionAnimator = AnimatorSet().apply {
            if (resetFadeOut != null) {
                play(moveOut).with(moveIn).with(alpha).with(resetFadeOut)
            } else {
                play(moveOut).with(moveIn).with(alpha)
            }
            duration = DURATION_TRANSITION
            interpolator = PathInterpolator(CONTROL_0_3, CONTROL_0_15, CONTROL_0_3, CONTROL_1)
            doOnStart { targetView.visibility = View.VISIBLE }
            doOnEnd {
                sourceView.translationX = 0f
                sourceView.visibility = View.INVISIBLE
                resetMiniAnim()
            }
            doOnCancel {
                sourceView.translationX = 0f
                sourceView.visibility = View.INVISIBLE
                miniAppTransitionAnimator = null
            }
        }.also { it.start() }
    }

    private fun getExitAlphaAnimator(targetView: View, sourceView: View): ValueAnimator? {
        val alpha = ValueAnimator.ofFloat(HALF_ALPHA, 0f).apply {
            addUpdateListener {
                val drawable = ColorDrawable(Color.BLACK)
                drawable.alpha = ((it.animatedValue as Float) * MAX_COLOR).toInt()
                targetView.foreground = drawable
            }
            doOnEnd { sourceView.foreground = ColorDrawable(Color.TRANSPARENT) }
            doOnCancel { sourceView.foreground = ColorDrawable(Color.TRANSPARENT) }
        }
        return alpha
    }

    private fun getExitMoveOutAnimator(sourceView: View, start: Int): ValueAnimator {
        val moveOut = ObjectAnimator.ofFloat(
            sourceView,
            "translationX",
            0f,
            start.toFloat()
        )
        return moveOut
    }

    private fun getExitMoveInAnimator(targetView: View, start: Int): ValueAnimator {
        val moveIn = ObjectAnimator.ofFloat(
            targetView,
            "translationX",
            -(start.toFloat() / MOVE_OUT_RATIO),
            0f
        )
        return moveIn
    }

    private fun getMiniResetExitAnimator(mResetBtn: View, mMiniStart: View): ValueAnimator? {
        val resetFadeOut = mResetBtn.let {
            var start = it.marginStart.toFloat()
            if (start == 0F) {
                resetButton(it, false)
                return null
            }
            it.isClickable = false
            start =
                DisplayUtils.getRealWindowSize(it.context).width.toFloat() / 2 - abs(start) - it.resources.getDimensionPixelSize(
                        R.dimen.layout_dp_56
                ).toFloat() / 2
            if (Utils.isRtl()) {
                start = -start
            }
            val alphaTimeRate = DURATION_FADE_SHORT_NEW.toFloat() / DURATION_FADE_LONG_NEW.toFloat()
            val alphaRate = DURATION_FADE_LONG_NEW.toFloat() / DURATION_FADE_SHORT_NEW.toFloat()
            ValueAnimator.ofFloat(0f, start).apply {
                duration = DURATION_FADE_LONG_NEW
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animValue ->
                    val value = animValue.animatedValue as Float
                    it.translationX = value
                    /**应设计要求，透明度需要200ms就刷新到0F，此处根据动画的执行百分比来进行计算*/
                    if (animatedFraction <= alphaTimeRate) {
                        it.alpha = ONE_F - animatedFraction * alphaRate
                    } else if (it.alpha == 0F) {
                        /**doNothing 避免重复刷新*/
                    } else {
                        it.alpha = 0F
                    }
                    miniStartAnimation(mMiniStart, start, this, false)
                }
                addListener(onEnd = { _ ->
                    it.translationX = 0f
                    it.isClickable = true
                }, onCancel = { _ ->
                    it.translationX = 0f
                    it.isClickable = true
                })
            }
        }
        return resetFadeOut
    }

    /**
     * miniApp出场动画自定义计时器-> 计时页
     */
    @Suppress("LongMethod", "LongParameterList")
    fun miniAppEnterAnimationWithFade(
        outParent: View,
        titleView: View,
        numberPicker: View,
        inParent: View,
        timeView: View,
        nameView: View,
        resetView: View,
        mMiniStart: View,
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        resetMiniAnim()
        val titleViewAlphaOut = titleView.createAlphaAnim(DURATION_FADE_SHORT, false)
        val numberPickerAlphaOut = numberPicker.createAlphaAnim(DURATION_FADE_SHORT, false)
        val translationY =
            numberPicker.resources.getDimensionPixelSize(R.dimen.timer_animator_translation)
        val moveOut = numberPicker.createTranslationYAnim(
            DURATION_FADE_SHORT,
            0f,
            -translationY.toFloat(),
            false
        )

        val timerFadeIn = timeView.createFadeAnim(
            DURATION_FADE_SHORT,
            DURATION_FADE_LONG,
            translationY.toFloat(),
            0f,
            true
        ).apply {
            doOnStart {
                outParent.visibility = View.INVISIBLE
                inParent.visibility = View.VISIBLE
            }
        }
        val nameFadeIn = nameView.createFadeAnim(
            DURATION_FADE_SHORT,
            DURATION_FADE_LONG,
            translationY.toFloat(),
            0f,
            true
        )

        val resetFadeIn = resetView.let {
            var start = it.marginStart.toFloat()
            if (start == 0F) {
                it.alpha = ONE_F
                return
            }
            it.isClickable = false
            start = abs(
                DisplayUtils.getRealWindowSize(it.context).width.toFloat() / 2 - abs(start) - it.resources.getDimensionPixelSize(
                        R.dimen.layout_dp_56
                ).toFloat() / 2
            )
            if (Utils.isRtl()) {
                start = -start
            }
            it.translationX = start
            it.alpha = 0f
            val alphaTimeRate = DURATION_FADE_SHORT_NEW.toFloat() / DURATION_FADE_LONG_NEW.toFloat()
            val alphaRate = DURATION_FADE_LONG_NEW.toFloat() / DURATION_FADE_SHORT_NEW.toFloat()
            ValueAnimator.ofFloat(start, 0f).apply {
                duration = DURATION_FADE_LONG_NEW
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animValue ->
                    val value = animValue.animatedValue as Float
                    it.translationX = value
                    miniStartAnimation(mMiniStart, start, this, true)
                    /**应设计要求，透明度需要200ms就刷新到1F，此处根据动画的执行百分比来进行计算*/
                    if (animatedFraction <= alphaTimeRate) {
                        it.alpha = animatedFraction * alphaRate
                    } else if (it.alpha == ONE_F) {
                        /**doNothing 避免重复刷新*/
                    } else {
                        it.alpha = ONE_F
                    }
                }
                addListener(onEnd = { _ ->
                    it.isClickable = true
                    it.translationX = 0f
                }, onCancel = { _ ->
                    it.isClickable = true
                    it.translationX = 0f
                })
            }
        }
        miniAppTransitionAnimator = AnimatorSet().apply {
            play(moveOut).with(titleViewAlphaOut).with(numberPickerAlphaOut)
            play(timerFadeIn).with(nameFadeIn).with(resetFadeIn)
            doOnEnd {
                titleView.alpha = 1f
                numberPicker.alpha = 1f
                numberPicker.translationY = 0f
                resetMiniAnim()
            }
            doOnCancel {
                titleView.alpha = 1f
                numberPicker.alpha = 1f
                numberPicker.translationY = 0f
                miniAppTransitionAnimator = null
            }
        }.also {
            it.start()
        }
    }

    /**外屏计时按钮平移动画*/
    private fun miniStartAnimation(
        mMiniStart: View,
        start: Float,
        valueAnimator: ValueAnimator,
        isDisplay: Boolean
    ) {
        var total = abs(start)
        total = if (isDisplay) {
            //X轴正向偏移
            total * valueAnimator.animatedFraction
        } else {
            //X轴反向偏移
            total - total * valueAnimator.animatedFraction
        }
        if (Utils.isRtl()) {
            total = -total
        }
        mMiniStart.translationX = total
    }

    /**
     * miniApp出场动画计时页->自定义计时器
     */
    @Suppress("LongMethod", "LongParameterList")
    fun miniAppExitAnimationWithFade(
        outParent: View,
        titleView: View,
        numberPicker: View,
        inParent: View,
        timeView: View,
        nameView: View,
        resetView: View,
        mMiniStart: View,
        mStartButton: View
    ) {
        if (miniAppTransitionAnimator?.isRunning == true) {
            Log.d(TAG, "transition anim is running")
            return
        }
        resetMiniAnim()
        val translationY =
            numberPicker.resources.getDimensionPixelSize(R.dimen.timer_animator_translation)
        val timerFadeOut = timeView.createFadeAnim(
            DURATION_FADE_SHORT,
            DURATION_FADE_LONG,
            0f,
            translationY.toFloat(),
            false
        )
        val nameFadeOut = nameView.createFadeAnim(
            DURATION_FADE_SHORT,
            DURATION_FADE_LONG,
            0f,
            translationY.toFloat(),
            false
        )

        val resetFadeOut = resetView.let {
            var start = it.marginStart.toFloat()
            if (start == 0F) {
                resetButton(it, false)
                return
            }
            it.isClickable = false
            start = abs(
                DisplayUtils.getRealWindowSize(it.context).width.toFloat() / 2 - abs(start) - it.resources.getDimensionPixelSize(
                        R.dimen.layout_dp_56
                ).toFloat() / 2
            )
            if (Utils.isRtl()) {
                start = -start
            }
            val alphaTimeRate = DURATION_FADE_SHORT_NEW.toFloat() / DURATION_FADE_LONG_NEW.toFloat()
            val alphaRate = DURATION_FADE_LONG_NEW.toFloat() / DURATION_FADE_SHORT_NEW.toFloat()
            ValueAnimator.ofFloat(0f, start).apply {
                duration = DURATION_FADE_LONG_NEW
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animValue ->
                    val value = animValue.animatedValue as Float
                    it.translationX = value
                    /**应设计要求，透明度需要200ms就刷新到0F，此处根据动画的执行百分比来进行计算*/
                    if (animatedFraction <= alphaTimeRate) {
                        it.alpha = ONE_F - animatedFraction * alphaRate
                    } else if (it.alpha == 0F) {
                        /**doNothing 避免重复刷新*/
                    } else {
                        it.alpha = 0F
                    }
                    miniStartAnimation(mMiniStart, start, this, false)
                    miniStartAnimation(mStartButton, start, this, false)
                }
                addListener(onEnd = { _ ->
                    it.translationX = 0f
                    it.isClickable = true
                }, onCancel = { _ ->
                    it.translationX = 0f
                    it.isClickable = true
                })
            }
        }

        val titleViewAlphaIn = titleView.createAlphaAnim(DURATION_FADE_SHORT, true)
        val numberPickerAlphaIn = numberPicker.createAlphaAnim(DURATION_FADE_SHORT, true)
        val moveIn = numberPicker.createTranslationYAnim(
            DURATION_FADE_LONG,
            -translationY.toFloat(),
            0f,
            true
        ).apply {
            doOnStart {
                outParent.visibility = View.INVISIBLE
                inParent.visibility = View.VISIBLE
            }
        }

        miniAppTransitionAnimator = AnimatorSet().apply {
            play(timerFadeOut).with(nameFadeOut).with(resetFadeOut)
            play(moveIn).with(titleViewAlphaIn).with(numberPickerAlphaIn)
            doOnEnd {
                timeView.alpha = 1f
                timeView.translationY = 0f
                nameView.alpha = 1f
                nameView.translationY = 0f
                resetMiniAnim()
            }
            doOnCancel {
                timeView.alpha = 1f
                timeView.translationY = 0f
                nameView.alpha = 1f
                nameView.translationY = 0f
                miniAppTransitionAnimator = null
            }
        }.also {
            it.start()
        }
    }

    /**
     * 结束miniApp动画
     */
    fun resetMiniAnim() {
        miniAppTransitionAnimator?.cancel()
        miniAppTransitionAnimator = null
    }
}

fun View.createFadeAnim(
    alphaDuration: Long,
    transDuration: Long,
    startY: Float,
    endY: Float,
    enter: Boolean
): AnimatorSet {
    val moveIn = this.createTranslationYAnim(transDuration, startY, endY, enter)
    val alphaIn = this.createAlphaAnim(alphaDuration, enter)
    return AnimatorSet().apply {
        play(moveIn).with(alphaIn)
    }
}

fun View.createAlphaAnim(
    duration: Long,
    enter: Boolean
): ObjectAnimator {
    return if (enter) {
        ObjectAnimator.ofFloat(this, "alpha", 0f, 1f).apply {
            this.duration = duration
            interpolator = COUIEaseInterpolator()
        }
    } else {
        ObjectAnimator.ofFloat(this, "alpha", 1f, 0f).apply {
            this.duration = duration
            interpolator = COUIEaseInterpolator()
        }
    }
}

fun View.createTranslationYAnim(
    duration: Long,
    startY: Float,
    endY: Float,
    enter: Boolean
): ObjectAnimator {
    return ObjectAnimator.ofFloat(this, "translationY", startY, endY).apply {
        this.duration = duration
        interpolator = if (enter) {
            COUIInEaseInterpolator()
        } else {
            COUIOutEaseInterpolator()
        }
    }
}
