/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/10/13
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin   2022/10/13     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.content.Context
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.Formatter
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.TimerConstant

private const val UNIT_SECOND_MINUTE: Long = 60
private const val UNIT_HOUR: Long = 24
private const val SECOND: Long = 1000
private const val MINUTE = SECOND * UNIT_SECOND_MINUTE
private const val HOUR = MINUTE * UNIT_SECOND_MINUTE
private const val TIMER_DRAGONFLY_TIME = "timer_dragonfly_time"
private const val TIMER_TOTAL_TIME = "timer_total_time"

fun getTimeMsg(context: Context?): String {
    return context?.run {
        val time = getTime()
        val second = time / SECOND % UNIT_SECOND_MINUTE
        val minute = if (time >= MINUTE) time / MINUTE % UNIT_SECOND_MINUTE else 0
        val hour = if (time >= HOUR) time / HOUR % UNIT_HOUR else 0
        if (hour > 0) {
            if (second > 0) {
                resources.getString(R.string.timer_all_new, hour, minute, second)
            } else {
                if (minute > 0) {
                    resources.getString(R.string.timer_hour_minute_new, hour, minute)
                } else {
                    resources.getString(R.string.timer_hour, hour)
                }
            }
        } else {
            if (minute > 0) {
                if (second > 0) {
                    resources.getString(R.string.timer_minute_second_new, minute, second)
                } else {
                    resources.getString(R.string.timer_minute_new, minute)
                }
            } else {
                resources.getString(R.string.timer_second, second)
            }
        }
    } ?: ""
}

fun getTimeMsgByFluidCloud(context: Context?): String {
    return context?.run {
        val time = getTime()
        val second = time / SECOND % UNIT_SECOND_MINUTE
        val minute = if (time >= MINUTE) time / MINUTE % UNIT_SECOND_MINUTE else 0
        val hour = if (time >= HOUR) time / HOUR % UNIT_HOUR else 0
        if (hour > 0) {
            if (second > 0) {
                resources.getString(R.string.timer_all_new2, hour, minute, second)
            } else {
                if (minute > 0) {
                    resources.getString(R.string.timer_hour_minute_new2, hour, minute)
                } else {
                    resources.getString(R.string.timer_hour_new2, hour)
                }
            }
        } else {
            if (minute > 0) {
                if (second > 0) {
                    resources.getString(R.string.timer_minute_second_new2, minute, second)
                } else {
                    resources.getString(R.string.timer_minute_new2, minute)
                }
            } else {
                resources.getString(R.string.timer_second_new2, second)
            }
        }
    } ?: ""
}

/**
 * 流体云talkback朗读内容
 */
fun getTimeMsgByCon(context: Context?, time: Long, isAlarm: Boolean = false): String {
    return context?.run {
        val second = time / SECOND % UNIT_SECOND_MINUTE + 1
        val minute = if (time >= MINUTE) time / MINUTE % UNIT_SECOND_MINUTE else 0
        val hour = if (time >= HOUR) time / HOUR % UNIT_HOUR else 0
        if (hour > 0) {
            if (second > 0) {
                resources.getString(R.string.timer_all_new2, hour, minute, second)
            } else {
                if (minute > 0) {
                    resources.getString(R.string.timer_hour_minute_new2, hour, minute)
                } else {
                    resources.getString(R.string.timer_hour_new2, hour)
                }
            }
        } else {
            if (isAlarm) {
                //稍后提醒
                if (minute >= ClockConstant.FLUID_CLOUD_COUNT_DOWN_TIME) {
                    resources.getString(R.string.timer_minute_new2, minute)
                } else {
                    getTimeMsgMinute(context, minute, second)
                }
            } else {
                //计时器
                getTimeMsgMinute(context, minute, second)
            }
        }
    } ?: ""
}

private fun getTimeMsgMinute(context: Context?, minute: Long, second: Long): String {
    return context?.run {
        if (minute > 0) {
            if (second > 0) {
                resources.getString(R.string.timer_minute_second_new2, minute, second)
            } else {
                resources.getString(R.string.timer_minute_new2, minute)
            }
        } else {
            resources.getString(R.string.timer_second_new2, second)
        }
    } ?: ""
}


fun getTime(): Long {
    val context: Context = AlarmClockApplication.getInstance()
    val name = AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP
    val time = PrefUtils.getLong(context, name, TIMER_TOTAL_TIME, 0)
    return if (time > 0) {
        time
    } else PrefUtils.getLong(context, name, TIMER_DRAGONFLY_TIME, 0)
}

/**
 * 获取计时时长信息
 * @param context 上下文
 * @return
 */
fun getTimeMsgByCondition(context: Context?): String? {
    return if (TimerSeedlingHelper.canUseFluidCloudStyle(context) || TimerSeedlingHelper.isSupportCapsuleFeature()) {
        getTimeMsg(context)
    } else {
        Formatter.getCountDownTime(context, getTimerSetTime(context))
    }
}

private fun getTimerSetTime(context: Context?): Long {
    return PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_SET_TIME_PREFERENCE, 0L)
}