package com.oplus.alarmclock.timer;

import static com.oplus.alarmclock.timer.TimerAlertUtilsKt.getTime;
import static com.oplus.alarmclock.timer.TimerSeedlingHelper.EVENT_REFRESH_TIMER_VIEW;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.coloros.refusedesktop.model.TimerEntity;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.TimerConstant;

public class TimerNotificationReceiver extends BroadcastReceiver {
    public static final String TIMER_RESUME_TIMER = "com.oplus.alarmclock.Timer.RESUME_TIMER";
    public static final String TIMER_STOP_TIMER = "com.oplus.alarmclock.Timer.STOP_TIMER";
    public static final String TIMER_START_TIMER = "com.oplus.alarmclock.Timer.START_TIMER";
    public static final String TIMER_PAUSE_TIMER = "com.oplus.alarmclock.Timer.PAUSE_TIMER";
    /**
     * 展示计时器通知
     */
    public static final String SHOW_TIMER_NOTIFICATION = "com.oplus.alarmclock.Timer.SHOW_TIMER_NOTIFICATION";
    /**
     * 隐藏计时器通知
     */
    public static final String HIDE_TIMER_NOTIFICATION = "com.oplus.alarmclock.Timer.HIDE_TIMER_NOTIFICATION";


    private static final String TAG = "TimerNotificationReceiver";
    private static final String KEY_IS_FROM_NOTIFICATION = "is_from_notification";
    private static final int DEFAULT_TIMER_INDEX = 0;

    private TimerService mTimerService;
    private String mAction;
    private Context mContext;
    private boolean mFromNotification;
    private int mIndex = DEFAULT_TIMER_INDEX;
    private TimerEntity mTimerEntity;

    @Override
    public void onReceive(Context context, Intent intent) {

        mContext = context.getApplicationContext();
        mAction = intent.getAction();
        Log.d(TAG, "onReceive:" + mAction);
        mFromNotification = intent.getBooleanExtra(KEY_IS_FROM_NOTIFICATION, false);
        mIndex = intent.getIntExtra(TimerNotificationManager.KEY_TIMER_INDEX, DEFAULT_TIMER_INDEX);
        if (TextUtils.isEmpty(mAction)) {
            Log.w(TAG, "action error,return!");
            return;
        }
        if (mTimerService == null) {
            startTimerService(mContext);
        } else {
            handlerTimer();
        }

    }

    private void handlerTimer() {
        if (mTimerService == null) {
            Log.e(TAG, "handlerTimer service not connected!");
            return;
        }
        switch (mAction) {
            case TIMER_STOP_TIMER:
                stopAllTimer(mTimerService);
                break;
            case TIMER_RESUME_TIMER:
                resumeTimer(mTimerService);
                break;

            case TIMER_PAUSE_TIMER:
                pauseTimer(mTimerService);
                break;
            case TIMER_START_TIMER:
                restartTimer(mTimerService);
                break;
            case HIDE_TIMER_NOTIFICATION:
                swtichTimerNotification(mTimerService, true);
                break;
            case SHOW_TIMER_NOTIFICATION:
                swtichTimerNotification(mTimerService, false);
                break;

        }
    }

    private void swtichTimerNotification(TimerService mTimerService, boolean show) {
        mTimerService.loadSeedlingCardResult(show);
    }

    private void startTimerService(Context context) {
        Intent intent = new Intent(context, TimerService.class);
        context.getApplicationContext().bindService(intent, mTimerServiceConnection,
                Service.BIND_AUTO_CREATE);
        context.startService(intent);
    }

    private ServiceConnection mTimerServiceConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "onServiceConnected Bind successfully mTimerServiceConnection");
            mTimerService = ((TimerService.TimerBinder) service).getService();
            handlerTimer();
        }

        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected");
            mTimerService = null;
            mFromNotification = false;
            mIndex = DEFAULT_TIMER_INDEX;
        }
    };

    /**
     * 计时器再来一次
     *
     * @param timerService
     */
    private void restartTimer(TimerService timerService) {
        long total = timerService.getLastTotalTime(mIndex);
        if (total == 0) {
            total = getTime();
        }
        Log.i(TAG, "restartTimer total:" + total);
        timerService.setTotalTime(total, total, mIndex);
        timerService.startTimer(0);
        LiteEventBus.Companion.getInstance().send(EVENT_REFRESH_TIMER_VIEW, "");
    }

    private void stopAllTimer(TimerService timerService) {
        if (timerService == null) {
            Log.e(TAG, "stopAllTimer service not connected!");
            return;
        }

        Log.d(TAG, "stopAllTimer index:" + mIndex + ",fromNotification:" + mFromNotification);
        timerService.stopAllTimer();
        ClockOplusCSUtils.onEvent(mContext, ClockOplusCSUtils.STR_PRESS_TIMER_RESET_MENU);
        if (mFromNotification) {
            TimerNotificationManager.statisticsTimerNotificationOperation(mContext, ClockOplusCSUtils.TIMER_NOTIFICATION_TYPE_CANCEL);
        }
    }

    private void pauseTimer(TimerService timerService) {
        if (timerService == null) {
            Log.e(TAG, "pauseTimer service not connected!");
            return;
        }
        boolean isStart = timerService.isStart(mIndex);
        boolean isPause = timerService.isPause(mIndex);
        Log.d(TAG, "pauseTimer: Timer state: isStart: " + isStart + ", isPause: " + isPause +
                ",index:" + mIndex + ",fromNotification:" + mFromNotification);
        timerService.pauseTimer(mIndex);
        if (isStart) {
            ClockOplusCSUtils.onEvent(mContext, ClockOplusCSUtils.STR_PRESS_TIMER_PAUSE_MENU);
            Intent resumeTimerIntent = new Intent(TimerConstant.RESUME_TIMER_BROADCAST);
            resumeTimerIntent.putExtra(TimerNotificationManager.KEY_TIMER_INDEX, mIndex);
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(resumeTimerIntent);
        }
        if (mFromNotification) {
            TimerNotificationManager.statisticsTimerNotificationOperation(mContext, ClockOplusCSUtils.TIMER_NOTIFICATION_TYPE_CONTINUE);
        }
    }

    private void resumeTimer(TimerService timerService) {
        if (timerService == null) {
            Log.e(TAG, "resumeTimer service not connected!");
            return;
        }
        boolean isStart = timerService.isStart(mIndex);
        boolean isPause = timerService.isPause(mIndex);
        Log.d(TAG, "resumeTimer: Timer state: isStart: " + isStart + ", isPause: " + isPause +
                ",index:" + mIndex + ",fromNotification:" + mFromNotification);
        if (isPause) {
            timerService.startTimer(mIndex);
            ClockOplusCSUtils.onEvent(mContext, ClockOplusCSUtils.STR_PRESS_TIMER_START_MENU);
            Intent resumeTimerIntent = new Intent(TimerConstant.RESUME_TIMER_BROADCAST);
            resumeTimerIntent.putExtra(TimerNotificationManager.KEY_TIMER_INDEX, mIndex);
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(resumeTimerIntent);
        }
        if (mFromNotification) {
            TimerNotificationManager.statisticsTimerNotificationOperation(mContext, ClockOplusCSUtils.TIMER_NOTIFICATION_TYPE_CONTINUE);
        }

    }

}
