/*
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer;

import static com.oplus.alarmclock.utils.ClockConstant.DEVICE_CASE_OPEN;
import static com.oplus.alarmclock.utils.ClockOplusCSUtils.EVENT_DEVICE_CASE_CLOSE_TIMER;

import android.annotation.SuppressLint;
import android.app.WallpaperInfo;
import android.app.WallpaperManager;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatButton;

import com.coui.appcompat.button.COUIButton;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.AlarmAlertWakeLock;
import com.oplus.alarmclock.utils.AppFeatureUtils;
import com.oplus.alarmclock.utils.BitmapUtils;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.OplusDeviceCaseUtilsKt;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.TimerTimeView;
import com.oplus.clock.common.utils.Log;
import com.oplus.hardware.devicecase.OplusDeviceCaseStateCallback;
import com.oplus.vfx.watergradient.VFXFrameLayout;

import java.util.HashMap;
import java.util.concurrent.Executors;

public class TimerAlertFullScreen extends TimerAlert implements DeviceCaseTimerAlertView.DeviceCaseCallback {
    public static final String IS_DRAGONFLY_SMALL = "is_dragonfly_small";
    private static final String TAG = "TimerAlertFullScreen";
    private final static int INVISBLE = 0x00200000 | 0x00400000 | 0x01000000;
    private BitmapDrawable mLockBgBitmap;
    private TimerTimeView mAlarmTimeView;
    private TextView mTimeTv;

    private COUIButton mBtnLockRepeat;
    private AppCompatButton mBtnLockClose;
    private OplusDeviceCaseStateCallback mOplusDeviceCaseStateCallBack;
    private boolean mFistEnter = true;

    private RelativeLayout mAquaticAnimationBg;
    private RelativeLayout mBackground;

    @Override
    @SuppressLint("WrongConstant")
    public void onCreate(Bundle icicle) {
        Log.d(TAG, "OnCreate");
        super.onCreate(icicle);
        mFistEnter = true;
        Intent intent = getIntent();
        Window window = this.getWindow();
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | INVISBLE
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        window.setStatusBarColor(Color.TRANSPARENT);
        window.setNavigationBarColor(Color.TRANSPARENT);

        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_HOME_MENU, Utils.DISABLE_STATUS_BAR);
        if (OplusDeviceCaseUtilsKt.isAvailable()) {
            registerCallBack();
        } else {
            brightScreen();
        }
        updateLayout(intent);
        /**设定水生动画或背景：OS14及以上为水生效果，以下背景为壁纸效果*/
        setAquaticAnimationEffects();
        /**响铃开始时间，用于响铃时长埋点*/
        mStartRingTime = System.currentTimeMillis();
    }

    protected View inflateView(LayoutInflater inflater) {
        if (inflater == null) {
            inflater = LayoutInflater.from(this);
        }
        int id = mIsDragonfly ? R.layout.timer_alert_fullscreen_dragonfly : R.layout.timer_alert_fullscreen_view;
        return inflater.inflate(id, null);
    }

    @Override
    protected void updateFullScreenTime() {
        if (mAlarmTimeView != null) {
            mAlarmTimeView.update();
        }
        /**时间变化时重新设置一次动画效果*/
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS() && mVFXFrameLayout != null) {
            setBackgroundAndCircleColor();
        }
    }

    @Override
    public void updateLayout(Intent intent) {
        mIsDragonfly = intent.getBooleanExtra(IS_DRAGONFLY_SMALL, false);
        if (mView == null) {
            LayoutInflater inflater = LayoutInflater.from(this);
            mView = inflateView(inflater);
        }
        setContentView(mView);
        mAlarmTimeView = mView.findViewById(R.id.time_view);
        mAlarmTimeView.update();
        mTimeTv = mView.findViewById(R.id.timer_tv);
        mBtnLockRepeat = mView.findViewById(R.id.btn_lock_repeat);

        String timerName = intent.getStringExtra(TimerService.TIMER_NAME);
        if (TextUtils.isEmpty(timerName)) {
            timerName = AlarmClockApplication.getInstance().getString(R.string.timer_title);
        }
        mTimeTv.setText(timerName + " • " + TimerAlertUtilsKt.getTimeMsgByFluidCloud(this));
        mBtnLockRepeat.setOnClickListener(v -> repeatClock());
        mBtnLockClose = mView.findViewById(R.id.btn_lock_close);
        mBtnLockClose.setOnTouchListener((v, event) -> {
            if (MotionEvent.ACTION_DOWN == event.getAction()) {
                startPressAnim(v, true);
            } else if ((MotionEvent.ACTION_UP == event.getAction())
                    || (MotionEvent.ACTION_CANCEL == event.getAction())) {
                startPressAnim(v, false);
            }
            return false;
        });
        mBtnLockClose.setOnClickListener(v -> closeTime());
    }

    private void setAquaticAnimationEffects() {
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            if (mVFXFrameLayout == null) {
                mVFXFrameLayout = new VFXFrameLayout(this);
            }
            setVFXFrameLayout();
            changeAquaticState(AQUATIC_STATE_01);
            if (!mIsDragonfly) {
                mBackground = findViewById(R.id.main_bg);
                mBackground.setBackground(null);
            }
            mAquaticAnimationBg = findViewById(R.id.aquatic_animation_bg);
            mAquaticAnimationBg.setVisibility(View.VISIBLE);
            /**水生动效View创建好后添加到背景View中*/
            mAquaticAnimationBg.addView(mVFXFrameLayout);
            /**注册点击事件*/
            mFingerControl = findViewById(R.id.mfv_finger_control);
            mFingerControl.registOnTouchEventCallback(this);
            /**设定重力加速度监听事件*/
            mVFXHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    doRegisterSensor();
                }
            }, DELAY_REGISTER_TIME);
        } else {
            if (mIsDragonfly) {
                mView.setBackgroundResource(R.drawable.alert_bg);
                getWindow().setBackgroundDrawableResource(R.drawable.alert_bg);
            } else {
                mLockBgBitmap = BitmapUtils.getLockBackground(this);
                WallpaperManager wallpaperManager = WallpaperManager.getInstance(this);
                WallpaperInfo wallpaperInfo = (wallpaperManager == null) ? null : wallpaperManager.getWallpaperInfo();
                if (wallpaperInfo != null) {
                    // have dynamic wallpaper
                } else if (mLockBgBitmap != null) {
                    mView.setBackground(mLockBgBitmap);
                    getWindow().setBackgroundDrawable(mLockBgBitmap);
                }
            }
        }
        setViewSpacing();
    }

    private void setViewSpacing() {
        ViewGroup.MarginLayoutParams timeViewLp = (ViewGroup.MarginLayoutParams) mAlarmTimeView.getLayoutParams();
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            mLLSnooze = mView.findViewById(R.id.ll_snooze);
            if (mIsDragonfly) {
                /**副屏文字上下间距调整*/
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_45);
            } else if (FoldScreenUtils.UiMode.LARGE_VERTICAL == obtainUiMode()) {
                /**平板竖屏计时器响铃设置顶部间距*/
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_152);
                mLLSnooze.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.layout_dp_145));
                /**调整平板下按钮的宽度*/
                mBtnLockClose.setWidth(getResources().getDimensionPixelSize(R.dimen.layout_dp_160));
            } else if (FoldScreenUtils.UiMode.LARGE_HORIZONTAL == obtainUiMode()) {
                /**平板横屏计时器响铃设置顶部间距*/
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_100);
                mLLSnooze.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.layout_dp_100));
                /**调整平板下按钮的宽度*/
                mBtnLockClose.setWidth(getResources().getDimensionPixelSize(R.dimen.layout_dp_220));
            } else if (FoldScreenUtils.UiMode.MIDDLE == obtainUiMode()) {
                /**直板机文字上下间距调整*/
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_54);
                mLLSnooze.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.layout_dp_100));
            } else {
                /**直板机文字上下间距调整*/
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_74);
                mLLSnooze.setPadding(0, 0, 0, getResources().getDimensionPixelSize(R.dimen.layout_dp_100));
            }
            mAlarmTimeView.setLayoutParams(timeViewLp);
        } else {
            /**
             * 非水生效果保持原样
             * 平板竖屏计时器响铃设置顶部间距
             */
            if (FoldScreenUtils.UiMode.LARGE_VERTICAL == obtainUiMode()) {
                timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_120);
                mAlarmTimeView.setLayoutParams(timeViewLp);
            }
        }
    }

    /**
     * 注册皮套模式监听
     */
    private void registerCallBack() {
        Log.i(TAG, "timer registerOplusDeviceCase");
        if (OplusDeviceCaseUtilsKt.getManager() != null) {
            OplusDeviceCaseUtilsKt.getManager().registerCallback(
                    Executors.newSingleThreadExecutor(), mOplusDeviceCaseStateCallBack = new OplusDeviceCaseStateCallback() {
                        @Override
                        public void onStateChanged(int state) {
                            OplusDeviceCaseStateCallback.super.onStateChanged(state);
                            Log.i(TAG, "DeviceCaseState:" + state + "mFistEnter :" + mFistEnter);
                            runOnUiThread(() -> {
                                if (state == DEVICE_CASE_OPEN) {
                                    String msg = TimerAlertUtilsKt.getTimeMsg(TimerAlertFullScreen.this);
                                    View view = DeviceCaseTimerAlertView.INSTANCE.createDeviceCaseView(
                                            TimerAlertFullScreen.this, msg, TimerAlertFullScreen.this);
                                    DeviceCaseTimerAlertView.INSTANCE.showDeviceCaseView(view);
                                    brightScreen();
                                    //皮套闭合
                                    if (mFistEnter) {
                                    } else {
                                        DeviceCaseTimerAlertView.INSTANCE.hideDeviceCaseView();
                                        finish();
                                    }
                                } else {
                                    //皮套打开
                                    if (!mFistEnter) {
                                        DeviceCaseTimerAlertView.INSTANCE.hideDeviceCaseView();
                                    } else {
                                        brightScreen();
                                    }
                                }
                                mFistEnter = false;
                            });
                        }
                    });
        }
    }


    /**
     * 亮屏
     */
    private void brightScreen() {
        mView.post(this::acquireCpuWakeLockPartial);
    }

    public void acquireCpuWakeLockPartial() {
        TimerWakeLock.releaseCpuLockPartial();
        TimerWakeLock.acquireCpuWakeLockFull(AlarmClockApplication.getInstance());
    }

    /**
     * 重复
     */
    public void repeatClock() {
        Intent intent = new Intent(this, TimerNotificationReceiver.class);
        intent.setAction(TimerNotificationReceiver.TIMER_START_TIMER);
        intent.putExtra(TimerNotificationManager.KEY_TIMER_INDEX, 0);
        sendBroadcast(intent);
        finish();
    }

    protected void closeTime() {
        Log.d(TAG, "closeTime");
        requestKeyGuard();
        finish();
    }

    @Override
    public void onDestroy() {
        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_NORMAL, Utils.DEFAULT_STATUS_BAR);
        AlarmAlertWakeLock.releasePartialWakelock();
        super.onDestroy();
        if (mAlarmTimeView != null) {
            mAlarmTimeView.removeAllViews();
            mAlarmTimeView = null;
        }

        //隐藏皮套view
        DeviceCaseTimerAlertView.INSTANCE.hideDeviceCaseView();
        if (OplusDeviceCaseUtilsKt.getManager() != null && (mOplusDeviceCaseStateCallBack != null)) {
            OplusDeviceCaseUtilsKt.getManager().unregisterCallback(mOplusDeviceCaseStateCallBack);
        }
        DeviceCaseTimerAlertView.INSTANCE.setShow(false);
        mFistEnter = false;
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            if (mTimer != null) {
                mTimer.cancel();
                mTimer = null;
            }
            if (mSensorManager != null) {
                mSensorManager.unregisterListener(this);
            }
        }
        uploadRingTime();
    }

    private void uploadRingTime() {
        /**上传响铃时长的埋点*/
        mEndRingTime = System.currentTimeMillis();
        String ringTime = (mEndRingTime - mStartRingTime) + MS;
        HashMap<String, String> map = new HashMap();
        map.put(ClockOplusCSUtils.EVENT_LOCK_SCREEN_TIMER_RING_DURATION_KEY, ringTime);
        ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_LOCK_SCREEN_TIMER_RING_DURATION, map);
    }


    @Override
    public void deviceCaseClose() {
        //皮套模式关闭计时器埋点
        ClockOplusCSUtils.onCommon(
                AlarmClockApplication.getInstance(),
                EVENT_DEVICE_CASE_CLOSE_TIMER
        );
        finish();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            if (mVFXFrameLayout == null) {
                mVFXFrameLayout = new VFXFrameLayout(this);
            }
            setVFXFrameLayout();
        }
        setViewSpacing();
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    public void touchEvent(MotionEvent ev) throws IllegalArgumentException {
        final int action = ev.getActionMasked();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_MOVE:
                if (mTimer != null) {
                    mTimer.cancel();
                    mTimer = null;
                    mTimerIsRunning = false;
                }
                mIsTouch = true;
                changeAquaticState(AQUATIC_STATE_03);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mIsTouch = false;
                break;
            default:
                break;
        }
    }
}