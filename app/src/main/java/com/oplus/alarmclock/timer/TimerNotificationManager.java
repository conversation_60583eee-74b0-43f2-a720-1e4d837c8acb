/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerNotificationManager.java
 ** Description: Notification manager of timer.
 ** Version: 1.0
 ** Date : 2019/9/29
 ** Author: YangL<PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/9/29     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.text.format.DateFormat;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.NotificationUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.VersionUtils;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Locale;

public class TimerNotificationManager {

    public static final String KEY_TIMER_INDEX = "TIMER_INDEX";
    public static final String KEY_TIMER_MINUTE = "TIMER_INDEX_MINUTE";
    public static final int TIMER_NOTIFICATION_TYPE_INTERCEPT_SEND = 1;
    public static final String TIMER_ACTION_TYPE = "TIMER_ACTION_TYPE";
    private static final String TAG = "TimerNotificationManager";

    public static final int TIMER_NOTIFICATION_ID = -1000;
    public static final String TIMER_NOTIFICATION_CHANNEL_ID = "timer_notification_channel2";

    /**
     * @param isStart true for start, false for pause
     */
    @SuppressLint("StringFormatInvalid")
    public static void showTimerNotification(Context context, long seconds, boolean isStart, int timerIndex, long runTime) {
        String contentString = Formatter.getTimerStr(runTime, context);
        String cancelOrContinueString = "";
        //继续和暂停的intent
        Intent startOrPauseIntent = null;
        //取消intent
        Intent cancelIntent = new Intent(context, TimerNotificationReceiver.class);
        cancelIntent.setAction(TimerNotificationReceiver.TIMER_STOP_TIMER);
        String cancel = context.getString(R.string.cancel);

        if (!isStart) {
            //走继续 ，计时已经暂停
            cancelOrContinueString = context.getString(R.string.text_timer_btn_continue);
            startOrPauseIntent = new Intent(context, TimerNotificationReceiver.class);
            startOrPauseIntent.setAction(TimerNotificationReceiver.TIMER_RESUME_TIMER);
            startOrPauseIntent.putExtra(KEY_TIMER_INDEX, timerIndex);
        } else {
            //当前计时在运行  需要有暂停
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, (int) seconds);

            String format = Formatter.getTimeFormatHMSWithoutAMPM(context);
            CharSequence newTime = DateFormat.format(format, calendar);

            String amPm = "";
            if (!DateFormat.is24HourFormat(context)) {
                if (calendar.get(Calendar.AM_PM) == Calendar.AM) {
                    amPm = context.getString(R.string.am);
                } else {
                    amPm = context.getString(R.string.pm);
                }
            }
            String country = Locale.getDefault().getCountry();
            boolean showAmPmForwards = ("CN").equals(country) || ("TW").equals(country);
            String newTimeWithAmPm = showAmPmForwards ? amPm + newTime : newTime + amPm;
            startOrPauseIntent = new Intent(context, TimerNotificationReceiver.class);
            startOrPauseIntent.setAction(TimerNotificationReceiver.TIMER_PAUSE_TIMER);
            cancelOrContinueString = context.getString(R.string.text_timer_btn_pause);
        }
        cancelIntent.putExtra(TimerService.KEY_IS_FROM_NOTIFICATION, true);
        //暫停or继续
        PendingIntent pauseTimerOrContinueIntent = PendingIntent.getBroadcast(context, 0,
                startOrPauseIntent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        //取消
        PendingIntent cancelPendingIntent = PendingIntent.getBroadcast(context, 0,
                cancelIntent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        Intent interceptIntent = new Intent(context, TimerService.class);
        interceptIntent.putExtra(TIMER_ACTION_TYPE, TIMER_NOTIFICATION_TYPE_INTERCEPT_SEND);
        //删除通知
        PendingIntent deletePendingIntent = PendingIntent.getService(context, 0,
                interceptIntent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

        Intent enterApp = new Intent(context, AlarmClock.class);
        enterApp.setAction(TimerAlertReceiver.ACTION_ENTER_AND_OPEN_TIMER);
        enterApp.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        enterApp.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_OPLUSTIME);
        PendingIntent enterAppIntent = PendingIntent.getActivity(context, 0,
                enterApp, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

        NotificationManager notificationManager = (NotificationManager) context.getSystemService(
                Context.NOTIFICATION_SERVICE);

        Notification.Builder notificationBuilder = new Notification.Builder(context, TIMER_NOTIFICATION_CHANNEL_ID);
        notificationBuilder.setContentTitle(context.getString(R.string.timer_title));
        notificationBuilder.setShowWhen(false);
        notificationBuilder.setDeleteIntent(deletePendingIntent);
        notificationBuilder.setAutoCancel(false);
        notificationBuilder.setSmallIcon(R.drawable.ic_launcher_clock);
        Notification.Action.Builder actionBuilderCancelSnooze = new Notification.Action.Builder(
                R.drawable.ic_launcher_clock, cancelOrContinueString, pauseTimerOrContinueIntent);
        notificationBuilder.addAction(actionBuilderCancelSnooze.build());

        Notification.Action.Builder builder = new Notification.Action.Builder(
                R.drawable.ic_launcher_clock, cancel, cancelPendingIntent);
        notificationBuilder.addAction(builder.build());

        CharSequence name = context.getString(R.string.timer_notification_label);
        NotificationChannel channel = null;
        channel = new NotificationChannel(TIMER_NOTIFICATION_CHANNEL_ID, name,
                NotificationManager.IMPORTANCE_DEFAULT);
        channel.setSound(null, null);
        channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        if (notificationManager != null) {
            notificationManager.createNotificationChannel(channel);
        }
        Notification notification = notificationBuilder.build();
        notification.flags = Notification.FLAG_NO_CLEAR | Notification.FLAG_ONLY_ALERT_ONCE;
        notificationBuilder.setContentIntent(enterAppIntent);
        notificationBuilder.setOngoing(true);
        notificationBuilder.setContentText(contentString);
        if (VersionUtils.isOSVersion1501()) {
            String serviceID = TimerSeedlingHelper.queryTimerFluidServiceId(context);
            notification.extras.putString(NotificationUtils.FLUID_SERVICE_ID, serviceID);
        }
        if (notificationManager != null) {
            notificationManager.notify(TIMER_NOTIFICATION_ID, notification);
        }
    }

    public static void cancelTimerNotification(Context context) {
        Log.d(TAG, "cancelTimerNotification");
        final NotificationManager nm = (NotificationManager) context
                .getSystemService(Context.NOTIFICATION_SERVICE);
        if (nm != null) {
            nm.cancel(TIMER_NOTIFICATION_ID);
        }
    }

    public static void statisticsTimerNotificationOperation(Context context, String operationType) {
        Log.d(TAG, "statistics " + operationType);
        HashMap<String, String> map = new HashMap<>();
        map.put(ClockOplusCSUtils.EVENT_KEY_TIMER_NOTIFICATION, operationType);
        ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.EVENT_ID_TIMER_NOTIFICATION, map);
    }

}
