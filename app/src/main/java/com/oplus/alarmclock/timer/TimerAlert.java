/*
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityOptions;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.SimpleAdapter;
import android.widget.TextView;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alert.AbsSliderActivity;
import com.oplus.alarmclock.alert.AlarmAlert;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.MultiFingerView;
import com.oplus.clock.common.osdk.WindowNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.ContinueUtils;
import com.oplus.utils.DragonflyUtils;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class TimerAlert extends AbsSliderActivity implements MultiFingerView.Callback {
    private static final String IS_DRAGONFLY_SMALL = "is_dragonfly_small";
    private static final String TAG = "TimerAlert";
    private static final boolean DEBUG = true;
    private static final int TIMER_OVER = 8;
    private static final int TIMER_OVER_DELAY = 420;
    private static final int TIMER_ID = 1235;
    public static boolean sIsCountDownSceenLock = true;

    private static class StaticHandler extends Handler {
        private final WeakReference<TimerAlert> mActivity;

        public StaticHandler(TimerAlert activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            TimerAlert activity = mActivity.get();
            if (activity != null) {
                if (msg.what == TIMER_OVER) {
                    activity.finish();
                }
            }
        }
    }

    private final StaticHandler mHandler = new StaticHandler(this);
    /* receive the broadcast of shutdown the phone or finish() timer alert */
    private final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action == null) {
                return;
            }
            if (DEBUG) {
                Log.i(TAG, "intent.getAction()=" + intent.getAction());
            }
            if (action.endsWith(Intent.ACTION_SCREEN_OFF)) {
                finish();
            }
            if (action.endsWith(TimerConstant.STOP_TIMERALERT)) {
                finish();
            }
            if (action.endsWith(Intent.ACTION_TIME_TICK)) {
                updateFullScreenTime();
                DeviceCaseTimerAlertView.INSTANCE.updateTimeView();
            }
            if (Intent.ACTION_USER_PRESENT.equals(action) && DeviceUtils.isKeyGuardEnable()) {
                finish();
            }
        }
    };

    private final BroadcastReceiver mLocalBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "Received broadcast:" + action);
            if (action == null) {
                return;
            }
            if (TimerConstant.STOP_TIMERALERT.equals(action)
                    || TimerConstant.TIMER_ALERT_TIMEOUT.equals(action)) {
                finish();
            }
        }
    };

    private final BroadcastReceiver mSwitchUserReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action == null) {
                Log.e(TAG, "switch user action is null");
                return;
            }
            Log.d(TAG, "switch user onReceive:" + action);
            switch (action) {

                case Intent.ACTION_USER_FOREGROUND:
                case Intent.ACTION_USER_BACKGROUND:
                    Log.d(TAG, "finish TimerAlert when switch user");
                    finish();
                    break;
            }
        }
    };

    protected Context mContext;
    private SimpleAdapter mListAdapter;
    private ArrayList<Map<String, String>> mTimerItems;
    private LocalBroadcastManager mLocalBroadcastManager;
    private boolean mIsCTS = false;
    /**
     * mIsContinue用于蜻蜓主屏和副屏的接续 true表示需要接续，只关闭界面不关闭功能
     */
    private boolean mIsContinue = false;
    private DragonflyUtils mDragonflyUtils;

    @Override
    @SuppressLint({"InlinedApi", "ScreencaptureDetector"})
    public void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate");
        setTitle("");
        mContext = this;

        mTimerItems = new ArrayList<>();

        Window window = getWindow();
        Intent intent = getIntent();
        boolean isDragonflySmall = intent.getBooleanExtra(IS_DRAGONFLY_SMALL, false);
        int flags = WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_FULLSCREEN
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON;
        if (!DeviceUtils.isSuperPowerSaveMode(this.getApplicationContext())) {
            flags |= WindowManager.LayoutParams.FLAG_SHOW_WALLPAPER;
        }
        window.addFlags(flags);
        window.setStatusBarColor(Color.TRANSPARENT);
        updateLayout(intent);//override by subclass

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        intentFilter.addAction(Intent.ACTION_TIME_TICK);
        intentFilter.addAction(Intent.ACTION_USER_PRESENT);
        registerReceiver(mBroadcastReceiver, intentFilter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(this);
        IntentFilter intentLocalFilter = new IntentFilter();
        intentLocalFilter.addAction(TimerConstant.STOP_TIMERALERT);
        intentLocalFilter.addAction(TimerConstant.TIMER_ALERT_TIMEOUT);
        mLocalBroadcastManager.registerReceiver(mLocalBroadcastReceiver, intentLocalFilter);
        if (AlarmAlert.sIsAlarmAlertRing) {
            AlarmAlert.sIsAlarmAlertRing = false;
        }

        final IntentFilter switchUserFilter = new IntentFilter();
        switchUserFilter.addAction(Intent.ACTION_USER_FOREGROUND);
        switchUserFilter.addAction(Intent.ACTION_USER_BACKGROUND);
        registerReceiver(mSwitchUserReceiver, switchUserFilter);

        setBroadcastToOthers();
        mDragonflyUtils = ContinueUtils.registerListener(this, mDragonflyUtils, isSmallScreen -> continueActivity(intent, isDragonflySmall));
    }

    private void setBroadcastToOthers() {
        final Intent intentStopAlarm = new Intent(TimerService.STOP_ALARM_ACTION);
        mLocalBroadcastManager.sendBroadcast(intentStopAlarm);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent()");
        updateLayout(intent);
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d(TAG, "onStop() -- " + sIsCountDownSceenLock);
        if (!sIsCountDownSceenLock) {
            sIsCountDownSceenLock = true;
            finish();
        }
    }

    @Override
    public void finish() {
        if (!mIsContinue) {
            TimerFloatingViewService.stopTimer(mContext);
            mContext.stopService(new Intent(mContext, TimerKlaxon.class));
        }
        super.finish();
    }

    protected void updateLayout(Intent intent) {

        if (intent == null) {
            return;
        }

        String name = intent.getStringExtra(TimerService.TIMER_NAME);
        String index = intent.getStringExtra(TimerService.TIMER_INDEX);
        Log.d(TAG, "dialog:" + name + intent + ", index:" + index);

        if (index == null) {
            // Here maybe run by using a empty intent to start activity.For ex., phone
            // is coming and delay the timer ringing.
            Log.w(TAG, "index = null!!!! error!, ring default timer!!!");
            index = TimerService.DEFAULT_TIMER_INDEX;
            name = "Timer 0";
        }

        Map<String, String> map = new HashMap<>();
        map.put(TimerService.TIMER_NAME, name);
        map.put(TimerService.TIMER_INDEX, index);

        boolean isExist = false;
        for (int i = 0; i < mTimerItems.size(); i++) {
            if (index.equals(mTimerItems.get(i).get(TimerService.TIMER_INDEX))
                    && name.equals(mTimerItems.get(i).get(TimerService.TIMER_NAME))) {
                isExist = true;
            }
        }
        if (!isExist) {
            mTimerItems.add(map);
        }

        Dialog dialog = null;
        if (((mTimerItems.size() == 1) && (mTimerItems.get(0).get(TimerService.TIMER_NAME).equals("Timer 0")))) {
            Log.d(TAG, "getEmptyTimeAlarmDialog");
            dialog = getEmptyTimeAlarmDialog(this);
            mIsCTS = false;
            dialog.show();
            View view = (View) dialog.findViewById(android.R.id.message);
            if (view != null) {
                if (view instanceof TextView) {
                    ((TextView) view).setGravity(Gravity.CENTER_HORIZONTAL);
                }
            }
        } else {
            if (!mIsCTS) {
                dialog = getTimeAlarmDialog();
                mIsCTS = true;
                dialog.show();
            } else {
                if (mListAdapter != null) {
                    mListAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    protected void updateFullScreenTime() {
        // do nothing
    }

    protected long getTimerSetTime() {
        return PrefUtils.getLong(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_SET_TIME_PREFERENCE, 0L);
    }

    private Dialog getTimeAlarmDialog() {
        mListAdapter = new SimpleAdapter(this, mTimerItems, R.layout.timer_list_item_view_for_cts,
                new String[]{TimerService.TIMER_NAME}, new int[]{R.id.timer_name});

        Dialog dialog = new AlertDialog.Builder(this).setTitle(getString(R.string.timer_finished))
                .setPositiveButton(R.string.stop_alarm, (dialog1, which) -> {
                    Log.i(TAG, "dialog click button!");
                    mTimerItems.clear();
                    dialog1.dismiss();
                }).setAdapter(mListAdapter, null).create();

        dialog.setCanceledOnTouchOutside(false);


        dialog.setOnDismissListener(dialog12 -> {
            Log.d(TAG, "dialog on dismiss!");
            mTimerItems.clear();
            stopService(new Intent(mContext, TimerKlaxon.class));
            mHandler.sendEmptyMessageDelayed(TIMER_OVER, TIMER_OVER_DELAY);
        });
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowNativeUtils.setIgnoreHomeMenuKey(dialogWindow);
        }
        dialog.setOnKeyListener((dialog13, keyCode, event) -> {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_BACK:
                    return true;
                case KeyEvent.KEYCODE_VOLUME_UP:
                case KeyEvent.KEYCODE_VOLUME_DOWN:
                    stopService(new Intent(mContext, TimerKlaxon.class));
                    return true;
                default:
                    break;
            }
            return false;
        });

        return dialog;
    }

    private Dialog getEmptyTimeAlarmDialog(Activity activity) {
        long time = getTimerSetTime();
        String countDownTime = Formatter.getCountDownTime(this, time);

        Dialog dialog = new AlertDialog.Builder(this).setTitle(R.string.timer_finished)
                .setMessage(countDownTime)
                .setPositiveButton(R.string.stop_alarm, (dialog1, which) -> {
                    Log.d(TAG, "dialog click button!");
                    mTimerItems.clear();
                    dialog1.dismiss();
                }).setAdapter(mListAdapter, null).create();
        dialog.setCanceledOnTouchOutside(false);

        dialog.setOnDismissListener(dialog12 -> {
            Log.d(TAG, "dialog on dismiss!");
            mTimerItems.clear();
            stopService(new Intent(mContext, TimerKlaxon.class));
            // finish();
            mHandler.sendEmptyMessageDelayed(TIMER_OVER, TIMER_OVER_DELAY);
        });
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowNativeUtils.setIgnoreHomeMenuKey(dialogWindow);
        }

        Utils.setHomeKeyLocked(activity);

        dialog.setOnKeyListener((dialog13, keyCode, event) -> {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_BACK:
                    return true;
                case KeyEvent.KEYCODE_VOLUME_UP:
                case KeyEvent.KEYCODE_VOLUME_DOWN:
                    stopService(new Intent(mContext, TimerKlaxon.class));
                    return true;
                default:
                    break;
            }
            return false;
        });
        return dialog;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        // Don't allow back to dismiss.
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        ContinueUtils.unregisterListener(this, mDragonflyUtils);
        resetTimerStatus();
        NotificationManager mNotification = (NotificationManager) getSystemService(
                NOTIFICATION_SERVICE);
        if (mNotification != null) {
            mNotification.cancel(TIMER_ID);
        }
        unregisterReceiver(mBroadcastReceiver);
        mLocalBroadcastManager.unregisterReceiver(mLocalBroadcastReceiver);
        unregisterReceiver(mSwitchUserReceiver);
        TimerWakeLock.releaseCpuLockFull();
    }

    private void resetTimerStatus() {
        PrefUtils.putBoolean(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_START_PREFERENCE, false);
        PrefUtils.putBoolean(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        // Do this on key down to handle a few of the system keys.
        switch (event.getKeyCode()) {
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_CAMERA:
            case KeyEvent.KEYCODE_FOCUS:
                finish();
                return true;
            default:
                break;
        }
        return super.dispatchKeyEvent(event);
    }

    /**
     * 蜻蜓界面接续
     */
    private void continueActivity(Intent intent, boolean isDragonflySmall) {
        ContinueUtils.unregisterListener(TimerAlert.this, mDragonflyUtils);
        //为避免系统获取的isSmallScreen可能不准确，直接使用isDragonflySmall
        boolean isSmall = !isDragonflySmall;
        if (isSmall) {
            finish();
        } else {
            mIsContinue = true;
            finish();
            intent.putExtra(IS_DRAGONFLY_SMALL, false);
            ActivityOptions options = ActivityOptions.makeBasic();
            options.setLaunchDisplayId(0);
            startActivity(intent, options.toBundle());
        }
    }
}