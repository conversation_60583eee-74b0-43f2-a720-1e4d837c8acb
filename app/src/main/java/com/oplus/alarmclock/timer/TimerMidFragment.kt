/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - TimerMidFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.content.Context
import android.graphics.drawable.Drawable
import android.transition.AutoTransition
import android.transition.TransitionManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.recyclerview.widget.GridLayoutManager
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.TimerMainMidViewBinding
import com.oplus.alarmclock.timer.ui.TimerAdapterSpaceItemDecoration
import com.oplus.alarmclock.timer.ui.TimerController
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.TimerRecyclerView
import com.oplus.alarmclock.view.water.WaterClockView

class TimerMidFragment : OplusTimerFragment<TimerMainMidViewBinding>() {

    override fun layoutId(): Int {
        return R.layout.timer_main_mid_view
    }

    override fun createTimerController(index: Int): TimerController {
        return TimerController(index, this)
    }

    override fun updateSplitParams() {
        super.updateSplitParams()
        mTimerController?.mOplusTimerPicker?.updatePadSplitParams()
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        if (mTimerController != null && !mTimerController.mIsHover) {
            timerTextView()?.apply {
                setTextSize(resources.getDimension(R.dimen.text_size_sp_28))
            }
        }
        timerProgressViewLayout()?.viewTreeObserver?.addOnGlobalLayoutListener {
            moveToScale()
        }
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
        }
        mTimerController?.mOplusTimerPicker?.setBurmeseDiffusion()
    }

    override fun checkHoverStatus() {
        super.checkHoverStatus()
        (activity as? AlarmClock)?.apply {
            if (mLayoutInfo == ClockConstant.WINDOW_LAYOUT_TABLE_TOP_POSTURE) {
                changeToHover()
            }
        }
    }

    override fun flexibleScenario() {
        super.flexibleScenario()
        mTimerController?.run {
            context?.let {
                if (FoldScreenUtils.isFlexibleScenario(it)) {
                    if (mIsHover) {
                        changeNormal()
                    }
                }
            }
        }
    }

    override fun setTimerProgressLayout() {
        super.setTimerProgressLayout()
        mViewBinding?.apply {
            mTimerController?.run {
                if (timerLayout() != null) {
                    val paddingTop = Utils.getStatusBarHeight(context)
                    if (mIsHover) {
                        //悬停状态不设置间距
                        timerLayout()?.setPadding(0, 0, 0, 0)
                    } else {
                        val bottomMargin = resources.getDimensionPixelSize(R.dimen.clock_dial_top_margin)
                        timerLayout()?.setPadding(0, paddingTop - bottomMargin, 0, 0)
                    }
                }
                timerText.center(mIsHover)
                what.run {
                    val llp = layoutParams as ViewGroup.MarginLayoutParams
                    val whatMargin = if (mIsHover) {
                        resources.getDimensionPixelSize(R.dimen.layout_dp_77)
                    } else {
                        timerText.getMarginTop() - resources.getDimensionPixelSize(R.dimen.layout_dp_32)
                    }
                    llp.topMargin = whatMargin
                    layoutParams = llp
                }
            }
        }
    }

    override fun setOsloTimerListPadding() {
        addTimerLayout()?.setPadding(0, 0, 0, 0)
        timerRecyclerView()?.setPadding(0, 0, 0, 0)
    }

    override fun setComponentBtn() {
        mViewBinding?.apply {
            buttonStart()?.translationX = TimerAnimationManager.mButtonStartTransitionX
        }
    }

    override fun getBtnHorizontalSpacing(): Int {
        return mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_28)
    }

    /**
     * 切换至悬停
     */
    override fun changeToHover() {
        mTimerController?.apply {
            if (mTimerAnimatorManager == null || mIsHover) {
                return
            }
            mIsHover = true
            if (mIsStart || mIsPause) {
                timerRecyclerView()?.visibility = View.GONE
            }
            TransitionManager.endTransitions(timerLayout())
            moveToScale()
            timerTextView()?.visibility = View.INVISIBLE
            val ac = AutoTransition()
            ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
            ac.interpolator = COUIEaseInterpolator()
            val set = ConstraintSet()
            set.clone(mContext, R.layout.timer_main_view_hover)
            TransitionManager.beginDelayedTransition(timerLayout(), ac)
            set.applyTo(timerLayout())
            //表盘放大
            moveToScale()
            if (mIsStart || mIsPause) {
                //隐藏阴影
                shadowBg()?.visibility = View.INVISIBLE
                timerView()?.visibility = View.INVISIBLE
                //显示表盘内数字
                timerProgressViewLayout()?.visibility = View.VISIBLE
                //隐藏列表时间选择器
                timerRecyclerView()?.visibility = View.GONE
                addTimerLayout()?.visibility = View.INVISIBLE
                oplusTimerPicker()?.visibility = View.INVISIBLE
                timerTextView()?.visibility = View.VISIBLE
            } else {
                timerRecyclerView()?.visibility = View.VISIBLE
                oplusTimerPicker()?.visibility = View.VISIBLE
                addTimerLayout()?.visibility = View.VISIBLE
                timerProgressViewLayout()?.visibility = View.INVISIBLE
            }
            timerTextView()?.apply {
                //切换为数字模式，数字放大
                setTextSize(mContext.resources.getDimension(R.dimen.text_size_sp_58))
                val llp = layoutParams as ViewGroup.MarginLayoutParams
                llp.topMargin = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_109)
                layoutParams = llp
            }
            //设置字体顶部间距
            setTimerProgressLayout()
        }
    }

    override fun changeNormal() {
        super.changeNormal()
        mTimerController?.run {
            if (!mIsHover || mTimerAnimatorManager == null) {
                return
            }
            TransitionManager.endTransitions(timerLayout())
            mIsHover = false
            //表盘放大
            moveToScale()
            mTimerTextView.visibility = View.INVISIBLE
            val ac = AutoTransition()
            ac.duration = FoldScreenUtils.CHANG_SCENE_DURATION.toLong()
            ac.interpolator = COUIEaseInterpolator()
            val set = ConstraintSet()
            set.clone(mContext, R.layout.timer_main_mid_view_content)
            TransitionManager.beginDelayedTransition(timerLayout(), ac)
            set.applyTo(timerLayout())
            //表盘放大
            moveToScale()
            //显示阴影
            mShadowBg.visibility = View.VISIBLE
            mTimerView.visibility = View.VISIBLE
            if (mIsStart || mIsPause) {
                //显示表盘内数字
                mTimerProgressViewLayout.visibility = View.VISIBLE
                //隐藏列表时间选择器
                timerRecyclerView()?.visibility = View.GONE
                mAddTimerLayout.visibility = View.INVISIBLE
                mOplusTimerPicker.visibility = View.INVISIBLE
                mTimerTextView.visibility = View.VISIBLE
            } else {
                timerRecyclerView()?.visibility = View.VISIBLE
                mOplusTimerPicker.visibility = View.VISIBLE
                mAddTimerLayout.visibility = View.VISIBLE
                mTimerProgressViewLayout.visibility = View.INVISIBLE
            }
            mTimerTextView.apply {
                setTextSize(mContext.resources.getDimension(R.dimen.text_size_sp_28))
                val llp = layoutParams as ViewGroup.MarginLayoutParams
                llp.topMargin = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_36)
                layoutParams = llp
            }
            setTimerProgressLayout()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mTimerController?.run {
            if (mTimerTextView.handler != null) {
                mTimerTextView.handler.removeCallbacksAndMessages(null)
            }
        }
    }

    override fun setRecyclerViewLayoutManager() {
        super.setRecyclerViewLayoutManager()
        mTimerController?.run {
            timerRecyclerView()?.let {
                val gridLayoutManager1 = GridLayoutManager(mContext, 2, GridLayoutManager.VERTICAL, false)
                it.layoutManager = gridLayoutManager1
                it.addItemDecoration(TimerAdapterSpaceItemDecoration().also { its -> mTimerAdapterDec = its })
                }
            }
    }

    private fun moveToScale() {
        timerProgressViewLayout()?.apply {
            pivotX = width / FoldScreenUtils.NUMBER_TWO
            pivotY = 0f
            scaleX = FoldScreenUtils.DIAL_SCALE_MOVE
            scaleY = FoldScreenUtils.DIAL_SCALE_MOVE
        }
    }

    override fun timerAdd(): TextView? {
        return mViewBinding?.timerAdd
    }

    override fun oplusTimerPicker(): OplusTimePickerCustomClock? {
        return mViewBinding?.oplusTimerPicker
    }

    override fun timerView(): TimerView? {
        return mViewBinding?.timerView
    }

    override fun timerTextView(): TimerTextView? {
        return mViewBinding?.timerText
    }

    override fun titleName(): TextView? {
        return mViewBinding?.what
    }

    override fun addTimerLayout(): RelativeLayout? {
        return mViewBinding?.addTimerLayout
    }

    override fun timerProgressViewLayout(): View? {
        return mViewBinding?.timerProgressViewLayout
    }

    override fun shadowBg(): WaterClockView? {
        return mViewBinding?.timerBg
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun timerLayout(): ConstraintLayout? {
        return mViewBinding?.timerRootPort
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }


    override fun timerRecyclerView(): TimerRecyclerView? {
        return mViewBinding?.situationTimerViewList
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_235)
    }

    override fun getMainFabDrawable(context: Context, isStart: Boolean): Drawable? {
        return if (isStart) {
            context.getDrawable(R.drawable.button_start_mid)
        } else {
            context.getDrawable(R.drawable.button_pause_mid)
        }
    }
}