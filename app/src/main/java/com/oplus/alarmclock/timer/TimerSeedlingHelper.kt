/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerSeedlingHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/7/06
 ** Author: ********
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ********   2023/7/06     1.0            add file
 ****************************************************************/
@file:Suppress(
    "MaximumLineLength",
    "WhenExpressionFormattingRule",
    "LongParameterList",
    "LongMethod"
)

package com.oplus.alarmclock.timer

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.heytap.addon.content.OplusFeatureConfigManager
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.fluid.AlarmRingVolumeUtils
import com.oplus.alarmclock.provider.widget.ClockSeedlingCardProvider
import com.oplus.alarmclock.provider.widget.ClockSeedlingCardProvider.Companion.statusBarSeedlingCard
import com.oplus.alarmclock.timer.data.FluidCloudTimerEntity
import com.oplus.alarmclock.utils.AppPlatformUtils.isSubUser
import com.oplus.alarmclock.utils.ChannelManager
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_DEFAULT
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_FAIL
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_LOADING
import com.oplus.alarmclock.utils.ClockConstant.STATUS_LOADCARD_SUCCESS
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FluidWakeLockUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.IBaseChannel
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.TimerConstant
import com.oplus.alarmclock.utils.TimerConstant.STOP_TIMERALERT_EXTRA
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.Log.d
import com.oplus.clock.common.utils.Log.e
import com.oplus.clock.common.utils.Log.i
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils
import com.oplus.pantanal.seedling.bean.CancelPanelActionConfigEnum
import com.oplus.pantanal.seedling.bean.PanelActionEnum
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingHostEnum
import com.oplus.pantanal.seedling.bean.SeedlingIntent
import com.oplus.pantanal.seedling.bean.SeedlingIntentFlagEnum
import com.oplus.pantanal.seedling.intent.IIntentResultCallBack
import com.oplus.pantanal.seedling.update.SeedlingCardOptions
import com.oplus.pantanal.seedling.util.SeedlingTool
import com.oplus.utils.ContinueUtils.Companion.registerListener
import com.oplus.utils.ContinueUtils.Companion.unregisterListener
import com.oplus.utils.DragonflyUtils
import com.oplus.utils.DragonflyUtils.ScreenListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicInteger

object TimerSeedlingHelper {
    /** 卡片数据*/
    const val METHOD_OPERATE = "operateTimer"
    const val METHOD_CANCEL = "cancelTimer"
    const val METHOD_RESTARTTIMER = "restartTimer"
    const val SERVICE_ID = "268451854"
    const val SERVICE_ID_EX = "268451943"
    const val DELAY_CHECK = 3 * 1000L
    const val PARAMS_STRING_PAGES_ID = "pages/index"
    const val DEFAULT_EMPTY_STRING = ""
    private const val JUMP_CLOCK_URI = "nativeapp://com.oplus.alarmclock.AlarmClock"

    /**
     * 开关数据获取到后转为2进制，第四位为状态栏开关状态
     */
    const val STATUS_BAR_INDEX = 4
    const val RESET_CLOSE_TIMER_SWITCH = 1

    /**
     * 计时器响铃上滑卡片会收到onDestory回调和关闭状态栏冲突
     * 关闭状态栏开关会收到onDestory回调且会有多次，需要过滤掉开关关闭后的回调，避免计时器停止响铃
     */
    const val RESET_CLOSE_TIMER_SWITCH_DELAY = 150L
    const val EVENT_REFRESH_TIMER_VIEW = "event_refresh_timer_view"
    private const val TAG = "TimerSeedlingHelper"
    private const val DEFAULT_TIMER_INDEX = "0"
    private const val DEFAULT_TIMER_NAME: String = "Timer"

    private const val SEEDLING_TIMER_ACTION = "pantanal.intent.business.app.system.TIMER"
    private const val TIMER_FONT_SIZE_LG = "lg-font"
    private const val TIMER_FONT_SIZE_MD = "md-font"
    private const val SEEDLING_FEATURE = "oplus.software.support_fluid_entry"
    private const val SMALL_CAPSULES_FEATURE = "com.oplus.software.support.mini_capsule"
    private const val KEY_MINI_CAPSULE_TIMER_SWITCH = "mini_capsule_timer_switch"
    private const val KEY_MINI_CAPSULE_SWITCH = "mini_capsule_switch"
    private const val MINI_CAPSULE_TIMER_SWITCH_OPEN = 1
    private const val MINI_CAPSULE_TIMER_SWITCH_DEFAULT = 1
    private const val PARAMS_STRING_INIT_DATA = "initData"
    private const val PARAMS_STRING_TIMER_RUNING = "timer_runing"
    private const val PARAMS_STRING_TIMER_RING = "timer_ring"
    private const val PARAMS_STRING_TIMER_INDEX = "timer_index"
    private const val PARAMS_STRING_TIMER_TIME = "timer_time"
    private const val PARAMS_STRING_TIMER_FONT_SIZE = "timer_font_size"
    private const val PARAMS_STRING_TIMER_TIME_HOUR = "timer_hour"
    private const val PARAMS_STRING_TIMER_BTTON_IMAGE = "timer_btton_image"
    private const val PARAMS_STRING_TIMER_TIME_SPLIT = "timer_split"
    private const val PARAMS_STRING_TIMER_TIME_MINUTE = "timer_minute"
    private const val PARAMS_STRING_TIMER_TIME_SECOND = "timer_second"
    private const val PARAMS_STRING_TIMER_OPERATE_CHECKED = "timer_operate_checked"

    /**
     * 胶囊是否播放动画
     */
    private const val PARAMS_STRING_TIMER_CAPSULE_PLAY = "timer_capsule_play"
    private const val PARAMS_STRING_TIMER_STATUS_DESCRIPTION = "timer_status_description"
    private const val PARAMS_STRING_TIMER_OPERATE_VOICE = "timer_operate_voice"
    private const val PARAMS_STRING_TIMER_OPERATE_METHOD = "timer_operate_method"
    private const val PARAMS_STRING_TIMER_DESCRIPTION = "timer_description"
    private const val PARAMS_STRING_TIMER_VOICE_STR = "voice_str"
    private const val PARAMS_STRING_TIMER_VOICE_CARD_STR = "voice_card_str"
    private const val PARAMS_STRING_PANEL_CLICK_URI = "panel_click_uri"
    private const val VALUE_STRING_TIMER_TIME_SPLIT = ":"
    private const val VALUE_STRING_TIMER_START_DESCRIPTION = "\$t('strings.start_description')"
    private const val VALUE_STRING_TIMER_PAUSE_DESCRIPTION = "\$t('strings.pause_description')"
    private const val VALUE_STRING_TIMER_START_VOICE = "\$t('strings.continue')"
    private const val VALUE_STRING_TIMER_PAUSE_VOICE = "\$t('strings.pause')"
    private const val VALUE_STRING_TIMER_FINISHED = "\$t('strings.timer_finished')"

    private const val TIMER_BOTTON_IMAGE_PASE = "\$r('images.icon_pause')"
    private const val TIMER_BOTTON_IMAGE_START = "\$r('images.icon_start')"
    private const val TIMER_BOTTON_IMAGE_RESTART = "\$r('images.icon_restart')"
    private const val METHOD_RESUME = "resumeTimer"
    private const val METHOD_PAUSE = "pauseTimer"
    private const val METHOD_RESTART = "restartTimer"
    private const val EXTRA_TIMER_OPERATE_METHOD = "timerOperateMethod"
    private const val EXTRA_TIMER_INDEX = "timerIndex"
    private const val EXTRA_IS_TIMER_RUNING = "isTimerRuning"

    private var mCurrentTimeIndex: Int = 0
    private var mCurrentTimeName: String? = DEFAULT_TIMER_NAME
    private var mCurrentTimeMsg: FluidCloudTimerEntity? = null

    /**
     * 卡片是否暂停
     */
    private var mTimerStart: Boolean = false

    /**
     * 卡片是否运行
     */
    private var mTimerRuning: Boolean = false

    /** 是否配置了seedling feature */
    private var isSeedlingFeature: Boolean = false

    /** 是否配置了realme小胶囊 feature */
    private var isCapsuleFeature: Boolean = false

    /** 是否支持流体云 */
    private var isSupportFluidCloud: Boolean = false

    /**
     * 是否支持多实例
     */
    private var isSupportMultiInstance: Boolean = false

    /** 是否是计时结束页面 */
    private var isFinishedPage: Boolean = false

    /**
     * 过滤开关关闭回调
     */
    private var canCloseTimer: Boolean = true

    /**
     * 计时器是否正在响铃
     */
    private var isTimerRing = false

    /**
     * 全景息屏监听
     */
    var timerFluidWakeLock: FluidWakeLockUtils? = null

    /**
     * 加载卡片状态
     * 0:default
     * 1:loading
     * 2:fail
     * 3:success
     */
    private var mSeedlingCardStatus: AtomicInteger = AtomicInteger(STATUS_LOADCARD_DEFAULT)

    private var mDragonflyUtils: DragonflyUtils? = null
    private var mLocalBroadcastManager: LocalBroadcastManager? = null
    private val mExportReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            i(TAG, "Received broadcast:$action")
            if ((Intent.ACTION_SCREEN_OFF == action) && Utils.screenChangedByPowerButton()) {
                i(TAG, "screen off by user return.")
                closeTimer(context)
            }
        }
    }
    private val mLocalReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val closeCard = intent.getBooleanExtra(STOP_TIMERALERT_EXTRA, true)
            i(TAG, "Received broadcast:$action closeCard:$closeCard")
            when (action) {
                TimerConstant.STOP_TIMERALERT -> closeTimer(context, closeCard)
                TimerConstant.STOP_ALERT,
                TimerConstant.TIMER_ALERT_TIMEOUT -> closeTimer(context)

                else -> {}
            }
        }
    }

    /**
     * 胶囊是否播放动画
     */
    private var timerCapsulePlay = true

    /**
     * 计时器名称
     */
    var mTimerName: String? = ""

    /**
     * 计时器动画执行倒计时
     */
    private val mTimerAnimation: TimerAnimationCountDown = object : TimerAnimationCountDown() {
        override fun onTimerTick() {
            timerCapsulePlay = true
        }

        override fun onTimerFinish() {
            timerCapsulePlay = false
        }
    }

    /**
     * 流体云状态同步
     * isSupportFluidCloud 是个耗时操作 && 返回的值可能会变
     * SeedlingFeature 管控机型
     * @param context
     */
    @JvmStatic
    fun asynSupportFluidCloud(context: Context) {
        isSeedlingFeature =
            OplusFeatureConfigManager.getInstance(context).hasFeature(SEEDLING_FEATURE)
        if (DeviceUtils.isRealmeBrand()) {
            isCapsuleFeature = AppFeatureProviderUtils.isFeatureSupport(
                context.contentResolver,
                SMALL_CAPSULES_FEATURE
            )
        }
        d(TAG, "isSeedlingFeature:$isSeedlingFeature, isCapsuleFeature:$isCapsuleFeature")
        GlobalScope.launch(Dispatchers.IO) {
            isSupportMultiInstance = SeedlingTool.isSupportMultiInstance(context)
            d(TAG, "isSupportMultiInstance:$isSupportMultiInstance")
            SeedlingTool.isSupportFluidCloud(context) {
                isSupportFluidCloud = it
                if (isSupportFluidCloud && !isSupportMultiInstance) {
                    //收到更新回调，重新获取
                    isSupportMultiInstance = SeedlingTool.isSupportMultiInstance(context)
                    isSeedlingFeature =
                        OplusFeatureConfigManager.getInstance(context).hasFeature(SEEDLING_FEATURE)
                }
                d(
                    TAG,
                    "isSupportFluidCloud:$it isSeedlingFeature:$isSeedlingFeature isSupportMultiInstance：$isSupportMultiInstance"
                )
            }
        }
    }

    /**
     * 是否支持流体云(有feature && sdk返回支持)
     */
    @JvmStatic
    fun isSupportFluidCloud(): Boolean {
        return isSeedlingFeature && isSupportFluidCloud
    }

    /**
     * 是否支持多实例
     */
    @JvmStatic
    fun isSupportMultiInstance(): Boolean {
        return isSupportMultiInstance
    }

    /**
     * 是否支持realme小胶囊(是realme && 有feature && 开关打开)
     * 小胶囊基于流体云通知做的,如果是realme就返回是否支持小胶囊和开关结果
     */
    @JvmStatic
    fun isSupportCapsuleFeature(): Boolean {
        return if (DeviceUtils.isRealmeBrand()) {
            isCapsuleFeature && isSupportCapsuleSettings()
        } else {
            false
        }
    }

    /**
     * 从设置读取小胶囊开关
     */
    @JvmStatic
    fun isSupportCapsuleSettings(): Boolean {
        return if (MINI_CAPSULE_TIMER_SWITCH_OPEN == Settings.System.getInt(
                AlarmClockApplication.getInstance().contentResolver,
                KEY_MINI_CAPSULE_SWITCH, MINI_CAPSULE_TIMER_SWITCH_DEFAULT
            )
        ) {
            MINI_CAPSULE_TIMER_SWITCH_OPEN == Settings.System.getInt(
                AlarmClockApplication.getInstance().contentResolver,
                KEY_MINI_CAPSULE_TIMER_SWITCH, MINI_CAPSULE_TIMER_SWITCH_DEFAULT
            )
        } else {
            false
        }
    }

    /**
     * 是否使用流体云样式（用于流体云1.0强提醒使用流体云样式）
     * 超省使用旧样式
     */
    @JvmStatic
    fun canUseFluidCloudStyle(context: Context?): Boolean {
        return isSupportFluidCloud() && !DeviceUtils.isSuperPowerSaveMode(context)
    }

    /**
     * 卡片是不是正在加载状态
     */
    @JvmStatic
    fun isSeedlingCardLoading(): Boolean {
        return mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING
    }

    /**
     * 卡片是不是加载失败(用于推送卡片/更新卡片数据)
     * 初始状态和成功状态是可以推送数据的
     * 加载中/加载失败不能推送数据
     */
    @JvmStatic
    fun isSeedlingCardLoadFail(): Boolean {
        return mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL
    }

    /**
     * 确保显示计时卡
     * @param timerIndex 计时序号
     * @param timerName
     * @param timeMsg 计时的时间msg
     * @param isTimerStart 是否正在计时
     * @param callBack 加载卡片是否成功的回调
     */
    @JvmStatic
    fun showTimerCardSecure(
        context: Context,
        timerIndex: Int,
        timerName: String?,
        timeMsg: FluidCloudTimerEntity,
        isTimerStart: Boolean,
        callBack: SeedCallback? = null
    ) {
        if ((!isSupportFluidCloud()
                    || DeviceUtils.isSuperPowerSaveMode(context)
                    || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL))
            && !isSupportCapsuleFeature()
        ) {
            callBack?.onLoadCardResult(false)
            return
        }
        if (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING) {
            d(TAG, "mSeedlingCardStatus is loading")
            return
        }
        isTimerRing = false
        if (mSeedlingCardStatus.get() == STATUS_LOADCARD_SUCCESS) {
            updateTimerCard(context, true, timerIndex, timerName, timeMsg, isTimerStart)
        } else {
            showTimerCard(context, true, timerIndex, timerName, timeMsg, isTimerStart, callBack)
        }
    }

    private fun isShowFinishPageSecure(): Boolean {
        return !VersionUtils.isOsVersion15()
                || !isSupportFluidCloud()
                || DeviceUtils.isSuperPowerSaveMode(AlarmClockApplication.getInstance())
                || (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING)
                || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
    }

    /**
     * 确保显示计时完成
     * OS15以前不支持强提醒，直接返回
     * @param context
     * @param timerIndex
     * @param timerName
     * @param callBack
     */
    @JvmStatic
    fun showFinishPageSecure(
        context: Context,
        timerIndex: Int,
        timerName: String?,
        callBack: SeedCallback?
    ) {
        if (isShowFinishPageSecure() && !isSupportCapsuleFeature()) {
            d(TAG, "showFinishPageSecure false")
            callBack?.onLoadCardResult(false)
            return
        }
        d(TAG, "showFinishPageSecure")
        if (isSubUser()) {
            d(TAG, "subUser timer ring")
            //子用户下响铃展示老通知
            callBack?.onLoadCardResult(false)
            return
        }
        isTimerRing = true
        isFinishedPage = true
        registerBroadCast(context)
        mDragonflyUtils = registerListener(context, mDragonflyUtils, object : ScreenListener {
            override fun onScreenChange(isSmallScreen: Boolean) {
                closeTimer(context)
            }
        })
        if (mSeedlingCardStatus.get() == STATUS_LOADCARD_SUCCESS) {
            callBack?.onLoadCardResult(true)
            updateTimerCard(context, false, timerIndex, timerName, null, false)
        } else {
            showTimerCard(context, false, timerIndex, timerName, null, false, callBack)
        }
    }

    /**
     * 关闭计时提醒
     * 流体云2.0之前不支持流体云卡，此方法调用不到
     */
    @JvmStatic
    fun closeTimer(context: Context, closeCard: Boolean = true) {
        d(TAG, "isFinishedPage:$isFinishedPage canCloseTimer:$canCloseTimer")
        if ((mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_DEFAULT)
        ) {
            return
        }
        d(TAG, "closeTimer")
        if (isFinishedPage) {
            context.stopService(Intent(context, TimerKlaxon::class.java))
            TimerFloatingViewService.stopTimer(context)
            if (closeCard) {
                closeSeedlingCard(context)
            }
            //取消持续亮屏
            TimerWakeLock.releaseCpuLockFull()
        } else {
            resetHelper()
        }
    }

    /**
     *  关闭流体云卡
     *  流体云2.0(OS15)开始支持强提醒，2.0版本在倒计时结束的时候，无需关闭流体云
     *  @param context
     */
    @JvmStatic
    fun closeSeedlingCardByVersionControl(context: Context) {
        if (VersionUtils.isOsVersion15()) {
            return
        }
        d(TAG, "closeSeedlingCardByUpkVersion")
        closeSeedlingCard(context)
    }

    /**
     *  关闭流体云卡
     *  隐藏卡的时候要反注册callback,因多处调用隐藏方法,context来源不一样,所以这里使用applicationcontext
     *  @param context
     */
    @JvmStatic
    fun closeSeedlingCard(context: Context) {
        if (!isSupportFluidCloud
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
            || (mSeedlingCardStatus.get() == STATUS_LOADCARD_DEFAULT)
        ) {
            return
        }
        d(TAG, "closeSeedlingCard")
        timerFluidWakeLock?.apply {
            releasePartialWakeLock()
            unRegisterPanoramicAOD()
        }
        resetHelper()
        mTimerAnimation.cancel()
        SeedlingTool.sendSeedling(
            context,
            SeedlingIntent(action = SEEDLING_TIMER_ACTION, flag = SeedlingIntentFlagEnum.END)
        )
        mDragonflyUtils?.apply {
            d(TAG, "unregisterListener")
            unregisterListener(context, this)
            mDragonflyUtils = null
        }
        AlarmRingVolumeUtils.unregisterKeyEvent(context)
        //取消监听流体云开关
        timerFluidObserver?.run {
            context.contentResolver.unregisterContentObserver(this)
            timerFluidObserver = null
        }
        kotlin.runCatching {
            mLocalBroadcastManager?.apply {
                unregisterReceiver(mLocalReceiver)
                mLocalBroadcastManager = null
            }
            context.unregisterReceiver(mExportReceiver)
        }.onFailure { e(TAG, "unregisterReceiver error") }
    }

    /**
     * 重置helper
     */
    @JvmStatic
    fun resetHelper() {
        d(TAG, "resetHelper")
        isTimerRing = false
        isFinishedPage = false
        mSeedlingCardStatus.set(STATUS_LOADCARD_DEFAULT)
        statusBarSeedlingCard = null
        ClockSeedlingCardProvider.cardList.clear()
    }

    /**
     * 显示计时卡
     * @param context 上下文
     * @param isTimerRuning 是否计时中
     * @param timerIndex 计时index
     * @param timerName 计时名称
     * @param timeEntity 计时内容
     * @param isTimerStart 是否正在计时
     * @param callBack 状态回调
     */
    @JvmStatic
    private fun showTimerCard(
        context: Context,
        isTimerRuning: Boolean,
        timerIndex: Int,
        timerName: String?,
        timeEntity: FluidCloudTimerEntity?,
        isTimerStart: Boolean,
        callBack: SeedCallback?
    ) {
        d(TAG, "showTimerCard timeMsg:$timeEntity")
        mCurrentTimeIndex = timerIndex
        mCurrentTimeName = timerName
        mCurrentTimeMsg = timeEntity
        mTimerStart = isTimerStart
        mTimerRuning = isTimerRuning
        val businessData = JSONObject().apply {
            put(PARAMS_STRING_TIMER_RUNING, isTimerRuning)
            put(PARAMS_STRING_TIMER_RING, !isTimerRuning)
            if (isTimerRuning) {
                put(PARAMS_STRING_TIMER_INDEX, timerIndex)
                put(PARAMS_STRING_TIMER_TIME, timeEntity?.timerMsg)
                val isScreenLg =
                    FoldScreenUtils.screenDisplayModel() == FoldScreenUtils.SCREEN_DISPLAY_LARGE
                put(
                    PARAMS_STRING_TIMER_FONT_SIZE,
                    if (isScreenLg) TIMER_FONT_SIZE_MD else TIMER_FONT_SIZE_LG
                )
                put(PARAMS_STRING_TIMER_DESCRIPTION, timerName)
                put(PARAMS_STRING_TIMER_VOICE_STR, timeEntity?.voiceStr)
                put(PARAMS_STRING_TIMER_VOICE_CARD_STR, timeEntity?.voiceCardStr)

                put(
                    PARAMS_STRING_TIMER_TIME_HOUR,
                    if (timeEntity?.includeHours == true) timeEntity.hour else ""
                )
                put(
                    PARAMS_STRING_TIMER_BTTON_IMAGE,
                    if (isTimerStart) TIMER_BOTTON_IMAGE_PASE else TIMER_BOTTON_IMAGE_START
                )
                put(
                    PARAMS_STRING_TIMER_TIME_MINUTE,
                    if (timeEntity?.includeHours == true) "" else timeEntity?.minute
                )
                put(
                    PARAMS_STRING_TIMER_TIME_SPLIT,
                    if (timeEntity?.includeHours == true) "" else VALUE_STRING_TIMER_TIME_SPLIT
                )
                put(
                    PARAMS_STRING_TIMER_TIME_SECOND,
                    if (timeEntity?.includeHours == true) "" else timeEntity?.second
                )
                put(PARAMS_STRING_TIMER_OPERATE_CHECKED, isTimerStart)
                if (isTimerStart) {
                    if (timeEntity != null && timeEntity.thanTenMinute) {
                        timerCapsulePlay = true
                        put(PARAMS_STRING_TIMER_CAPSULE_PLAY, true)
                    } else {
                        put(PARAMS_STRING_TIMER_CAPSULE_PLAY, timerCapsulePlay)
                    }
                } else {
                    put(PARAMS_STRING_TIMER_CAPSULE_PLAY, false)
                }
                put(
                    PARAMS_STRING_TIMER_STATUS_DESCRIPTION,
                    if (isTimerStart) VALUE_STRING_TIMER_START_DESCRIPTION else VALUE_STRING_TIMER_PAUSE_DESCRIPTION
                )
                put(
                    PARAMS_STRING_TIMER_OPERATE_VOICE,
                    if (isTimerStart) VALUE_STRING_TIMER_PAUSE_VOICE else VALUE_STRING_TIMER_START_VOICE
                )
                put(
                    PARAMS_STRING_TIMER_OPERATE_METHOD,
                    if (isTimerStart) METHOD_PAUSE else METHOD_RESUME
                )
            } else {
                put(PARAMS_STRING_TIMER_TIME, VALUE_STRING_TIMER_FINISHED)
                put(PARAMS_STRING_TIMER_TIME_HOUR, VALUE_STRING_TIMER_FINISHED)
                put(PARAMS_STRING_TIMER_TIME_MINUTE, "")
                put(PARAMS_STRING_TIMER_TIME_SPLIT, "")
                put(PARAMS_STRING_TIMER_TIME_SECOND, "")
                put(
                    PARAMS_STRING_TIMER_DESCRIPTION,
                    timerName + " • " + getTimeMsgByFluidCloud(context)
                )
                put(
                    PARAMS_STRING_TIMER_OPERATE_VOICE,
                    AlarmClockApplication.getInstance().getString(R.string.repeat)
                )
                put(
                    PARAMS_STRING_TIMER_VOICE_CARD_STR,
                    AlarmClockApplication.getInstance().getString(R.string.timer_finished)
                )
                put(PARAMS_STRING_TIMER_OPERATE_METHOD, METHOD_RESTART)
                put(PARAMS_STRING_TIMER_BTTON_IMAGE, TIMER_BOTTON_IMAGE_RESTART)
                put(PARAMS_STRING_PANEL_CLICK_URI, DEFAULT_EMPTY_STRING)
            }
        }
        val businessInitData = JSONObject().apply {
            put(PARAMS_STRING_INIT_DATA, businessData)
        }

        val options = if (timeEntity == null) {
            getSeedlingCardOptions(isTimerRuning, false)
        } else {
            timeEntity.thanHour.let { getSeedlingCardOptions(isTimerRuning, it) }
        }
        SeedlingTool.sendSeedling(
            context, SeedlingIntent(
                action = SEEDLING_TIMER_ACTION,
                flag = SeedlingIntentFlagEnum.START,
                data = businessData,
                options = businessInitData,
                cardOptions = options
            ),
            callBack = object : IIntentResultCallBack {
                override fun onIntentResultCodeCallBack(
                    action: String,
                    flag: Int,
                    resultCode: Int
                ) {
                    if (SEEDLING_TIMER_ACTION == action) {
                        mSeedlingCardStatus.updateAndGet {
                            d(TAG, "onIntentResult resultCode:$resultCode")
                            val isSuccess =
                                (resultCode == SeedlingTool.DECISION_RESULT_SUCCEED) || (resultCode == SeedlingTool.DECISION_RESULT_REPEATED_ACTION)
                            callBack?.onLoadCardResult(isSuccess)
                            if (isSuccess) {
                                STATUS_LOADCARD_SUCCESS
                            } else {
                                STATUS_LOADCARD_FAIL
                            }
                        }
                    }
                }
            })
        mSeedlingCardStatus.set(STATUS_LOADCARD_LOADING)
        CoroutineScope(Dispatchers.IO).launch {
            d(TAG, "DELAY_CHECK")
            delay(DELAY_CHECK)
            withContext(Dispatchers.Main) {
                d(TAG, "mSeedlingCardStatus.get() :" + mSeedlingCardStatus.get())
                if (mSeedlingCardStatus.get() == STATUS_LOADCARD_SUCCESS) {
                    return@withContext
                }
                mSeedlingCardStatus.getAndUpdate {
                    if (it == STATUS_LOADCARD_LOADING) {
                        callBack?.onLoadCardResult(false)
                        STATUS_LOADCARD_FAIL
                    } else {
                        it
                    }
                }
            }
        }
        timerFluidWakeLock = FluidWakeLockUtils(TAG, context)
        timerFluidWakeLock?.registerPanoramicAOD(context)
    }

    /**
     * 发送流体云数据
     * @param context 上下文
     * @param isTimerRuning 是否计时中
     * @param timerIndex 计时index
     * @param timerName 计时名称
     * @param timeEntity 计时内容
     * @param isTimerStart 是否正在计时
     */
    @JvmStatic
    private fun updateTimerCard(
        context: Context,
        isTimerRuning: Boolean,
        timerIndex: Int,
        timerName: String?,
        timeEntity: FluidCloudTimerEntity?,
        isTimerStart: Boolean
    ) {
        if (isInvalidSend(timerIndex, timeEntity?.timerMsg, isTimerStart)) {
            d(TAG, "is repeat data")
            return
        }
        mCurrentTimeIndex = timerIndex
        mCurrentTimeName = timerName
        mCurrentTimeMsg = timeEntity
        mTimerStart = isTimerStart
        mTimerRuning = isTimerRuning
        d(
            TAG,
            "updateTimerCard timeEntity:$timeEntity isTimerRuning:$isTimerRuning isTimerStart$isTimerStart"
        )
        val businessData = JSONObject().apply {
            put(PARAMS_STRING_TIMER_RUNING, isTimerRuning)
            put(PARAMS_STRING_TIMER_RING, !isTimerRuning)
            if (isTimerRuning) {
                put(PARAMS_STRING_TIMER_INDEX, timerIndex)
                put(PARAMS_STRING_TIMER_TIME, timeEntity?.timerMsg)
                val isScreenLg =
                    FoldScreenUtils.screenDisplayModel() == FoldScreenUtils.SCREEN_DISPLAY_LARGE
                put(
                    PARAMS_STRING_TIMER_FONT_SIZE,
                    if (isScreenLg) TIMER_FONT_SIZE_MD else TIMER_FONT_SIZE_LG
                )
                put(PARAMS_STRING_TIMER_DESCRIPTION, timerName)
                put(PARAMS_STRING_TIMER_VOICE_STR, timeEntity?.voiceStr)
                put(PARAMS_STRING_TIMER_VOICE_CARD_STR, timeEntity?.voiceCardStr)
                put(
                    PARAMS_STRING_TIMER_TIME_HOUR,
                    if (timeEntity?.includeHours == true) timeEntity.hour else ""
                )
                put(
                    PARAMS_STRING_TIMER_TIME_MINUTE,
                    if (timeEntity?.includeHours == true) "" else timeEntity?.minute
                )
                put(
                    PARAMS_STRING_TIMER_TIME_SPLIT,
                    if (timeEntity?.includeHours == true) "" else VALUE_STRING_TIMER_TIME_SPLIT
                )
                put(
                    PARAMS_STRING_TIMER_TIME_SECOND,
                    if (timeEntity?.includeHours == true) "" else timeEntity?.second
                )
                put(PARAMS_STRING_TIMER_OPERATE_CHECKED, isTimerStart)
                if (isTimerStart) {
                    if (timeEntity != null && timeEntity.thanTenMinute) {
                        timerCapsulePlay = true
                        put(PARAMS_STRING_TIMER_CAPSULE_PLAY, true)
                    } else {
                        put(PARAMS_STRING_TIMER_CAPSULE_PLAY, timerCapsulePlay)
                    }
                } else {
                    put(PARAMS_STRING_TIMER_CAPSULE_PLAY, false)
                }
                put(
                    PARAMS_STRING_TIMER_STATUS_DESCRIPTION,
                    if (isTimerStart) VALUE_STRING_TIMER_START_DESCRIPTION else VALUE_STRING_TIMER_PAUSE_DESCRIPTION
                )
                put(
                    PARAMS_STRING_TIMER_OPERATE_VOICE,
                    if (isTimerStart) VALUE_STRING_TIMER_PAUSE_VOICE else VALUE_STRING_TIMER_START_VOICE
                )
                put(
                    PARAMS_STRING_TIMER_OPERATE_METHOD,
                    if (isTimerStart) METHOD_PAUSE else METHOD_RESUME
                )
                put(
                    PARAMS_STRING_TIMER_BTTON_IMAGE,
                    if (isTimerStart) TIMER_BOTTON_IMAGE_PASE else TIMER_BOTTON_IMAGE_START
                )
                put(PARAMS_STRING_PANEL_CLICK_URI, JUMP_CLOCK_URI)
            } else {
                put(PARAMS_STRING_TIMER_TIME, VALUE_STRING_TIMER_FINISHED)
                put(PARAMS_STRING_TIMER_TIME_HOUR, VALUE_STRING_TIMER_FINISHED)
                put(PARAMS_STRING_TIMER_OPERATE_METHOD, METHOD_RESTART)
                put(PARAMS_STRING_TIMER_BTTON_IMAGE, TIMER_BOTTON_IMAGE_RESTART)
                put(PARAMS_STRING_TIMER_TIME_MINUTE, "")
                put(PARAMS_STRING_TIMER_TIME_SPLIT, "")
                put(PARAMS_STRING_TIMER_TIME_SECOND, "")
                put(
                    PARAMS_STRING_TIMER_OPERATE_VOICE,
                    AlarmClockApplication.getInstance().getString(R.string.repeat)
                )
                put(
                    PARAMS_STRING_TIMER_VOICE_CARD_STR,
                    AlarmClockApplication.getInstance().getString(R.string.timer_finished)
                )
                put(
                    PARAMS_STRING_TIMER_DESCRIPTION,
                    timerName + " • " + getTimeMsgByFluidCloud(context)
                )
                put(PARAMS_STRING_PANEL_CLICK_URI, DEFAULT_EMPTY_STRING)
            }
        }
        if (timeEntity == null) {
            sendDataToSeedlingCard(context, isTimerRuning, businessData, false)
        } else {
            timeEntity?.thanHour?.let {
                sendDataToSeedlingCard(
                    context,
                    isTimerRuning,
                    businessData,
                    it
                )
            }
        }
    }

    /**
     * 改变按钮到继续计时
     * @param context
     */
    @JvmStatic
    private fun changeButtonToResume(context: Context) {
        val businessData = JSONObject().apply {
            put(PARAMS_STRING_TIMER_RUNING, true)
            put(PARAMS_STRING_TIMER_RING, false)
            put(PARAMS_STRING_TIMER_INDEX, mCurrentTimeIndex)
            val isScreenLg =
                FoldScreenUtils.screenDisplayModel() == FoldScreenUtils.SCREEN_DISPLAY_LARGE
            put(
                PARAMS_STRING_TIMER_FONT_SIZE,
                if (isScreenLg) TIMER_FONT_SIZE_MD else TIMER_FONT_SIZE_LG
            )
            put(PARAMS_STRING_TIMER_TIME, mCurrentTimeMsg?.timerMsg)
            put(PARAMS_STRING_TIMER_DESCRIPTION, mCurrentTimeName)
            put(PARAMS_STRING_TIMER_VOICE_STR, mCurrentTimeMsg?.voiceStr)
            put(PARAMS_STRING_TIMER_VOICE_CARD_STR, mCurrentTimeMsg?.voiceCardStr)
            put(
                PARAMS_STRING_TIMER_TIME_HOUR,
                if (mCurrentTimeMsg?.includeHours == true) mCurrentTimeMsg?.hour else ""
            )
            put(
                PARAMS_STRING_TIMER_TIME_SPLIT,
                if (mCurrentTimeMsg?.includeHours == true) "" else VALUE_STRING_TIMER_TIME_SPLIT
            )
            put(
                PARAMS_STRING_TIMER_TIME_MINUTE,
                if (mCurrentTimeMsg?.includeHours == true) "" else mCurrentTimeMsg?.minute
            )
            put(
                PARAMS_STRING_TIMER_TIME_SECOND,
                if (mCurrentTimeMsg?.includeHours == true) "" else mCurrentTimeMsg?.second
            )

            put(PARAMS_STRING_TIMER_OPERATE_CHECKED, true)
            put(PARAMS_STRING_TIMER_CAPSULE_PLAY, timerCapsulePlay)
            put(PARAMS_STRING_TIMER_STATUS_DESCRIPTION, VALUE_STRING_TIMER_START_DESCRIPTION)
            put(PARAMS_STRING_TIMER_OPERATE_VOICE, VALUE_STRING_TIMER_PAUSE_VOICE)
            put(PARAMS_STRING_TIMER_OPERATE_METHOD, METHOD_PAUSE)
            put(PARAMS_STRING_TIMER_BTTON_IMAGE, TIMER_BOTTON_IMAGE_PASE)
        }
        mCurrentTimeMsg?.thanHour?.let { sendDataToSeedlingCard(context, true, businessData, it) }
    }

    /**
     * 获取cardOptions
     * @param isTimerRuning 是否正在计时
     */
    @JvmStatic
    private fun getSeedlingCardOptions(isTimerRuning: Boolean, hour: Boolean): SeedlingCardOptions {
        return SeedlingCardOptions().apply {
            pageId = PARAMS_STRING_PAGES_ID
            isMilestone = true
            if (hour) {
                grade = SeedlingCardOptions.GRADE_1
            } else {
                grade = SeedlingCardOptions.GRADE_4
            }
            lockScreenShowHostMap = mapOf(SeedlingHostEnum.StatusBar to false)
            if (hour) {
                showHostMap = mapOf(SeedlingHostEnum.StatusBar to false)
            } else {
                showHostMap = mapOf(SeedlingHostEnum.StatusBar to true)
            }
            if (isTimerRuning) {
                remindType = SeedlingCardOptions.REMIND_TYPE_NORMAL
                panelActionConfigMap = mapOf(
                    PanelActionEnum.PANEL_SLIDE to CancelPanelActionConfigEnum.Retract,
                    PanelActionEnum.OUTSIDE_CLICK to CancelPanelActionConfigEnum.Retract
                )
            } else {
                remindType = SeedlingCardOptions.REMIND_TYPE_STRONG_ALWAYS
                panelActionConfigMap = mapOf(
                    PanelActionEnum.PANEL_SLIDE to CancelPanelActionConfigEnum.Disappear,
                    PanelActionEnum.OUTSIDE_CLICK to CancelPanelActionConfigEnum.NoAction
                )
            }
        }
    }

    private fun isUpdateCardTimerData(): Boolean {
        return !isSupportFluidCloud()
                || (mSeedlingCardStatus.get() == STATUS_LOADCARD_LOADING)
                || (mSeedlingCardStatus.get() == STATUS_LOADCARD_FAIL)
                || mTimerStart
    }

    /**
     * 特殊情况下计时器卡片被销毁后重新创建需要更新计时器卡片数据
     */
    @JvmStatic
    fun updateCardTimerData(context: Context) {
        if (isUpdateCardTimerData() && !isSupportCapsuleFeature()) {
            return
        }
        d(TAG, "updateCardTimerData mCurrentTimeMsg:$mCurrentTimeMsg")
        mSeedlingCardStatus.set(STATUS_LOADCARD_SUCCESS)
        CoroutineScope(Dispatchers.IO).launch {
            updateTimerCard(
                context,
                mTimerRuning,
                mCurrentTimeIndex,
                mCurrentTimeName,
                mCurrentTimeMsg,
                mTimerStart
            )
        }
    }

    /**
     * 发送数据到流体云卡
     * @param businessData 卡片数据
     */
    @JvmStatic
    private fun sendDataToSeedlingCard(
        context: Context,
        isTimerRuning: Boolean,
        businessData: JSONObject,
        hour: Boolean
    ) {
        val seedlingCardId = PrefUtils.getString(
            context,
            ClockConstant.SEEDLING_PREF_FILE_NAME,
            ClockConstant.KEY_SEEDLING_CARD_ID,
            DEFAULT_EMPTY_STRING
        )
        d(TAG, "sendDataToSeedlingCard: $seedlingCardId" + "hour: " + hour)
        if (!TextUtils.isEmpty(seedlingCardId)) {
            if (hour) {
                //更新状态栏卡片
                statusBarSeedlingCard?.let {
                    SeedlingTool.updateData(
                        it,
                        businessData,
                        getSeedlingCardOptions(isTimerRuning, true)
                    )
                }
                //更新通知栏数据
                for (seedingCard in ClockSeedlingCardProvider.cardList) {
                    SeedlingTool.updateData(
                        seedingCard,
                        businessData,
                        getSeedlingCardOptions(isTimerRuning, false)
                    )
                }
            } else {
                SeedlingTool.updateAllCardData(
                    SeedlingCard.build(seedlingCardId),
                    businessData,
                    getSeedlingCardOptions(isTimerRuning, false)
                )
            }
            if (!hour && mTimerAnimation.mTimerAnimationTimer == null) {
                mTimerAnimation.startTimer()
            }
        }
    }

    /**
     * 是否是非法推送(过滤毫秒级别推送)
     * @param timerIndex 计时index
     * @param timeMsg 计时msg
     * @param isTimerStart 是否是正在计时
     */
    @JvmStatic
    private fun isInvalidSend(timerIndex: Int, timeMsg: String?, isTimerStart: Boolean): Boolean {
        return (mCurrentTimeIndex == timerIndex) && (timeMsg == mCurrentTimeMsg?.timerMsg) && isTimerStart
    }

    /**
     * 监听计时相关广播
     */
    @JvmStatic
    private fun registerBroadCast(context: Context) {
        val filterLocal = IntentFilter()
        filterLocal.addAction(TimerConstant.STOP_TIMERALERT)
        filterLocal.addAction(TimerConstant.STOP_ALERT)
        filterLocal.addAction(TimerConstant.TIMER_ALERT_TIMEOUT)
        val filterExport = IntentFilter(Intent.ACTION_SCREEN_OFF)
        if (mLocalBroadcastManager == null) {
            mLocalBroadcastManager =
                LocalBroadcastManager.getInstance(AlarmClockApplication.getInstance())
        }
        kotlin.runCatching {
            mLocalBroadcastManager?.registerReceiver(mLocalReceiver, filterLocal)
            AlarmRingVolumeUtils.registerKeyEvent(context, false)
            context.registerReceiver(
                mExportReceiver,
                filterExport,
                ClockConstant.OPLUS_SAFE_PERMISSION,
                null,
                RECEIVER_EXPORTED
            )
        }.onFailure { e(TAG, "registerReceiver error") }
    }

    /**
     * 处理卡片上的操作逻辑
     * @param context
     * @param action 卡片操作action
     * @param timerIndex timerIndex
     */
    @JvmStatic
    private fun operateTimerByAction(context: Context, action: String, timerIndex: Int) {
        val operateIntent = Intent(context, TimerNotificationReceiver::class.java)
        operateIntent.action = action
        operateIntent.putExtra(TimerNotificationManager.KEY_TIMER_INDEX, timerIndex)
        context.sendBroadcast(operateIntent)
    }

    /**
     * 处理方法回调
     * @param context
     * @param method
     * @param extras
     */
    @JvmStatic
    fun handleMethodCalls(context: Context, method: String, extras: Bundle?) {
        if ((extras == null)) {
            d(TAG, "extras is empty. return")
            return
        }

        val timerIndex = extras.getString(EXTRA_TIMER_INDEX, DEFAULT_TIMER_INDEX)
        when (method) {
            METHOD_OPERATE -> {
                val timerOperateMethod = extras.getString(EXTRA_TIMER_OPERATE_METHOD)
                d(TAG, "timerOperateMethod:$timerOperateMethod")
                if (METHOD_RESUME == timerOperateMethod) {
                    changeButtonToResume(context)
                    operateTimerByAction(
                        context,
                        TimerNotificationReceiver.TIMER_RESUME_TIMER,
                        timerIndex.toInt()
                    )
                } else if (METHOD_PAUSE == timerOperateMethod) {
                    operateTimerByAction(
                        context,
                        TimerNotificationReceiver.TIMER_PAUSE_TIMER,
                        timerIndex.toInt()
                    )
                } else if (METHOD_RESTART == timerOperateMethod) {
                    //计时器再来一次
                    closeTimer(context, false)
                    operateTimerByAction(
                        context,
                        TimerNotificationReceiver.TIMER_START_TIMER,
                        timerIndex.toInt()
                    )
                }
            }

            METHOD_CANCEL -> {
                Log.d(TAG, "isTimerRing:$isTimerRing")
                if (!isTimerRing) {
                    operateTimerByAction(
                        context,
                        TimerNotificationReceiver.TIMER_STOP_TIMER,
                        timerIndex.toInt()
                    )
                } else {
                    closeTimer(context)
                }
            }
        }
    }


    /**
     * 计时器流体云开关监听
     */
    var timerFluidObserver: ContentObserver? = null

    /**
     * 状态栏子开关子开关
     */
    var timerCloseEntry = false

    /**
     * handler回调，收到后重置开关参数
     */
    private val resetHandler: Handler = Handler(Looper.getMainLooper()) {
        if (RESET_CLOSE_TIMER_SWITCH == it.what) {
            canCloseTimer = true
        }
        false
    }

    fun interface SeedCallback {
        fun onLoadCardResult(isSuccess: Boolean)
    }

    /**
     * 查询计时器流体云serviceId
     */
    @JvmStatic
    fun queryTimerFluidServiceId(context: Context): String {
        val serviceId: String =
            if (DeviceUtils.isExpVersion(context) && ChannelManager.getChannelUtils()
                    .getChannel() == IBaseChannel.CHANNEL_WPLUS
            ) {
                SERVICE_ID_EX
            } else {
                SERVICE_ID
            }
        d(TAG, "queryTimerFluidServiceId: $serviceId")
        return serviceId
    }
}