/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.Message;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alert.AlarmFloatingWindowManager;
import com.oplus.alarmclock.alert.AlarmFloatingWindowView;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.osdk.IntentNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.ContinueUtils;
import com.oplus.utils.DragonflyUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static android.content.Context.RECEIVER_EXPORTED;

public class TimerFloatingWindowViewWrapper extends AlarmFloatingWindowView {
    private static final String TAG = "TimerFloatingWindowViewWrapper";
    private static final boolean DEBUG = true;

    private final AlarmFloatingWindowManager mTimerFloatingWindowManager;
    private final TimerTaskHandler mHandler;
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "Received broadcast:" + action);
            if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                if (Utils.screenChangedByPowerButton()) {
                    Log.i(TAG, "screen off by user return.");
                    if (null != mHandler) {
                        mHandler.removeMessages(TimerTaskHandler.MSG_STOP_TIMER);
                        mHandler.sendEmptyMessage(TimerTaskHandler.MSG_STOP_TIMER);
                    }
                }
            }
        }

    };
    private final BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "Received broadcast:" + action);
            switch (action) {
                case TimerConstant.STOP_TIMERALERT:
                    hideFloatingWindowFromAlarmAlert();
                    dismiss();
                    if (mHandler != null) {
                        mHandler.removeMessages(TimerTaskHandler.MSG_STOP_TIMER);
                    }
                    break;
                case TimerConstant.STOP_ALERT: {
                    if (null != mHandler) {
                        mHandler.removeMessages(TimerTaskHandler.MSG_STOP_TIMER);
                        mHandler.sendEmptyMessage(TimerTaskHandler.MSG_STOP_TIMER);
                    }
                    break;
                }
                case TimerConstant.TIMER_ALERT_TIMEOUT:
                    hideFloatingWindow();
                    dismiss();
                    if (mHandler != null) {
                        mHandler.removeMessages(TimerTaskHandler.MSG_STOP_TIMER);
                    }
                    break;
                default:
                    break;
            }
        }

    };
    private LocalBroadcastManager mLocalBroadcastManager;
    private boolean mIsRegister = false;
    private DragonflyUtils mDragonflyUtils;

    public TimerFloatingWindowViewWrapper(Context context, int orientation, String timerName,
                                          android.view.WindowManager.LayoutParams param,
                                          AlarmFloatingWindowManager alarmFloatingWindowManager, boolean isNotCTS) {
        super(context, orientation, false, isNotCTS ? TimerAlertUtilsKt.getTimeMsgByCondition(context) : "",
                timerName, param, false, AlarmFloatingWindowView.FLOATING_VIEW_TYPE_TIMER, false, isNotCTS);
        mContext = context;
        mTimerFloatingWindowManager = alarmFloatingWindowManager;

        // Register the broadcast receiver
        final IntentFilter filter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
        final IntentFilter filterLocal = new IntentFilter();
        filterLocal.addAction(TimerConstant.STOP_TIMERALERT);
        filterLocal.addAction(TimerConstant.STOP_ALERT);
        filterLocal.addAction(TimerConstant.TIMER_ALERT_TIMEOUT);
        if (!mIsRegister) {
            mLocalBroadcastManager = LocalBroadcastManager.getInstance(mContext);
            mLocalBroadcastManager.registerReceiver(mLocalReceiver, filterLocal);
            mContext.registerReceiver(mReceiver, filter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
            mIsRegister = true;
        }

        mHandler = new TimerTaskHandler();

        setBroadcastToOthers();
        mDragonflyUtils = ContinueUtils.registerListener(context, mDragonflyUtils, isSmallScreen -> continueActivity(context));
    }


    private void setBroadcastToOthers() {
        final Intent intentStopAlarm = new Intent(TimerService.STOP_ALARM_ACTION);
        mLocalBroadcastManager.sendBroadcast(intentStopAlarm);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        if (DEBUG) {
            Log.v(TAG, "onConfigurationChanged newConfig = " + newConfig + " ori = "
                    + newConfig.orientation);
        }
        switch (newConfig.orientation) {
            case Configuration.ORIENTATION_LANDSCAPE:
            case Configuration.ORIENTATION_PORTRAIT:
                shouldToReCreateView(newConfig.orientation);
                break;
            default:
                break;
        }
        super.onConfigurationChanged(newConfig);
    }

    void shouldToReCreateView(int orientation) {
        if ((mAnimatingState == ANIM_STATE_IDLE) && (!mIrremovable)) {
            if (DeviceUtils.isAbnormalScreen(mContext)) {
                updateWindowView(mTimerFloatingWindowManager.getWindowParams());
            }
            recreateView(orientation);
        }
    }

    @Override
    public void updateView(String name, String time) {
        updateAlarmSchedule(name, time);
        setAlarmTimeLabel(name, time);
    }

    @Override
    protected void hideFloatingWindow() {
        mTimerFloatingWindowManager.hideFloatingWindow();
    }

    protected void hideFloatingWindowFromAlarmAlert() {
        mTimerFloatingWindowManager.hideFloatingWindowFromAlarmAlert();
    }

    public void hideFloatingWindowWithAction(int action, int type) {
        if (mContext != null && ACTION_RESTART == action) {
            Intent intent = new Intent(mContext, TimerNotificationReceiver.class);
            intent.setAction(TimerNotificationReceiver.TIMER_START_TIMER);
            intent.putExtra(TimerNotificationManager.KEY_TIMER_INDEX, 0);
            mContext.sendBroadcast(intent);
        }
        if (null != mHandler) {
            mHandler.removeMessages(TimerTaskHandler.MSG_STOP_TIMER);
            mHandler.sendEmptyMessage(TimerTaskHandler.MSG_STOP_TIMER);
        }
    }

    @Override
    protected void dismiss() {
        sendMessageToDismiss();
    }

    @Override
    protected void snooze() {
        // do nothing
    }

    private void sendMessageToDismiss() {
        mContext.stopService(new Intent(mContext, TimerKlaxon.class));
        TimerFloatingViewService.stopTimer(mContext);
    }

    @Override
    public void unregisterReceiver() {
        if (mIsRegister) {
            mLocalBroadcastManager.unregisterReceiver(mLocalReceiver);
            mContext.unregisterReceiver(mReceiver);
            mIsRegister = false;
        }
        ContinueUtils.unregisterListener(mContext, mDragonflyUtils);
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    @Override
    protected void onVolumeAndCameraKeyPressed() {
        Intent intent = new Intent(TimerAlertReceiver.ACTION_STOP_TIMER_RING);
        intent.addFlags(IntentNativeUtils.getFLAG_RECEIVER_INCLUDE_BACKGROUND());
        mContext.sendBroadcast(intent);
        mContext.stopService(new Intent(mContext, TimerKlaxon.class));
    }

    @Override
    protected void removeFloatingWindowByFluidCloud() {
        mTimerFloatingWindowManager.removeViewForWindowManager();
    }

    public class TimerTaskHandler extends Handler {
        public static final int MSG_STOP_TIMER = 1;

        @Override
        public void handleMessage(Message msg) {
            if (msg.what == MSG_STOP_TIMER) {
                Log.d(TAG, "MSG_STOP_TIMER");
                hideFloatingWindow();
                dismiss();
            }
            super.handleMessage(msg);
        }
    }

    private void continueActivity(Context context) {
        Log.d(TAG, "continueActivity: ");
        ContinueUtils.unregisterListener(context, mDragonflyUtils);
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        hideFloatingWindow();
        dismiss();
    }
}
