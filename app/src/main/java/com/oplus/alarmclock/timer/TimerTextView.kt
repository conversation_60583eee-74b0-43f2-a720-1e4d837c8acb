/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerTextView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.StopWatchLongBinding
import com.oplus.alarmclock.utils.TextWeightUtils.setTextBold
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightMedium
import com.oplus.alarmclock.utils.TextWeightUtils.setTextWeightNoChange
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.ViewExt
import com.oplus.alarmclock.view.ViewExt.setColon
import java.text.NumberFormat
import kotlin.math.abs

class TimerTextView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val SIZE_UNSPECIFIED = -1
        private const val WEIGHT_BOLD = 800
        private const val ZERO = 0
        private const val ONE = 1
        private const val TWO = 2
        private const val THREE = 3
        private const val FOUR = 4
        private const val FIVE = 5
        private const val SIX = 6
        private const val SEVEN = 7
        private const val EIGHT = 8
        private const val NINE = 9
        private const val TWENTY_FOUR = 24
        private const val SIXTY = 60
        private const val SECOND_OR_MILLI = 1000L
        private const val MINUTE = SIXTY * SECOND_OR_MILLI
        private const val HOUR = SIXTY * MINUTE
    }

    private lateinit var mNumberFormat: NumberFormat
    private lateinit var mViewBinding: StopWatchLongBinding
    private var mFormatZero = ""
    private var mTime = 0L
    private var mTextLength = 0F
    private var mTextSize = 0F
    private var mTextSizeNotChange = -1F
    private var mTextColor = context.getColor(R.color.text_black_alpha_100)
    private var mMaxNum: String? = null
    private var mThemeColor = 0
    private var mIsCenter = false
    private var mMarginTop = 0
    private var mTextFontWeight = SIZE_UNSPECIFIED
    private val mColonText by lazy {
        ViewExt.getColonStr()
    }

    init {
        initAttrs(context, attrs)
    }

    /**
     * 1、因为9.5S显示的是10S，因此补偿了1S
     * 2、time可能传过来的是负数
     */
    fun update(time: Long) {
        initializedView()
        mTime = if (time <= 0) 0 else time + SECOND_OR_MILLI
        updateData()
    }

    fun getTime(): Long {
        return mTime
    }

    fun getMarginTop(): Int {
        return mMarginTop
    }

    fun center(isHover: Boolean) {
        if (isHover) {
            return
        }
        initializedView()
        if (layoutParams != null) {
            layoutParams = (layoutParams as MarginLayoutParams).run {
                if (!mIsCenter || (topMargin != mMarginTop)) {
                    mIsCenter = true
                    topMargin += mMarginTop
                    mMarginTop = topMargin
                }
                this
            }
        }
    }

    private fun initializedView() {
        if (!this::mViewBinding.isInitialized) {
            initView(context)
            initText()
        }
    }

    private fun updateData() {
        mViewBinding.run {
            val hour = mTime / HOUR % TWENTY_FOUR
            val minute = mTime / MINUTE % SIXTY
            val second = mTime / SECOND_OR_MILLI % SIXTY
            setData(hourStartTv, hourEndTv, hour)
            setData(minuteStartTv, minuteEndTv, minute)
            setData(secondStartTv, secondEndTv, second)
            colonMiddleTv.text = mColonText
            colonEndTv.text = mColonText
        }
    }

    private fun setData(startTv: TextView, endTv: TextView, time: Long) {
        val timeStr = mNumberFormat.format(time)
        if (timeStr.length >= TWO) {
            startTv.text = timeStr.substring(ZERO, ONE)
            endTv.text = timeStr.substring(ONE, TWO)
        } else {
            startTv.text = mFormatZero
            endTv.text = timeStr
        }
    }

    private fun initText() {
        measureMaxLength(mTextSize)
        mViewBinding.run {
            setTextColor()
            setTypeface()
            setTextSize()
            setColon()
        }
    }

    private fun initAttrs(context: Context, attrs: AttributeSet?) {
        mThemeColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimaryText)
        mTextFontWeight = Utils.getAttrTextFontWeight(context, attrs, R.attr.text_font_weight, SIZE_UNSPECIFIED)
        attrs?.let {
            context.resources.run {
                val defaultTextSize = getDimension(R.dimen.text_size_sp_36)
                val obtain = obtainAttributes(it, R.styleable.TimerTextView)
                val multiple = obtain.getFloat(R.styleable.TimerTextView_timer_multiple, 1F)
                mTextSizeNotChange = obtain.getFloat(R.styleable.TimerTextView_text_size_mini, -1F)
                mTextSize = if (mTextSizeNotChange != -1F) {
                    mTextSizeNotChange * multiple
                } else {
                    defaultTextSize * multiple
                }
                mTextColor = obtain.getColor(R.styleable.TimerTextView_default_text_color,
                    context.getColor(R.color.text_black_alpha_100))
                obtain.recycle()
            }
        }
    }

    private fun initView(context: Context) {
        val inflater = LayoutInflater.from(context)
        mViewBinding = StopWatchLongBinding.inflate(inflater, this, true)
        mNumberFormat = NumberFormat.getInstance()
        mFormatZero = mNumberFormat.format(0)
        layoutDirection = LAYOUT_DIRECTION_LTR
    }

    private fun measureMaxLength(textSize: Float) {
        val paint = Paint().apply {
            this.textSize = if (mTextSizeNotChange != -1F) {
                textSize
            } else {
                Utils.getTextSize(textSize)
            }
            if (mTextFontWeight != SIZE_UNSPECIFIED) {
                setTextWeightNoChange(mTextFontWeight)
            } else {
                setTextBold()
            }
        }
        if (!this::mNumberFormat.isInitialized) {
            mNumberFormat = NumberFormat.getInstance()
        }
        mTextLength = if (mMaxNum == null) {
            mNumberFormat.run {
                val numArr =
                        arrayOf(
                                format(ZERO),
                                format(ONE),
                                format(TWO),
                                format(THREE),
                                format(FOUR),
                                format(FIVE),
                                format(SIX),
                                format(SEVEN),
                                format(EIGHT),
                                format(NINE)
                        )
                var maxWidth = 0F
                numArr.forEach {
                    val width = paint.measureText(it)
                    maxWidth = maxWidth.coerceAtLeast(width)
                    if (maxWidth == width) {
                        mMaxNum = it
                    }
                }
                maxWidth
            }
        } else {
            paint.measureText(mMaxNum)
        }
        mTextSize = paint.textSize
        val textHeight = paint.fontMetrics.run { abs(bottom - top).toInt() }
        val parentHeight = context.resources.getDimension(R.dimen.layout_dp_252)
        mMarginTop = ((parentHeight - textHeight) / TWO).toInt()
    }

    private fun TextView.setSize(length: Int, size: Float) {
        layoutParams.width = length
        this.setTextSize(TypedValue.COMPLEX_UNIT_PX, size)
    }

    private fun StopWatchLongBinding.setTextSize() {
        mTextLength.toInt().run {
            mTextSize.let {
                hourStartTv.setSize(this, it)
                hourEndTv.setSize(this, it)
                minuteStartTv.setSize(this, it)
                minuteEndTv.setSize(this, it)
                secondStartTv.setSize(this, it)
                secondEndTv.setSize(this, it)
                colonMiddleTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
                colonEndTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, it)
            }
        }
    }

    private fun StopWatchLongBinding.setTypeface() {
        if (mTextFontWeight != SIZE_UNSPECIFIED) {
            hourStartTv.setTextWeightNoChange(mTextFontWeight)
            hourEndTv.setTextWeightNoChange(mTextFontWeight)
            minuteStartTv.setTextWeightNoChange(mTextFontWeight)
            minuteEndTv.setTextWeightNoChange(mTextFontWeight)
            secondStartTv.setTextWeightNoChange(mTextFontWeight)
            secondEndTv.setTextWeightNoChange(mTextFontWeight)
            colonMiddleTv.setTextWeightNoChange(mTextFontWeight)
            colonEndTv.setTextWeightNoChange(mTextFontWeight)
        } else {
            if (Utils.isAboveS()) {
                hourStartTv.setTextWeightMedium()
                hourEndTv.setTextWeightMedium()
                minuteStartTv.setTextWeightMedium()
                minuteEndTv.setTextWeightMedium()
                secondStartTv.setTextWeightMedium()
                secondEndTv.setTextWeightMedium()
                colonMiddleTv.setTextWeightMedium()
                colonEndTv.setTextWeightMedium()
            } else {
                //R版本不支字体粗细不支持900，改为默认加粗
                hourStartTv.setTextBold()
                hourEndTv.setTextBold()
                minuteStartTv.setTextBold()
                minuteEndTv.setTextBold()
                secondStartTv.setTextBold()
                secondEndTv.setTextBold()
                colonMiddleTv.setTextBold()
                colonEndTv.setTextBold()
            }
        }
    }

    private fun StopWatchLongBinding.setTextColor() {
        mTextColor.run {
            hourStartTv.setTextColor(this)
            hourEndTv.setTextColor(this)
            colonMiddleTv.setTextColor(this)
        }
        mThemeColor.run {
            minuteStartTv.setTextColor(this)
            minuteEndTv.setTextColor(this)
            colonEndTv.setTextColor(this)
            secondStartTv.setTextColor(this)
            secondEndTv.setTextColor(this)
        }
    }

    private fun StopWatchLongBinding.setColon() {
        mColonText.let {
            colonMiddleTv.setColon(it, R.dimen.timer_dial_colon_offset)
            colonEndTv.setColon(it, R.dimen.timer_dial_colon_offset)
        }
    }

    fun setTextSize(textSize: Float) {
        mTextSize = textSize
        measureMaxLength(mTextSize)
        if (this::mViewBinding.isInitialized) {
            mViewBinding.run {
                setTextSize()
            }
        }
    }
}