/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer;

import android.app.ActivityOptions;
import android.app.KeyguardManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.UserManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alert.AlarmFloatingWindowManager;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.HighPriorityHeadsUpServiceProxy;
import com.oplus.alarmclock.utils.NotificationUtils;
import com.oplus.alarmclock.utils.PhonyManagerExtensionKt;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static com.oplus.alarmclock.utils.DeviceUtils.registerSuperPowerObserver;
import static com.oplus.alarmclock.utils.DeviceUtils.unRegisterSuperPowerObserver;

public class TimerFloatingViewService extends Service {
    private static final String TAG = "TimerFloatingViewService";
    private static final String START_TIMER_ACTION = "start_timer_action";
    private static final String STOP_TIMER_ACTION = "stop_timer_action";
    private static final Long DELAY_TIMER_ALERT_TIMEOUT = 60 * 1000L;
    private static AlarmFloatingWindowManager sAlarmFloatingWindowManager;
    private static boolean sIsStoped = false;
    /**
     * 计时器是否正在响铃
     */
    private static boolean sIsStart = false;
    private HighPriorityHeadsUpServiceProxy mHighPriorityHeadsUpServiceProxy;
    private boolean mIsBindSystemUI = false;
    private int mCurrentTimeIndex = 0;
    private String mCurrentTimeName = "Timer";
    private Handler mHandler = null;
    private ContentObserver mSuperPowerSaveObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            Log.d(TAG, "onChange:" + selfChange);
            TimerSeedlingHelper.showFinishPageSecure(TimerFloatingViewService.this, mCurrentTimeIndex, mCurrentTimeName, isSuccess -> {
                if (isSuccess) {
                    hideFloatingWindow();
                } else {
                    TimerSeedlingHelper.closeSeedlingCard(TimerFloatingViewService.this);
                    showOldFloatingWindow(TimerFloatingViewService.this, mCurrentTimeIndex, mCurrentTimeName);
                }
            });
        }
    };

    public static void startTimer(Context context, String index, String name, int userID, String ringUri, String ringName) {
        sIsStoped = false;
        sIsStart = true;
        final Intent intent = new Intent(context, TimerFloatingViewService.class);
        intent.putExtra(TimerService.TIMER_INDEX, index);
        intent.putExtra(TimerService.TIMER_NAME, name);
        intent.putExtra(TimerService.TIMER_OWNER_USER_ID, userID);
        intent.putExtra(TimerService.TIMER_RING_URI, ringUri);
        intent.putExtra(TimerService.TIMER_RING_NAME, ringName);
        intent.setAction(START_TIMER_ACTION);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

    public static void stopTimer(Context context) {
        sIsStoped = true;
        sIsStart = false;
        final Intent intent = new Intent(context, TimerFloatingViewService.class);
        intent.setAction(STOP_TIMER_ACTION);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }

        Intent stopTimerIntent = new Intent(TimerAlertReceiver.ACTION_STOP_TIMER_RING_NORMAL);
        stopTimerIntent.setClassName(context.getPackageName(), TimerAlertReceiver.class.getName());
        context.sendBroadcast(stopTimerIntent);
    }

    public static boolean getIsTimerStoped() {
        Log.v(TAG, "getIsTimerStoped = " + sIsStoped);
        return sIsStoped;
    }

    public static boolean getIsTimerStart() {
        Log.v(TAG, "getIsTimerStart = " + sIsStart);
        return sIsStart;
    }

    public static void initFloatingViewManager(Context context, AlarmSchedule alarmSchedule,
                                               String timerIndex, String timerName) {
        if ((sAlarmFloatingWindowManager != null)
                && sAlarmFloatingWindowManager.floatingWindowIsShowing()) {
            return;
        }
        sAlarmFloatingWindowManager = new AlarmFloatingWindowManager(context, alarmSchedule, timerIndex, timerName);
    }

    @Override
    public IBinder onBind(Intent paramIntent) {
        // do nothing
        return null;
    }

    @Override
    public void onDestroy() {
        Log.v(TAG, "TimerService.onDestroy() called");
        super.onDestroy();
        TimerWakeLock.releaseCpuLockFull();
        finishFloatingAlarmView();
        setHighPriorityHeadsUp(false);
        sAlarmFloatingWindowManager = null;
        mHighPriorityHeadsUpServiceProxy = null;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        unRegisterSuperPowerObserver(this, mSuperPowerSaveObserver);
    }

    private void finishFloatingAlarmView() {
        if (sAlarmFloatingWindowManager != null) {
            sAlarmFloatingWindowManager.hideFloatingWindow();
            TimerSeedlingHelper.closeSeedlingCard(TimerFloatingViewService.this);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        NotificationUtils notificationUtils = new NotificationUtils();
        notificationUtils.sendDefaultForegroundNotification(this, this);
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        mHandler.postDelayed(this::timerAlertTimeout, DELAY_TIMER_ALERT_TIMEOUT);
        registerSuperPowerObserver(this, mSuperPowerSaveObserver);
    }

    private void timerAlertTimeout() {
        Intent intent = new Intent(TimerConstant.TIMER_ALERT_TIMEOUT);
        intent.setPackage(getPackageName());
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.v(TAG, "onStartCommand() with " + " " + intent);
        if (intent != null) {
            String action = intent.getAction();
            if (!TextUtils.isEmpty(action)) {
                switch (action) {
                    case START_TIMER_ACTION:
                        setHighPriorityHeadsUp(true);
                        startTimerAlert(this, intent);
                        break;
                    case STOP_TIMER_ACTION:
                        stopSelf();
                        break;
                    default:
                        break;
                }
            }
        }
        return Service.START_NOT_STICKY;
    }

    private void startTimerAlert(Context context, Intent intent) {
        startTimerKlaxon(context, intent);
        if (Utils.isAboveQ()) {
            startTimerAlertAsUser(context, intent);
        } else {
            String name = TimerService.DEFAULT_TIMER_NAME;
            String index = TimerService.DEFAULT_TIMER_INDEX;
            if (intent != null) {
                // for CTS
                name = intent.getStringExtra(TimerService.TIMER_NAME);
                index = intent.getStringExtra(TimerService.TIMER_INDEX);
                //end for cts
                Log.d(TAG, "CTS TimerName:" + name + ",index:" + index);
            }
            startTimerAlertActivityOrFloatingView(context, intent, name, index);
        }
    }

    private void startTimerKlaxon(Context context, Intent intent) {
        Intent timerRing = new Intent(this, TimerKlaxon.class);
        timerRing.putExtra(TimerService.TIMER_ALERT_TYPE, 0/* Default:Ring */);
        String uriStr = TimerUtils.getDefaultTimerRingtoneUri(this).toString();
        if (intent != null) {
            timerRing.putExtra(TimerService.TIMER_RING_NAME, intent.getStringExtra(TimerService.TIMER_RING_NAME));
            uriStr = intent.getStringExtra(TimerService.TIMER_RING_URI);
        }
        if (TextUtils.isEmpty(uriStr)) {
            uriStr = TimerUtils.getDefaultTimerRingtoneUri(this).toString();
        }
        if (uriStr != null) {
            timerRing.putExtra(TimerService.TIMER_RINGTONE_URI, uriStr);
            Log.d(TAG, "timerRing: " + timerRing);
        }
        context.startService(timerRing);
    }

    private void startTimerAlertActivityOrFloatingView(Context context, Intent intent, String name, String index) {

        TelephonyManager telephonyManager = (TelephonyManager) getSystemService(
                Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int callState = PhonyManagerExtensionKt.getTelephonyCallState(telephonyManager);
            KeyguardManager km = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
            if (km != null) {
                boolean isSmall = FoldScreenUtils.isDragonflySmallScreen(context);
                if ((callState == TelephonyManager.CALL_STATE_IDLE) && (km.isKeyguardLocked() || isSmall)) {
                    // Use the full screen activity for security.
                    Log.d(TAG, "isKeyguardLocked");
                    TimerAlert.sIsCountDownSceenLock = true;
                    Intent alert = new Intent(context, TimerAlertFullScreen.class);
                    alert.setFlags(
                            Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET
                                    | Intent.FLAG_ACTIVITY_NO_USER_ACTION);
                    if (intent != null) {
                        alert.putExtra(TimerService.TIMER_NAME, name);
                        alert.putExtra(TimerService.TIMER_INDEX, index);
                    }
                    TimerSeedlingHelper.closeSeedlingCard(context);
                    if (isSmall) {
                        alert.putExtra(TimerAlertFullScreen.IS_DRAGONFLY_SMALL, true);
                        ActivityOptions options = ActivityOptions.makeBasic();
                        options.setLaunchDisplayId(1);
                        context.startActivity(alert, options.toBundle());
                    } else {
                        context.startActivity(alert);
                    }
                    DeviceCaseTimerAlertView.INSTANCE.setShow(true);
                } else {
                    TimerAlert.sIsCountDownSceenLock = false;
                    showFloatingWindow(context, index, name);
                    resetTimerStatus(context);
                }
            }
        }
    }

    private void resetTimerStatus(Context context) {
        if (context != null) {
            Log.d(TAG, "resetTimerStatus");
            PrefUtils.putBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_START_PREFERENCE, false);
            PrefUtils.putBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false);
        }
    }

    private void startTimerAlertAsUser(Context context, Intent intent) {
        Log.d(TAG, "startTimerAlertAsUser intent:" + intent);

        int timerUserID = ClockConstant.OWNER_USER_ID;
        int currentUserID = AppPlatformUtils.getCurrentUser();

        String name = TimerService.DEFAULT_TIMER_NAME;
        String index = TimerService.DEFAULT_TIMER_INDEX;
        if (intent != null) {
            // for CTS
            name = intent.getStringExtra(TimerService.TIMER_NAME);
            index = intent.getStringExtra(TimerService.TIMER_INDEX);
            //end for cts
            timerUserID = intent.getIntExtra(TimerService.TIMER_OWNER_USER_ID, ClockConstant.OWNER_USER_ID);
            Log.d(TAG, "CTS TimerName:" + name + ",index:" + index);
        }
        Log.d(TAG, "timer user:" + timerUserID + ",current user:" + currentUserID);
        if ((timerUserID == currentUserID) || (!UserManager.supportsMultipleUsers())) {
            startTimerAlertActivityOrFloatingView(context, intent, name, index);
        } else {
            TimerAlert.sIsCountDownSceenLock = false;
            showFloatingWindow(context, index, name);
        }
    }

    /**
     * 非锁屏下显示浮窗
     *
     * @param context
     * @param timerIndex
     */
    private void showFloatingWindow(Context context, String timerIndex, String timerName) {
        mCurrentTimeIndex = Integer.parseInt(timerIndex);
        mCurrentTimeName = timerName;
        TimerSeedlingHelper.showFinishPageSecure(context, mCurrentTimeIndex, mCurrentTimeName, isSuccess -> {
            if (isSuccess) {
                if (sAlarmFloatingWindowManager != null) {
                    sAlarmFloatingWindowManager.hideFloatingWindow();
                }
                //计时器响铃后需要保持亮屏
                TimerWakeLock.acquireCpuWakeLockFull(context);
            } else {
                initFloatingViewManager(context, null, timerIndex, timerName);
                if (sAlarmFloatingWindowManager != null) {
                    sAlarmFloatingWindowManager.showFloatingWindow(null);
                }
            }
        });
    }

    public static void showOldFloatingWindow(Context context, int timerIndex, String timerName) {
        initFloatingViewManager(context, null, String.valueOf(timerIndex), timerName);
        if (sAlarmFloatingWindowManager != null) {
            sAlarmFloatingWindowManager.showFloatingWindow(null);
        }
    }

    public static void hideFloatingWindow() {
        if (sAlarmFloatingWindowManager != null) {
            sAlarmFloatingWindowManager.hideFloatingWindow();
        }
    }

    private void setHighPriorityHeadsUp(boolean enabled) {
        if (mHighPriorityHeadsUpServiceProxy == null) {
            mHighPriorityHeadsUpServiceProxy = new HighPriorityHeadsUpServiceProxy(
                    this);
        }
        Log.d(TAG, "setHighPriorityHeadsUp, enabled=" + enabled + " mIsBindSystemUI=" + mIsBindSystemUI);
        if (enabled) {
            mHighPriorityHeadsUpServiceProxy.show(() -> mIsBindSystemUI = true);
        } else {
            mHighPriorityHeadsUpServiceProxy.unbindService();
            if (mIsBindSystemUI) {
                mHighPriorityHeadsUpServiceProxy.dismiss();
                mIsBindSystemUI = false;
            }
        }
    }
}
