/*
 * Copyright 2008-2010 OPLUS Mobile Comm Corp., Ltd, All rights reserved. FileName: OplusTimer.java
 * ModuleName: Timer Author: Chenyk Create Date: Description: the main activity of timer.
 * <p>
 * History: <version > <time> <author> <desc> 1.0 2010-9-24 Ma<PERSON>ong TimerMainActivity 2.0 2011-8-25
 * <PERSON><PERSON> code reconsitution from TimerMainActivity
 */

package com.oplus.alarmclock.timer;


import static com.oplus.alarmclock.timer.TimerSeedlingHelper.EVENT_REFRESH_TIMER_VIEW;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.tintimageview.COUITintImageView;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.base.BaseUiModeFragment;
import com.oplus.alarmclock.deletesound.SoundEffectManager;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.alarmclock.timer.data.TimerSyncEntity;
import com.oplus.alarmclock.timer.ui.TimerAdapterSpaceItemDecoration;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.alarmclock.timer.ui.TimerSetFrament;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.EditMenuClickUtils;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode;
import com.oplus.alarmclock.utils.HapticsStyleController;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.TimerRecyclerView;
import com.oplus.alarmclock.view.water.WaterClockView;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.ProcessCommunicateTimer;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;
import com.oplus.alarmclock.view.modelview.ISaveInstanceState;
import com.oplus.statistics.OplusTrack;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.ViewDataBinding;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public abstract class OplusTimerFragment<T extends ViewDataBinding> extends BaseUiModeFragment<T>
        implements AlarmClock.IFragmentFocused,
        AlarmClock.NavigationDelete,
        ISaveInstanceState,
        HapticsStyleController.OnTactileSteplessChangeListener,
        HapticsStyleController.OnTactileStyleChangeListener {
    public static final int SET_DEFAULT_TIME = 1117;
    public static final int SET_DEFAULT_TIME_DELAY = 150;
    public static final int EDIT_ITEM_NUMBER = 0;
    protected static final int CONSTANT_130 = 130;
    protected static final int CONSTANT_110 = 110;
    protected static final int CONSTANT_1920 = 1920;
    protected static final int CONSTANT_1696 = 1696;
    protected static final int CONSTANT_1456 = 1456;
    protected static final int STATUS_PAUSE = 2;
    protected static final int CONTROLLER_MARGIN_TOP = 180;
    protected static final int CONTROLLER_MARGIN = 140;


    private static final float MARK_PX_DP = 0.5f;
    private static final String TAG = "OplusTimerFragment";
    public static boolean sEditMode;
    private static boolean sIsShowEditItem = true;
    public COUIToolbar mToolbar;
    /**
     * 分割线
     */
    public TimerAdapterSpaceItemDecoration mTimerAdapterDec;
    public TimerService mLoadService;
    protected TimerController mTimerController;
    protected TimerController mCtsTimerController;

    private LocalBroadcastManager mLocalBroadcastManager;
    private Handler mHandler;
    private volatile LayoutInflater mLayoutInflater;
    private volatile ViewGroup mViewGroup;
    private Bundle mBundle;
    private boolean mHasBindService;
    private ExecutorService mExecutorService = Executors.newSingleThreadExecutor();
    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();
    private boolean mIsPause;
    private boolean mIsStart;
    private boolean mToSetting;
    private boolean mIsFirstEnter = true;
    private boolean mIsSelected = false;
    private boolean mNotInitEffect;
    private int mClickMenuId;
    private BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {

        public void onReceive(Context context, Intent intent) {

            String action = intent.getAction();
            if (TextUtils.isEmpty(action)) {
                return;
            }
            Log.i(TAG, "receive intent = " + intent.getAction());
            if (TimerService.REFRESH_TIMERS.endsWith(action)) {
                if (mTimerController != null) {
                    mTimerController.refreshLayout();
                }
                refreshCtsTimers();
            } else if (TimerConstant.SYNCHRONIZE_TIME_BROADCAST.equals(action)) {
                if (mTimerController != null) {
                    long currentTime = intent.getLongExtra(ProcessCommunicateTimer.TIMER_CURRENT_TIME, 0);
                    long totalTime = intent.getLongExtra(ProcessCommunicateTimer.TIMER_TOTAL_TIME, 0);
                    String name = intent.getStringExtra(ProcessCommunicateTimer.TIMER_NAME);
                    int selectedPosition = intent.getIntExtra(ProcessCommunicateTimer.TIMER_SELECTED_POSITION, ProcessCommunicateTimer.POSITION_NOT_VALUE);
                    int status = intent.getIntExtra(ProcessCommunicateTimer.TIMER_STATUS, 0);
                    mTimerController.syncTime(new TimerSyncEntity(currentTime, totalTime, name, selectedPosition, status));
                }
            } else if (TimerConstant.PAUSE_TIMER_BROADCAST.equals(action)) {
                if (mTimerController != null) {
                    mTimerController.refreshViewsFromAiSupport();
                }
            } else if (TimerConstant.RESUME_TIMER_BROADCAST.equals(action)) {
                int timerIndex = intent.getIntExtra(TimerNotificationManager.KEY_TIMER_INDEX, 0);
                if (mTimerController != null) {
                    mTimerController.refreshViewsFromAiSupport();
                }
                if ((timerIndex != 0) && (mCtsTimerController != null)) {
                    mCtsTimerController.refreshViewsFromAiSupport();
                }
            } else if (TimerService.STOP_TIMER.equals(action)) {
                if (mTimerController != null) {
                    mTimerController.refreshLayout();
                    mTimerController.stopTimer();
                }
            }
        }
    };
    /**
     * Called when the activity is first created.
     */
    private ServiceConnection mLoadServiceConnect = new ServiceConnection() {
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "onServiceConnected Bind successfully mLoadServiceConnect");
            Log.i(TAG, "onServiceConnected Bind successfully");
            if (service instanceof TimerService.TimerBinder) {
                TimerService.TimerBinder iBinder = (TimerService.TimerBinder) service;
                mLoadService = iBinder.getService();
            }
            refreshCtsTimers();
            if (mLoadService != null) {
                mTimerController.onServiceConnected(mLoadService);
                createATimedTask();
            }
        }

        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected");
        }
    };

    public boolean isTimer0Running() {
        boolean isTimer0Running = false;
        if (mLoadService != null) {
            isTimer0Running = mLoadService.isStart(0);
        }
        return isTimer0Running;
    }

    /**
     * Called when the activity is first created.
     */
    @Override
    public void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate");
        registerBroadcast();
        setHasOptionsMenu(true);
        super.onCreate(savedInstanceState);
    }


    public void redDotSetting() {
        if (couiToolbar() != null) {
            redDotSetting(couiToolbar());
        }
    }

    private void registerBroadcast() {
        IntentFilter filterLocal = new IntentFilter();
        filterLocal.addAction(TimerService.REFRESH_TIMERS);
        filterLocal.addAction(TimerConstant.RESUME_TIMER_BROADCAST);
        filterLocal.addAction(TimerConstant.PAUSE_TIMER_BROADCAST);
        filterLocal.addAction(TimerConstant.SYNCHRONIZE_TIME_BROADCAST);
        filterLocal.addAction(TimerService.STOP_TIMER);
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(getActivity());
        mLocalBroadcastManager.registerReceiver(mLocalReceiver, filterLocal);
    }

    /**
     * 初始化view
     *
     * @param inflater
     * @param group
     */
    @Override
    protected void initView(@NonNull LayoutInflater inflater, @androidx.annotation.Nullable ViewGroup group) {
        super.initView(inflater, group);
        if (group != null) {
            mContext = group.getContext();
        } else {
            mContext = getContext();
        }
        //when start app
        getTimerCountCurrent();
        // Add Default timers.
        addDefaultTimer(getRootView());
        startService();
        updateSplitParams();
        calculationLocationY();
        checkHoverStatus();
        initClockEffect();
        HapticsStyleController.registerContentObserver(mContext, this, this);
        if (couiToolbar() != null) {
            couiToolbar().setPopupWindowOnDismissListener(() -> {
                if (mClickMenuId == R.id.edit) {
                    changeMode(true);
                }
                mClickMenuId = 1;
            });
            couiToolbar().setOnMenuItemClickListener(menuItem -> {
                onOptionsItemSelected(menuItem);
                return false;
            });
        }
        LiteEventBus.Companion.getInstance().with(TimerConstant.TIMER_REFRESH_BY_AI, String.valueOf(hashCode())).observe(this, o -> {
            if (mTimerController != null) {
                mTimerController.onResume();
                updateShadow();
            }
        });
        LiteEventBus.Companion.getInstance().with(EVENT_REFRESH_TIMER_VIEW, String.valueOf(hashCode())).observe(this, o -> {
            Log.i(TAG, "timer reset");
            //计时器再来一次广播监听刷新界面
            onResume();
        });
    }

    /**
     * 初始化光追时钟
     */
    protected void initClockEffect() {
        if (Utils.isAboveOS14()) {
            if (mNotInitEffect) {
                updateShadow();
                if (mIsFirstEnter) {
                    isFirstEnter();
                }
                mNotInitEffect = false;
            }
        }
    }

    /**
     * 初始化数据
     */
    @Override
    protected void initData() {
        super.initData();

    }

    private void getTimerCountCurrent() {
        if (mExecutorService != null) {
            mExecutorService.execute(() -> ClockOplusCSUtils.statisticsTimerCount(
                    AlarmClockApplication.getInstance(),
                    TimerDataHelper.getTimerCount(AlarmClockApplication.getInstance())));
        }
    }

    private synchronized void addDefaultTimer(View view) {
        if (view == null) {
            Log.e(TAG, "addDefaultTimer view is null!");
            return;
        }

        mIsPause = TimerPreUtils.getTimerStatusPause();
        mIsStart = TimerPreUtils.getTimerStatusStart();

        Log.i(TAG, "addDefaultTimer index = 0");
        mTimerController = createTimerController(0);
        mHandler = mTimerController.getTimerHandler();
        mTimerController.inflateView(view);
    }

    private void startService() {
        if (getActivity() != null && mLoadServiceConnect != null) {
            Intent intent = new Intent(getActivity(), TimerService.class);
            getActivity().bindService(intent, mLoadServiceConnect, Service.BIND_AUTO_CREATE);
            getActivity().startService(intent);
            mHasBindService = true;
        } else {
            Log.e(TAG, "getActivity() or mLoadServiceConnect is null");
        }
    }

    private synchronized void addCtsTimerObj(int index) {
        Log.i(TAG, "addCtsTimerObj index = " + index);
        if (mTimerController != null) {
            long time = TimerPreUtils.getTimeRemained();
            int status = TimerPreUtils.getTimerStatus();
            long remain = TimerPreUtils.getTimer0RemainTime(time);
            Log.d(TAG, "addCtsTimerObj mTimerController: " + time + ", status: " + status + " remain:" + remain);
            if (status == STATUS_PAUSE) {
                mTimerController.getOplusTimerPicker().setVisibility(View.INVISIBLE);
                mTimerController.getCountdownTime().setVisibility(View.VISIBLE);
                mTimerController.setCountDownTime(remain);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshCtsTimers();
        Log.i(TAG, "onResume");
        if (mTimerController != null) {
            mTimerController.onResume();
        }
        OplusTrack.onResume(getActivity());
        if (mLoadService != null) {
            createATimedTask();
        }
        updateShadow();
    }

    public void resetButton() {
        if (mTimerController != null) {
            mTimerController.resetButton();
        }
    }

    private void createATimedTask() {
        Log.d(TAG, "createATimedTask mBundle: " + mBundle);
        if (mBundle != null && mTimerController != null) {
            boolean startCustomTimer = mBundle.getBoolean(AlarmClock.EXTRA_TIMER_START, false);
            long mCustomTimerSeconds = mBundle.getLong(AlarmClock.EXTRA_TIMER_SECONDS, 0);
            Log.i(TAG, "onResume: mStartCustomTimer: " + startCustomTimer + ", mCustomTimerSeconds: " + mCustomTimerSeconds);
            //开启通过cts创建的计时器
            if (startCustomTimer) {
                if (mTimerController.isEditStatus()) {
                    mTimerController.disableEditStatus();
                    if (mToolbar != null) {
                        sEditMode = false;
                        AlarmClock.correctAllMenuItemFromFragment(getActivity());
                    }
                }
                mTimerController.saveTimeStatus(true, true);
                if (mHandler != null) {
                    Message message = mHandler.obtainMessage(TimerController.START_CUSTOM_TIMER, (int) mCustomTimerSeconds, 0);
                    mHandler.sendMessage(message);
                    Log.i(TAG, "sendMessage: " + "START_CUSTOM_TIMER");
                }
            }
            ClockOplusCSUtils.uploadTimerInfoOPlusFromAi(mCustomTimerSeconds);
            mBundle = null;
        }
    }

    @Override
    public void onPause() {
        Log.i(TAG, "onPause");
        super.onPause();
        if (getActivity() != null) {
            // 中屏，大屏 不暂停计时
            boolean showFlexibleWindow = (getUiMode() == UiMode.LARGE_HORIZONTAL || getUiMode() == UiMode.MIDDLE
                    || getUiMode() == UiMode.LARGE_VERTICAL) && FlexibleWindowUtils.isSupportFlexibleActivity() && mToSetting;
            if (!showFlexibleWindow && mTimerController != null) {
                mTimerController.onPause();
            }
        }
        mIsSelected = false;
        TimerWakeLock.releaseCpuLockPartial();
        OplusTrack.onPause(getActivity());
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "onDestroy");
        super.onDestroy();
        HapticsStyleController.unregisterContentObserver(mContext, this, this);
        if (mTimerController != null) {
            mTimerController.unBindService();
        }
        if (mHasBindService) {
            Activity activity = getActivity();
            if (activity != null) {
                activity.unbindService(mLoadServiceConnect);

                mLoadServiceConnect = null;
            }
            mHasBindService = false;
        }

        TimerWakeLock.releaseCpuLockPartial();
        Log.i(TAG, "onDestroy called!");
        mLocalBroadcastManager.unregisterReceiver(mLocalReceiver);
        mLocalReceiver = null;
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        sEditMode = false;
        if (mExecutorService != null) {
            mExecutorService.shutdownNow();
            mExecutorService = null;
        }
        LiteEventBus.Companion.getInstance().releaseEvent(TimerConstant.TIMER_REFRESH_BY_AI);
        LiteEventBus.Companion.getInstance().releaseEvent(EVENT_REFRESH_TIMER_VIEW);
    }

    public void setBundle(Bundle bundle) {
        mBundle = bundle;
    }

    @Override
    public void onStop() {
        Log.i(TAG, "onStop");
        super.onStop();
    }

    private void refreshCtsTimers() {
        Activity activity = getActivity();
        if (activity == null) {
            return;
        }
        if ((mLoadService != null)) {

            Set<Integer> timersKey = mLoadService.getTimersKey();
            if (timersKey == null) {
                return;
            }
            Object[] set = timersKey.toArray();
            for (Object o : set) {
                addCtsTimerObj((Integer) o);
            }
            if (activity.getIntent() != null) {
                String action = activity.getIntent().getAction();
                String message = activity.getIntent().getStringExtra(AlarmClock.EXTRA_MESSAGE);
                int length = activity.getIntent().getIntExtra(AlarmClock.EXTRA_LENGTH, 0);
                Log.i(TAG, "intent action:" + action + ", message:" + message + ", length" + length);
            }
        }

    }

    /**
     * 搬家数据恢复
     */
    public void onRestoreCompleted() {
        Log.i(TAG, "onRestoreCompleted");
        if (mTimerController != null) {
            ArrayList<OplusTimer> timerList = TimerDataHelper.getAllTimers(getContext());
            if (mTimerController.getAdapter() != null) {
                mTimerController.getAdapter().setTimerData(timerList);
            }
            OplusTimer timer = TimerDataHelper.getSeletedTimer(timerList);
            mTimerController.setSelectedTimer(timer);
            mTimerController.setTimerList(timerList);
            mTimerController.invalidateAdapter();
            mTimerController.updateAddTimerBtnStatus();
            if (timer != null) {
                long time = TimerPreUtils.getTimerSetTime();
                if (time != 0) {
                    TimerUtils.inflateTimer(mTimerController.getOplusTimerPicker(), time);
                }
            }
            setOsloTimerListPadding();
        }
    }

    @Override
    public void delete() {
        deleteTimer();
    }

    @Override
    public String getDeleteTitle() {

        String defaultString = getString(R.string.oplus_delete);

        if (mTimerController == null) {
            return defaultString;
        }
        ArrayList<OplusTimer> timerList = mTimerController.getTimerList();
        if ((timerList == null)) {
            return defaultString;
        }
        int totalCount = timerList.size();
        int selectCount = mTimerController.selectQuerySelectedItemNumber();
        return getDeleteTitle(selectCount, totalCount);
    }


    public interface TimerFragmentCallback {
        void setTime(long duration);

        void invalidateAdapter();

        void onItemModifyCallback(OplusTimer timer);

        void onItemOnLongClickListener(View v, int position);

        void onItemClickCallback(OplusTimer timer);

        void onCheckBoxStateChanged(OplusTimer oplusTimer);
    }

    public void dismissAllModelView() {
        if (mTimerController != null) {
            mTimerController.closedModelView();
        }
    }

    /**
     * 浮窗下跳转表盘大小
     */
    public void moveToFloatingScale(Boolean isFloatingWindow) {
        if (timerProgressViewLayout() != null) {
            timerProgressViewLayout().post(() -> {
                int viewWidth = timerProgressViewLayout().getWidth();
                //设置缩放锚点
                timerProgressViewLayout().setPivotX(viewWidth / FoldScreenUtils.NUMBER_TWO);
                timerProgressViewLayout().setPivotY(0);
                Float scale = 1F;
                if (isFloatingWindow) {
                    scale = FoldScreenUtils.FLOATING_WINDOW_SCALE;
                }
                //设置缩放比例，可以根据需要调整
                timerProgressViewLayout().setScaleX(scale);
                timerProgressViewLayout().setScaleY(scale);
            });
        }
    }

    public void onAlarmClockResult(Intent data) {
        if (mTimerController == null) {
            Log.e(TAG, "onAlarmClockResult timer controller is null!");
            return;
        }
        int type = -1;
        if (data != null) {
            type = data.getIntExtra(TimerSetFrament.TIMER_SET_ACTIVITY_TYPE, TimerSetFrament.ACTIVITY_TYPE_ADD_TIME);
            if (type == TimerSetFrament.ACTIVITY_TYPE_ADD_TIME) {
                mTimerController.updateTimerData();
            } else if (type == TimerSetFrament.ACTIVITY_TYPE_SET_TIME) {
                mTimerController.updateTimeAfterEdit(getContext());
            }
            changeMode(false);//when modification is saved, exit the editable status.
        }

        if ((mTimerController.getTimerList() != null) && (mTimerController.getTimerList().size() > EDIT_ITEM_NUMBER)) {
            setEditItemVisible(true);
        }
        if (mTimerController.getAdapter() != null) {
            mTimerController.getAdapter().setTimerData(mTimerController.getTimerList());
        }
        mTimerController.invalidateAdapter();
        setOsloTimerListPadding();
        if ((data != null) && (type == TimerSetFrament.ACTIVITY_TYPE_ADD_TIME)) {
            mTimerController.scrollToTheBottom();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case TimerSetFrament.START_SET_TIMER_FOR_RESULT:
                if (resultCode == Activity.RESULT_OK) {
                    if (mTimerController == null) {
                        Log.e(TAG, "onActivityResult timer controller is null!");
                        return;
                    }
                    if (data != null) {
                        int type = data.getIntExtra(TimerSetFrament.TIMER_SET_ACTIVITY_TYPE, TimerSetFrament.ACTIVITY_TYPE_ADD_TIME);
                        if (type == TimerSetFrament.ACTIVITY_TYPE_ADD_TIME) {
                            mTimerController.updateTimerData();
                        } else if (type == TimerSetFrament.ACTIVITY_TYPE_SET_TIME) {
                            mTimerController.updateTimeAfterEdit(getContext());
                        }
                    }
                    if ((mTimerController.getTimerList() != null) && (mTimerController.getTimerList().size() > EDIT_ITEM_NUMBER)) {
                        setEditItemVisible(true);
                    }
                    if (mTimerController.getAdapter() != null) {
                        mTimerController.getAdapter().setTimerData(mTimerController.getTimerList());
                    }
                    mTimerController.invalidateAdapter();
                    setOsloTimerListPadding();
                }
                break;
            default:
                break;
        }
    }


    public void setModeToNormal() {
        if (sEditMode) {
            changeMode(false);
        }
    }

    @Override
    public ViewGroup getBlurView() {
        return null;
    }


    @Override
    public void onFocused(boolean focused) {
        if (focused) {
            refreshEditItemVisible();
            //调用计时面板的闪烁动画
            startFlashingAnimator();
        }
    }

    @Override
    public void onPreChangeTab() {

    }

    @Override
    public void onStyleChange(int tactileStyle) {
        if (mTimerController != null) {
            mTimerController.updateVibrateLevel(tactileStyle);
        }
    }

    @Override
    public void onSteplessChange(float tactileStepless) {
        if (mTimerController != null) {
            mTimerController.updateVibrateIntensity(tactileStepless);
        }
    }

    private void refreshEditItemVisible() {
        if ((mTimerController != null) && (mTimerController.getTimerList() != null)) {
            int size = mTimerController.getTimerList().size();
            if (size <= EDIT_ITEM_NUMBER) {
                setEditItemVisible(false);
            }
        }
    }

    @SuppressLint("StringFormatMatches")
    private void updateSelectCount(int count) {
        Activity activity = getActivity();
        if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)) {
            if (couiToolbar() != null) {
                couiToolbar().setTitle(getToolbarTitle(count));
            }
            ((AlarmClock) activity).setNavigationItemEnable(count > 0, R.id.navigation_delete);
        }
    }

    protected int getCurrentCount() {
        return getTimerCount();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        Log.d(TAG, "onOptionsItemSelected item.getItemId():" + item.getItemId());
        int itemId = item.getItemId();
        if (itemId == R.id.edit) {
            if (mDoubleClickHelper.canClick() && EditMenuClickUtils.Companion.canClickAfterTabSwitch()) {
                mClickMenuId = item.getItemId();
            }
        } else if (itemId == android.R.id.home || itemId == R.id.cancel_select) {
            changeMode(false);
        } else if (itemId == R.id.settings) {
            if (mDoubleClickHelper.canClick()) {
                mToSetting = true;
                startToSetting();
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.SETTING_FROM_TIMER);
            }
        } else if (itemId == R.id.select_all_clock) {
            if (mDoubleClickHelper.canClick()) {
                doQuickSelect();
            }
        }
        return true;
    }

    public void clearState() {
        if (sEditMode) {
            if (mTimerController != null) {
                mTimerController.selectAllTimer(false);
                mTimerController.disableEditStatus();
                mTimerController.onRecycling();
            }
        }
    }

    public void changeMode(boolean mode) {
        if (couiToolbar() != null) {
            couiToolbar().getMenu().close();
            couiToolbar().getMenu().clear();
        }
        updateSelectCount(0);
        if (mode) {
            if ((mTimerController != null) && (sEditMode != mode)) {
                mTimerController.startAnimation(true);
            }
            if (mTimerController != null) {
                mTimerController.enableEditStatus();
            }
            Activity activity = getActivity();
            if ((activity != null) && (!activity.isFinishing())) {
                ((AlarmClock) activity).showNavigation();
                if (FoldScreenUtils.isInDealMultiWindowMode(isInMultiWindowMode())) {
                    RelativeLayout.LayoutParams lpOplusTimerPicker = (RelativeLayout.LayoutParams) ((AlarmClock) activity).mHideNavigationView.getLayoutParams();
                    lpOplusTimerPicker.height = getResources().getDimensionPixelSize(R.dimen.layout_dp_56);
                    ((AlarmClock) activity).mHideNavigationView.setLayoutParams(lpOplusTimerPicker);
                }
            }
            if (mTimerController != null) {
                mTimerController.mTimerAdd.setVisibility(View.INVISIBLE);
            }
            if (couiToolbar() != null) {
                couiToolbar().setIsTitleCenterStyle(true);
                couiToolbar().inflateMenu(R.menu.menu_edit_mode);
            }
        } else {
            refreshEditItemVisible();
            clearState();
            if ((mTimerController != null) && (sEditMode != mode)) {
                mTimerController.startAnimation(false);
            }
            if (mTimerController != null) {
                mTimerController.disableEditStatus();
            }
            Activity activity = getActivity();
            if ((activity != null) && !activity.isFinishing()) {
                ((AlarmClock) activity).dismissNavigation();
            }
            if (mTimerController != null) {
                mTimerController.mTimerAdd.setVisibility(View.VISIBLE);
            }
            if (couiToolbar() != null) {
                couiToolbar().setTitle("");
                couiToolbar().setIsTitleCenterStyle(false);
                couiToolbar().inflateMenu(R.menu.action_menu_icon_all);
            }
        }
        sEditMode = mode;
        AlarmClock.correctAllMenuItemFromFragment(getActivity());
        if (mTimerController != null) {
            mTimerController.refreshBottomLayout(false);
        }
    }

    private void deleteTimer() {
        if (mTimerController != null) {
            mTimerController.deleteSelectAllTimer();
            if (mTimerController.getAdapter() != null) {
                mTimerController.getAdapter().setTimerData(mTimerController.getTimerList());
            }
            mTimerController.updateAddTimerBtnStatus();
            isResetTag(mTimerController.getTimerList());
            setOsloTimerListPadding();
        }
        selectQuerySelected();
        SoundEffectManager.getInstance().playSoundEffect();
    }


    public void updateDbData() {
        if (mTimerController != null) {
            mTimerController.updateDbData();
        }
    }

    public void isResetTag(ArrayList<OplusTimer> timerList) {

        if ((timerList != null) && (mTimerController != null)) {
            OplusTimer selectedTimer = mTimerController.getSelectedTimer();
            if (selectedTimer != null) {
                boolean resetTag = timerList.contains(selectedTimer);
                if (!resetTag) {
                    Log.e(TAG, "isResetTag  isResetTag");
                    mTimerController.setSelectedTimer(null);
                }
            }
        }
    }

    //执行编辑打开模态视图
    public void carriedEdit() {
        OplusTimer oplusTimer = getSelectTimer();
        if ((mTimerController != null) && (oplusTimer != null)) {
            mTimerController.onItemModifyCallback(oplusTimer);
        }
    }

    public OplusTimer getSelectTimer() {
        if (mTimerController != null) {
            ArrayList<OplusTimer> oplusTimers = mTimerController.getTimerList();
            if (oplusTimers != null) {
                for (int i = 0; i < oplusTimers.size(); i++) {
                    OplusTimer oplusTimer = oplusTimers.get(i);
                    if (oplusTimer.getCheckBox()) {
                        return oplusTimer;
                    }
                }
            }
        }
        return null;
    }

    public boolean getTimerWhetherCheckAll() {
        boolean mAllSelect = false;
        if (mTimerController != null) {
            ArrayList<OplusTimer> oplusTimers = mTimerController.getTimerList();
            if (oplusTimers != null) {
                for (int i = 0; i < oplusTimers.size(); i++) {
                    OplusTimer oplusTimer = oplusTimers.get(i);
                    if (!oplusTimer.getCheckBox()) {
                        mAllSelect = true;
                        break;
                    }
                }
            }
        }
        return mAllSelect;
    }

    public void startFlashingAnimator() {
        if (mTimerController != null) {
            mTimerController.startTimerPauseAnimator();
        }
    }

    //更新选中数量
    public void selectQuerySelected() {
        AlarmClock.correctAllMenuItemFromFragment(getActivity());
        if (mTimerController != null) {
            int number = mTimerController.selectQuerySelectedItemNumber();
            updateSelectCount(number);
            Activity activity = getActivity();
            if (number == 1) {
                //编辑可用
                if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)) {
                    ((AlarmClock) activity).setNavigationItemEnable(true, R.id.navigation_oplus_edit);
                }
            } else {
                if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)) {
                    ((AlarmClock) activity).setNavigationItemEnable(false, R.id.navigation_oplus_edit);
                }
            }
            updateMenuText();
        }
    }

    public boolean onBackPressed() {
        Log.i(TAG, "onBackPressed, mEditMode: " + sEditMode);
        if (sEditMode) {
            changeMode(false);
            return true;
        } else {
            return false;
        }
    }

    public void setEditItemVisible(boolean visible) {
        Activity activity = getActivity();
        if ((activity != null) && !activity.isDestroyed()) {
            setIsShowEditItem(visible);
            AlarmClock.correctAllMenuItemFromFragment(getActivity());
        }
    }


    private static void setIsShowEditItem(boolean showEditItem) {
        sIsShowEditItem = showEditItem;
    }

    public static boolean getIsShowEditItem() {
        return sIsShowEditItem;
    }

    public boolean isEditMode() {
        return sEditMode;
    }

    public int getTimerCount() {
        return ((mTimerController == null) || (mTimerController.getTimerList() == null)) ? 0 : mTimerController.getTimerList().size();
    }

    /**
     * 是否展示编辑按钮
     */
    public void isShowEditMenu() {
        if (couiToolbar() != null) {
            MenuItem menuItem = couiToolbar().getMenu().findItem(R.id.edit);
            if (menuItem != null && mTimerController != null) {
                if (mTimerController.mIsStart || mTimerController.mIsPause) {
                    menuItem.setVisible(false);
                } else {
                    menuItem.setVisible(getTimerCount() > 0);
                }
            }
        }
    }

    public void isShowEditMenu(Boolean isStart, Boolean isPause) {
        if (couiToolbar() != null) {
            MenuItem menuItem = couiToolbar().getMenu().findItem(R.id.edit);
            if ((menuItem != null)) {
                if (isStart || isPause) {
                    menuItem.setVisible(false);
                } else {
                    menuItem.setVisible(getTimerCount() > 0);
                }
            }
        }
    }

    public void doQuickSelect() {
        if (mTimerController != null) {
            boolean allSelect = getTimerWhetherCheckAll();
            if (allSelect) {
                mTimerController.selectAllTimer(true);
            } else {
                mTimerController.selectAllTimer(false);
            }
            selectQuerySelected();
            updateMenuText();
        }
    }


    /**
     * 更新顶部菜单文字
     */
    private void updateMenuText() {
        if (couiToolbar() != null) {
            boolean allSelect = getTimerWhetherCheckAll();
            MenuItem menuItem = couiToolbar().getMenu().findItem(R.id.select_all_clock);
            if (menuItem != null) {
                if (!allSelect) {
                    menuItem.setTitle(getString(R.string.unselect_all_text));
                } else {
                    menuItem.setTitle(getString(R.string.select_all));
                }
            }
        }
    }

    @Nullable
    @Override
    public Bundle getBundleWhenSaveInstanceState() {
        if (mTimerController != null) {
            return mTimerController.getBundleWhenSaveInstanceState();
        } else if (mCtsTimerController != null) {
            return mCtsTimerController.getBundleWhenSaveInstanceState();
        } else {
            return null;
        }
    }

    public void showTimerModelView(Bundle bundle) {
        if (mTimerController != null) {
            mTimerController.openModelView(null, false, bundle);
        }
    }

    public void selectEd() {
        if (mTimerController == null || mTimerController.mShadowManager == null) {
            mNotInitEffect = true;
        } else {
            updateShadow();
            if (mIsFirstEnter) {
                isFirstEnter();
            }
        }
    }

    public void onStatusBarClicked() {
        Log.d(TAG, "onStatusBarClicked: ");
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (permissions != null) {
            Log.i(TAG, "onRequestPermissionsResult  permissions.length = " + permissions.length);
        }
        if (grantResults != null) {
            Log.i(TAG, "onRequestPermissionsResult  grantResults.length = " + grantResults.length);
        }
        if (mTimerController != null) {
            mTimerController.requestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    public boolean timerModelViewInited() {
        return (mTimerController != null) && mTimerController.timerModelViewInited();
    }

    private void updateShadow() {
        if (mTimerController != null && !mIsSelected) {
            mIsSelected = true;
        }
    }

    protected void updateSplitParams() {
    }

    protected void calculationLocationY() {
    }

    public void setLayoutPadding() {
    }

    /**
     * 设置平板底部间距
     */
    public void setComponentBtn() {
    }

    /**
     * 获取按钮横向间距
     */
    public int getBtnHorizontalSpacing() {
        return mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_24);
    }

    /**
     * 检查悬停状态
     */
    public void checkHoverStatus() {
    }

    /**
     * 切换到悬停
     */
    public void changeToHover() {
    }

    /**
     * 切换到普通模式
     */
    public void changeNormal() {
    }

    /**
     * 设置表盘距离顶部的间距
     */
    public void setTimerProgressLayout() {
    }

    /**
     * 平板设置间距
     */
    public void setOsloTimerListPadding() {
    }

    /**
     * 设置布局管理器
     */
    public void setRecyclerViewLayoutManager() {
    }

    /**
     * 首次进入
     */
    protected void isFirstEnter() {
        if (Utils.isAboveOS14()) {
            if (mTimerController != null && mTimerController.mShadowManager != null && !mTimerController.mIsHover) {
                mIsFirstEnter = !mIsFirstEnter;
            }
        }
    }

    /**
     * 创建TimerController对象
     *
     * @param index 第几个计时器
     * @return TimerController
     */
    protected abstract TimerController createTimerController(int index);

    /**
     * 添加计时器按钮
     *
     * @return TextView
     */
    protected abstract TextView timerAdd();

    /**
     * 计时器滚轮
     *
     * @return OplusTimePickerCustomClock
     */
    protected abstract OplusTimePickerCustomClock oplusTimerPicker();

    /**
     * @return TimerView
     */
    protected abstract TimerView timerView();

    /**
     * @return TimerTextView
     */
    protected abstract TimerTextView timerTextView();

    /**
     * 标题
     *
     * @return TextView
     */
    protected abstract TextView titleName();

    /**
     * 添加计时器layout
     *
     * @return RelativeLayout
     */
    protected abstract RelativeLayout addTimerLayout();

    /**
     * 计时器滚轮父布局
     *
     * @return View
     */
    protected abstract View timerProgressViewLayout();

    /**
     * 阴影
     *
     * @return WaterClockView
     */
    protected abstract WaterClockView shadowBg();

    /**
     * 开始
     *
     * @return COUIFloatingButton
     */
    protected abstract COUIFloatingButton buttonStart();

    /**
     * 取消
     *
     * @return COUITintImageView
     */
    protected abstract COUITintImageView buttonCancel();

    /**
     * @return ConstraintLayout
     */
    protected abstract ConstraintLayout timerLayout();

    /**
     * 计时器列表
     *
     * @return TimerRecyclerView
     */
    protected abstract TimerRecyclerView timerRecyclerView();

    /**
     * 工具栏
     *
     * @return COUIToolbar
     */
    abstract protected COUIToolbar couiToolbar();

    /**
     * 表盘大小
     *
     * @return 大小
     */
    abstract protected int clockSize();

    /**
     * 设置中间按钮图片
     *
     * @param context
     * @param isStart
     * @return
     */
    public Drawable getMainFabDrawable(Context context, boolean isStart) {
        if (isStart) {
            return context.getDrawable(R.drawable.button_start);
        } else {
            return context.getDrawable(R.drawable.button_pause);
        }
    }
}
