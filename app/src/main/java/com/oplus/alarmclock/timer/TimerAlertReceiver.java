/*
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer;

import static com.oplus.alarmclock.utils.TimerConstant.STOP_TIMERALERT_EXTRA;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Message;
import android.os.SystemClock;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.widget.Toast;

import com.google.gson.JsonObject;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.stopwatch.StopWatchService;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FlashbackUtils;
import com.oplus.clock.common.osdk.SystemPropNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PhonyManagerExtensionKt;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.TimerUtils;
import com.oplus.alarmclock.utils.ToastManager;
import com.oplus.alarmclock.utils.Utils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;


public class TimerAlertReceiver extends BroadcastReceiver {
    public static final String ACTION_STOP_TIMER_RING = "oplus.intent.action.STOP_TIMER_RING_BY_VOLUME_KEY";
    public static final String ACTION_STOP_TIMER_RING_NORMAL = "TIMER_ALERT_STOP_TIMER";
    public static final String ACTION_ENTER_AND_OPEN_TIMER = "com.oplus.alarmclock.alarmclock.enter_and_open_timer";

    private static final boolean DEBUG = true;
    private static final String TAG = "TimerAlertReceiver";
    private static final int REMOVE_PHONE_LISTENER = 106;
    private static final long REMOVE_PHONE_LISTENER_DELAY = DateUtils.MINUTE_IN_MILLIS;
    private static boolean sIsRingStopedByVolumeKey = false;
    private static TelephonyManager sTelephonyManager;
    private static PhoneListenerHandler sHandler;
    private int mInitialCallState;
    private static PhoneStateListener sPhoneStateListener;

    private static class PhoneListenerHandler extends StaticHandler<Context> {

        PhoneListenerHandler(Context ctx) {
            super(ctx);
        }

        protected void handleMessage(Message msg, TimerAlertReceiver receiver) {
            if (msg.what == REMOVE_PHONE_LISTENER) {
                unRegisterListener();
            }
        }
    }

    @Override
    public void onReceive(final Context context, Intent intent) {
        String action = intent.getAction();
        if (DEBUG) {
            Log.d(TAG, "intent.getAction() =" + action + context.getPackageName());
        }
        if (Intent.ACTION_SHUTDOWN.equals(action)) {
            PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    StopWatchService.LAST_TIME_MILLIS_PREFERENCE, System.currentTimeMillis());
            Log.d(TAG, "onReceive: save timemillis");
            PrefUtils.putBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false);
            PrefUtils.putBoolean(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    TimerConstant.TIMER_STATUS_START_PREFERENCE, false);
        }
        if (ACTION_STOP_TIMER_RING.equals(action) || Intent.ACTION_SHUTDOWN.equals(action)) {
            sIsRingStopedByVolumeKey = true;
        } else if (ACTION_STOP_TIMER_RING_NORMAL.equals(action)) {
            unRegisterListener();
        } else {
            sIsRingStopedByVolumeKey = false;
            Log.d(TAG, "onReceive sHandler=" + sHandler);
            if (sHandler == null) {
                sHandler = new PhoneListenerHandler(context);
            }
            sTelephonyManager = (TelephonyManager) context
                    .getSystemService(Context.TELEPHONY_SERVICE);
            if (sTelephonyManager != null) {
                mInitialCallState = PhonyManagerExtensionKt.getTelephonyCallState(sTelephonyManager);
            }

            sPhoneStateListener = new PhoneStateListener() {
                @Override
                public void onCallStateChanged(int state, String ignored) {
                    // The user might already be in a call when the alarm fires. When
                    // we register onCallStateChanged, we get the initial in-call state
                    // which kills the alarm. Check against the initial call state so
                    // we don't kill the alarm during a call.
                    int stateTemp = TelephonyManager.CALL_STATE_IDLE;
                    if (sTelephonyManager != null) {
                        stateTemp = PhonyManagerExtensionKt.getTelephonyCallState(sTelephonyManager);
                    }
                    Log.i(TAG, "onCallStateChanged state = " + state + "stateTemp = " + stateTemp
                            + " sInitialCallState = " + mInitialCallState
                            + " mIsRingStopedByVolumeKey = " + sIsRingStopedByVolumeKey);
                    if ((mInitialCallState != TelephonyManager.CALL_STATE_IDLE)
                            && (stateTemp != TelephonyManager.CALL_STATE_IDLE)
                            && (stateTemp != TelephonyManager.CALL_STATE_RINGING)
                            && !TimerFloatingViewService.getIsTimerStoped()) {
                        //ringing to offHock : restart timer ring
                        Log.i(TAG, "change ring to in call ringtone");
                        mInitialCallState = stateTemp;
                        if (!sIsRingStopedByVolumeKey) {
                            startTimerKlaxon(intent);
                        }
                    } else if ((mInitialCallState == TelephonyManager.CALL_STATE_IDLE)
                            && (stateTemp != TelephonyManager.CALL_STATE_IDLE)) {
                        //idle to ringing : close timer ring
                        Log.i(TAG, "stop timer by phone");
                        sHandler.removeMessages(REMOVE_PHONE_LISTENER);
                        sHandler.sendEmptyMessage(REMOVE_PHONE_LISTENER);
                        TimerFloatingViewService.stopTimer(context);
                        sendMessageToCloseFullScreenAlert(context);
                        stopTimerKlaxon();
                    } else if ((mInitialCallState == TelephonyManager.CALL_STATE_OFFHOOK)
                            && (stateTemp == TelephonyManager.CALL_STATE_IDLE)
                            && !TimerFloatingViewService.getIsTimerStoped()) {
                        //offHock to idle : restart timer ring
                        Log.i(TAG, "change ring back to oringinal");
                        mInitialCallState = stateTemp;
                        if (!sIsRingStopedByVolumeKey) {
                            startTimerKlaxon(intent);
                        }
                    }
                }
            };

            sHandler.removeMessages(REMOVE_PHONE_LISTENER);
            sHandler.sendEmptyMessageDelayed(REMOVE_PHONE_LISTENER, REMOVE_PHONE_LISTENER_DELAY);

            if (TimerService.TIMER_ALERT_ACTION.equals(action) && !Utils.supportConfineMode()) {
                String value = "changeoff";
                try {
                    value = SystemPropNativeUtils.get(
                            SystemPropNativeUtils.OPLUS_CHANGE_THEME,
                            SystemPropNativeUtils.DEFAULT_CHANGE_THEME
                    );
                } catch (Exception e) {
                    try {
                        value = SystemPropNativeUtils.get(
                                SystemPropNativeUtils.OPPO_CHANGE_THEME,
                                SystemPropNativeUtils.DEFAULT_CHANGE_THEME
                        );
                    } catch (Exception ex) {
                        Log.e(TAG, "getLctCity get region error:" + ex);
                    }
                }
                Log.d(TAG, "oppo.change.theme get:" + value);
                if (value.equals("changeon")) {
                    ToastManager.showToast(R.string.alarm_alert_snooze_set, Toast.LENGTH_LONG);
                    return;
                }
                TimerWakeLock.releaseCpuLockPartial();
                TimerWakeLock.acquireCpuWakeLockPartial(context);
                String index = intent.getStringExtra(TimerService.TIMER_INDEX);
                String name = intent.getStringExtra(TimerService.TIMER_NAME);
                String mRingUri = intent.getStringExtra(TimerService.TIMER_RING_URI);
                String mRingName = intent.getStringExtra(TimerService.TIMER_RING_NAME);
                String selectedTimerDes = intent.getStringExtra(TimerService.SELECTED_TIMER_DES);
                if (selectedTimerDes != null) {
                    String[] defaultTimerArray = context.getResources().getStringArray(R.array.default_timer_description);
                    int nameIndex = Integer.parseInt(selectedTimerDes);
                    name = String.valueOf(defaultTimerArray[nameIndex]);
                }
                int userID = intent.getIntExtra(TimerService.TIMER_OWNER_USER_ID, ClockConstant.OWNER_USER_ID);
                Log.d(TAG, "timer index:" + index + " name:" + name);
                if (Utils.isAboveQ()) {
                    Log.d(TAG, "timer id:" + userID + ",current id" + AppPlatformUtils.getCurrentUser());
                }
                boolean isNeedToAlarm = PrefUtils.getBoolean(context,
                        AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_NEED_TO_ALARM_PREFERENCE, false);
                boolean clearedData = !PrefUtils.contains(context,
                        AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, ClockConstant.KEY_TIMER_DATA_CLEAR);
                // in case of Intent.PACKAGE_DATA_CLEARED is sent. Timer should not ring.
                // but AlarmManager still will trigger it. use isStarted to verify if timer is
                // started.
                // Except CTS timer: CTS timer will be set as index[1 to 65526].
                // original timer will be set as index[0];
                if (clearedData || (!isNeedToAlarm)) {
                    return;
                }
                PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerService.TIMER0_FILT_CLOCK, 0);
                updateTimeFlashbackElapsedRealtime(context);
                TimerFloatingViewService.startTimer(context, index, name, userID, mRingUri, mRingName);

            }
            if (sTelephonyManager != null) {
                PhonyManagerExtensionKt.listen(sTelephonyManager, sPhoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
            }
        }
    }


    public void updateTimeFlashbackElapsedRealtime(Context context) {
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            if (context == null) {
                return;
            }
            try {
                JsonObject jsonObject = new JsonObject();
                String timeJson = PrefUtils.getString(context, FlashbackUtils.FLASHBACK_STATE_STORE, TimerService.TIME_FLASHBACK_DATA_JSON, "");
                if ((timeJson == null) || timeJson.equals("")) {
                    return;
                }
                long realtime = SystemClock.elapsedRealtime();
                jsonObject.addProperty(TimerService.REALTIME, realtime);
                jsonObject.addProperty(TimerService.TIME_STATUS, false);
                PrefUtils.putString(context, FlashbackUtils.FLASHBACK_STATE_STORE, TimerService.TIME_FLASHBACK_DATA_JSON, jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private static void unRegisterListener() {
        Log.d(TAG, "remove phone state listener");
        if ((sTelephonyManager != null) && (sPhoneStateListener != null)) {
            PhonyManagerExtensionKt.listen(sTelephonyManager, sPhoneStateListener, PhoneStateListener.LISTEN_NONE);
        }
        sTelephonyManager = null;
        sPhoneStateListener = null;
    }

    private static void startTimerKlaxon(Intent intent) {
        Log.d(TAG, "startTimerKlaxon");
        Context context = AlarmClockApplication.getInstance();
        Intent timerRing = new Intent(context, TimerKlaxon.class);
        timerRing.putExtra(TimerService.TIMER_ALERT_TYPE, 0/* Default:Ring */);
        timerRing.putExtra(TimerService.TIMER_RING_NAME, intent.getStringExtra(TimerService.TIMER_RING_NAME));
        String uriStr = intent.getStringExtra(TimerService.TIMER_RING_URI);
        if (TextUtils.isEmpty(uriStr)) {
            uriStr = TimerUtils.getDefaultTimerRingtoneUri(context).toString();
        }
        if (uriStr != null) {
            timerRing.putExtra(TimerService.TIMER_RINGTONE_URI, uriStr);
            Log.d(TAG, "timerRing: " + timerRing);
        }
        context.startService(timerRing);
    }

    private static void stopTimerKlaxon() {
        Log.d(TAG, "stopTimerKlaxon");
        Context context = AlarmClockApplication.getInstance();
        Intent stopRing = new Intent(context, TimerKlaxon.class);
        context.stopService(stopRing);
    }


    public static void sendMessageToCloseFullScreenAlert(Context context, Boolean closeCard) {
        if (context != null) {
            final Intent intentStopTimer = new Intent(TimerConstant.STOP_TIMERALERT);
            intentStopTimer.putExtra(STOP_TIMERALERT_EXTRA, closeCard);
            LocalBroadcastManager localBroadcastManager = LocalBroadcastManager
                    .getInstance(context);
            localBroadcastManager.sendBroadcast(intentStopTimer);
        }
    }

    public static void sendMessageToCloseFullScreenAlert(Context context) {
        sendMessageToCloseFullScreenAlert(context, true);
    }
}
