/****************************************************************
 ** Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmSnoozeService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: TimerForegroundService
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  yangchenguang  2024/12/11     1.0            build this module
 ****************************************************************/


package com.oplus.alarmclock.timer

import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.lifecycle.LifecycleService
import com.oplus.alarmclock.timer.TimerNotificationManager.TIMER_NOTIFICATION_ID
import com.oplus.alarmclock.utils.NotificationUtils
import com.oplus.alarmclock.utils.TimerUtils
import com.oplus.clock.common.event.LiteEventBus.Companion.instance
import com.oplus.clock.common.utils.Log

class TimerForegroundService : LifecycleService() {
    companion object {
        const val TAG = "TimerForegroundService"
    }

    private var mBinder: IBinder? = TimerForegroundBinder()
    override fun onCreate() {
        liteEventBusState()
        val notificationUtils = NotificationUtils()
        notificationUtils.sendForegroundNotificationByOld(
            this,
            this,
            TIMER_NOTIFICATION_ID,
            NotificationUtils.TIMER
        )
        super.onCreate()
        Log.d(TAG, "onCreate")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy: ")
        TimerUtils.setForegroundServiceStarted(false)
        stopForeground(STOP_FOREGROUND_REMOVE)
        instance.releaseObserver(hashCode().toString())
    }

    inner class TimerForegroundBinder : Binder() {
        fun getService(): TimerForegroundService = this@TimerForegroundService
    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        return mBinder
    }

    private fun liteEventBusState() {
        instance.with(NotificationUtils.STOP_TIMER_FOREGROUND_SERVICE, hashCode().toString())
            .observe(this) {
                Log.d(TAG, "stopSelf: ")
                stopSelf()
            }
    }
}