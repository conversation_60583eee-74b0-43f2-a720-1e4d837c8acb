/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerItemTouchHelperCallBack.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: Ni<PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.util.Log
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.ItemTouchHelper.DOWN
import androidx.recyclerview.widget.ItemTouchHelper.UP
import androidx.recyclerview.widget.ItemTouchHelper.START
import androidx.recyclerview.widget.ItemTouchHelper.END
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.oplus.alarmclock.timer.Adapter.TimerAdapter
import com.oplus.alarmclock.utils.LinearMotorHelper
import kotlin.math.abs

class TimerItemTouchHelperCallBack(private val mAdapter: TimerAdapter?) :
    ItemTouchHelper.Callback() {
    companion object {
        private const val TAG = "TimerItemTouchHelperCallBack"
        private const val TWO = 2
        private const val DRAG_FLAG = UP or DOWN or START or END
    }

    private var mLinearMotorHelper: LinearMotorHelper? = null

    init {
        mAdapter?.run {
            mLinearMotorHelper = LinearMotorHelper(mContext)
        }
    }

    override fun onSwiped(viewHolder: ViewHolder, direction: Int) {
        Log.d(TAG, "onSwiped: ")
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: ViewHolder): Int {
        return makeMovementFlags(DRAG_FLAG, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: ViewHolder,
        target: ViewHolder
    ): Boolean {
        if (viewHolder.itemViewType != target.itemViewType) {
            return false
        }
        mAdapter?.run {
            val targetPosition = target.bindingAdapterPosition
            onItemMove(viewHolder.bindingAdapterPosition, targetPosition)
            vibrate()
        }
        return true
    }


    override fun isLongPressDragEnabled(): Boolean {
        return false
    }

    override fun onSelectedChanged(viewHolder: ViewHolder?, actionState: Int) {
        viewHolder?.run {
            if (this is TimerAdapter.ViewHolder) {
                if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
                    onItemDrop()
                } else {
                    onItemSelected()
                }
            }
        }
    }

    override fun chooseDropTarget(
        selected: ViewHolder,
        dropTargets: List<ViewHolder>,
        curX: Int,
        curY: Int
    ): ViewHolder? {
        val right = curX + selected.itemView.width
        val bottom = curY + selected.itemView.height
        var winner: ViewHolder? = null
        var winnerScore = -1
        val dx = curX - selected.itemView.left
        val dy = curY - selected.itemView.top
        val targetsSize = dropTargets.size
        for (i in 0 until targetsSize) {
            val target = dropTargets[i]
            if (dx > 0) {
                val diff = target.itemView.right - right
                if ((diff < 0) && (target.itemView.right > selected.itemView.right)) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dx < 0) {
                val diff = target.itemView.left - curX
                if ((diff > 0) && (target.itemView.left < selected.itemView.left)) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dy < 0) {
                val diff = target.itemView.top + target.itemView.height / TWO - curY
                if ((diff > 0) && (target.itemView.top + target.itemView.height / TWO < selected.itemView.top)) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dy > 0) {
                val diff = target.itemView.bottom - target.itemView.height / TWO - bottom
                if ((diff < 0) && (target.itemView.bottom - target.itemView.height / TWO > selected.itemView.bottom)) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
        }
        return winner
    }

    fun vibrate() {
        mLinearMotorHelper?.vibrateWeakShortOnce(true)
    }
}