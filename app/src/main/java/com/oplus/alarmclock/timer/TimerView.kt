/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/6/30     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.animation.AnimatorSet
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import android.view.animation.PathInterpolator
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.stopwatch.StopWatchPointer.Companion.TEN
import com.oplus.alarmclock.utils.ChannelManager
import com.oplus.clock.common.utils.Log

class TimerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "TimerView"
        private const val TWO = 2
        private const val ONE_HUNDRED_TWENTY = 250
        private const val ANGLE_INTERVAL = 1.44//120个点，每个点间隔3°
        private const val ANGLE_ALL = 360F
        private const val PATH_ONE = 0.3F
        private const val PATH_TWO = 0F
        private const val PATH_THREE = 0.2F
        private const val PAUSE_PATH_THREE = 0.2F
        private const val PATH_FOUR = 1F
        private const val SIXTY = 60L
        private const val RESTORE_TIME = 500L
        private const val THOUSAND = 1000L
        private const val SCALE_OFFSET_COUNT = 41
        private const val SCALE_OFFSET_COUNT_F = 41F
        private const val DURATION_100 = 100L
        private const val DURATION_800 = 800L
    }

    private var mScaleToTopDistance = 0F
    private var mScaleWidth = 0F
    private var mScaleLength = 0F
    private var mThemeColor = 0
    private var mInitColor = 0
    private var mCenterX = 0F
    private var mCenterY = 0F
    private val mInitPaint by lazy { getPaint(mInitColor, mScaleWidth) }
    private val mThemePaint by lazy { getPaint(mThemeColor, mScaleWidth) }
    private var mTime = 0L
    private var mRemainingTime = 0L
    private var mCurrentSecondAngle = 0f
    private var mCurrentIndex = 0
    private var mInterceptInvalidate = false
    private var mAnimator: ValueAnimator? = null
    private var mAnimatorSet: AnimatorSet? = null
    private var mPauseSecondTime = 0L
    private var mPauseSecondAngle = 0f
    private val mResumeInterpolator by lazy {
        PathInterpolator(PATH_ONE, PATH_TWO, PATH_THREE, PATH_FOUR)
    }
    private val mPauseInterpolator by lazy {
        PathInterpolator(PATH_ONE, PATH_TWO, PAUSE_PATH_THREE, PATH_FOUR)
    }
    private val mGradientColor = mutableMapOf<Int, Int>()
    private val colorEvaluator = ArgbEvaluator()
    private var fadeProgress = 0f

    init {
        initColor(context)
        initSize(context, attrs)
        initGradientColor()
    }

    fun setTotalTime(time: Long) {
        mTime = time
    }

    fun update(remainingTime: Long) {
        if (mTime == 0L) {
            Log.d(TAG, "update e: mTime is 0")
        } else {
            mRemainingTime = if (remainingTime <= 0) 0 else remainingTime
            mCurrentSecondAngle = mRemainingTime * ANGLE_ALL / mTime
            mCurrentIndex = (mCurrentSecondAngle / ANGLE_INTERVAL).toInt()
            if (mInterceptInvalidate) {
                return
            }
            postInvalidate()
        }
    }

    fun onResume(remainingTime: Long) {
        if (mTime == 0L || remainingTime <= 0) {
            resetProperties()
            return
        }
        cancelAnimator()
        mRemainingTime = remainingTime
        mCurrentSecondAngle = mRemainingTime * ANGLE_ALL / mTime
        Log.d(TAG, "onResume mCurrentSecondAngle:$mCurrentSecondAngle, mPauseSecondAngle:$mPauseSecondAngle")
        mCurrentIndex = 0
        if (mPauseSecondAngle == 0F || mCurrentSecondAngle == mPauseSecondAngle) {
            resetProperties()
            return
        }
        val time = getRestoreTime()
        val timeOffset = mRemainingTime - time
        val targetSecondAngle = if (timeOffset >= 0) {
            timeOffset * ANGLE_ALL / mTime
        } else {
            0f
        }
        mAnimator = ValueAnimator.ofFloat(mPauseSecondAngle, targetSecondAngle).apply {
            duration = time
            interpolator = mResumeInterpolator
            addUpdateListener {
                mCurrentSecondAngle = it.animatedValue as Float
                invalidate()
            }
            doOnCancel {
                resetProperties()
                postInvalidate()
            }

            doOnEnd {
                resetProperties()
            }
        }
        mAnimator?.start()
    }

    fun onPause() {
        Log.d(TAG, "onPause mPauseSecondAngle:$mCurrentSecondAngle")
        cancelAnimator()
        cancelAnimatorSet()
        mInterceptInvalidate = true
        mPauseSecondTime = mRemainingTime
        mPauseSecondAngle = mCurrentSecondAngle
    }

    fun pause() {
        if (ChannelManager.getLightOSUtils().isLightOS()) {
            return
        }
        cancelAnimatorSet()
        val fadeInAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = DURATION_100
            interpolator = COUIMoveEaseInterpolator()
            addUpdateListener { animation ->
                fadeProgress = animation.animatedValue as Float
                invalidate()
            }
        }

        val fadeOutAnimator = ValueAnimator.ofFloat(1f, 0f).apply {
            duration = DURATION_800
            interpolator = mPauseInterpolator
            addUpdateListener { animation ->
                fadeProgress = animation.animatedValue as Float
                invalidate()
            }
        }
        mAnimatorSet = AnimatorSet().apply {
            playSequentially(fadeInAnimator, fadeOutAnimator)
            doOnCancel {
                fadeProgress = 0f
                mAnimatorSet = null
            }
            doOnEnd {
                fadeProgress = 0f
                mAnimatorSet = null
            }
            start()
        }
    }

    private fun initColor(context: Context) {
        context.resources?.run {
            mThemeColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorPrimary)
            mInitColor = getColor(R.color.timer_color, null)
        }
    }

    private fun initSize(context: Context, attrs: AttributeSet?) {
        context.resources?.run {
            val multiple = getMultiple(context, attrs)
            mScaleToTopDistance =
                getDimension(R.dimen.app_dial_scale_distance_edge_offset) * multiple
            mScaleWidth = getDimension(R.dimen.app_dial_timer_scale_width) * multiple
            mScaleLength = getDimension(R.dimen.app_dial_scale_length) * multiple
        }
    }

    private fun initGradientColor() {
        ArgbEvaluator().apply {
            for (i in 0 until SCALE_OFFSET_COUNT) {
                val fraction = i / SCALE_OFFSET_COUNT_F
                mGradientColor[i] = evaluate(fraction, Color.WHITE, mThemeColor) as Int
            }
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mCenterX = w.toFloat() / TWO
        mCenterY = h.toFloat() / TWO
    }

    override fun onDraw(canvas: Canvas) {
        canvas.run {
            mCurrentSecondAngle.let {
                for (i in 0 until ONE_HUNDRED_TWENTY) {
                    drawScale(
                        i * ANGLE_INTERVAL,
                        i,
                        it,
                        mCurrentIndex,
                        mCurrentIndex - SCALE_OFFSET_COUNT
                    )
                }
            }
        }
    }

    /**
     * 画刻度
     * */
    private fun Canvas.drawScale(
        angle: Double,
        index: Int,
        secondAngle: Float,
        secondIndex: Int,
        limitIndex: Int
    ) {
        save()
        rotate(angle.toFloat(), mCenterX, mCenterY)
        val paint = if (angle >= secondAngle) {
            mInitPaint
        } else {
            if (index in (limitIndex + 1)..secondIndex) {
                val position = secondIndex - index
                val color = mGradientColor[position]?.let {
                    colorEvaluator.evaluate(fadeProgress, mThemeColor, it) as Int
                }
                color?.let { getPaint(it, mScaleWidth) }
            } else {
                mThemePaint
            }
        }
        paint?.let {
            drawLine(
                mCenterX,
                mScaleToTopDistance,
                mCenterX,
                mScaleToTopDistance + mScaleLength,
                it
            )
        }
        restore()
    }

    private fun getPaint(paintColor: Int, mScaleWidth: Float): Paint {
        return Paint(Paint.ANTI_ALIAS_FLAG).apply {
            isAntiAlias = true
            style = Paint.Style.FILL_AND_STROKE
            strokeCap = Paint.Cap.ROUND
            color = paintColor
            strokeWidth = mScaleWidth
        }
    }

    /**
     * 获取缩放的比例
     * */
    private fun getMultiple(context: Context, attrs: AttributeSet?): Float {
        var multiple = 1F
        context.resources?.run {
            attrs?.let {
                val obtainAttributes = obtainAttributes(attrs, R.styleable.TimerView)
                multiple = obtainAttributes.getFloat(R.styleable.TimerView_multiple, 1F)
            }
        }
        return multiple
    }

    /**
     * 重置属性
     */
    private fun resetProperties() {
        mAnimator = null
        mInterceptInvalidate = false
        mPauseSecondTime = 0L
        mPauseSecondAngle = 0F
    }

    /**
     * 取消动画
     */
    private fun cancelAnimator() {
        mAnimator?.run {
            if (isRunning) {
                cancel()
            }
        }
    }

    private fun cancelAnimatorSet() {
        mAnimatorSet?.run {
            if (isRunning) {
                cancel()
            }
        }
    }

    private fun getRestoreTime(): Long {
        return if (mPauseSecondTime > mRemainingTime) {
            RESTORE_TIME + ((mPauseSecondTime - mRemainingTime) / THOUSAND % SIXTY) * TEN
        } else {
            RESTORE_TIME
        }
    }
}