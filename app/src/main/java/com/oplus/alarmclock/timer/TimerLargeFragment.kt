/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - TimerLargeFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.recyclerview.widget.GridLayoutManager
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.tintimageview.COUITintImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.alarmclock.R
import com.oplus.alarmclock.databinding.TimerMainLargeViewBinding
import com.oplus.alarmclock.timer.ui.TimerAdapterSpaceItemDecoration
import com.oplus.alarmclock.timer.ui.TimerController
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.TimerRecyclerView
import com.oplus.alarmclock.view.water.WaterClockView

class TimerLargeFragment : OplusTimerFragment<TimerMainLargeViewBinding>() {

    override fun layoutId(): Int {
        return R.layout.timer_main_large_view
    }

    override fun createTimerController(index: Int): TimerController {
        return TimerController(index, this)
    }

    override fun initView(inflater: LayoutInflater, group: ViewGroup?) {
        super.initView(inflater, group)
        setLabelTextSize()
        applyConstraintByOrientation()
        mViewBinding?.apply {
            initToolbar(coordinator, worldClockToolbarInclude.toolbar, worldClockToolbarInclude.appBarLayout, null, R.menu.action_menu_icon_all)
        }
        mTimerController?.mOplusTimerPicker?.setBurmeseDiffusion()
    }

    /**
    横竖屏下文字大小需要不一致
     */
    private fun setLabelTextSize() {
        mTimerController?.run {
            val fontScale = mTimerAdd.context.resources.configuration.fontScale
            val textSize = if (isLandscapeScreen()) {
                resources.getDimensionPixelSize(R.dimen.text_size_sp_16)
            } else {
                resources.getDimensionPixelSize(R.dimen.text_size_sp_14)
            }
            Utils.setSuitableFontSize(mTimerAdd, textSize.toFloat(), fontScale, COUIChangeTextUtil.G3)
            Utils.setSuitableFontSize(mTimerLabel, textSize.toFloat(), fontScale, COUIChangeTextUtil.G3)
        }
    }

    override fun onScreenOrientationChanged(orientation: Int) {
        super.onScreenOrientationChanged(orientation)
        applyConstraintByOrientation()
        if (mTimerController != null) {
            onScreenOrientationChanged(isPortraitScreen())
        } else if (mCtsTimerController != null) {
            onScreenOrientationChanged(isPortraitScreen())
        }
    }

    private fun applyConstraintByOrientation() {
        mViewBinding?.apply {
            val set = ConstraintSet()
            if (isLandscapeScreen()) {
                set.clone(context, R.layout.timer_main_large_land_view)
            } else {
                set.clone(context, R.layout.timer_main_large_land_content)
            }
            set.applyTo(timerRootPortContent)
        }
    }

    /**
     * 横竖屏切换
     *
     * @param isPortrait 是否竖屏
     */
    private fun onScreenOrientationChanged(isPortrait: Boolean) {
        mTimerController?.run {
            timerRecyclerView()?.let {
                if (mTimerAdapter != null) {
                    mTimerAdapter.setUiMode(uiMode)
                }
                setLayoutPadding()
                setOsloTimerListPadding()
                if (isPortrait) {
                    if (mTimerAdapterDec != null) {
                        it.removeItemDecoration(mTimerAdapterDec!!)
                        it.addItemDecoration(mTimerAdapterDec!!)
                    } else {
                        it.addItemDecoration(TimerAdapterSpaceItemDecoration().also { its -> mTimerAdapterDec = its })
                    }
                } else {
                    if (mTimerAdapterDec != null) {
                        it.removeItemDecoration(mTimerAdapterDec!!)
                    }
                }
                refreshDynamicViews(false)
            }
            setLabelTextSize()
        }
    }

    /**
     * 排除状态栏高度
     */
    override fun setLayoutPadding() {
        super.setLayoutPadding()
        timerLayout()?.let {
            //排除状态栏高度
            val paddingTop = Utils.getStatusBarHeight(mContext)
            it.setPadding(0, paddingTop, 0, 0)
        }
    }

    /**
     * 平板计时器底部按钮间距
     */
    override fun setComponentBtn() {
        mViewBinding?.apply {
            buttonStart()?.translationX = TimerAnimationManager.mButtonStartTransitionX
        }
    }

    override fun getBtnHorizontalSpacing(): Int {
        return mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_52)
    }

    override fun setOsloTimerListPadding() {
        mTimerController?.run {
            if (UiMode.LARGE_VERTICAL === uiMode) {
                if (mTimerAdapter != null && mTimerAdapter.itemCount == 1) {
                    val listPadding: Int = resources.getDimensionPixelSize(R.dimen.layout_dp_102)
                    setListAndAddMargin(listPadding)
                } else {
                    val listPadding: Int = resources.getDimensionPixelSize(R.dimen.layout_dp_24)
                    setListAndAddMargin(listPadding)
                }
            } else if (UiMode.LARGE_HORIZONTAL === uiMode) {
                addTimerLayout()?.setPadding(0, 0, 0, 0)
                timerRecyclerView()?.setPadding(0, 0, 0, 0)
            }
        }
    }

    override fun setRecyclerViewLayoutManager() {
        super.setRecyclerViewLayoutManager()
        mTimerController?.run {
            timerRecyclerView()?.let {
                if (uiMode == UiMode.LARGE_HORIZONTAL) {
                    val gridLayoutManager = GridLayoutManager(mContext, 2, GridLayoutManager.VERTICAL, false)
                    it.layoutManager = gridLayoutManager
                } else {
                    val gridLayoutManager1 = GridLayoutManager(mContext, 2, GridLayoutManager.VERTICAL, false)
                    it.layoutManager = gridLayoutManager1
                    it.addItemDecoration(TimerAdapterSpaceItemDecoration().also { its -> mTimerAdapterDec = its })
                }
            }
        }
    }

    /**
     * 设置列表和顶部的间距
     *
     * @param listPadding
     */
    private fun setListAndAddMargin(listPadding: Int) {
        addTimerLayout()?.let {
            val timerLayoutPar = it.layoutParams as ViewGroup.MarginLayoutParams
            timerLayoutPar.marginStart = listPadding
            timerLayoutPar.marginEnd = listPadding
            it.layoutParams = timerLayoutPar
        }
        timerRecyclerView()?.let {
            val listLayoutPar = it.layoutParams as ViewGroup.MarginLayoutParams
            listLayoutPar.marginStart = listPadding
            listLayoutPar.marginEnd = listPadding
            it.layoutParams = listLayoutPar
        }
    }

    private fun isLandscapeScreen(): Boolean {
        return UiMode.LARGE_HORIZONTAL == uiMode
    }

    private fun isPortraitScreen(): Boolean {
        return UiMode.LARGE_VERTICAL == uiMode
    }

    override fun timerAdd(): TextView? {
        return mViewBinding?.timerAdd
    }

    override fun oplusTimerPicker(): OplusTimePickerCustomClock? {
        return mViewBinding?.oplusTimerPicker
    }

    override fun timerView(): TimerView? {
        return mViewBinding?.timerView
    }

    override fun timerTextView(): TimerTextView? {
        return mViewBinding?.timerText
    }

    override fun couiToolbar(): COUIToolbar? {
        return mViewBinding?.worldClockToolbarInclude?.toolbar
    }


    override fun titleName(): TextView? {
        return mViewBinding?.what
    }

    override fun addTimerLayout(): RelativeLayout? {
        return mViewBinding?.addTimerLayout
    }

    override fun timerProgressViewLayout(): View? {
        return mViewBinding?.timerProgressViewLayout
    }

    override fun shadowBg(): WaterClockView? {
        return mViewBinding?.timerBg
    }

    override fun buttonStart(): COUIFloatingButton? {
        return mViewBinding?.buttonInclude?.start
    }

    override fun buttonCancel(): COUITintImageView? {
        return mViewBinding?.buttonInclude?.firstComponent
    }

    override fun timerLayout(): ConstraintLayout? {
        return mViewBinding?.timerRootPortContent
    }

    override fun timerRecyclerView(): TimerRecyclerView? {
        return mViewBinding?.situationTimerViewList
    }

    override fun clockSize(): Int {
        return resources.getDimensionPixelSize(R.dimen.layout_dp_333)
    }

    override fun getMainFabDrawable(context: Context, isStart: Boolean): Drawable? {
        return if (isStart) {
            context.getDrawable(R.drawable.button_start_mid)
        } else {
            context.getDrawable(R.drawable.button_pause_mid)
        }
    }
}