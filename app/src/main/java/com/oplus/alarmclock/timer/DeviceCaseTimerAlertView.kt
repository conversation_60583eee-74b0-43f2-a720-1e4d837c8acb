/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DeviceCaseTimerAlertView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/10/13
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin   2022/10/13     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alert.DeviceCaseAlarmAlertView
import com.oplus.alarmclock.utils.*
import com.oplus.alarmclock.utils.ClockOplusCSUtils.EVENT_DEVICE_CASE_TIMER_COUNT
import com.oplus.alarmclock.view.TimerTimeView
import com.oplus.clock.common.utils.Log
import com.oplus.hardware.devicecase.OplusDeviceCaseManager.FLAG_CONTENT_IN_VIEW_PORT

/**
 * 皮套模式响铃
 */
object DeviceCaseTimerAlertView : View.OnClickListener {
    var isShow = false
    private const val TAG = "DeviceCaseAlertView"
    private var mCaseView: View? = null
    private var mTimeView: TimerTimeView? = null

    private var mContext: Context? = null
    private var mCallBack: DeviceCaseCallback? = null

    /**
     * 展示View
     */
    fun showDeviceCaseView(view: View?) {
        if (isAvailable()) {
            Log.i(TAG, "showDeviceCaseView")
            getManager()?.showContentView(view, FLAG_CONTENT_IN_VIEW_PORT)
            mCaseView = view
            //皮套模式响铃埋点
            ClockOplusCSUtils.onCommon(
                AlarmClockApplication.getInstance(),
                EVENT_DEVICE_CASE_TIMER_COUNT
            )
        }
    }

    /**
     * 展示皮套模式计时器view
     */
    fun createDeviceCaseView(
        context: Context,
        timerText: String,
        callBack: DeviceCaseCallback
    ): View? {
        mCallBack = callBack
        val inflater = LayoutInflater.from(context)
        mContext = context
        mCaseView = inflater.inflate(R.layout.timer_device_case_view, null)
        updateView(timerText)
        return mCaseView
    }

    /**
     * 更新view
     */
    fun updateView(timerText: String) {
        mCaseView?.let {
            mTimeView = it.findViewById(R.id.time_view)
            it.findViewById<TextView>(R.id.timer_tv)?.apply {
                text = timerText
            }
            it.findViewById<View>(R.id.btn_lock_close)?.let { close ->
                close.setOnClickListener(this)
                Utils.initPressFeedback(close, close)
            }
        }
        //设置时间
        setAlertTime()
    }

    /**
     * 隐藏皮套模式view
     */
    fun hideDeviceCaseView() {
        mTimeView?.removeAllViews()
        if (!DeviceCaseAlarmAlertView.isShow) {
            getManager()?.hideContentView(mCaseView)
        }
        isShow = false
        Log.i(TAG, "hideDeviceCaseView")
    }

    /**
     * 设置时间和内容
     */
    fun setAlertTime() {
        mTimeView?.setDeviceCaseView()
        //设置头部时间日期星期几
        updateTimeView()
    }

    /**
     * 更新时间
     */
    fun updateTimeView() {
        mTimeView?.update()
    }

    /**
     * 点击事件
     */
    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_lock_close -> {
                Log.i(TAG, "deviceCaseClose")
                mCallBack?.deviceCaseClose()
            }
        }
    }

    interface DeviceCaseCallback {
        fun deviceCaseClose()
    }
}