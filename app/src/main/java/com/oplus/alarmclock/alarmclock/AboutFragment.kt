package com.oplus.alarmclock.alarmclock

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DoubleClickHelper
import com.coui.appcompat.preference.COUIPreferenceFragment

class AboutFragment : COUIPreferenceFragment() {

    companion object {
        private const val TAG = "AboutFragment"
        private const val KEY_OPEN_SOURCE = "key_open_source"
    }

    private val mDoubleClickHelper = DoubleClickHelper()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initPrefs()
        return view
    }

    private fun initPrefs() {
        addPreferencesFromResource(R.xml.alarm_about_prefs)
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        when (preference?.key) {
            KEY_OPEN_SOURCE -> {
                if (mDoubleClickHelper.canClick()) {
                    val intent = Intent(activity, OpenSourceActivity::class.java)
                    startActivity(intent)
                    return true
                }
            }
        }

        return false
    }
}