/*********************************************************************************
 ** Copyright (C), 2008-2030, Oplus, All rights reserved.
 **
 ** File: - AlarmCloseModelUtils.kt
 ** Description:
 **    AlarmCloseModelUtils.
 **
 ** Version: 1.0
 ** Date: 2022-06-30
 ** Author: RongWenYang.Clock
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** RongWenYang    2022-06-30   1.0    Create this module
 ********************************************************************************/

package com.oplus.alarmclock.alarmclock

import android.content.Context
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.provider.settings.SettingDbUtils
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.PrefUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AlarmCloseModelUtils {
    var mCloseModel = CLOSE_MODEL_SLIDE
    var mCloseModelHideRed = false // false显示红点 true不显示红点

    companion object {
        private const val TAG = "AlarmCloseModelUtils"

        const val CLOSE_MODEL_SLIDE = 1 // 滑块模式
        const val CLOSE_MODEL_BUTTON = 0 // 点击模式
        const val SETTING_ALARM_CLOSE_MODEL = "setting_alert_close_model"
        const val SETTING_ALARM_CLOSE_HIDE_RED_DOT = "alert_close_model_red"

        val sInstance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            AlarmCloseModelUtils()
        }
    }

    /**
     * 初始化设置
     */
    fun init(context: Context?) {
        mCloseModel = sInstance.getAlertCloseModel(context)
        mCloseModelHideRed = sInstance.getAlertCloseModelRed(context)
        Log.i(TAG, "init closeModel:$mCloseModel not redDot:$mCloseModelHideRed")
        if (mCloseModel == -1) {
            mCloseModel = CLOSE_MODEL_BUTTON
        } else {
            mCloseModelHideRed = true
        }
    }

    /**
     * 获取闹钟响铃关闭模式
     * @param context
     * @return
     */
     fun getAlertCloseModel(context: Context?): Int {
        val spCloseModel = PrefUtils.getInt(
            context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
            SETTING_ALARM_CLOSE_MODEL, -1
        )
        val closeModel = if (spCloseModel == -1) {
            SettingDbUtils.getAlarmClockModel(context)
        } else {
            spCloseModel
        }
        Log.i(TAG, "getAlertCloseModel sp: $spCloseModel, mode:$closeModel")

        ClockOplusCSUtils.statisticsCloseModel("$spCloseModel$closeModel", context)
        return closeModel
    }

    /**
     * 获取红点是否显示
     * @param context
     * @return
     */
    private fun getAlertCloseModelRed(context: Context?): Boolean {
        return PrefUtils.getBoolean(
            context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
            SETTING_ALARM_CLOSE_HIDE_RED_DOT, false
        )
    }

    /**
     * 更新闹钟响铃关闭模式
     */
    fun updateAlertCloseModel(context: Context?, closeModel: Int) {
        Log.d(TAG, "updateAlertCloseModel closeModel:$closeModel")
        mCloseModel = closeModel
        CoroutineScope(Dispatchers.IO).launch {
            PrefUtils.putInt(
                context,
                AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                SETTING_ALARM_CLOSE_MODEL,
                closeModel,
                true
            )
            SettingDbUtils.setAlarmClockModel(context, closeModel)

            val spSaved = PrefUtils.getInt(
                context,
                AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                SETTING_ALARM_CLOSE_MODEL,
                2
            )
            val dbSaved = SettingDbUtils.getAlarmClockModel(context)
            Log.d(TAG, "updateAlertCloseModel closeModel:$closeModel, sp:$spSaved, db:$dbSaved")
            ClockOplusCSUtils.statisticsCloseModel("$closeModel$spSaved$dbSaved", context)
        }
    }

    /**
     * 更新红点是否显示
     */
    fun updateAlertCloseModelRedDot(context: Context?, hide: Boolean) {
        if (hide) {
            Log.d(TAG, "updateAlertCloseModelRed hide redDot")
        } else {
            Log.d(TAG, "updateAlertCloseModelRed show redDot")
        }

        mCloseModelHideRed = hide
        CoroutineScope(Dispatchers.IO).launch {
            PrefUtils.putBoolean(
                context,
                AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                SETTING_ALARM_CLOSE_HIDE_RED_DOT,
                hide,
                true
            )
        }
    }
}