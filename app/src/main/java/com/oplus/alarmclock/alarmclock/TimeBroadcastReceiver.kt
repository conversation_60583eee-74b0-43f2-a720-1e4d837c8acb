/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimeBroadcastReceiver.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/2/19
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2024/07/24     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.view.WindowManager
import com.oplus.clock.common.utils.Log
import java.lang.ref.WeakReference

class TimeBroadcastReceiver(activity: Activity, panelFragment: AddAlarmPanelFragment) : BroadcastReceiver() {

    companion object {
        private const val TAG = "TimeBroadcastReceiver"
    }

    private var mWeakReference: WeakReference<Activity> = WeakReference(activity)
    private var mPanelFragment: WeakReference<AddAlarmPanelFragment> = WeakReference(panelFragment)

    override fun onReceive(context: Context, intent: Intent) {
        val activity = mWeakReference.get()
        val panelFragment = mPanelFragment.get()
        val action = intent.action
        Log.i(TAG, "Receive action: $action")
        if (Intent.ACTION_SCREEN_OFF == action) {
            if (activity != null && !activity.isFinishing) {
                activity.window.setFlags(0, WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
            }
        } else if (Intent.ACTION_TIME_TICK == action) {
            panelFragment?.getAddAlarmFragment()?.mAddAlarmManager?.updateLeftTimeInfo()
        } else if (Intent.ACTION_TIME_CHANGED == action || Intent.ACTION_TIMEZONE_CHANGED == action) {
            panelFragment?.getAddAlarmFragment()?.mAddAlarmManager?.customAlarmManager?.updateDatePicker()
        }
    }
}
