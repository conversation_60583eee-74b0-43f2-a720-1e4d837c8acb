/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : connects AlarmAlert IntentReceiver to AlarmAlert activity.
 * Passes through Alarm ID.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 * v1.1, 2018-08-15, <PERSON><PERSON><PERSON>, Clean the code.
 ************************************************************/

package com.oplus.alarmclock.alarmclock;

import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.StatusBarUtils;
import com.oplus.alarmclock.utils.Utils;


public class AlarmReceiver extends BroadcastReceiver {

    public static final String NOTIFYCATION_CANCEL_KEY = "notifycation_cancel_key";
    private static final String TAG = "AlarmReceiver";

    private static final String DATABASE_RESTORED_INTENT_OLD = "oppo.intent.action.OPPO_NEW_ALARMCLOCK_INSERTED";
    private static final String DATABASE_RESTORED_INTENT = "oplus.intent.action.OPLUS_NEW_ALARMCLOCK_INSERTED";
    private static final String PLATFORM_CLOCK_BROADCAST_UNBIND_ACTION_OLD = "com.coloros.alarmclock.service.unbind_services";
    private static final String PLATFORM_CLOCK_BROADCAST_UNBIND_ACTION = "com.oplus.alarmclock.service.unbind_services";
    private static final String EXTRA_CHANNEL_NAME = "extra_channel_name";

    public static void dealEnterApk() {
        StatusBarUtils.collapseStatusBar(AlarmClockApplication.getInstance());
    }

    public static void dealEnterApkFromScreen(Intent intent) {
        try {
            boolean isFromNextAlarmNotices = intent.getBooleanExtra(ClockConstant.ALARM_INTENT_CANCEL_NEXT, false);
            Log.i(TAG, "onReceive: ENTER_APK_FROM_SCREEN isFromNextAlarmNotices = " + isFromNextAlarmNotices);

            if (isFromNextAlarmNotices) {
                // entry the apk from next alarm notice
                ClockOplusCSUtils.statisticsNextAlarmNotices(AlarmClockApplication.getInstance(), ClockOplusCSUtils.VALUE_ENTRY_APK_FROM_NEXT_ALARM_NOTICES);
            }
        } catch (Exception e) {
            Log.e(TAG, "onReceive ENTER_APK_FROM_SCREEN : " + e.getMessage());
        }
    }

    public static void dealEnterApkFromNotify(Intent intent) {
        try {
            int missNotificationID = intent.getIntExtra(AlarmClock.ACTION_SEND_MISS_NOTIFICATION_ID, 0);
            if (missNotificationID != 0) {
                String removeScheduleId = Integer.toString(missNotificationID);
                PrefUtils.removeNotification(AlarmClockApplication.getInstance(), null, removeScheduleId, false,false);
            }
        } catch (Exception e) {
            Log.e(TAG, "onReceive ENTER_APK_FROM_NOTIFY : " + e.getMessage());
        }
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        final String action = intent.getAction();
        Log.i(TAG, "Receive action: " + action);

        if (action != null) {
            switch (action) {
                case PLATFORM_CLOCK_BROADCAST_UNBIND_ACTION_OLD:
                case PLATFORM_CLOCK_BROADCAST_UNBIND_ACTION:
                    String channelName = intent.getStringExtra(EXTRA_CHANNEL_NAME);
                    PlatformUtils.getInstance().unregisterListener(channelName);
                    break;
                case ClockConstant.CANCEL_SNOOZE:
                case ClockConstant.CANCEL_SNOOZE_OLD:
                    Log.i(TAG, "onReceive: CANCEL_SNOOZE ");
                    try {
                        // from lockscreen notification end
                        AlarmSchedule schedule = intent.getParcelableExtra(
                                ClockConstant.ALARM_INTENT_EXTRA);
                        boolean isCancelNextAlarm = intent.getBooleanExtra(ClockConstant.ALARM_INTENT_CANCEL_NEXT, false);
                        if (!isCancelNextAlarm) {
                            // from lockscreen notification, when delete notification, will send this intent
                            ClockOplusCSUtils.onEvent(context, ClockOplusCSUtils.STR_PRESS_ALARM_CANCEL_SNOOZE_MENU);
                        } else {
                            // from lockscreen notification, when delete notification, will cancel next alarm notices
                            ClockOplusCSUtils.statisticsNextAlarmNotices(context, ClockOplusCSUtils.VALUE_CANCEL_NEXT_ALARM_NOTICE);
                        }
                        if (schedule != null) {
                            // Delete instance as it is not needed anymore
                            schedule.setAlarmId(schedule.getAlarm().getId());
                            if (isCancelNextAlarm) {
                                final NotificationManager nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                                if (nm != null) {
                                    nm.cancel(AlarmStateManager.AHEAD_REMIND_NOTIFICATION_ID);
                                    Log.d(TAG, "cancel next ahead notification");
                                }
                                long previousAlarmTime = AlarmUtils.getPreviousAlarmTimeForRepeatAndWorkDay(schedule.getAlarm(), schedule.getTime(), "");
                                Log.e(TAG, "isCancelNextAlarm previousAlarmTime previousAlarmTime " + Formatter.formatTime(previousAlarmTime)
                                        + "  schedule.getTime() = " + Formatter.formatTime(schedule.getTime()));
                                AlarmStateManager.setDismissStateCloseOnceNext(context, schedule, schedule.getTime(), previousAlarmTime);
                            } else {
                                AlarmStateManager.setDismissState(context, schedule);
                                Alarm alarm = schedule.getAlarm();
                                if ((alarm != null) && alarm.isRepeatAlarm()) {
                                    PlatformUtils.sUpdateType = PlatformUtils.UPDATE_TYPE_CLOCK_SNOOZE;
                                    IOTUtil.notifyIOTDataChange(alarm);
                                }
                            }
                            AlarmUtils.stopAlarm(context, schedule.getAlarmId());
                            Alarm alarm = schedule.getAlarm();
                            if (alarm != null) {
                                AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.CLOSE_ALARM_LIST);
                            }
                        } else {
                            Log.e(TAG, "CANCEL_SNOOZE: No schedule found!");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "onReceive CANCEL_SNOOZE : " + e.getMessage());
                    }
                    break;
                case DATABASE_RESTORED_INTENT_OLD:
                case DATABASE_RESTORED_INTENT:
                    Log.i(TAG, "onReceive: DATABASE_RESTORED_INTENT ");
                    AlarmUtils.disableAlarmInAlarmManager(context);
                    break;
                case ClockConstant.ALARM_GO_SNOOZE_OLD:
                case ClockConstant.ALARM_GO_SNOOZE:
                    Utils.sendLocalBroadcast(context, ClockConstant.CLOCK_ALARM_SNOOZE_ACTION);
                    break;
                case ClockConstant.ALARM_GO_STOP_OLD:
                case ClockConstant.ALARM_GO_STOP:
                    Intent stopIntent = new Intent(ClockConstant.ALARM_DISMISS_ACTION);
                    stopIntent.putExtra(NOTIFYCATION_CANCEL_KEY, AlarmRingOperateUtils.ALARM_CLOSE_USER_NOTIFICATION);
                    Utils.sendLocalBroadcastWithIntent(context, stopIntent);
                    break;
                case ClockConstant.DELETE_NEXT_ALARM_NOTICE:
                    Log.d(TAG, "DELETE_NEXT_ALARM_NOTICE: user delete the notice for next alarm");
                    // from lockscreen notification, when delete notification, will delete next alarm notices
                    ClockOplusCSUtils.statisticsNextAlarmNotices(context, ClockOplusCSUtils.VALUE_DELETE_NEXT_ALARM_NOTICE);
                    break;
                case ClockConstant.SNOOZE_ALARM_FROM_NOTIFICATION:
                    Utils.sendLocalBroadcast(context, ClockConstant.ALARM_SNOOZE_ACTION);
                    break;
                case ClockConstant.DELETE_ONE_NOTIFICATION:
                    dealEnterApkFromNotify(intent);
                    break;
                default:
                    Log.e(TAG, "Don't care about the action: [" + action + "], Check it.");
                    break;
            }
        }
    }

}
