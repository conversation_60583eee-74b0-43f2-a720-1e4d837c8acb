/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseVBActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/3/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.globalclock.AddCityPanelFragment
import com.oplus.alarmclock.utils.ToastManager

/**
 * 稍后提醒弹出Panel
 * @Date 2022年3月29日17:03:41
 */
class AddAlarmSnoozePanelFragment() : COUIPanelFragment() {

    companion object {
        fun newInstance(alarm: Alarm, call: AddAlarmFragment.SnoozePreferenceCallBack): AddAlarmSnoozePanelFragment {
            var bundle = Bundle().apply {
                putParcelable("alarm", alarm)
            }
            return AddAlarmSnoozePanelFragment().apply {
                arguments = bundle
                mCall = call
            }
        }
    }

    private lateinit var mAlarm: Alarm
    private lateinit var mCall: AddAlarmFragment.SnoozePreferenceCallBack
    private var mSnoozePreferenceFragment: SnoozePreferenceFragment? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        mAlarm = arguments?.getParcelable<Alarm>("alarm")!!
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        initToolbar()
        initPreference()
        initListener()
    }

    /**
     * 初始化状态栏
     */
    private fun initToolbar() {
        hideDragView()
        toolbar?.apply {
            visibility = View.VISIBLE
            title = context.getString(R.string.snooze_alarm)
            isTitleCenterStyle = false
            setTitleTextAppearance(
                requireContext(),
                R.style.TextAppearance_COUI_AppCompatSupport_Toolbar_Title_Panel_Second
            )
            navigationIcon = ContextCompat.getDrawable(
                    context,
                    R.drawable.coui_back_arrow)
            setNavigationOnClickListener {
                (parentFragment as? COUIBottomSheetDialogFragment)?.backToFirstPanel()
                mCall.backTo()
            }
        }
    }

    private fun initPreference() {
        mSnoozePreferenceFragment = SnoozePreferenceFragment.newInstance(mAlarm)
        mSnoozePreferenceFragment?.let {
            childFragmentManager.beginTransaction().replace(contentResId, it).commit()
        }
    }

    /**
     * 设置事件
     */
    private fun initListener() {
        //手势返回
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                (parentFragment as? COUIBottomSheetDialogFragment)?.backToFirstPanel()
                mCall.backTo()
            }
            false
        }
        setPanelDragListener {
            mSnoozePreferenceFragment?.canClosePanel() == true
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(ContextCompat.getColor(
                requireContext(),
                R.color.coui_color_background_elevatedWithCard
            ))
    }
}