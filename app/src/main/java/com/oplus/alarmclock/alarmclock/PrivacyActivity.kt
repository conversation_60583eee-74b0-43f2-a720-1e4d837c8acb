/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - PrivacyActivity.kt
 ** Description: 隐私政策activity
 ** Version: 1.0
 ** Date : 2022/5/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  dengqian  2022/5/11     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.res.Configuration
import android.os.Bundle
import android.view.MenuItem
import android.widget.FrameLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.Utils
import com.oplus.utils.ActivityUtils

class PrivacyActivity : BaseActivity() {
    private var mAppBarLayout: AppBarLayout? = null
    private var mListView: FrameLayout? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_privacy)

        val toolbar = findViewById<COUIToolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar!!.setDisplayHomeAsUpEnabled(true)

        mAppBarLayout = findViewById<AppBarLayout>(R.id.app_bar)
        mListView = findViewById<FrameLayout>(R.id.fragment_container)
        setLayoutPadding(mListView, mAppBarLayout)
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView)
        supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, PrivacyFragment()).commit()
        setFlexibleWindowBg()
        addActivityWeakAndSetOnTouch()
    }

    override fun onDestroy() {
        super.onDestroy()
        ActivityUtils.sSettingActivity.remove(mActivityWeak)
    }

    override fun onScreenFold() {
        super.onScreenFold()
        setLayoutPadding(mListView, mAppBarLayout)
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> finish()
            else -> {
            }
        }
        return true
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        setFlexibleWindowBg()
    }
}