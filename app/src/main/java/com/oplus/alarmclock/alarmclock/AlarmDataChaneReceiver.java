/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AlarmDataChaneReceiver.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2021/01/20
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2021/01/20    1.0            AlarmDataChaneReceiver.java
 *  ***************************************************************
 */

package com.oplus.alarmclock.alarmclock;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;

import com.oplus.alarmclock.provider.AlarmContract;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.List;

public class AlarmDataChaneReceiver extends BroadcastReceiver {

    private static final String TAG = "AlarmDataChaneReceiver";

    @SuppressLint("UnsafeBroadcastReceiverActionDetector")
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "iot intent:" + intent);

        long id = intent.getLongExtra(IOTUtil.ALARM_ID_KEY, -1);
        int associate = intent.getIntExtra(IOTUtil.ENABLE_ASSOCIATE_KEY, IOTUtil.DISABLE_ASSOCIATE);
        List<Alarm> list = getAssociateAlarm(context, id);
        IOTUtil.notifyIOTDataChange(list, associate, id);
    }

    private List<Alarm> getAssociateAlarm(Context context, long alarmId) {
        Cursor cursor = null;
        List<Alarm> list = new ArrayList<>();

        try {
            String selection = ClockContract.Alarm.ID + "=" + alarmId;
            cursor = context.getContentResolver().query(ClockContract.ALARM_CONTENT_URI,
                    AlarmContract.INSTANCE.getQUERY_IOT_COLUMNS(), selection, null, null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    list.add(createAlarmFromCur(cursor));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getAssociateAlarm error:" + e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return list;
    }

    private Alarm createAlarmFromCur(Cursor cursor) {
        Alarm alarm = new Alarm();
        alarm.setId(cursor.getLong(0));
        alarm.setEnabled(cursor.getInt(1) == 1);
        alarm.setEnableAssociate(cursor.getInt(2));
        return alarm;
    }

}
