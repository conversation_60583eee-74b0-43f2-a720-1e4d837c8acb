/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - PrivacyFragment.kt
 ** Description: 隐私政策Fragment
 ** Version: 1.0
 ** Date : 2022/5/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  dengqian  2022/5/11     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.statement.ClockStatementBottomSheetDialog.Companion.openPrivacyPage
import com.oplus.alarmclock.utils.DoubleClickHelper

class PrivacyFragment : COUIPreferenceFragment() {

    companion object {
        private const val KEY_PERSONAL_INFORMATION_PROTECTION = "personal_information_protection"
    }

    private val mDoubleClickHelper = DoubleClickHelper()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initPrefs()
        return view
    }

    private fun initPrefs() {
        addPreferencesFromResource(R.xml.alarm_privacy_prefs)
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        when (preference?.key) {
            KEY_PERSONAL_INFORMATION_PROTECTION -> {
                if (mDoubleClickHelper.canClick()) {
                    openPrivacyPage(activity)
                    return true
                }
            }
        }

        return false
    }
}