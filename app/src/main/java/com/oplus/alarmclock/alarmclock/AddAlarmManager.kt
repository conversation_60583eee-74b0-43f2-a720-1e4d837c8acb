/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AddAlarmManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin    203/5/8     1.0            build this module
 ****************************************************************/
@file:Suppress(
    "LongParameterList",
    "MaximumLineLength",
    "Magic<PERSON>umber",
    "ComplexCondition",
    "LargeClass",
    "ParameterListWrapping",
    "CollapsibleIfStatements"
)

package com.oplus.alarmclock.alarmclock

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Parcelable
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.text.format.DateFormat
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.picker.COUITimeLimitPicker
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.heytap.addon.os.WaveformEffect
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.ClockEventDispatcher.Companion.shouldFinishMain
import com.oplus.alarmclock.R
import com.oplus.alarmclock.RuntimePermissionAlert
import com.oplus.alarmclock.RuntimePermissionAlert.RuntimePermissionCallBack
import com.oplus.alarmclock.alarmclock.AddAlarmFragment.TAB_TYPE_INDEX_2
import com.oplus.alarmclock.alarmclock.AddAlarmFragment.TAB_TYPE_INDEX_3
import com.oplus.alarmclock.alarmclock.AddAlarmSnoozePanelFragment.Companion.newInstance
import com.oplus.alarmclock.alarmclock.WorkDayPanelFragment.Companion.newInstance
import com.oplus.alarmclock.alarmclock.WorkDayPanelFragment.WorkDayPanelBack
import com.oplus.alarmclock.alarmclock.adapter.AddAlarmTypeAdapter
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper.isSupportGarbAlarm
import com.oplus.alarmclock.alarmclock.mini.AlarmMiniEvent
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils.Companion.instance
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils
import com.oplus.alarmclock.alarmclock.utils.AlarmSetStaticHandler
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_WORK_TYPE
import com.oplus.alarmclock.utils.*
import com.oplus.alarmclock.utils.ChannelManager.getChannelUtils
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.alarmclock.utils.HapticsStyleController.getTactileStepless
import com.oplus.alarmclock.utils.HapticsStyleController.getTactileStyle
import com.oplus.alarmclock.utils.LoopAlarmUtils.deleteLoopAlarm
import com.oplus.alarmclock.view.ReboundScrollView
import com.oplus.alarmclock.view.WeekPicker.OnWeekPickerClickListener
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.Log.d
import com.oplus.clock.common.utils.Log.i
import com.oplus.clock.common.utils.Log.w
import com.oplus.clock.common.utils.VersionUtils
import com.oplus.clock.common.utils.loadAsync
import com.oplus.clock.common.utils.then
import com.oplus.os.WaveformEffect.EFFECT_RINGTONE_NOVIBRATE
import java.util.Calendar


class AddAlarmManager(val viewHolder: AddAlarmViewHolder, val mContext: Context) {

    companion object {
        const val TAG = "AddAlarmManager"
        const val SP_NAME_SET_ALARM_RINGTONE = "set_alram_ringtone"
        const val MAX_ALARM_NAME_LENGTH = 40
        const val HOUR_IN_DAY = 24
        const val MIN_IN_HOUR = 60
        const val UPDATE_TIME_INFO_DELAY = 100
        const val TOAST_DELAYED_TIME = 250
        const val SET_BUTTON_CLICK_DELAY_TIME = 500
        const val WAITE_ANIMATION_MILLISECOND: Long = 120
        const val KEY_SAVED_ALARM = "saved_alarm"
        const val KEY_WORK_DAY_TYPE = "work_day_type"
        const val KEY_SAVED_ALARM_TEMP = "saved_alarm_temp"
        const val KEY_SAVED_CUSTOM_TAB = "is_saved_custom_tab"
        const val KEY_SHOW_SNOOZE_PANEL = "is_show_snooze_panel"
        const val KEY_SHOW_WORK_TIPS = "is_show_work_tips"
        const val KEY_SHOW_WORK_DAY_TIPS = "is_show_work_day_tips"
        const val KEY_SHOW_LOOP_PANEL = "is_show_loop_panel"
        const val KEY_SHOW_LOOP_PREFERENCE_PANEL = "is_show_loop_preference_panel"
        const val KEY_HAVE_EDIT_ALARM_PANEL = "have_edit_alarm_panel"
        const val KEY_GARB_ALARM_DATE = "key_garb_alarm_date"
        const val KEY_GARB_ALARM_RING = "key_garb_alarm_ring"
    }


    val mToastHandler = Handler(Looper.getMainLooper())
    private val mInputFilters = arrayOf<InputFilter>(
        InputFilter.LengthFilter(MAX_ALARM_NAME_LENGTH)
    )

    /**
     * 轮班闹钟临时变量，用户计算编辑后周期是否变化
     */
    var mTempLoopAlarm: Alarm? = null

    var mWorkdDayPanel: WorkDayPanelFragment? = null

    /**
     * Handler
     */
    var mHandlerThread: HandlerThread? = null
    var mAlertNone: String? = null
    var mAlertArray: Array<String>
    var mDefaultSummary: String? = null
    var mOriginalAlert: Uri? = null
    var mTimerParams: LinearLayout.LayoutParams? = null
    var mAlarmTypeParams: LinearLayout.LayoutParams? = null
    var mDoubleClickHelper = DoubleClickHelper()
    var workdayManage: AddWorkAlarmManager = AddWorkAlarmManager(this, mContext)
    var garbAlarmManager: GarbAlarmManager = GarbAlarmManager(this, mContext)
    var customAlarmManager: AddCustomAlarmManager = AddCustomAlarmManager(this, mContext)
    var alarmRingManager: AddAlarmRingManager = AddAlarmRingManager(this, mContext)
    var mAlarmWorkdayTypeTemp = 0
    var mAlatmTimesDialog: AlertDialog? = null

    /**
     * 闹钟临时变量，用于判断是否有编辑
     */
    private var mAlarmWorkdaySwitchTemp = 0
    private var mTextWatcher: TextWatcher = object : TextWatcherStub() {
        override fun afterTextChanged(nameStr: Editable) {
            val label = nameStr.toString()
            viewHolder.apply {
                if (mAlarm != null) {
                    mAlarm.label = label
                }
            }
        }
    }

    /**
     * 初始化
     */
    init {
        val resources = mContext.resources
        mAlertArray = resources.getStringArray(R.array.oplus_AlertType)
        mAlertNone = resources.getString(R.string.alert_no_ring)
        mDefaultSummary = resources.getString(R.string.default_alarm_summary)
    }

    /**
     * 初始化数据
     *
     * @param view
     */
    fun initData(intent: Intent) {
        viewHolder.apply {
            i(TAG, " initData")
            mHandler = AlarmSetStaticHandler(mFragment)
            setListener()
            mAnimatorUtil = AlarmAnimatorUtil()
            if (mHandlerThread == null) {
                mHandlerThread = HandlerThread("set_alarm_thread")
                mHandlerThread!!.start()
            }
            alarmRingManager.initHandler()
            mFragment.activity?.let {
                if (!it.isFinishing) {
                    mIsFromScreen =
                        intent.getBooleanExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, false)
                    if (mIsFromScreen) {
                        it.window.setFlags(
                            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED,
                            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                        )
                    }
                }
            }
            getAlarmInstance(intent)
            setHolidaySwitch()
            customAlarmManager.setAlarmHoliday()
            workdayManage.initWork()

            initPref()
            initUI()
            //申请权限后进入铃音界面
            mRuntimePermissionAlert =
                RuntimePermissionAlert(mFragment.activity, object : RuntimePermissionCallBack {
                    override fun doAfterGranted(isNeedOpenAddView: Boolean) {
                        i(TAG, " doAfterGranted")
                        alarmRingManager.enterAlarmRing()
                    }

                    override fun doAfterDenieD() {
                        d(TAG, "doAfterDenieD")
                    }

                    override fun onExitClick() {}
                    override fun onClickOutside() {
                    }
                })
            setShowPanel()
        }
    }

    /**
     * 设置闹铃重复日期和节假日开关
     */
    private fun setHolidaySwitch() {
        viewHolder.apply {
            if (mAlarm.id >= 0 && (mAlarm.repeatSet > 0 || !DatePickerUtils.isEmptySpecialDay(mAlarm.getmSpecialAlarmDays()))
                && mAlarm.getmGarbSwitch() != 1
            ) {
                mAlarmRepeatSet = mAlarm.repeatSet
                mAlarmHolidaySwitch = mAlarm.holidaySwitch
            } else {
                mAlarmRepeatSet = PrefUtils.getInt(
                    mContext,
                    PrefUtils.ALARM_WEEK_PICK,
                    PrefUtils.ALARM_WEEK_PICK_KEY,
                    RepeatSet.REPEAT_DAILY
                )
                mAlarmHolidaySwitch = PrefUtils.getInt(
                    mContext,
                    PrefUtils.ALARM_HOLIDAY_SWITCH,
                    PrefUtils.ALARM_HOLIDAY_SWITCH_KEY,
                    ClockConstant.ALARM_HOLIDAY_SWITCH_OFF
                )
            }
        }
    }

    /**
     * 是否显示面板
     */
    private fun setShowPanel() {
        viewHolder.apply {
            if (mIsShowSnoozePanel) {
                showSnoozePanle(true)
            }
        }
    }

    fun updateMargin() {
        viewHolder.apply {
            updatePrefLayoutParams()
            val timerhorizontalMargin =
                mContext.resources.getDimensionPixelOffset(R.dimen.add_alarm_activity_timepicker_margin)
            mContext.resources.getDimensionPixelOffset(R.dimen.color_preference_titel_padding_start)
            val alarmTypehorizontalMargin =
                mContext.resources.getDimensionPixelOffset(R.dimen.color_preference_titel_padding_start)
            mTimerParams?.leftMargin = timerhorizontalMargin
            mTimerParams?.rightMargin = timerhorizontalMargin
            mAlarmTypeParams?.leftMargin = alarmTypehorizontalMargin
            mAlarmTypeParams?.rightMargin = alarmTypehorizontalMargin
            mOplusTimePicker.layoutParams = mTimerParams
            mAlarmTypeTabList.layoutParams = mAlarmTypeParams
            mOplusTimePicker.invalidate()
            mAlarmTypeTabList.invalidate()
        }
    }

    /**
     * 设置事件
     */
    fun setListener() {
        viewHolder.run {
            mRingLayout.setOnClickListener(mFragment)

            mVibrate.setOnCheckedChangeListener(mFragment)
            mVibrateLayout.setOnClickListener(mFragment)
            mVibrateTypeLayout.setOnClickListener(mFragment)
            mSnoozeLayout.setOnClickListener(mFragment)
            mWorkDayLayout.setOnClickListener(mFragment)
            mRingTypeLayout.setOnClickListener(mFragment)
            mAlarmRepeatMore.setOnClickListener(mFragment)
            mAlarmHolidayLayout.setOnClickListener(mFragment)
            mHolidaySwitch.setOnCheckedChangeListener(mFragment)
            mScrollPanel.setOnScrollListener(object : ReboundScrollView.OnScrollListener {
                override fun onScroll(l: Int, t: Int, oldl: Int, oldt: Int) {
                    mLoopAlarmTips?.let {
                        if (mLoopAlarmTips.isShowing) {
                            mLoopAlarmTips.refreshWhileLayoutChange()
                        }
                    }
                    mWorkDayAlarmTips?.let {
                        if (mWorkDayAlarmTips.isShowing) {
                            mWorkDayAlarmTips.refreshWhileLayoutChange()
                        }
                    }
                }

                override fun stopScroll() {}
                override fun dispatchTouchEvent(event: MotionEvent, conflictRect: Rect): Boolean {
                    if (Utils.isViewContains(event, mAlarmTypeTabList)) {
                        return true
                    }
                    var result: Boolean = Utils.isViewContains(event, mAlarmLabel)
                    if (!result) {
                        hideSoftInput()
                        mAlarmLabel.clearFocus()
                    }
                    if (mOplusTimePicker != null) {
                        mOplusTimePicker.getGlobalVisibleRect(conflictRect)
                    }
                    if (conflictRect.top > 0) {
                        result = Utils.isViewContains(event, mOplusTimePicker)
                    }
                    return result
                }
            })
            //周选择器点击
            mWeekPick.setOnWeekPickerClickListener(object : OnWeekPickerClickListener {
                override fun onClick(view: View, day: Int, isCheck: Boolean) {
                    customAlarmManager.onWeekPickClick(day, isCheck)
                }
            })
        }
    }

    fun updatePrefLayoutParams() {
        viewHolder.apply {
            val params = mLayoutPrefs.layoutParams as LinearLayout.LayoutParams
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            mLayoutPrefs.layoutParams = params
        }
    }

    /**
     * 弹出稍后提醒Panle
     */
    fun showSnoozePanle(resetPanel: Boolean) {
        viewHolder.apply {
            if (mFragment.parentFragment is COUIPanelFragment && !mIsShowSnoozePanel || resetPanel) {
                val cou = mFragment.parentFragment as COUIPanelFragment
                if (cou.parentFragment is COUIBottomSheetDialogFragment) {
                    if (cou.parentFragmentManager.isStateSaved) {
                        return
                    }
                    val si = cou.parentFragment as? COUIBottomSheetDialogFragment
                    si?.replacePanelFragment(newInstance(mAlarm) {
                        mIsShowSnoozePanel = false
                        setAlarmSnoozeAndRingNum()
                    })
                    mIsShowSnoozePanel = true
                }
            }
        }
    }

    /**
     * 展示工作日类型面板
     * @param isShowLoopPreference 是否跳转轮班闹钟详情页
     */
    fun showWorkPanel(isShowLoopPreference: Boolean) {
        viewHolder.apply {
            if (mFragment.parentFragment is COUIPanelFragment) {
                val cou = mFragment.parentFragment as COUIPanelFragment
                if (cou.parentFragment is COUIBottomSheetDialogFragment) {
                    val si = cou.parentFragment as? COUIBottomSheetDialogFragment
                    if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                        //外销直接进入轮班闹钟详情页面
                        si?.replacePanelFragment(
                            LoopDayPanelFragment.newInstance(
                                mAlarm,
                                mReloadAlarm
                            ) {
                                Log.d(TAG, "LoopDayPanelFragment:onBackPressed")
                            })
                    } else {
                        mWorkdDayPanel = newInstance(
                            mAlarm,
                            mReloadAlarm,
                            isShowLoopPreference,
                            object : WorkDayPanelBack {
                                override fun backTo(workType: Int) {
                                    if (workType >= 0) {
                                        //刷新工作日类型
                                        mAlarm.setmWorkDayType(workType)
                                        workdayManage.workDayBack(mAlarm.getmWorkDayType())
                                    }
                                }
                            })
                        si?.replacePanelFragment(mWorkdDayPanel)
                    }
                }
            }
        }
    }

    /**
     * 初始化TimePicker，涉及震动、监听
     */
    private fun initUI() {
        viewHolder.apply {
            i(TAG, " initUI")
            initAlarmTypeTab()
            mOplusTimePicker.setTextVisibility(false)
            if (mAlarm != null) {
                mOplusTimePicker.currentHour = mAlarm.hour % HOUR_IN_DAY
                mOplusTimePicker.currentMinute = mAlarm.minutes % MIN_IN_HOUR
            }
            d("AlarmMiniEvent:${AlarmMiniEvent.mTimePickerHour}")
            //miniApp时间接续
            if (AlarmMiniEvent.mTimePickerHour != -1) {
                mOplusTimePicker.currentHour = AlarmMiniEvent.mTimePickerHour
                mOplusTimePicker.currentMinute = AlarmMiniEvent.mTimePickerMinute
                mTimePickerMinute = AlarmMiniEvent.mTimePickerMinute
                mTimePickerHour = AlarmMiniEvent.mTimePickerHour
                AlarmMiniEvent.mTimePickerHour = -1
                AlarmMiniEvent.mTimePickerMinute = -1
            }
            if (DeviceUtils.isHapticFeedBackSupport(mContext)) {
                mOplusTimePicker.setVibrateIntensity(getTactileStepless(mContext))
                mOplusTimePicker.setVibrateLevel(getTactileStyle(mContext))
            }
            //时间滚轮事件监听
            mOplusTimePicker.setOnTimeChangedListener(COUITimeLimitPicker.OnTimeChangedListener { _: COUITimeLimitPicker?, hourOfDay: Int, minute: Int ->
                if (!mIsSaveClicked) {
                    mTimePickerMinute = minute
                    mTimePickerHour = hourOfDay
                    mHandler.removeMessages(AddAlarmViewHolder.MESSAGE_WHAT_UPDATE_TIME_INFO)
                    mHandler.sendMessageDelayed(
                        mHandler.obtainMessage(
                            AddAlarmViewHolder.MESSAGE_WHAT_UPDATE_TIME_INFO,
                            hourOfDay,
                            minute
                        ), UPDATE_TIME_INFO_DELAY.toLong()
                    )
                }
            })
            setPreferenceValueDelay()
            val fontScale = mContext.resources.configuration.fontScale
            Utils.setSuitableFontSize(mAlarmLeftTime, fontScale, COUIChangeTextUtil.G2)
            mTimerParams = mOplusTimePicker.layoutParams as LinearLayout.LayoutParams
            mAlarmTypeParams = mAlarmTypeTabList.layoutParams as LinearLayout.LayoutParams
            if (DeviceUtils.isExpVersion(mContext) && getChannelUtils().getChannel() == IBaseChannel.CHANNEL_WPLUS) {
                //一加外销展示日历闹钟入口
                mAlarmRepeatMore.visibility = View.VISIBLE
            }
        }
    }

    private fun initAlarmTypeTab() {
        viewHolder.apply {
            mAlarmTypeTabList.apply {
                adapter = null
                itemAnimator = null
                setHasFixedSize(true)
                val arr = ArrayList<String>().apply {
                    add(context.getString(R.string.ring_once))
                    add(context.getString(R.string.ring_on_weekdays))
                    add(context.getString(R.string.grab_alarm_title))
                    add(context.getString(R.string.customize))
                }
                var isShowRedHot = true
                if (!isSupportGarbAlarm()) {
                    arr.remove(context.getString(R.string.grab_alarm_title))
                } else {
                    isShowRedHot = PrefUtils.getBoolean(
                        mContext,
                        PrefUtils.GARB_ALARM_TIPS,
                        PrefUtils.GARB_ALARM_TIPS_KEY,
                        false
                    )
                }
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                mAlarmTypeTabAdapter = AddAlarmTypeAdapter(context).apply {
                    showRedPoint(isShowRedHot)
                    setListener(mFragment)
                    updateData(arr)
                }
                adapter = mAlarmTypeTabAdapter
                addOnScrollListener(object : RecyclerView.OnScrollListener() {
                    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                        super.onScrollStateChanged(recyclerView, newState)
                        dimissLoopAlarmTips()
                    }
                })
            }
        }
    }

    private fun setPreferenceValueDelay() {
        viewHolder.apply {
            mCanClickSaveOrCancel = true
            if (mAlarm != null) {
                if (mAlarm.isSilent) {
                    d(TAG, "mRingPref setSummary mAlertNone :$mAlertNone")
                    mRingSummary.text = mAlertNone
                } else {
                    d(TAG, "mRingPref setSummary getRingName :" + mAlarm.ringName)
                    if (AlarmRingUtils.isDefaultRing(mAlarm.alert)) {
                        mRingSummary.text = mContext.resources.getString(R.string.default_ringtone)
                    } else {
                        mRingSummary.text = mAlarm.ringName
                    }
                }
            }
            updateTime()
            updateAlarmTypeTab()
        }
    }

    private fun updateTime() {
        viewHolder.apply {
            if (mAlarm != null) {
                mAlertTypeItem = mAlarm.alertType
                updateAlertType(mAlertTypeItem)
            }
        }
    }

    private fun updateAlertType(iAlertItem: Int) {
        viewHolder.apply {
            mRingTypeSummary.text = mAlertArray[iAlertItem]
        }
    }

    /**
     * 更新时间选择器触感风格
     * @param tactileStyle  0-清脆；1-柔和
     */
    fun updateVibrateLevel(tactileStyle: Int) {
        if (viewHolder.mOplusTimePicker != null) {
            viewHolder.mOplusTimePicker.setVibrateLevel(tactileStyle)
        }
    }

    /**
     * 更新时间选择器触感强度
     * @param tactileStepless  0-清脆；1-柔和
     */
    fun updateVibrateIntensity(tactileStepless: Float) {
        if (viewHolder.mOplusTimePicker != null) {
            viewHolder.mOplusTimePicker.setVibrateIntensity(tactileStepless)
        }
    }

    /**
     * 当前选中的tab
     */
    fun getCurrentSelectedItem(): Int {
        viewHolder.mAlarmTypeTabAdapter?.let {
            return it.getCurrentSelectedItem()
        }
        return 0
    }

    /**
     * 编辑闹铃时更新Tab
     */
    private fun updateAlarmTypeTab() {
        viewHolder.apply {
            if (mAlarm != null && mAlarmTypeTabAdapter != null) {
                i(TAG, "updateAlarmTypeTab workdayTab " + mAlarm.workdaySwitch)
                //界面销毁时是否选择了自定义tab
                var isSavedCustomTab = false
                mFragment.activity?.let { its ->
                    its.intent?.let {
                        isSavedCustomTab = it.getBooleanExtra(KEY_SAVED_CUSTOM_TAB, false)
                    }
                }
                mAlarmLabel.setText(mAlarm.label)
                val workdayTab = mAlarm.workdaySwitch == ClockConstant.ALARM_WORKDAY_SWITCH_ON
                val loopAlarm = mAlarm.getmLoopSwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_ON
                val garbAlarm = mAlarm.getmGarbSwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_ON
                if (workdayTab || loopAlarm) {
                    isInitTab = true
                    mAlarmTypeTabAdapter?.setSelectedIndex(1)
                } else if (garbAlarm) {
                    isInitTab = true
                    mAlarmTypeTabAdapter?.setSelectedIndex(AddAlarmFragment.TAB_TYPE_INDEX_2)
                } else if (mAlarm.repeatSet == 0 && !isSavedCustomTab && DatePickerUtils.SPLIT == mAlarm.getmSpecialAlarmDays()) {
                    mAlarmTypeTabAdapter?.setSelectedIndex(AddAlarmFragment.TAB_TYPE_INDEX_0)
                } else {
                    isInitTab = true
                    mAlarmTypeTabAdapter?.setSelectedIndex(TAB_TYPE_INDEX_3)
                    //自定义tab需要测量高度以便执行动画
                    if (getCurrentSelectedItem() == TAB_TYPE_INDEX_3) {
                        mAnimatorUtil.isMeasuredCst = true
                    }
                    if (!isSupportGarbAlarm() && getCurrentSelectedItem() == TAB_TYPE_INDEX_2) {
                        mAnimatorUtil.isMeasuredCst = true
                    }
                }
            }
        }
    }

    /**
     * 获取闹钟信息
     *
     * @param intent
     */
    private fun getAlarmInstance(intent: Intent) {
        viewHolder.run {
            val bundle: Bundle? = mFragment.arguments
            mFragment.arguments?.let {
                d(TAG, "getAlarmInstance bundle not null")
                mIsReload = true
                val alarm = it.getParcelable<Alarm>(KEY_SAVED_ALARM)
                if (alarm != null) {
                    intent.putExtra(AlarmClockFragment.ALARM_MODIFY, alarm)
                    intent.putExtra(ClockConstant.ALARM_ID, alarm.id)
                    intent.putExtra(
                        ClockConstant.KEY_IS_NEW_WHEN_RESTORE,
                        it.getBoolean(ClockConstant.KEY_IS_NEW_WHEN_RESTORE)
                    )
                    intent.putExtra(KEY_SAVED_CUSTOM_TAB, it.getBoolean(KEY_SAVED_CUSTOM_TAB))
                    mReloadAlarm = it.getParcelable<Alarm>(KEY_SAVED_ALARM_TEMP)
                    mIsShowSnoozePanel = it.getBoolean(KEY_SHOW_SNOOZE_PANEL)
                    mIsNeedShowWorkTips = it.getBoolean(KEY_SHOW_WORK_TIPS)
                    mIsNeedShowWorkDayTips = it.getBoolean(KEY_SHOW_WORK_DAY_TIPS)
                    mWorkDayType = it.getInt(KEY_WORK_DAY_TYPE)
                    garbAlarmManager.mGarbAlarmDate =
                        DatePickerUtils.getTimeForAfter1970(it.getLong(KEY_GARB_ALARM_DATE))
                    garbAlarmManager.mGarbAlarmRingString =
                        it.getString(KEY_GARB_ALARM_RING).toString()
                }
            }
            val alarmId = intent.getLongExtra(ClockConstant.ALARM_ID, -1L)
            mAlarmCount = intent.getIntExtra(ClockConstant.ALARM_COUNT, -1)
            i(
                TAG, "getAlarmInstance: alarmId: " + alarmId
                        + ", mAlarmCount: " + mAlarmCount + ", mAlarm: "
                        + intent.getParcelableExtra(AlarmClockFragment.ALARM_MODIFY)
            )
            if (alarmId >= 0) {
                // edit an exist alarm.
                mAlarm =
                    intent.getParcelableExtra<Parcelable>(AlarmClockFragment.ALARM_MODIFY) as Alarm
                mPreAlarm = mAlarm.clone()
                //保存当前闹钟数据，判断是否修改
                mAlarmWorkdaySwitchTemp = mAlarm.workdaySwitch
                mAlarmWorkdayTypeTemp = mAlarm.getmWorkDayType()
                mIsNewAlarm = intent.getBooleanExtra(ClockConstant.KEY_IS_NEW_WHEN_RESTORE, false)
                //默认闹钟，名称适配多语言
                if (mAlarm.getmDefaultAlarm() == 1) {
                    mAlarm.label = mContext.resources.getString(R.string.wake_up_alarm)
                }
                mAlarmLabel.setText(mAlarm.label)
                //编辑闹钟，设置稍后提醒
                setAlarmSnoozeAndRingNum()
            } else {
                createNewAlarm(intent)
            }
            alarmRingManager.setAutoRingNameAndUri(mAlarm)
            mTimePickerHour = mAlarm.hour
            mTimePickerMinute = mAlarm.minutes
            mOriginalAlert = mAlarm.alert
            // CTS
            if (mSkipUi) {
                mFragment.activity?.let {
                    if (!it.isFinishing) {
                        it.finish()
                    }
                }
            }
        }
    }

    /**
     * 新建闹钟
     */
    private fun createNewAlarm(intent: Intent) {
        viewHolder.apply {
            // create a new alarm.
            val alarm = Alarm()
            setAlarmExtras(alarm, intent)
            //默认铃声
            val dfltAlarmUri = AlarmRingUtils.getDefaultRingtoneUri(mContext, true)
            // save default alarm uri the first time.
            val dfltRingUri: String? = alarmRingManager.getDefaultRingUri()
            if (TextUtils.isEmpty(dfltRingUri) && dfltAlarmUri != null && !TextUtils.isEmpty(
                    dfltAlarmUri.toString()
                )
            ) {
                alarmRingManager.setDefaultRingUri(dfltAlarmUri.toString())
            }
            if (dfltAlarmUri == null) {
                AlarmRingStatisticUtils.statisticsAlarmException(
                    AlarmClockApplication.getInstance(),
                    AlarmRingStatisticUtils.EVENT_NO_DEFAULT_ALARM_RINGTONE, null, null
                )
            }
            alarm.alert = dfltAlarmUri
            d(
                TAG, "getAlarmInstance: Default alarm alert uri: " + dfltAlarmUri
                        + ", dfltRingUri uri: " + dfltRingUri
            )
            mAlarm = alarm
            alarmRingManager.setAutoRingNameAndUri(mAlarm)
            mIsNewAlarm = true
            //默认振动
            val defaultVibrate = AlarmRingUtils.getDefaultVibrate(mContext)
            mAlarm.vibrate = defaultVibrate
            //设置稍后提醒内容
            setSnoozeTime()
        }
    }

    /**
     * 设置闹铃参数
     *
     * @param alarm
     * @param intent
     */
    private fun setAlarmExtras(alarm: Alarm, intent: Intent) {
        viewHolder.run {
            val bundle = intent.extras
            i(TAG, "bundle: $bundle")
            if (bundle == null) {
                return
            }
            val hour = bundle.getInt(AlarmClock.EXTRA_HOUR, -1)
            val minutes = bundle.getInt(AlarmClock.EXTRA_MINUTES, -1)
            val message = bundle.getString(AlarmClock.EXTRA_MESSAGE)
            d(TAG, "hour: $hour minutes: $minutes message: $message")
            if (hour >= 0) {
                alarm.hour = hour
            }
            if (minutes >= 0) {
                alarm.minutes = minutes
            }
            if (!TextUtils.isEmpty(message)) {
                alarm.label = message
            }
            mSkipUi = intent.getBooleanExtra(android.provider.AlarmClock.EXTRA_SKIP_UI, false)
            val alert = intent.getStringExtra(android.provider.AlarmClock.EXTRA_RINGTONE)
            val repeatSet: Int = customAlarmManager.getDaysFromIntent(intent)
            alarm.setRepeat(repeatSet)
            if (alert != null && alert == ClockConstant.ALARM_ALERT_SILENT) {
                alarm.isSilent = true
            }
            alarm.snoonzeItem = ClockConstant.SNOOZE_SWITCH_ON_5_MIN
        }
    }

    /**
     * 设置稍后提醒内容
     */
    private fun setAlarmSnoozeAndRingNum() {
        viewHolder.run {
            if (mAlarm.snoonzeItem == ClockConstant.SNOOZE_SWITCH_ON_5_MIN) {
                mSnoozePosition = getSnoozeListIndex(mAlarm.getmSnoozeTime())
                mRingNumPosition = getRingNumListIndex(mAlarm.ringNum)
                setSnoozeTime()
            } else {
                val snoozeStr = mContext.getString(R.string.oplus_snooze_switch)
                mSnoozeTime.text = mContext.getString(R.string.add_alarm_snooze_close)
                mSnoozeLayout.contentDescription = snoozeStr + mSnoozeTime.text
            }
        }
    }

    private fun initPref() {
        viewHolder.apply {
            i(TAG, " initPref")
            if (mAlarm != null) {
                mVibrate.isChecked = mAlarm.vibrate != WaveformEffect.EFFECT_RINGTONE_NOVIBRATE
            } else {
                mVibrate.isChecked = false
            }
            //设置铃声与震动
            alarmRingManager.setRingToneText()
            mAlarmLabel.clearFocus()
            initLabelEditText()
            //alertType
            if (DeviceUtils.isCmccVersion(mContext)) {
                mAlarmLabel.setText(R.string.default_alarm_summary)
            } else {
                mRingTypeLayout.setVisibility(View.GONE)
            }
        }
    }

    private fun initLabelEditText() {
        viewHolder.apply {
            mAlarmLabel.setHint(R.string.alarm_name)
            mAlarmLabel.isSingleLine = true
            if (mTextWatcher != null) {
                mAlarmLabel.addTextChangedListener(mTextWatcher)
            }
            mAlarmLabel.filters = mInputFilters
        }
    }

    /**
     * 获取稍后提醒时间的下标
     *
     * @param snoozeTime
     * @return
     */
    private fun getSnoozeListIndex(snoozeTime: Int): Int {
        for (i in AlarmRepeat.REPEAT_ALERT_INTERVAL.indices) {
            if (snoozeTime == AlarmRepeat.REPEAT_ALERT_INTERVAL[i]) {
                return i
            }
        }
        return 0
    }

    /**
     * 获取响铃次数的下标
     *
     * @param ringNum
     * @return
     */
    private fun getRingNumListIndex(ringNum: Int): Int {
        for (i in AlarmRepeat.REPEAT_ALERT_NUM.indices) {
            if (ringNum == AlarmRepeat.REPEAT_ALERT_NUM[i]) {
                return i
            }
        }
        return 0
    }

    /**
     * 设置稍后提醒文本内容
     */
    private fun setSnoozeTime() {
        viewHolder.run {
            mContext.let {
                val snoozeStr = it.resources.getQuantityString(
                    R.plurals.add_alarm_interval_minutes,
                    AlarmRepeat.REPEAT_ALERT_INTERVAL[mSnoozePosition],
                    AlarmRepeat.REPEAT_ALERT_INTERVAL[mSnoozePosition]
                )
                val ringNumStr = it.resources.getQuantityString(
                    R.plurals.add_alarm_interval_frequency,
                    AlarmRepeat.REPEAT_ALERT_NUM[mRingNumPosition],
                    AlarmRepeat.REPEAT_ALERT_NUM[mRingNumPosition]
                )
                val snoozeTextStr = mContext.getString(R.string.oplus_snooze_switch)
                val text = String.format(
                    it.resources.getString(R.string.add_alarm_interval_minutes_and_frequency),
                    snoozeStr,
                    ringNumStr
                )
                mSnoozeTime.text = text
                mSnoozeLayout.contentDescription = snoozeTextStr + text
            }
        }
    }

    /**
     * 更新顶部提示响铃时间
     */
    fun updateLeftTimeInfo() {
        viewHolder.run {
            if (mOplusTimePicker != null && mAlarm != null) {
                var hour: Int = mOplusTimePicker.currentHour
                var minute: Int = mOplusTimePicker.currentMinute
                if (mAlarm.getmLoopSwitch() == ALARM_HOLIDAY_SWITCH_ON) {
                    //轮班闹钟使用闹钟时间
                    hour = mAlarm.hour
                    minute = mAlarm.minutes
                }
                updateLeftTimeInfo(
                    mAlarm.id,
                    hour,
                    minute,
                    mAlarm.repeatSet,
                    mAlarm.workdaySwitch,
                    mAlarm.holidaySwitch,
                    mAlarm.getmWorkdayUpdateTime(),
                    mAlarm.getmWorkDayType(),
                    mAlarm.getmSpecialAlarmDays(),
                    mAlarm.getmLoopSwitch(),
                    mAlarm.getmLoopCycleDays(),
                    mAlarm.getmLoopWorkDays(),
                    mAlarm.getmLoopDay(),
                    mAlarm.getmLoopRestDays(),
                    mAlarm.loopAlarmList,
                    mAlarm.getmGarbSwitch()
                )
            }
        }
    }


    /**
     * 异步更新下次响铃时间提示文本
     *
     * @param hour
     * @param minute
     * @param repeatSet
     * @param workdaySwitch
     * @param holidaySwitch
     * @param workdayUpdate
     * @param workType
     */
    fun updateLeftTimeInfo(
        id: Long,
        hour: Int,
        minute: Int,
        repeatSet: Int,
        workdaySwitch: Int,
        holidaySwitch: Int,
        workdayUpdate: Long,
        workType: Int,
        specDays: String?,
        loopSwitch: Int,
        loopCycleDays: Int,
        loopWorkDays: Int,
        loopDay: Int,
        loopResetDays: String,
        loopAlarmList: List<Alarm>,
        garbSwitch: Int
    ) {
        viewHolder.run {
            val alarm = Alarm()
            alarm.id = id
            alarm.setRepeat(repeatSet)
            alarm.hour = hour
            alarm.minutes = minute
            alarm.workdaySwitch = workdaySwitch
            alarm.holidaySwitch = holidaySwitch
            alarm.setmWorkdayUpdateTime(workdayUpdate)
            alarm.setmWorkDayType(workType)
            alarm.setmSpecialAlarmDays(specDays)
            alarm.setmLoopSwitch(loopSwitch)
            alarm.setmLoopCycleDays(loopCycleDays)
            alarm.setmLoopWorkDays(loopWorkDays)
            alarm.setmLoopDay(loopDay)
            alarm.setmLoopRestDays(loopResetDays)
            alarm.loopAlarmList = loopAlarmList
            alarm.setmGarbSwitch(garbSwitch)
            if (garbSwitch == 1) {
                garbAlarmManager.parserGarbAlarmRingText()
                alarm.loopAlarmList = garbAlarmManager.mGarbAlarmRingTimeArray
                alarm.setmSpecialAlarmDays(
                    DatePickerUtils.todayAfter1970days(garbAlarmManager.mGarbAlarmDate).toString()
                )
                alarm.setmLoopRestDays(garbAlarmManager.mGarbAlarmRingString)
                garbAlarmManager.setGarbAlarmNextRing(
                    AlarmUtils.getElapsedTimeUntilAlarmTime(alarm),
                    alarm
                )
            }
            mFragment.loadAsync {
                //用于计算并更新稍后提醒时间
                AlarmUtils.getElapsedTimeUntilAlarmDescription(
                    AlarmClockApplication.getInstance(),
                    alarm,
                    true
                )
            } then {
                mAlarmLeftTime.text = it
            }
        }
    }

    /**
     * 更新响铃信息
     */
    fun updateDescription(daysSet: Int, isUpdateAlarm: Boolean) {
        viewHolder.run {
            var description: String? = ""
            mHolidaySwitchTextView.text = ""
            if (mOplusTimePicker != null && isUpdateAlarm) {
                mAlarm.hour = mOplusTimePicker.currentHour
                mAlarm.minutes = mOplusTimePicker.currentMinute
            }
            mOnceClock = false
            val isSpec = mAlarm.getmSpecialAlarmDays().split(DatePickerUtils.SPLIT.toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().size == 2
            if (DatePickerUtils.isEmptySpecialDay(mAlarm.getmSpecialAlarmDays()) || isSpec) {
                val isExpire: Boolean = customAlarmManager.isExpireTime()
                if (isExpire && daysSet == 0) {
                    val planets: Array<String> =
                        mContext.resources.getStringArray(R.array.global_timezone_day_offset)
                    description = planets[1]
                    mOnceClock = true
                } else {
                    description = RepeatSet.getDescription(
                        mContext,
                        daysSet,
                        mAlarm.workdaySwitch,
                        mAlarm.holidaySwitch,
                        true,
                        mAlarm,
                        true
                    )
                }
            } else {
                val des = AlarmUtils.getCustomAlarmDateDescription(
                    mAlarm,
                    mContext,
                    false,
                    AlarmUtils.ALARM_DESCRIPTION_MAX,
                    Calendar.getInstance()
                )
                description = des
                if (mAlarm.holidaySwitch == ClockConstant.ALARM_HOLIDAY_SWITCH_ON && !DeviceUtils.isExpVersion(
                        mContext
                    )
                ) {
                    mHolidaySwitchTextView.text = mContext.getString(R.string.exclude_holiday)
                }
            }
            mRepeatDes.text = description
        }
    }

    /**
     * 设置通用属性布局margin
     *
     * @param height
     */
    fun setCommonLayoutMargin(height: Int) {
        viewHolder.apply {
            val lp = mCommonLayout.layoutParams as LinearLayout.LayoutParams
            lp.topMargin = height
            mCommonLayout.layoutParams = lp
        }
    }

    /**
     * 切换至响一次闹钟
     */
    fun onTabRingOnce(isClick: Boolean) {
        viewHolder.apply {
            mGarbAlarmDateLayout?.visibility = View.GONE
            mGarbAlarmRingLayout?.visibility = View.GONE
            mGarbAlarmDescription?.visibility = View.GONE
            mSnoozeLayout.visibility = View.VISIBLE
            setItemCardBackground()
            COUICardListHelper.setItemCardBackground(
                mAlarmLabelLayout,
                COUICardListHelper.getPositionInGroup(3, 0)
            )
            dimissLoopAlarmTips()
            LiteEventBus.instance.send(LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE, false)
            mLoopAlarmListLayout.visibility = View.GONE
            mTimePickerLayout.visibility = View.VISIBLE
            setCommonLayoutMargin(0)
            if (isClick && !mIsReload) {
                //执行动画
                mAnimatorUtil.changeToRingOnce(mCustomLayout, mWorkDayLayout)
            } else {
                mWorkDayLayout.visibility = View.GONE
                mCustomLayout.visibility = View.GONE
                mIsReload = false
                mAnimatorUtil.changeToRingOnce()
            }
            mAlarm.apply {
                setRepeat(0)
                workdaySwitch = 0
                holidaySwitch = 0
                setmLoopSwitch(0)
                setmGarbSwitch(0)
                setmSpecialAlarmDays(DatePickerUtils.SPLIT)
                hour = mOplusTimePicker.currentHour
                minutes = mOplusTimePicker.currentMinute
            }
            hideSoftInput()
            updateLeftTimeInfo()
            if (mCurrentView != AddAlarmViewHolder.VIEW_MONTH_DAY) {
                customAlarmManager.changToMonthDay(AddAlarmViewHolder.VIEW_MONTH_DAY)
            }
        }
    }

    /**
     * 保存页面数据
     *
     * @return
     */
    fun getAlarmWhenSaveInstanceState(): Bundle {
        viewHolder.apply {
            mAlarm.hour = mTimePickerHour
            mAlarm.minutes = mTimePickerMinute
            if (customAlarmManager.isTabCustomSelected()) {
                mAlarm.setRepeat(customAlarmManager.getDaysSet())
            }
            //特殊日期
            if (mAlarmDatePicker != null) {
                mAlarm.setmSpecialAlarmDays(mAlarmDatePicker.curSpecialDays)
            }
            i(TAG, " save alarm  :$mAlarm")
            val bundle = Bundle()
            bundle.putParcelable(KEY_SAVED_ALARM, mAlarm)
            bundle.putInt(KEY_WORK_DAY_TYPE, workdayManage.mWorkdayTypePosition)
            bundle.putBoolean(ClockConstant.KEY_IS_NEW_WHEN_RESTORE, mIsNewAlarm)
            bundle.putBoolean(KEY_SAVED_CUSTOM_TAB, customAlarmManager.isTabCustomSelected())
            bundle.putBoolean(KEY_SHOW_SNOOZE_PANEL, mIsShowSnoozePanel)
            bundle.putLong(
                KEY_GARB_ALARM_DATE,
                DatePickerUtils.todayAfter1970days(garbAlarmManager.mGarbAlarmDate)
            )
            bundle.putString(KEY_GARB_ALARM_RING, garbAlarmManager.mGarbAlarmRingString)
            mLoopAlarmTips?.let {
                //tips是否展示
                bundle.putBoolean(KEY_SHOW_WORK_TIPS, mLoopAlarmTips.isShowing)
            }
            mWorkDayAlarmTips?.let {
                bundle.putBoolean(KEY_SHOW_WORK_DAY_TIPS, mWorkDayAlarmTips.isShowing)
            }
            mWorkdDayPanel?.let {
                bundle.putBoolean(KEY_SHOW_LOOP_PREFERENCE_PANEL, it.isShowLoopPreferencePanel())
                if (it.isShowLoopPreferencePanel()) {
                    //轮班闹钟详情页alarm对象
                    val tempAlarm = it.saveTempAlarm()
                    tempAlarm?.let { tAlarm ->
                        bundle.putParcelable(KEY_SAVED_ALARM_TEMP, tAlarm)
                        bundle.remove(KEY_SAVED_ALARM)
                        bundle.putParcelable(KEY_SAVED_ALARM, mAlarm)
                        bundle.putInt(KEY_WORK_DAY_TYPE, LOOP_ALARM_WORK_TYPE)
                    }
                }
            }
            bundle.putBoolean(KEY_HAVE_EDIT_ALARM_PANEL, true)
            return bundle
        }
    }

    /**
     * 提交保存
     */
    fun confirm() {
        viewHolder.run {
            if (!canSave()) {
                return
            }
            i(TAG, "confirm click save menu,mCanClickSaveOrCancel: $mCanClickSaveOrCancel")
            if (mIsNewAlarm) {
                if (mAlarmCount == -1) {
                    mAlarmCount = AlarmUtils.getAlarmsCount(mContext)
                }
                if (mAlarmCount >= AlarmClockFragment.MAX_ALARM_COUNT) {
                    ToastManager.showToast(
                        mContext.getString(R.string.shortcut_alarm_num_reach_max),
                        Toast.LENGTH_LONG
                    )
                    mFragment.closeAlarmSetSetPage()
                    return
                }
            }
            //默认闹钟修改后改为常规闹钟
            if (mAlarm.getmDefaultAlarm() == 1) {
                mAlarm.label = mAlarmLabel.text.toString()
            }
            mAlarm.setmDefaultAlarm(0)
            saveAlarm()
            dimissLoopAlarmTips()
        }
    }

    private fun saveAlarm() {
        viewHolder.run {
            if (mCanClickSaveOrCancel) {
                mIsSaveClicked = true
                mCanClickSaveOrCancel = false
                when (ALARM_HOLIDAY_SWITCH_ON) {
                    mAlarm.getmLoopSwitch() -> {
                        //保存轮班闹钟
                        asyncLoopAlarm()
                    }

                    mAlarm.getmGarbSwitch() -> {
                        //保存秒抢闹钟
                        garbAlarmManager.asyncGarbAlarm()
                    }

                    else -> asyncSaveResult()
                }
                mFragment.closeAlarmSetSetPage()
                //保存闹钟后需要关闭流体云卡片
                GarbAlarmSeedlingHelper.closeSingleSeedlingCard(mContext, mAlarm.id)
            }
        }
    }

    /**
     * 是否可以保存
     */
    private fun canSave(): Boolean {
        viewHolder.run {
            if (mAlarm.getmGarbSwitch() == 1) {
                var context = mContext
                mFragment?.context?.let {
                    context = it
                }
                return garbAlarmManager.canSaveGarbAlarm(mAlarm, context)
            }
            if (mOnceClock) {
                //选择时间已过期
                ToastManager.showToast(
                    mFragment.activity,
                    mContext.getString(R.string.add_alarm_clock_time_expire)
                )
                return false
            }
            if (!AddAlarmFragment.sCanClickDelay) {
                w(TAG, "confirm fragment is pause, Click is invalid!")
                return false
            }
            if (!mCanClickSaveOrCancel) {
                mFragment.closeAlarmSetSetPage()
                return false
            }
            if (!mDoubleClickHelper.canClick()) {
                return false
            }
        }
        return true
    }

    /**
     * 关闭轮班闹钟tips
     */
    fun dimissLoopAlarmTips() {
        viewHolder.run {
            mLoopAlarmTips?.let {
                if (mLoopAlarmTips.isShowing) {
                    mLoopAlarmTips.dismiss()
                    mLoopAlarmTips = null
                }
            }
            dismissWorkDayTips()
        }
    }

    fun dismissWorkDayTips() {
        viewHolder.run {
            mWorkDayAlarmTips?.let {
                if (mWorkDayAlarmTips.isShowing) {
                    mWorkDayAlarmTips.dismiss()
                    mWorkDayAlarmTips = null
                }
            }
        }
    }


    /**
     * 隐藏软键盘
     */
    fun hideSoftInput() {
        viewHolder.apply {
            val imm = mContext.getSystemService(
                Context.INPUT_METHOD_SERVICE
            ) as InputMethodManager
            if (imm != null && mAlarmLabel != null) {
                imm.hideSoftInputFromWindow(mAlarmLabel.windowToken, 0)
            }
        }
    }

    /**
     * 取消
     */
    fun cancel() {
        viewHolder.apply {
            if (!AddAlarmFragment.sCanClickDelay) {
                w(TAG, "cancel fragment is pause, Click is invalid!")
                return
            }
            if (!mCanClickSaveOrCancel) {
                mFragment.closeAlarmSetSetPage()
                return
            }
            if (!mDoubleClickHelper.canClick()) {
                return
            }
            i(TAG, "cancel click cancel menu,mCanClickSaveOrCancel: $mCanClickSaveOrCancel")
            if (mCanClickSaveOrCancel) {
                mCanClickSaveOrCancel = false
                mFragment.finishWithAction()
            }
            dimissLoopAlarmTips()
        }
    }

    /**
     * 更新轮班闹钟列表
     */
    private fun updateLoopAlarmList() {
        kotlin.runCatching {
            Log.i(TAG, "updateLoopAlarmList")
            viewHolder.apply {
                val conStr = LoopAlarmUtils.contrastAlarmList(
                    mAlarm?.loopAlarmList,
                    mTempLoopAlarm?.loopAlarmList
                )
                updateNowLoopAlarm(conStr)
                //从其他类型闹钟改为轮班轮班闹钟，直接新增子闹钟
                if (mTempLoopAlarm == null) {
                    mAlarm?.loopAlarmList?.let { alarmList ->
                        for (alarm in alarmList) {
                            alarm.setmLoopID(mAlarm.id.toInt())
                            LoopAlarmUtils.saveSubLoopAlarm(mContext, alarm)
                        }
                    }
                }
                //轮班闹钟修改子闹钟数据
                mTempLoopAlarm?.let {
                    val tempSize = it.loopAlarmList.size
                    val loopSize = mAlarm.loopAlarmList.size
                    Log.i(TAG, "tempSize:$tempSize  loopSize:$loopSize")
                    addLoopAlarmList(tempSize, loopSize)
                    removeLoopAlarmList(tempSize, loopSize, it)
                }
            }
        }
    }

    /**
     * 减少轮班周期
     */
    private fun removeLoopAlarmList(tempSize: Int, loopSize: Int, alarm: Alarm) {
        viewHolder.run {
            if (tempSize > loopSize) {
                for (index in loopSize until tempSize) {
                    val alarm = alarm.loopAlarmList[index]
                    //需要删除关联闹钟
                    LoopAlarmUtils.deleteAlarm(mContext, alarm)
                }
            }
        }
    }

    /**
     * 增加轮班周期
     */
    private fun addLoopAlarmList(tempSize: Int, loopSize: Int) {
        viewHolder.run {
            if (tempSize < loopSize) {
                for (index in tempSize until loopSize) {
                    val alarm = mAlarm.loopAlarmList[index]
                    alarm.setmLoopID(mAlarm.id.toInt())
                    LoopAlarmUtils.saveSubLoopAlarm(mContext, alarm)
                }
            }
        }
    }

    /**
     * 更新已有的子闹钟
     */
    private fun updateNowLoopAlarm(conStr: String) {
        viewHolder.run {
            if (!TextUtils.isEmpty(conStr)) {
                val changeLis = conStr.split(DatePickerUtils.SPLIT)
                changeLis.let {
                    for (change in changeLis) {
                        if (!TextUtils.isEmpty(change)) {
                            val alarm = mAlarm.loopAlarmList[Integer.parseInt(change)]
                            //更新已有的子闹钟
                            AlarmUtils.updateAlarmInfo(
                                AlarmClockApplication.getInstance(),
                                alarm,
                                true
                            )
                        }
                    }
                }
            }
        }
    }

    /**
     * 保存轮班闹钟
     */
    private fun asyncLoopAlarm() {
        viewHolder.apply {
            if (mAlarm.label == null) {
                mAlarm.label = ""
            }
            mAlarm.setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
            mAlarm.isEnabled = true
            Log.d(TAG, "mAlarm:$mAlarm")
            mFragment.activity?.let { act ->
                if (!act.isFinishing) {
                    act.loadAsync {
                        val success = if (mIsNewAlarm) {
                            LoopAlarmUtils.addLoopAlarm(
                                act,
                                mAlarm,
                                true,
                                mAlarm.loopAlarmList,
                                false
                            )
                        } else {
                            AlarmUtils.updateAlarmInfo(
                                AlarmClockApplication.getInstance(),
                                mAlarm,
                                true
                            )
                            updateLoopAlarmList()
                            AlarmUtils.enableAlarm(
                                AlarmClockApplication.getInstance(),
                                mAlarm,
                                false
                            )
                        }
                        saveLoopAlarmSuccess(success)
                        success
                    }
                }
            }
        }
    }

    /**
     * 保存轮班闹钟成功
     */
    private fun saveLoopAlarmSuccess(result: Boolean) {
        viewHolder.apply {
            if (result) {
                //保存轮班闹钟类型
                PrefUtils.putInt(
                    AlarmClockApplication.getInstance(),
                    PrefUtils.ALARM_WORKDAY_SWITCH,
                    PrefUtils.ALARM_WORKDAY_SWITCH_KEY,
                    LOOP_ALARM_WORK_TYPE
                )
                //弹出toast
                var context = AlarmClockApplication.getInstance().applicationContext
                if (mFragment != null) {
                    context = mFragment.context
                }
                mToastHandler.postDelayed(
                    { AlarmUtils.popAlarmSetToast(context, mAlarm) },
                    TOAST_DELAYED_TIME.toLong()
                )
                //埋点
                ClockOplusCSUtils.onCommon(
                    AlarmClockApplication.getInstance(),
                    ClockOplusCSUtils.EVENT_LOOP_ALARM_SAVE
                )
            }
        }
    }

    /**
     * 异步提交保存
     */
    private fun asyncSaveResult() {
        viewHolder.apply {
            if (mAlarm.label == null) {
                mAlarm.label = ""
            }
            mAlarm.apply {
                isEnabled = true
                alertType = mAlertTypeItem
                hour = mTimePickerHour
                minutes = mTimePickerMinute
                setmLoopSwitch(0)
                setmLoopCycleDays(0)
                setmLoopID(-1)
                setmLoopWorkDays(0)
                setmLoopDay(1)
                setmLoopAlarmNumber(0)
                setmLoopRestDays(DatePickerUtils.SPLIT)
            }

            //保存的非轮班闹钟，删除此闹钟的轮班闹钟关联
            if (!mIsNewAlarm) {
                deleteLoopAlarm(mContext, mAlarm)
            }
            //选中的特殊日期
            if (mAlarmDatePicker != null) {
                mAlarm.setmSpecialAlarmDays(mAlarmDatePicker.curSpecialDays)
            }
            mFragment.activity?.let { act ->
                if (!act.isFinishing) {
                    act.loadAsync {
                        saveResult(mAlarm, mIsNewAlarm)
                    } then {
                        saveAlarmInfo(mIsNewAlarm, mAlarm, it, mPreAlarm)
                        //闹钟埋点
                        alarmStatistics(mAlarm, mIsNewAlarm)
                        //保存工作日类型
                        putAlarmPre(mAlarm)
                        shouldFinish(act)
                    }
                }
            }
        }
    }

    /**
     * 是否关闭页面
     */
    private fun shouldFinish(act: Activity) {
        if (shouldFinishMain(act.intent)) {
            act.finish()
        } else if (AiTripHelper.isFromAiTrip(act)) {
            AiTripHelper.setAlarmDone(act)
        }
    }

    /**
     * 保存工作日类型
     */
    private fun putAlarmPre(mAlarm: Alarm) {
        if (mAlarm.workdaySwitch == 1) {
            //保存工作日类型
            PrefUtils.putInt(
                AlarmClockApplication.getInstance(),
                PrefUtils.ALARM_WORKDAY_SWITCH,
                PrefUtils.ALARM_WORKDAY_SWITCH_KEY,
                mAlarm.getmWorkDayType()
            )
        }
    }

    /**
     * 闹钟埋点
     */
    private fun alarmStatistics(mAlarm: Alarm, mIsNewAlarm: Boolean) {
        //日历闹钟埋点
        if (DatePickerUtils.SPLIT != mAlarm.getmSpecialAlarmDays() && mIsNewAlarm) {
            if (mAlarm.repeatSet != 0) {
                ClockOplusCSUtils.statisticsCreateCalendarAlarm(ClockOplusCSUtils.CALENDAR_REPEAT_ALARM)
            } else {
                ClockOplusCSUtils.statisticsCreateCalendarAlarm(ClockOplusCSUtils.CALENDAR_ALARM)
            }
        }
        viewHolder.apply {
            if (mAlarm.alert != null && isSupportSettingDefaultAlarm) {
                //铃声埋点
                val defaultRing = AlarmRingUtils.getDefaultRingtoneUri(mContext, true)
                if (defaultRing != null && TextUtils.equals(
                        mAlarm.alert.toString(),
                        defaultRing.toString()
                    )
                ) {
                    ClockOplusCSUtils.statisticsRingSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_DEFAULT)
                } else if (TextUtils.equals(
                        ClockConstant.ALARM_ALERT_SILENT,
                        mAlarm.alert.toString()
                    )
                ) {
                    ClockOplusCSUtils.statisticsRingSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_NULL)
                } else {
                    ClockOplusCSUtils.statisticsRingSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_NOT_DEFAULT)
                }
                //振动埋点
                if (FoldScreenUtils.isRealOslo() || DeviceUtils.isLinearmotoSupport(mContext)) {
                    val defaultVibrate = AlarmRingUtils.getDefaultVibrate(mContext)
                    if (defaultVibrate == mAlarm.vibrate) {
                        ClockOplusCSUtils.statisticsVibrateSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_DEFAULT)
                    } else if (mAlarm.vibrate == EFFECT_RINGTONE_NOVIBRATE) {
                        ClockOplusCSUtils.statisticsVibrateSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_NULL)
                    } else {
                        ClockOplusCSUtils.statisticsVibrateSetting(ClockOplusCSUtils.RING_AND_VIBRATE_TYPE_NOT_DEFAULT)
                    }
                }
            }
        }
    }

    private fun saveAlarmInfo(mIsNewAlarm: Boolean, mAlarm: Alarm, it: Boolean, mPreAlarm: Alarm?) {
        if (mIsNewAlarm) {
            instance.addAlarmInfo(System.currentTimeMillis(), mAlarm)
        } else {
            instance.editAlarmInfo(
                System.currentTimeMillis(),
                if (mPreAlarm == null) mAlarm else mPreAlarm,
                mAlarm
            )
        }
        if (it) {
            if (FbeRingUtils.checkFBESupport(mContext) && !mIsNewAlarm && mOriginalAlert != null && mAlarm.alert != null
                && !TextUtils.equals(mOriginalAlert.toString(), mAlarm.alert.toString())
            ) {
                FbeRingUtils.deleteInternalRingFile(mContext, mOriginalAlert)
            }
        }
    }

    private fun saveResult(alarm: Alarm, isNewAlarm: Boolean): Boolean {
        var success = false
        if (isNewAlarm) {
            success = AlarmUtils.addNewAlarm(
                AlarmClockApplication.getInstance(),
                alarm,
                alarm.isEnabled
            ) > 0
        } else {
            //是否更新数据库工作日类型时间
            var isUpdateWorkdayTime = false
            if (mAlarmWorkdaySwitchTemp != alarm.workdaySwitch) {
                isUpdateWorkdayTime = true
            }
            if (mAlarmWorkdayTypeTemp != alarm.getmWorkDayType()) {
                isUpdateWorkdayTime = true
            }
            AlarmUtils.updateAlarmInfo(
                AlarmClockApplication.getInstance(),
                alarm,
                isUpdateWorkdayTime
            )
            // Alarm is set enabled after edit.
            success = AlarmUtils.enableAlarm(AlarmClockApplication.getInstance(), alarm, false)
        }
        d(TAG, "saveResult isNewAlarm = $isNewAlarm  alarm = $alarm")
        AlarmRingStatisticUtils.statisticsAlarmInfo(
            AlarmClockApplication.getInstance(), alarm,
            if (isNewAlarm) ClockOplusCSUtils.ALARM_INFO_TYPE_add else ClockOplusCSUtils.ALARM_INFO_TYPE_EDIT
        )
        i(TAG, "Save Alarm[New: $isNewAlarm]: $alarm success:$success")
        if (success) {
            //弹出toast
            if (alarm.isEnabled) {
                var context = AlarmClockApplication.getInstance().applicationContext
                if (viewHolder.mFragment != null) {
                    context = viewHolder.mFragment.context
                }
                mToastHandler.postDelayed(
                    { AlarmUtils.popAlarmSetToast(context, alarm) },
                    TOAST_DELAYED_TIME.toLong()
                )
            }
            if (alarm.repeatSet != 0) {
                //保存用户上次选择的周数据
                PrefUtils.putInt(
                    AlarmClockApplication.getInstance(),
                    PrefUtils.ALARM_WEEK_PICK,
                    PrefUtils.ALARM_WEEK_PICK_KEY,
                    alarm.repeatSet
                )
            }
            if (alarm.repeatSet != 0 || DatePickerUtils.SPLIT != alarm.getmSpecialAlarmDays()) {
                //自定义闹钟保存节假日开关状态
                PrefUtils.putInt(
                    AlarmClockApplication.getInstance(),
                    PrefUtils.ALARM_HOLIDAY_SWITCH,
                    PrefUtils.ALARM_HOLIDAY_SWITCH_KEY,
                    alarm.holidaySwitch
                )
            }
            //统计闹钟工作日类型和工作日闹钟的数量
            ClockOplusCSUtils.setWorkdayTypeEvent(AlarmClockApplication.getInstance())
        }
        return success
    }

    fun onDestroy() {
        viewHolder.apply {
            workdayManage.onDestroy()
            mAlarmLabel.removeTextChangedListener(mTextWatcher)
            mAlarmLabel.onFocusChangeListener = null
            // mHandlerQuery is used to query SQL
            alarmRingManager.mHandlerQuery?.removeCallbacksAndMessages(null)
            // mSetAlarmHandler and mHandlerThread is used to do time_consuming work asynchronously
            alarmRingManager.mSetAlarmHandler?.removeCallbacksAndMessages(null)
            if (mHandlerThread != null) {
                mHandlerThread!!.quit()
                mHandlerThread = null
            }
            // mHandler is a static member variable
            if (mHandler != null) {
                mHandler.removeCallbacksAndMessages(null)
            }

            mAlarmTypeTabAdapter?.setListener(null)
            //清除动画
            mAnimatorUtil.clearAnimation()
            workdayManage.dismissDialog()
            mAlatmTimesDialog?.dismiss()
            mAlatmTimesDialog = null
            mLoopAlarmTips?.dismissImmediately()
            mWorkDayAlarmTips?.dismissImmediately()
        }
    }

    fun onResume() {
        viewHolder.apply {
            mHandler.sendEmptyMessageDelayed(
                AddAlarmViewHolder.SET_BUTTON_CLICK_DELAY,
                SET_BUTTON_CLICK_DELAY_TIME.toLong()
            )
            if (mOplusTimePicker != null) {
                mOplusTimePicker.setIs24HourView(DateFormat.is24HourFormat(mContext))
            }
            updateLeftTimeInfo()
            alarmRingManager.resumeRingData()
        }
    }

    /**
     * 切换轮班闹钟
     */
    fun changeLoopAlarm() {
        workdayManage.changeLoopAlarm()
    }

    /**
     * 更新数据
     */
    fun notifyData(list: List<Alarm>, alarm: Alarm) {
        workdayManage.notifyData(list, alarm)
    }

    /**
     * 外销切换到轮班闹钟
     */
    fun expChangeLoopAlarm() {
        viewHolder.run {
            mAlarm?.apply {
                loopAlarmList?.let { mList ->
                    val data = LoopAlarmUtils.computeLoopDays(mList)
                    setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                    setmLoopWorkDays(data.first)
                    setmLoopRestDays(data.second)
                    changeLoopAlarm()
                }
            }
        }
    }
}