// OPLUS Java File Skip Rule:MethodLength
/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :AsyncRingtonePlayer ,handle mediaPlayer.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.Ringtone;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.heytap.addon.media.MediaFile;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.alert.AlarmKlaxon;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FbeRingUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PhonyManagerExtensionKt;
import com.oplus.alarmclock.utils.SoundUriUtils;
import com.oplus.alarmclock.utils.Utils;

import java.io.IOException;

import androidx.annotation.RequiresApi;

/**
 * <p>
 * Plays the alarm ringtone. Uses {@link Ringtone} in a separate thread so that this class can be
 * used from the main thread. Consequently, problems controlling the ringtone do not cause ANRs in
 * the main thread of the application.
 * </p>
 * <p>
 * <p>
 * This class also serves a second purpose. It accomplishes alarm ringtone playback using two
 * different mechanisms depending on the underlying platform.
 * </p>
 * <p>
 * <ul>
 * <li>Prior to the M platform release, ringtone playback is accomplished using {@link MediaPlayer}.
 * android.permission.READ_EXTERNAL_STORAGE is required to play custom ringtones located on the SD
 * card using this mechanism. {@link MediaPlayer} allows clients to adjust the volume of the stream
 * and specify that the stream should be looped.</li>
 * <p>
 * <li>Starting with the M platform release, ringtone playback is accomplished using
 * {@link Ringtone}. android.permission.READ_EXTERNAL_STORAGE is <strong>NOT</strong> required to
 * play custom ringtones located on the SD card using this mechanism. {@link Ringtone} allows
 * clients to adjust the volume of the stream and specify that the stream should be looped but those
 * methods are marked @hide in M and thus invoked using reflection. Consequently, revoking the
 * android.permission.READ_EXTERNAL_STORAGE permission has no effect on playback in M+.</li>
 * </ul>
 */
public class AsyncRingtonePlayer {

    private static final String TAG = "AsyncRingtonePlayer";
    private static final String RINGTONE_PLAYER = "ringtone-player";
    private static final String RINGTONE_PLAYER1111 = "ringtone-player1111";

    // Volume suggested by media team for in-call alarms.
    private static final float IN_CALL_VOLUME = 0.125f;

    private static final float FLOAT_1 = 1.0f;
    private static final float FLOAT_3 = 3.0f;
    private static final float FLOAT_12 = 12.0f;

    // Message codes used with the ringtone thread.
    private static final int EVENT_PLAY = 1;
    private static final int EVENT_STOP = 2;
    private static final int EVENT_SET_VOLUME = 3;
    private static final int EVENT_SET_VOLUME_REDUCE_BY_TIME = 4;
    private static final int RATIO = 6;
    private static final int DEFAULT_DELAY_TIME = 10;
    private static final int SET_VOLUME_DELAY_TIME = 500;
    private static final int SET_VOLUME_REDUCE_DELAY_TIME = 200;
    private static final float VOLUME_ONE = 1.0f;
    private static final float VOLUME_HALF_ONE = 0.1f;
    private static final float VOLUME_HALF_FOUR = 0.4f;
    private static final String RINGTONE_URI_KEY = "RINGTONE_URI_KEY";
    private static final String RINGTONE_RES_OR_NAME_KEY = "RINGTONE_RES_OR_NAME_KEY";

    /**
     * The context.
     */
    private final Context mContext;

    /**
     * Handler running on the ringtone thread.
     */
    private Handler mHandler;
    private float mAcceleration; // for ring volume
    private float mVelocity;

    private float mReduction; // for ring volume
    private float mMinReduction;
    private float mReduceVelocity = VOLUME_ONE;

    private PlaybackDelegate mPlaybackDelegate = new MediaPlayerPlaybackDelegate();

    public AsyncRingtonePlayer(Context context) {
        if (context == null) {
            mContext = AlarmClockApplication.getInstance();
        } else {
            mContext = context;
        }
    }

    /**
     * @return Uri of the ringtone to play when the user is in a telephone call
     */
    private static Uri getInCallRingtoneUri(Context context) {
        String packageName = "";
        if (context == null) {
            packageName = AlarmClockApplication.getInstance().getPackageName();
        } else {
            packageName = context.getPackageName();
        }
        return Uri.parse("android.resource://" + packageName + "/" + R.raw.in_call_alarm);
    }

    private static Uri getLocalDefaultAlarmUri(Context context) {
        String packageName = "";
        if (context == null) {
            packageName = AlarmClockApplication.getInstance().getPackageName();
        } else {
            packageName = context.getPackageName();
        }
        return Uri.parse("android.resource://" + packageName + "/" + R.raw.ringtone_008);
    }

    private static Uri getLocalRingtoneUriFromResId(Context context, int ringtoneResId) {
        String packageName = "";
        if (context == null) {
            packageName = AlarmClockApplication.getInstance().getPackageName();
        } else {
            packageName = context.getPackageName();
        }
        return Uri.parse("android.resource://" + packageName + "/" + ringtoneResId);
    }

    /**
     * @return Uri of the ringtone to play when the chosen ringtone fails to play
     */
    private static Uri getFallbackRingtoneUri(Context context) {
        return MediaFile.getDefaultAlarmUri(context);
    }

    /**
     * Plays the ringtone.
     *
     * @param useHapticRingtoneVibrate 是否使用铃声随振接口
     */
    public void play(Uri ringtoneUri, boolean useDynamicWeatherAlert, Object weatherAlertResOrName,
                     boolean useHapticRingtoneVibrate, AlarmSchedule schedule) {
        Log.d(TAG, "Posting play: " + ringtoneUri + " useHapticRingtoneVibrate: " + useHapticRingtoneVibrate);
        mPlaybackDelegate.setUseHapticRingtoneVibrate(useHapticRingtoneVibrate);
        mPlaybackDelegate.setAlarmSchedule(schedule);
        if (!useDynamicWeatherAlert) {
            postMessage(EVENT_PLAY, ringtoneUri, -1, null);
        } else {
            postMessage(EVENT_PLAY, null, -1, weatherAlertResOrName);
        }
    }

    /**
     * Stops playing the ringtone.
     */
    public void stop() {
        Log.d(TAG, "Posting stop.");
        postMessage(EVENT_STOP, null, -1, null);
    }

    /**
     * set volume.
     */
    public void setVolume(Context context, int volume) {
        if (context == null) {
            return;
        }
        AudioManager am = ((AudioManager) context.getSystemService(Context.AUDIO_SERVICE));
        int maxVolume = (am != null) ? am.getStreamMaxVolume(AudioManager.STREAM_ALARM) : 0;
        Log.d(TAG, "setVolume maxVolume: " + maxVolume);
        if (maxVolume == 0) {
            return;
        }
        mAcceleration = (((maxVolume / FLOAT_3)) / (maxVolume * FLOAT_1));

        Log.v(TAG, "volume: " + volume + ", acceleration: " + mAcceleration);

        mVelocity = mAcceleration / RATIO;
        postMessage(EVENT_SET_VOLUME, null, volume, null);
    }

    /**
     * set volume reduce by time.
     */
    public void setVolumeReduceByTime(Context context) {
        if (context == null) {
            return;
        }
        AudioManager am = ((AudioManager) context.getSystemService(Context.AUDIO_SERVICE));
        int maxVolume = (am != null) ? am.getStreamVolume(AudioManager.STREAM_ALARM) : 0;
        int currentVolume = (am != null) ? am.getStreamVolume(AudioManager.STREAM_ALARM) : 0;
        Log.d(TAG, "setVolumeReduceByTime maxVolume: " + maxVolume + " currentVolume:" + currentVolume);
        if ((maxVolume == 0) || (currentVolume == 0)) {
            return;
        }

        mReduceVelocity = VOLUME_ONE;
        mReduction = (currentVolume * VOLUME_ONE) / maxVolume;
        mMinReduction = mReduction * VOLUME_HALF_FOUR;
        Log.v(TAG, "volume: " + currentVolume + ", reduction: " + mReduction + ", minReduction: " + mMinReduction);  //1.0max / 0.5 current  0.5 * 0.4

        postReduceVolumeMessage(EVENT_SET_VOLUME_REDUCE_BY_TIME);
    }

    /**
     * Posts a message to the ringtone-thread handler.
     *
     * @param messageCode The message to post.
     */
    private void postMessage(int messageCode, Uri ringtoneUri, float volume, Object weatherAlertResOrName) {
        synchronized (this) {
            if (mHandler == null) {
                mHandler = getNewHandler();
            }
            final Message message = mHandler.obtainMessage(messageCode);
            if (volume != -1) {
                mHandler.sendMessageDelayed(message, DEFAULT_DELAY_TIME);
                return;
            }
            if (ringtoneUri != null) {
                final Bundle bundle = new Bundle();
                bundle.putParcelable(RINGTONE_URI_KEY, ringtoneUri);
                message.setData(bundle);
            } else if ((messageCode == EVENT_PLAY) && (weatherAlertResOrName != null)) {
                final Bundle bundle = new Bundle();
                if (Utils.isAboveR()) {
                    bundle.putString(RINGTONE_RES_OR_NAME_KEY, (String) weatherAlertResOrName);
                } else {
                    bundle.putInt(RINGTONE_RES_OR_NAME_KEY, (Integer) weatherAlertResOrName);
                }
                message.setData(bundle);
            }
            message.sendToTarget();
        }
    }

    /**
     * Posts a message to handler.
     *
     * @param messageCode The message to post.
     */
    private void postReduceVolumeMessage(int messageCode) {
        synchronized (this) {
            if (mHandler == null) {
                mHandler = getNewHandler();
            }

            mHandler.removeMessages(EVENT_SET_VOLUME);
            mHandler.sendEmptyMessage(messageCode);
        }
    }

    /**
     * Creates a new ringtone Handler running in its own thread.
     */
    private Handler getNewHandler() {
        final HandlerThread thread = new HandlerThread(RINGTONE_PLAYER);
        thread.start();

        return new Handler(thread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case EVENT_PLAY:
                        Log.d(TAG, "handleMessage  EVENT_PLAY  ");
                        final Uri ringtoneUri = msg.getData().getParcelable(RINGTONE_URI_KEY);
                        Object weatherAlertResOrName = Utils.isAboveR() ? msg.getData().getString(RINGTONE_RES_OR_NAME_KEY)
                                : msg.getData().getInt(RINGTONE_RES_OR_NAME_KEY, -1);
                        mPlaybackDelegate.play(mContext, ringtoneUri, weatherAlertResOrName);
                        break;
                    case EVENT_STOP:
                        Log.d(TAG, "handleMessage  EVENT_STOP  ");
                        mPlaybackDelegate.stop(mContext);
                        break;
                    case EVENT_SET_VOLUME:
                        mVelocity = mVelocity + (mAcceleration / FLOAT_12);

                        float volume = (mVelocity / mAcceleration) * (mVelocity / mAcceleration);
                        if (volume > 1) {
                            volume = 1;
                        }
                        /*Log.d(TAG, "SET_VOLUME: velocity = " + mVelocity + ", acceleration = "
                                + mAcceleration + ", volume = " + volume);*/
                        mPlaybackDelegate.setVolume(volume, volume);

                        if ((mVelocity <= mAcceleration) && (volume < FLOAT_1) && (mHandler != null)) {
                            mHandler.sendEmptyMessageDelayed(EVENT_SET_VOLUME,
                                    SET_VOLUME_DELAY_TIME);
                        }
                        break;
                    case EVENT_SET_VOLUME_REDUCE_BY_TIME:
                        //降低规则：降低 200ms 音量90%， 400ms 音量80% ，600ms 音量70%  ，800ms 音量60%，1000ms 音量50% ，1200ms 音40%
                        mReduceVelocity = mReduceVelocity - VOLUME_HALF_ONE; // -0.1f
                        mReduction = mReduction * mReduceVelocity;

                        Log.d(TAG, "SET_VOLUME_REDUCE_BY_TIME: reduction = " + mReduction + ", reduceVelocity = "
                                + mReduceVelocity);
                        mPlaybackDelegate.setVolume(mReduction, mReduction);

                        if ((mReduction >= mMinReduction) && (mHandler != null)) {
                            mHandler.sendEmptyMessageDelayed(EVENT_SET_VOLUME_REDUCE_BY_TIME,
                                    SET_VOLUME_REDUCE_DELAY_TIME);
                        }
                        break;
                    default:
                        break;
                }
            }
        };
    }

    /**
     * @return <code>true</code> iff the device is currently in a telephone call
     */
    public static boolean isInTelephoneInCall(Context context) {
        if (context == null) {
            return false;
        }
        final TelephonyManager tm = (TelephonyManager) context
                .getSystemService(Context.TELEPHONY_SERVICE);
        if (tm != null) {
            int callState = PhonyManagerExtensionKt.getTelephonyCallState(tm);
            return ((callState != TelephonyManager.CALL_STATE_IDLE)
                    && (callState != TelephonyManager.CALL_STATE_RINGING));
        }
        return false;
    }

    /**
     * This interface abstracts away the differences between playing ringtones via {@link Ringtone}
     * vs {@link MediaPlayer}.
     */
    private interface PlaybackDelegate {
        void play(Context context, Uri ringtoneUri, Object weatherAlertResOrName);

        void stop(Context context);

        void setVolume(float leftVolume, float rightVolume);

        void setUseHapticRingtoneVibrate(boolean useHapticRingtoneVibrate);

        void setAlarmSchedule(AlarmSchedule schedule);
    }

    /**
     * Loops playback of a ringtone using {@link MediaPlayer}.
     */
    private static class MediaPlayerPlaybackDelegate implements PlaybackDelegate {

        /**
         * The audio focus manager. Only used by the ringtone thread.
         */
        private AudioManager mAudioManager;

        /**
         * Non-{@code null} while playing a ringtone; {@code null} otherwise.
         */
        private MediaPlayer mMediaPlayer;

        /**
         * 是否使用铃声随振接口
         */
        private boolean mUseHapticRingtoneVibrate;

        private AlarmSchedule mAlarmSchedule;

        private final Handler mHandler = getNewHandler();

        private Handler getNewHandler() {
            final HandlerThread thread = new HandlerThread(RINGTONE_PLAYER1111);
            thread.start();
            return new Handler(thread.getLooper()) {};
        }

        @Override
        public void setUseHapticRingtoneVibrate(boolean useHapticRingtoneVibrate) {
            mUseHapticRingtoneVibrate = useHapticRingtoneVibrate;
        }

        @Override
        public void setAlarmSchedule(AlarmSchedule schedule) {
            mAlarmSchedule = schedule;
        }

        /**
         * Starts the actual playback of the ringtone. Executes on ringtone-thread.
         */
        @RequiresApi(api = Build.VERSION_CODES.Q)
        @Override
        public void play(final Context context, Uri ringtoneUri, Object weatherAlertResOrName) {
            if (Looper.getMainLooper() == Looper.myLooper()) {
                Log.e(TAG, "Must not be on the main thread!", new IllegalStateException());
            }
            if ((ringtoneUri != null)
                    && AlarmSpotifyUtils.isSpotifyRing(ringtoneUri.toString())) {
                playSpotifyRingtone(context, ringtoneUri);
            } else {
                playMediaRingtone(context, ringtoneUri, weatherAlertResOrName);
            }
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        private void playMediaRingtone(final Context context,
                                       Uri ringtoneUri, Object weatherAlertResOrName) {
            //处理天气闹钟铃声逻辑，R上使用文件名去系统查询铃声uri，Q上使用的本地铃声资源文件
            boolean useDynamicWeatherAlert = false;
            Uri weatherAlertUri = null;
            if (weatherAlertResOrName != null) {
                if (Utils.isAboveR()) {
                    String weatherAlertName = (String) weatherAlertResOrName;
                    useDynamicWeatherAlert = (ringtoneUri == null) && !TextUtils.isEmpty(weatherAlertName);
                    weatherAlertUri = SoundUriUtils.INSTANCE.getSoundPath(weatherAlertName);
                } else {
                    int weatherRingtoneResId = (int) weatherAlertResOrName;
                    useDynamicWeatherAlert = (ringtoneUri == null) && (weatherRingtoneResId > 0);
                    weatherAlertUri = getLocalRingtoneUriFromResId(context, weatherRingtoneResId);
                }
            }

            Uri defaultWeatherAlertUri = AsyncRingtonePlayer.getLocalRingtoneUriFromResId(context, ChannelManager.INSTANCE.getLightOSUtils().getWeatherDefaultResId());
            Log.i(TAG, "useDynamicWeatherAlert: " + useDynamicWeatherAlert + " weatherAlertUri: " + weatherAlertUri);

            Log.i(TAG, "Play ringtone via android.media.MediaPlayer.");

            if (mAudioManager == null) {
                mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            }

            Uri alarmNoise = ringtoneUri;
            // Fall back to the default alarm if the database does not have an alarm stored.
            if (alarmNoise == null) {
                alarmNoise = MediaFile.getDefaultAlarmUri(context);
                Log.w("Using default alarm uri: " + alarmNoise);
            }

            /*for bug2343456:make sure we are stopped before starting*/
            stop(context);

            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {

                @Override
                public boolean onError(MediaPlayer mp, int what, int extra) {
                    Log.e(TAG, "Error occurred while playing audio. Stopping AlarmKlaxon.");
                    stop(context);
                    AlarmRingStatisticUtils.statisticsAlarmException(context, AlarmRingStatisticUtils.EVENT_PLAY_RINGTONE_ERROR,
                            mAlarmSchedule, "playMediaRingtone onError what:" + what + "extra:" + extra);
                    return true;
                }
            });

            try {
                // If alarmNoise is a custom ringtone on the sd card the app must be granted
                // android.permission.READ_EXTERNAL_STORAGE. Pre-M this is ensured at app
                // installation time. M+, this permission can be revoked by the user any time.
                boolean useInternalRingFile = FbeRingUtils.checkFBESupport(context)
                        && Utils.isUserKeyUnlocked(context);
                Log.d(TAG, "useInternalRingFile: " + useInternalRingFile);

                // Check if we are in a call. If we are, use the in-call alarm resource at a
                // low volume to not disrupt the call.
                if (isInTelephoneInCall(context)) {
                    mMediaPlayer.setVolume(IN_CALL_VOLUME, IN_CALL_VOLUME);
                    alarmNoise = getInCallRingtoneUri(context);
                    Log.w(TAG, "Using the in-call alarm, uri:" + alarmNoise);
                    useDynamicWeatherAlert = false;
                    useInternalRingFile = false;
                }

                if (useInternalRingFile) {
                    if (useDynamicWeatherAlert) {
                        mMediaPlayer.setDataSource(context, defaultWeatherAlertUri);
                    } else {
                        String ringPath = FbeRingUtils.getInternalRingPathFromUri(context, ringtoneUri);
                        if (!TextUtils.isEmpty(ringPath)) {
                            Log.d(TAG, "setDataSource : " + ringPath);
                            mMediaPlayer.setDataSource(ringPath);
                        } else {
                            mMediaPlayer.setDataSource(context, alarmNoise);
                        }
                    }
                } else {
                    if (useDynamicWeatherAlert) {
                        mMediaPlayer.setDataSource(context, weatherAlertUri);
                    } else {
                        mMediaPlayer.setDataSource(context, alarmNoise);
                    }
                }

                startAlarm(context, mMediaPlayer);
            } catch (Exception e) {
                Log.e(TAG, "Use the fallback ringtone, original was " + alarmNoise, e);
                playDefaultRingtone(context, useDynamicWeatherAlert, defaultWeatherAlertUri);
            }
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        private void playSpotifyRingtone(final Context context, Uri ringtoneUri) {
            try {
                if (mAudioManager == null) {
                    mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
                }
                if (mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM) != 0) {
                    AlarmSpotifyUtils.play(context, ringtoneUri.toString(), success -> {
                        Log.d(TAG, "play result:" + success);
                        if (!success) {
                            Log.e(TAG, "play spotify ringtone fail!");
                            if (AlarmKlaxon.isRunningStart()) {
                                playDefaultRingtone(context, false, null);
                            }
                        }
                        return null;
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, "play spotify ringtone exception:" + e.getMessage());
                playDefaultRingtone(context, false, null);
            }
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        private void playDefaultRingtone(final Context context, boolean useDynamicWeatherAlert, Uri defaultWeatherAlertUri) {
            if (mMediaPlayer == null) {
                mMediaPlayer = new MediaPlayer();
                mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {

                    @Override
                    public boolean onError(MediaPlayer mp, int what, int extra) {
                        Log.e(TAG, "Error occurred while playing audio. Stopping AlarmKlaxon.");
                        stop(context);
                        AlarmRingStatisticUtils.statisticsAlarmException(context, AlarmRingStatisticUtils.EVENT_PLAY_RINGTONE_ERROR,
                                mAlarmSchedule, "playDefaultRingtone onError what:" + what + "extra:" + extra);
                        return true;
                    }
                });
                if (isInTelephoneInCall(context)) {
                    mMediaPlayer.setVolume(IN_CALL_VOLUME, IN_CALL_VOLUME);
                }
            }
            Uri localUri = getLocalDefaultAlarmUri(context);
            Uri dfltUri = getFallbackRingtoneUri(context);
            boolean useLocalUri = (DeviceUtils.isFbeEnabled())
                    && (Utils.isUserKeyUnlocked(context));
            Uri uri = useLocalUri ? localUri : dfltUri;
            uri = useDynamicWeatherAlert ? defaultWeatherAlertUri : uri;
            Log.d(TAG, "UseLocalUri: " + useLocalUri + ", try: " + uri);
            // The alarmNoise may be on the sd card which could be busy right now.
            // Use the fallback ringtone.
            try {
                // Must reset the media player to clear the error state.
                mMediaPlayer.reset();
                mMediaPlayer.setDataSource(context, uri);
                startAlarm(context, mMediaPlayer);
            } catch (Exception t2) {
                Log.e(TAG, "Failed to play fallback ringtone 2: " + uri);
                // ...OK, Try the last time.
                try {
                    mMediaPlayer.reset();
                    mMediaPlayer.setDataSource(context, useDynamicWeatherAlert ? defaultWeatherAlertUri : localUri);
                    startAlarm(context, mMediaPlayer);
                } catch (Exception t3) {
                    // At this point we just don't play anything.
                    Log.e(TAG, "Give up to play any ringtone!", t3);
                    AlarmRingStatisticUtils.statisticsAlarmException(context, AlarmRingStatisticUtils.EVENT_PLAY_RINGTONE_ERROR,
                            mAlarmSchedule, t3.getMessage());
                }
            }
        }

        /**
         * Do the common stuff when starting the alarm.
         */
        @RequiresApi(api = Build.VERSION_CODES.Q)
        private void startAlarm(Context context, MediaPlayer player) throws IOException {
            // do not play alarms if stream volume is 0 (typically because ringer mode is silent).
            if (mAudioManager == null) {
                mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            }
            if (mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM) != 0) {
                player.setAudioStreamType(AudioManager.STREAM_ALARM);
                player.setLooping(AlarmKlaxon.isRingLoop());
                if (DeviceUtils.isHapticChannelSupport(context)) {
                    Log.d(TAG, "startAlarm setHapticChannelsMuted " + mUseHapticRingtoneVibrate);
                    player.setAudioAttributes(new AudioAttributes.Builder()
                            .setLegacyStreamType(AudioManager.STREAM_ALARM)
                            // false时播放且振动，true代表播放不振动
                            .setHapticChannelsMuted(!mUseHapticRingtoneVibrate)
                            .build());
                }
                player.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    @Override
                    public void onCompletion(MediaPlayer mediaPlayer) {
                        AlarmSchedule currentAlarmSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                        /*The following invocation sequence cannot be changed
                              because need to guarantee to vibrate and ring again*/
                        if ((!AlarmKlaxon.isRingLoop()) && (currentAlarmSchedule != null) && (mMediaPlayer != null)) {
                            Context context = AlarmClockApplication.getInstance();
                            mMediaPlayer.pause();
                            AlarmKlaxon.doVibrator(context, true, currentAlarmSchedule, mUseHapticRingtoneVibrate);
                            mMediaPlayer.start();
                            Log.d(TAG, "startAlarm play ring repeat,currentAlarmSchedule:" + currentAlarmSchedule);
                        }
                    }
                });
                player.prepare();
                AudioAttributes.Builder builder = new AudioAttributes.Builder();
                builder.setUsage(AudioAttributes.USAGE_ALARM);
                builder.setContentType(AudioAttributes.CONTENT_TYPE_MUSIC);
                AudioFocusRequest.Builder onAudioFocusChangeListener =
                        new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                                .setAudioAttributes(builder.build())
                                .setAcceptsDelayedFocusGain(true)
                                .setWillPauseWhenDucked(true)
                                .setOnAudioFocusChangeListener(focusChange -> {
                                    Log.e(TAG, "onAudioFocusChange:" + focusChange + " ;" + mMediaPlayer);
                                    switch (focusChange) {
                                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                                            if (mMediaPlayer != null) {
                                                mMediaPlayer.pause();
                                            }
                                            break;
                                        case AudioManager.AUDIOFOCUS_LOSS:
                                            break;
                                        case AudioManager.AUDIOFOCUS_GAIN:
                                            if (mMediaPlayer != null) {
                                                mMediaPlayer.start();
                                            }
                                            break;
                                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                                            break;
                                    }
                                }, mHandler);
                AudioFocusRequest focusRequest = onAudioFocusChangeListener.build();
                mAudioManager.requestAudioFocus(focusRequest);
                player.start();
                Log.i(TAG, "startAlarm player start");
            }
        }

        /**
         * Stops the playback of the ringtone. Executes on the ringtone-thread.
         */
        @Override
        public void stop(Context context) {
            if (Looper.getMainLooper() == Looper.myLooper()) {
                Log.e(TAG, "Must not be on the main thread!", new IllegalStateException());
                return;
            }

            Log.i(TAG, "Stop ringtone via android.media.MediaPlayer.");

            // Stop audio playing
            if (mMediaPlayer != null) {
                mMediaPlayer.stop();
                mAudioManager.abandonAudioFocus(null);
                mMediaPlayer.setOnCompletionListener(null);
                mMediaPlayer.release();
                mMediaPlayer = null;
            }
            AlarmSpotifyUtils.stop(context);
        }

        @Override
        public void setVolume(float leftVolume, float rightVolume) {
            if (mMediaPlayer != null) {
//                Log.i(TAG, "setVolume leftVolume: " + leftVolume + ", rightVolume: " + rightVolume);
                mMediaPlayer.setVolume(leftVolume, rightVolume);
            }
//            AlarmSpotifyUtils.setVolume(rightVolume);
        }
    }
}
