/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : Preferences group for radio
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-02-20, xiaolong,yu, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.os.SystemClock;
import android.util.AttributeSet;

import androidx.preference.Preference;

import com.coui.appcompat.preference.COUIMarkPreference;
import com.coui.appcompat.preference.COUIPreferenceCategory;
import com.oplus.clock.common.utils.Log;

public class OplusRadioPreferencesGroup extends COUIPreferenceCategory
        implements Preference.OnPreferenceClickListener, Preference.OnPreferenceChangeListener {
    private static final long MENU_CLICK_DURATION = 1000; // 1s.
    private long mLastClickTime;
    private OnCheckedStateChangeListener mListener;
    private COUIMarkPreference mCurrentSelected = null;

    public OplusRadioPreferencesGroup(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public boolean addPreference(Preference preference) {
        Context context = getContext();
        COUIMarkPreference pref = new COUIMarkPreference(context);
        pref.setTitle(preference.getTitle());
        pref.setIcon(preference.getIcon());
        pref.setKey(preference.getKey());
        pref.setSummary(preference.getSummary());
        pref.setOnPreferenceClickListener(this);
        pref.setOnPreferenceChangeListener(this);
        pref.setChecked(false);
        return super.addPreference(pref);
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        COUIMarkPreference pref = (COUIMarkPreference) preference;
        if (canClick() || (mCurrentSelected != pref)) {
            setCheckedPreference(preference, true/*need to notify change*/);
            return true;
        }
        setCurrentChecked(true);
        return true;
    }

    private boolean canClick() {
        long elapsed = SystemClock.elapsedRealtime();
        if ((elapsed - mLastClickTime) > MENU_CLICK_DURATION) {
            mLastClickTime = elapsed;
            return true;
        }
        return false;
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        return true;
    }

    public int getCheckedPreferenceIndex() {
        int index = -1;
        final int preferenceCount = getPreferenceCount();
        for (int i = 0; i < preferenceCount; i++) {
            final Preference preference = getPreference(i);
            if (mCurrentSelected == preference) {
                index = i;
                break;
            }
        }

        return index;
    }

    public void setCheckedPreference(int index) {
        final int count = getPreferenceCount();
        if ((index >= 0) && (index < count)) {
            final Preference preference = getPreference(index);
            setCheckedPreference(preference, false/* not need to notify change*/);
        }
    }

    private void setCleanPreferenceCheckedStatus() {
        final int preferenceCount = getPreferenceCount();
        for (int i = 0; i < preferenceCount; i++) {
            final Preference preference = getPreference(i);
            if (preference instanceof COUIMarkPreference) {
                COUIMarkPreference pref = (COUIMarkPreference) preference;
                pref.setChecked(false);
            }
        }
    }

    public void setCheckedPreference(Preference preference, boolean isNeedNotifyChange) {

        Log.d("OplusRadioPreferencesGroup", "setCheckedPreference : " + isNeedNotifyChange);
        COUIMarkPreference pref = null;
        if (preference instanceof COUIMarkPreference) {
            pref = (COUIMarkPreference) preference;
        } else {
            pref = (COUIMarkPreference) findPreference(preference.getKey());
        }

        setCurrentSelectedPreference(pref, isNeedNotifyChange);
    }

    private void setCurrentSelectedPreference(COUIMarkPreference pref, boolean isNeedNotifyChange) {
        if (pref == null) {
            return;
        }
        Log.d("OplusRadioPreferencesGroup", "setCurrentSelectedPreference : " + isNeedNotifyChange);
        setCleanPreferenceCheckedStatus();
        if (mCurrentSelected != null) {
            if (mCurrentSelected != pref) {
                mCurrentSelected.setChecked(false);
                callChangeListener(mCurrentSelected);
            } else {
                mCurrentSelected.setChecked(true);
                callChangeListener(mCurrentSelected);
                if ((mListener != null) && isNeedNotifyChange) {
                    mListener.onCheckedStateChange(pref.isChecked());
                }
                return;
            }
        }

        mCurrentSelected = pref;
        mCurrentSelected.setChecked(true);
        callChangeListener(pref);
        if ((mListener != null) && isNeedNotifyChange) {
            mListener.onCheckedStateChange(pref.isChecked());
        }
    }


    public void setCurrentChecked(boolean state) {
        if (mCurrentSelected != null) {
            mCurrentSelected.setChecked(state);
            if (!state) {
                mCurrentSelected = null;
            }
        }
    }

    public void setOnCheckedStateChangeListener(OnCheckedStateChangeListener listener) {
        mListener = listener;
    }

    public interface OnCheckedStateChangeListener {
        void onCheckedStateChange(boolean state);
    }

    void resetContent() {
        mListener = null;
        mCurrentSelected = null;
    }
}
