/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - OpenSourceActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/2/20     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.res.Configuration
import android.os.AsyncTask
import android.os.Bundle
import android.text.Html
import android.widget.TextView
import androidx.core.widget.NestedScrollView
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FlexibleWindowUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.isScreenUnfold
import com.oplus.alarmclock.utils.Utils
import com.oplus.utils.ActivityUtils
import java.io.BufferedReader
import java.io.InputStreamReader
import java.lang.ref.WeakReference

class OpenSourceActivity : BaseActivity() {
    companion object {
        private const val OPEN_SOURCE = "clock_source_license.html"
    }

    private var scrollView: NestedScrollView? = null
    private var appBarLayout: AppBarLayout? = null
    private var textContent: TextView? = null
    private var asyncTaskLoad: AsyncTaskLoad? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_open_source)
        val toolbar = findViewById<COUIToolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        toolbar?.setNavigationOnClickListener { finish() }
        appBarLayout = findViewById<AppBarLayout>(R.id.app_bar)
        textContent = findViewById<TextView>(R.id.text_content)
        scrollView = findViewById<NestedScrollView>(R.id.scrollView)
        //这里与隐私政策保持一致，分屏下效果不变
        if (FlexibleWindowUtils.isSupportFlexibleActivity()
                && FlexibleWindowUtils.isFlexibleActivitySuitable(resources.configuration)) {
            val paddingStartAndEnd = resources.getDimensionPixelSize(R.dimen.layout_dp_24)
            scrollView?.setPadding(
                    paddingStartAndEnd,
                    resources.getDimensionPixelSize(R.dimen.layout_dp_20),
                    paddingStartAndEnd,
                    0
            )
        } else {
            if (FoldScreenUtils.isRealOslo()) {
                val padding = if (DeviceUtils.isLandscapeScreen()) {
                    resources.getDimensionPixelSize(R.dimen.open_source_oslo_land_padding)
                } else {
                    resources.getDimensionPixelSize(R.dimen.layout_dp_84)
                }
                scrollView?.setPadding(
                        padding,
                        resources.getDimensionPixelSize(R.dimen.layout_dp_20),
                        padding,
                        0
                )
            } else if (isScreenUnfold(this)) {
                scrollView?.setPadding(
                        resources.getDimensionPixelSize(R.dimen.layout_dp_84),
                        0,
                        resources.getDimensionPixelSize(R.dimen.layout_dp_84),
                        0
                )
            } else {
                val paddingStartAndEnd = resources.getDimensionPixelSize(R.dimen.layout_dp_24)
                scrollView?.setPadding(
                        paddingStartAndEnd,
                        resources.getDimensionPixelSize(R.dimen.layout_dp_20),
                        paddingStartAndEnd,
                        0
                )
            }
        }
        Utils.setupAnimToolbarAndBlurView(this, appBarLayout, scrollView)
        initView()
        setFlexibleWindowBg()
        addActivityWeakAndSetOnTouch()
    }

    private fun initView() {
        asyncTaskLoad = AsyncTaskLoad(this)
        asyncTaskLoad?.execute()
    }

    internal class AsyncTaskLoad(activity: OpenSourceActivity) :
            AsyncTask<Void?, Void?, String?>() {
        private val mWekRef: WeakReference<OpenSourceActivity>

        override fun onPostExecute(value: String?) {
            super.onPostExecute(value)
            mWekRef.get()?.textContent?.text = value
        }

        init {
            mWekRef = WeakReference(activity)
        }

        override fun doInBackground(vararg p0: Void?): String? {
            BufferedReader(InputStreamReader(mWekRef.get()?.assets?.open(OPEN_SOURCE)))
                    .use {
                        val readText = it.readText()
                        var value = Html.fromHtml(readText, Html.FROM_HTML_MODE_COMPACT).toString()
                        return value
                    }
            return ""
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        asyncTaskLoad?.cancel(true)
        ActivityUtils.sSettingActivity.remove(mActivityWeak)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val appBarHeight = appBarLayout?.measuredHeight ?: 0
        if (FoldScreenUtils.isRealOslo() && !FlexibleWindowUtils.isSupportFlexibleActivity()) {
            val padding = if (DeviceUtils.isLandscapeScreen()) {
                resources.getDimensionPixelSize(R.dimen.open_source_oslo_land_padding)
            } else {
                resources.getDimensionPixelSize(R.dimen.layout_dp_84)
            }
            scrollView?.setPadding(
                    padding,
                    appBarHeight + resources.getDimensionPixelSize(R.dimen.layout_dp_20),
                    padding,
                    0
            )
        } else {
            val paddingStartAndEnd = resources.getDimensionPixelSize(R.dimen.layout_dp_24)
            scrollView?.setPadding(
                    paddingStartAndEnd,
                    appBarHeight + resources.getDimensionPixelSize(R.dimen.layout_dp_20),
                    paddingStartAndEnd,
                    0
            )
        }
        Utils.setupAnimToolbarAndBlurView(this, appBarLayout, scrollView)
        setFlexibleWindowBg()
    }
}


