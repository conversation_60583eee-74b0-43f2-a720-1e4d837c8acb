/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - LegalHolidayUtil.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2019/09/16
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2019/09/16     1.0            LegalHolidayUtil.java
 *  ***************************************************************
 */

package com.oplus.alarmclock.alarmclock;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

public class LegalHolidayUtil {
    public static final int COLOR_TYPE_HOLIDAY = 1;
    public static final int COLOR_TYPE_WORKDAY = 2;
    private static final String TAG = "LegalHolidayUtil";
    private static final int WORKDAY_FIRST = 2;
    private static final int WORKDAY_END_SAT = 6;
    private static final int WORKDAY_END_SUN = 7;
    private static final int TWENTY_HOUR = 20;
    private static final int ONE_MINUTE = 60 * 1000;

    private static List<Holiday> sHolidayList;
    private static List<Long> sWorkdayList;
    private static int sCurrentYear = -1;

    public static List<Holiday> getHolidayFromCache(Calendar calendar) {
        int startYear = calendar.get(Calendar.YEAR);
        int endYear = startYear + 1;
        Log.d(TAG, "startYear:" + startYear + ",endYear:" + endYear);
        /*If the cache is empty or the current year changes, get data from db*/
        if ((sHolidayList == null) || (sCurrentYear != startYear) || sHolidayList.isEmpty()) {
            sHolidayList = getHolidayFromDb(startYear, endYear, true);
        }
        return sHolidayList;
    }

    /**
     * 从数据库获取节假日信息
     *
     * @param startYear
     * @param endYear
     * @param isQueryHoliday
     * @return
     */
    private static List<Holiday> getHolidayFromDb(int startYear, int endYear, boolean isQueryHoliday) {
        sCurrentYear = startYear;
        String selection = null;
        if (isQueryHoliday) {
            selection = "year >= '" + startYear + "' AND year <= '" + endYear + "'";
        } else {
            selection = "year >= '" + startYear + "' AND year <= '" + endYear
                    + "' AND COLOR_TYPE = '" + COLOR_TYPE_WORKDAY + "'";
        }
        return AlarmUtils.getAlarmsHolidayInfo(selection);
    }


    /**
     * 查询给定的日期是否为法定节假日
     *
     * @param calendar 日期
     * @return 1：节假日  2 ：工作日 -1 ：默认
     */
    public static int queryWorkdayOrHoliday(Calendar calendar) {
        int colorType = -1;
        List<Holiday> holidayList = getHolidayFromCache(Calendar.getInstance());
        if (holidayList != null) {
            for (Holiday holiday : holidayList) {
                if ((calendar.get(Calendar.YEAR) == holiday.year)
                        && (calendar.get(Calendar.DAY_OF_YEAR) - 1 == holiday.yearOfDay)) {
                    Log.d(TAG, "query result:" + holiday.yearOfDay + ";" + holiday.title + "color_type:" + holiday.colorType);
                    colorType = holiday.colorType;
                    break;
                }
            }
        }
        return colorType;
    }

    /**
     * 是否为法定节假日
     *
     * @param days 特殊日期
     * @return
     */
    public static boolean isHoliday(String days) {
        Calendar cal = DatePickerUtils.getTimeForAfter1970(Long.parseLong(days));
        int holidayType = queryWorkdayOrHoliday(cal);
        if (holidayType == COLOR_TYPE_HOLIDAY) {
            //节假日
            return true;
        }
        return false;
    }


    public static int queryWorkdayOrHoliday(Calendar calendar, List<Holiday> holidayList, int workdaySwitch, Calendar workdayCalendar, Alarm alarm) {
        int colorType = -1;
        if (holidayList != null) {
            for (Holiday holiday : holidayList) {

                if ((calendar.get(Calendar.YEAR) == holiday.year)
                        && (calendar.get(Calendar.DAY_OF_YEAR) - 1 == holiday.yearOfDay)) {
                    Log.d("query", "query result:" + holiday.title + "color_type:" + holiday.colorType);
                    colorType = holiday.colorType;
                    break;
                }
            }
        }

        if (colorType != -1) {
            return colorType;
        } else {
            /*workday switch on*/
            if (workdaySwitch == 1) {
                int day = calendar.get(Calendar.DAY_OF_WEEK);
                Log.d(TAG, "workdaySwitch is on,dayOfWeek:" + day);
                int workdayType = WorkDayTypeUtils.getNeedShowWorkDayType(AlarmClockApplication.getInstance(), workdayCalendar, alarm);
                Log.i(TAG, "queryWorkdayOrHoliday workdayType = " + workdayType);
                if ((workdayType == WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY)
                        || (workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE)) {
                    if ((day >= WORKDAY_FIRST) && (day <= WORKDAY_END_SAT)) {
                        //workday
                        return COLOR_TYPE_WORKDAY;
                    } else {
                        //holiday
                        return COLOR_TYPE_HOLIDAY;
                    }
                } else if ((workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY)
                        || (workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY)) {
                    if ((day >= WORKDAY_FIRST) && (day <= WORKDAY_END_SUN)) {
                        //workday
                        return COLOR_TYPE_WORKDAY;
                    } else {
                        //holiday
                        return COLOR_TYPE_HOLIDAY;
                    }
                } else {
                    return COLOR_TYPE_HOLIDAY;
                }
            } else {
                return COLOR_TYPE_WORKDAY;
            }
        }
    }

    /**
     * @param calendar      Calendar
     * @param holidayList   holiday list
     * @param workdaySwitch 1 workday switch is open ,0 workday switch is close
     * @param priTime       the alarm time
     * @return workday or holiday
     */
    public static int queryPriTimeWorkdayOrHoliday(Calendar calendar, List<Holiday> holidayList, int workdaySwitch, long priTime, Alarm alarm) {
        int colorType = -1;
        if (holidayList != null) {
            for (Holiday holiday : holidayList) {

                if ((calendar.get(Calendar.YEAR) == holiday.year)
                        && (calendar.get(Calendar.DAY_OF_YEAR) - 1 == holiday.yearOfDay)) {
                    Log.d("query", "query result:" + holiday.title + "color_type:" + holiday.colorType);
                    colorType = holiday.colorType;
                    break;
                }
            }
        }

        if (colorType != -1) {
            return colorType;
        } else {
            /*workday switch on*/
            if (workdaySwitch == 1) {
                int day = calendar.get(Calendar.DAY_OF_WEEK);
                Log.d(TAG, "workdaySwitch is on,dayOfWeek:" + day);

                /*
                It's different from method queryWorkdayOrHoliday only here
                 */
                int workdayType = WorkDayTypeUtils.getPriTimeWorkDayType(AlarmClockApplication.getInstance(), priTime, alarm);

                Log.i(TAG, "queryWorkdayOrHoliday workdayType = " + workdayType);
                if ((workdayType == WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY)
                        || (workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE)) {
                    if ((day >= WORKDAY_FIRST) && (day <= WORKDAY_END_SAT)) {
                        //workday
                        return COLOR_TYPE_WORKDAY;
                    } else {
                        //holiday
                        return COLOR_TYPE_HOLIDAY;
                    }
                } else if ((workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY)
                        || (workdayType == WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY)) {
                    if ((day >= WORKDAY_FIRST) && (day <= WORKDAY_END_SUN)) {
                        //workday
                        return COLOR_TYPE_WORKDAY;
                    } else {
                        //holiday
                        return COLOR_TYPE_HOLIDAY;
                    }
                } else {
                    return COLOR_TYPE_HOLIDAY;
                }
            } else {
                return COLOR_TYPE_WORKDAY;
            }
        }
    }

    public static class Holiday {
        public int year;
        public int yearOfDay;
        public String title;
        //1：holiday  2:workday;
        public int colorType;

        @Override
        public String toString() {
            return "Holiday{" + "year=" + year + ", yearOfDay=" + yearOfDay + ", title='" + title + ", colorType=" + colorType + '}';
        }
    }

    /**
     * 查询给定的日期之后的第一次调休工作日
     *
     * @param calendar 日期
     * @param currentTime 当前时间
     * @param isFixAlarmManager 是否修复AlarmManger
     */
    public static long queryNextWorkday(Calendar calendar, long currentTime, boolean isFixAlarmManager) {
        long result = -1;
        if (calendar == null) {
            return result;
        }
        long currentTimeInMillis = currentTime;
        if (isFixAlarmManager) {
            currentTimeInMillis -= ONE_MINUTE;
        }
        List<Long> workdayList = queryWorkdayList(calendar);
        if (workdayList != null && !workdayList.isEmpty()) {
            Collections.sort(workdayList);
            long lastWorkdayTime = workdayList.get(workdayList.size() - 1);
            if (currentTimeInMillis > lastWorkdayTime) {
                return result;
            }
            result = findNextWorkDay(workdayList, currentTimeInMillis);
        }
        return result;
    }

    private static List<Long> queryWorkdayList(Calendar currentCalendar) {
        int startYear = currentCalendar.get(Calendar.YEAR);
        int endYear = startYear + 1;
        if (sWorkdayList == null || startYear != sCurrentYear || sWorkdayList.isEmpty()) {
            List<Holiday> workdayList = getHolidayFromDb(startYear, endYear, false);
            List<Long> tempList = new ArrayList<>();
            for (Holiday workday : workdayList) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.YEAR, workday.year);
                calendar.set(Calendar.DAY_OF_YEAR, workday.yearOfDay);
                calendar.set(Calendar.HOUR_OF_DAY, TWENTY_HOUR);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long timeInMillis = calendar.getTimeInMillis();
                tempList.add(timeInMillis);
            }
            sWorkdayList = tempList;
        }
        return sWorkdayList;
    }

    private static long findNextWorkDay(List<Long> sortedList, long target) {
        int left = 0;
        int right = sortedList.size() - 1;
        while (left <= right) {
            int mid = left + (right - left) / 2;
            long midValue = sortedList.get(mid);
            if (midValue > target) {
                if (mid == 0 || sortedList.get(mid - 1) <= target) {
                    return midValue;
                } else {
                    right = mid - 1;
                }
            } else {
                left = mid + 1;
            }
        }
        return -1;
    }
}
