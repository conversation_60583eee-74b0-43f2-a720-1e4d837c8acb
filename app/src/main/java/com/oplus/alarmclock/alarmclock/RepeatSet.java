/*
 * Copyright (C) 2013 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.text.TextUtils;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;

import java.util.Calendar;
import java.util.Locale;

/**
 * Bitmask of all repeating days mDays = 000....**********; |||||||||| ********** 0~6: Monday~Sunday
 * 7: is not used 8: is not used 9: is not used
 */
public final class RepeatSet {

    private static final String TAG = "RepeatSet";

    static final int MONDAY = 0x01;      //1
    static final int TUESDAY = 0x02;     //2
    static final int WEDNESDAY = 0x04;   //4
    static final int THURSDAY = 0x08;    //8
    static final int FRIDAY = 0x10;      //16
    static final int SATURDAY = 0x20;    //32
    static final int SUNDAY = 0x40;      //64

    static final int REPEAT_NONE = 0;   //0

    static final int FIRST_DAY_IS_MONDAY = 1;
    static final int FIRST_DAY_IS_TUESDAY = 2;
    static final int FIRST_DAY_IS_WEDNESDAY = 3;
    static final int FIRST_DAY_IS_THURSDAY = 4;
    static final int FIRST_DAY_IS_FRIDAY = 5;
    static final int FIRST_DAY_IS_SATURDAY = 6;

    public static final int REPEAT_DAILY = MONDAY | TUESDAY
            | WEDNESDAY | THURSDAY | FRIDAY | SATURDAY | SUNDAY;    //127

    public static final int MONDAY_TO_FRIDAY = MONDAY | TUESDAY
            | WEDNESDAY | THURSDAY | FRIDAY;    //31

    public static final int MONDAY_TO_SATURDAY = MONDAY | TUESDAY
            | WEDNESDAY | THURSDAY | FRIDAY | SATURDAY;    //63
    public static final int SATURDAY_TO_SUNDAY = SATURDAY | SUNDAY;

    private static final int[] SPE_WEEK_NUMS = {REPEAT_NONE, REPEAT_DAILY};

    private static final int DAYS_OF_WEEK = 7;
    private static final int WORKDAYS_OF_WEEK = 5;

    //Note: Weekday index according to R.array.days_of_week.
    private static final int MONDAY_INDEX = 0;
    private static final int TUESDAY_INDEX = 1;
    private static final int WEDNESDAY_INDEX = 2;
    private static final int THURSDAY_INDEX = 3;
    private static final int FRIDAY_INDEX = 4;
    private static final int SATURDAY_INDEX = 5;
    private static final int SUNDAY_INDEX = 6;

    /**
     * 获取闹钟响铃描述信息
     *
     * @param context
     * @param repeatSet
     * @param workdaySwitch
     * @param holidaySwitch
     * @param showNever
     * @param alarm
     * @return
     */
    public static String getDescription(Context context, int repeatSet, int workdaySwitch, int holidaySwitch, boolean showNever, Alarm alarm, boolean isShowAll) {
        if (context == null) {
            context = AlarmClockApplication.getInstance();
        }
        //轮班制闹钟
        if (alarm.getmLoopSwitch() == 1) {
            return context.getString(R.string.loop_alarm_title);
        }
        //秒抢闹钟
        if (alarm.getmGarbSwitch() == 1) {
            return context.getString(R.string.grab_alarm_title);
        }
        if (repeatSet == 0 && (alarm.getmSpecialAlarmDays() == null || DatePickerUtils.SPLIT.equals(alarm.getmSpecialAlarmDays())) && workdaySwitch == 0) {
            return context.getString(R.string.ring_once);
        }
        String result = "";
        if ((workdaySwitch == 1) && (!DeviceUtils.isExpVersion(context))) {
            //workday alarm
            int workdayType = WorkDayTypeUtils.getNeedShowWorkDayType(context, alarm);
            Log.i(TAG, "getDescription workdayType = " + workdayType);
            result = WorkDayTypeUtils.getWorkdayTypeLabelString(workdayType);
            return result;
        }
        int alarmCount = 3;
        if (isShowAll) {
            alarmCount = AlarmUtils.ALARM_DESCRIPTION_MAX;
        }

        if (repeatSet != 0 && DatePickerUtils.SPLIT.equals(alarm.getmSpecialAlarmDays())) {
            //自定义重复闹铃
            result = getDescription(context, repeatSet, showNever);
        } else if (!DatePickerUtils.SPLIT.equals(alarm.getmSpecialAlarmDays())) {
            String alarmArrStr;
            String space = context.getText(R.string.day_concat).toString();
            //自定义闹铃
            if (alarm.isCloseOnceAlarmClock()) {
                //关闭下一次响铃的闹钟 
                Calendar nextCalendar = Calendar.getInstance();
                nextCalendar.setTimeInMillis(alarm.getmCloseOnceTimeNext());
                //跳过
                nextCalendar.set(Calendar.SECOND, 1);
                alarmArrStr = AlarmUtils.getCustomAlarmDateDescription(alarm, context, false, alarmCount, nextCalendar);
            } else {
                //普通重复闹钟
                alarmArrStr = AlarmUtils.getCustomAlarmDateDescription(alarm, context, false, alarmCount, Calendar.getInstance());
            }
            String[] specArr = alarm.getmSpecialAlarmDays().split(DatePickerUtils.SPLIT);
            if ((specArr.length > 4) || (repeatSet != 0)) {
                result = alarmArrStr + context.getString(R.string.alarm_list_more);
            } else {
                result = alarmArrStr;
            }
        }
        if (holidaySwitch == 1 && (!DeviceUtils.isExpVersion(context))) {
            String space = context.getText(R.string.day_concat).toString();
            result += space + context.getString(R.string.exclude_holiday);
        }
        return result;
    }

    public static String getDescription(Context context, int repeatSet, boolean showNever) {
        return getDescription(context, repeatSet, showNever, SPE_WEEK_NUMS,
                context.getResources().getStringArray(R.array.alarm_repeat_strings_2));
    }

    public static String getDescription(Context context, int repeatSet, boolean showNever,
                                        int[] specialDays, String[] specialStr) {
        if (context == null) {
            context = AlarmClockApplication.getInstance();
        }
        String specialString = null;
        if (specialDays != null && specialStr != null && specialDays.length <= specialStr.length) {
            for (int i = 0; i < specialDays.length; i++) {
                if (repeatSet == specialDays[i]) {
                    specialString = specialStr[i];
                }
            }
        }
        Log.v(TAG, "RepeatSet: " + repeatSet + ", specialString: " + specialString);

        // no days
        if (repeatSet == REPEAT_NONE) {
            if (specialString == null) {
                return showNever ? context.getString(R.string.ring_once) : "";
            } else {
                return showNever ? specialString : "";
            }
        }

        // every day
        if (repeatSet == REPEAT_DAILY) {
            if (specialString == null) {
                return context.getString(R.string.every_day);
            } else {
                return specialString;
            }
        }

        if (repeatSet == MONDAY_TO_FRIDAY) {
            return context.getString(R.string.monday_to_friday);
        }

        if (repeatSet == MONDAY_TO_SATURDAY) {
            return context.getString(R.string.monday_to_saturday);
        }

        if (specialString != null) {
            return specialString;
        }

        // count selected days
        int dayCount = 0;
        int days = repeatSet;
        while (days > 0) {
            if ((days & 1) == 1) {
                dayCount++;
            }
            days >>= 1;
        }

        // short or long form.
        CharSequence[] dayList = context.getResources()
                .getTextArray(R.array.days_of_week_short);


        StringBuilder ret = new StringBuilder();
        // selected days
        WeekdayOrder weekdayOrder = getCheckOrder(context);
        if (weekdayOrder != null) {
            boolean isJapan = Locale.JAPAN.getLanguage().equals(Locale.getDefault().getLanguage());
            String space = context.getText(isJapan ? R.string.dot : R.string.day_concat).toString();
            int[] weekdays = weekdayOrder.mWeekdays;
            int[] orders = weekdayOrder.mOrders;
            for (int i = 0; i < weekdays.length; i++) {
                if (hasBitSet(repeatSet, weekdays[i])) {
                    ret.append(dayList[orders[i]]).append(space);
                }
            }

            int index = ret.lastIndexOf(space);
            if (index > -1) {
                ret.delete(index, index + space.length());
            }
        }

        return ret.toString();
    }

    private static boolean isSet(int days, int day) {
        return ((days & (1 << day)) > 0);
    }

    public static boolean isRepeat(int days) {
        return days != REPEAT_NONE;
    }


    /**
     * 重复日期中是否包含给定的日期
     *
     * @param day
     * @param mDays
     * @return
     */
    public static boolean isRepeat(int day, int mDays) {
        Calendar cal = DatePickerUtils.getTimeForAfter1970(day);
        int today = (cal.get(Calendar.DAY_OF_WEEK) + WORKDAYS_OF_WEEK) % DAYS_OF_WEEK;
        if (isSet(mDays, today)) {
            return true;
        }
        return false;
    }

    /*
     * returns number of days from today until next alarm
     */
    static int getNextAlarm(Calendar c, int mDays) {

        // Calendar.DAY_OF_WEEK: SUNDAY 1 .... SATURDAY 7
        // Our code: Monday... 0 Sunday 6
        int today = (c.get(Calendar.DAY_OF_WEEK) + WORKDAYS_OF_WEEK) % DAYS_OF_WEEK;

        int day = 0;
        int dayCount = 0;
        for (; dayCount < DAYS_OF_WEEK; dayCount++) {
            day = (today + dayCount) % DAYS_OF_WEEK;
            if (isSet(mDays, day)) {
                break;
            }
        }
        return dayCount;
    }

    /*
     * get number of days the last alert passed from the reference date
     */
    static int getLastAlarm(Calendar c, int mDays) {
        // Calendar.DAY_OF_WEEK: SUNDAY 1 Monday 2 .... SATURDAY 7
        // Our code: Monday ... 0 Sunday 6
        int dayCount = -1;
        int currentDayIndex = (c.get(Calendar.DAY_OF_WEEK) + WORKDAYS_OF_WEEK) % DAYS_OF_WEEK;
        for (; dayCount >= -DAYS_OF_WEEK; dayCount--) {
            int previousAlarmBitIndex = (currentDayIndex + dayCount);
            if (previousAlarmBitIndex < 0) {
                previousAlarmBitIndex = previousAlarmBitIndex + DAYS_OF_WEEK;
            }
            if (isSet(mDays, previousAlarmBitIndex)) {
                break;
            }
        }
        // return a positive value
        return dayCount * -1;
    }

    private static boolean hasBitSet(int value, int mask) {
        return (value & mask) == mask;
    }

    public static String toString(int mDays) {
        StringBuilder sb = new StringBuilder("Week Day: [");

        if (mDays == REPEAT_NONE) {
            sb.append("NONE");
        } else {
            if (hasBitSet(mDays, MONDAY)) {
                sb.append(" MON ");
            }

            if (hasBitSet(mDays, TUESDAY)) {
                sb.append(" TUES ");
            }

            if (hasBitSet(mDays, WEDNESDAY)) {
                sb.append(" WED ");
            }

            if (hasBitSet(mDays, THURSDAY)) {
                sb.append(" THUR ");
            }

            if (hasBitSet(mDays, FRIDAY)) {
                sb.append(" FRI ");
            }

            if (hasBitSet(mDays, SATURDAY)) {
                sb.append(" SAT ");
            }

            if (hasBitSet(mDays, SUNDAY)) {
                sb.append(" SUN ");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    private static class WeekdayOrder {
        private int[] mWeekdays;
        private int[] mOrders;
    }

    private static WeekdayOrder getCheckOrder(Context context) {
        if (context == null) {
            return null;
        }

        int startDay = Calendar.getInstance().getFirstDayOfWeek() - 1;
        String language = Locale.getDefault().toString();
        if (WeekDayStartFromHelper.BENGALI.equals(language)) {
            startDay = FIRST_DAY_IS_FRIDAY;
        } else if (WeekDayStartFromHelper.PORTU_PORTU.equals(language) || WeekDayStartFromHelper.KISWAHILI.equals(language)) {
            startDay = FIRST_DAY_IS_MONDAY;
        }
        Log.i(TAG, "getCheckOrder language = " + language + "startDay = " + startDay);
        WeekdayOrder weekdayOrder = new WeekdayOrder();
        switch (startDay) {
            case FIRST_DAY_IS_MONDAY:
                weekdayOrder.mWeekdays = new int[]{MONDAY, TUESDAY, WEDNESDAY, THURSDAY,
                        FRIDAY, SATURDAY, SUNDAY};
                weekdayOrder.mOrders = new int[]{MONDAY_INDEX, TUESDAY_INDEX, WEDNESDAY_INDEX,
                        THURSDAY_INDEX, FRIDAY_INDEX, SATURDAY_INDEX, SUNDAY_INDEX};
                break;
            case FIRST_DAY_IS_TUESDAY:
                weekdayOrder.mWeekdays = new int[]{TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY,
                        SUNDAY, MONDAY};
                weekdayOrder.mOrders = new int[]{TUESDAY_INDEX, WEDNESDAY_INDEX, THURSDAY_INDEX,
                        FRIDAY_INDEX, SATURDAY_INDEX, SUNDAY_INDEX, MONDAY_INDEX};
                break;
            case FIRST_DAY_IS_WEDNESDAY:
                weekdayOrder.mWeekdays = new int[]{WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY,
                        MONDAY, TUESDAY};
                weekdayOrder.mOrders = new int[]{WEDNESDAY_INDEX, THURSDAY_INDEX, FRIDAY_INDEX,
                        SATURDAY_INDEX, SUNDAY_INDEX, MONDAY_INDEX, TUESDAY_INDEX};
                break;
            case FIRST_DAY_IS_THURSDAY:
                weekdayOrder.mWeekdays = new int[]{THURSDAY, FRIDAY, SATURDAY, SUNDAY, MONDAY,
                        TUESDAY, WEDNESDAY};
                weekdayOrder.mOrders = new int[]{THURSDAY_INDEX, FRIDAY_INDEX, SATURDAY_INDEX,
                        SUNDAY_INDEX, MONDAY_INDEX, TUESDAY_INDEX, WEDNESDAY_INDEX};
                break;
            case FIRST_DAY_IS_FRIDAY:
                weekdayOrder.mWeekdays = new int[]{FRIDAY, SATURDAY, SUNDAY, MONDAY, TUESDAY,
                        WEDNESDAY, THURSDAY};
                weekdayOrder.mOrders = new int[]{FRIDAY_INDEX, SATURDAY_INDEX, SUNDAY_INDEX,
                        MONDAY_INDEX, TUESDAY_INDEX, WEDNESDAY_INDEX, THURSDAY_INDEX};
                break;
            case FIRST_DAY_IS_SATURDAY:
                weekdayOrder.mWeekdays = new int[]{SATURDAY, SUNDAY, MONDAY, TUESDAY, WEDNESDAY,
                        THURSDAY, FRIDAY};
                weekdayOrder.mOrders = new int[]{SATURDAY_INDEX, SUNDAY_INDEX, MONDAY_INDEX,
                        TUESDAY_INDEX, WEDNESDAY_INDEX, THURSDAY_INDEX, FRIDAY_INDEX};
                break;
            default:    //case WeekDayStartFromHelper.EN_SUNDAY
                weekdayOrder.mWeekdays = new int[]{SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY,
                        FRIDAY, SATURDAY};
                weekdayOrder.mOrders = new int[]{SUNDAY_INDEX, MONDAY_INDEX, TUESDAY_INDEX,
                        WEDNESDAY_INDEX, THURSDAY_INDEX, FRIDAY_INDEX, SATURDAY_INDEX};
                break;
        }
        return weekdayOrder;
    }
}
