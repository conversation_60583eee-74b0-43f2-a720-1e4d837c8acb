/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : The Alarm Object for the AlarmClock application
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-10, <PERSON>, create
 * v1.1, 2018-8-10, <PERSON><PERSON><PERSON>, clean the code.
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import static com.oplus.alarmclock.alarmclock.utils.DatePickerUtils.getTimeForAfter1970;
import static com.oplus.alarmclock.utils.ClockConstant.LOOP_DEFAULT_ID;
import static com.oplus.alarmclock.utils.ClockConstant.SETTING_PKG;

import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.clock.common.utils.Log;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.UUID;

public final class Alarm implements Parcelable, Cloneable {


    /**
     * Alarms start with an invalid id when it hasn't been saved to the database.
     */
    public static final long INVALID_ID = -1;

    private long mId;
    private boolean mEnabled;
    private int mHour;
    private int mMinutes;
    private int mRepeatSet = RepeatSet.REPEAT_NONE;
    private long mTime;
    private String mLabel;
    private Uri mAlert;
    private String mRingName;
    private boolean mSilent;
    //0 only ring, 1 vibrate only , 2 vibrate and ring
    private int mAlertType;
    /* 1 will delete the alarm when dismissed, if it is a repeat alarm, don't delete */
    private int mDeleteAfterUse;
    /* ring volume, user can change. */
    private int mVolume = 0;
    /* snooze 5 minutes, default. */
    private int mSnoonzeItem = ClockConstant.SNOOZE_SWITCH_ON_5_MIN;

    //for multi-select
    private boolean mSelected;
    // vibrate type @WaveformEffect.EffectType
    private int mVibrate = WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE;
    //workday enable switch 0:off, 1:on
    private int mWorkdaySwitch;
    //holiday enable switch 0:off, 1:on
    private int mHolidaySwitch;
    //user id ,owner:0
    private int mOwnerUserId;
    //close alarm once
    private long mCloseOncePriTime;
    //close alarm once time
    private long mCloseOnceTimeNext;

    //是否关联iot智能场景 是：2 否：1
    private int mEnableAssociate = 1;

    private String mUUID;
    /**
     * 稍后提醒时长,默认5分钟
     */
    private int mSnoozeTime = ClockConstant.SNOOZE_AFTER_MIN;

    /**
     * 工作日类型
     */
    private int mWorkDayType;

    /**
     * 闹钟创建时间
     */
    private long mWorkdayUpdateTime;

    /**
     * 自定义闹钟排除的特殊日期
     */
    private String mSpecialAlarmDays = DatePickerUtils.SPLIT;

    /**
     * 是否为默认闹钟
     */
    private int mDefaultAlarm = 0;

    /**
     * 响铃次数
     */
    private int mRingNum = ClockConstant.SNOOZE_RING_NUM;

    /**
     * 是否轮班闹钟
     */
    private int mLoopSwitch = 0;
    /**
     * 轮班周期天数
     */
    private int mLoopCycleDays = 0;

    /**
     * 轮班闹钟ID
     */
    private int mLoopID = -1;
    /**
     * 轮班闹钟工作天数
     */
    private int mLoopWorkDays = 0;
    /**
     * 轮班闹钟编号
     */
    private int mLoopAlarmNumber = 0;
    /**
     * 当前轮班第几天
     */
    private int mLoopDay = 0;
    /**
     * 当前哪几天休息
     */
    private String mLoopRestDays = DatePickerUtils.SPLIT;

    /**
     * 轮班闹钟子列表
     */
    private List<Alarm> mLoopAlarmList = new ArrayList<>();

    /**
     * 响铃Uri绝对路径
     */
    private String mRingAbsolutePath;

    /**
     * 秒抢闹钟开关
     */
    private int mGarbSwitch = 0;

    public String getRingAbsolutePath() {
        return mRingAbsolutePath;
    }

    public void setRingAbsolutePath(String mRingAbsolutePath) {
        this.mRingAbsolutePath = mRingAbsolutePath;
    }

    public List<Alarm> getLoopAlarmList() {
        return mLoopAlarmList;
    }

    public void setLoopAlarmList(List<Alarm> loopAlarmList) {
        this.mLoopAlarmList = loopAlarmList;
    }


    public String getmLoopRestDays() {
        return mLoopRestDays;
    }

    public void setmLoopRestDays(String mLoopRestDays) {
        this.mLoopRestDays = mLoopRestDays;
    }

    public int getmLoopDay() {
        return mLoopDay;
    }

    public void setmLoopDay(int mLoopDay) {
        this.mLoopDay = mLoopDay;
    }

    public int getmLoopAlarmNumber() {
        return mLoopAlarmNumber;
    }

    public void setmLoopAlarmNumber(int mLoopAlarmNumber) {
        this.mLoopAlarmNumber = mLoopAlarmNumber;
    }

    public int getmLoopWorkDays() {
        return mLoopWorkDays;
    }

    public void setmLoopWorkDays(int mLoopWorkDays) {
        this.mLoopWorkDays = mLoopWorkDays;
    }

    public int getmLoopID() {
        return mLoopID;
    }

    public void setmLoopID(int mLoopID) {
        this.mLoopID = mLoopID;
    }

    public int getmLoopCycleDays() {
        return mLoopCycleDays;
    }

    public void setmLoopCycleDays(int mLoopCycleDays) {
        this.mLoopCycleDays = mLoopCycleDays;
    }

    public int getmLoopSwitch() {
        return mLoopSwitch;
    }

    public void setmLoopSwitch(int mLoopSwitch) {
        this.mLoopSwitch = mLoopSwitch;
    }

    public int getRingNum() {
        return mRingNum;
    }

    public void setRingNum(int mRingNum) {
        this.mRingNum = mRingNum;
    }

    // Create a default alarm using current time.
    public Alarm() {
        Calendar c = Calendar.getInstance();
        mHour = c.get(Calendar.HOUR_OF_DAY);
        mMinutes = c.get(Calendar.MINUTE);

        // once, default, user can change.
        // Vib. and ring, user cannot change.
        mAlertType = 2;
        // default ring uri, usr cannot change.
        mAlert = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM);
        mRingAbsolutePath = AlarmRingUtils.getRingAbsolutePath(AlarmClockApplication.getInstance(), mAlert);
        mLoopID = LOOP_DEFAULT_ID;
        mOwnerUserId = AppPlatformUtils.myUserId();

        mUUID = UUID.randomUUID().toString().replace("-", "");
        //添加默认工作日更新时间
        mWorkdayUpdateTime = System.currentTimeMillis();
    }

    public static Alarm build(boolean enable, int hour, int minute,
                              int repeatSet, int alerttype, String label,
                              Uri alertUri, String ringName, int volume,
                              int deleteAfterUse, int vibrate, int workdaySwitch, int holidaySwitch) {
        Alarm alarm = new Alarm();
        alarm.setEnabled(enable);
        alarm.setHour(hour);
        alarm.setMinutes(minute);
        alarm.setRepeat(repeatSet);
        alarm.setAlertType(alerttype);
        alarm.setLabel(label);
        alarm.setAlert(alertUri);
        alarm.setRingName(ringName);
        alarm.setVolume(volume);
        alarm.setDeleteAfterUse(deleteAfterUse);
        alarm.setVibrate(vibrate);
        alarm.setWorkdaySwitch(workdaySwitch);
        alarm.setHolidaySwitch(holidaySwitch);
        return alarm;
    }

    public static Alarm build(boolean enable, int hour, int minute,
                              int repeatSet, int alerttype, String label,
                              Uri alertUri, String ringName, int volume,
                              int deleteAfterUse, int workdaySwitch, int holidaySwitch) {
        return build(enable, hour, minute, repeatSet, alerttype, label, alertUri, ringName, volume, deleteAfterUse,
                WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE, workdaySwitch, holidaySwitch);
    }

    Alarm(Parcel p) {
        mId = p.readLong();
        mEnabled = p.readInt() == 1;
        mHour = p.readInt();
        mMinutes = p.readInt();
        mRepeatSet = p.readInt();
        mTime = p.readLong();
        mAlertType = p.readInt();
        mLabel = p.readString();
        mAlert = (Uri) p.readParcelable(null);
        mRingName = p.readString();
        mSilent = p.readInt() == 1;
        mSnoonzeItem = p.readInt();
        mVolume = p.readInt();
        mDeleteAfterUse = p.readInt();
        mVibrate = p.readInt();
        mWorkdaySwitch = p.readInt();
        mHolidaySwitch = p.readInt();
        mOwnerUserId = p.readInt();
        mCloseOncePriTime = p.readLong();
        mCloseOnceTimeNext = p.readLong();
        mEnableAssociate = p.readInt();
        mUUID = p.readString();
        mSnoozeTime = p.readInt();
        mWorkDayType = p.readInt();
        mWorkdayUpdateTime = p.readLong();
        mSpecialAlarmDays = p.readString();
        mDefaultAlarm = p.readInt();
        mRingNum = p.readInt();
        mLoopSwitch = p.readInt();
        mLoopCycleDays = p.readInt();
        mLoopID = p.readInt();
        mLoopWorkDays = p.readInt();
        mLoopAlarmNumber = p.readInt();
        mLoopDay = p.readInt();
        mLoopRestDays = p.readString();
        mLoopAlarmList = p.readArrayList(Alarm.class.getClassLoader());
        mRingAbsolutePath = p.readString();
        mGarbSwitch = p.readInt();
    }

    // Getters and setters.
    public long getId() {
        return mId;
    }

    public void setId(long id) {
        this.mId = id;
    }

    public boolean isEnabled() {
        return mEnabled;
    }

    public void setEnabled(boolean enabled) {
        this.mEnabled = enabled;
    }

    public int getHour() {
        return mHour;
    }

    public void setHour(int hour) {
        this.mHour = hour;
    }

    public int getMinutes() {
        return mMinutes;
    }

    public void setMinutes(int minutes) {
        this.mMinutes = minutes;
    }

    public int getRepeatSet() {
        return mRepeatSet;
    }

    public void setRepeat(int repeatSet) {
        this.mRepeatSet = repeatSet;
    }

    public long getTime() {
        return mTime;
    }

    public void setTime(long time) {
        this.mTime = time;
    }

    public String getLabel() {
        return mLabel;
    }

    public void setLabel(String label) {
        this.mLabel = label;
    }

    public Uri getAlert() {
        return mAlert;
    }

    public void setAlert(Uri alert) {
        this.mAlert = alert;
    }

    public int getVolume() {
        return mVolume;
    }

    public void setVolume(int volume) {
        this.mVolume = volume;
    }

    public String getRingName() {
        return mRingName;
    }

    public void setRingName(String ringName) {
        this.mRingName = ringName;
    }

    public boolean isSilent() {
        return mSilent;
    }

    public void setSilent(boolean silent) {
        this.mSilent = silent;
    }

    public int getSnoonzeItem() {
        return mSnoonzeItem;
    }

    public void setSnoonzeItem(int snoonzeItem) {
        this.mSnoonzeItem = snoonzeItem;
    }

    public int getAlertType() {
        return mAlertType;
    }

    public void setAlertType(int alertType) {
        this.mAlertType = alertType;
    }

    public int getDeleteAfterUse() {
        return mDeleteAfterUse;
    }

    public void setDeleteAfterUse(int deleteAfterUse) {
        this.mDeleteAfterUse = deleteAfterUse;
    }

    public int getWorkdaySwitch() {
        return mWorkdaySwitch;
    }

    public void setWorkdaySwitch(int mWorkdaySwitch) {
        this.mWorkdaySwitch = mWorkdaySwitch;
    }

    public int getHolidaySwitch() {
        return mHolidaySwitch;
    }

    public void setHolidaySwitch(int mHolidaySwitch) {
        this.mHolidaySwitch = mHolidaySwitch;
    }

    public long getmCloseOncePriTime() {
        return mCloseOncePriTime;
    }

    public void setmCloseOncePriTime(long mCloseOncePriTime) {
        this.mCloseOncePriTime = mCloseOncePriTime;
    }

    public long getmCloseOnceTimeNext() {
        return mCloseOnceTimeNext;
    }

    public void setmCloseOnceTimeNext(long mCloseOnceTimeNext) {
        this.mCloseOnceTimeNext = mCloseOnceTimeNext;
    }

    public boolean isSelected() {
        return mSelected;
    }

    public void setSelected(boolean selected) {
        mSelected = selected;
    }

    public int getVibrate() {
        return mVibrate;
    }

    public int getmSnoozeTime() {
        return mSnoozeTime;
    }

    public void setmSnoozeTime(int mSnoozeTime) {
        this.mSnoozeTime = mSnoozeTime;
    }

    public void setVibrate(int vibrate) {
        //to avoid unExcepted value
        if ((vibrate == WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE)
                || (vibrate == WaveformEffect.EFFECT_NOTIFICATION_SYMPHONIC)
                || (vibrate == WaveformEffect.EFFECT_NOTIFICATION_STREAK)
                || (vibrate == WaveformEffect.EFFECT_NOTIFICATION_HEARTBEAT)
                || (vibrate == WaveformEffect.EFFECT_NOTIFICATION_REMIND)
                || (vibrate == WaveformEffect.EFFECT_NOTIFICATION_RAPID)
                || (vibrate == WaveformEffect.EFFECT_RINGTONE_NOVIBRATE)) {
            this.mVibrate = vibrate;
        } else {
            this.mVibrate = WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE;
        }
    }

    public int getOwnerUserId() {
        return mOwnerUserId;
    }

    public void setOwnerUserId(int ownerUserId) {
        this.mOwnerUserId = ownerUserId;
    }


    public int getEnableAssociate() {
        return mEnableAssociate;
    }

    public String getUUID() {
        return mUUID;
    }

    public void setUUID(String uuid) {
        this.mUUID = uuid;
    }

    public int getmWorkDayType() {
        return mWorkDayType;
    }

    public void setmWorkDayType(int mWorkDayType) {
        this.mWorkDayType = mWorkDayType;
    }

    public long getmWorkdayUpdateTime() {
        return mWorkdayUpdateTime;
    }

    public void setmWorkdayUpdateTime(long mWorkdayUpdateTime) {
        this.mWorkdayUpdateTime = mWorkdayUpdateTime;
    }

    public void setEnableAssociate(int enableAssociate) {
        this.mEnableAssociate = enableAssociate;
    }

    public String getmSpecialAlarmDays() {
        if (!(TextUtils.isEmpty(mSpecialAlarmDays)) && !("null".equalsIgnoreCase(mSpecialAlarmDays))) {
            return mSpecialAlarmDays;
        } else {
            return DatePickerUtils.SPLIT;
        }
    }

    public void setmSpecialAlarmDays(String mSpecialAlarmDays) {
        this.mSpecialAlarmDays = mSpecialAlarmDays;
    }

    public int getmDefaultAlarm() {
        return mDefaultAlarm;
    }

    public void setmDefaultAlarm(int mDefaultAlarm) {
        this.mDefaultAlarm = mDefaultAlarm;
    }

    public static final Creator<Alarm> CREATOR = new Creator<Alarm>() {
        @Override
        public Alarm createFromParcel(Parcel p) {
            return new Alarm(p);
        }

        @Override
        public Alarm[] newArray(int size) {
            return new Alarm[size];
        }
    };

    @Override
    public void writeToParcel(Parcel p, int flags) {
        p.writeLong(mId);
        p.writeInt(mEnabled ? 1 : 0);
        p.writeInt(mHour);
        p.writeInt(mMinutes);
        p.writeInt(mRepeatSet);
        p.writeLong(mTime);
        p.writeInt(mAlertType);
        p.writeString(mLabel);
        p.writeParcelable(mAlert, flags);
        p.writeString(mRingName);
        p.writeInt(mSilent ? 1 : 0);
        p.writeInt(mSnoonzeItem);
        p.writeInt(mVolume);
        p.writeInt(mDeleteAfterUse);
        p.writeInt(mVibrate);
        p.writeInt(mWorkdaySwitch);
        p.writeInt(mHolidaySwitch);
        p.writeInt(mOwnerUserId);
        p.writeLong(mCloseOncePriTime);
        p.writeLong(mCloseOnceTimeNext);
        p.writeInt(mEnableAssociate);
        p.writeString(mUUID);
        p.writeInt(mSnoozeTime);
        p.writeInt(mWorkDayType);
        p.writeLong(mWorkdayUpdateTime);
        p.writeString(mSpecialAlarmDays);
        p.writeInt(mDefaultAlarm);
        p.writeInt(mRingNum);
        p.writeInt(mLoopSwitch);
        p.writeInt(mLoopCycleDays);
        p.writeInt(mLoopID);
        p.writeInt(mLoopWorkDays);
        p.writeInt(mLoopAlarmNumber);
        p.writeInt(mLoopDay);
        p.writeString(mLoopRestDays);
        p.writeList(mLoopAlarmList);
        p.writeString(mRingAbsolutePath);
        p.writeInt(mGarbSwitch);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public boolean timeEqual(Alarm alarm) {
        return ((alarm != null) && (alarm.mHour == this.mHour) && (alarm.mMinutes == this.mMinutes));
    }

    @Override
    public boolean equals(Object o) {
        return (o != null) && (o instanceof Alarm) && (mId == ((Alarm) o).mId);
    }

    @Override
    public int hashCode() {
        return Long.valueOf(mId).hashCode();
    }

    @Override
    public String toString() {
        return "Alarm{"
                + "mId=" + mId
                + ", mEnabled=" + mEnabled
                + ", mHour=" + mHour
                + ", mMinutes=" + mMinutes
                + ", mRepeatSet=" + mRepeatSet
                + ", mTime=" + mTime
                + ", mLabel='" + (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance()) ? "" : mLabel)
                + ", mAlert=" + mAlert
                + ", mRingName='" + mRingName
                + ", mSilent=" + mSilent
                + ", mAlertType=" + mAlertType
                + ", mDeleteAfterUse=" + mDeleteAfterUse
                + ", mVolume=" + mVolume
                + ", mSnoonzeItem=" + mSnoonzeItem
                + ", mSelected=" + mSelected
                + ", mVibrate=" + mVibrate
                + ", mWorkdaySwitch=" + mWorkdaySwitch
                + ", mHolidaySwitch=" + mHolidaySwitch
                + ", mOwnerUserId=" + mOwnerUserId
                + ", mCloseOncePriTime=" + mCloseOncePriTime
                + ", mCloseOnceTime=" + mCloseOnceTimeNext
                + ", mEnableAssociate=" + mEnableAssociate
                + ", mUUID=" + getPrintUUID(mUUID)
                + ", mSnoozeTime=" + mSnoozeTime
                + ", mWorkDayType=" + mWorkDayType
                + ", mWorkdayUpdateTime=" + Formatter.formatTime(mWorkdayUpdateTime)
                + ", mSpecialAlarmDays=" + mSpecialAlarmDays
                + ", mDefaultAlarm=" + mDefaultAlarm
                + ", mRingNum=" + mRingNum
                + ", mLoopSwitch=" + mLoopSwitch
                + ", mLoopCycleDays=" + mLoopCycleDays
                + ", mLoopID=" + mLoopID
                + ", mLoopWorkDays=" + mLoopWorkDays
                + ", mLoopAlarmNumber=" + mLoopAlarmNumber
                + ", mLoopDay=" + mLoopDay
                + ", mLoopRestDays=" + mLoopRestDays
                + ", loopList size=" + mLoopAlarmList.size()
                + ", mRingAbsolutePath=" + mRingAbsolutePath
                + ", mGarbSwitch=" + mGarbSwitch
                + '}';
    }

    @NonNull
    @Override
    public Alarm clone() throws CloneNotSupportedException {
        return (Alarm) super.clone();
    }

    private String getPrintUUID(String uuid) {
        if (TextUtils.isEmpty(uuid)) {
            return "";
        }
        int numberSix = 6;
        if (uuid.length() < numberSix) {
            return uuid;
        }
        return uuid.substring(0, uuid.length() - numberSix) + "******";
    }

    /**
     * 是否有两个以上自定义日期
     *
     * @return
     */
    public boolean isHaveTwoSpecialDays() {
        String[] specArr = getmSpecialAlarmDays().split(DatePickerUtils.SPLIT);
        if (specArr.length > 2) {
            return true;
        }
        return false;
    }

    /**
     * 是否为重复闹钟
     *
     * @return
     */
    public boolean isRepeatAlarm() {
        if ((RepeatSet.isRepeat(getRepeatSet())) || (getWorkdaySwitch() == 1) || (isHaveTwoSpecialDays()) || (getmLoopSwitch() == 1)) {
            return true;
        }
        return false;
    }

    /**
     * 相对于 nowTime 是否为重复闹钟
     *
     * @param nowTime
     * @return
     */
    public boolean isRepeatAlarm(Calendar nowTime) {
        if (getmGarbSwitch() == 1) {
            //秒抢闹钟
            return GarbAlarmUtils.garbAlarmIsRepeatAlarm(this,nowTime);
        }
        boolean isRepeatSpecialAlarm = false;
        if (isSpecialDaysAlarm()) {
            if (AlarmUtils.specialAlarmIsHaveNextTime(this, nowTime.getTimeInMillis())) {
                isRepeatSpecialAlarm = true;
            }
        }
        if ((RepeatSet.isRepeat(getRepeatSet())) || (getWorkdaySwitch() == 1) || (isRepeatSpecialAlarm) || (getmLoopSwitch() == 1)) {
            return true;
        }
        return false;
    }

    /**
     * 是否是关闭一次的闹钟
     *
     * @return
     */
    public boolean isCloseOnceAlarmClock() {
        if ((getmCloseOnceTimeNext() > 0) && (getmCloseOncePriTime() > 0)) {
            return true;
        }
        return false;
    }

    /**
     * 是否含有特殊日期
     *
     * @return
     */
    public boolean isSpecialDaysAlarm() {
        if (!TextUtils.isEmpty(mSpecialAlarmDays) && !DatePickerUtils.SPLIT.equals(mSpecialAlarmDays)) {
            return true;
        }
        return false;
    }

    /**
     * 深度克隆
     *
     * @return
     * @throws CloneNotSupportedException
     */
    public Alarm deepCopy() {
        Alarm newAlarm = null;
        try {
            newAlarm = this.clone();
            List<Alarm> newLoopAlarmList = new ArrayList<>();
            for (int i = 0; i < mLoopAlarmList.size(); i++) {
                newLoopAlarmList.add(mLoopAlarmList.get(i).clone());
            }
            newAlarm.mLoopAlarmList = newLoopAlarmList;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
        return newAlarm;
    }

    public int getmGarbSwitch() {
        return mGarbSwitch;
    }

    public void setmGarbSwitch(int mGarbSwitch) {
        this.mGarbSwitch = mGarbSwitch;
    }

}
