/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : set workday type utils
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-02-20, xiaolong,yu, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.content.SharedPreferences;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class WorkDayTypeUtils {

    public static final int WORKDAY_TYPE_WORKDAY = 0;
    public static final int WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY = 1;
    public static final int WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY = 2;
    public static final int WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE = 3;

    private final static String TAG = "WorkDayTypeUtils";
    private final static String ALARM_CLOCK_WORKDAY_TYPE_KEY = "alarm_clock_workday_type_key";
    private final static String ALARM_CLOCK_WORKDAY_TYPE_SET_TIME_KEY = "alarm_clock_workday_type_set_time_key";
    private final static long DEFAULT_SET_WORKDAY_TYPE_TIME = -1;

    /**
     * 获取需要显示的工作日类型
     *
     * @param context
     * @param alarm
     * @return
     */
    public static int getNeedShowWorkDayType(Context context, Alarm alarm) {
        if (alarm == null) {
            return WORKDAY_TYPE_WORKDAY;
        }
        return getNeedShowWorkDayType(context, Calendar.getInstance(Locale.CHINA), alarm);
    }

    public static int getNeedShowWorkDayType(Context context, Calendar calendarCurrent, Alarm alarm) {
        if (context != null) {
            int workdayType = alarm.getmWorkDayType();
            long setWorkdayTime = alarm.getmWorkdayUpdateTime();
            Log.d(TAG, "getNeedShowWorkDayType user set workday type is : " + workdayType + " , setWorkdayTime is : " + setWorkdayTime);
            if ((workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) || (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE)) {
                if (setWorkdayTime > 0) {

                    //get current date info
                    calendarCurrent.setFirstDayOfWeek(Calendar.MONDAY);
                    int weekOfYearCurrent = calendarCurrent.get(Calendar.WEEK_OF_YEAR);
                    int weekYearCurrent = calendarCurrent.get(Calendar.YEAR);
                    int weekMonthCurrent = calendarCurrent.get(Calendar.MONTH) + 1;
                    int weeksInWeekYearCurrent = calendarCurrent.getWeeksInWeekYear();
                    int weekYearCount = calendarCurrent.getWeekYear();
                    Log.d(TAG, "getNeedShowWorkDayType weeksInWeekYearCurrent = " + weeksInWeekYearCurrent + " - " + weekYearCurrent
                            + " - " + weekMonthCurrent + " - " + calendarCurrent.get(Calendar.DAY_OF_MONTH)
                            + "  weekYearCount = " + weekYearCount + "  weekOfYearCurrent =" + weekOfYearCurrent);

                    //get set workday type date info
                    Calendar calendarSet = Calendar.getInstance(Locale.CHINA);
                    calendarSet.setTimeInMillis(setWorkdayTime);
                    calendarSet.setFirstDayOfWeek(Calendar.MONDAY);
                    int weekOfYearSet = calendarSet.get(Calendar.WEEK_OF_YEAR);
                    int weekYearSet = calendarSet.get(Calendar.YEAR);
                    int weekMonthSet = calendarSet.get(Calendar.MONTH) + 1;
                    int weekDaySet = calendarSet.get(Calendar.DAY_OF_MONTH);

                    int weekCountSet = calendarSet.getWeekYear();
                    int weeksInWeekYearSet = calendarSet.getWeeksInWeekYear();
                    Log.i(TAG, "getNeedShowWorkDayType set date = " + weekOfYearSet + "  weekYearSet = "
                            + weekYearSet + " - " + weekMonthSet + " - " + weekDaySet + "  weekCountSet = "
                            + weekCountSet + "  getWeeksInWeekYear = " + weeksInWeekYearSet);
                    Log.i(TAG, "getNeedShowWorkDayType weekYearCurrent = " + weekYearCurrent + "  weekYearSet = " + weekYearSet);
                    //if current year is equal to workday set year ,we can use weeks in week year of current - set
                    if (weekYearCurrent == weekYearSet) {
                        return whenCurrentIsSetYear(weekOfYearCurrent, weekOfYearSet, workdayType);
                    } else {

                        int monthOfYearLast = 12;
                        int monthOfYearFirst = 1;
                        Log.i(TAG, "getNeedShowWorkDayType weekYearCurrent = " + weekYearCurrent + "  weekYearSet = " + weekYearSet);
                        //current and set year differs 1 year , and set month of current month is december (12 Dec),and the week is first of year
                        // (for example:2019-12-30,is the first meek of 2020 )
                        if ((Math.abs(weekYearCurrent - weekYearSet) == 1) && (((weekMonthSet == monthOfYearLast) && (weekOfYearSet == monthOfYearFirst))
                                || ((weekMonthCurrent == monthOfYearLast) && (weekOfYearCurrent == monthOfYearFirst)))) {
                            return whenSetOrCurrentIsDecAndThisIsFirstWearOrYear(weekOfYearCurrent, weekOfYearSet, workdayType);
                        } else {
                            return whenOtherScene(weekYearCurrent, weekYearSet, calendarSet, calendarCurrent, workdayType);
                        }
                    }
                } else {
                    return WORKDAY_TYPE_WORKDAY;
                }
            } else {
                return workdayType;
            }
        }
        return WORKDAY_TYPE_WORKDAY;
    }

    /**
     * @param priTime last work clock time
     * @return ast work's workday type
     */
    public static int getPriTimeWorkDayType(Context context, long priTime, Alarm alarm) {
        if (context != null) {
            int workdayType = alarm.getmWorkDayType();
            long setWorkdayTime = alarm.getmWorkdayUpdateTime();

            Log.i(TAG, "getPriTimeWorkDayType user set workday type is : " + workdayType + " , setWorkdayTime is : " + setWorkdayTime);
            if ((workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) || (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE)) {
                if (setWorkdayTime > 0) {

                    //get current date info
                    Calendar calendarCurrent = Calendar.getInstance(Locale.CHINA);
                    calendarCurrent.setFirstDayOfWeek(Calendar.MONDAY);
                    if (priTime > 0) {
                        calendarCurrent.setTimeInMillis(priTime);
                    }
                    //current time
                    int weekOfYearCurrent = calendarCurrent.get(Calendar.WEEK_OF_YEAR);
                    int weekYearCurrent = calendarCurrent.get(Calendar.YEAR);
                    int weekMonthCurrent = calendarCurrent.get(Calendar.MONTH) + 1;
                    int weeksInWeekYearCurrent = calendarCurrent.getWeeksInWeekYear();
                    int weekYearCount = calendarCurrent.getWeekYear();
                    Log.i(TAG, "getPriTimeWorkDayType weeksInWeekYearCurrent = " + weeksInWeekYearCurrent + " - " + weekYearCurrent
                            + " - " + weekMonthCurrent + " - " + calendarCurrent.get(Calendar.DAY_OF_MONTH)
                            + "  weekYearCount = " + weekYearCount + "  weekOfYearCurrent =" + weekOfYearCurrent);

                    //get set workday type date info
                    Calendar calendarSet = Calendar.getInstance(Locale.CHINA);
                    calendarSet.setTimeInMillis(setWorkdayTime);
                    calendarSet.setFirstDayOfWeek(Calendar.MONDAY);
                    int weekOfYearSet = calendarSet.get(Calendar.WEEK_OF_YEAR);
                    int weekYearSet = calendarSet.get(Calendar.YEAR);
                    int weekMonthSet = calendarSet.get(Calendar.MONTH) + 1;
                    int weekDaySet = calendarSet.get(Calendar.DAY_OF_MONTH);

                    int weekCountSet = calendarSet.getWeekYear();
                    int weeksInWeekYearSet = calendarSet.getWeeksInWeekYear();
                    Log.i(TAG, "getPriTimeWorkDayType set date = " + weekOfYearSet + "  weekYearSet = "
                            + weekYearSet + " - " + weekMonthSet + " - " + weekDaySet + "  weekCountSet = "
                            + weekCountSet + "  getWeeksInWeekYear = " + weeksInWeekYearSet);
                    Log.i(TAG, "getPriTimeWorkDayType weekYearCurrent = " + weekYearCurrent + "  weekYearSet = " + weekYearSet);
                    //if current year is equal to workday set year ,we can use weeks in week year of current - set
                    if (weekYearCurrent == weekYearSet) {
                        return whenCurrentIsSetYear(weekOfYearCurrent, weekOfYearSet, workdayType);
                    } else {

                        int monthOfYearLast = 12;
                        int monthOfYearFirst = 1;
                        Log.i(TAG, "getPriTimeWorkDayType weekYearCurrent = " + weekYearCurrent + "  weekYearSet = " + weekYearSet);
                        //current and set year differs 1 year , and set month of current month is december (12 Dec),and the week is first of year
                        // (for example:2019-12-30,is the first meek of 2020 )
                        if ((Math.abs(weekYearCurrent - weekYearSet) == 1) && (((weekMonthSet == monthOfYearLast) && (weekOfYearSet == monthOfYearFirst))
                                || ((weekMonthCurrent == monthOfYearLast) && (weekOfYearCurrent == monthOfYearFirst)))) {
                            return whenSetOrCurrentIsDecAndThisIsFirstWearOrYear(weekOfYearCurrent, weekOfYearSet, workdayType);
                        } else {
                            return whenOtherScene(weekYearCurrent, weekYearSet, calendarSet, calendarCurrent, workdayType);
                        }
                    }
                } else {
                    return WORKDAY_TYPE_WORKDAY;
                }
            } else {
                return workdayType;
            }
        }
        return WORKDAY_TYPE_WORKDAY;
    }

    private static int whenCurrentIsSetYear(int weekOfYearCurrent, int weekOfYearSet, int workdayType) {

        if ((weekOfYearCurrent - weekOfYearSet) % 2 == 0) {
            return workdayType;
        } else {
            Log.i(TAG, "getNeedShowWorkDayType workdayType = " + workdayType);
            if (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) {

                Log.i(TAG, "getNeedShowWorkDayType workdayType 111= " + workdayType + "   return = " + WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE);
                return WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE;
            } else {
                Log.i(TAG, "getNeedShowWorkDayType workdayType 222= " + workdayType + "   return = " + WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY);
                return WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY;
            }
        }
    }

    private static int whenSetOrCurrentIsDecAndThisIsFirstWearOrYear(int weekOfYearCurrent, int weekOfYearSet, int workdayType) {
        if ((Math.max(weekOfYearCurrent, weekOfYearSet) - Math.min(weekOfYearCurrent, weekOfYearSet)) % 2 == 0) {
            return workdayType;
        } else {
            if (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) {
                return WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE;
            } else {
                return WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY;
            }
        }
    }

    private static int whenOtherScene(int weekYearCurrent, int weekYearSet, Calendar calendarSet, Calendar calendarCurrent, int workdayType) {
        if ((calendarSet != null) && (calendarCurrent != null)) {

            int weekCountTotal = 0;
            int monthOrDateStart = 1;

            int yearMin = Math.min(weekYearCurrent, weekYearSet);
            int yearMax = Math.max(weekYearCurrent, weekYearSet);

            Log.i(TAG, "whenOtherScene : weekYearSet = " + weekYearSet + "  weekYearCurrent = " + weekYearCurrent
                    + " yearMin = " + yearMin + "  yearMax = " + yearMax);
            for (int year = yearMin; year <= yearMax; year++) {
                if ((year > yearMin) && (year < yearMax)) {

                    Calendar calendar = Calendar.getInstance(Locale.CHINA);
                    calendar.set(year, monthOrDateStart, monthOrDateStart);
                    calendar.setFirstDayOfWeek(Calendar.MONDAY);
                    weekCountTotal += calendar.getWeeksInWeekYear();

                    Log.i(TAG, "whenOtherScene  yearMin = " + yearMin + " currentYear = " + year + "  calendar.getWeeksInWeekYear() = "
                            + calendar.getWeeksInWeekYear() + "  yearMax = " + yearMax);
                } else {
                    if (year == yearMin) {

                        if (yearMin == weekYearSet) {
                            int startYearTotalCountMin = calendarSet.getWeeksInWeekYear();
                            int weekOfYearMin = calendarSet.get(Calendar.WEEK_OF_YEAR);
                            Log.i(TAG, "whenOtherScene calendarSet startYearTotalCountMin =" + startYearTotalCountMin + "  weekOfYearMin = " + weekOfYearMin);
                            weekCountTotal += (startYearTotalCountMin - weekOfYearMin) + 1;
                        } else {
                            int startYearTotalCountCurrent = calendarCurrent.getWeeksInWeekYear();
                            int weeksInWeekYearStart = calendarCurrent.get(Calendar.WEEK_OF_YEAR);
                            Log.i(TAG, "whenOtherScene startYearTotalCountCurrent =" + startYearTotalCountCurrent + "   weeksInWeekYearStart = "
                                    + weeksInWeekYearStart);
                            weekCountTotal += (startYearTotalCountCurrent - weeksInWeekYearStart) + 1;
                        }
                    } else {
                        if (yearMin == weekYearSet) {
                            Log.i(TAG, "whenOtherScene yearMin == weekYearSet calendarCurrent = " + calendarCurrent.get(Calendar.WEEK_OF_YEAR));
                            weekCountTotal += calendarCurrent.get(Calendar.WEEK_OF_YEAR);
                        } else {
                            Log.i(TAG, "whenOtherScene yearMin != weekYearSet calendarSet = " + calendarSet.get(Calendar.WEEK_OF_YEAR));
                            weekCountTotal += calendarSet.get(Calendar.WEEK_OF_YEAR);
                        }
                    }
                }
            }
            Log.i(TAG, "whenOtherScene weekCountTotal == " + weekCountTotal);

            if (weekCountTotal % 2 == 0) {
                return workdayType;
            } else {
                if (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) {
                    return WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE;
                } else {
                    return WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY;
                }
            }
        } else {
            return WORKDAY_TYPE_WORKDAY;
        }
    }


    public static int getSetWorkDayType(Context context) {
        return PrefUtils.getInt(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, ALARM_CLOCK_WORKDAY_TYPE_KEY, WORKDAY_TYPE_WORKDAY);
    }

    public static long getSetWorkDayTypeTime(Context context) {
        if (context != null) {
            return PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    ALARM_CLOCK_WORKDAY_TYPE_SET_TIME_KEY, DEFAULT_SET_WORKDAY_TYPE_TIME);
        }
        return DEFAULT_SET_WORKDAY_TYPE_TIME;
    }

    public static void setWorkDayType(Context context, int workdayType, long workdayTime) {
        if (workdayTime <= 0) {
            Log.i(TAG, "setWorkDayType workdayTime = " + workdayTime);
            return;
        }
        if (context == null) {
            context = AlarmClockApplication.getInstance();
        }

        PrefUtils.putInt(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, ALARM_CLOCK_WORKDAY_TYPE_KEY, workdayType);
        PrefUtils.putLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, ALARM_CLOCK_WORKDAY_TYPE_SET_TIME_KEY, workdayTime);
    }

    static String getWorkdayTypeLabelString(int workdayType) {
        Context context = AlarmClockApplication.getInstance();
        if (workdayType == WORKDAY_TYPE_WORKDAY) {
            return context.getString(R.string.oplus_workday_switch);
        } else if (workdayType == WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY) {
            return context.getString(R.string.single_dayoff_on_sunday);
        } else if (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) {
            return context.getString(R.string.work_six_days_this_week);
        } else if (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE) {
            return context.getString(R.string.work_five_days_this_week);
        }
        return context.getString(R.string.oplus_workday_switch);
    }

    public static String getWorkdayTypeLabelForAddClock() {
        Context context = AlarmClockApplication.getInstance();
        int workdayType = getNeedShowWorkDayType(context, null);

        if (workdayType == WORKDAY_TYPE_WORKDAY) {
            return context.getString(R.string.legal_work_day_ring_except_holiday);
        } else if (workdayType == WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY) {
            return context.getString(R.string.single_dayoff_on_sunday_ring_except_holiday);
        } else if ((workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY) || (workdayType == WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE)) {
            return context.getString(R.string.one_or_two_days_off_a_week_ring_except_holiday);
        }
        return context.getString(R.string.legal_work_day_ring_except_holiday);
    }
}
