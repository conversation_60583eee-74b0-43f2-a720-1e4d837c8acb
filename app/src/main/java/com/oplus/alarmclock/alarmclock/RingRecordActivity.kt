/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - RingRecordActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.adapter.RingRecordRVAdapter
import com.oplus.alarmclock.databinding.ActivityRingRecordBinding
import com.oplus.alarmclock.mvvm.base.BaseVMActivity
import com.oplus.alarmclock.mvvm.ringrecord.RingRecordViewModel
import com.oplus.alarmclock.provider.alarmring.AlarmRing
import com.oplus.alarmclock.utils.BackgroundUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.RingRecordPanel

class RingRecordActivity : BaseVMActivity<ActivityRingRecordBinding, RingRecordViewModel>(),
        RingRecordPanel.DataListener {
    companion object {
        private const val TAG = "RingRecordActivity"
        private const val SEARCH_TIME = "search_time"
        private const val TRANSLUCENT = 0.5F
        private const val TRANSPARENT = 1F
    }

    private val mRvAdapter by lazy { RingRecordRVAdapter() }
    override fun initView() {
        mViewBinding.run {
            setSupportActionBar(toolbar)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            list.run {
                layoutManager = LinearLayoutManager(this@RingRecordActivity)
                adapter = mRvAdapter
            }
            appBar.setPadding(0, Utils.getStatusBarHeight(this@RingRecordActivity), 0, 0)

            val uiMode = obtainUiMode()
            if (FoldScreenUtils.UiMode.LARGE_HORIZONTAL == uiMode) { /*平板横屏*/
                val padding = resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding)
                list.setPadding(padding, 0, padding, 0)
            } else if (FoldScreenUtils.UiMode.LARGE_VERTICAL == uiMode) { /*平板竖屏*/
                val padding = resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding)
                list.setPadding(padding, 0, padding, 0)
            } else {
                list.setPadding(0, appBar.measuredHeight, 0, 0)
            }
            Utils.setupAnimToolbarAndBlurView(this@RingRecordActivity, appBar, list)
        }
    }

    override fun initData() {
        mViewModel.mData.observe(this) {
            setData(it)
        }
        val searchTime = intent.getLongExtra(SEARCH_TIME, 0)
        if (searchTime == 0L) {
            mViewModel.loadData()
        } else {
            mViewModel.loadData(searchTime)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
            }
            R.id.jump_to_date -> {
                val searchTime = intent.getLongExtra(SEARCH_TIME, 0)
                RingRecordPanel(this, searchTime).show(this)
            }
        }
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.activity_ring_record_menu, menu)
        return true
    }

    override fun onResult(time: Long) {
        intent.putExtra(SEARCH_TIME, time)
        mViewBinding.loadingRl.visibility = View.VISIBLE
        mViewModel.loadData(time)
    }

    override fun onOsloLandOrientation() {
        super.onOsloLandOrientation()
        mViewBinding.run {
            list.setPadding(
                resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                appBar.measuredHeight,
                resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                0
            )
        }
    }

    override fun onOsloPortOrientation() {
        super.onOsloPortOrientation()
        mViewBinding.run {
            list.setPadding(
                resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                appBar.measuredHeight,
                resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                0
            )
        }
    }

    override fun onOsloOtherOrientation() {
        super.onOsloOtherOrientation()
        mViewBinding.run {
            list.setPadding(0, appBar.measuredHeight, 0, 0)
        }
    }

    private fun setData(data: MutableList<AlarmRing>) {
        mViewBinding.run {
            loadingRl.visibility = View.GONE
            if (data.isEmpty()) {
                playEmptyAnimOrShowEmptyIcon()
            } else {
                noDataCl.visibility = View.GONE
                list.visibility = View.VISIBLE
                mRvAdapter.mData = data
            }
        }
    }

    private fun playEmptyAnimOrShowEmptyIcon() {
        mViewBinding.run {
            if (noDataCl.visibility != View.VISIBLE) {
                noDataCl.visibility = View.VISIBLE
                list.visibility = View.GONE
                viewEmpty.run {
                    val isDark = COUIDarkModeUtil.isNightMode(this@RingRecordActivity)
                    /**响铃记录处透明度修改方案同无闹钟界面*/
                    alpha = TRANSPARENT
                    BackgroundUtils.setEmptyBackground(isDark, this)
                }
            }
        }
    }
}