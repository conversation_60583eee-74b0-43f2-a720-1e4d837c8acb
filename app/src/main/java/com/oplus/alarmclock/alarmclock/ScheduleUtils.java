/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.10.10   liukun build
 *******************************************************/
package com.oplus.alarmclock.alarmclock;

import static com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper.SUPPORT_ALARM_SNOOZE_FLUID;

import android.annotation.SuppressLint;
import android.app.NotificationManager;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeServiceUtils;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.provider.ClockContract.Schedule;
import com.oplus.clock.common.utils.VersionUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.FutureTask;


/**
 * Created by 80033968 on 2018/7/19.
 */

public class ScheduleUtils {

    /**
     * Offset from alarm time to stop ringing.
     */
    public static final int MISSED_TIME_TO_LIVE_MINUTE_OFFSET = 14;

    /**
     * Default timeout for alarms in minutes.
     */
    public static final int DEFAULT_ALARM_TIMEOUT_SETTING = 15;

    private static final long ALARM_TIMEOUT_MILLS = DEFAULT_ALARM_TIMEOUT_SETTING * 60 * 1000;

    private static final int DEFAULT_TIME_ONE_SECOND = 1000;
    private static final int DEFAULT_TIME_ONE_MINUTE = 60;

    private static final String TAG = "ScheduleUtils";

    public static boolean deleteSchedule(Context context, long scheduleId) {
        Log.i(TAG, "delete scheduleId = " + scheduleId);
        boolean success = true;
        try {
            Uri uri = ContentUris.withAppendedId(Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
            success = context.getContentResolver().delete(uri, null, null) == 1;
        } catch (Exception e) {
            Log.i(TAG, "deleteSchedule Exception: " + e.getMessage());
            success = false;
        }
        return success;
    }

    private static boolean deleteSchedulesOfAlarm(Context context, long alarmId) {
        boolean success = true;
        try {
            context.getContentResolver().delete(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                    Schedule.ALARM_ID + "=" + alarmId, null);
        } catch (Exception e) {
            Log.e(TAG, "deleteSchedulesOfAlarm Exception: " + e.getMessage());
            success = false;
        }
        Log.i(TAG, "deleteSchedulesOfAlarm: alarmId: " + alarmId + ", result: " + success);
        return success;
    }

    public static boolean deleteSchedulesOfAlarm(Context context, long alarmId, long excludeScheduleId) {
        boolean success = true;
        try {
            context.getContentResolver().delete(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                    Schedule.ALARM_ID + "=" + alarmId + " AND "
                            + Schedule._ID + "!=" + excludeScheduleId + "", null);
        } catch (Exception e) {
            Log.e(TAG, "deleteSchedulesOfAlarm Exception: " + e.getMessage());
            success = false;
        }
        Log.i(TAG, "deleteSchedulesOfAlarm alarmId = " + alarmId + ", exclude Schedule: "
                + excludeScheduleId + ", result: " + success);
        return success;
    }

    public static void cancelNotification(Context context, long scheduleId) {
        final NotificationManager nm = (NotificationManager) context
                .getSystemService(Context.NOTIFICATION_SERVICE);
        if (nm != null) {
            Log.i(TAG, "cancelNotification scheduleId:" + scheduleId);
            nm.cancel((int) (scheduleId + ClockConstant.NOTIFICATION_ELMINATED));
        }
        if (VersionUtils.isOsVersion15() && TimerSeedlingHelper.isSupportFluidCloud() && SUPPORT_ALARM_SNOOZE_FLUID) {
            //取消流体云卡
            AlarmSnoozeServiceUtils.stopAlarmSnoozeService(context, scheduleId);
        }
    }

    public static void cancelNotification2(Context context, long scheduleId, Boolean closeFluid) {
        final NotificationManager nm = (NotificationManager) context
                .getSystemService(Context.NOTIFICATION_SERVICE);
        if (nm != null) {
            Log.i(TAG, "cancelNotification2 scheduleId:" + scheduleId
                    + ",sCurrentNoticesScheduleId:" + AlarmStateManager.sCurrentNoticesScheduleId);
            nm.cancel((int) (scheduleId + ClockConstant.NOTIFICATION_ELMINATED));
            nm.cancel((int) scheduleId);
            //如果当前闹钟又提前响铃通知，也取消掉
            if (AlarmStateManager.sCurrentNoticesScheduleId == scheduleId) {
                nm.cancel(AlarmStateManager.AHEAD_REMIND_NOTIFICATION_ID);
            }
        }
        if (TimerSeedlingHelper.isSupportFluidCloud() && SUPPORT_ALARM_SNOOZE_FLUID && closeFluid) {
            //取消流体云卡
            AlarmSnoozeServiceUtils.stopAlarmSnoozeService(context, scheduleId);
        }
    }

    private static ContentValues createContentValues(long alarmId, AlarmSchedule alarmShcedule) {
        final ContentValues values = new ContentValues();

        values.put(Schedule.YEAR, alarmShcedule.getYear());
        values.put(Schedule.MONTH, alarmShcedule.getMonth());
        values.put(Schedule.DAY, alarmShcedule.getDay());
        values.put(Schedule.HOUR, alarmShcedule.getHour());
        values.put(Schedule.MINUTES, alarmShcedule.getMinute());
        values.put(Schedule.ALARM_TIME, alarmShcedule.getTime());
        values.put(Schedule.SNOOZETIME, alarmShcedule.getSnoonzeTime());
        values.put(Schedule.ALARM_ID, alarmId);
        values.put(Schedule.ALARM_STATE, alarmShcedule.getAlarmState());
        return values;
    }


    public static long getAlarmTimeInMills(AlarmSchedule schedule) {
        return getAlarmTimeInMills(schedule, true);
    }

    public static long getAlarmTimeInMills(AlarmSchedule schedule, Boolean clearSecond) {
        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, schedule.getYear());
        calendar.set(Calendar.MONTH, schedule.getMonth());
        calendar.set(Calendar.DAY_OF_MONTH, schedule.getDay());
        calendar.set(Calendar.HOUR_OF_DAY, schedule.getHour());
        calendar.set(Calendar.MINUTE, schedule.getMinute());
        if (clearSecond) {
            //稍后提醒时间和秒抢闹钟时间不需要清除秒
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
        }
        return calendar.getTimeInMillis();
    }

    public static long getMissedTimeToLiveInMills(long alarmTime) {
        long mills = alarmTime + MISSED_TIME_TO_LIVE_MINUTE_OFFSET * DEFAULT_TIME_ONE_MINUTE * DEFAULT_TIME_ONE_SECOND;
        Log.i("getMissedTimeToLive", "mills = " + mills);
        return mills;
    }


    public static long getTimeoutInMills(long alarmMills, long mAlarmDuration) {
        Log.i("getTimeoutInMills", "alarmMills = " + alarmMills + "  mAlarmDuration : " + mAlarmDuration);
        return alarmMills + mAlarmDuration * DEFAULT_TIME_ONE_MINUTE * DEFAULT_TIME_ONE_SECOND;
    }

    public static AlarmSchedule getSchedule(Context context, long scheduleId) {
        final String selection = Schedule._ID + "=" + scheduleId;
        List<AlarmSchedule> list = getSchedules(context, selection, null, null);
        if ((list != null) && (!list.isEmpty())) {
            return list.get(0);
        }
        return null;
    }

    public static List<AlarmSchedule> getSchedulesOfAlarm(Context context, long alarmId) {
        final String selection = Schedule.ALARM_ID + "=" + alarmId;
        return getSchedules(context, selection, null, null);
    }

    public static List<AlarmSchedule> getSchedulesOfAlarm(Context context, Alarm alarm) {
        final String selection = Schedule.ALARM_ID + "=" + alarm.getId();
        return getSchedulesWithAlarm(context, selection, null, null, alarm);
    }

    public static List<AlarmSchedule> getSchedulesOfAlarmNoExecutor(Context context, Alarm alarm) {
        final String selection = Schedule.ALARM_ID + "=" + alarm.getId();
        return getSchedulesWithAlarmNoExecutor(context, selection, null, null, alarm);
    }

    public static List<AlarmSchedule> getAllSchedules(Context context) {
        return getSchedules(context, null, null, null);
    }

    public static List<AlarmSchedule> getAllSnoozeSchedules(Context context) {
        long now = Calendar.getInstance().getTimeInMillis();
        String selection = Schedule.ALARM_TIME + ">" + now + " AND " + Schedule.ALARM_STATE
                + "=" + Schedule.SNOOZE_STATE;
        String orderBy = Schedule.ALARM_TIME + " ASC";
        return getSchedules(context, selection, null, orderBy);
    }

    public static List<AlarmSchedule> getAllSchedulesNoExecutor(Context context) {
        return getSchedulesNoExecutor(context, null, null, null);
    }

    //selection next schedule where state = 0 and the schedule time is minimum,
    public static List<AlarmSchedule> getNextSchedules(Context context, long alarmId) {
        if (isCanGetNextSchedule(context)) {
            Log.i(TAG, "getNextSchedules isCanGetNextSchedule : true");
            String selection = Schedule.ALARM_STATE + "=" + Schedule.SILENT_STATE;
            if (alarmId >= 0) {
                selection = selection + " AND " + Schedule.ALARM_ID + "!=" + alarmId;
            }

            return getSchedules(context, selection, null, Schedule.ALARM_TIME + " asc LIMIT 0,1");
        } else {
            Log.i(TAG, "getNextSchedules isCanGetNextSchedule : false");
            return null;
        }
    }

    public static List<AlarmSchedule> getNextSchedules(Context context) {
        if (isCanGetNextSchedule(context)) {
            Log.i(TAG, "getNextSchedules isCanGetNextSchedule : true");
            String selection = Schedule.ALARM_STATE + "=" + Schedule.SILENT_STATE;
            return getSchedules(context, selection, null, Schedule.ALARM_TIME + " asc LIMIT 0,1");
        } else {
            Log.i(TAG, "getNextSchedules isCanGetNextSchedule : false");
            return null;
        }
    }


    //if have alarm state is not SILENT_STATE ,can not to get next  schedule
    private static boolean isCanGetNextSchedule(Context context) {

        String selection = Schedule.ALARM_STATE + ">" + Schedule.SILENT_STATE;

        Log.i(TAG, "isCanGetNextSchedule = " + selection);

        return isCanGetNextSchedule(context, selection, null, Schedule.ALARM_STATE + " desc LIMIT 0,1");
    }

    private static boolean isCanGetNextSchedule(Context context, String selection,
                                                String[] selectionArgs, String orderBy) {
        Cursor cursor = null;
        boolean result = true;
        try {
            cursor = context.getContentResolver().query(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                    AlarmSchedule.SCHEDULE_PROJECTION, selection, selectionArgs, orderBy);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                Log.i(TAG, "is there have alarm in ring or fir");
                result = false;
            } else {
                Log.i(TAG, "can get next alarm");
            }
        } catch (Exception e) {
            Log.e(TAG, "isCanGetNextSchedule Exception: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }


    public static List<AlarmSchedule> getSchedules(Context context, String selection,
                                                    String[] selectionArgs, String orderBy) {
        return getSchedulesWithAlarm(context, selection, selectionArgs, orderBy, null);
    }

    private static List<AlarmSchedule> getSchedulesNoExecutor(Context context, String selection,
                                                              String[] selectionArgs, String orderBy) {
        return getSchedulesWithAlarmNoExecutor(context, selection, selectionArgs, orderBy, null);
    }

    private static List<AlarmSchedule> getSchedulesWithAlarm(Context context, String selection,
                                                             String[] selectionArgs, String orderBy, Alarm alarm) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        FutureTask<List<AlarmSchedule>> task = new FutureTask<>(() -> {
            List<AlarmSchedule> result = null;
            Cursor cursor = null;
            try {
                cursor = context.getContentResolver().query(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                        AlarmSchedule.SCHEDULE_PROJECTION, selection, selectionArgs, orderBy);
                if ((cursor != null) && (cursor.getCount() > 0)) {
                    result = new ArrayList<>();
                    while (cursor.moveToNext()) {
                        if (alarm == null) {
                            result.add(AlarmSchedule.fromCursor(cursor));
                        } else {
                            result.add(AlarmSchedule.fromCursorAndAlarm(cursor, alarm));
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "getSchedules Exception: " + e.getMessage());
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return result;
        });
        executorService.submit(task);
        try {
            return task.get();
        } catch (ExecutionException | InterruptedException e) {
            Log.e(TAG, "task Exception: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    private static List<AlarmSchedule> getSchedulesWithAlarmNoExecutor(Context context, String selection,
                                                                       String[] selectionArgs, String orderBy, Alarm alarm) {
        List<AlarmSchedule> result = null;
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                    AlarmSchedule.SCHEDULE_PROJECTION, selection, selectionArgs, orderBy);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                result = new ArrayList<>();
                while (cursor.moveToNext()) {
                    if (alarm == null) {
                        result.add(AlarmSchedule.fromCursor(cursor));
                    } else {
                        result.add(AlarmSchedule.fromCursorAndAlarm(cursor, alarm));
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getSchedules Exception: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return result;
    }

    /**
     * Returns an alarm schedule of an alarm that's going to fire next.
     *
     * @param context application context
     * @return an alarm instance that will fire earliest relative to current time.
     */
    public static AlarmSchedule getNextFiringAlarm(Context context) {

        final String activeAlarmQuery = Schedule.ALARM_STATE + "<" + Schedule.FIRED_STATE;
        final String sortOrder = Schedule.ALARM_TIME + " ASC LIMIT 1";

        final List<AlarmSchedule> alarmInstances = getSchedules(context, activeAlarmQuery, null, sortOrder);

        AlarmSchedule nextAlarm = null;
        if ((alarmInstances != null) && !alarmInstances.isEmpty()) {
            nextAlarm = alarmInstances.get(0);
        }

        return nextAlarm;
    }


    public static void clearAllSchedulesOfAlarm(Context context, long alarmId) {
        final List<AlarmSchedule> schedules = getSchedulesOfAlarm(context, alarmId);
        Alarm alarm = AlarmUtils.getAlarm(context, alarmId);
        //取消秒抢闹钟
        if ((alarm != null) && (alarm.getmGarbSwitch() == 1)) {
            GarbAlarmUtils.cancelGarbAlarmState(context, alarm);
        }
        if ((schedules != null) && (!schedules.isEmpty())) {
            deleteSchedulesOfAlarm(context, alarmId);
            for (final AlarmSchedule schedule : schedules) {
                AlarmStateManager.unregisterInstance(context, schedule);
            }
        }
    }

    public static AlarmSchedule getEarlySchedule(Context context, long alarmScheduleTime) {
        Cursor cursor = null;
        AlarmSchedule schedule = null;
        try {
            long now = Calendar.getInstance().getTimeInMillis();
            String selection = Schedule.ALARM_TIME + ">" + now + " AND " + Schedule.ALARM_STATE
                    + "<" + Schedule.FIRED_STATE;
            if (alarmScheduleTime > 0) {
                selection = selection + " AND " + Schedule.ALARM_TIME + "<" + alarmScheduleTime;
            }
            cursor = context.getContentResolver().query(Schedule.ALARM_SCHEDULE_CONTENT_URI,
                    AlarmSchedule.SCHEDULE_PROJECTION, selection, null, Schedule.ALARM_TIME + " ASC");
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    schedule = AlarmSchedule.fromCursor(cursor);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getEarlySchedule Exception: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return schedule;
    }


    public static Uri add(Context context, long alarmId, AlarmSchedule schedule) {
        String where = Schedule.ALARM_ID + "=?";
        String[] selectionArgs = {alarmId + ""};
        context.getContentResolver().delete(Schedule.ALARM_SCHEDULE_CONTENT_URI, where, selectionArgs);
        final ContentValues values = ScheduleUtils.createContentValues(alarmId, schedule);
        final Uri uri = context.getContentResolver().insert(Schedule.ALARM_SCHEDULE_CONTENT_URI, values);
        if (uri != null) {
            scheduleResolverNotifyChange(context.getContentResolver(), uri);
        }
        Log.d(TAG, "add AlarmSchedule: Uri: " + uri);
        return uri;
    }

    /**
     * 修改操作统一调用 notifyChange() 手动通知 ContentObserver
     * <p>
     * notifyChange()的 flags 参数固定为 ContentResolver.NOTIFY_NO_DELAY
     * 由此跳过时钟应用在后后台时系统ContentService的dispatch()方法延迟10秒发送通知的延迟
     *
     * @param contentResolver
     * @param uri
     */
    @SuppressLint("WrongConstant")
    public static void scheduleResolverNotifyChange(ContentResolver contentResolver, Uri uri) {
        contentResolver.notifyChange(uri, null, AlarmUtils.NOTIFY_NO_DELAY);
    }


}
