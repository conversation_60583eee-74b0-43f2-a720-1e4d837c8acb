/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseVBActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <PERSON><PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/3/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.poplist.COUIClickSelectMenu
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.LoopAlarmUtils
import com.oplus.clock.common.utils.Log
import com.oplus.alarmclock.utils.WindowUtil.getWindowSize
import java.text.NumberFormat

/**
 * 稍后提醒设置页面
 * <AUTHOR>
 * @date 2022年3月30日09:35:01
 */
class SnoozePreferenceFragment : COUIPanelFragment() {

    companion object {
        private const val TAG = "SnoozePreferenceFragment"
        fun newInstance(alarm: Alarm): SnoozePreferenceFragment {
            val bundle = Bundle().apply {
                putParcelable("alarm", alarm)
            }
            return SnoozePreferenceFragment().apply {
                arguments = bundle
            }
        }
    }

    private lateinit var mAlarm: Alarm

    /**
     * 当前点击的稍后提醒列表下标
     */
    private var mSnoozePosition = 0

    /**
     * 当前点击的响铃次数下标
     */
    private var mNumPosition = 0

    /**
     * 根布局
     */
    private var mRootView: View? = null

    /**
     * 稍后提醒时间
     */
    private var mSnoozeTime: TextView? = null

    /**
     * 响铃次数
     */
    private var mRingNumText: TextView? = null

    /**
     * 标题
     */
    private var mSnoozeTitle: TextView? = null
    private var mRingNumTitle: TextView? = null

    private var mRootLayout: LinearLayout? = null

    /**
     * 稍后提醒弹窗layout
     */
    private var mSnoozePopupLayout: LinearLayout? = null

    /**
     * 响铃次数弹窗
     */
    private var mAlarmNumLayout: LinearLayout? = null

    /**
     * 稍后提醒开关
     */
    private var mSnoozeSwitch: COUISwitch? = null

    /**
     * 稍后提醒弹窗
     */
    private var mSnoozePopWindow: COUIClickSelectMenu? = null

    /**
     * 响铃次数弹窗
     */
    private var mNumPopWindow: COUIClickSelectMenu? = null

    /**
     * 稍后提醒列表
     */
    private var mAlarmSnoozeList: ArrayList<PopupListItem> = ArrayList()

    /**
     * 响铃次数列表
     */
    private var mAlarmNumList: ArrayList<PopupListItem> = ArrayList()

    /**
     * 点击位置
     */
    private val mLastTouchDownXY = arrayOfNulls<Float>(2)

    private lateinit var sAlarmSnoozeStr: Array<String>

    /**
     * 数字百分比国际化
     */
    private val mNumberFormat = NumberFormat.getIntegerInstance()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mRootView = inflater.inflate(R.layout.add_alarm_snooze_preference, container, false)
        mAlarm = arguments?.getParcelable<Alarm>("alarm")!!
        initAlarmLayoutData()
        initViews()
        return mRootView
    }


    /**
     * 初始化view
     */
    private fun initViews() {
        mRootView?.apply {
            mRootLayout = findViewById<LinearLayout>(R.id.root_layout)
            val size = <EMAIL>?.let { getWindowSize(it) }
            mRootLayout?.apply {
                size?.let {
                    layoutParams.height = it.height
                }
            }

            //弹出选择内容
            mSnoozePopupLayout = findViewById<LinearLayout>(R.id.snooze_popup_layout)?.apply {
                COUICardListHelper.setItemCardBackground(this,
                    COUICardListHelper.getPositionInGroup(2, 0))
            }

            //弹出次数
            mAlarmNumLayout = findViewById<LinearLayout>(R.id.num_popup_layout)?.apply {
                COUICardListHelper.setItemCardBackground(this,
                    COUICardListHelper.getPositionInGroup(2, 1))
            }

            //稍后提醒
            mSnoozeTime = findViewById<TextView>(R.id.snooze_time)?.apply {
                text = if (mAlarm.snoonzeItem == ClockConstant.SNOOZE_SWITCH_OFF) {
                    sAlarmSnoozeStr[0]
                } else {
                    sAlarmSnoozeStr[mSnoozePosition]
                }
            }

            //响铃次数
            mRingNumText = findViewById<TextView?>(R.id.alarm_num_text)?.apply {
                text = getString(
                    R.string.alert_time_num,
                    mNumberFormat.format(AlarmRepeat.REPEAT_ALERT_NUM[mNumPosition])
                )
            }

            mSnoozeTitle = findViewById<TextView>(R.id.snooze_time_title)
            mRingNumTitle = findViewById<TextView>(R.id.num_time_title)
            //设置switch
            mSnoozeSwitch = findViewById<COUISwitch>(R.id.snooze_switch)?.apply {
                setOnCheckedChangeListener { _, check ->
                    selectSnooze(check)
                }
                isChecked = mAlarm.snoonzeItem == ClockConstant.SNOOZE_SWITCH_ON_5_MIN
            }

            //稍后提醒
            mSnoozePopWindow = COUIClickSelectMenu(activity)
            mSnoozePopWindow?.apply {
                popup.setDismissTouchOutside(true)
                popup.setUseBackgroundBlur(true)
                mSnoozePopupLayout?.let { registerForClickSelectItems(it, mAlarmSnoozeList) }
                setOnItemClickListener { _: AdapterView<*>?, _: View?, position: Int, id: Long ->
                    onSnoozeItemClick(
                        position
                    )
                }
                setHelperEnabled(mAlarm.snoonzeItem == ClockConstant.SNOOZE_SWITCH_ON_5_MIN)
            }

            //响铃次数
            mNumPopWindow = COUIClickSelectMenu(activity)
            mNumPopWindow?.apply {
                popup.setDismissTouchOutside(true)
                popup.setUseBackgroundBlur(true)
                mAlarmNumLayout?.let { registerForClickSelectItems(it, mAlarmNumList) }
                setOnItemClickListener { _: AdapterView<*>?, _: View?, position: Int, id: Long ->
                    onRingNumClick(
                        position
                    )
                }
                setHelperEnabled(mAlarm.snoonzeItem == ClockConstant.SNOOZE_SWITCH_ON_5_MIN)
            }

            findViewById<LinearLayout>(R.id.ll_snooze_switch)?.setOnClickListener {
                mSnoozeSwitch?.apply { isChecked = !isChecked }
            }

            //公共点击效果适配
            findViewById<LinearLayout>(R.id.ll_snooze_switch)?.let {
                COUICardListHelper.setItemCardBackground(it,
                    COUICardListHelper.getPositionInGroup(1, 0))
            }
        }
    }

    /**
     * 是否可以关闭面板
     */
    fun canClosePanel(): Boolean {
        val location = Rect()
        mRootLayout?.getGlobalVisibleRect(location)
        if (location.top > LoopAlarmUtils.PANEL_TOUCH_DOWN_DIS) {
            return false
        }
        return true
    }

    /**
     * 设置状态
     */
    private fun selectSnooze(checked: Boolean) {
        Log.i(TAG, "onPreferenceChange ringSnooze checked = $checked")
        if (checked) {
            mAlarm.apply {
                snoonzeItem = ClockConstant.SNOOZE_SWITCH_ON_5_MIN
                setmSnoozeTime(AlarmRepeat.REPEAT_ALERT_INTERVAL[mSnoozePosition])
                ringNum = AlarmRepeat.REPEAT_ALERT_NUM[mNumPosition]
            }
            mSnoozeTitle?.apply {
                setTextColor(ContextCompat.getColor(context, R.color.coui_preference_title_color))
            }
            mRingNumTitle?.apply {
                setTextColor(ContextCompat.getColor(context, R.color.coui_preference_title_color))
            }
            mSnoozePopupLayout?.isClickable = true
            mAlarmNumLayout?.isClickable = true
            mSnoozePopWindow?.setHelperEnabled(true)
            mNumPopWindow?.setHelperEnabled(true)
            setSnoozeTime(mSnoozePosition)
            setRingNumTime()
        } else {
            mAlarm.apply {
                snoonzeItem = ClockConstant.SNOOZE_SWITCH_OFF
            }
            mSnoozeTime?.apply {
                text = sAlarmSnoozeStr[mSnoozePosition]
                setTextColor(ContextCompat.getColor(context, R.color.alarm_list_bg_division))
            }
            mRingNumText?.apply {
                text = getRingNumText()
                setTextColor(ContextCompat.getColor(context, R.color.alarm_list_bg_division))
            }
            mSnoozeTitle?.apply {
                setTextColor(ContextCompat.getColor(context, R.color.alarm_list_bg_division))
            }
            mRingNumTitle?.apply {
                setTextColor(ContextCompat.getColor(context, R.color.alarm_list_bg_division))
            }
            mSnoozePopupLayout?.isClickable = false
            mAlarmNumLayout?.isClickable = false
            mSnoozePopWindow?.setHelperEnabled(false)
            mNumPopWindow?.setHelperEnabled(false)
        }
    }

    /**
     * 获取次数文本
     */
    private fun getRingNumText(): String {
        return getString(
            R.string.alert_time_num,
            mNumberFormat.format(AlarmRepeat.REPEAT_ALERT_NUM[mNumPosition])
        )
    }

    /**
     * 设置稍后提醒文本
     *
     * @param position
     */
    private fun setSnoozeTime(position: Int) {
        mSnoozeTime?.apply {
            text = sAlarmSnoozeStr[position]
            setTextColor(ContextCompat.getColor(context, R.color.timer_fluid_cloud_des_color))
        }
    }

    private fun setRingNumTime() {
        mRingNumText?.apply {
            text = getString(
                R.string.alert_time_num,
                mNumberFormat.format(AlarmRepeat.REPEAT_ALERT_NUM[mNumPosition])
            )
            setTextColor(ContextCompat.getColor(context, R.color.timer_fluid_cloud_des_color))
        }
    }

    /**
     * 获取稍后提醒时间的下标
     *
     * @param snoozeTime
     * @return
     */
    private fun getSnoozeListIndex(snoozeTime: Int): Int {
        for (i in AlarmRepeat.REPEAT_ALERT_INTERVAL.indices) {
            if (snoozeTime == AlarmRepeat.REPEAT_ALERT_INTERVAL[i]) {
                return i
            }
        }
        return 0
    }

    /**
     * 获取响铃次数的下标
     */
    private fun getNumListIndex(num: Int): Int {
        for (i in AlarmRepeat.REPEAT_ALERT_NUM.indices) {
            if (num == AlarmRepeat.REPEAT_ALERT_NUM[i]) {
                return i
            }
        }
        return 0
    }

    /**
     * 稍后提醒弹窗点击
     *
     * @param position
     */
    private fun onSnoozeItemClick(position: Int) {
        val item = mAlarmSnoozeList[position]
        if (mSnoozePosition != position || !item.isChecked) {
            item.isChecked = !item.isChecked
            val preItem = mAlarmSnoozeList[mSnoozePosition]
            preItem.isChecked = false
            mSnoozePosition = position
        }
        mSnoozePosition = position
        //打开开关
        mSnoozeSwitch?.apply {
            if (isChecked) {
                setChecked(true)
            }
        }
        //隐藏popup
        mSnoozePopWindow?.apply {
            if (popup.isShowing) {
                dismiss()
            }
        }
        setSnoozeTime(position)
        mAlarm.setmSnoozeTime(AlarmRepeat.REPEAT_ALERT_INTERVAL[position])
    }

    /**
     * 响铃次数点击
     */
    private fun onRingNumClick(position: Int) {
        val item = mAlarmNumList[position]
        if (mNumPosition != position || !item.isChecked) {
            item.isChecked = !item.isChecked
            val preItem = mAlarmNumList[mNumPosition]
            preItem.isChecked = false
            mNumPosition = position
        }
        mNumPosition = position
        //打开开关
        mSnoozeSwitch?.apply {
            if (isChecked) {
                setChecked(true)
            }
        }
        //隐藏popup
        mNumPopWindow?.apply {
            if (popup.isShowing) {
                dismiss()
            }
        }
        setRingNumTime()
        mAlarm.ringNum = AlarmRepeat.REPEAT_ALERT_NUM[position]
    }

    /**
     * 初始化闹铃布局数据
     */
    private fun initAlarmLayoutData() {
        mSnoozePosition = getSnoozeListIndex(mAlarm.getmSnoozeTime())
        mNumPosition = getNumListIndex(mAlarm.ringNum)
        sAlarmSnoozeStr = arrayOf(
            getString(R.string.five_minutes),
            getString(R.string.ten_minutes),
            getString(R.string.fifteen_minutes),
            getString(R.string.twenty_minutes),
            getString(R.string.twenty_five_minutes),
            getString(R.string.thrity_minutes)
        )

        for (i in sAlarmSnoozeStr.indices) {
            val builder = PopupListItem.Builder().apply {
                setTitle(sAlarmSnoozeStr[i])
                setIsEnable(true)
            }
            if (i == mSnoozePosition) {
                builder.setIsChecked(true)
                mAlarmSnoozeList.add(builder.build())
            } else {
                mAlarmSnoozeList.add(builder.build())
            }
        }

        for (i in AlarmRepeat.REPEAT_ALERT_NUM.indices) {
            val str = getString(
                R.string.alert_time_num,
                mNumberFormat.format(AlarmRepeat.REPEAT_ALERT_NUM[i])
            )
            val builder = PopupListItem.Builder().apply {
                setTitle(str)
                setIsEnable(true)
            }
            if (i == mNumPosition) {
                builder.setIsChecked(true)
                mAlarmNumList.add(builder.build())
            } else {
                mAlarmNumList.add(builder.build())
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mSnoozePopWindow?.popup?.dismiss()
        mNumPopWindow?.dismiss()
    }
}