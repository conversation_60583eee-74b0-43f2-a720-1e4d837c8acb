/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AddWorkAlarmManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin    203/5/8     1.0            build this module
 ****************************************************************/
@file:Suppress(
    "LongParameterList",
    "Maximum<PERSON>ineLength",
    "<PERSON><PERSON><PERSON>ber",
    "ComplexCondition",
    "LargeClass",
    "ParameterListWrapping",
    "CollapsibleIfStatements"
)

package com.oplus.alarmclock.alarmclock

import android.content.Context
import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.coui.appcompat.dialog.widget.COUIAlertDialogMessageView
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.adapter.LoopAlarmGridAdapter
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmDialogUtils
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_LIST_SPAN_COUNT
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_LIST_TWO_LINES_COUNT
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_POSITION_WITH_OFFSET
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_WORK_TIPS_2
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_WORK_TIPS_4
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_WORK_TYPE
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.MIN_CURRENT_VALUE
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.WORK_DAY_ALARM_WORK_TIPS
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmGridItemDecoration
import com.oplus.alarmclock.globalclock.view.ScrollGridLayoutManager
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_OFF
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.alarmclock.utils.ClockConstant.ALARM_WORKDAY_SWITCH_OFF
import com.oplus.alarmclock.utils.ClockConstant.ALARM_WORKDAY_SWITCH_ON
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils.DEFAULT_LOOP_CYCLE
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.loadAsync
import com.oplus.clock.common.utils.then
import java.util.Calendar

class AddWorkAlarmManager(private val alarmManager: AddAlarmManager, val mContext: Context) {

    /**
     * 工作类型选项下标
     */
    var mWorkdayTypePosition = 0

    /**
     * 工作日类型弹窗
     */
    private var mWorkDayTypeDialog: AlertDialog? = null
    private var mAlarmWorkdayCheck: BooleanArray = booleanArrayOf(false, false, false, false)
    private var mAlarmWorkdayStr: Array<String> = arrayOf(
        mContext.resources.getString(R.string.oplus_workday_switch),
        mContext.resources.getString(R.string.single_dayoff_on_sunday),
        mContext.resources.getString(R.string.work_six_days_this_week),
        mContext.resources.getString(R.string.work_five_days_this_week),
        mContext.resources.getString(R.string.work_five_days_this_week)
    )

    /**
     * 适配器
     */
    private val mListAdapter by lazy {
        LoopAlarmGridAdapter()
    }
    private var mGridLayoutManager: ScrollGridLayoutManager? = null

    /**
     * 分割线
     */
    private var mLoopAlarmListDecoration = LoopAlarmGridItemDecoration()

    /**
     * 轮班闹钟不同item数量对应不同高度
     */
    private val mHeightSmall: Int = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_174)
    private val mHeightSmall2: Int = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_88)
    private val mHeightMedium = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_206)
    private val mHeightLarge = mContext.resources.getDimensionPixelSize(R.dimen.layout_dp_320)

    private val mHandle = Looper.myLooper()?.let { Handler(it) }

    /**
     * 初始工作日类型
     */
    fun initWork() {
        alarmManager.apply {
            //设置节假日类型
            if (viewHolder.mAlarm.id > 0) {
                if (viewHolder.mAlarm.workdaySwitch == ALARM_WORKDAY_SWITCH_ON) {
                    //获取所需要显示的工作日类型
                    mWorkdayTypePosition =
                        WorkDayTypeUtils.getNeedShowWorkDayType(mContext, viewHolder.mAlarm)
                    mAlarmWorkdayTypeTemp = mWorkdayTypePosition
                }
                if (viewHolder.mAlarm.getmLoopSwitch() == ALARM_WORKDAY_SWITCH_ON) {
                    //轮班闹钟类型
                    mWorkdayTypePosition = LOOP_ALARM_WORK_TYPE
                }
            } else {
                mWorkdayTypePosition = PrefUtils.getInt(
                    mContext,
                    PrefUtils.ALARM_WORKDAY_SWITCH,
                    PrefUtils.ALARM_WORKDAY_SWITCH_KEY,
                    0
                )
                viewHolder.mAlarm.setmWorkDayType(mWorkdayTypePosition)
            }
            if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                //外销只支持轮班工作日闹钟
                mWorkdayTypePosition = LOOP_ALARM_WORK_TYPE
            }
            if (viewHolder.mWorkDayType != -1) {
                //恢复数据工作日类型
                mWorkdayTypePosition = viewHolder.mWorkDayType
            }
        }
        LoopAlarmUtils.setLoopAlarmTime()
        initLoopAlamList()
        setWorkdayTypeText(mWorkdayTypePosition)
    }

    /**
     * 初始化列表
     */
    private fun initLoopAlamList() {
        alarmManager.run {
            viewHolder.run {
                mLoopAlarmList?.apply {
                    adapter = null
                    itemAnimator = null
                    setHasFixedSize(true)
                    mGridLayoutManager =
                        ScrollGridLayoutManager(mContext, LOOP_ALARM_LIST_SPAN_COUNT)
                    layoutManager = mGridLayoutManager
                    removeItemDecoration(mLoopAlarmListDecoration)
                    addItemDecoration(mLoopAlarmListDecoration)
                    adapter = mListAdapter
                    //item 点击事件
                    mListAdapter.setOnItemClickListener {
                        val list = mAlarm.loopAlarmList
                        if (list.size > it) {
                            val itemAlarm = list[it]
                            mAlatmTimesDialog = LoopAlarmDialogUtils.showLoopAlarmTimeSwitchDialog(
                                mAlarm.loopAlarmList,
                                context,
                                itemAlarm,
                                it + 1
                            ) { click, h, m ->
                                clickLoopAlarmItem(click, h, m, itemAlarm, it)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 轮班闹钟item点击
     */
    private fun clickLoopAlarmItem(click: Int, h: Int, m: Int, itemAlarm: Alarm, pos: Int) {
        alarmManager.run {
            viewHolder.run {
                if (click == LoopAlarmDialogUtils.BUTTON_POSITIVE) {
                    itemAlarm.hour = h
                    itemAlarm.minutes = m
                    mAlarm.loopAlarmList[pos] = itemAlarm
                    mListAdapter.notifyItemChanged(pos)
                    val conStr = LoopAlarmUtils.computeLoopDays(mAlarm.loopAlarmList)
                    mAlarm.setmLoopWorkDays(conStr.first)
                    mAlarm.setmLoopRestDays(conStr.second)
                    updateLeftTimeInfo()
                    setWorkdayTypeText(mWorkdayTypePosition)
                    mScrollPanel.setNestedScrollingEnabled(true)
                }
            }
        }
    }

    /**
     * 切换为轮班闹钟样式
     */
    fun changeLoopAlarm() {
        alarmManager.apply {
            viewHolder.apply {
                mLoopAlarmListLayout.visibility = View.VISIBLE
                mTimePickerLayout.visibility = View.GONE
                workDayBack(mAlarm.getmWorkDayType())
                notifyData(mAlarm.loopAlarmList, mAlarm)
                LiteEventBus.instance.send(
                    LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE,
                    true
                )
            }
        }
    }

    /**
     * 设置工作日类型文本
     *
     * @param position
     */
    private fun setWorkdayTypeText(position: Int) {
        alarmManager.apply {
            viewHolder.apply {
                if (position == mAlarmWorkdayStr.size - 1) {
                    mWorkDaySubTitle.text = mContext.resources.getQuantityString(
                        R.plurals.loop_count_number,
                        mAlarm.getmLoopCycleDays(),
                        mAlarm.getmLoopCycleDays(),
                        mAlarm.getmLoopWorkDays(),
                        mAlarm.getmLoopWorkDays()
                    )
                } else {
                    mWorkDaySubTitle.text = mAlarmWorkdayStr[position]
                }
            }
        }
    }

    /**
     * 显示工作日类型弹窗
     */
    fun showWorkDayDialog() {
        alarmManager.apply {
            viewHolder.mFragment.activity?.let {
                val choiceListAdapter = ChoiceListAdapter(
                    it,
                    R.layout.coui_select_dialog_singlechoice,
                    mAlarmWorkdayStr,
                    null,
                    mAlarmWorkdayCheck,
                    false
                )
                val singleChoiceListItemClickListener =
                    DialogInterface.OnClickListener { dialog1: DialogInterface, which: Int ->
                        mAlarmWorkdayCheck[mWorkdayTypePosition] = false
                        mAlarmWorkdayCheck[which] = true
                        mWorkdayTypePosition = which
                        viewHolder.mAlarm.setmWorkDayType(mWorkdayTypePosition)
                        viewHolder.mWorkDaySubTitle.text = mAlarmWorkdayStr[which]
                        updateLeftTimeInfo()
                        dialog1.dismiss()
                        mWorkDayTypeDialog = null
                    }
                val mBottomDialog =
                    COUIAlertDialogBuilder(it, R.style.COUIAlertDialog_BottomAssignment).run {
                        setTitle(R.string.weekdays_type)
                        setBlurBackgroundDrawable(false)
                        setMessage(R.string.workaday_to_create_weekdays_clock)
                        setNegativeButton(R.string.cancel, null)
                        setAdapter(choiceListAdapter, singleChoiceListItemClickListener)
                        setWindowGravity(Gravity.BOTTOM)
                        setWindowAnimStyle(R.style.Animation_COUI_Dialog)
                        show()
                    }
                mBottomDialog.findViewById<COUIAlertDialogMessageView>(android.R.id.message)
                    ?.setTextColor(
                        ContextCompat.getColor(mContext, R.color.text_work_type_message)
                    )
                mWorkDayTypeDialog = mBottomDialog
            }
        }
    }

    /**
     * 设置工作日相关数据
     */
    fun workDayBack(which: Int) {
        alarmManager.apply {
            viewHolder.apply {
                if (which == mAlarmWorkdayStr.size - 1) {
                    //选择轮班闹钟选项
                    mLoopAlarmListLayout.visibility = View.VISIBLE
                    mTimePickerLayout.visibility = View.GONE
                    mAlarm.workdaySwitch = ALARM_HOLIDAY_SWITCH_OFF
                    mAlarm.setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                } else {
                    //选择其他工作日类型
                    mAlarm.setmLoopSwitch(ALARM_HOLIDAY_SWITCH_OFF)
                    mAlarm.workdaySwitch = ALARM_HOLIDAY_SWITCH_ON
                    mLoopAlarmListLayout.visibility = View.GONE
                    mTimePickerLayout.visibility = View.VISIBLE
                }
                mWorkdayTypePosition = which
                //设置工作日类型
                mAlarm.setmWorkDayType(mWorkdayTypePosition)
                //设置工作日类文本
                setWorkdayTypeText(which)
                //更新下次响铃时间
                updateLeftTimeInfo()
                //更新标题
                LiteEventBus.instance.send(
                    LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE,
                    mAlarm.getmLoopSwitch() == 1
                )
                mWorkDayTypeDialog = null
            }
        }
    }

    /**
     * 切换至工作日闹钟
     */
    fun onTabWorkDay(isClick: Boolean, fragment: AddAlarmFragment) {
        alarmManager.apply {
            viewHolder.run {
                COUICardListHelper.setItemCardBackground(
                    mAlarmLabelLayout,
                    COUICardListHelper.getPositionInGroup(3, 1)
                )
                setItemCardBackground()
                mGarbAlarmDateLayout?.visibility = View.GONE
                mGarbAlarmRingLayout?.visibility = View.GONE
                mGarbAlarmDescription?.visibility = View.GONE
                mSnoozeLayout.visibility = View.VISIBLE

                setCommonLayoutMargin(0)
                mAlarm.setmGarbSwitch(0)
                mWorkDayLayout.visibility = View.VISIBLE
                if (isClick) {
                    //执行动画
                    mAnimatorUtil.changeToWorkDay(
                        mCustomLayout,
                        mWorkDayLayout,
                        mWorkdayTypePosition == LOOP_ALARM_WORK_TYPE
                    )
                } else {
                    mCustomLayout.visibility = View.GONE
                }
                if (mCurrentView != AddAlarmViewHolder.VIEW_MONTH_DAY) {
                    customAlarmManager.changToMonthDay(AddAlarmViewHolder.VIEW_MONTH_DAY)
                }
                hideSoftInput()
                if (mWorkdayTypePosition == LOOP_ALARM_WORK_TYPE) {
                    //轮班闹钟
                    tabLoopAlarm(isClick, fragment)
                } else {
                    mAlarm.workdaySwitch = ALARM_WORKDAY_SWITCH_ON
                    viewHolder.mAlarm.setmWorkdayUpdateTime(System.currentTimeMillis())
                    //设置工作日内容
                    mAlarm.setmWorkDayType(mWorkdayTypePosition)
                    mAlarm.holidaySwitch = ALARM_WORKDAY_SWITCH_OFF
                    mAlarm.setRepeat(0)
                }
                setWorkdayTypeText(mWorkdayTypePosition)
                updateLeftTimeInfo()
            }
        }
    }

    /**
     * 是否需要展示轮班闹钟提示
     */
    private fun needShowWorkTips(click: Boolean) {
        alarmManager.apply {
            viewHolder.run {
                if (click) {
                    dismissWorkDayTips()
                }
                val isShow = PrefUtils.getBoolean(
                    mContext,
                    PrefUtils.LOOP_ALARM_TIPS,
                    PrefUtils.LOOP_ALARM_TIPS_KEY,
                    false
                )
                if ((!isShow || mIsNeedShowWorkTips) && mWorkdDayImageRight != null) {
                    //首次进入或者重新进入展示新增工作日tips
                    mIsNeedShowWorkTips = false
                    val delay = if (click) {
                        LOOP_ALARM_WORK_TIPS_2
                    } else {
                        LOOP_ALARM_WORK_TIPS_4
                    }
                    mHandle?.postDelayed({
                        mLoopAlarmTips = LoopAlarmUtils.showLoopAlarmTips(
                            mWorkdDayImageRight,
                            mWorkdDayImageRight.context
                        )
                    }, delay)
                }
            }
        }
    }

    /**
     * 是否需要展示工作日闹钟提示
     */
    fun needShowWorkDayTips() {
        alarmManager.apply {
            viewHolder.run {
                val isShowLoop = PrefUtils.getBoolean(
                    mContext,
                    PrefUtils.LOOP_ALARM_TIPS,
                    PrefUtils.LOOP_ALARM_TIPS_KEY,
                    false
                )
                if (!isShowLoop && mAlarm.id >= 0 && (mAlarm.getmLoopSwitch() == 1 || mAlarm.workdaySwitch == 1)) {
                    //工作日类型首次进入只需要弹出一个tips
                    PrefUtils.putBoolean(
                        mContext,
                        PrefUtils.WORK_DAY_ALARM_TIPS,
                        PrefUtils.WORK_DAY_ALARM_TIPS_KEY,
                        true
                    )
                    return
                }
                val isShow = PrefUtils.getBoolean(
                    mContext,
                    PrefUtils.WORK_DAY_ALARM_TIPS,
                    PrefUtils.WORK_DAY_ALARM_TIPS_KEY,
                    false
                )
                if ((!isShow || mIsNeedShowWorkDayTips) && mAlarmTypeTabList != null) {
                    mIsNeedShowWorkDayTips = false
                    mHandle?.postDelayed({
                        val tipsView = mAlarmTypeTabList.layoutManager?.findViewByPosition(1)
                        if (tipsView != null) {
                            mWorkDayAlarmTips =
                                LoopAlarmUtils.showWorkDayAlarmTips(tipsView, tipsView.context)
                        }
                    }, WORK_DAY_ALARM_WORK_TIPS)
                }
            }
        }
    }

    /**
     * 销毁
     */
    fun onDestroy() {
        mHandle?.removeCallbacksAndMessages(null)
    }

    /**
     * 切换轮班闹钟
     */
    private fun tabLoopAlarm(click: Boolean, fragment: AddAlarmFragment) {
        alarmManager.apply {
            viewHolder.run {
                mLoopAlarmListLayout.visibility = View.VISIBLE
                mTimePickerLayout.visibility = View.GONE
                if (!click && mAlarm.id > 0) {
                    //非点击切换，加载数据
                    fragment.loadAsync {
                        LoopAlarmUtils.getLoopAlarms(mContext, mAlarm.id.toInt())
                    }.then {
                        //是否为页面重载
                        val reload = mAlarm.loopAlarmList.size != 0
                        if (!reload) {
                            mAlarm.loopAlarmList = it
                        }
                        if (reload) {
                            //重载数据
                            notifyData(mAlarm.loopAlarmList, mAlarm)
                            val conStr = LoopAlarmUtils.computeLoopDays(mAlarm.loopAlarmList)
                            mAlarm.setmLoopWorkDays(conStr.first)
                            mAlarm.setmLoopRestDays(conStr.second)
                            updateLeftTimeInfo()
                            setWorkdayTypeText(mAlarmWorkdayStr.size - 1)
                        } else {
                            notifyData(it, mAlarm)
                        }
                        mTempLoopAlarm = mAlarm.deepCopy()
                        if (reload) {
                            mTempLoopAlarm?.loopAlarmList = it
                        }
                    }
                } else {
                    mAlarm.setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                    if (mAlarm.loopAlarmList.size == 0) {
                        //新建闹钟
                        initLoopAlarm(mAlarm, mAlarmHolidaySwitch)
                        notifyData(mAlarm.loopAlarmList, mAlarm)
                    } else {
                        val loopDayTime = LoopAlarmUtils.computeLoopHourAndMinute(
                            mAlarm.loopAlarmList,
                            mAlarm.getmLoopDay()
                        )
                        mAlarm.hour = loopDayTime.first
                        mAlarm.minutes = loopDayTime.second
                        updateLeftTimeInfo()
                        if (!click || mWorkDayType != -1) {
                            //页面重新创建需要刷新数据
                            notifyData(mAlarm.loopAlarmList, mAlarm)
                        }
                    }
                }
                LiteEventBus.instance.send(
                    LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE,
                    true
                )
            }
        }
    }

    /**
     * 初始化轮班闹钟数据
     */
    private fun initLoopAlarm(alarm: Alarm, hSwitch: Int) {
        alarm.apply {
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
            setmLoopCycleDays(LoopAlarmUtils.DEFAULT_LOOP_CYCLE)
            setmLoopWorkDays(LoopAlarmUtils.DEFAULT_LOOP_WORK_DAY)
            setmLoopSwitch(ALARM_WORKDAY_SWITCH_ON)
            setmLoopRestDays("$DEFAULT_LOOP_CYCLE${DatePickerUtils.SPLIT}")
            setmLoopDay(1)
            hour = LoopAlarmUtils.mLoopAlarmHour
            minutes = LoopAlarmUtils.mLoopAlarmMinutes
            workdaySwitch = ALARM_WORKDAY_SWITCH_OFF
            holidaySwitch = hSwitch
            loopAlarmList = LoopAlarmUtils.addDefaultLoopAlarm(LoopAlarmUtils.DEFAULT_LOOP_CYCLE)
        }
    }

    /**
     * 刷新轮班闹钟数据
     */
    fun notifyData(list: List<Alarm>, alarm: Alarm) {
        mListAdapter.updateData(list, alarm)
        mListAdapter.notifyDataSetChanged()
        setLoopAlarmLayout()
        scrollPosition(alarm)
    }

    /**
     * 滚动到指定位置
     */
    private fun scrollPosition(alarm: Alarm) {
        alarmManager.apply {
            viewHolder.run {
                val nowTime = Calendar.getInstance()
                val create = Calendar.getInstance()
                create.timeInMillis = alarm.getmWorkdayUpdateTime()
                val dis = LoopAlarmUtils.getTimeDistance(create, nowTime)
                if (dis >= 0) {
                    //轮班开始时间小于当前时间计算滚动位置
                    val alarmLoopDay = LoopAlarmUtils.computeLoopAlarmDay(
                        alarm.getmLoopCycleDays(),
                        alarm.getmLoopDay(),
                        create,
                        nowTime
                    )
                    if (alarmLoopDay > 0) {
                        mGridLayoutManager?.scrollToPositionWithOffset(
                            alarmLoopDay - 1,
                            LOOP_ALARM_POSITION_WITH_OFFSET
                        )
                    }
                } else {
                    //轮班闹钟开始时间大于当前时间，滚动到顶部
                    mGridLayoutManager?.scrollToPositionWithOffset(
                        0,
                        LOOP_ALARM_POSITION_WITH_OFFSET
                    )
                }
            }
        }
    }

    /**
     * 设置轮班闹钟子闹钟高度
     */
    private fun setLoopAlarmLayout() {
        alarmManager.apply {
            viewHolder.apply {
                val loopAlarmListSize = mAlarm.loopAlarmList.size
                val listLayoutParams: MarginLayoutParams =
                    mLoopAlarmList.layoutParams as MarginLayoutParams
                val layoutParams: MarginLayoutParams =
                    mLoopAlarmListLayout.layoutParams as MarginLayoutParams
                mGridLayoutManager?.setCanScrollVertically(true)
                mGridLayoutManager = ScrollGridLayoutManager(mContext, LOOP_ALARM_LIST_SPAN_COUNT)
                layoutParams.height = MarginLayoutParams.WRAP_CONTENT
                listLayoutParams.topMargin = 0
                if (loopAlarmListSize == MIN_CURRENT_VALUE) {
                    listLayoutParams.height = MarginLayoutParams.WRAP_CONTENT
                    layoutParams.height = mHeightSmall
                    mGridLayoutManager = ScrollGridLayoutManager(mContext, MIN_CURRENT_VALUE)
                    mGridLayoutManager?.setCanScrollVertically(false)
                    mLoopAlarmList.isVerticalFadingEdgeEnabled = false
                } else if (loopAlarmListSize == LOOP_ALARM_LIST_SPAN_COUNT) {
                    listLayoutParams.height = MarginLayoutParams.WRAP_CONTENT
                    listLayoutParams.topMargin =
                        mContext.resources.getDimensionPixelOffset(R.dimen.layout_dp_16)
                    layoutParams.height = mHeightMedium
                    mGridLayoutManager?.setCanScrollVertically(false)
                    mLoopAlarmList.isVerticalFadingEdgeEnabled = false
                } else if (loopAlarmListSize < LOOP_ALARM_LIST_TWO_LINES_COUNT) {
                    listLayoutParams.height = mHeightMedium
                    mLoopAlarmList.isVerticalFadingEdgeEnabled = false
                } else {
                    mLoopAlarmList.isVerticalFadingEdgeEnabled = true
                    listLayoutParams.height = mHeightLarge
                }
                mLoopAlarmList.layoutManager = mGridLayoutManager
                mLoopAlarmList.layoutParams = listLayoutParams
                mLoopAlarmListLayout.layoutParams = layoutParams
            }
        }
    }

    /**
     *
     */
    fun dismissDialog() {
        mWorkDayTypeDialog?.let {
            if (it.isShowing) {
                it.dismiss()
                mWorkDayTypeDialog = null
            }
        }
    }
}