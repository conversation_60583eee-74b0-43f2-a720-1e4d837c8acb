/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : use to schedule every alarm to fire or to snooze ,manage state of the alarm
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
// OPLUS Java File Skip Rule:LineLength,MethodLength
package com.oplus.alarmclock.alarmclock;

import static android.content.Context.NOTIFICATION_SERVICE;
import static com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper.SUPPORT_ALARM_SNOOZE_FLUID;
import static com.oplus.alarmclock.utils.ClockConstant.EVENT_ELAPSED_TIME_UNTIL_NEXT_ALARM;
import static com.oplus.alarmclock.utils.GarbAlarmUtils.INT_MAX_VALUE;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.KeyguardManager;
import android.app.Notification;
import android.app.Notification.Builder;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.OplusNotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RemoteViews;
import android.widget.Toast;

import androidx.appsearch.builtintypes.AlarmInstance;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper;
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeServiceUtils;
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils;
import com.oplus.alarmclock.alert.AlarmService;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.appfunctions.ClockAppSearchManager;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.BitmapUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.alarmclock.utils.LoopAlarmUtils;
import com.oplus.alarmclock.utils.NotificationUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.StatusBarUtils;
import com.oplus.alarmclock.utils.ToastManager;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.clock.common.utils.VersionUtils;

import java.util.Calendar;
import java.util.List;

public class AlarmStateManager extends BroadcastReceiver {
    private static final String TAG = "AlarmStateManager";

    /* delay 2s to avoid ringing at the same time with Calendar Alert.*/
    public static final long DELAY_TIME = 0;

    public static final String ACTION_CANCEL_POWEROFF_ALARM = "org.codeaurora.poweroffalarm.action.CANCEL_ALARM";
    public static final String POWER_OFF_ALARM_PACKAGE = "com.qualcomm.qti.poweroffalarm";
    public static final String POWER_OFF_ALARM_EXTRA_TIME = "time";
    public static final String CHANGE_STATE_ACTION = "change_state";

    public static final int AHEAD_REMIND_NOTIFICATION_ID = -1011;
    public static final int MISS_ALARM_NOTIFICATION_ID = -1012;

    public static final String OLD_CHANNEL_ID = "com.oplus.alarmclock";
    public static final String CHANNEL_ID = "com.oplus.alarmclock.channel";

    // Intent category tag used when schedule state change intents in alarm manager.
    private static final String ALARM_MANAGER_TAG = "ALARM_MANAGER";
    private static final String ALARM_NEXT_NOTICES_TAG = "ALARM_NEXT_NOTICES_TAG";
    // Intent action to trigger an instance state change.
    // Intent action for an AlarmManager alarm serving only to set the next alarm indicators
    private static final String INDICATOR_ACTION = "indicator";
    // Extra key to set the global broadcast id.
    private static final String ALARM_GLOBAL_ID_EXTRA = "intent.extra.alarm.global.id";
    // Extra key to set the desired state change.
    private static final String ALARM_STATE_EXTRA = "intent.extra.alarm.state";
    // Intent category tags used to dismiss, snooze or delete an alarm
    private static final String ALARM_DISMISS_TAG = "DISMISS_TAG";
    private static final String ALARM_SNOOZE_TAG = "SNOOZE_TAG";
    private static final String ACTION_SET_POWEROFF_ALARM = "org.codeaurora.poweroffalarm.action.SET_ALARM";
    //Use package as unique id.

    private static final String CHANNEL_ID_NEXT_ALARM = "com.oplus.alarmclock.next.alarm";

    private static final String ACTION_NEXT_ALARM_NOTICES = "action.next.alarm.notices";

    private static final long NEXT_ALARM_NOTICES = 15 * 60 * 1000;
    private static final long ONE_MIN = 60 * 1000;
    private static final int MTK_EXTRA = 8;
    private static final int QUALCOMM_EXTRA = 5;
    private static final int MTK_PRE_SCHEDULE_POWER_OFF_ALARM = 7;
    private static final long ALARM_RECREATE_MIN_TIME = 500L;
    public static long sCurrentNoticesScheduleId = -1;
    private static AlarmRepeat sAlarmRepeat;


    public static void registerInstance(Context context, AlarmSchedule alarmSchedule) {
        registerInstance(context, alarmSchedule, false);
    }

    public static void registerInstance(Context context, AlarmSchedule alarmSchedule, Boolean fixAlarmInstances) {
        if ((context == null) || (alarmSchedule == null)) {
            Log.e(TAG, "registerInstance context is null or alarmSchedule is null");
            return;
        }
        Log.e(TAG, "registerInstance :alarmSchedule = " + Formatter.formatTime(alarmSchedule.getTime()) + " fix:" + fixAlarmInstances);
        //注册新闹钟如果是轮班闹钟需更新轮班闹钟主闹钟时间
        LoopAlarmUtils.updateLoopAlarmTime(alarmSchedule);
        StatusBarUtils.setStatusBarAlarmIconVisible(context, true);
        final long currentTime = Calendar.getInstance().getTimeInMillis();
        long alarmTime = ScheduleUtils.getAlarmTimeInMills(alarmSchedule);
        if ((alarmSchedule.getAlarm() != null && alarmSchedule.getAlarm().getmGarbSwitch() == 1) || fixAlarmInstances) {
            alarmTime = alarmSchedule.getTime();
        }
        int alarmSnooze = ClockConstant.SNOOZE_AFTER_MIN;
        if (alarmSchedule.getAlarm() != null) {
            alarmSnooze = alarmSchedule.getAlarm().getmSnoozeTime();
        }
        long timeoutTime = ScheduleUtils.getTimeoutInMills(alarmTime, alarmSnooze);
        final long missedTTL = ScheduleUtils.getMissedTimeToLiveInMills(alarmTime);
        Log.i(TAG, "registerInstance currentTime = " + Formatter.formatTime(currentTime) + ", alarmTime = " + Formatter.formatTime(alarmTime)
                + ", timeoutTime = " + Formatter.formatTime(timeoutTime) + ", missedTTL = " + Formatter.formatTime(missedTTL));
        final int alarmState = alarmSchedule.getAlarmState();
        Log.i(TAG, "registerInstance alarmState =  " + alarmState);
        if (alarmState == ClockContract.Schedule.DISMISSED_STATE) {
            Log.e(TAG, "Alarm Instance is dismissed, but never deleted");
            AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "alarmState is DISMISSED_STATE, setDismissState");
            setDismissState(context, alarmSchedule);
            return;
        }
        if (alarmState == ClockContract.Schedule.FIRED_STATE) {
            final boolean hasTimeout = currentTime > timeoutTime;
            Log.i(TAG, "registerInstance hasTimeout:" + hasTimeout);
            if (!hasTimeout) {
                AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "alarmState is FIRED_STATE & hasTimeout is false, dispatchAlarmAction FIRED_STATE");
                dispatchAlarmAction(context, alarmSchedule, ClockContract.Schedule.FIRED_STATE);
                return;
            }
        }
        if ((currentTime < alarmTime) || (alarmState == ClockContract.Schedule.SNOOZE_STATE)) {
            Log.i(TAG, "registerInstance setupRtcAlarm scheduleId:" + alarmSchedule.getId());
            AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "currentTime < alarmTime or alarmState is SNOOZE_STATE, setupRtcAlarm FIRED_STATE");
            setupRtcAlarm(context, alarmTime, alarmSchedule.getId(), ClockContract.Schedule.FIRED_STATE);
            return;
        }
        if (currentTime > missedTTL) {
            Log.i(TAG, "registerInstance  Alarm is so old, just dismiss it, missedTTL = " + DateFormat.format("MM/dd/yyyy hh:mm a", missedTTL));
            AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "currentTime > missedTTL, setDismissState");
            setDismissState(context, alarmSchedule);
            return;
        }
        if ((currentTime > alarmTime) && (currentTime < missedTTL)) {
            AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "currentTime > alarmTime or currentTime < missedTTL, dispatchAlarmAction FIRED_STATE");
            dispatchAlarmAction(context, alarmSchedule, ClockContract.Schedule.FIRED_STATE);
            return;
        }
        AlarmRingStatisticUtils.statisticsregisterInstance(context, alarmSchedule, "setSilentState");
        setSilentState(context, alarmSchedule);
    }

    /**
     * 各种特殊情况下如时区变更，当前时间变更后修复所有闹钟响铃任务
     *
     * @param context
     * @param action
     */
    public static void fixAlarmInstances(Context context, String action) {
        Log.i(TAG, "fixAlarmInstances");
        if (context == null) {
            Log.i(TAG, "fixAlarmInstances context is null");
            return;
        }
        // Register all instances after major time changes or when phone restarts
        List<AlarmSchedule> list = ScheduleUtils.getAllSchedules(context);
        if ((list == null) || (list.isEmpty())) {
            Log.e(TAG, "fixAlarmInstances alarm data is empty");
            return;
        }
        //当前时间
        final long currentTime = Calendar.getInstance().getTimeInMillis();
        for (AlarmSchedule schedule : list) {
            long alarmId = schedule.getAlarmId();
            boolean alarmEnabled = schedule.isAlarmEnabled();
            if (schedule.getAlarm().getmGarbSwitch() == 1 && alarmEnabled) {
                GarbAlarmUtils.fixGarbAlarm(schedule, context, action);
                continue;
            }
            if (alarmEnabled) {
                final Alarm alarm = schedule.getAlarm();
                //响铃时间
                long alarmTime = ScheduleUtils.getAlarmTimeInMills(schedule);
                //上次响铃时间
                long priorAlarmTime = AlarmUtils.getPreviousAlarmTimeForRepeatAndWorkDay(alarm, schedule.getTime(), "fixAlarmInstances");
                //响铃+稍后提醒时间 （ 稍后提醒改为新字段，不在使用系统配置字段 ）
                long missedTTLTime = ScheduleUtils.getTimeoutInMills(alarmTime, alarm.getmSnoozeTime());

                //没有重复响铃的闹钟
                boolean isNoneRepeatedAlarm = priorAlarmTime == 0;
                //响一次闹铃
                // 如果非自定义闹钟，并且非工作日闹钟则为响一次闹钟
                if ((alarm.getRepeatSet() == 0) && (DatePickerUtils.isEmptySpecialDay(alarm.getmSpecialAlarmDays()))
                        && (alarm.getWorkdaySwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_OFF) && (alarm.getmLoopSwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_OFF)) {
                    isNoneRepeatedAlarm = true;
                }
                Log.i(TAG, " fixAlarmInstances currentTime: " + Formatter.formatTime(currentTime) + ", alarmTime: " + Formatter.formatTime(alarmTime)
                        + ", priorAlarmTime: " + Formatter.formatTime(priorAlarmTime) + ", missedTTLTime: " + Formatter.formatTime(missedTTLTime) + " isNoneRepeatedAlarm: " + isNoneRepeatedAlarm);

                if ((isNoneRepeatedAlarm || Intent.ACTION_TIMEZONE_CHANGED.equals(action)) && (currentTime < alarmTime) && (schedule.getSnoonzeTime() == 0)) {
                    // Delete old schedule.Register a new schedule to state manager
                    Log.i(TAG, "fixAlarmInstances alarm is not repeated. change the alertTime nearest time!");
                    // recreate instance.
                    if (alarmTime - currentTime > ALARM_RECREATE_MIN_TIME) {
                        Log.w(TAG, "fixAlarmInstances alarm time is bigger than 500ms, recreate the alarm");
                        AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                        if ((Intent.ACTION_TIME_CHANGED.equals(action)) && (currentSchedule != null)
                                && (currentSchedule.getId() == schedule.getId())) {
                            //to avoid bug 1859264, time change when alarm is ringing, alarm will be canceled.
                            Log.w(TAG, "fixAlarmInstances time change when alarm is ringing, schedule " + currentSchedule);
                        } else {
                            // recreate instance.
                            //修复关闭一次的闹钟，根据时间计算是否再次展示关闭一次的闹钟
                            recreateAlarmSchedulesForCloseNext(context, alarm);
                        }
                    } else {
                        Log.i(TAG, "fixAlarmInstances alarm time is less than 500ms");
                        //注册一个新的闹钟
                        registerInstance(context, schedule, true);
                    }
                    //priorAlarmTime is schedule's prior alarm time
                } else if ((currentTime < priorAlarmTime) || (currentTime > missedTTLTime)) {
                    // The time change is so dramatic the AlarmInstance doesn't make any sense;
                    // remove it and schedule the new appropriate instance.
                    Log.i(TAG, "fixAlarmInstances The current time is not between the last ring and the next ring  isCloseOnce:" + alarm.isCloseOnceAlarmClock());
                    if (alarm.isCloseOnceAlarmClock()) {
                        boolean isNeedCloseNextAlarm = AlarmUtils.isNeedKeepCloseNextAlarm(alarm);
                        Log.i(TAG, "fixAlarmInstances The current time is not between the last ring and the next ring"
                                + " action  = " + action + " isNeedCloseNextAlarm = " + isNeedCloseNextAlarm);
                        if (isNeedCloseNextAlarm) {
                            //关闭一次的闹钟，检查是否继续保持关闭一次
                            AlarmStateManager.setDismissStateCloseOnceNext(context, schedule, alarm.getmCloseOnceTimeNext(), alarm.getmCloseOncePriTime());
                        } else {
                            //关闭当前闹钟，检查是否为重复闹钟并且设置新的闹钟
                            AlarmStateManager.setDismissState(context, schedule);
                        }
                    } else {
                        //关闭当前闹钟，检查是否为重复闹钟并且设置新的闹钟
                        AlarmStateManager.setDismissState(context, schedule);
                    }
                } else {
                    Log.i(TAG, "fixAlarmInstances registerInstance");
                    boolean isNeedCloseNextAlarm = AlarmUtils.isNeedKeepCloseNextAlarm(alarm);
                    Log.i(TAG, "fixAlarmInstances action  other = " + action + " isNeedCloseNextAlarm = " + isNeedCloseNextAlarm);
                    if (!isNeedCloseNextAlarm) {
                        Log.i(TAG, "fixAlarmInstances setAlarmCloseOnce");
                        AlarmUtils.cleanAlarmCloseOnceInfo(AlarmClockApplication.getInstance(), schedule.getAlarmId());
                    }
                    if (!TimerSeedlingHelper.isSupportFluidCloud() && !SUPPORT_ALARM_SNOOZE_FLUID) {
                        //不支持流体云，直接取消通知栏通知信息
                        ScheduleUtils.cancelNotification2(context, schedule.getId(), true);
                    }
                    sCurrentNoticesScheduleId = -1;
                    registerInstance(context, schedule, true);
                }
            } else {
                Log.i(TAG, "fixAlarmInstances clearAllSchedulesOfAlarm");

                ScheduleUtils.clearAllSchedulesOfAlarm(context, alarmId);
            }
        }
        AlarmStateManager.showNextAlarmNotices(context);
        AlarmUtils.checkIfShowStatusIcon(context);
    }

    /**
     * Fix and update all alarm when a SpeechAssistant new alarm is added.
     *
     * @param context application context
     */
    public static void fixAlarmInstancesForSpeechAssistant(Context context) {
        Log.i(TAG, "fixAlarmInstancesForSpeechAssistant!");
        boolean isEnable = false;
        List<Alarm> list = AlarmUtils.getAllAlarms(context);
        if (list != null) {
            for (final Alarm alarm : list) {
                if (alarm.isEnabled()) {
                    isEnable = true;
                    recreateAlarmSchedulesForCloseNext(context, alarm);
                }
            }
        }
        StatusBarUtils.setStatusBarAlarmIconVisible(context, isEnable);
    }

    public static void recreateAlarmSchedulesForCloseNext(Context context, Alarm alarm) {

        boolean isNeedKeep = AlarmUtils.isNeedKeepCloseNextAlarm(alarm);
        //是否继续保持关闭
        if (isNeedKeep) {
            Log.e(TAG, "fixAlarmInstances action isNoneRepeatedAlarm : ");
            recreateAlarmSchedulesCloseOnceNext(context, alarm);
        } else {
            recreateAlarmSchedules(context, alarm);
        }
    }


    //当系统当前时间和闹钟时间一样  并且是工作日类型闹钟时 -- isCloseOnce = true ,不会清除关闭下一次数据，同时会向下延续一天
    public static void recreateAlarmSchedules(Context context, Alarm alarm) {
        Log.i(TAG, "recreateAlarmSchedules");
        ScheduleUtils.clearAllSchedulesOfAlarm(context, alarm.getId());
        AlarmUtils.cleanCloseOnceStatus(alarm);
        AlarmUtils.setupAlarmInstance(context, alarm);

    }

    public static void recreateAlarmSchedulesCloseOnceNext(Context context, Alarm alarm) {
        Log.i(TAG, "recreateAlarmSchedulesCloseOnceNext");
        ScheduleUtils.clearAllSchedulesOfAlarm(context, alarm.getId());
        AlarmUtils.setupAlarmInstanceCloseOnceNext(context, alarm);
    }


    private static void setSilentState(Context context, AlarmSchedule schedule) {
        final long scheduleId = schedule.getId();
        Log.v(TAG, "Setting silent state to instance " + scheduleId);

        if (context == null) {
            Log.d(TAG, "setSilentState context is null");
            return;
        }
        // Update alarm in db
        schedule.setAlarmState(ClockContract.Schedule.SILENT_STATE);
        updateScheduleState(context, scheduleId, ClockContract.Schedule.SILENT_STATE);

        // Setup instance notification and scheduling timers
        long alarmMills = ScheduleUtils.getAlarmTimeInMills(schedule);
        setupRtcAlarm(context, alarmMills, scheduleId, ClockContract.Schedule.FIRED_STATE);
    }

    private static void setFiredState(Context context, AlarmSchedule alarmSchedule) {
        final long scheduleId = alarmSchedule.getId();

        Log.i(TAG, "setFiredState setting fire state to instance " + scheduleId);

        if (context == null) {
            Log.e(TAG, "setFiredState context is null");
            return;
        }

        long alarmTime = ScheduleUtils.getAlarmTimeInMills(alarmSchedule, false);
        if (alarmSchedule.getTime() == 0) {
            Log.w(TAG, "setFiredState update from datebase 7, mTime is 0!");
            alarmSchedule.setTime(alarmTime);
        }

        long currentMills = Calendar.getInstance().getTimeInMillis();
        if ((alarmTime < currentMills
                - ScheduleUtils.MISSED_TIME_TO_LIVE_MINUTE_OFFSET * ONE_MIN)) {
            Log.i(TAG, "setFiredState Time past, dismiss alarm! alarmTime: " + alarmSchedule.getTime() + " ,currentMills:" + currentMills);
            Log.i(TAG, "Time past, dismiss alarm! " + alarmSchedule.getTime() + " " + currentMills);
            setDismissState(context, alarmSchedule);
            return;
        }
        // Update alarm state in db
        updateScheduleState(context, scheduleId, ClockContract.Schedule.FIRED_STATE);
        final long alarmId = alarmSchedule.getAlarmId();
        if (alarmId > 0) {
            // if the time changed *backward* and pushed an instance from missed back to fired,
            // remove any other schedules that may exist
            ScheduleUtils.deleteSchedulesOfAlarm(context, alarmId, scheduleId);

            Alarm alarm = AlarmUtils.getAlarm(context, alarmId);
            if ((alarm == null) || (!alarm.isEnabled())) {
                Log.e(TAG, "setFiredState alarm is deleted or disabled, but schedule is not!!!");
                ScheduleUtils.deleteSchedule(context, scheduleId);
                return;
            }
        }

        AlarmService.startAlarm(context, scheduleId);
        // Start the alarm and schedule timeout timer for it
        long alarmMills = ScheduleUtils.getTimeoutInMills(alarmTime, getAlarmRepeat(context).getmAlarmDuration());
        //闹钟响铃后设置响铃时长到达后的action
        if (alarmSchedule.isSnoozeAvailble(alarmSchedule.getAlarm())) {
            //稍后提醒
            setupRtcAlarm(context, alarmMills, scheduleId, ClockContract.Schedule.SNOOZE_STATE);
            ClockAppSearchManager.updateAlarmState(context, alarmId, AlarmInstance.STATUS_FIRING);
        } else {
            //没有稍后提醒次数关闭闹钟
            Log.i(TAG, "setFiredState can not be snoozed, dismiss this alarm. ");
            setupRtcAlarm(context, alarmMills, scheduleId, ClockContract.Schedule.DISMISSED_STATE);
            ClockAppSearchManager.updateAlarmState(context, alarmId, AlarmInstance.STATUS_SCHEDULED);
        }
    }

    private static void updateNextSnoozeSchedule(Context context, AlarmSchedule schedule) {
        ContentValues values = new ContentValues();
        values.put(ClockContract.Schedule.YEAR, schedule.getYear());
        values.put(ClockContract.Schedule.MONTH, schedule.getMonth());
        values.put(ClockContract.Schedule.DAY, schedule.getDay());
        values.put(ClockContract.Schedule.HOUR, schedule.getHour());
        values.put(ClockContract.Schedule.MINUTES, schedule.getMinute());
        values.put(ClockContract.Schedule.ALARM_TIME, schedule.getTime());

        values.put(ClockContract.Schedule.ALARM_STATE, schedule.getAlarmState());
        values.put(ClockContract.Schedule.SNOOZETIME, schedule.getSnoonzeTime());

        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, schedule.getId());
        context.getContentResolver().update(uri, values, null, null);
    }

    private static void updateNextAlarm(Context context) {
        // Sets a surrogate alarm with alarm manager that provides the AlarmClockInfo for the
        // alarm that is going to fire next. The operation is constructed such that it is ignored
        // by AlarmStateManager.

        final AlarmSchedule nextAlarm = ScheduleUtils.getNextFiringAlarm(context);

        final AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        if (alarmManager == null) {
            Log.w(TAG, "updateNextAlarm, can not get AlarmManager instance!");
            return;
        }
        final int flags = (nextAlarm == null) ? PendingIntent.FLAG_NO_CREATE : 0;
        final PendingIntent operation = PendingIntent.getBroadcast(context, 0 /* requestCode */,
                AlarmStateManager.createIndicatorIntent(context), Utils.getPendingIntentFlagAboveS(flags));

        try {
            if (nextAlarm != null) {
                Log.i(TAG, "Setting upcoming AlarmClockInfo for alarm: " + nextAlarm.getId());
                long alarmTime = nextAlarm.getTime();

                final AlarmManager.AlarmClockInfo info = new AlarmManager.AlarmClockInfo(alarmTime, operation);
                alarmManager.setAlarmClock(info, operation);
            } else if (operation != null) {
                Log.i(TAG, "Canceling upcoming AlarmClockInfo");
                alarmManager.cancel(operation);
            }
        } catch (Exception e) {
            Log.e(TAG, "updateNextAlarm error:" + e);
        }
    }


    static AlarmRepeat getAlarmRepeat(Context context) {
        sAlarmRepeat = AlarmUtils.getAlarmsRepeatInfo(context);
        if (null == sAlarmRepeat) {
            sAlarmRepeat = new AlarmRepeat();
            sAlarmRepeat.initAlarmRepeat();
        }
        Log.i(TAG, " alarm repeat :" + sAlarmRepeat);
        return sAlarmRepeat;
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    public static void setSnoozeState(final Context context, AlarmSchedule schedule) {
        if ((schedule == null) || (context == null)) {
            return;
        }
        if (schedule.getAlarmState() == ClockContract.Schedule.SNOOZE_STATE) {
            //已经是稍后提醒状态
            return;
        }

        final long scheduleId = schedule.getId();
        Log.i(TAG, "Setting snooze state to instance " + scheduleId);
        // Stop alarm if this schedule is firing it
        if ((CurrentAlarmScheduleHolder.getAlarmSchedule() != null) || !Utils.supportConfineMode()) {
            AlarmService.stopAlarm(context, scheduleId, false/*isDismissedAlarm*/);
        }

        // Add 5 minutes from now time.
        Calendar c = Calendar.getInstance();
        //稍后提醒时间修改为整分钟
        c.add(Calendar.MINUTE, schedule.getAlarm().getmSnoozeTime());
        if (schedule.getAlarm().getmGarbSwitch() == 1) {
            //秒抢下次响铃时间
            c = GarbAlarmUtils.getNextAlarmTime(schedule.getAlarm(), Calendar.getInstance());
        }

        // Update schedule state and new alarm time in db.
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH);
        int day = c.get(Calendar.DAY_OF_MONTH);
        int hour = c.get(Calendar.HOUR_OF_DAY);
        int minute = c.get(Calendar.MINUTE);
        long mills = c.getTimeInMillis();

        schedule.setYear(year);
        schedule.setMonth(month);
        schedule.setDay(day);
        schedule.setHour(hour);
        schedule.setMinute(minute);
        schedule.setSnoonzeTime(schedule.getSnoonzeTime() + 1);
        schedule.setAlarmState(ClockContract.Schedule.SNOOZE_STATE);
        schedule.setTime(mills);

        updateNextSnoozeSchedule(context, schedule);
        // Setup schedule notification and scheduling timers
        setupRtcAlarm(context, mills, scheduleId, ClockContract.Schedule.FIRED_STATE);
        if (!Utils.supportConfineMode()) {
            AlarmSnoozeServiceUtils.showAlarmSnoozeCardAndStartService(context.getApplicationContext(), schedule, mills);
        }
        Log.i(TAG, "sendSnoozeNotification schedule = " + schedule);

        // Display the snooze minutes in a toast.
        // Do not display TOAST in confine mode
        if (!Utils.supportConfineMode()) {
            showSnoozeToast(context, String.valueOf(schedule.getAlarm().getmSnoozeTime()));
        }

        Log.d(TAG, "send Snooze ACTION_SNOOZE_ALARM ");
        Intent intent = new Intent(AlarmClock.ACTION_SNOOZE_ALARM);
        intent.putExtra(AlarmClockFragment.CURRENT_REPEAT_ALARM, schedule.getAlarm());
        context.sendBroadcast(intent);
        ClockAppSearchManager.updateAlarmState(context, schedule.getAlarmId(), AlarmInstance.STATUS_SNOOZED);

    }

    /**
     * 弹出旧的闹钟稍后提醒通知
     *
     * @param context
     * @param schedule
     */
    public static void showSnoozeNotificationOld(Context context, AlarmSchedule schedule) {
        if (schedule == null || context == null) {
            return;
        }
        // Add 5 minutes from now time.
        final Calendar c = Calendar.getInstance();
        //稍后提醒时间修改为整分钟
        c.add(Calendar.MINUTE, schedule.getAlarm().getmSnoozeTime());

        long mills = c.getTimeInMillis();
        sendSnoozeNotification(context, schedule, mills);
    }

    private static void updateParentAlarm(Context context, long alarmId) {
        final Alarm alarm = AlarmUtils.getAlarm(context, alarmId);
        if (alarm == null) {
            AlarmRingStatisticUtils.statisticsScheduleSetUpInterrupt(context, alarmId, "updateParentAlarm alarm is null");
            Log.e(TAG, "updateParentAlarm Parent has been deleted with alarmId: " + alarmId);
            return;
        }

        if (!alarm.isRepeatAlarm(Calendar.getInstance())) {
            if (alarm.getmGarbSwitch() == 0) {
                if (alarm.getDeleteAfterUse() == 1) {
                    Log.i(TAG, "updateParentAlarm Deleting parent alarm: " + alarmId);
                    AlarmUtils.deleteAlarm(context, alarmId, false);
                } else {
                    Log.i(TAG, "updateParentAlarm Disabling parent alarm: " + alarmId);
                    AlarmUtils.disableAlarmNoNeedSetNextAlarm(context, alarmId, false);
                }
            } else {
                //秒抢无响铃闹钟，更新闹钟列表
                LiteEventBus.Companion.getInstance().send(EVENT_ELAPSED_TIME_UNTIL_NEXT_ALARM, true);
//                AlarmUtils.disableAlarmNoNeedSetNextAlarm(context, alarmId, false);
            }
        } else {
            // Schedule the next repeating schedule after the current time
            Log.i(TAG, "this alarm is repeat alarm : " + alarm);
            AlarmUtils.setupAlarmInstance(context, alarm);
            AlarmStateManager.showNextAlarmNotices(context);
        }
    }

    private static void updateParentCloseNextAlarm(Context context, long alarmId) {
        final Alarm alarm = AlarmUtils.getAlarm(context, alarmId);
        if (alarm == null) {
            Log.e(TAG, "updateParentAlarm Parent has been deleted with alarmId: " + alarmId);
            return;
        }

        if (!alarm.isRepeatAlarm()) {
            if (alarm.getDeleteAfterUse() == 1) {
                Log.i(TAG, "updateParentAlarm Deleting parent alarm: " + alarmId);
                AlarmUtils.deleteAlarm(context, alarmId, false);
            } else {
                Log.i(TAG, "updateParentAlarm Disabling parent alarm: " + alarmId);
                AlarmUtils.disableAlarm(context, alarmId, false);
            }
        } else {
            // Schedule the next repeating schedule after the current time
            Log.i(TAG, "updateParentAlarm this alarm is repeat alarm : " + alarm);
            //there close once  diff with updateParentAlarm
            AlarmUtils.setupAlarmInstanceCloseOnceNext(context, alarm);
            AlarmStateManager.showNextAlarmNotices(context);
        }
    }

    /**
     * This will set the alarm schedule to the SILENT_STATE and update the application notifications
     * and schedule any state changes that need to occur in the future.
     *
     * @param context  application context
     * @param schedule to set state to
     */
    @SuppressLint("UnsafeImplicitIntentLaunch")
    public static void setDismissState(Context context, AlarmSchedule schedule) {
        try {
            Log.i(TAG, "setDismissState schedule: " + schedule + ",sCurrentNoticesScheduleId" + sCurrentNoticesScheduleId);
            sCurrentNoticesScheduleId = -1;
            if ((schedule != null) && (context != null)) {
                // Delete instance as it is not needed anymore
                ScheduleUtils.deleteSchedule(context, schedule.getId());
                // Remove all other timers and notifications associated to it
                unregisterInstance(context, schedule);

                // Check parent if it needs to reschedule, disable or delete itself
                updateParentAlarm(context, schedule.getAlarm().getId());
                //cancel the next alarm or close once alarm

                if ((schedule.getAlarm().getmCloseOnceTimeNext() > 0) || (schedule.getAlarm().getmCloseOncePriTime() > 0)) {
                    Log.i(TAG, "setDismissState cancel close once time");
                    //the third parameter is close once status,false:clean close once info,true:keep or add close once info
                    //the fourth parameter is the next ring time under normal condition
                    //the fifth parameter is last ring time of current time
                    AlarmUtils.cleanAlarmCloseOnceInfo(AlarmClockApplication.getInstance(), schedule.getAlarmId());
                }
                if ((schedule.getAlarm() != null) && schedule.getAlarm().isRepeatAlarm()) {
                    Log.d(TAG, "send dismiss ACTION_REPEAT_ALARM_DISMISS ");
                    Intent intent = new Intent(AlarmClockFragment.ACTION_REPEAT_ALARM_DISMISS);
                    intent.putExtra(AlarmClockFragment.CURRENT_REPEAT_ALARM, schedule.getAlarm());
                    context.sendBroadcast(intent);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "setDismissState " + e);
        }
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    public static void setDismissStateCloseOnceNext(Context context, AlarmSchedule schedule, long nextAlarmTime, long previousAlarmTime) {
        Log.i(TAG, "setDismissState schedule: " + schedule + "   isKeepCancelNextAlarm = " + ",sCurrentNoticesScheduleId:" + sCurrentNoticesScheduleId);
        if ((schedule != null) && (context != null)) {
            // Delete instance as it is not needed anymore
            ScheduleUtils.deleteSchedule(context, schedule.getId());
            // Remove all other timers and notifications associated to it
            unregisterInstance(context, schedule);
            sCurrentNoticesScheduleId = -1;

            // Check parent if it needs to reschedule, disable or delete itself

            updateParentCloseNextAlarm(context, schedule.getAlarm().getId());
            //cancel the next alarm or close once alarm

            if ((previousAlarmTime > 0) && (nextAlarmTime > 0)) {
                //更新闹钟数据库
                boolean closeOnce = AlarmUtils.setAlarmCloseOnce(AlarmClockApplication.getInstance(), schedule.getAlarmId(), nextAlarmTime, previousAlarmTime);
                Log.i(TAG, "setupAlarmInstance Next close has been set closeOnce = " + closeOnce);
            }
            if ((schedule.getAlarm() != null) && schedule.getAlarm().isRepeatAlarm()) {
                //更新闹钟列表
                Log.d(TAG, "send dismiss ACTION_REPEAT_ALARM_DISMISS ");
                Intent intent = new Intent(AlarmClockFragment.ACTION_REPEAT_ALARM_DISMISS);
                intent.putExtra(AlarmClockFragment.CURRENT_REPEAT_ALARM, schedule.getAlarm());
                context.sendBroadcast(intent);
            }
        }
    }

    /**
     * This will not change the state of schedule, but stopAlarm and cancelSchedule.
     *
     * @param context  application context
     * @param schedule to unregister
     */
    public static void unregisterInstance(Context context, AlarmSchedule schedule) {
        if ((context == null) || (schedule == null)) {
            Log.e(TAG, "unregisterInstance context is null or schedule is null");
            return;
        }
        final long scheduleId = schedule.getId();
        // Stop alarm if this schedule is firing it
        //此时需要将currentScheduleId更新为当前id，否则在删除时无法对应id
        sCurrentNoticesScheduleId = scheduleId;
        AlarmSchedule currentAlarmSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
        if (currentAlarmSchedule != null) {
            Log.i(TAG, "unregisterInstance currentAlarmSchedule " + currentAlarmSchedule.getId() + "  : scheduleId = " + scheduleId);
            if (currentAlarmSchedule.getId() == scheduleId) {
                Log.i(TAG, "unregisterInstance alarmSchedule is ring ");
                AlarmService.stopAlarm(context, scheduleId, true/*isDismissedAlarm*/);
            } else {
                Log.i(TAG, "unregisterInstance this currentAlarmSchedule is not  equals scheduleId , so  no need to stop AlarmService");
            }
        } else {
            Log.i(TAG, "unregisterInstance alarmSchedule is null,no need to stop AlarmService");
        }
        cancelRtcAlarm(context, scheduleId, schedule.getTime(), schedule.getAlarmState());
        ScheduleUtils.cancelNotification2(context, scheduleId, true);
    }

    public static Notification sendSnoozeNotification(Context context, AlarmSchedule alarmSchedule, long snoozeTime, boolean isForeGroundNotification) {
        Log.d(TAG, "sendSnoozeNotification AlarmSchedule: " + alarmSchedule + " snoozeTime:" + snoozeTime);
        if (context == null) {
            Log.d(TAG, "sendSnoozeNotification context is null");
            return null;
        }
        final long scheduleId = alarmSchedule.getId();
        // Get the display time for the snooze and update the notification.
        final Calendar c = Calendar.getInstance();
        c.setTimeInMillis(snoozeTime);
        // Notify the user that the alarm has been snoozed.
        final Intent cancelSnooze = new Intent(context, AlarmReceiver.class);
        cancelSnooze.setAction(ClockConstant.CANCEL_SNOOZE);
        final Bundle bundle = new Bundle();
        bundle.putParcelable(ClockConstant.ALARM_INTENT_EXTRA, alarmSchedule);
        cancelSnooze.putExtras(bundle);
        // Notify the user slide the screen notification.
        final Intent entryApkFromScreen = new Intent(context, AlarmClock.class);
        entryApkFromScreen.setAction(ClockConstant.ENTER_APK_FROM_SCREEN);
        entryApkFromScreen.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
        entryApkFromScreen.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        final PendingIntent broadcast = PendingIntent.getBroadcast(context, (int) scheduleId,
                cancelSnooze, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        final PendingIntent broadcastEnterApkFromScreen = PendingIntent.getActivity(context, 0,
                entryApkFromScreen, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        String label = alarmSchedule.getAlarmLabel();
        if ((alarmSchedule.getAlarm() != null) && (alarmSchedule.getAlarm().getmDefaultAlarm() == 1)) {
            label = context.getResources().getString(R.string.wake_up_alarm);
        }
        if (TextUtils.isEmpty(label)) {
            label = context.getString(R.string.default_label);
        }
        NotificationManager nm = (NotificationManager) context.getSystemService(
                NOTIFICATION_SERVICE);
        Builder b = null;
        Notification notification = null;
        if (Utils.isAboveO()) {
            b = createBuilder(label, context, broadcast, alarmSchedule, nm);
            notification = b.build();
            notification.flags = Notification.FLAG_NO_CLEAR;
            b.setContentIntent(broadcastEnterApkFromScreen);
            b.setOngoing(true);
            b.setContentText(
                    context.getString(R.string.alarm_notify_snooze_text, Formatter.formatTime(context, c)));
            Log.d(TAG, "notification: above o:" + scheduleId);
        } else {
            notification = createBuilder(context, label, broadcast, c, broadcastEnterApkFromScreen);
            Log.d(TAG, "notification: below o:" + scheduleId);
        }
        Log.d(TAG, "nm.notify:" + nm + ",scheduleId:" + scheduleId);
        if (VersionUtils.isOSVersion1501()) {
            String serviceID = AlarmSnoozeSeedingHelper.queryAlarmFluidServiceId(context);
            notification.extras.putString(NotificationUtils.FLUID_SERVICE_ID, serviceID);
        }
        if (nm != null) {
            if (!isForeGroundNotification) {
                Log.d(TAG, "sendSnoozeNotification: " + scheduleId);
                nm.notify((int) scheduleId, notification);
            } else {
                Log.d(TAG, "sendSnoozeService ForeGroundNotification ");
                return notification;
            }
        }
        return null;
    }

    public static void sendSnoozeNotification(Context context, AlarmSchedule alarmSchedule,
                                              long snoozeTime) {
        sendSnoozeNotification(context, alarmSchedule, snoozeTime, false);
    }

    private static Notification createBuilder(
            Context context, String label, PendingIntent broadcast,
            Calendar c, PendingIntent broadcastEnterApkFromScreen) {
        Builder b = new Builder(context);
        String labelTemp = context.getString(R.string.alarm_notify_snooze_label, label);
        b.setContentTitle(labelTemp);
        b.setSmallIcon(R.drawable.stat_notify_alarm);
        b.setLargeIcon(BitmapFactory.decodeResource(context.getResources(),
                R.drawable.stat_notify_alarm_large));

        final RemoteViews contentView = new RemoteViews(context.getPackageName(),
                R.layout.oplus_notification_template_base);

        RemoteViews childView = new RemoteViews(context.getPackageName(),
                R.layout.oplus_notification_template_base);

        childView.setTextViewText(R.id.title, labelTemp);
        childView.setTextViewText(R.id.text,
                context.getString(R.string.alarm_notify_snooze_text, Formatter.formatTime(context, c)));
        childView.setImageViewBitmap(R.id.icon, BitmapUtils.drawableToBitmap(context, BitmapUtils.getAppIconBitmap(context)));
        childView.setOnClickPendingIntent(R.id.cancel_snooze, broadcast);

        // Notify the user click the notification.
        final Intent entryApk = new Intent(context, AlarmClock.class);
        entryApk.setAction(ClockConstant.ENTER_APK);
        entryApk.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
        entryApk.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        final PendingIntent broadcastEnterApk = PendingIntent.getActivity(context, 0, entryApk,
                Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        childView.setOnClickPendingIntent(R.id.status_bar_latest_event_content, broadcastEnterApk);

        final View viewNo = LayoutInflater.from(context)
                .inflate(R.layout.oplus_notification_template_base, null);
        contentView.addView(viewNo.getId(), childView);
        contentView.setOnClickPendingIntent(viewNo.getId(), null);

        Notification notification = b.build();
        notification.contentView = contentView;
        notification.flags = Notification.FLAG_NO_CLEAR;
        b.setContentIntent(broadcastEnterApkFromScreen);
        b.setSound(null);
        b.setOngoing(true);
        b.setContentText(
                context.getString(R.string.alarm_notify_snooze_text, Formatter.formatTime(context, c)));
        return notification;
    }

    /**
     * 错过闹钟通知
     *
     * @param label
     * @param context
     * @param alarmSchedule
     * @param nm
     * @return
     */
    private static Builder createMissAlarmBuilder(
            String label, Context context,
            AlarmSchedule alarmSchedule, NotificationManager nm) {
        Builder b = new Builder(context, CHANNEL_ID);
        b.setContentTitle(label);
        b.setWhen(ScheduleUtils.getAlarmTimeInMills(alarmSchedule));
        b.setShowWhen(false);
        b.setAutoCancel(true);
        b.setSmallIcon(R.drawable.ic_launcher_clock);
        b.setSound(null);
        CharSequence name = context.getString(R.string.clock_notification_label);
        NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name,
                NotificationManager.IMPORTANCE_DEFAULT);
        channel.setSound(null, null);
        channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        if (nm != null) {
            nm.createNotificationChannel(channel);
        }
        return b;

    }

    private static Builder createBuilder(
            String label, Context context, PendingIntent broadcast,
            AlarmSchedule alarmSchedule, NotificationManager nm) {
        Builder b = new Builder(context, CHANNEL_ID);
        b.setContentTitle(label);
        b.setWhen(ScheduleUtils.getAlarmTimeInMills(alarmSchedule));
        b.setShowWhen(false);
        b.setAutoCancel(false);
        b.setSmallIcon(R.drawable.ic_launcher_clock);
        b.setSound(null);

        Notification.Action.Builder actionBuilderCancelSnooze = new Notification.Action.Builder(
                R.drawable.ic_launcher_clock, context.getString(R.string.cancel_snooze), broadcast);
        b.addAction(actionBuilderCancelSnooze.build());

        CharSequence name = context.getString(R.string.clock_notification_label);
        NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name,
                NotificationManager.IMPORTANCE_DEFAULT);
        channel.setSound(null, null);
        channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        if (nm != null) {
            nm.createNotificationChannel(channel);
        }
        return b;

    }

    /**
     * Creates an intent that can be used to set an AlarmManager alarm to set the next alarm
     * indicators.
     */
    public static Intent createIndicatorIntent(Context context) {
        return new Intent(context, AlarmStateManager.class).setAction(INDICATOR_ACTION);
    }


    /**
     * Utility method to create a proper change state intent.
     *
     * @param context    application context
     * @param tag        used to make intent differ from other state change intents.
     * @param scheduleId to change state to
     * @param state      to change to.
     * @return intent that can be used to change an alarm schedule state
     */
    private static Intent createStateChangeIntent(Context context, String tag,
                                                  long scheduleId, Integer state) {
        if (context == null) {
            Log.d(TAG, "createStateChangeIntent context is null");
            return null;
        }
        final Intent intent = new Intent(context, AlarmStateManager.class);
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        int globalId = getGlobalIntentId(context);
        intent.setData(uri);
        Log.i(TAG, "createStateChangeIntent: alarmSchedule.id = " + scheduleId + ",globalId=" + globalId);
        intent.setAction(CHANGE_STATE_ACTION);
        intent.addCategory(tag);
        intent.putExtra(ALARM_GLOBAL_ID_EXTRA, globalId);
        if (state != null) {
            intent.putExtra(ALARM_STATE_EXTRA, state.intValue());
        }
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        return intent;
    }

    public static synchronized void showNextAlarmNotices(Context context) {
        showNextAlarmNotices(context, null);
    }

    public static synchronized void showNextAlarmNotices(Context context, Alarm alarm) {
        if (context != null) {
            boolean isShowNextAlarm = AlarmUtils.isOpenNextAlarmNotices(context);
            Log.d(TAG, "showNextAlarmNotices is show next alarm : " + isShowNextAlarm);

            if (isShowNextAlarm && (CurrentAlarmScheduleHolder.getAlarmSchedule() == null)) {
                AlarmSchedule alarmSchedule = getNextAlarmTime(context);
                Log.d(TAG, "showNextAlarmNotices  show next alarmSchedule : " + alarmSchedule + ",sCurrentNoticesScheduleId:" + sCurrentNoticesScheduleId);
                if ((alarmSchedule != null) && (alarmSchedule.getTime() > 0)) {
                    AlarmSchedule schedule = ScheduleUtils.getSchedule(context, sCurrentNoticesScheduleId);
                    Log.d(TAG, "showNextAlarmNotices current alarmSchedule : " + schedule);
                    if (alarm != null && alarm.getmGarbSwitch() == 1) {
                        //编辑闹钟查询的alarm可能是未修改的数据，直接使用传递的alarm判断
                        Log.d(TAG, "showNextAlarmNotices garb alarm");
                        cancelNextAlarmNotices(context);
                        return;
                    }
                    if (schedule != null && schedule.getAlarm() != null && schedule.getAlarm().getmGarbSwitch() == 1) {
                        //秒抢闹钟不发送即将响铃通知
                        Log.d(TAG, "showNextAlarmNotices garb alarm");
                        cancelNextAlarmNotices(context);
                        return;
                    }
                    if ((schedule == null)
                            || ((alarmSchedule.getId() != schedule.getId()) && (schedule.getTime() != alarmSchedule.getTime()))) {
                        Log.d(TAG, "showNextAlarmNotices alarmSchedule.getId() : " + alarmSchedule.getId());
                        cancelNextAlarmNotices(context);
                        setNextAlarmNotices(context, alarmSchedule, alarmSchedule.getTime() - NEXT_ALARM_NOTICES);
                    }
                } else {
                    Log.e(TAG, "showNextAlarmNotices  alarmSchedule is null or alarmSchedule.getTime() <= 0 ");
                }
            } else {
                cancelNextAlarmNotices(context);
            }
        }
    }


    public static AlarmSchedule getNextAlarmTime(Context context, long alarmId) {
        Log.e(TAG, "getNextAlarmTime alarmId =" + alarmId);
        List<AlarmSchedule> scheduleList = ScheduleUtils.getNextSchedules(context, alarmId);
        if ((scheduleList != null) && (scheduleList.size() > 0)) {
            Log.d(TAG, "getNextAlarmTime scheduleList size = " + scheduleList.size());
            return scheduleList.get(0);
        } else {
            Log.d(TAG, "getNextAlarmTime scheduleList is null ");
        }
        return null;
    }

    /**
     * 查询下次响铃时间，排除秒抢闹钟
     *
     * @param context
     * @return
     */
    public static AlarmSchedule getNextAlarmTime(Context context) {
        Log.e(TAG, "getNextAlarmTime alarmId =");
        List<AlarmSchedule> scheduleList = ScheduleUtils.getNextSchedules(context);
        if ((scheduleList != null) && (scheduleList.size() > 0)) {
            Log.d(TAG, "getNextAlarmTime scheduleList size = " + scheduleList.size());
            for (int i = 0; i < scheduleList.size(); i++) {
                AlarmSchedule sch = scheduleList.get(i);
                if (sch.getAlarm() != null && sch.getAlarm().getmGarbSwitch() != 1) {
                    return scheduleList.get(i);
                }
            }
        } else {
            Log.d(TAG, "getNextAlarmTime scheduleList is null ");
        }
        return null;
    }


    public static void cancelNextAlarmNotices(Context context) {
        Log.d(TAG, "cancelNextAlarmNotices sCurrentNoticesScheduleId = " + sCurrentNoticesScheduleId);
        if (context != null) {
            cancelNextAlarmNoticesById(context, PrefUtils.getNextNoticeScheduleId(context));
            PrefUtils.setNextNoticeScheduleId(context, -1);
        }
        sCurrentNoticesScheduleId = -1;
    }

    private static void cancelNextAlarmNoticesById(Context context, long scheduleId) {
        Log.d(TAG, "cancelNextAlarmNotices scheduleId : " + scheduleId);
        Intent intent = createNextAlarmNotice(context, scheduleId);

        final PendingIntent pendingIntent = PendingIntent.getBroadcast(context,
                toRequestCode(scheduleId), intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_NO_CREATE));
        Log.d(TAG, "scheduleId :" + scheduleId + "  pendingIntent = " + pendingIntent);
        final AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if ((am != null) && (pendingIntent != null)) {
            am.cancel(pendingIntent);
            AppPlatformUtils.cancelPendingIntent(pendingIntent);
        }
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            notificationManager.cancel(AHEAD_REMIND_NOTIFICATION_ID);
        }
    }

    private static void setNextAlarmNotices(Context context, AlarmSchedule alarmSchedule, long mills) {
        long scheduleId = alarmSchedule.getId();
        Log.d(TAG, "setNextAlarmNotices start scheduleId = " + scheduleId + "  mills = " + mills);
        Intent intent = createNextAlarmNotice(context, alarmSchedule.getId());
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);

        final PendingIntent pendingIntent = PendingIntent.getBroadcast(context,
                toRequestCode(scheduleId), intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

        final AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if ((am != null) && (mills > 0)) {
            long currentTime = Calendar.getInstance().getTimeInMillis();
            Log.d(TAG, "setNextAlarmNotices end currentTime = " + currentTime + "   mills :" + mills + ",sCurrentNoticesScheduleId:" + sCurrentNoticesScheduleId);
            if (mills <= currentTime) {
                sCurrentNoticesScheduleId = scheduleId;
                Log.d(TAG, "setNextAlarmNotices mills <= currentTime = " + (currentTime - mills));
                showNextAlarmNotices(context, scheduleId);
            } else {
                Log.d(TAG, "setNextAlarmNotices mills > currentTime = " + (currentTime - mills));
                PrefUtils.setNextNoticeScheduleId(context, scheduleId);
                am.setExact(AlarmManager.RTC_WAKEUP, mills, pendingIntent);
            }
        }
        Log.d(TAG, "setNextAlarmNotices end scheduleId = " + scheduleId);
    }

    private static Intent createNextAlarmNotice(Context context, long scheduleId) {
        final Intent intent = new Intent(context, AlarmStateManager.class);
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        intent.setData(uri);
        Log.d(TAG, "createStateChangeIntent: alarmSchedule.id = " + scheduleId);
        intent.addCategory(ALARM_NEXT_NOTICES_TAG);
        intent.setAction(ACTION_NEXT_ALARM_NOTICES);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        return intent;
    }


    private static void showNextAlarmNotices(Context context, long scheduleId) {
        Log.d(TAG, "showNextAlarmNotices scheduleId: " + scheduleId);

        NotificationManager notificationManager = (NotificationManager) context.getSystemService(NOTIFICATION_SERVICE);

        if ((notificationManager != null)) {
            final AlarmSchedule alarmSchedule = ScheduleUtils.getSchedule(context, scheduleId);
            Log.d(TAG, "showNextAlarmNotices alarmSchedule: " + alarmSchedule);
            if (alarmSchedule == null) {
                return;
            }
            if (alarmSchedule.getAlarm() != null && alarmSchedule.getAlarm().getmGarbSwitch() == 1) {
                //秒抢闹钟不发送即将响铃通知
                return;
            }
            final Intent cancelSnooze = new Intent(context, AlarmReceiver.class);
            cancelSnooze.setAction(ClockConstant.CANCEL_SNOOZE);
            cancelSnooze.setAction(ClockConstant.CANCEL_SNOOZE_OLD);
            final Bundle bundle = new Bundle();
            bundle.putParcelable(ClockConstant.ALARM_INTENT_EXTRA, alarmSchedule);
            bundle.putBoolean(ClockConstant.ALARM_INTENT_CANCEL_NEXT, true);
            cancelSnooze.putExtras(bundle);

            final Intent entryApkFromScreen = new Intent(context, AlarmClock.class);
            entryApkFromScreen.setAction(ClockConstant.ENTER_APK_FROM_SCREEN);
            entryApkFromScreen.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
            entryApkFromScreen.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                    | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            Alarm alarm = alarmSchedule.getAlarm();
            if (alarm != null) {
                entryApkFromScreen.putExtra(AlarmClock.ALARM_ID, alarm.getId());
            }
            entryApkFromScreen.putExtra(AlarmClock.IS_NEED_CHANGE_POSITION, true);
            final Bundle entryApkBundle = new Bundle();
            entryApkBundle.putBoolean(ClockConstant.ALARM_INTENT_CANCEL_NEXT, true);
            entryApkFromScreen.putExtras(entryApkBundle);

            final Intent deleteIntent = new Intent(context, AlarmReceiver.class);
            deleteIntent.setAction(ClockConstant.DELETE_NEXT_ALARM_NOTICE);
            deleteIntent.putExtras(entryApkBundle);

            final PendingIntent broadcast = PendingIntent.getBroadcast(context, (int) scheduleId,
                    cancelSnooze, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            final PendingIntent broadcastEnterApkFromScreen = PendingIntent.getActivity(context, 0,
                    entryApkFromScreen, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            final PendingIntent broadcastDelete = PendingIntent.getBroadcast(context, 0,
                    deleteIntent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

            String label = alarmSchedule.getAlarmLabel();
            if ((alarmSchedule.getAlarm() != null) && (alarmSchedule.getAlarm().getmDefaultAlarm() == 1)) {
                label = context.getResources().getString(R.string.wake_up_alarm);
            }
            if (TextUtils.isEmpty(label)) {
                label = context.getString(R.string.default_label);
            }

            Builder b = null;
            Notification notification = null;
            if (Utils.isAboveO()) {
                b = new Builder(context, CHANNEL_ID_NEXT_ALARM);
                b.setContentTitle(label);
                b.setWhen(ScheduleUtils.getAlarmTimeInMills(alarmSchedule));
                b.setShowWhen(false);
                b.setDeleteIntent(broadcastDelete);
                b.setAutoCancel(false);
                b.setSmallIcon(R.drawable.ic_launcher_clock);
                b.setSound(null);

                boolean isRepeat = false;
                if (alarm != null) {
                    Log.e(TAG, " getRepeatSet = " + alarm.getRepeatSet() + "  getHolidaySwitch = " + alarm.getHolidaySwitch()
                            + "    getWorkdaySwitch = " + alarm.getWorkdaySwitch());
                    isRepeat = alarm.isRepeatAlarm();
                    Log.i(TAG, "showNextAlarmNotices  isRepeat = " + isRepeat);
                }

                Notification.Action.Builder actionBuilderCancelSnooze = new Notification.Action.Builder(
                        R.drawable.ic_launcher_clock, context.getString(isRepeat ? R.string.close_once : R.string.close), broadcast);
                b.addAction(actionBuilderCancelSnooze.build());

                CharSequence name = context.getString(R.string.clock_notification_ahead_label);
                NotificationChannel channel = new NotificationChannel(CHANNEL_ID_NEXT_ALARM, name, NotificationManager.IMPORTANCE_DEFAULT);
                channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
                channel.setSound(null, null);
                notificationManager.createNotificationChannel(channel);
                notification = b.build();

                Log.d(TAG, "notification: above o:" + scheduleId);
            } else {
                b = new Builder(context);
                label = context.getString(R.string.alarm_notify_snooze_label, label);
                b.setContentTitle(label);
                b.setSmallIcon(R.drawable.stat_notify_alarm);
                b.setLargeIcon(BitmapFactory.decodeResource(context.getResources(),
                        R.drawable.stat_notify_alarm_large));
                final RemoteViews contentView = new RemoteViews(context.getPackageName(),
                        R.layout.oplus_notification_template_base);

                RemoteViews childView = new RemoteViews(context.getPackageName(),
                        R.layout.oplus_notification_template_base);

                childView.setTextViewText(R.id.title, label);
                childView.setTextViewText(R.id.text,
                        context.getString(R.string.next_alarm_will_ring, AlarmUtils.getTimeStringShort(context, alarmSchedule.getTime())));
                childView.setImageViewBitmap(R.id.icon, BitmapUtils.drawableToBitmap(context, BitmapUtils.getAppIconBitmap(context)));
                childView.setOnClickPendingIntent(R.id.cancel_snooze, broadcast);

                // Notify the user click the notification.
                final Intent entryApk = new Intent(context, AlarmClock.class);
                entryApk.setAction(ClockConstant.ENTER_APK);
                entryApk.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
                entryApk.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                        | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                final PendingIntent broadcastEnterApk = PendingIntent.getActivity(context, 0, entryApk,
                        Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
                childView.setOnClickPendingIntent(R.id.status_bar_latest_event_content, broadcastEnterApk);

                final View viewNo = LayoutInflater.from(context)
                        .inflate(R.layout.oplus_notification_template_base, null);
                contentView.addView(viewNo.getId(), childView);
                contentView.setOnClickPendingIntent(viewNo.getId(), null);

                notification = b.build();
                notification.contentView = contentView;

                Log.d(TAG, "notification: below o:" + scheduleId);
            }
            b.setContentIntent(broadcastEnterApkFromScreen);
            b.setOngoing(false);
            b.setContentText(context.getString(R.string.next_alarm_will_ring, AlarmUtils.getTimeStringShort(context, alarmSchedule.getTime())));

            try {
                Log.d(TAG, "nm.notify:" + notificationManager + ",scheduleId:" + scheduleId);
                notificationManager.notify(AHEAD_REMIND_NOTIFICATION_ID, notification);
                /**
                 * 不允许锁屏通知且处于锁屏情况下，则return，不亮屏
                 */
                NotificationChannel channel = notificationManager.getNotificationChannel(CHANNEL_ID_NEXT_ALARM);
                KeyguardManager km = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
                boolean isKeyguardLock = ((km != null) && (km.isKeyguardLocked()));
                Log.d(TAG, "isKeyguardLock:" + isKeyguardLock);
                if (!canShowAppLockScreenNotification(channel) && isKeyguardLock) {
                    return;
                }
                AlarmAlertWakeLock.acquireCpuWakeLockFull(context);
            } catch (Exception e) {
                Log.e(TAG, "showNextAlarmNotices notificationManager.notify error:" + e);
            }
        } else {
            Log.i(TAG, "notificationManager  is null , no need to send notification");
        }
    }

    /**
     * 判断是否开启了 锁屏通知
     *
     * @param
     * @return
     */
    public static boolean canShowAppLockScreenNotification(NotificationChannel channel) {
        if (channel == null) {
            return false;
        }
        PackageManager packageManager = AlarmClockApplication.getInstance().getPackageManager();
        try {
            PackageInfo packageInfo = packageManager.getPackageInfo(AlarmClockApplication.getInstance().getPackageName(), 0);
            int uid = packageInfo.applicationInfo.uid;
            String pkg = packageInfo.applicationInfo.packageName;
            if (canShowAppLockScreen(pkg, uid) && canShowChannelLockScreen(pkg, uid, channel)) {
                return true;
            } else {
                return false;
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "PackageManager.NameNotFoundException:" + e.getMessage());
        }
        return false;
    }

    public static boolean canShowAppLockScreen(String pkg, int uid) {
        int visibility = Notification.VISIBILITY_PUBLIC;
        try {
            visibility = new OplusNotificationManager().getAppVisibility(pkg, uid);
        } catch (NoSuchFieldError e1) {
            Log.e(TAG, "OplusNotificationManager NoSuchFieldError e=:" + e1.getMessage());
        } catch (NoClassDefFoundError e2) {
            Log.e(TAG, "OplusNotificationManager NoClassDefFoundError e=:" + e2.getMessage());
        }
        return visibility != Notification.VISIBILITY_SECRET;
    }

    public static boolean canShowChannelLockScreen(String pkg, int uid, NotificationChannel channel) {
        if (channel != null) {
            int visibility = channel.getLockscreenVisibility();
            boolean isChecked = visibility != Notification.VISIBILITY_SECRET;
            return isChecked;
        }
        return false;
    }

    @Override
    public void onReceive(final Context context, final Intent intent) {
        Log.w(TAG, "onReceive intent: " + intent.getAction());
        if (INDICATOR_ACTION.equals(intent.getAction())) {
            return;
        }
        // goAsync() keep the broadcast active after returning from that function
        final PendingResult result = goAsync();
        AlarmAlertWakeLock.acquirePartialWakeLock(context);

        AsyncHandler.post(() -> {
            //因BroadcastReceiver静态注册,故context（ReceiverRestrictedContext）是一个受限制的context,可能会造成内存泄漏，故此处改为context.getApplicationContext()
            handleIntent(context.getApplicationContext(), intent);
            result.finish();
        });
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    private void handleIntent(Context context, Intent intent) {
        final String action = intent.getAction();
        if (CHANGE_STATE_ACTION.equals(action)) {
            Log.w(TAG, "------ClockTag------\nhandleIntent: " + intent);
            final Uri uri = intent.getData();
            final long scheduleId = ContentUris.parseId(uri);
            final AlarmSchedule alarmSchedule = ScheduleUtils.getSchedule(context, scheduleId);
            final int globalId = getGlobalIntentId(context);
            final int intentId = intent.getIntExtra(ALARM_GLOBAL_ID_EXTRA, -1);
            final int alarmState = intent.getIntExtra(ALARM_STATE_EXTRA, -1);
            if (alarmState == ClockContract.Schedule.GARB_ALARM_STATE) {
                //秒抢时间
                long alarmId = scheduleId - INT_MAX_VALUE;
                Log.i(TAG, "garb alarm:" + alarmId + "  time up");
                AlarmUtils.stopAlarm(context, alarmId);
                GarbAlarmSeedlingHelper.timerUpGarbAlarmCard(context, alarmId);
                return;
            }
            if (alarmSchedule == null) {
                // Not a big deal, but it shouldn't happen
                Log.e(TAG, "handleIntent can not change state for unknown instance: " + uri);
                AlarmAlertWakeLock.releaseCpuLockCpu(TAG + "handleIntent == null");
                AlarmAlertWakeLock.releasePartialWakelock();
                return;
            }

            //this Broadcast only to close first pager dialog of close once dialog
            if (alarmSchedule.getAlarm().isRepeatAlarm()) {
                Intent intentCloseOnceDialog = new Intent(AlarmClockFragment.ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG);
                intentCloseOnceDialog.putExtra(AlarmClockFragment.EXTRA_REPEAT_ALARM_CLOSE_ONCE_DIALOG, alarmSchedule.getAlarmId());
                context.sendBroadcast(intentCloseOnceDialog);
            }

            Log.i(TAG, "handleIntent alarmSchedule =  " + alarmSchedule);

            AlarmRingStatisticUtils.statisticsAlarmRtc(context, AlarmRingStatisticUtils.EVENT_RECEIVED_RTC_ALARM,
                    (int) scheduleId, -1, alarmState);
            Log.i(TAG, "handleIntent alarmState =  " + alarmState + "IntentId: " + intentId + " globalId = "
                    + globalId);
            if (intentId != globalId) {
                //异常情况导致 globalId和intentId未更新导致闹钟不响
                Alarm alarm = AlarmUtils.getAlarm(context, alarmSchedule.getAlarmId());
                boolean alarmHour = false;
                boolean alarmMinute = false;
                if (alarm != null) {
                    boolean garbAlarm = alarm.getmGarbSwitch() == 1;
                    alarmHour = alarm.getHour() != alarmSchedule.getHour() && !garbAlarm;
                    alarmMinute = alarm.getMinutes() != alarmSchedule.getAlarmMinute() && !garbAlarm;
                }
                //判断当前闹钟时间不一致，或者开关已关闭 不响铃，否则继续响铃
                if ((alarm == null) || (!alarm.isEnabled()) || alarmHour || alarmMinute) {
                    // Allows dismiss/snooze requests to go through
                    Log.e(TAG, "handleIntent： alarm error");
                    if (!intent.hasCategory(ALARM_DISMISS_TAG)
                            && !intent.hasCategory(ALARM_SNOOZE_TAG)) {
                        Log.e(TAG, "Ignoring old Intent");
                        AlarmAlertWakeLock.releaseCpuLockCpu(TAG + "intentId != globalId");
                        return;
                    }
                } else {
                    Log.e(TAG, "handleIntent： continue ringing ");
                }
            }

            /*Automatically stop reporting when the alarm rings*/
            if ((alarmState == ClockContract.Schedule.SNOOZE_STATE) || (alarmState == ClockContract.Schedule.DISMISSED_STATE)) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(context,
                        alarmSchedule,
                        AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP,
                        AlarmRingStatisticUtils.AlarmAutoStopExtraReason.TIME_IS_UP);
                if (alarmState == ClockContract.Schedule.DISMISSED_STATE) {
                    /*错过闹钟通知,通知消除规则未定义，暂时删除*/
                    Log.d(TAG, "handleIntent: sendMissAlarmNotification");
                    sendMissAlarmNotification(context, alarmSchedule);
                    AlarmRingOperateUtils.closeAlarm(alarmSchedule.getAlarm(), AlarmRingOperateUtils.CLOSE_ALARM_OUT_DURATION);
                } else {
                    AlarmRingOperateUtils.snoozeAlarm(alarmSchedule.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_OUT_DURATION);
                }
            }
            if (alarmState >= 0) {
                if (alarmSchedule.getAlarm().getmGarbSwitch() == 1 && alarmState == ClockContract.Schedule.SNOOZE_STATE) {
                    Log.d(TAG, "garb alarm snooze");
                    dispatchAlarmAction(context, alarmSchedule, ClockContract.Schedule.DISMISSED_STATE);
                } else {
                    dispatchAlarmAction(context, alarmSchedule, alarmState);
                }
            } else {
                Log.d(TAG, "registerInstance a new alarm ");
                registerInstance(context, alarmSchedule);
            }
        } else if (ACTION_NEXT_ALARM_NOTICES.equals(action)) {
            Uri uri = intent.getData();
            if ((uri != null)) {
                long scheduleId = ContentUris.parseId(uri);
                Log.d(TAG, "handleIntent action ACTION_NEXT_ALARM_NOTICES scheduleId : " + scheduleId + "   sCurrentNoticesScheduleId == " + sCurrentNoticesScheduleId);
//                cancelNextAlarmNoticesById(context, scheduleId);
                sCurrentNoticesScheduleId = scheduleId;
                showNextAlarmNotices(context, scheduleId);
            }
            AlarmAlertWakeLock.releaseCpuLockCpu(TAG + " ACTION_NEXT_ALARM_NOTICES");
        } else if (AlarmNotify.ACTION_NEXT_WORKDAY_NOTICES.equals(action)) {
            long currentTimeInMillis = Calendar.getInstance().getTimeInMillis();
            long sendTimeInMillis = intent.getLongExtra(AlarmNotify.ACTION_NEXT_WORKDAY_NOTICES, -1);
            if (sendTimeInMillis != -1) {
                AlarmNotify alarmNotify = new AlarmNotify();
                if (currentTimeInMillis < sendTimeInMillis + ONE_MIN) {
                    alarmNotify.showNextWorkdayNotices(context, sendTimeInMillis);
                }
                alarmNotify.updateNextWorkdayNotice(context, false);
            }
            AlarmAlertWakeLock.releaseCpuLockCpu(TAG + " ACTION_NEXT_WORKDAY_NOTICES");
        }
    }

    /**
     * 错过的闹钟通知
     *
     * @param context
     * @param alarmSchedule
     */
    public static void sendMissAlarmNotification(Context context, AlarmSchedule alarmSchedule) {
        if (context == null) {
            Log.d(TAG, "sendMissAlarmNotification context is null");
            return;
        }
        final long scheduleId = alarmSchedule.getId();
        final long alarmId = alarmSchedule.getAlarmId();
        // Get the display time for the snooze and update the notification. Notify the user that the alarm has been snoozed.
        final Intent cancelSnooze = new Intent(context, AlarmReceiver.class);
        cancelSnooze.setAction(ClockConstant.CANCEL_SNOOZE);
        final Bundle bundle = new Bundle();
        bundle.putParcelable(ClockConstant.ALARM_INTENT_EXTRA, alarmSchedule);
        cancelSnooze.putExtras(bundle);
        int missAlarmNotificationID = (int) (MISS_ALARM_NOTIFICATION_ID - scheduleId);
        final Intent entryApkFromScreen = new Intent(context, AlarmClock.class);
        entryApkFromScreen.setAction(ClockConstant.ENTER_APK_FROM_SCREEN);
        entryApkFromScreen.setData(Uri.parse(ClockConstant.ENTER_APK_FROM_SCREEN + missAlarmNotificationID));
        entryApkFromScreen.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
        entryApkFromScreen.putExtra(AlarmClock.ACTION_SEND_MISS_NOTIFICATION_ID, missAlarmNotificationID);
        entryApkFromScreen.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        final PendingIntent broadcastEnterApkFromScreen = PendingIntent.getActivity(context, 0,
                entryApkFromScreen, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        String title = context.getString(R.string.miss_alarm_title);
        String label = alarmSchedule.getAlarm().getLabel();
        if ((alarmSchedule.getAlarm().getmDefaultAlarm() == 1)) {
            label = context.getResources().getString(R.string.wake_up_alarm);
        }
        if (TextUtils.isEmpty(label)) {
            label = context.getString(R.string.default_label);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, alarmSchedule.getAlarm().getHour());
        calendar.set(Calendar.MINUTE, alarmSchedule.getAlarm().getMinutes());
        //日期
        String date = Utils.getLocaleDateMDFormat(context, calendar.getTimeInMillis());
        //时间
        String time = Utils.getLocaleTimeMDFormat(context, calendar.getTimeInMillis(), DateUtils.FORMAT_SHOW_TIME);
        String content = context.getString(R.string.miss_alarm_content, date, time, label);
        NotificationManager nm = (NotificationManager) context.getSystemService(
                NOTIFICATION_SERVICE);
        Builder b = createMissAlarmBuilder(title, context, alarmSchedule, nm);
        b.setContentIntent(broadcastEnterApkFromScreen);
        b.setOngoing(false);
        b.setContentText(content);
        final Intent IntentCancel = new Intent(context, AlarmReceiver.class);
        IntentCancel.setAction(ClockConstant.DELETE_ONE_NOTIFICATION);
        IntentCancel.setData(Uri.parse(ClockConstant.DELETE_ONE_NOTIFICATION + missAlarmNotificationID));
        IntentCancel.putExtra(AlarmClock.ACTION_SEND_MISS_NOTIFICATION_ID, missAlarmNotificationID);
        final PendingIntent pendingIntentCancel = PendingIntent.getBroadcast(context, 0,
                IntentCancel, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        b.setDeleteIntent(pendingIntentCancel);
        Notification notification = b.build();
        if (nm != null) {
            if (getNotificationChannelPermission(nm, CHANNEL_ID)) {
                nm.notify(missAlarmNotificationID, notification);
                PrefUtils.setMissNoticeScheduleId(context, missAlarmNotificationID, alarmId);
                Log.d(TAG, "sendMissAlarmNotification: " + missAlarmNotificationID);
            }
        }
    }

    /**
     * Utility method to set alarm schedule state via constants.
     *
     * @param context  application context
     * @param schedule to change state on
     * @param state    to change to
     */
    private static void setAlarmState(Context context, AlarmSchedule schedule, int state) {
        if (schedule == null) {
            Log.e(TAG, "Null alarm instance while setting state to " + state);
            return;
        }
        switch (state) {
            case ClockContract.Schedule.SILENT_STATE:
                setSilentState(context, schedule);
                break;
            case ClockContract.Schedule.FIRED_STATE:
                setFiredState(context, schedule);
                break;
            case ClockContract.Schedule.SNOOZE_STATE:
                setSnoozeState(context, schedule);
                break;
            case ClockContract.Schedule.DISMISSED_STATE:
                setDismissState(context, schedule);
                break;
            default:
                Log.e(TAG, "Trying to change to unknown alarm state: " + state);
        }
    }

    private static void dispatchAlarmAction(Context context, AlarmSchedule schedule, int state) {
        if (schedule == null) {
            Log.e(TAG, "Null alarm instance while get state ");
            return;
        }

        Log.d(TAG, "dispatchAlarmAction dispatch before state:" + state);
        final boolean supportConfineMode = Utils.supportConfineMode();
        if (supportConfineMode) {
            if (schedule.isSnoozeAvailble(schedule.getAlarm())) {
                state = ClockContract.Schedule.SNOOZE_STATE;
            } else {
                state = ClockContract.Schedule.DISMISSED_STATE;
            }
            AlarmAlertWakeLock.releaseCpuLockCpu(TAG + "dispatchAlarmAction");
        }
        Log.d(TAG, "dispatchAlarmAction dispatch after state:" + state);
        setAlarmState(context, schedule, state);

        /*if supportConfineMode,should resend next alarm notice notification when alarm state was changed*/
        if (supportConfineMode) {
            cancelNextAlarmNotices(context);
            showNextAlarmNotices(context);
        }

    }


    /**
     * Schedules state change callbacks within the AlarmManager.
     */
    public static void setupRtcAlarm(Context context, long timeInMillis, long schduleId, int newState) {
        Log.e(TAG, "powerOffAlarm:" + DeviceUtils.isPowerOffAlarm() + "  timeInMillis:" + Formatter.formatTime(timeInMillis));
        //QCOM:
        if (DeviceUtils.isQcomPlatform(context) && !DeviceUtils.isPowerOffAlarm()) {
            setupQcomRtcAlarm(context, schduleId, newState, timeInMillis);
            // qualcomm DON'T support RTC_WAKEUP anymore.Now we need to send a boradcast to do it.
            if (Utils.isAboveO()) {
                Calendar c = Calendar.getInstance();
                c.setTimeInMillis(timeInMillis);
                c.add(Calendar.MINUTE, 1);
                c.set(Calendar.SECOND, 0);
                c.set(Calendar.MILLISECOND, 0);
                long mills = c.getTimeInMillis();

                if (newState != ClockContract.Schedule.GARB_ALARM_STATE) {
                    // Check if there is another schedule need to set rtc.
                    AlarmSchedule schedule = ScheduleUtils.getEarlySchedule(context, mills);
                    Log.i(TAG, "setupRtcAlarm getEarlySchedule: " + Formatter.formatTime(mills) + ", AlarmSchedule: " + schedule);
                    if (schedule != null) {
                        sendQcomRtcAlarmBroadcast(context, schedule.getTime());
                    }
                }
            }
        }

        //MTK:
        if (DeviceUtils.isMtkPlatform(context) || DeviceUtils.isPowerOffAlarm()) {
            Log.i(TAG, "setupRtcAlarm hasSystemFeature --- [oplus.hw.manufacturer.mtk]");
            setupMtkRtcAlarm(context, schduleId, newState, timeInMillis);
            if (newState != ClockContract.Schedule.GARB_ALARM_STATE) {
                // Check if there is another schedule need to set rtc.
                /**
                 * Because mtk DON'T rearrange the most close rtc alarm.we need to do it ourself.
                 */
                final long triggerAtTime = timeInMillis + DELAY_TIME;
                AlarmSchedule schedule = ScheduleUtils.getEarlySchedule(context, triggerAtTime);
                if (schedule != null) {
                    Log.i(TAG, "setupRtcAlarm found an early schedule need to set RTC: " + schedule);
                    setupMtkRtcAlarm(context, schedule.getId(), ClockContract.Schedule.FIRED_STATE, schedule.getTime());
                }
            }
        }
        updateNextAlarm(context);
    }

    @SuppressLint("WrongConstant")
    private static void setupMtkRtcAlarm(Context context, long schduleId, int newState, long mills) {
        Log.d(TAG, "[MTK]Setup RTC: schduleId: " + schduleId + ", newState: " + newState
                + ", time: " + Formatter.formatTime(mills));
        Intent intent = createStateChangeIntent(context, ALARM_MANAGER_TAG, schduleId, newState);
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        final PendingIntent pendingIntent = PendingIntent.getBroadcast(context,
                toRequestCode(schduleId), intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
        final AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (am != null) {
            final long triggerAtTime = mills + DELAY_TIME;
            if (Utils.isAboveO()) {
                am.setExact(MTK_PRE_SCHEDULE_POWER_OFF_ALARM, triggerAtTime, pendingIntent);
            } else {
                am.setExact(MTK_EXTRA, triggerAtTime, pendingIntent);
            }
            Log.i(TAG, "setupMtkRtcAlarm at time:" + triggerAtTime);
            PrefUtils.putString(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    PrefUtils.SETUP_RTC_ALARM_TIME, Formatter.formatTime(triggerAtTime));
            AlarmRingStatisticUtils.statisticsAlarmRtc(context, AlarmRingStatisticUtils.EVENT_SET_UP_RTC_ALARM,
                    (int) schduleId, triggerAtTime, newState);
        }
    }

    @SuppressLint("WrongConstant")
    private static void setupQcomRtcAlarm(Context context, long schduleId, int newState, long mills) {
        Log.i(TAG, "setupQcomRtcAlarm hasSystemFeature --- [oplus.hw.manufacturer.qualcomm]");
        final Intent intent = createStateChangeIntent(context, ALARM_MANAGER_TAG, schduleId, newState);
        // Treat alarm state change as high priority, use foreground broadcasts
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        final PendingIntent pendingIntent = PendingIntent.getBroadcast(context,
                toRequestCode(schduleId), intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));

        final AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (am != null) {
            final long triggerAtTime = mills + DELAY_TIME;
            if (Utils.isAboveO()) {
                am.setExact(0, triggerAtTime, pendingIntent);
            } else {
                am.setExact(QUALCOMM_EXTRA, triggerAtTime, pendingIntent);
            }
            Log.i(TAG, "setupRtcAlarm AlarmManager.POWER_OFF_WAKE_UP,atTimeInMillis ="
                    + Formatter.formatTime(triggerAtTime) + " newState: " + newState);
            AlarmRingStatisticUtils.statisticsAlarmRtc(context, AlarmRingStatisticUtils.EVENT_SET_UP_RTC_ALARM,
                    (int) schduleId, triggerAtTime, newState);
        }
    }

    public static void cancelRtcAlarm(Context context, long scheduleId, long alarmMills, int scheduleState) {
        Log.i(TAG, "Canceling instance " + scheduleId + " timers");
        if (context == null) {
            Log.e(TAG, "cancelRtcAlarm context is null");
            return;
        }
        Log.i(TAG, "powerOffAlarm：" + DeviceUtils.isPowerOffAlarm());
        // Create a PendingIntent that will match any one set for this instance
        PendingIntent pendingIntent = PendingIntent.getBroadcast(context,
                toRequestCode(scheduleId),
                createStateChangeIntent(context, ALARM_MANAGER_TAG, scheduleId, null),
                Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_NO_CREATE));

        Log.v(TAG, "pendingIntent = " + pendingIntent + " scheduleId:" + scheduleId);
        final AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if ((am != null) && (pendingIntent != null)) {
            am.cancel(pendingIntent);
            AppPlatformUtils.cancelPendingIntent(pendingIntent);
            AlarmRingStatisticUtils.statisticsAlarmRtc(context, AlarmRingStatisticUtils.EVENT_CANCEL_RTC_ALARM,
                    (int) scheduleId, alarmMills, scheduleState);
        }

        // qualcomm DON'T reschedule any rtc alarm after Android-O.we need to do it ourself.
        if (Utils.isAboveO() && DeviceUtils.isQcomPlatform(context) && !DeviceUtils.isPowerOffAlarm()) {
            AlarmUtils.cancelQcomRtcAlarm(context, alarmMills);

            AlarmSchedule schedule = ScheduleUtils.getEarlySchedule(context, 0);
            if (schedule != null) {
                sendQcomRtcAlarmBroadcast(context, schedule.getTime());
            }
        }

        // mtk DON'T clear the any rtc alarm.we need to do it ourself.
        if (DeviceUtils.isMtkPlatform(context) || DeviceUtils.isPowerOffAlarm()) {
            AlarmUtils.cancelMtkRtcAlarm(context);

            AlarmSchedule schedule = ScheduleUtils.getEarlySchedule(context, 0);
            if (schedule != null) {
                setupMtkRtcAlarm(context, schedule.getId(), ClockContract.Schedule.FIRED_STATE, schedule.getTime());
                Log.d(TAG,
                        "setupRtcAlarm AlarmManager.POWER_OFF_WAKE_UP,atTimeInMillis ="
                                + Formatter.formatTime(schedule.getTime()));
            }
        }
        if (scheduleState != ClockContract.Schedule.GARB_ALARM_STATE) {
            updateNextAlarm(context);
        }
    }

    private static void sendQcomRtcAlarmBroadcast(Context context, long alarmMills) {
        if (Utils.isSystemUser(context)) {
            Intent intent = new Intent(ACTION_SET_POWEROFF_ALARM);
            intent.setPackage(POWER_OFF_ALARM_PACKAGE);
            intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
            long mills = alarmMills + DELAY_TIME;
            intent.putExtra(POWER_OFF_ALARM_EXTRA_TIME, mills);
            context.sendBroadcast(intent);
            Log.i(TAG, "sendQcomRtcAlarmBroadcast time =" + Formatter.formatTime(mills));
            PrefUtils.putString(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    PrefUtils.SETUP_RTC_ALARM_TIME, Formatter.formatTime(mills));
        } else {
            Log.w(TAG, "secondary user not support set poweroffalarm");
        }
    }

    private static int toRequestCode(long id) {
        return Long.valueOf(id).hashCode();
    }

    private static int getGlobalIntentId(Context context) {
        final SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(Utils.getDeviceContext(context));
        return prefs.getInt(ALARM_GLOBAL_ID_EXTRA, -1);
    }

    public static void updateGloablIntentId(Context context) {
        if (context != null) {
            final SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(Utils.getDeviceContext(context));
            final int globalId = prefs.getInt(ALARM_GLOBAL_ID_EXTRA, -1) + 1;
            Log.i(TAG, " updateGloablIntentId globalId = " + globalId);
            //NOTE: Do NOT use apply here!!!
            prefs.edit().putInt(ALARM_GLOBAL_ID_EXTRA, globalId).commit();
        }
    }

    private static boolean updateScheduleState(Context context, long scheduleId, int state) {
        ContentValues values = new ContentValues(1);
        values.put(ClockContract.Schedule.ALARM_STATE, ClockContract.Schedule.SILENT_STATE);
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        boolean success = context.getContentResolver().update(uri, values, null, null) == 1;
        Log.i(TAG, "updateScheduleState: " + scheduleId + ", state: " + state);
        return success;
    }

    private static void showSnoozeToast(final Context context, final String snoozeTime) {
        final Handler mainHandler = new Handler(context.getMainLooper());
        final Runnable r = new Runnable() {
            @Override
            public void run() {
                ToastManager.showToast(context.getString(R.string.alarm_alert_snooze_set_min, snoozeTime), Toast.LENGTH_LONG);
            }
        };
        mainHandler.post(r);
    }

    private static boolean getNotificationChannelPermission(NotificationManager nm, String channelId) {
        if (Utils.isAboveO()) {
            NotificationChannel channel = nm.getNotificationChannel(channelId);
            if (channel == null) {
                return nm.areNotificationsEnabled();
            } else {
                return nm.areNotificationsEnabled()
                        && channel.getImportance() != NotificationManager.IMPORTANCE_NONE;
            }
        }
        return true;
    }
}
