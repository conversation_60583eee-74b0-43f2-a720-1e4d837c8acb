package com.oplus.alarmclock.alarmclock;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.oplus.clock.common.utils.Log;

public class MediaScannerReceiver extends BroadcastReceiver {

    private static final String TAG = "MediaScannerReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.v(TAG, "MediaScannerReceiver receive: " + intent.getAction());
        AlarmUtils.startUpdateRingUri(context);
        AlarmUtils.unRegistedMediaScannerFinishedReceiver();
    }
}
