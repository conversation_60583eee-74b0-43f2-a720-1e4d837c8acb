/************************************************************
 * Copyright 2008-2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description :Views of the Alarms
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 * v1.1, 2018-10-20, <PERSON><PERSON><PERSON>, refactor
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import static android.content.Context.RECEIVER_EXPORTED;

import static com.oplus.alarmclock.utils.ClockConstant.EVENT_ELAPSED_TIME_UNTIL_NEXT_ALARM;
import static java.lang.Math.abs;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.ContextMenu;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.accessibility.AccessibilityEvent;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.coui.appcompat.progressbar.COUILoadingView;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.coui.component.responsiveui.ResponsiveUIModel;
import com.coui.component.responsiveui.layoutgrid.MarginType;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.COUICollapsableAppBarLayout;
import com.google.android.material.appbar.COUICollapsingToolbarLayout;
import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ClockEventDispatcher;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeService;
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeServiceUtils;
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper;
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmAdapterSpaceItemDecoration;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder;
import com.oplus.alarmclock.base.BaseUiModeFragment;
import com.oplus.alarmclock.globalclock.AlarmListGridLayoutManager;
import com.oplus.alarmclock.globalclock.AlarmListLinearLayoutManager;
import com.oplus.alarmclock.migration.WPlusNotifyUtils;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.AiTripHelper;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.DiffCallBack;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.EditMenuClickUtils;
import com.oplus.alarmclock.utils.FloatingButtonTool;
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.ToastManager;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.anim.EffectiveAnimationView;
import com.oplus.clock.common.utils.VersionUtils;
import com.oplus.statistics.OplusTrack;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class AlarmClockFragment extends BaseUiModeFragment
        implements AlarmClock.IFragmentFocused, AlarmListAdapter.OnItemLongClickListener,
        AlarmListAdapter.OnSwitchChangeListener, AlarmListAdapter.OnItemClickListener,
        AlarmListAdapter.OnCheckedChangeListener, AlarmClock.NavigationDelete {

    public static final String ALARM_MODIFY = "alarm_modify";
    public static final String ALARM_MODIFY_TEMP = "alarm_modify_temp";
    public static final String MODE = "alarm_mode";
    public static final int MAX_ALARM_COUNT = 450;
    public static final String ACTION_REPEAT_ALARM_CLOSE_ONCE = "com.oplus.alarmclock.action.repeat_alarm_close_once";
    public static final String ACTION_REPEAT_ALARM_DISMISS = "com.oplus.alarmclock.action.repeat_alarm_dismiss";
    public static final String ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG = "com.oplus.alarmclock.action.repeat_alarm_close_once_dialog";
    public static final String EXTRA_REPEAT_ALARM_CLOSE_ONCE_DIALOG = "extra_repeat_alarm_close_once_dialog";
    public static final String CURRENT_REPEAT_ALARM = "current_repeat_alarm";
    public static final String ACTION_REFRESH_ALARM_NEXT_TIME = "com.oplus.alarmclock.action.refresh_alarm_next_time;";
    public static final int MODEL_EDIT = 1;
    public static final int MODEL_NORMAL = 2;
    public static final int NO_ALARM_ID = -1;
    private static final int CLOSE_ONCE_WHAT = 1;
    private static final int ALARM_STATUS_WHAT = 2;
    private static final double FACTOR = 0.45;
    private static final String CLOSE_ONCE_KEY_ALARM_SCHEDULE = "close_once_key_alarm_schedule";
    private static final String CLOSE_ONCE_KEY_ALARM_CURRENT_RING_SCHEDULE = "close_once_key_alarm_current_ring_schedule";
    private static final String CLOSE_ONCE_KEY_NEXT_ALARM_TIME = "close_once_key_next_alarm_time";
    private static final String CLOSE_ONCE_KEY_PREVIOUS_ALARM_TIME = "close_once_key_previous_alarm_time";
    private static final String CLOSE_ONCE_KEY_POSITION = "close_once_key_position";

    private static final String TAG = "AlarmClockFragment";

    private static final long EMPTY_SELECT_ID = -1;
    private static final int MIN_COUNT_TO_SHOW_LOADING = 15;
    private static final int TOAST_DELAYED_TIME = 250;//250ms
    private static final int TALK_BACK_DELAY = 1500;
    private static boolean sActionForChange = true;
    private View mView;
    private COUIToolbar mToolbar;
    private TextView mSubtitle;
    private Float mScrollFraction = 0f;
    private COUICollapsingToolbarLayout mCollapsingToolbarLayout;
    private COUICollapsableAppBarLayout mAppBarLayout;
    private COUIRecyclerView mAlarmsList;
    private AlarmListAdapter mListAdapter;
    private AlarmAdapterSpaceItemDecoration mItemDecoration;
    private boolean mActionForItemClick = true;
    private boolean mShowSpeechMenu;
    private Bundle mSavedState = null;
    private int mMode = MODEL_NORMAL;
    private COUILoadingView mLoadingView;
    private EffectiveAnimationView mViewEmpty;
    private TextView mEmptyTextView;
    private boolean mHasLoadSpeechMenu = false;
    private int mCurrentClickItem = -1; //This flag is only used to record the current  position of clicked or checked alarm
    private boolean mIsCheckByPermission = false; //This flag is only used to record the dialog permission of open alarm
    private COUIFloatingButton mCouiFloatingButton;
    private View mLayoutEmpty;
    private AlarmContentObserver mAlarmContentObserver;
    private ScheduleContentObserver mScheduleContentObserver;
    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();
    private AlertDialog mCloseOnceAlertDialog;
    private FloatingButtonTool mFloatingButtonTool;
    private ArrayList<Alarm> mAlarmList;
    private int mClickMenuId;
    private boolean mIsFromBroadcast;
    private boolean mIsNeedBackPosition = false;
    private long mAlarmId = -1;
    private Handler mAlarmClockHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case CLOSE_ONCE_WHAT: //turn off alarm close once
                    Bundle bundle = msg.getData();
                    if (bundle != null) {
                        AlarmSchedule alarmSchedule = bundle.getParcelable(CLOSE_ONCE_KEY_ALARM_SCHEDULE);
                        AlarmSchedule currentRingSchedule = bundle.getParcelable(CLOSE_ONCE_KEY_ALARM_CURRENT_RING_SCHEDULE);
                        long nextAlarmTime = bundle.getLong(CLOSE_ONCE_KEY_NEXT_ALARM_TIME);
                        long previousAlarmTime = bundle.getLong(CLOSE_ONCE_KEY_PREVIOUS_ALARM_TIME);
                        int alarmId = bundle.getInt(CLOSE_ONCE_KEY_POSITION);
                        if (alarmSchedule != null) {
                            long alarmScheduleTime = 0L;
                            if ((alarmSchedule.getAlarmState() > ClockContract.Schedule.SILENT_STATE) || ((currentRingSchedule != null) && (currentRingSchedule.getAlarmId() == alarmSchedule.getAlarmId()))) { //snooze state or other not of normal
                                //there need close this time and next time alarm , so need get next alarm time
                                Log.e(TAG, "AlarmClockFragment mAlarmClockHandler current alarm is snooze or is ring");
                                AlarmSchedule newInstance = AlarmUtils.createScheduleForAlarm(alarmSchedule.getAlarm());
                                alarmScheduleTime = newInstance.getTime();
                            } else {
                                Log.e(TAG, "AlarmClockFragment mAlarmClockHandler normal");
                                alarmScheduleTime = alarmSchedule.getTime();
                            }
                            closeOnceViewRefresh(alarmScheduleTime, alarmId, nextAlarmTime, previousAlarmTime);
                        }
                    }
                    break;
                case ALARM_STATUS_WHAT:
                    if (!isEditMode()) {
                        setSubTitle((String) msg.obj);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    //初始化悬浮按钮
    public void initializeFloatingButton(List<Alarm> alarmList) {
        if (mFloatingButtonTool == null) {
            mFloatingButtonTool = new FloatingButtonTool();
        }
        if ((mContext != null) && (mCouiFloatingButton == null) && (mFloatingButtonTool != null)) {
            View view = ((ViewStub) getView().findViewById(R.id.layout_floating_button)).inflate();
            mCouiFloatingButton = view.findViewById(R.id.color_floating_button_activity_main_fab);
            mFloatingButtonTool.setFloatingButton(AlarmClock.TAB_INDEX_ALARMCLOCK, mContext, mCouiFloatingButton, getString(R.string.new_alarm));
        }
        if (mFloatingButtonTool != null) {
            //控制显隐并且控制背景更新
            mFloatingButtonTool.updateFloatingButton(mContext, mCouiFloatingButton, !(mMode == MODEL_EDIT), alarmList.size() < AlarmClockFragment.MAX_ALARM_COUNT);
        }
    }


    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater menuInflater) {
        menuInflater.inflate(R.menu.action_menu_icon_clock, menu);
        super.onCreateOptionsMenu(menu, menuInflater);
    }

    public void redDotSetting() {
        if (mToolbar != null) {
            redDotSetting(mToolbar);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        Log.d(TAG, "onCreate");
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
        // Register alarm observer to observe alarm database
        Context context = getContext();
        if (context != null) {
            ContentResolver cr = getContext().getContentResolver();
            mAlarmContentObserver = new AlarmContentObserver(this);
            cr.registerContentObserver(ClockContract.ALARM_CONTENT_URI, true, mAlarmContentObserver);
            mScheduleContentObserver = new ScheduleContentObserver(this);
            cr.registerContentObserver(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, true, mScheduleContentObserver);
        } else {
            Log.e(TAG, "context is null!");
        }

        /* If the Fragment was destroyed inbetween (screen rotation), we need to recover the savedState first */
        /* However, if it was not, it stays in the instance from the last onDestroyView() and we don't want to overwrite it */
        if ((savedInstanceState != null) && (mSavedState == null)) {
            mSavedState = savedInstanceState.getBundle(MODE);
        }
        if (mSavedState != null) {
            mMode = mSavedState.getInt(MODE);
        }
        mSavedState = null;
        registerReceiver();
        Activity activity = getActivity();
        if (activity instanceof AlarmClock) {
            ((AlarmClock) activity).onResumed(this);
        }
        initEvent();
    }

    /**
     * 初始化事件
     */
    private void initEvent() {
        //更新下次响铃时间
        LiteEventBus.Companion.getInstance().with(EVENT_ELAPSED_TIME_UNTIL_NEXT_ALARM, String.valueOf(hashCode())).observe(this, o -> {
            if (mAlarmsList != null) {
                if (o instanceof Boolean) {
                    refreshList((Boolean) o);
                } else {
                    mAlarmsList.postDelayed(() -> refreshList(false), TALK_BACK_DELAY);
                }
            }
        });
    }

    @Override
    public void onSaveInstanceState(Bundle bundle) {
        super.onSaveInstanceState(bundle);
        /* Save the fragment's state here
           If onDestroyView() is called first, we can use the previously savedState but we can't call saveState() anymore
           If onSaveInstanceState() is called first, we don't have savedState, so we need to call saveState() => (?:) operator inevitable!*/
        bundle.putBundle(MODE, (mSavedState != null) ? mSavedState : saveState());
    }

    // called either from onDestroyView() or onSaveInstanceState()
    private Bundle saveState() {
        Bundle state = new Bundle();
        state.putInt(MODE, mMode);
        return state;
    }

    private void initLayoutEmpty() {
        if (getView() != null) {
            mLayoutEmpty = ((ViewStub) getView().findViewById(R.id.layout_empty)).inflate();
            mLoadingView = mLayoutEmpty.findViewById(R.id.loadingView);
            mEmptyTextView = mLayoutEmpty.findViewById(android.R.id.empty);
            mViewEmpty = mLayoutEmpty.findViewById(R.id.view_empty);
            mEmptyTextView.setText(R.string.no_alarms_clock);
            mEmptyTextView.setVisibility(View.GONE);
            mViewEmpty.setVisibility(View.GONE);

        }
    }

    /**
     * 设置闹钟列表距离左右间距
     */
    private void initLayoutAlarmList() {
        View root = getView();
        if (root != null) {
            mAlarmsList = mView.findViewById(android.R.id.list);
            int de8dp = mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_8);
            if (UiMode.LARGE_HORIZONTAL == getUiMode()) {
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mAlarmsList.getLayoutParams();
                layoutParams.setMarginStart(de8dp);
                layoutParams.setMarginEnd(de8dp);
                mAlarmsList.setLayoutParams(layoutParams);
            }
            Activity activity = getActivity();
            if ((activity != null) && (activity instanceof AlarmClock)) {
                initRecycleList(activity);
                Log.d(TAG, "initView  mListAdapter : " + mListAdapter + "    fragment : " + this);
            }
        }
    }

    /**
     * 初始化RecycleList
     *
     * @param activity
     */
    private void initRecycleList(Activity activity) {
        if (mAlarmsList == null) {
            return;
        }
        if (mListAdapter != null) {
            ((AlarmClock) activity).getLifecycle().removeObserver(mListAdapter);
            mAlarmsList.setAdapter(null);
        }
        mListAdapter = new AlarmListAdapter(activity, getUiMode());
        ((AlarmClock) activity).getLifecycle().addObserver(mListAdapter);
        if (mAlarmList != null) {
            mListAdapter.updateData(mAlarmList);
            ClockOplusCSUtils.statisticsClockCount(mContext, mAlarmList.size());
            ClockOplusCSUtils.statisticsClockOpenCount(mContext, mAlarmList);
        }
        mListAdapter.setMultiSelect(false);
        mListAdapter.setSwitchChangeListener(this);
        mAlarmsList.setItemAnimator(null);
        setRecyclerViewLayoutManager();
        mListAdapter.setOnItemLongClickListener(this);
        mListAdapter.setOnItemClickListener(this);
        mAlarmsList.setIsUseNativeOverScroll(false);
    }

    /**
     * 设置布局管理
     */
    public void setRecyclerViewLayoutManager() {
        if (mAlarmsList == null || mListAdapter == null) {
            Log.e(TAG, "mAlarmsList or mListAdapter is null!");
            return;
        }
        int spanCount = 1;
        switch (getUiMode()) {
            case LARGE_HORIZONTAL: //平板横屏
                spanCount = AlarmListAdapter.LIST_ITEM_OSLO_LAND_COUNT_MAX;
                setGridLayout(spanCount);
                setListMargin(AlarmClockApplication.getInstance()
                        .getResources().getDimensionPixelSize(R.dimen.layout_dp_32));
                break;
            case LARGE_VERTICAL: //平板竖屏
                if (mAlarmList != null) {
                    if (mAlarmList.size() >= AlarmListAdapter.LIST_ITEM_OSLO_PORTRAIT_COUNT_MAX) {
                        spanCount = AlarmListAdapter.LIST_ITEM_OSLO_PORTRAIT_COUNT_MAX;
                    }
                }
                setGridLayout(spanCount);
                setListMargin(AlarmClockApplication.getInstance()
                        .getResources().getDimensionPixelSize(R.dimen.layout_dp_18));
                break;
            case MIDDLE:
                spanCount = AlarmListAdapter.LIST_ITEM_OSLO_PORTRAIT_COUNT_MAX;
                setGridLayout(spanCount);
                setListMargin(AlarmClockApplication.getInstance()
                        .getResources().getDimensionPixelSize(R.dimen.layout_dp_18));
                break;
            default:
                setLinearLayout();
        }
        mAlarmsList.setAdapter(mListAdapter);
    }

    /**
     * 设置列表中大屏边距
     *
     * @param margin
     */
    private void setListMargin(int margin) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mAlarmsList.getLayoutParams();
        layoutParams.setMarginStart(margin);
        layoutParams.setMarginEnd(margin);
        mAlarmsList.setLayoutParams(layoutParams);
    }

    /**
     * 闹钟数量
     *
     * @return
     */
    public int alarmListSize() {
        if (mAlarmList != null) {
            return mAlarmList.size();
        } else {
            return 0;
        }
    }

    /**
     * 网格布局
     */
    private void setGridLayout(int spanCount) {
        AlarmListGridLayoutManager gridLayoutManager = new AlarmListGridLayoutManager(mContext, spanCount,
                GridLayoutManager.VERTICAL, false);
        mAlarmsList.setLayoutManager(gridLayoutManager);
        if (mItemDecoration == null) {
            mItemDecoration = new AlarmAdapterSpaceItemDecoration();
        }
        mAlarmsList.removeItemDecoration(mItemDecoration);
        mAlarmsList.addItemDecoration(mItemDecoration);
        mListAdapter.setSpanCount(spanCount);
    }

    /**
     * 线性布局
     */
    private void setLinearLayout() {
        AlarmListLinearLayoutManager linearLayoutManager = new AlarmListLinearLayoutManager(mContext);
        mAlarmsList.setLayoutManager(linearLayoutManager);
        if (mItemDecoration == null) {
            mItemDecoration = new AlarmAdapterSpaceItemDecoration();
        }
        mAlarmsList.removeItemDecoration(mItemDecoration);
        mAlarmsList.addItemDecoration(mItemDecoration);
    }

    @Override
    public void onResume() {
        Log.d(TAG, "onResume");
        super.onResume();
        if (getActivity() != null) {
            OplusTrack.onResume(getActivity());
        }
        if (getUserVisibleHint()) {
            Log.d(TAG, "onResume mIsFromBroadcast: " + mIsFromBroadcast);
            refreshList(mIsFromBroadcast);
            mIsFromBroadcast = false;
        }

        //Note: Forcibly set the menu here, because the ColorTabLayout cannot set default
        // selected tab and notify the listener.
        //end.

        mActionForItemClick = true;

        resetViewStub();
    }

    private void resetViewStub() {
        if ((mAlarmList != null) && (mAlarmList.size() == 0)) {
            if (mEmptyTextView != null) {
                mEmptyTextView.setVisibility(View.VISIBLE);
                mViewEmpty.setVisibility(View.VISIBLE);
                playEmptyAnimOrShowEmptyIcon(mViewEmpty, mEmptyTextView, AlarmClock.TAB_INDEX_ALARMCLOCK);
            }
            if (mAlarmsList != null) {
                mAlarmsList.setVisibility(View.GONE);
            }
        } else {
            if ((mAlarmsList != null) && (mAlarmsList.getVisibility() != View.VISIBLE)) {
                mAlarmsList.setVisibility(View.VISIBLE);
            }
            if ((mEmptyTextView != null) && (mEmptyTextView.getVisibility() != View.GONE)) {
                mEmptyTextView.setVisibility(View.GONE);
                mViewEmpty.setVisibility(View.GONE);
            }
        }
        if ((mFloatingButtonTool != null) && (mAlarmList != null)) {
            //控制显隐并且控制背景更新
            mFloatingButtonTool.updateFloatingButton(mContext, mCouiFloatingButton, !(mMode == MODEL_EDIT),
                    mAlarmList.size() < AlarmClockFragment.MAX_ALARM_COUNT);
        }
    }

    private void refreshList(boolean isForceRefresh) {
        if (mListAdapter != null) {
            if (isForceRefresh) {
                Log.i(TAG, "refreshList notifyDataSetChanged");
                mListAdapter.notifyDataSetChanged();
            }
            sendAlarmStatusMsg(mListAdapter.getList());
        }
    }

    @Override
    public void onPause() {
        Log.d(TAG, "onPause");
        super.onPause();
        OplusTrack.onPause(getActivity());
        mActionForItemClick = false;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView");
        if (container != null) {
            mContext = container.getContext();
        } else {
            mContext = getContext();
        }
        mView = inflateContentView(inflater, container, savedInstanceState);
        mCollapsingToolbarLayout = mView.findViewById(R.id.collapsingToolbarLayout);
        mToolbar = mView.findViewById(R.id.toolbar);
        mAppBarLayout = mView.findViewById(R.id.appBarLayout);
        mSubtitle = mView.findViewById(R.id.coui_appbar_subtitle_content);
        //解决首页副标题首次进入位置不正确闪动问题，手动设置副标题margin
        if (getActivity() != null && getActivity().getWindow() != null && getActivity().getWindow().getDecorView() != null) {
            View decView = getActivity().getWindow().getDecorView();
            ResponsiveUIModel responsiveUIModel = new ResponsiveUIModel(getActivity(), decView.getMeasuredWidth(),
                    decView.getMeasuredHeight());
            responsiveUIModel.chooseMargin(MarginType.MARGIN_SMALL);
            int leftMargin = responsiveUIModel.getResponsiveUI().margin();
            ViewGroup.MarginLayoutParams margin = (ViewGroup.MarginLayoutParams) mSubtitle.getLayoutParams();
            margin.leftMargin = leftMargin;
            mSubtitle.setLayoutParams(margin);
            //解决首次进入副标题文字未对齐问题
            mAppBarLayout.post(() -> {
                if (mSubtitle.getMeasuredHeight() == 0) {
                    mSubtitle.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
                }
                mAppBarLayout.updateSubtitle();
            });
        }
        initStatusBar();
        initTopBar();
        return mView;
    }


    private void initStatusBar() {
        CoordinatorLayout coor = mView.findViewById(R.id.coordinator);
        if (coor != null) {
            coor.setPadding(coor.getPaddingLeft(), Utils.getStatusBarHeight(coor.getContext()), coor.getPaddingRight(), coor.getPaddingBottom());
        }
    }

    private void initTopBar() {
        if (mToolbar == null || getActivity() == null || mAppBarLayout == null) {
            return;
        }
        setTitle(mContext.getResources().getString(R.string.Alarm_Clock_app_label));
        mToolbar.inflateMenu(R.menu.action_menu_icon_clock);
        mToolbar.setTitleMarginStart(0);
        mToolbar.setIsTitleCenterStyle(false);
        mAppBarLayout.addOnOffsetChangedListener((AppBarLayout.BaseOnOffsetChangedListener) (appBarLayout, verticalOffset) -> {
            if (appBarLayout == null || appBarLayout.getTotalScrollRange() == 0) {
                return;
            }
            Float fraction = (abs(verticalOffset) / (float) appBarLayout.getTotalScrollRange());
            if (mScrollFraction == fraction) {
                return;
            }
            mScrollFraction = fraction;
        });
        mAppBarLayout.setSubtitleHideEnable(true);
        mAppBarLayout.setStartPaddingBottom(getResources().getDimensionPixelOffset(R.dimen.layout_dp_8));
        redDotSetting(mToolbar);
        mToolbar.setVisibility(View.INVISIBLE);
        mToolbar.setPopupWindowOnDismissListener(() -> {
            if (mClickMenuId == R.id.edit) {
                changeMode(MODEL_EDIT, EMPTY_SELECT_ID);
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_ALARM_NEW_MENU);
            }
            mClickMenuId = 1;
        });
        mToolbar.setOnMenuItemClickListener(menuItem -> {
            onOptionsItemSelected(menuItem);
            return false;
        });
        mCollapsingToolbarLayout.setVisibility(View.INVISIBLE);
    }

    private void setTitle(String title) {
        mCollapsingToolbarLayout.setTitle(title);
        mToolbar.setTitle(title);
    }

    /**
     * 设置副标题
     *
     * @param subTitle
     */
    private void setSubTitle(String subTitle) {
        if (mSubtitle == null) {
            return;
        }
        mSubtitle.setText(subTitle);
        mSubtitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void onItemClick(int position) {
        alarmListItemClick(position);
    }

    /**
     * 列表item点击
     *
     * @param position
     */
    public void alarmListItemClick(int position) {
        if ((getAlarmList() != null) && (getAlarmList().size() > position)) {
            Alarm alarm = getAlarmList().get(position);
            Log.d(TAG, "onItemClick, pos " + position + ",is " + alarm.getId() + ", mActionForItemClick: " + mActionForItemClick + "   alarm = " + alarm);
            if (mMode == MODEL_EDIT) {
                Log.d(TAG, "mAlarmItemClickListener isSelected = " + !alarm.isSelected());
                setEditAlarmItemSelect(alarm.getId(), !alarm.isSelected());
                if (mListAdapter != null) {
                    Log.i(TAG, "onItemClick notifyDataSetChanged");
                    mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(position));
                }
                return;
            }
            Activity activity = getActivity();
            if ((activity != null) && !activity.isDestroyed() && mDoubleClickHelper.canClick()
                    && (activity instanceof AlarmClock)) {
                //检查是否同意音频须知
                StatementDialogUtils.Companion.setMediaStatementContentView(activity, new PrivacyPolicyAlert.PrivacyPolicyCallback() {
                    @Override
                    public void doAfterPermitted() {
                        mCurrentClickItem = position;
                        ((AlarmClock) activity).requestRuntimePermissions(true, true, false);
                    }

                    @Override
                    public void onExitClick() {
                        Log.i(TAG, "mediaStatement exit");
                    }
                }, false);
            }

        }
    }


    @Override
    public void onItemLongClick(int position) {
        Log.d(TAG, "onItemLongClick position = " + position);
        if ((getAlarmList() != null) && (getAlarmList().size() > position)) {
            Alarm alarm = getAlarmList().get(position);
            if (alarm != null) {
                if (mMode != MODEL_EDIT) {
                    changeMode(MODEL_EDIT, alarm.getId());
                }
            }
        }
    }

    @Override
    public void onCheckedChanged(int position, boolean isChecked) {
        Log.d(TAG, "onCheckedChanged position = " + position);
        if ((getAlarmList() != null) && (getAlarmList().size() > position)) {
            setEditAlarmItemSelect(getAlarmList().get(position).getId(), isChecked);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (isListAnimating()) {
            Log.d(TAG, "onOptionsItemSelected list is animating");
            return true;
        }
        int itemId = item.getItemId();
        if (itemId == R.id.edit) {
            Log.d(TAG, "edit alarm clock list");
            if (mDoubleClickHelper.canClick() && EditMenuClickUtils.Companion.canClickAfterTabSwitch()) {
                mClickMenuId = item.getItemId();
            }
        } else if (itemId == R.id.voice) {
            Log.d(TAG, "onOptionsItemSelected clicked voice");
            if (mDoubleClickHelper.canClick()) {
                startVoiceSetAlarm();
            }
            //test();
        } else if (itemId == R.id.settings) {
            Log.d(TAG, "onOptionsItemSelected clicked settings");
            if (mDoubleClickHelper.canClick()) {
                startToSetting();
                ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.SETTING_FROM_CLOCK);
            }
        } else if (itemId == android.R.id.home) {
            Log.d(TAG, "onOptionsItemSelected clicked cancel");
            changeMode(MODEL_NORMAL, EMPTY_SELECT_ID);
        } else if (itemId == R.id.cancel_select) {
            changeMode(MODEL_NORMAL, EMPTY_SELECT_ID);
        } else if (itemId == R.id.select_all_clock) {
            if (mDoubleClickHelper.canClick()) {
                doQuickSelect();
            }
        }
        return true;
    }

    /**
     * 设置当前编辑闹钟的id
     *
     * @param mCurrentClickItem
     */
    public void setmCurrentClickItem(int mCurrentClickItem) {
        this.mCurrentClickItem = mCurrentClickItem;
    }

    public void openModelView(boolean isNeedDelay) {
        Log.d(TAG, "onOptionsItemSelected clicked add");
        if (mToolbar != null && mToolbar.getMenuView() != null) {
            //隐藏菜单
            mToolbar.getMenuView().dismissPopupMenus();
        }
        if ((mAlarmsList == null) || (mListAdapter == null)) {
            initLayoutAlarmList();
        }
        if ((mListAdapter != null) && ((mListAdapter.getList().size() < MAX_ALARM_COUNT) || (mCurrentClickItem > -1))) {
            Log.d(TAG, "addAlarmClock");
            ClockOplusCSUtils.onEvent(getActivity(), ClockOplusCSUtils.STR_PRESS_ALARM_NEW_MENU);
            if ((mCurrentClickItem > -1) && (mListAdapter.getList().size() > mCurrentClickItem)) {
                startAlarmSetActivity(getActivity(), mListAdapter.getList().get(mCurrentClickItem), mListAdapter.getList().size(), isNeedDelay);
            } else {
                startAlarmSetActivity(getActivity(), null, mListAdapter.getList().size(), isNeedDelay);
            }
            mCurrentClickItem = -1;
        } else {
            ToastManager.showToast(getString(R.string.add_alarm_limit), Toast.LENGTH_SHORT);
        }
    }

    /**
     * 全选/取消全选
     */
    public void doQuickSelect() {
        Log.d(TAG, "setOnStateChangeListener clicked select_all");
        if (mListAdapter != null) {
            mListAdapter.doQuickSelect();
        }
        updateEditMenuAndTitle();
    }


    public void init() {
        if (!mHasLoadSpeechMenu) {
            mShowSpeechMenu = Utils.isSpeechAiAvailable(AlarmClockApplication.getInstance());
            Log.d(TAG, "init mShowSpeechMenu = " + mShowSpeechMenu);
            mHasLoadSpeechMenu = true;
        }
    }

    public boolean showVoiceAddMenu() {
        return mShowSpeechMenu;
    }

    public void clearEdit() {
        changeMode(MODEL_NORMAL, EMPTY_SELECT_ID);
    }

    private void changeMode(int mode, long alarmId) {
        if (mListAdapter != null && mToolbar != null) {
            mMode = mode;
            List<Alarm> alarmList = mListAdapter.getList();
            Activity activity = getActivity();

            if ((mAlarmsList == null)) {
                initLayoutAlarmList();
            }

            if ((activity != null) && (!activity.isFinishing()) && (activity instanceof AlarmClock)
                    && (mAlarmsList != null)) {
                if (((AlarmClock) activity).getSupportActionBar() != null) {
                    ((AlarmClock) getActivity()).getSupportActionBar().setDisplayHomeAsUpEnabled(false);
                }
                mToolbar.getMenu().close();
                mToolbar.getMenu().clear();
                if (mode == MODEL_EDIT) {
                    Log.d(TAG, "changeMode mode == MODEL_EDIT");
                    mListAdapter.changeMode(MODEL_EDIT);
                    mListAdapter.setOnCheckedChangeListener(this);
                    ((AlarmClock) activity).showNavigation();
                    mToolbar.inflateMenu(R.menu.menu_edit_mode);
                    setSubTitle("");
                    mCollapsingToolbarLayout.setEditStyle(true);
                } else {
                    setTitle(mContext.getResources().getString(R.string.Alarm_Clock_app_label));
                    sendAlarmStatusMsg(mListAdapter.getList());
                    mToolbar.inflateMenu(R.menu.action_menu_icon_clock);
                    mCollapsingToolbarLayout.setEditStyle(false);
                    Log.d(TAG, "changeMode mode == MODEL_NORMAL");
                    mListAdapter.changeMode(MODEL_NORMAL);
                    mListAdapter.setSwitchChangeListener(this);
                    ((AlarmClock) activity).dismissNavigation();
                    updateMenu();
                }
                ((AlarmClock) activity).viewpageCanInputEnabled(mode != MODEL_EDIT);
                mListAdapter.clearAlarmSelect(alarmList, alarmId);
                Log.i(TAG, "changeMode notifyDataSetChanged");
                mListAdapter.notifyDataSetChanged();
                AlarmClock.correctAllMenuItemFromFragment(activity);
            }
            changeMenu(mode);
            if ((mFloatingButtonTool != null)) {
                //控制显隐并且控制背景更新
                mFloatingButtonTool.updateFloatingButton(mContext, mCouiFloatingButton, !(mMode == MODEL_EDIT), alarmList.size() < AlarmClockFragment.MAX_ALARM_COUNT);
            }
        }
    }


    // to refresh clock list every min
    private final BroadcastReceiver mIntentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "mIntentReceiver Action: " + action);
            if (Intent.ACTION_TIME_TICK.equals(action) && mActionForItemClick && getUserVisibleHint()) {
                mIsFromBroadcast = true;
                refreshList(true);
            } else if (ACTION_REPEAT_ALARM_CLOSE_ONCE.equals(action) || ACTION_REPEAT_ALARM_DISMISS.equals(action) || AlarmClock.ACTION_SNOOZE_ALARM.equals(action)) {
                mIsFromBroadcast = true;
                Alarm alarm = intent.getParcelableExtra(CURRENT_REPEAT_ALARM);
                if ((alarm != null) && (mListAdapter != null)) {
                    List<Alarm> alarmList = mListAdapter.getList();
                    if ((alarmList != null) && (alarmList.size() > 0)) {
                        for (int i = 0; i < alarmList.size(); i++) {
                            if (alarm.getId() == alarmList.get(i).getId()) {
                                Log.d(TAG, "notifyItemChanged notifyDataSetChanged  position = " + i + "  alarm = " + alarm);
                                if (ACTION_REPEAT_ALARM_CLOSE_ONCE.equals(action)) {
                                    alarmList.get(i).setmCloseOncePriTime(alarm.getmCloseOncePriTime());
                                    alarmList.get(i).setmCloseOnceTimeNext(alarm.getmCloseOnceTimeNext());
                                }
                                mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(i));
                                break;
                            }
                        }
                    } else {
                        Log.d(TAG, "notifyDataSetChanged notifyDataSetChanged alarm : " + alarm);
                        mListAdapter.notifyDataSetChanged();
                    }

                    cancelDialog(alarm.getId());

                } else {
                    if (mListAdapter != null) {
                        Log.d(TAG, "notifyDataSetChanged notifyDataSetChanged");
                        mListAdapter.notifyDataSetChanged();
                    }
                }
                if (mListAdapter != null) {
                    sendAlarmStatusMsg(mListAdapter.getList());
                }

            } else if (ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG.equals(action)) {

                Log.i(TAG, "ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG");
                if ((mCloseOnceAlertDialog != null) && mCloseOnceAlertDialog.isShowing()) {
                    long alarmId = intent.getLongExtra(EXTRA_REPEAT_ALARM_CLOSE_ONCE_DIALOG, -1);
                    Log.i(TAG, "ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG alarmId == " + alarmId);
                    cancelDialog(alarmId);
                }
            } else if (ACTION_REFRESH_ALARM_NEXT_TIME.equals(action)) {
                if (mListAdapter != null) {
                    sendAlarmStatusMsg(mListAdapter.getList());
                }
            } else if ((Intent.ACTION_TIMEZONE_CHANGED.equals(action)) || (Intent.ACTION_TIME_CHANGED.equals(action))) {
                mIsFromBroadcast = true;
                refreshList(true);
            }
        }
    };

    private void cancelDialog(long alarmId) {
        if ((mCurrentClickItem >= 0)
                && (mListAdapter.getList().size() > mCurrentClickItem)
                && (mCloseOnceAlertDialog != null)
                && mCloseOnceAlertDialog.isShowing()
                && (mListAdapter.getList().get(mCurrentClickItem).getId() == alarmId)) {
            mCloseOnceAlertDialog.cancel();
            mCurrentClickItem = -1;
        }
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_TIME_TICK);
        filter.addAction(ACTION_REPEAT_ALARM_CLOSE_ONCE);
        filter.addAction(ACTION_REPEAT_ALARM_DISMISS);
        filter.addAction(AlarmClock.ACTION_SNOOZE_ALARM);
        filter.addAction(ACTION_REPEAT_ALARM_CLOSE_ONCE_DIALOG);
        filter.addAction(ACTION_REFRESH_ALARM_NEXT_TIME);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED);
        if (getActivity() != null) {
            getActivity().registerReceiver(mIntentReceiver, filter, null, null, RECEIVER_EXPORTED);
        }
    }

    private void updateEditMenuAndTitle() {
        if (mMode == MODEL_NORMAL) {
            return;
        }
        if (mListAdapter != null && mToolbar != null) {
            final int count = mListAdapter.getSelectedIds().size();
            Log.d(TAG, "SelectedIds count:" + count);
            if (isAdded()) {
                Activity activity = getActivity();
                if ((activity != null) && (!getActivity().isFinishing())) {
                    String title = getToolbarTitle(count);
                    mToolbar.setTitle(title);
                    setTitle(title);
                    ((AlarmClock) activity).setNavigationItemEnable(!mListAdapter.getSelectedIds().isEmpty(), R.id.navigation_delete);
                    MenuItem menuItem = mToolbar.getMenu().findItem(R.id.select_all_clock);
                    if (menuItem != null) {
                        if (count == getCurrentCount()) {
                            menuItem.setTitle(getString(R.string.unselect_all_text));
                        } else {
                            menuItem.setTitle(getString(R.string.select_all));
                        }
                    }
                }
            }
        }
    }

    @Override
    public void onCreateContextMenu(@NonNull ContextMenu menu, @NonNull View v, @Nullable ContextMenu.ContextMenuInfo menuInfo) {
        super.onCreateContextMenu(menu, v, menuInfo);
    }


    public void changeMenu(int mMode) {
        if (mMode == MODEL_EDIT) {
            Log.d(TAG, "changeMenu mMode == MODEL_EDIT");
            updateEditMenuAndTitle();
        } else {
            Log.d(TAG, "changeMenu mMode == MODEL_NORMAL");
        }
    }

    @Override
    public ViewGroup getBlurView() {
        return mAlarmsList;
    }


    @Override
    protected String getTitle() {
        return mContext.getResources().getString(R.string.Alarm_Clock_app_label);
    }

    @Override
    protected boolean shouldDisplaySubTitle() {
        return !isEditMode();
    }

    @Override
    public void onFocused(boolean focused) {
        if (focused) {
            playEmptyAnimOrShowEmptyIcon(mViewEmpty, mEmptyTextView, AlarmClock.TAB_INDEX_ALARMCLOCK);
        }
    }

    @Override
    public void onPreChangeTab() {
        resetEmptyAnimToBegin(mViewEmpty);
    }

    protected View inflateContentView(LayoutInflater inflater, ViewGroup container,
                                      Bundle savedInstanceState) {
        if (inflater == null) {
            inflater = LayoutInflater.from(getContext());
        }
        UiMode uiMode = getUiMode();
        if ((UiMode.LARGE_HORIZONTAL == uiMode) || (UiMode.LARGE_VERTICAL == uiMode)) {
            return inflater.inflate(R.layout.general_main_list_view_large, container, false);
        } else if (UiMode.MIDDLE == uiMode) {
            return inflater.inflate(R.layout.general_main_list_view_mid, container, false);
        } else {
            return inflater.inflate(R.layout.general_main_list_view, container, false);
        }
    }

    private void updateMenu() {
        AlarmClock.correctAllMenuItemFromFragment(getActivity());
    }

    /**
     * 是否展示编辑/语音按钮
     */
    public void isShowEditMenu() {
        if (mToolbar != null) {
            //是否展示编辑按钮
            MenuItem menuItem = mToolbar.getMenu().findItem(R.id.edit);
            if (menuItem != null && mListAdapter != null && mListAdapter.getList() != null) {
                menuItem.setVisible(mListAdapter.getList().size() > 0);
            }
            //是否展示语音新建闹钟
            MenuItem voice = mToolbar.getMenu().findItem(R.id.voice);
            if (voice != null) {
                voice.setVisible(showVoiceAddMenu());
            }
        }
    }

    public void setModeToNormal() {
        if (mMode == MODEL_EDIT) {
            Log.i(TAG, "setModeToNormal");
            changeMode(MODEL_NORMAL, EMPTY_SELECT_ID);
        }
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        Context context = getContext();
        if (context != null) {
            ContentResolver cr = getContext().getContentResolver();
            cr.unregisterContentObserver(mAlarmContentObserver);
            cr.unregisterContentObserver(mScheduleContentObserver);
        }

        dismissDialogForCloseOnce();
        Activity activity = getActivity();
        if (activity != null) {
            activity.unregisterReceiver(mIntentReceiver);
        }
        if (mListAdapter != null) {
            mListAdapter.setSwitchChangeListener(null);
        }
        if (mAlarmClockHandler != null) {
            mAlarmClockHandler.removeMessages(CLOSE_ONCE_WHAT);
            mAlarmClockHandler.removeCallbacksAndMessages(null);
            mAlarmClockHandler = null;
        }
        super.onDestroy();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        LiteEventBus.Companion.getInstance().releaseEvent(String.valueOf(hashCode()));
        saveState();
    }

    private void setEditAlarmItemSelect(long alarmId, boolean checked) {
        if (alarmId > 0) {
            if (mListAdapter != null) {
                mListAdapter.setAlarmSelected(alarmId, checked);
            }
            updateEditMenuAndTitle();
        } else {
            Log.d(TAG, "onCheckedChanged error: No alarm id found in view's tag!");
        }
    }

    public boolean onBackPressed() {
        if (isListAnimating()) {
            Log.d(TAG, "onBackPressed list is animating");
            return true;
        }
        if (mMode == MODEL_EDIT) {
            changeMode(MODEL_NORMAL, EMPTY_SELECT_ID);
            return true;
        }
        return false;
    }

    public void onStatusBarClicked() {
        backToTop();
    }

    private void backToTop() {
        if ((mAlarmsList != null) && (mAlarmsList.getScrollState() == COUIRecyclerView.SCROLL_STATE_IDLE)) {
            mAlarmsList.smoothScrollToPosition(0);
        }
    }

    public void backToPosition(int position) {
        Log.d(TAG, "backToPosition");
        if ((mAlarmsList != null)) {
            Log.d(TAG, "backToPosition " + position);
            mAlarmsList.smoothScrollToPosition(position);
        }
    }

    public int getAlarmPosition(long mId) {
        if (mAlarmList == null) {
            return 0;
        }
        int position = 0;
        for (Alarm a : mAlarmList) {
            if (a.getId() == mId) {
                if (mListAdapter != null) {
                    position += mListAdapter.getHeaderCount();
                }
                return position;
            }
            position++;
        }
        return -1;
    }

    public void checkIsBackToPosition(boolean isNeedBackPosition, long alarmId) {
        mIsNeedBackPosition = isNeedBackPosition;
        mAlarmId = alarmId;
    }


    @Override
    public void onSwitchChangeSwitchToCloseShowDialog(CompoundButton compoundButton, int position, boolean isTalkBackOpen) {

        Log.i(TAG, "onSwitchChangeSwitchToCloseShowDialog");
        if (mDoubleClickHelper.canClick()) {
            mCurrentClickItem = position;
            mCloseOnceAlertDialog = getCloseClockDialog(compoundButton, position, isTalkBackOpen);
            if (mCloseOnceAlertDialog != null) {
                Log.i(TAG, "onSwitchChange  alertDialog");
                mCloseOnceAlertDialog.show();
                Button button3 = mCloseOnceAlertDialog.findViewById(android.R.id.button3);
                if (button3 != null) {
                    button3.setTextColor(COUIContextUtil.getAttrColor(mContext, R.attr.couiColorPrimaryText));
                }
            } else {
                onItemSwitchChange(compoundButton, false, position);
            }
        } else {
            Log.i(TAG, "onSwitchChange  canClick = false");
        }
    }

    @Override
    public void onSwitchChangeSwitchToCloseNotDialog(CompoundButton compoundButton, int position, boolean isTalkBackOpen) {
        Log.i(TAG, "onSwitchChangeSwitchToCloseNotDialog");
        onItemSwitchChange(compoundButton, false, position);
    }

    @Override
    public void onSwitchChangeSwitchToOpen(CompoundButton compoundButton, int position, boolean isTalkBackOpen) {
        Log.i(TAG, "onSwitchChangeSwitchToOpen");
        mCurrentClickItem = position;
        onPermissionBackSwitchChange();
    }

    public void onPermissionBackSwitchChange() {
        Log.d(TAG, "onPermissionBackSwitchChange mPosition = " + mCurrentClickItem);

        LinearLayoutManager layoutManager = (LinearLayoutManager) mAlarmsList.getLayoutManager();
        if (layoutManager != null) {
            int viewPosition = mCurrentClickItem;
            if (mListAdapter != null) {
                viewPosition = mListAdapter.getViewPosition(mCurrentClickItem);
            }
            View v = layoutManager.findViewByPosition(viewPosition);
            if (v != null) {
                CompoundButton alarmSwitch = v.findViewById(R.id.alarm_switch);
                if (alarmSwitch != null) {
                    boolean checked = alarmSwitch.isChecked();
                    Log.d(TAG, "checked = " + checked + " mCurrentClickItem = " + mCurrentClickItem);

                    //talk back
                    boolean isEnabled = Utils.isTalkBackOpen();

                    onItemSwitchChange(alarmSwitch, isEnabled == checked, mCurrentClickItem);
                }
            }
        }
        mCurrentClickItem = -1;
        mIsCheckByPermission = false;
    }

    private void onItemSwitchChange(CompoundButton compoundButton, boolean checked, int position) {
        PlatformUtils.sUpdateType = checked ? PlatformUtils.UPDATE_TYPE_OPERATION_OPEN_ALARM : PlatformUtils.UPDATE_TYPE_OPERATION_CLOSE_ALARM;
        if (compoundButton != null) {
            Object object = compoundButton.getTag();
            Log.d(TAG, "onItemSwitchChange : checked = " + checked + "  mPosition = " + mCurrentClickItem);
            if ((object != null) && (object instanceof Long)) {
                long id = (Long) object;

                Log.d(TAG, "onItemSwitchChange id = " + id + " position = " + position);
                Alarm alarm = (mListAdapter == null) ? null : mListAdapter.getAlarmById(id);
                if (alarm != null) {
                    if (alarm.getmGarbSwitch() == 1 && checked) {
                        if (GarbAlarmUtils.isGarbAlarmRingTimeExpired(alarm) || GarbAlarmUtils.isGarbAlarmTimeExpired(alarm)) {
                            ToastManager.showToast(
                                    compoundButton.getContext(),
                                    mContext.getString(R.string.grab_alarm_list_expiration_time)
                            );
                            return;
                        }
                    }

                    asyncSetAlarmEnabled(alarm, checked, compoundButton, position, this);
                }
            } else {
                Log.d(TAG, "onCheckedChanged error: No alarm id found in view's tag!");
            }
        } else {
            if (mListAdapter != null) {
                Log.i(TAG, "onItemSwitchChange notifyDataSetChanged");
                mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(position));
            }
        }
    }

    @Override
    public void delete() {
        deleteSelectedAlarms();
    }

    @Override
    public String getDeleteTitle() {
        return getDeleteTitle(getSelectCount(), getCurrentCount());
    }

    public int getSelectCount() {
        return (mListAdapter == null) ? 0 : mListAdapter.getSelectedIds().size();
    }

    private static class AlarmAsyncTask extends AsyncTask<Void, Void, Boolean> {

        private WeakReference<Alarm> mAlarmWeakReference;
        private WeakReference<CompoundButton> mCompoundButtonWeakReference;
        private WeakReference<AlarmListAdapter> mAdapterWeakReference;
        private WeakReference<AlarmClockFragment> mFragmentWeakReference;
        private int mPosition;
        private boolean mEnabled;
        private Handler mHandler = new Handler(Looper.getMainLooper());


        public AlarmAsyncTask(Alarm alarm, boolean enabled, CompoundButton compoundButton,
                              int position, AlarmListAdapter adapter, AlarmClockFragment fragment) {

            mAlarmWeakReference = new WeakReference<>(alarm);
            mCompoundButtonWeakReference = new WeakReference<>(compoundButton);
            mAdapterWeakReference = new WeakReference<>(adapter);
            mFragmentWeakReference = new WeakReference<>(fragment);
            this.mPosition = position;
            this.mEnabled = enabled;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            Log.i(TAG, " onPreExecute sActionForChange = false");
            sActionForChange = false;
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            boolean success = true;
            Alarm alarm = mAlarmWeakReference.get();
            if (alarm != null) {
                Alarm preAlarm = null;
                try {
                    preAlarm = alarm.clone();
                } catch (CloneNotSupportedException e) {
                    e.printStackTrace();
                }
                alarm.setmCloseOnceTimeNext(0);
                alarm.setmCloseOncePriTime(0);
                Context context = AlarmClockApplication.getInstance().getApplicationContext();
                Log.i(TAG, "doInBackground mEnabled = " + mEnabled);
                alarm.setEnabled(mEnabled);
                if (mEnabled) {
                    success = AlarmUtils.enableAlarm(context, alarm, true);
                } else {
                    success = AlarmUtils.disableAlarm(context, alarm.getId(), false);
                }

                if (success) {
                    //Update the state, since we don't reload the alarm data from db for efficiency.
                    if (alarm.getmGarbSwitch() == 1) {
                        GarbAlarmSeedlingHelper.closeSingleSeedlingCard(context, alarm.getId());
                    }
                    if (mEnabled) {
                        AlarmRingOperateUtils.openAlarm(alarm);
                    } else {
                        AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.CLOSE_ALARM_LIST);
                    }
                    AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(System.currentTimeMillis(),
                            ((preAlarm == null) ? alarm : preAlarm), alarm, mEnabled);
                }
                AlarmClockFragment fragment = mFragmentWeakReference.get();
                if (fragment != null) {
                    fragment.sendAlarmStatusMsg(AlarmUtils.getAllAlarms(context));
                }
            }
            return success;
        }

        @Override
        protected void onPostExecute(Boolean success) {
            super.onPostExecute(success);

            final Alarm alarm = mAlarmWeakReference.get();
            if (alarm != null) {
                if (success) {
                    if (mEnabled) {
                        mHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (mCompoundButtonWeakReference.get() != null) {
                                    AlarmUtils.popAlarmSetToast(mCompoundButtonWeakReference.get().getContext(), alarm);
                                }
                            }
                        }, TOAST_DELAYED_TIME);
                    }
                    AlarmListAdapter adapter = mAdapterWeakReference.get();

                    Log.i(TAG, "onPostExecute ----- mEnabled = " + mEnabled);
                    if (adapter != null) {
                        if ((adapter.getList() != null) && (adapter.getList().size() > mPosition)) {
                            adapter.getList().get(mPosition).setEnabled(mEnabled);
                            Log.i(TAG, "onPostExecute notifyDataSetChanged");
                            adapter.notifyDataSetChanged();
                        }
                    }
                } else {
                    CompoundButton btn = mCompoundButtonWeakReference.get();
                    if (btn != null) {
                        btn.setChecked(!mEnabled);
                    }
                    AlarmListAdapter adapter = mAdapterWeakReference.get();
                    if (adapter != null) {
                        Log.i(TAG, "onPostExecute notifyDataSetChanged");
                        adapter.notifyItemChanged(adapter.getViewPosition(mPosition));
                    }
                    Log.d(TAG, mEnabled ? "Enable" : "Disable" + " Alarm: " + alarm + " Failed!");
                }
            }
            Log.i(TAG, " onPreExecute sActionForChange = true");
            sActionForChange = true;
        }
    }

    private void asyncSetAlarmEnabled(Alarm alarm, boolean enabled, CompoundButton compoundButton, int position, AlarmClockFragment fragment) {
        AlarmAsyncTask loadTask = new AlarmAsyncTask(alarm, enabled, compoundButton, position, mListAdapter, fragment);
        loadTask.execute();
    }


    @Override
    public int getCurrentCount() {
        return (mListAdapter == null) ? 0 : mListAdapter.getList().size();
    }

    protected List<Alarm> getAlarmList() {
        return (mListAdapter == null) ? null : mListAdapter.getList();
    }

    private void deleteSelectedAlarms() {
        Long[] alarmIds = (mListAdapter == null) ? null : mListAdapter.getSelectedIds().toArray(new Long[0]);
        if ((alarmIds != null) && (alarmIds.length >= MIN_COUNT_TO_SHOW_LOADING)) {
            if (mLoadingView == null) {
                initLayoutEmpty();
            }
            if (mLoadingView != null) {
                mLoadingView.setVisibility(View.VISIBLE);
            }
        }
        AlarmUtils.asyncDeleteAlarms(getActivity().getApplicationContext(), alarmIds);
    }

    //Used for test.
    private void test() {
        Intent startIntent = new Intent(this.getActivity(), AlarmRingForOther.class);
        //startIntent.putExtra(AlarmRingForOther.RING_URI, "content://media/internal/audio/media/155");
        startActivity(startIntent);
        getActivity().overridePendingTransition(R.anim.coui_push_up_enter_activitydialog, R.anim.coui_zoom_fade_enter);
    }

    public void startVoiceSetAlarm() {
        Utils.startAiClock(getActivity());
    }

    public static void startAlarmSetActivity(Activity activity, Alarm alarm, int count, boolean isNeedDelay, Bundle extraBundle) {
        long alarmId = (alarm != null) ? alarm.getId() : -1;
        Log.d(TAG, "startAlarmSetActivity: " + alarm + ", alarmId: " + alarmId + "  isNeedDelay = " + isNeedDelay);

        if (activity != null) {
            Intent intent = new Intent();
            intent.putExtra(ClockConstant.ALARM_ID, alarmId);
            if (alarm != null) {
                intent.putExtra(ClockConstant.IS_LOOP_ALARM, alarm.getmLoopSwitch() == 1);
            }
            intent.putExtra(ClockConstant.ALARM_COUNT, count);
            Intent aiIntent = AiTripHelper.getAiTripIntent(activity);
            intent.putExtras(aiIntent);
            intent.setAction(ClockEventDispatcher.Companion.getDispatchAction(activity));

            Alarm result = null;
            try {
                if (alarm != null) {
                    result = alarm.clone();
                }
            } catch (CloneNotSupportedException e) {
                e.printStackTrace();
            }
            intent.putExtra(ALARM_MODIFY, (result == null) ? alarm : result);
            intent.putExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, AlarmClock.sStartFromScreen);

            AlarmClock main = (AlarmClock) activity;
            if ((main != null) && (!main.isFinishing())) {
                main.openAlarmSetPage(intent, isNeedDelay);
            }
        }
    }

    public static void startAlarmSetActivity(Activity activity, Alarm alarm, int count, boolean isNeedDelay) {
        startAlarmSetActivity(activity, alarm, count, isNeedDelay, null);
    }

    /**
     * @param operatedAlarmId 上次被操作的alarm id
     */
    public static void asyncLoadAllAlarms(AlarmClockFragment fragment, int operatedAlarmId) {
        new LoadAlarmTask(fragment, operatedAlarmId).execute();
    }


    private String getAlarmStatus(List<Alarm> alarms) {

        if ((alarms == null) || alarms.isEmpty()) {
            return "";
        }

        Context context = AlarmClockApplication.getInstance();
        final AlarmSchedule nextSchedule = ScheduleUtils.getNextFiringAlarm(context);
        if (nextSchedule == null) {
            return context.getResources().getString(R.string.all_alarm_closed);
        } else {
            Log.d(TAG, "---顶部倒计时，下次响铃时间：" + Formatter.formatTime(nextSchedule.getTime()));
            return AlarmUtils.getElapsedTimeUntilNextAlarmDescription(context, nextSchedule.getTime(), true);
        }
    }

    private void sendAlarmStatusMsg(List<Alarm> alarms) {
        if (mAlarmClockHandler != null) {
            Message.obtain(mAlarmClockHandler, ALARM_STATUS_WHAT, getAlarmStatus(alarms)).sendToTarget();
        }
    }


    private static class LoadAlarmTask extends AsyncTask<Void, Void, ArrayList<Alarm>> {

        private WeakReference<AlarmClockFragment> mWeakRef;
        private int mOperatedAlarmId = NO_ALARM_ID;

        private LoadAlarmTask(AlarmClockFragment fragment, int operatedAlarmId) {
            mWeakRef = new WeakReference<>(fragment);
            mOperatedAlarmId = operatedAlarmId;
        }

        private AlarmClockFragment getFragment() {
            return (mWeakRef == null) ? null : mWeakRef.get();
        }

        private Activity getActivity(Fragment fragment) {
            return ((fragment == null) || !fragment.isAdded()) ? null : fragment.getActivity();
        }

        @Override
        protected ArrayList<Alarm> doInBackground(Void... voids) {
            AlarmClockFragment fragment = getFragment();
            Activity activity = getActivity(fragment);
            if (activity != null) {
                return AlarmUtils.getAllAlarms(activity);
            }
            return null;
        }

        @Override
        protected void onPostExecute(ArrayList<Alarm> list) {
            if (list != null) {
                AlarmClockFragment fragment = getFragment();
                Activity activity = getActivity(fragment);
                if (activity != null) {
                    fragment.dismissDialogForCloseOnce();
                    if (fragment.mMode == MODEL_EDIT) {
                        fragment.onEditAlarmsLoadComplete(activity, list, mOperatedAlarmId);
                    } else {
                        fragment.onAlarmsLoadComplete(activity, list, mOperatedAlarmId);
                    }
                    fragment.sendAlarmStatusMsg(list);
                }
            }
        }
    }

    /**
     * @param operatedAlarmId 上次被操作的alarm id
     */
    private void onAlarmsLoadComplete(Context context, ArrayList<Alarm> list, int operatedAlarmId) {
        if (list == null) {
            list = new ArrayList<>();
        }
        statisticsAlarmCount(context, list);
        if (mToolbar != null) {
            mToolbar.setVisibility(View.VISIBLE);
        }
        if (mCollapsingToolbarLayout != null) {
            mCollapsingToolbarLayout.setVisibility(View.VISIBLE);
        }
        mAlarmList = list;
        if (mEmptyTextView == null) {
            initLayoutEmpty();
        }
        if ((mAlarmsList == null) || (mListAdapter == null)) {
            initLayoutAlarmList();
        }
        initializeFloatingButton(list);
        if (list.size() == 0) {
            if (mEmptyTextView != null) {
                mEmptyTextView.setVisibility(View.VISIBLE);
                mViewEmpty.setVisibility(View.VISIBLE);
                playEmptyAnimOrShowEmptyIcon(mViewEmpty, mEmptyTextView, AlarmClock.TAB_INDEX_ALARMCLOCK);
            }
            if (mAlarmsList != null) {
                mAlarmsList.setVisibility(View.GONE);
            }
        } else {
            if ((mAlarmsList != null) && (mAlarmsList.getVisibility() != View.VISIBLE)) {
                mAlarmsList.setVisibility(View.VISIBLE);
            }
            if ((mEmptyTextView != null) && (mEmptyTextView.getVisibility() != View.GONE)) {
                mEmptyTextView.setVisibility(View.GONE);
                mViewEmpty.setVisibility(View.GONE);
            }
        }
        if (mListAdapter != null) {
            Log.i(TAG, "onAlarmsLoadComplete notifyDataSetChanged mMarkedNeedQuitEditMode : "
                    + mMarkedNeedQuitEditMode + "  listSize:" + list.size());
            if (mMarkedNeedQuitEditMode) {
                mMarkedNeedQuitEditMode = false;
                mListAdapter.updateData(list);
                clearEdit();
            } else {
                DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffCallBack(
                        mListAdapter.getList(), list, mListAdapter.getHeaderCount()), true);
                diffResult.dispatchUpdatesTo(mListAdapter);
                mListAdapter.updateData(list);
            }
            if (UiMode.LARGE_VERTICAL == getUiMode()) {
                if (list.size() > 0 && list.size() <= AlarmListAdapter.LIST_ITEM_OSLO_PORTRAIT_COUNT_MAX) {
                    setRecyclerViewLayoutManager();
                }
            }

            mListAdapter.notifyDataSetChanged();
        }
        if (mLoadingView == null) {
            initLayoutEmpty();
        }
        if ((mLoadingView != null) && (mLoadingView.getVisibility() == View.VISIBLE)) {
            mLoadingView.setVisibility(View.GONE);
        }
        updateMenu();

        if ((operatedAlarmId != NO_ALARM_ID) && Utils.isTalkBackOpen() && (mListAdapter != null)
                && (mListAdapter.getAlarmById(operatedAlarmId) != null) && (mAlarmsList != null)) {
            mAlarmsList.postDelayed(() -> {
                RecyclerView.ViewHolder viewHolder = mAlarmsList.findViewHolderForItemId(operatedAlarmId);
                if ((viewHolder != null) && (viewHolder.itemView != null)) {
                    Log.d(TAG, "talkback item selected id: " + operatedAlarmId);
                    viewHolder.itemView.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
                    viewHolder.itemView.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_SELECTED);
                }
            }, TALK_BACK_DELAY);
        }
        WPlusNotifyUtils.showMigrateSuccessTips(mView);
        //重启稍后提醒流体云服务
        if ((mAlarmsList != null)
                && VersionUtils.isOsVersion15()
                && TimerSeedlingHelper.isSupportFluidCloud()
                && (mContext != null)
                && AlarmSnoozeService.alarmSnoozeListIsNull()) {
            mAlarmsList.post(() -> AlarmSnoozeServiceUtils.restartAlarmSnoozeService(mContext));
        }
        if (mIsNeedBackPosition && mAlarmId != -1) {
            backToPosition(getAlarmPosition(mAlarmId) + 2);
            mIsNeedBackPosition = false;
        }
    }

    void onEditAlarmsLoadComplete(Context context, ArrayList<Alarm> list, int operatedAlarmId) {

        if (mEmptyTextView == null) {
            initLayoutEmpty();
        }
        if ((mAlarmsList == null) || (mListAdapter == null)) {
            initLayoutAlarmList();
        }
        initializeFloatingButton(list);
        if (mListAdapter != null) {
            if (mListAdapter.getSelectedIds().isEmpty()) {
                for (Alarm alarm : list) {
                    alarm.setSelected(false);
                }
            } else {
                Set<Long> set = new HashSet<>();
                boolean selected = false;
                for (Alarm alarm : list) {
                    long alarmId = alarm.getId();
                    selected = mListAdapter.getSelectedIds().contains(alarmId);
                    alarm.setSelected(selected);
                    if (selected) {
                        set.add(alarmId);
                    }
                }
                mListAdapter.getSelectedIds().clear();
                mListAdapter.getSelectedIds().addAll(set);
            }
        }
        onAlarmsLoadComplete(context, list, operatedAlarmId);
        updateEditMenuAndTitle();
    }

    private static class AlarmContentObserver extends ContentObserver {

        WeakReference<AlarmClockFragment> mAlarmClockFragmentWeakReference;

        public AlarmContentObserver(AlarmClockFragment fragment) {
            super(null);
            mAlarmClockFragmentWeakReference = new WeakReference<>(fragment);
        }

        /**
         * Android API level 30 新增的回调方法，通过  flags
         * 判断是否为主动调用 notifyChange（）更新的消息
         *
         * @param selfChange
         * @param uri
         * @param flags      ContentResolver.NOTIFY_SYNC_TO_NETWORK,
         *                   ContentResolver.NOTIFY_SKIP_NOTIFY_FOR_DESCENDANTS,
         *                   ContentResolver.NOTIFY_INSERT,
         *                   ContentResolver.NOTIFY_UPDATE,
         *                   ContentResolver.NOTIFY_DELETE
         *                   ContentResolver.NOTIFY_NO_DELAY 主动调用的notifyChange()的消息
         */
        @Override
        public void onChange(boolean selfChange, Uri uri, int flags) {
            super.onChange(selfChange, uri, flags);
            final boolean change = (flags & AlarmUtils.NOTIFY_NO_DELAY) != 0;
            Log.d(TAG, "update db: sActionForChange : " + sActionForChange + "  flags : " + flags + "  uri : " + uri);
            if (change) {
                if (sActionForChange) {
                    int alarmId = NO_ALARM_ID;
                    try {
                        alarmId = Integer.parseInt(uri.getLastPathSegment());
                    } catch (Exception e) {
                        Log.d(TAG, "onChange e " + e.getMessage());
                    }

                    AlarmClockFragment alarmClockFragment = mAlarmClockFragmentWeakReference.get();
                    if ((alarmClockFragment != null) && (alarmClockFragment.getActivity() != null)
                            && (!alarmClockFragment.getActivity().isFinishing())) {
                        asyncLoadAllAlarms(alarmClockFragment, alarmId);
                    }
                } else {
                    sActionForChange = true;
                }
            }
        }

        @Override
        public void onChange(boolean selfChange, @Nullable Uri uri) {
            super.onChange(selfChange, uri);
        }

    }

    private static class ScheduleContentObserver extends ContentObserver {

        WeakReference<AlarmClockFragment> mAlarmClockFragmentWeakReference;

        public ScheduleContentObserver(AlarmClockFragment fragment) {
            super(null);
            mAlarmClockFragmentWeakReference = new WeakReference<>(fragment);
        }

        /**
         * Android API level 30 新增的回调方法，通过  flags
         * 判断是否为主动调用 notifyChange（）更新的消息
         *
         * @param selfChange
         * @param uri
         * @param flags      ContentResolver.NOTIFY_SYNC_TO_NETWORK,
         *                   ContentResolver.NOTIFY_SKIP_NOTIFY_FOR_DESCENDANTS,
         *                   ContentResolver.NOTIFY_INSERT,
         *                   ContentResolver.NOTIFY_UPDATE,
         *                   ContentResolver.NOTIFY_DELETE
         *                   ContentResolver.NOTIFY_NO_DELAY 主动调用的notifyChange()的消息
         */
        @Override
        public void onChange(boolean selfChange, Uri uri, int flags) {
            super.onChange(selfChange, uri, flags);
            final boolean change = (flags & AlarmUtils.NOTIFY_NO_DELAY) != 0;
            Log.d(TAG, "update db: sActionForChange : " + sActionForChange + "  flags : " + flags + "  uri : " + uri);
            AlarmClockFragment alarmClockFragment = mAlarmClockFragmentWeakReference.get();
            if (change && (alarmClockFragment != null) && alarmClockFragment.getUserVisibleHint()) {
                alarmClockFragment.sendAlarmStatusMsg(alarmClockFragment.mAlarmList);
            }
        }

        @Override
        public void onChange(boolean selfChange, @Nullable Uri uri) {
            super.onChange(selfChange, uri);
        }

    }

    private static void statisticsAlarmCount(Context context, ArrayList<Alarm> list) {
        if (list != null) {
            HashMap<String, String> map = new HashMap<>();
            map.put(ClockOplusCSUtils.EVENT_ALARM_NUMS_KEY, String.valueOf(list.size()));
            dealVibrateType(context, map, list);
            ClockOplusCSUtils.onCommon(context, ClockOplusCSUtils.EVENT_ALARM_NUMS_ON_CREATE, map);
        }
    }

    private static void dealVibrateType(Context context, HashMap<String, String> map, ArrayList<Alarm> list) {
        if (!DeviceUtils.isLinearmotoSupport(context)) {
            return;
        }
        if ((list != null) && (list.size() > 0)) {
            int[] vibrate = new int[7];
            for (Alarm alarm : list) {
                int v = alarm.getVibrate();
                if (v == WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE) {
                    vibrate[0]++;
                } else if (v == WaveformEffect.EFFECT_NOTIFICATION_SYMPHONIC) {
                    vibrate[1]++;
                } else if (v == WaveformEffect.EFFECT_NOTIFICATION_STREAK) {
                    vibrate[2]++;
                } else if (v == WaveformEffect.EFFECT_NOTIFICATION_HEARTBEAT) {
                    vibrate[3]++;
                } else if (v == WaveformEffect.EFFECT_NOTIFICATION_REMIND) {
                    vibrate[4]++;
                } else if (v == WaveformEffect.EFFECT_NOTIFICATION_RAPID) {
                    vibrate[5]++;
                } else if (v == WaveformEffect.EFFECT_RINGTONE_NOVIBRATE) {
                    vibrate[6]++;
                } else {
                    vibrate[0]++;
                }
            }
            String vibrateKey = "event_alarm_vibrate%d_key";
            for (int i = 0; i < vibrate.length; i++) {
                if (vibrate[i] > 0) {
                    map.put(String.format(Locale.ENGLISH, vibrateKey, i + 1), String.valueOf(vibrate[i]));
                }
            }
        }

    }

    public void resetCurrentClickItem() {
        if ((mCurrentClickItem >= 0) && mIsCheckByPermission && (mListAdapter != null) && (mListAdapter.getList().size() > mCurrentClickItem)) {
            Log.i(TAG, "resetCurrentClickItem notifyDataSetChanged");
            mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(mCurrentClickItem));
        }
        mCurrentClickItem = -1;
        mIsCheckByPermission = false;
        sActionForChange = true;
    }


    public void dismissDialogForCloseOnce() {
        if ((mCloseOnceAlertDialog != null) && mCloseOnceAlertDialog.isShowing()) {
            mCloseOnceAlertDialog.dismiss();
            mCurrentClickItem = -1;
        }
    }

    public void onActivityNewIntent() {
        Log.d(TAG, "activity new intent");
        if (mListAdapter != null) {
            mListAdapter.checkQuestionnaireShow(false);
        }
    }

    /**
     * show dialog for close alarm
     *
     * @param compoundButton Switch button
     * @param position       the position for current touch item
     * @return need show dialog view
     */
    @SuppressLint("NewApi")
    private AlertDialog getCloseClockDialog(final CompoundButton compoundButton, final int position, final boolean isTalkBackOpen) {
        Activity activity = getActivity();
        if ((activity == null) || activity.isFinishing() && (mListAdapter == null)) {
            Log.e(TAG, "getCloseClockDialog NULL");
            return null;
        }

        String[] items = new String[2];
        List<Alarm> listAlarm = mListAdapter.getList();
        if (listAlarm.size() > position) {
            final List<AlarmSchedule> alarmScheduleList = ScheduleUtils.getSchedulesOfAlarm(
                    AlarmClockApplication.getInstance(), mListAdapter.getList().get(position).getId());
            if ((alarmScheduleList != null) && (alarmScheduleList.size() > 0)) {
                final AlarmSchedule alarmSchedule = alarmScheduleList.get(0);
                if (alarmSchedule != null) {
                    Log.i(TAG, "AlarmClockFragment  createScheduleForAlarm  alarmSchedule.getAlarmState() = " + alarmSchedule.getAlarmState());

                    AlarmSchedule currentSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();
                    if ((alarmSchedule.getAlarmState() > ClockContract.Schedule.SILENT_STATE) || ((currentSchedule != null) && (currentSchedule.getAlarmId() == alarmSchedule.getAlarmId()))) { //snooze state or other not of normal
                        //there need close this time and next time alarm , so need get next alarm time
                        Log.i(TAG, "AlarmClockFragment  createScheduleForAlarm");
                        AlarmSchedule newInstance = AlarmUtils.createScheduleForAlarm(alarmSchedule.getAlarm());
                        items[0] = Utils.getDialogDayDiffString(newInstance.getTime());
                    } else {
                        items[0] = Utils.getDialogDayDiffString(alarmSchedule.getTime());
                    }

                    items[1] = activity.getString(R.string.close_this_repeat_alarm);

                    final int DIALOG_ITEM_CLOSE_ONCE_TIME = 0;
                    final int DIALOG_ITEM_CLOSE_ALARM = 1;
                    return new COUIAlertDialogBuilder(activity)
                            .setTitle(getResources().getString(R.string.close_repeat_alarm_dialog_title))
                            .setNeutralButton(items[0], new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    closeOnce(isTalkBackOpen, alarmSchedule, position);
                                    mCurrentClickItem = -1;
                                }
                            })
                            .setBlurBackgroundDrawable(false)
                            .setPositiveButton(items[1], (dialog, which) -> {
                                Log.i(TAG, "getCloseClockDialog click item -- close alarm ");
                                //close alarm
                                onItemSwitchChange(compoundButton, false, position);
                                //near me close alarm
                                ClockOplusCSUtils.statisticsTurnOffRepeatAlarm(AlarmClockApplication.getInstance(),
                                        ClockOplusCSUtils.TURN_OFF_REPEAT_ALARM_VALUE_CLOSE_ALARM);
                                mCurrentClickItem = -1;
                            })
                            .setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {

                                @Override
                                public void onClick(DialogInterface dialog, int whichButton) {
                                    /* User clicked OK so do some stuff */
                                    //near me do nothing (close dialog)
                                    Log.i(TAG, "getCloseClockDialog click item -- cancel button ");
                                    ClockOplusCSUtils.statisticsTurnOffRepeatAlarm(AlarmClockApplication.getInstance(), ClockOplusCSUtils.TURN_OFF_REPEAT_ALARM_VALUE_DO_NOTHING);
                                    mCurrentClickItem = -1;
                                    if (isTalkBackOpen && (mListAdapter != null)) {
                                        mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(position));
                                    }
                                }
                            })
                            .setOnCancelListener(new DialogInterface.OnCancelListener() {
                                @Override
                                public void onCancel(DialogInterface dialog) {
                                    mCurrentClickItem = -1;
                                    if (isTalkBackOpen && (mListAdapter != null)) {
                                        mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(position));
                                    }
                                }
                            })
                            .create();
                } else {
                    Log.e(TAG, "getCloseClockDialog alarmSchedule is null");
                }
            } else {
                Log.e(TAG, "getCloseClockDialog alarmScheduleList is empty");
            }
        } else {
            Log.e(TAG, "getCloseClockDialog listAlarm.size() > position ");
        }
        return null;
    }


    private void closeOnce(final boolean isTalkBackOpen, final AlarmSchedule alarmSchedule, final int position) {
        PlatformUtils.sUpdateType = PlatformUtils.UPDATE_TYPE_OPERATION_CLOSE_ALARM;
        Log.i(TAG, "getCloseClockDialog click item -- close once time ");
        if (mListAdapter != null) {
            mListAdapter.mCloseOnceId = alarmSchedule.getAlarmId();
        }
        // close once  or cancel next alarm
        AsyncHandler.post(new Runnable() {
            @Override
            public void run() {
                //close once alarm
                Log.i(TAG, " getCloseClockDialog sActionForChange = false" + "   alarmSchedule = " + Formatter.formatTime(alarmSchedule.getTime()));
                if (!isTalkBackOpen) {
                    sActionForChange = false;
                }
                AlarmSchedule currentRingSchedule = CurrentAlarmScheduleHolder.getAlarmSchedule();

                long alarmScheduleTime = alarmSchedule.getTime();
                Log.i(TAG, "getCloseClockDialog alarmScheduleTime current time = " + Formatter.formatTime(alarmScheduleTime));

                  /*
                  is ring or snooze need close next alarm
                  */
                if ((alarmSchedule.getAlarmState() != ClockContract.Schedule.SILENT_STATE) || (currentRingSchedule != null)) {
                    //如果是下一个闹钟是正在响铃的闹铃或者是稍后提醒的闹钟
                    //则获取相对于当前时间的下一个闹钟
                    alarmScheduleTime = AlarmUtils.createScheduleForAlarm(alarmSchedule.getAlarm()).getTime();
                }
                Alarm preAlarm = null;
                try {
                    preAlarm = alarmSchedule.getAlarm().clone();
                } catch (CloneNotSupportedException e) {
                    e.printStackTrace();
                }
                //上一个闹钟的时间
                long previousAlarmTime = AlarmUtils.getPreviousAlarmTimeForRepeatAndWorkDay(alarmSchedule.getAlarm(), alarmScheduleTime, "getCloseClockDialog");

                Log.e(TAG, "---getCloseClockDialog previousAlarmTime = " + previousAlarmTime + "      " + Formatter.formatTime(previousAlarmTime)
                        + "  nextAlarmSchedule = " + alarmScheduleTime + "   " + Formatter.formatTime(alarmScheduleTime));

                AlarmStateManager.setDismissStateCloseOnceNext(AlarmClockApplication.getInstance(), alarmSchedule, alarmScheduleTime, previousAlarmTime);
                AlarmRingOperateUtils.closeAlarm(alarmSchedule.getAlarm(), AlarmRingOperateUtils.CLOSE_ALARM_LIST);
                AlarmPreferenceUtils.Companion.getInstance().editAlarmInfo(System.currentTimeMillis(),
                        ((preAlarm == null) ? alarmSchedule.getAlarm() : preAlarm),
                        alarmSchedule.getAlarm(), false);
                //send message to notice next alarm has been closed
                Message message = Message.obtain();
                message.what = CLOSE_ONCE_WHAT;
                Bundle bundle = new Bundle();
                bundle.putParcelable(CLOSE_ONCE_KEY_ALARM_SCHEDULE, alarmSchedule);
                bundle.putParcelable(CLOSE_ONCE_KEY_ALARM_CURRENT_RING_SCHEDULE, currentRingSchedule);
                bundle.putLong(CLOSE_ONCE_KEY_NEXT_ALARM_TIME, alarmScheduleTime);
                bundle.putLong(CLOSE_ONCE_KEY_PREVIOUS_ALARM_TIME, previousAlarmTime);
                bundle.putInt(CLOSE_ONCE_KEY_POSITION, (int) alarmSchedule.getAlarm().getId());
                message.setData(bundle);
                if (mAlarmClockHandler != null) {
                    mAlarmClockHandler.sendMessage(message);
                } else {
                    Log.d(TAG, "mAlarmClockHandler is null");
                }
            }
        });
    }


    private void closeOnceViewRefresh(final long alarmScheduleTime, final int alarmId, long nextAlarmTime, long previousAlarmTime) {
        if ((mListAdapter != null) && (mListAdapter.getList() != null)) {
            Log.i(TAG, "getCloseClockDialog  notifyDataSetChanged:" + alarmId);
            List<Alarm> alarmList = mListAdapter.getList();
            if ((alarmList != null) && (alarmList.size() > 0)) {
                for (int i = 0; i < alarmList.size(); i++) {
                    if (alarmId == alarmList.get(i).getId()) {
                        alarmList.get(i).setmCloseOncePriTime(previousAlarmTime);
                        alarmList.get(i).setmCloseOnceTimeNext(nextAlarmTime);
                        mListAdapter.notifyItemChanged(mListAdapter.getViewPosition(i));
                        long alarmTime = Utils.getNextAlarmTime(getActivity(), mListAdapter.getList().get(i));
                        if (alarmTime != 0) {
                            ToastManager.showToast(mAlarmsList.getContext(), Utils.getNextAlarmTipsString(alarmScheduleTime, alarmTime));
                        }
                        break;
                    }
                }
            }
        }
        //near me close once
        ClockOplusCSUtils.statisticsTurnOffRepeatAlarm(AlarmClockApplication.getInstance(), ClockOplusCSUtils.TURN_OFF_REPEAT_ALARM_VALUE_CLOSE_ONCE);
    }

    @Override
    public boolean isEditMode() {
        return mMode == MODEL_EDIT;
    }

    public int getAlarmSelectCount() {
        return (mListAdapter == null) ? 0 : mListAdapter.getSelectedIds().size();
    }

    private boolean isListAnimating() {
        return (mListAdapter != null) && mListAdapter.isAnimating();
    }


    /**
     * 平板横竖屏切换回调
     */
    @Override
    public void onScreenOrientationChanged(int orientation) {
        super.onScreenOrientationChanged(orientation);
        if ((getActivity() != null) && (mAlarmsList != null)) {
            if (mListAdapter != null) {
                mListAdapter.setUiMode(getUiMode());
            }
            setRecyclerViewLayoutManager();
        }
    }
}
