/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : The AlarmSchedule Object for the AlarmClock application An enabled alarm will
 * create an AlarmSchedule which will ring in a specific time.
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-10, <PERSON>, create
 * v1.1, 2018-8-10, <PERSON><PERSON><PERSON>, clean the code.
 ************************************************************/

package com.oplus.alarmclock.alarmclock;

import android.database.Cursor;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.provider.ClockContract.Schedule;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;

public final class AlarmSchedule implements Parcelable {

    private static final String TAG = "AlarmSchedule";

    //Query from view_schedules.
    static final String[] SCHEDULE_PROJECTION = new String[]{
            //Schecule info.
            Schedule._ID,               //0
            Schedule.YEAR,
            Schedule.MONTH,
            Schedule.DAY,
            Schedule.HOUR,
            Schedule.MINUTES,
            Schedule.ALARM_TIME,
            Schedule.SNOOZETIME,
            Schedule.ALARM_STATE,
            Schedule.ALARM_ID,          //9
            //Alarm info.
            ClockContract.ScheduleView.ALARM_HOUR,    //10
            ClockContract.ScheduleView.ALARM_MINUTE,
            ClockContract.ScheduleView.ALARM_MILLS,
            ClockContract.Alarm.DAYS_OF_WEEK,
            ClockContract.Alarm.ENABLED,
            ClockContract.Alarm.ALERTTYPE,
            ClockContract.Alarm.MESSAGE,
            ClockContract.Alarm.ALERT,
            ClockContract.Alarm.VOLUME,
            ClockContract.Alarm.SNOOZE,
            ClockContract.Alarm.ALERT_RINGNAME,
            ClockContract.Alarm.DELETE_AFTER_USE,
            ClockContract.Alarm.VIBRATE
    };

    private static final int SCHEDULE_ID_INDEX = 0;
    private static final int YEAR_INDEX = 1;
    private static final int MONTH_INDEX = 2;
    private static final int DAY_INDEX = 3;
    private static final int HOUR_INDEX = 4;
    private static final int MINUTES_INDEX = 5;
    private static final int ALARM_TIME_INDEX = 6;
    private static final int SNOOZETIME_INDEX = 7;
    private static final int ALARM_STATE_INDEX = 8;
    private static final int ALARM_ID_INDEX = 9;
    private static final int ALARM_HOUR_INDEX = 10;
    private static final int ALARM_MINUTE_INDEX = 11;
    private static final int ALARM_MILLS_INDEX = 12;
    private static final int ALARM_DAYS_OF_WEEK_INDEX = 13;
    private static final int ALARM_ENABLED_INDEX = 14;
    private static final int ALARM_ALERTTYPE_INDEX = 15;
    private static final int ALARM_MESSAGE_INDEX = 16;
    private static final int ALARM_ALERT_INDEX = 17;
    private static final int ALARM_VOLUME_INDEX = 18;
    private static final int ALARM_SNOOZE_INDEX = 19;
    private static final int ALARM_ALERT_RINGNAME_INDEX = 20;
    private static final int ALARM_DELETE_AFTER_USE_INDEX = 21;
    private static final int ALARM_VIBRATE_INDEX = 22;

    // Fields:
    private long mId;
    private int mYear;
    private int mMonth;
    private int mDay;
    private int mHour;
    private int mMinute;
    private long mTime;
    private int mSnoonzeTime;
    private int mAlarmState;
    private long mAlarmId;
    private Alarm mAlarm;

    public AlarmSchedule() {
        //no-op
    }

    public static AlarmSchedule build(Calendar calendar) {
        AlarmSchedule schedule = new AlarmSchedule();
        schedule.mYear = calendar.get(Calendar.YEAR);
        schedule.mMonth = calendar.get(Calendar.MONTH);
        schedule.mDay = calendar.get(Calendar.DAY_OF_MONTH);
        schedule.mHour = calendar.get(Calendar.HOUR_OF_DAY);
        schedule.mMinute = calendar.get(Calendar.MINUTE);
        schedule.mTime = calendar.getTimeInMillis();
        schedule.mAlarmState = Schedule.SILENT_STATE;
        schedule.mSnoonzeTime = 0;
        return schedule;
    }

    AlarmSchedule(Parcel p) {
        if (p == null) {
            return;
        }
        mId = p.readLong();
        mYear = p.readInt();
        mMonth = p.readInt();
        mDay = p.readInt();
        mHour = p.readInt();
        mMinute = p.readInt();
        mTime = p.readLong();
        mAlarm = (Alarm) p.readParcelable(Alarm.class.getClassLoader());
        mSnoonzeTime = p.readInt();
        mAlarmState = p.readInt();
        mAlarmId = p.readLong();
    }

    public void setId(long id) {
        this.mId = id;
    }

    public long getId() {
        return mId;
    }

    public int getYear() {
        return mYear;
    }

    public void setYear(int year) {
        this.mYear = year;
    }

    public int getMonth() {
        return mMonth;
    }

    public void setMonth(int month) {
        this.mMonth = month;
    }

    public int getDay() {
        return mDay;
    }

    public void setDay(int day) {
        this.mDay = day;
    }

    public int getHour() {
        return mHour;
    }

    public void setHour(int hour) {
        this.mHour = hour;
    }

    public int getMinute() {
        return mMinute;
    }

    public void setMinute(int minute) {
        this.mMinute = minute;
    }

    public long getTime() {
        return mTime;
    }

    public void setTime(long time) {
        this.mTime = time;
    }

    public int getSnoonzeTime() {
        return mSnoonzeTime;
    }

    public void setSnoonzeTime(int snoonzeTime) {
        this.mSnoonzeTime = snoonzeTime;
    }

    public int getAlarmState() {
        return mAlarmState;
    }

    public void setAlarmState(int alarmState) {
        this.mAlarmState = alarmState;
    }

    public void setAlarm(Alarm alarm) {
        this.mAlarm = alarm;
    }

    public long getAlarmId() {
        return mAlarmId;
    }

    public void setAlarmId(long alarmId) {
        this.mAlarmId = alarmId;
    }

    public boolean isAlarmEnabled() {
        return ((mAlarm != null) && (mAlarm.isEnabled()));
    }

    public int getAlarmHour() {
        return ((mAlarm != null) ? mAlarm.getHour() : 0);
    }

    public int getAlarmMinute() {
        return ((mAlarm != null) ? mAlarm.getMinutes() : 0);
    }

    public int getAlarmAlertType() {
        return ((mAlarm != null) ? mAlarm.getAlertType() : -1);
    }

    public String getAlarmLabel() {
        return ((mAlarm != null) ? mAlarm.getLabel() : "");
    }

    public int getWorkdaySwitch() {
        return ((mAlarm != null) ? mAlarm.getWorkdaySwitch() : -1);
    }

    public Uri getAlarmAlert() {
        return ((mAlarm != null) ? mAlarm.getAlert() : null);
    }

    public int getAlarmVolume() {
        return ((mAlarm != null) ? mAlarm.getVolume() : 0);
    }

    //TODO: Delete this later.
    public Alarm getAlarm() {
        return mAlarm;
    }

    public int getAlarmSnooze() {
        return ((mAlarm != null) ? mAlarm.getSnoonzeItem() : 0);
    }

    public boolean isAlarmSilent() {
        return ((mAlarm != null) && (mAlarm.isSilent()));
    }

    public int getVibrate() {
        return ((mAlarm != null) ? mAlarm.getVibrate() : WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE);
    }

    public String getRingtoneName() {
        return ((mAlarm != null) ? mAlarm.getRingName() : null);
    }

    static AlarmSchedule fromCursor(Cursor c) {
        return createSchedule(c, null);
    }

    static AlarmSchedule fromCursorAndAlarm(Cursor c, Alarm alarm) {
        return createSchedule(c, alarm);
    }

    /**
     * 稍后提醒是否继续可用
     *
     * @param snoozeTime
     * @return
     */
    public boolean isSnoozeAvailble(int snoozeTime) {
        Log.e(TAG, "isSnoozeAvailble  getSnoonzeTime()" + getSnoonzeTime() + "   snoozeTime:" + snoozeTime);
        return !((getAlarmSnooze() == ClockConstant.SNOOZE_SWITCH_OFF)
                || (getSnoonzeTime() >= (snoozeTime - 1)));
    }

    public boolean isSnoozeAvailble(Alarm alarm) {
        if (alarm.getmGarbSwitch() == 1) {
            return !GarbAlarmUtils.isGarbAlarmRingTimeExpired(alarm);
        }
        int snoozeTime = alarm.getRingNum();
        Log.e(TAG, "isSnoozeAvailble  getSnoonzeTime()" + getSnoonzeTime() + "   snoozeTime:" + snoozeTime);
        return !((getAlarmSnooze() == ClockConstant.SNOOZE_SWITCH_OFF)
                || (getSnoonzeTime() >= (snoozeTime - 1)));
    }

    public boolean isCreatedInCTS() {
        return ("Create Alarm Test").equals(getAlarmLabel())
                || ("Start Alarm Test").equals(getAlarmLabel());
    }

    private static AlarmSchedule createSchedule(Cursor c, Alarm alarm) {
        final AlarmSchedule schedule = new AlarmSchedule();
        schedule.setId(c.getLong(SCHEDULE_ID_INDEX));
        schedule.setYear(c.getInt(YEAR_INDEX));
        schedule.setMonth(c.getInt(MONTH_INDEX));
        schedule.setDay(c.getInt(DAY_INDEX));
        schedule.setHour(c.getInt(HOUR_INDEX));
        schedule.setMinute(c.getInt(MINUTES_INDEX));
        schedule.setTime(c.getLong(ALARM_TIME_INDEX));
        schedule.setSnoonzeTime(c.getInt(SNOOZETIME_INDEX));
        schedule.setAlarmState(c.getInt(ALARM_STATE_INDEX));
        schedule.setAlarmId(c.getLong(ALARM_ID_INDEX));

        if (alarm != null) {
            try {
                schedule.setAlarm(alarm.clone());
            } catch (CloneNotSupportedException e) {
                Log.e(TAG, "createSchedule e: " + e.getMessage());
                schedule.setAlarm(getAlarmFromCursor(c));
            }
        } else {
            schedule.setAlarm(getAlarmFromCursor(c));
        }
        return schedule;
    }

    private static Alarm getAlarmFromCursor(Cursor c) {
        Alarm alarm = new Alarm();

        alarm.setId(c.getLong(ALARM_ID_INDEX));
        alarm.setHour(c.getInt(ALARM_HOUR_INDEX));
        alarm.setMinutes(c.getInt(ALARM_MINUTE_INDEX));
        alarm.setTime(c.getLong(ALARM_MILLS_INDEX));
        alarm.setRepeat(c.getInt(ALARM_DAYS_OF_WEEK_INDEX));
        alarm.setEnabled(c.getInt(ALARM_ENABLED_INDEX) == 1);
        alarm.setAlertType(c.getInt(ALARM_ALERTTYPE_INDEX));
        alarm.setLabel(c.getString(ALARM_MESSAGE_INDEX));
        alarm.setSnoonzeItem(c.getInt(ALARM_SNOOZE_INDEX));
        alarm.setRingName(c.getString(ALARM_ALERT_RINGNAME_INDEX));
        alarm.setVolume(c.getInt(ALARM_VOLUME_INDEX));
        alarm.setDeleteAfterUse(c.getInt(ALARM_DELETE_AFTER_USE_INDEX));
        alarm.setVibrate(c.getInt(ALARM_VIBRATE_INDEX));

        Alarm temp = AlarmUtils.getAlarm(AlarmClockApplication.getInstance(), alarm.getId());
        if (temp != null) {
            alarm.setWorkdaySwitch(temp.getWorkdaySwitch());
            alarm.setHolidaySwitch(temp.getHolidaySwitch());
            alarm.setOwnerUserId(temp.getOwnerUserId());
            alarm.setmCloseOnceTimeNext(temp.getmCloseOnceTimeNext());
            alarm.setmCloseOncePriTime(temp.getmCloseOncePriTime());
            alarm.setRepeat(temp.getRepeatSet());
            alarm.setEnableAssociate(temp.getEnableAssociate());
            alarm.setUUID(temp.getUUID());
            alarm.setmSnoozeTime(temp.getmSnoozeTime());
            alarm.setmWorkDayType(temp.getmWorkDayType());
            alarm.setmWorkdayUpdateTime(temp.getmWorkdayUpdateTime());
            alarm.setmSpecialAlarmDays(temp.getmSpecialAlarmDays());
            alarm.setmDefaultAlarm(temp.getmDefaultAlarm());
            alarm.setRingNum(temp.getRingNum());
            alarm.setmLoopSwitch(temp.getmLoopSwitch());
            alarm.setmLoopCycleDays(temp.getmLoopCycleDays());
            alarm.setmLoopID(temp.getmLoopID());
            alarm.setmLoopWorkDays(temp.getmLoopWorkDays());
            alarm.setmLoopAlarmNumber(temp.getmLoopAlarmNumber());
            alarm.setmLoopDay(temp.getmLoopDay());
            alarm.setmLoopRestDays(temp.getmLoopRestDays());
            alarm.setAlert(temp.getAlert());
            alarm.setRingAbsolutePath(temp.getRingAbsolutePath());
            alarm.setmGarbSwitch(temp.getmGarbSwitch());
            if (Utils.isAboveQ()) {
                Log.d(TAG, "alarm user:" + temp.getOwnerUserId() + ",current user" + AppPlatformUtils.getCurrentUser());
            }
        }

        final String alertString = c.getString(ALARM_ALERT_INDEX);
        if (ClockConstant.ALARM_ALERT_SILENT.equals(alertString)) {
            alarm.setSilent(true);
        } else {
            if (!TextUtils.isEmpty(alertString)) {
                alarm.setAlert(Uri.parse(alertString));
            }

            // If the database alert is null or it failed to parse, use the default alert.
            if (alarm.getAlert() == null) {
                alarm.setAlert(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM));
            }
        }

        Log.d(TAG, "Get Alarm from cursor: " + alarm);
        return alarm;
    }

    public static final Creator<AlarmSchedule> CREATOR = new Creator<AlarmSchedule>() {
        @Override
        public AlarmSchedule createFromParcel(Parcel p) {
            return new AlarmSchedule(p);
        }

        @Override
        public AlarmSchedule[] newArray(int size) {
            return new AlarmSchedule[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel p, int flags) {
        if (p == null) {
            return;
        }
        p.writeLong(mId);
        p.writeInt(mYear);
        p.writeInt(mMonth);
        p.writeInt(mDay);
        p.writeInt(mHour);
        p.writeInt(mMinute);
        p.writeLong(mTime);
        p.writeParcelable(mAlarm, flags);
        p.writeInt(mSnoonzeTime);
        p.writeInt(mAlarmState);
        p.writeLong(mAlarmId);
    }

    @Override
    public boolean equals(Object o) {
        return ((o != null) && (o instanceof AlarmSchedule) && (((AlarmSchedule) o).mId == mId));
    }

    @Override
    public int hashCode() {
        return Long.valueOf(mId).hashCode();
    }

    @Override
    public String toString() {
        return "AlarmSchedule{"
                + "mId=" + mId
                + ", mYear=" + mYear
                + ", mMonth=" + mMonth
                + ", mDay=" + mDay
                + ", mHour=" + mHour
                + ", mMinute=" + mMinute
                + ", mTime=" + mTime
                + ", mSnoonzeTime=" + mSnoonzeTime
                + ", mAlarmState=" + mAlarmState
                + ", mAlarmId=" + mAlarmId
                + ", mAlarm=" + mAlarm
                + '}';
    }
}
