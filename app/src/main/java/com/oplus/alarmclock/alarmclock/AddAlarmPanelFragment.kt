/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseVBActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: Ni<PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/3/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.OnClockInflateFinishedListener
import com.oplus.clock.common.event.LiteEventBus.Companion.instance
import com.oplus.clock.common.utils.Log

/**
 * 添加闹钟的Panel
 */
class AddAlarmPanelFragment(private val mIntent: Intent) : COUIPanelFragment(), OnClockInflateFinishedListener<AddAlarmFragment> {

    companion object {
        fun newInstance(bundle: Bundle, mIntent: Intent): AddAlarmPanelFragment {
            return AddAlarmPanelFragment(mIntent).apply {
                arguments = bundle
            }
        }
    }

    private val mContext = AlarmClockApplication.getInstance()
    private var mAddAlarmFragment: AddAlarmFragment? = null
    private var mDialog: COUIBottomSheetDialog? = null

    /**
     * 标题
     */
    private var mPanelTitle: String = ""
    private var mLoopPanelTitle = mContext.getString(R.string.loop_alarm_title)

    override fun initView(panelView: View) {
        initPage()
        initListener()
        initToolbar()
    }

    private fun initEvent() {
        instance.with(EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE, hashCode().toString()).observe(this) { o: Any? ->
            if (o is Boolean) {
                toolbar?.apply {
                    title = if (o) {
                        //轮班闹钟
                        mLoopPanelTitle
                    } else {
                        mPanelTitle
                    }
                }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        inflater.inflate(R.menu.activity_add_alarm_menu, menu)
    }

    override fun onInflateFinished(page: AddAlarmFragment?) {
        initEvent()
    }

    private fun initListener() {
        mDialog =
            ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
        mDialog?.apply {
            setFrameRate(false)
            setPanelBackgroundTintColor(
                ContextCompat.getColor(
                    mContext,
                    R.color.coui_color_background_elevatedWithCard
                )
            )
        }
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                mAddAlarmFragment?.mAddAlarmManager?.dimissLoopAlarmTips()
            }
            false
        }
        setPanelDragListener {
            mAddAlarmFragment?.canClosePanel() == true
        }
    }

    private fun initToolbar() {
        //隐藏把手
        hideDragView()
        titleView?.visibility = View.GONE
        toolbar?.visibility = View.GONE
        view?.findViewById<ViewGroup>(R.id.title_view_container)?.visibility = View.GONE

        mPanelTitle = if (mIntent != null && mIntent.getLongExtra(ClockConstant.ALARM_ID, -1L) > 0) {
            mContext.getString(R.string.set_alarm)
        } else {
            mContext.getString(R.string.create_alarm)
        }
        val lay = toolbar?.run { layoutParams as LinearLayout.LayoutParams }
        lay?.setMargins(0, 0, 0, 0)
        toolbar = toolbar?.apply {
            visibility = View.VISIBLE
            title = if (mIntent.getBooleanExtra(ClockConstant.IS_LOOP_ALARM, false)) {
                mContext.getString(R.string.loop_alarm_title)
            } else {
                mPanelTitle
            }
            arguments?.let {
                val alarm = it.getParcelable<Alarm>(AddAlarmManager.KEY_SAVED_ALARM)
                if (alarm != null && alarm.getmLoopSwitch() == 1) {
                    title = mContext.getString(R.string.loop_alarm_title)
                }
            }
            isTitleCenterStyle = true
            inflateMenu(R.menu.activity_add_alarm_menu)
            menu.findItem(R.id.cancel).apply {
                val str = "${getString(R.string.cancel)}  ${getString(R.string.talk_back_voice_btn)}"
                contentDescription = str
                setOnMenuItemClickListener {
                    mAddAlarmFragment?.mAddAlarmManager?.cancel()
                    true
                }
            }
            //保存闹钟
            menu.findItem(R.id.save).apply {
                val str = "${getString(R.string.mini_app_btn_finish)}  ${getString(R.string.talk_back_voice_btn)}"
                contentDescription = str
                setOnMenuItemClickListener {
                    mAddAlarmFragment?.mAddAlarmManager?.confirm()
                    true
                }
            }
        }
    }

    private fun initPage() {
        runCatching {
            mAddAlarmFragment = AddAlarmFragment.getInstance(arguments, mIntent, this)
            mAddAlarmFragment?.let {
                childFragmentManager.beginTransaction().replace(contentResId, it)
                        .commitNowAllowingStateLoss()
            }
        }.onFailure {
            Log.e("AddAlarmPanelFragment", "initPage:e${it.message}")
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        mAddAlarmFragment?.let {
            childFragmentManager.beginTransaction().remove(it)
                    .commitAllowingStateLoss()
        }
        outSideViewOnTouchListener = null
        setPanelDragListener(null)
        dialogOnKeyListener = null
        mDialog?.setOnDismissListener(null)
        toolbar = null
    }

    private fun setCancelable(cancelable: Boolean) {
        (parentFragment as? COUIBottomSheetDialogFragment)?.isCancelable = cancelable
    }

    fun getAddAlarmFragment(): AddAlarmFragment? {
        return mAddAlarmFragment
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(
                ContextCompat.getColor(
                    mContext,
                    R.color.coui_color_background_elevatedWithCard
                )
            )
    }
}