/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - LoopDayPanelFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/08
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2023/11/08     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_LIST_CANCEL_CLICK
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_LIST_SAVE_CLICK
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log

/**
 * 轮班闹钟界面
 * @Date 2023年11月8日10:42:20
 */
class LoopDayPanelFragment() : COUIPanelFragment() {

    companion object {
        private const val TAG = "LoopDayPanelFragment"
        fun newInstance(alarm: Alarm, reloadAlarm: Alarm?, call: AddAlarmFragment.SnoozePreferenceCallBack): LoopDayPanelFragment {
            val bundle = Bundle().apply {
                putParcelable(LOOP_ALARM_PAGE_DATA_NAME, alarm)
                putParcelable(LOOP_ALARM_RELOAD_DATA_NAME, reloadAlarm)
            }
            return LoopDayPanelFragment().apply {
                arguments = bundle
                mCall = call
            }
        }
    }

    var mLoopPreferenceFragment: LoopPreferenceFragment? = null
    private var mAlarm: Alarm? = null
    private var mReloadAlarm: Alarm? = null
    private var mCall: AddAlarmFragment.SnoozePreferenceCallBack? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        arguments?.let {
            mAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LOOP_ALARM_PAGE_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LOOP_ALARM_PAGE_DATA_NAME)
            }
            mReloadAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LOOP_ALARM_RELOAD_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LOOP_ALARM_RELOAD_DATA_NAME)
            }
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.activity_add_alarm_menu, menu)
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        initToolbar()
        initPreference()
        initListener()
    }

    /**
     * 初始化状态栏
     */
    private fun initToolbar() {
        //隐藏把手
        hideDragView()
        titleView?.visibility = View.GONE
        toolbar?.visibility = View.GONE
        view?.findViewById<ViewGroup>(R.id.title_view_container)?.visibility = View.GONE

        val lay = toolbar.layoutParams as LinearLayout.LayoutParams
        lay.setMargins(0, 0, 0, 0)
        toolbar = toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(R.string.loop_type_alarm)
            isTitleCenterStyle = true
            inflateMenu(R.menu.activity_add_alarm_menu)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    LiteEventBus.instance.send(EVENT_LOOP_ALARM_LIST_CANCEL_CLICK)
                    finishPage()
                    true
                }
            }
            menu.findItem(R.id.save).apply {
                setOnMenuItemClickListener {
                    Log.d(TAG, "loop save")
                    mLoopPreferenceFragment?.let {
                        if (it.canSaveAlarm()) {
                            finishPage()
                            LiteEventBus.instance.send(EVENT_LOOP_ALARM_LIST_SAVE_CLICK)
                        }
                    }
                    true
                }
            }
        }
    }

    private fun initPreference() {
        mAlarm?.let {
            mLoopPreferenceFragment = LoopPreferenceFragment.newInstance(it, mReloadAlarm)
            mLoopPreferenceFragment?.let { loopFragment ->
                childFragmentManager.beginTransaction().replace(contentResId, loopFragment).commit()
            }
        }
    }

    fun saveTempAlarm(): Alarm? {
        mLoopPreferenceFragment?.let {
            it.saveAlarm()
            return it.mTempAlarm
        }
        return null
    }

    /**
     * 设置事件
     */
    private fun initListener() {
        //手势返回
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                LiteEventBus.instance.send(EVENT_LOOP_ALARM_LIST_CANCEL_CLICK)
                finishPage()
            }
            false
        }
        setPanelDragListener {
            mLoopPreferenceFragment?.canClosePanel() == true
        }
    }

    private fun finishPage() {
        Log.d(TAG, "finishPage loop day panel")
        (parentFragment as? COUIBottomSheetDialogFragment)?.backToFirstPanel()
        mCall?.backTo()
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
                ?.setPanelBackgroundTintColor(ContextCompat.getColor(
                        requireContext(),
                        R.color.coui_color_background_elevatedWithCard
                ))
    }
}