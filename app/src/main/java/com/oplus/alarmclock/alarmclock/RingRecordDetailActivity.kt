/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - RingRecordDetailActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.Context
import android.content.Intent
import android.view.MenuItem
import androidx.recyclerview.widget.LinearLayoutManager
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.adapter.RingRecordDetailRVAdapter
import com.oplus.alarmclock.databinding.ActivityRingRecordDetailBinding
import com.oplus.alarmclock.mvvm.base.BaseVMActivity
import com.oplus.alarmclock.mvvm.ringrecord.RingRecordDetailViewModel
import com.oplus.alarmclock.provider.alarmring.AlarmRing
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.alarmclock.view.DigitalClock
import java.util.Calendar

class RingRecordDetailActivity :
        BaseVMActivity<ActivityRingRecordDetailBinding, RingRecordDetailViewModel>() {
    private val mRvAdapter by lazy { RingRecordDetailRVAdapter() }

    companion object {
        private const val ALARM_RING = "alarm_ring"
        fun goToRingRecordDetail(context: Context, alarmRing: AlarmRing) {
            context.startActivity(Intent(context, RingRecordDetailActivity::class.java).apply {
                putExtra(ALARM_RING, alarmRing)
            })
        }
    }

    override fun initView() {
        mViewBinding.run {
            setSupportActionBar(toolbar)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)

            list.run {
                layoutManager = LinearLayoutManager(this@RingRecordDetailActivity)
                adapter = mRvAdapter
            }
            val uiMode = obtainUiMode()
            if (FoldScreenUtils.UiMode.LARGE_HORIZONTAL == uiMode) { /*平板横屏*/
                val padding = resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding)
                contentCl.setPadding(padding, 0, padding, 0)
            } else if (FoldScreenUtils.UiMode.LARGE_VERTICAL == uiMode) { /*平板竖屏*/
                val padding = resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding)
                contentCl.setPadding(padding, 0, padding, 0)
            } else {
                contentCl.setPadding(0, 0, 0, 0)
            }
            appBar.setPadding(0, Utils.getStatusBarHeight(this@RingRecordDetailActivity), 0, 0)
        }
    }

    override fun initData() {
        mViewModel.run {
            mHeadData.observe(this@RingRecordDetailActivity) {
                setHeadData(it)
            }
            mDetailList.observe(this@RingRecordDetailActivity) {
                mRvAdapter.mData = it
            }
            (intent.getSerializableExtra(ALARM_RING) as AlarmRing).run {
                loadHeadData(this)
                loadDetailList(alarmId)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
            }
        }
        return true
    }

    override fun onOsloLandOrientation() {
        super.onOsloLandOrientation()
        mViewBinding.run {
            contentCl.setPadding(
                resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                0,
                resources.getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                0
            )
        }
    }

    override fun onOsloPortOrientation() {
        super.onOsloPortOrientation()
        mViewBinding.run {
            contentCl.setPadding(
                resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                0,
                resources.getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                0
            )
        }
    }

    override fun onOsloOtherOrientation() {
        super.onOsloOtherOrientation()
        mViewBinding.run {
            contentCl.setPadding(0, 0, 0, 0)
        }
    }

    private fun setHeadData(alarmRing: AlarmRing) {
        alarmRing.run {
            mViewBinding.alarmRing = alarmRing
            setTime(mViewBinding.timeDc, this)
        }
    }

    private fun setTime(digitalClock: DigitalClock, alarmRing: AlarmRing) {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = alarmRing.stepTime
        digitalClock.updateTime(calendar)
    }
}