/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorkPreferenceFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/10
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/11/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_FINISH_PAGE
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.alarmclock.utils.ClockConstant.EVENT_ADD_ALARM_FINISH_PAGE
import com.oplus.alarmclock.utils.ClockConstant.EVENT_SET_WORK_DAY_TYPE
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils.DEFAULT_LOOP_CYCLE
import com.oplus.alarmclock.utils.LoopAlarmUtils.DEFAULT_LOOP_WORK_DAY
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.clock.common.event.LiteEventBus

/**
 * 工作日页面
 * <AUTHOR>
 * @date 2022年3月30日09:35:01
 */
class WorkPreferenceFragment : COUIPanelFragment(), OnClickListener {

    companion object {
        private const val TAG = "WorkPreferenceFragment"
        private const val ZERO = 0
        private const val ONE = 1
        private const val TWO = 2
        private const val THREE = 3
        private const val FOUR = 4
        private const val SHOW_LOOP_PREFERENCE = 200
        fun newInstance(alarm: Alarm, reloadAlarm: Alarm?, isShowLoopPreference: Boolean): WorkPreferenceFragment {
            val bundle = Bundle().apply {
                putParcelable(LOOP_ALARM_PAGE_DATA_NAME, alarm)
                putParcelable(LOOP_ALARM_RELOAD_DATA_NAME, reloadAlarm)
                putBoolean(AddAlarmManager.KEY_SHOW_LOOP_PREFERENCE_PANEL, isShowLoopPreference)
            }
            return WorkPreferenceFragment().apply {
                arguments = bundle
            }
        }
    }

    /**
     * 是否已弹出轮保闹钟详情面板
     */
    var isShowLoopPreferencePanel = false
    var mLoopDayPanelFragment: LoopDayPanelFragment? = null
    private var mClickItem = 0
    private var mAlarm: Alarm? = null
    private var mReloadAlarm: Alarm? = null

    private var mIsShowLoopPreference: Boolean = false

    /**
     * 根布局
     */
    private lateinit var mRootView: View

    /**
     * 是否为新建闹钟
     */
    private var mIsNewAlarm: Boolean = false

    /**
     * 单选按钮
     */
    private var mWorkDayRadio: RadioButton? = null
    private var mSingleDaysRadio: RadioButton? = null
    private var mSixDaysRadio: RadioButton? = null
    private var mFiveDaysRadio: RadioButton? = null
    private var mLoopDaysRadio: RadioButton? = null
    private var mLoopWorkText: TextView? = null
    private var mRootLayoutView: LinearLayout? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        mRootView = inflater.inflate(R.layout.add_alarm_work_preference, container, false)
        arguments?.let {
            mAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LOOP_ALARM_PAGE_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LOOP_ALARM_PAGE_DATA_NAME)
            }
            mReloadAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LOOP_ALARM_RELOAD_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LOOP_ALARM_RELOAD_DATA_NAME)
            }
            mIsShowLoopPreference = it.getBoolean(AddAlarmManager.KEY_SHOW_LOOP_PREFERENCE_PANEL)
        }
        init()
        return mRootView
    }

    /**
     * 初始化
     */
    private fun init() {
        initViews()
        setWordDayRadio()
        //页面销毁后重新恢复数据
        if (mIsShowLoopPreference) {
            mRootView.postDelayed({
                showLoopPreferencePanel()
                mIsShowLoopPreference = false
            }, SHOW_LOOP_PREFERENCE.toLong())
        }
    }

    /**
     * 初始化event
     */
    private fun initEvent() {
        //轮班闹钟详情页保存
        LiteEventBus.instance.with(LoopAlarmEvent.EVENT_LOOP_ALARM_WORK_DATA_CLICK, viewLifecycleOwner.hashCode().toString())
                .observe(viewLifecycleOwner) {
                    mAlarm?.apply {
                        loopAlarmList?.let { mList ->
                            val data = LoopAlarmUtils.computeLoopDays(mList)
                            setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                            setmLoopWorkDays(data.first)
                            setmLoopRestDays(data.second)
                            setLoopCountText()
                        }
                        LiteEventBus.instance.send(EVENT_ADD_LOOP_ALARM_WORK_FINISH_PAGE, mAlarm)
                        //post 在下一帧执行关闭，避免同时调用backToFirstPanel()导致当前页面未关闭
                        mRootView?.post {
                            //关闭当前页面
                            val si = parentFragment as? WorkDayPanelFragment
                            //工作日类型传递-1无需重复刷新
                            si?.finishPage(-1)
                        }
                    }
                }
        //轮班闹钟详情页取消
        LiteEventBus.instance.with(LoopAlarmEvent.EVENT_LOOP_ALARM_ADD_RESET, viewLifecycleOwner.hashCode().toString())
                .observe(viewLifecycleOwner) {
                    if (it is Alarm) {
                        mAlarm = it.deepCopy()
                        setLoopCountText()
                        LiteEventBus.instance.send(EVENT_ADD_LOOP_ALARM_WORK_FINISH_PAGE)
                    }
                }
    }


    /**
     * 是否可以关闭面板
     */
    fun canClosePanel(): Boolean {
        val location = Rect()
        mRootLayoutView?.getGlobalVisibleRect(location)
        if (location.top > LoopAlarmUtils.PANEL_TOUCH_DOWN_DIS) {
            return false
        }
        return true
    }

    /**
     * 初始化View
     */
    private fun initViews() {
        mAlarm?.let {
            mWorkDayRadio = mRootView.findViewById(R.id.work_day_radio)
            mSingleDaysRadio = mRootView.findViewById(R.id.single_days_radio)
            mSixDaysRadio = mRootView.findViewById(R.id.six_days_radio)
            mFiveDaysRadio = mRootView.findViewById(R.id.five_days_radio)
            mLoopDaysRadio = mRootView.findViewById(R.id.loop_days_radio)
            mLoopWorkText = mRootView.findViewById(R.id.loop_alarm_work_cycle_text)
            mRootLayoutView = mRootView.findViewById(R.id.root_layout)
            mRootView.findViewById<LinearLayout>(R.id.work_day_layout)?.setOnClickListener(this)
            mRootView.findViewById<LinearLayout>(R.id.single_days_layout)?.setOnClickListener(this)
            mRootView.findViewById<LinearLayout>(R.id.six_days_layout)?.setOnClickListener(this)
            mRootView.findViewById<LinearLayout>(R.id.five_days_layout)?.setOnClickListener(this)
            mRootView.findViewById<LinearLayout>(R.id.loop_days_layout)?.setOnClickListener(this)
            //适配公共控件背景点击效果
            COUICardListHelper.setItemCardBackground(mRootView.findViewById<LinearLayout>(R.id.work_day_layout),
                    COUICardListHelper.getPositionInGroup(THREE, 0))
            COUICardListHelper.setItemCardBackground(mRootView.findViewById<LinearLayout>(R.id.single_days_layout),
                    COUICardListHelper.getPositionInGroup(THREE, 1))
            COUICardListHelper.setItemCardBackground(mRootView.findViewById<LinearLayout>(R.id.six_days_layout),
                    COUICardListHelper.getPositionInGroup(THREE, 1))
            COUICardListHelper.setItemCardBackground(mRootView.findViewById<LinearLayout>(R.id.five_days_layout),
                    COUICardListHelper.getPositionInGroup(THREE, 1))
            COUICardListHelper.setItemCardBackground(mRootView.findViewById<LinearLayout>(R.id.loop_days_layout),
                    COUICardListHelper.getPositionInGroup(THREE, TWO))
            mClickItem = it.getmWorkDayType()
            mIsNewAlarm = it.id == 0L
            setLoopCountText()
        }
    }

    /**
     * 设置轮班闹钟周期文本
     */
    private fun setLoopCountText() {
        mAlarm?.let {
            mLoopWorkText?.apply {
                text = if ((!mIsNewAlarm && it.getmLoopSwitch() == 1) || it.loopAlarmList.size > 0) {
                    resources.getQuantityString(R.plurals.loop_count_number, it.getmLoopCycleDays(),
                            it.getmLoopCycleDays(), it.getmLoopWorkDays(), it.getmLoopWorkDays())
                } else {
                    resources.getQuantityString(R.plurals.loop_count_number, DEFAULT_LOOP_CYCLE, DEFAULT_LOOP_CYCLE,
                            DEFAULT_LOOP_WORK_DAY, DEFAULT_LOOP_WORK_DAY)
                }
            }
        }
    }

    /**
     * 工作日类型
     */
    private fun setWordDayRadio() {
        clearRadio()
        when (mClickItem) {
            ZERO ->
                mWorkDayRadio?.isChecked = true

            ONE ->
                mSingleDaysRadio?.isChecked = true

            TWO ->
                mSixDaysRadio?.isChecked = true

            THREE ->
                mFiveDaysRadio?.isChecked = true

            FOUR ->
                mLoopDaysRadio?.isChecked = true
        }
    }

    /**
     * 清除已选
     */
    private fun clearRadio() {
        mWorkDayRadio?.isChecked = false
        mSingleDaysRadio?.isChecked = false
        mSixDaysRadio?.isChecked = false
        mFiveDaysRadio?.isChecked = false
        mLoopDaysRadio?.isChecked = false
    }

    /**
     * 打开轮班闹钟页面
     */
    private fun startLoopAlarmPage() {
        if (mClickItem != FOUR) {
            LiteEventBus.instance.send(EVENT_ADD_ALARM_FINISH_PAGE, mClickItem)
        } else {
            mAlarm?.apply {
                if (loopAlarmList.size == 0) {
                    // 工作日闹钟编辑轮班闹钟数据为空，初始化数据
                    setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                    workdaySwitch = 0
                    setmLoopCycleDays(DEFAULT_LOOP_CYCLE)
                    setmLoopWorkDays(DEFAULT_LOOP_WORK_DAY)
                    setmLoopDay(1)
                    val alarmHolidaySwitch = PrefUtils.getInt(context, PrefUtils.ALARM_HOLIDAY_SWITCH,
                            PrefUtils.ALARM_HOLIDAY_SWITCH_KEY, ClockConstant.ALARM_HOLIDAY_SWITCH_OFF)
                    holidaySwitch = alarmHolidaySwitch
                    loopAlarmList.addAll(LoopAlarmUtils.addDefaultLoopAlarm(DEFAULT_LOOP_CYCLE))
                }
            }
            LiteEventBus.instance.send(EVENT_SET_WORK_DAY_TYPE, mClickItem)
            setWordDayRadio()
            showLoopPreferencePanel()
            //埋点
            ClockOplusCSUtils.onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.EVENT_LOOP_ALARM_TYPE_CLICK)
        }
    }

    /**
     * 展示轮班闹钟详情页面
     */
    private fun showLoopPreferencePanel() {
        if (parentFragment?.parentFragment is COUIBottomSheetDialogFragment && !isShowLoopPreferencePanel) {
            val si = parentFragment?.parentFragment as? COUIBottomSheetDialogFragment
            isShowLoopPreferencePanel = true
            mLoopDayPanelFragment = mAlarm?.let {
                LoopDayPanelFragment.newInstance(it, mReloadAlarm) {
                    isShowLoopPreferencePanel = false
                }
            }
            mLoopDayPanelFragment?.let {
                si?.replacePanelFragment(it)
            }
        }
    }

    fun saveTempAlarm(): Alarm? {
        mLoopDayPanelFragment?.let {
            return it.saveTempAlarm()
        }
        return null
    }

    /**
     * 点击事件
     */
    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.work_day_layout ->
                mClickItem = ZERO

            R.id.single_days_layout ->
                mClickItem = ONE

            R.id.six_days_layout ->
                mClickItem = TWO

            R.id.five_days_layout ->
                mClickItem = THREE

            R.id.loop_days_layout ->
                mClickItem = FOUR
        }
        mAlarm?.setmWorkDayType(mClickItem)
        startLoopAlarmPage()
    }

    override fun onDestroy() {
        super.onDestroy()
        mRootView?.handler?.removeCallbacksAndMessages(null)
    }

    override fun onResume() {
        super.onResume()
        initEvent()
    }

    override fun onStop() {
        super.onStop()
        LiteEventBus.instance.releaseObserver(viewLifecycleOwner.hashCode().toString())
    }

    /**
     * 销毁
     */
    override fun onDestroyView() {
        super.onDestroyView()
        LiteEventBus.instance.releaseObserver(viewLifecycleOwner.hashCode().toString())
    }
}