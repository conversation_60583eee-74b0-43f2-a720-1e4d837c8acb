/*
 * ***************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File:  - AddAlarmViewHolder.java
 *  ** Description:
 *  ** Version: 1.0
 *  ** Date : 2020/02/18
 *  ** Author: hewei
 *  **
 *  ** ---------------------Revision History: -----------------------
 *  **  <author>    <data>       <version >     <desc>
 *  **  hewei       2020/02/18     1.0            AddAlarmViewHolder.java
 *  ***************************************************************
 */
// OPLUS Java File Skip Rule:LineLength, MethodLength
package com.oplus.alarmclock.alarmclock;

import static com.coui.appcompat.edittext.COUIEditText.MODE_BACKGROUND_LINE;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ViewAnimator;

import com.coui.appcompat.cardlist.COUICardListHelper;
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.picker.COUITimeLimitPicker;
import com.coui.appcompat.rotateview.COUIRotateView;
import com.coui.appcompat.tooltips.COUIToolTips;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.RuntimePermissionAlert;
import com.oplus.alarmclock.alarmclock.adapter.AddAlarmTypeAdapter;
import com.oplus.alarmclock.alarmclock.datepick.AlarmDatePicker;
import com.oplus.alarmclock.timer.OplusTimerFragment;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.view.WeekPicker;
import com.coui.appcompat.couiswitch.COUISwitch;
import com.coui.appcompat.edittext.COUIEditText;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.view.ReboundScrollView;
import com.oplus.alarmclock.view.YearAndMonthPicker;

import androidx.annotation.Nullable;
import androidx.asynclayoutinflater.view.AsyncLayoutInflater;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.COUIRecyclerView;


public class AddAlarmViewHolder {

    public static final int SET_ALARL_SET_RING_AND_URI = 6;
    public static final int THREE = 3;
    public static final int RING_REQUEST_CODE = 2001;
    public static final int VIBRATE_REQUEST_CODE = 2002;
    public static final int SET_RING_NAME_PRE = 7;
    public static final int SET_BUTTON_CLICK_DELAY = 9;
    public static final int FINISH_ACTIVITY_DELAY = 11;
    public static final int SET_ALARL_GET_TITLE = 5;
    public static final int SET_ALARL_START_QUERY_AUTO = 1;
    public static final int SET_ALARL_START_QUERY_DEFAULT = 2;
    public static final int MESSAGE_WHAT_UPDATE_TIME_INFO = 100;
    /**
     * 日历控件
     */
    public static final int VIEW_MONTH_DAY = 0;
    /**
     * 年月控件
     */
    public static final int VIEW_YEAR = 1;
    public static final int UNINITIALIZED = -1;
    private static final String TAG = "AddAlarmViewHolder";
    public COUIEditText mAlarmLabel;
    public COUISwitch mVibrate;
    public TextView mVibrateText;
    public TextView mRingSummary;
    public TextView mRingTypeSummary;
    public LinearLayout mLayoutPrefs;
    public LinearLayout mRingLayout;
    public LinearLayout mRingTypeLayout;
    public LinearLayout mSnoozeLayout;
    public LinearLayout mVibrateLayout;
    public LinearLayout mVibrateTypeLayout;
    public View mVibrateLine;
    public View mVibrateTypeLine;

    public COUIRecyclerView mAlarmTypeTabList;
    /**
     * 是否首次初始化tab
     */
    public boolean isInitTab = false;
    /**
     * 周选择器
     */
    public WeekPicker mWeekPick;
    /**
     * 工作日类型
     */
    public LinearLayout mWorkDayLayout;
    public LinearLayout mAlarmLabelLayout;
    public LinearLayout mTimePickerLayout;
    /**
     * 秒抢闹钟
     */
    public COUICardListSelectedItemLayout mGarbAlarmDateLayout;
    public COUICardListSelectedItemLayout mGarbAlarmRingLayout;
    public COUICardListSelectedItemLayout mGarbAlarmDescription;
    public ImageView mGarbAlarmImage;


    /**
     * 自定义闹钟布局
     */
    public LinearLayout mCustomLayout;
    /**
     * 稍后提醒时间文本
     */
    public TextView mSnoozeTime;

    /**
     * 工作日子选项
     */
    public TextView mWorkDaySubTitle;
    /**
     * 重复日期文本
     */
    public TextView mRepeatDes;
    /**
     * 节假日开关
     */
    public COUISwitch mHolidaySwitch;

    /**
     * 时间选择器布局
     */
    public ViewStub mAlarmDataPickerViewStub;
    public ViewStub mGarbAlarmViewStub;
    public View mAlarmDataPicker;
    /**
     * 弹出日期选择
     */
    public COUIRotateView mAlarmRepeatMore;
    public ReboundScrollView mScrollPanel;
    /**
     * 开关文字信息
     */
    public TextView mHolidaySwitchTextView;
    /**
     * 当前闹钟数据
     */
    public Alarm mAlarm;
    public ImageView mAlertType;
    public ImageView mRingtone;

    public Alarm mPreAlarm;
    /**
     * 是否是新建闹钟
     */
    public boolean mIsNewAlarm;
    public Handler mHandler;
    public String mAlertNone;
    public RuntimePermissionAlert mRuntimePermissionAlert;
    public LinearLayout mExpandLayout;
    public int mCurrentView = UNINITIALIZED;
    /**
     * 日历是否展示
     */
    public boolean mShowDatePicker = false;
    /**
     * 稍后提醒页面是否展示
     */
    public boolean mIsShowSnoozePanel;

    public Alarm mReloadAlarm;
    /**
     * 页面销毁需要重新展示提示
     */
    public boolean mIsNeedShowWorkTips;
    public boolean mIsNeedShowWorkDayTips;

    /**
     * 工作日类型
     */
    public int mWorkDayType = -1;
    /**
     * 是否可以保存或取消添加闹钟页面
     */
    public boolean mCanClickSaveOrCancel = false;
    public CoordinatorLayout mAddAlarmRootView;
    /**
     * 时间选择器
     */
    public COUITimeLimitPicker mOplusTimePicker;
    /**
     * 顶部响铃倒计时
     */
    public TextView mAlarmLeftTime;
    /**
     * 通用属性布局
     */
    public LinearLayout mCommonLayout;
    /**
     * 日历相关控件
     */
    public COUIRotateView mExpandButton;
    public TextView mHeaderMonthView;
    public ViewAnimator mAnimator;
    public ImageButton mPrevButton;
    public ImageButton mNextButton;
    /**
     * 年月选择器
     */
    public YearAndMonthPicker mYearAndMonthView;
    /**
     * 铃声与震动文本
     */
    public TextView mRingToneText;
    /**
     * 节假日开关布局
     */
    public LinearLayout mAlarmHolidayLayout;
    public View mRootView;

    /**
     * 动画管理类
     */
    public AlarmAnimatorUtil mAnimatorUtil;

    /**
     * 添加闹钟 Fragment
     */
    public AddAlarmFragment mFragment;

    /**
     * 闹钟数量
     */
    public int mAlarmCount = 0;
    /**
     * 当前点击的稍后提醒列表下标
     */
    public int mSnoozePosition = 0;
    /**
     * 响铃次数下标
     */
    public int mRingNumPosition = 1;
    /**
     * 时间和分钟
     */
    public int mTimePickerMinute;
    public int mTimePickerHour;
    public boolean mSkipUi;
    /**
     * 闹铃重复日期
     */
    public int mAlarmRepeatSet = 0;
    /**
     * 日期选择器
     */
    public AlarmDatePicker mAlarmDatePicker;
    /**
     * 节假日开关
     */
    public int mAlarmHolidaySwitch = 0;
    public boolean mIsSaveClicked;

    /**
     * 自定义闹钟中的响一次闹钟
     */
    public boolean mOnceClock;
    public boolean mIsFromScreen;
    public int mAlertTypeItem = 2;

    /**
     * 页面是否是重新加载
     */
    public boolean mIsReload = false;
    public COUIRecyclerView mLoopAlarmList;
    public LinearLayout mLoopAlarmListLayout;
    /**
     * 工作日类型图片
     */
    public ImageView mWorkdDayImageRight;
    /**
     * 底部view
     */
    public View mBottomView;

    /**
     * 工作日类型标题
     */
    public TextView mWeekdaysType;
    /**
     * 轮班闹钟提示
     */
    public COUIToolTips mLoopAlarmTips;
    public COUIToolTips mWorkDayAlarmTips;
    /**
     * 是否支持设置默认铃声
     */
    public boolean isSupportSettingDefaultAlarm;
    public AddAlarmTypeAdapter mAlarmTypeTabAdapter;
    /**
     * 秒抢描述信息
     */
    public TextView mGarbAlarmDateSub;
    public TextView mGarbAlarmRingSub;
    public TextView mGarbAlarmRingDex;
    public TextView mGarbAlarmRingSec;
    public TextView mGarbAlarmInfo;
    private Context mContext;


    public void inflate(@Nullable ViewGroup container, @Nullable Bundle savedInstanceState, AddAlarmFragment fragment, AsyncLayoutInflater.OnInflateFinishedListener onInflateFinishedListener) {
        mFragment = fragment;
        Activity activity = fragment.getActivity();
        if (activity != null) {
            new AsyncLayoutInflater(activity).inflate(R.layout.add_or_update_alarm_activity_preference, container, onInflateFinishedListener);
        }
    }

    public boolean init(View rootView) {
        if (mFragment.getActivity() == null) {
            return false;
        }
        mRootView = rootView;
        mContext = AlarmClockApplication.getInstance();
        isSupportSettingDefaultAlarm = AlarmRingUtils.ifSupportSettingDefaultAlarm(mContext);
        Log.i(TAG, "init");
        mAlarmLabel = mRootView.findViewById(R.id.alarm_label);
        mLayoutPrefs = mRootView.findViewById(R.id.layout_prefs);
        mRingLayout = mRootView.findViewById(R.id.ll_ring);
        mRingSummary = mRootView.findViewById(R.id.ring_summary);
        mRingTypeLayout = mRootView.findViewById(R.id.ll_ring_type);
        mVibrateText = mRootView.findViewById(R.id.alarm_vibrate_text);
        mVibrate = mRootView.findViewById(R.id.vibrate_switch);
        mSnoozeLayout = mRootView.findViewById(R.id.ll_snooze_switch);
        mSnoozeLayout.setSoundEffectsEnabled(false);
        mVibrateTypeLayout = mRootView.findViewById(R.id.ll_vibrate_type);
        mVibrateTypeLine = mRootView.findViewById(R.id.ll_vibrate_type_line);
        mVibrateLine = mRootView.findViewById(R.id.ll_vibrate_line);
        mVibrateLayout = mRootView.findViewById(R.id.ll_vibrate);
        mVibrateTypeLayout.setSoundEffectsEnabled(false);
        mVibrateLayout.setSoundEffectsEnabled(false);
        mRingTypeSummary = mRootView.findViewById(R.id.ring_type);
        mWeekPick = mRootView.findViewById(R.id.add_alarm_week_picker);
        mWorkDayLayout = mRootView.findViewById(R.id.add_alarm_work_day_layout);
        mWorkDayLayout.setSoundEffectsEnabled(false);
        mCustomLayout = mRootView.findViewById(R.id.add_alarm_custom_layout);
        mAlarmLabelLayout = mRootView.findViewById(R.id.add_alarm_label_layout);
        mTimePickerLayout = mRootView.findViewById(R.id.time_pick_layout);
        mSnoozeTime = mRootView.findViewById(R.id.snooze_time);
        mWorkDaySubTitle = mRootView.findViewById(R.id.add_alarm_work_day_sub);
        mRepeatDes = mRootView.findViewById(R.id.add_alarm_repeat_des);
        mHolidaySwitch = mRootView.findViewById(R.id.add_alarm_holiday_switch);
        mAlarmDataPickerViewStub = mRootView.findViewById(R.id.add_alarm_datePicker);
        mAlarmRepeatMore = mRootView.findViewById(R.id.add_alarm_repeat_date_pick);
        mAlarmTypeTabList = mRootView.findViewById(R.id.alarm_type_tab_list);
        mScrollPanel = mRootView.findViewById(R.id.scroll_panel);
        mHolidaySwitchTextView = mRootView.findViewById(R.id.add_alarm_holiday_switch_text);
        mRingToneText = mRootView.findViewById(R.id.add_alarm_ringtone_text);
        mAlarmHolidayLayout = mRootView.findViewById(R.id.add_alarm_holiday_switch_layout);
        setAlarmHolidayTalkback();
        mOplusTimePicker = mRootView.findViewById(R.id.oplusTimePicker);
        mAlarmLeftTime = mRootView.findViewById(R.id.time_info);
        mCommonLayout = mRootView.findViewById(R.id.add_alarm_common_layout);
        mGarbAlarmViewStub = mRootView.findViewById(R.id.add_alarm_garb_layout);


        mAddAlarmRootView = mRootView.findViewById(R.id.add_alarm_root_view);
        mAlertType = mRootView.findViewById(R.id.img_alert_type);
        mRingtone = mRootView.findViewById(R.id.img_ringtone);
        mLoopAlarmList = mRootView.findViewById(R.id.loop_alarm_cycle_list);
        mLoopAlarmListLayout = mRootView.findViewById(R.id.loop_alarm_layout);
        mWorkdDayImageRight = mRootView.findViewById(R.id.add_alarm_work_day_right);
        mBottomView = mRootView.findViewById(R.id.add_alarm_bottom_view);

        TextView repeatBell = mRootView.findViewById(R.id.bell_repeat_data_text);
        TextView holidayNotRing = mRootView.findViewById(R.id.holiday_alarm_not_ring_text);
        mWeekdaysType = mRootView.findViewById(R.id.weekdays_type_text);
        TextView alarmRingtone = mRootView.findViewById(R.id.add_alarm_ringtone_text);
        TextView snoozeSwitch = mRootView.findViewById(R.id.snooze_switch_text);
        TextView vibrateTitle = mRootView.findViewById(R.id.alarm_vibrate_title);
        TextWeightUtils.setTextBold(repeatBell);
        TextWeightUtils.setTextBold(holidayNotRing);
        TextWeightUtils.setTextBold(mWeekdaysType);
        TextWeightUtils.setTextBold(vibrateTitle);
        TextWeightUtils.setTextBold(alarmRingtone);
        TextWeightUtils.setTextBold(snoozeSwitch);
        //适配公共背景点击
        COUICardListHelper.setItemCardBackground(mCustomLayout, COUICardListHelper.getPositionInGroup(1, 0));
        COUICardListHelper.setItemCardBackground(mWorkDayLayout, COUICardListHelper.getPositionInGroup(THREE, 0));
        COUICardListHelper.setItemCardBackground(mAlarmLabelLayout, COUICardListHelper.getPositionInGroup(THREE, 0));
        COUICardListHelper.setItemCardBackground(mVibrateTypeLayout, COUICardListHelper.getPositionInGroup(THREE, 1));
        COUICardListHelper.setItemCardBackground(mRingTypeLayout, COUICardListHelper.getPositionInGroup(THREE, 1));
        COUICardListHelper.setItemCardBackground(mTimePickerLayout, COUICardListHelper.getPositionInGroup(1, 0));
        setItemCardBackground();
        int col = COUIContextUtil.getAttrColor(mFragment.getActivity(), R.attr.couiColorSecondNeutral);
        mAlarmRepeatMore.setImageTintList(ColorStateList.valueOf(col));

        Resources resources = mContext.getResources();
        if (resources != null) {
            mAlertNone = resources.getString(R.string.alert_no_ring);
        }
        if (DeviceUtils.isExpVersion(mContext)) {
            //外销隐藏节假日开关
            mAlarmHolidayLayout.setVisibility(View.GONE);
            //外销工作日类型标题默认轮班制
            mWeekdaysType.setText(mContext.getResources().getText(R.string.loop_type_alarm));
        }
        return true;
    }

    public void setAlarmHolidayTalkback() {
        if (null == mContext || DeviceUtils.isExpVersion(mContext)) {
            return;
        }
        Resources resources = mContext.getResources();
        if (null != resources && mHolidaySwitch != null) {
            mAlarmHolidayLayout.setContentDescription(resources.getString(R.string.holiday_alarm_not_ring) + " "
                    + resources.getString(R.string.holiday_alarm_not_ring_tip)
                    + (mHolidaySwitch.isChecked() ? resources.getString(R.string.open) : resources.getString(R.string.close)));
        }
    }

    public boolean initGarbAlarmLayout(View rootView) {
        if (mFragment.getActivity() == null) {
            return false;
        }
        mGarbAlarmDateSub = rootView.findViewById(R.id.garb_alarm_date_sub);
        mGarbAlarmInfo = rootView.findViewById(R.id.add_alarm_garb_info);
        mGarbAlarmRingSub = rootView.findViewById(R.id.garb_alarm_ring_sub);
        mGarbAlarmRingDex = rootView.findViewById(R.id.garb_alarm_ring_dex);
        mGarbAlarmRingSec = rootView.findViewById(R.id.add_alarm_garb_sec);
        mGarbAlarmDateLayout = rootView.findViewById(R.id.garb_alarm_date);
        mGarbAlarmRingLayout = rootView.findViewById(R.id.garb_alarm_ring);
        mGarbAlarmDescription = rootView.findViewById(R.id.garb_alarm_description);
        mGarbAlarmImage = rootView.findViewById(R.id.add_alarm_garb_info_image);
        return true;
    }

    public boolean initDataPicker(View mRootView) {
        if (mFragment.getActivity() == null) {
            return false;
        }
        Log.d(TAG, "initDataPicker");
        mAlarmDataPicker = mRootView.findViewById(R.id.layout_prefs);
        mPrevButton = mRootView.findViewById(R.id.prev);
        mNextButton = mRootView.findViewById(R.id.next);
        mExpandButton = mRootView.findViewById(R.id.expand);
        mExpandLayout = mRootView.findViewById(R.id.date_picker_header_month_layout);
        mHeaderMonthView = mRootView.findViewById(R.id.month_and_year);

        mAnimator = mRootView.findViewById(R.id.animator);

        mYearAndMonthView = mRootView.findViewById(R.id.date_picker_year_picker);

        int col = COUIContextUtil.getAttrColor(mFragment.getActivity(), R.attr.couiColorSecondNeutral);
        mExpandButton.setImageTintList(ColorStateList.valueOf(col));

        return true;
    }

    /**
     * 设置卡片背景
     */
    public void setItemCardBackground() {
        COUICardListHelper.setItemCardBackground(mVibrateLayout, COUICardListHelper.getPositionInGroup(THREE, 1));
        COUICardListHelper.setItemCardBackground(mRingLayout, COUICardListHelper.getPositionInGroup(THREE, 1));
        COUICardListHelper.setItemCardBackground(mSnoozeLayout, COUICardListHelper.getPositionInGroup(THREE, 2));
        COUICardListHelper.setItemCardBackground(mVibrateTypeLayout, COUICardListHelper.getPositionInGroup(THREE, 1));
        mVibrateTypeLine.setVisibility(View.VISIBLE);
        mAlarmLabel.setBoxBackgroundMode(MODE_BACKGROUND_LINE);
        mVibrateLine.setVisibility(View.VISIBLE);
    }
}
