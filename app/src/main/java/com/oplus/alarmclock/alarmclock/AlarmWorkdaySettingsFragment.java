/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : fragment for set workday type
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-02-20, xiaolong,yu, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.app.AlertDialog;

import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.coui.appcompat.preference.COUIMarkPreference;
import com.coui.appcompat.preference.COUIPreferenceFragment;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class AlarmWorkdaySettingsFragment extends COUIPreferenceFragment implements OplusRadioPreferencesGroup.OnCheckedStateChangeListener {

    private static final String TAG = "AlarmWorkdaySettingsFragment";

    private static final String OPLUS_WORKDAY = "oplus_workday";
    private static final String OPLUS_RADIO_PREFERENCES_GROUP = "oplus_radio_preferences_group";
    private static final String SINGLE_CEASE_REST_DAY_SUNDAY = "single_cease_rest_day_sunday";
    private static final String SIZE_WORD_REST_DAY_ONLY_SUNDAY = "size_word_rest_day_only_sunday";
    private static final String SIZE_WORD_REST_DAY_DOUBLE = "size_word_rest_day_double";
    private COUIMarkPreference mWorkdayPreference;
    private COUIMarkPreference mSingleCeaseRestDaySunday;
    private COUIMarkPreference mSizeWorkRestDayOnlySunday;
    private COUIMarkPreference mSizeWorkRestDayDouble;
    private OplusRadioPreferencesGroup mRadioPreferencesGroup;
    private Context mContext;
    private int mCurrentCheck = 0;
    private AlertDialog mAlertDialog;
    private int mWorkdayTypeClockNum = -1;
    private boolean mIsSaveWorkday = false;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        View view = super.onCreateView(inflater, container, savedInstanceState);
        getListView().setVerticalScrollBarEnabled(false);
        mContext = AlarmClockApplication.getInstance();
        mCurrentCheck = WorkDayTypeUtils.getNeedShowWorkDayType(mContext,null);

        initPref();
        return view;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        addPreferencesFromResource(R.xml.workday_setting_prefs);
    }

    private void initPref() {
        mWorkdayPreference = findPreference(OPLUS_WORKDAY);
        mSingleCeaseRestDaySunday = findPreference(SINGLE_CEASE_REST_DAY_SUNDAY);
        mSizeWorkRestDayOnlySunday = findPreference(SIZE_WORD_REST_DAY_ONLY_SUNDAY);
        mSizeWorkRestDayDouble = findPreference(SIZE_WORD_REST_DAY_DOUBLE);
        mRadioPreferencesGroup = findPreference(OPLUS_RADIO_PREFERENCES_GROUP);
        if (mRadioPreferencesGroup != null) {
            mRadioPreferencesGroup.setCheckedPreference(mCurrentCheck);
            mRadioPreferencesGroup.setOnCheckedStateChangeListener(this);
        }
    }


    @SuppressLint("NewApi")
    private AlertDialog getSetExitDialog(final int workdayType, final int workdayTypeClockNum) {
        mIsSaveWorkday = false;
        Activity activity = getActivity();
        if ((activity == null) || activity.isFinishing()) {
            return null;
        }

        CharSequence[] charSequences = new CharSequence[]{activity.getString(R.string.weekdays_clock_confirm_change)};

        return new COUIAlertDialogBuilder(activity,R.style.COUIAlertDialog_Bottom)
                .setMessage(R.string.weekdays_clock_message)
                .setItems(charSequences, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        mIsSaveWorkday = true;
                        saveWorkdayTypeInfo(workdayType, workdayTypeClockNum);
                    }
                })
                .setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        Log.i(TAG, "getSetExitDialog onClick cancel ");
                        mIsSaveWorkday = true;
                        if (mRadioPreferencesGroup != null) {
                            mRadioPreferencesGroup.setCheckedPreference(mCurrentCheck);
                        }
                    }
                }).setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialogInterface) {
                        Log.i(TAG, "onDismiss  mIsSaveWorkday " + mIsSaveWorkday);
                        if ((mRadioPreferencesGroup != null) && (!mIsSaveWorkday)) {
                            mRadioPreferencesGroup.setCheckedPreference(mCurrentCheck);
                        }
                    }
                })
                .setCancelable(true)
                .create();
    }

    private void saveWorkdayTypeInfo(final int workdayType, final int workdayTypeClockNum) {
        AsyncHandler.post(new Runnable() {
            @Override
            public void run() {

                //set current workday type
                WorkDayTypeUtils.setWorkDayType(mContext, workdayType, Calendar.getInstance().getTimeInMillis());
                //Near me for change work day type
                ClockOplusCSUtils.setWorkdayType(mContext, workdayType, mWorkdayTypeClockNum);

                List<AlarmSchedule> list = ScheduleUtils.getAllSchedules(mContext);
                if ((list == null) || (list.isEmpty())) {
                    Log.e(TAG, "getSetExitDialog AlarmSchedule alarm data is empty");
                    return;
                }

                for (AlarmSchedule schedule : list) {

                    Alarm alarm = schedule.getAlarm();
                    //only recreate workday alarm
                    if ((alarm != null) && (alarm.getWorkdaySwitch() == ClockConstant.ALARM_WORKDAY_SWITCH_ON)) {
                        //recreate alarm
                        AlarmStateManager.recreateAlarmSchedules(mContext, alarm);
                    }
                }
                AlarmStateManager.showNextAlarmNotices(mContext);
            }
        });

        Activity activityInner = getActivity();
        if ((activityInner != null) && !activityInner.isFinishing()) {
            activityInner.setResult(Activity.RESULT_OK);
            activityInner.finish();
        }
    }

    private int getCurrentCheckWorkDay() {
        if ((mWorkdayPreference != null) && (mWorkdayPreference.isChecked())) {
            return WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY;
        }

        if ((mSingleCeaseRestDaySunday != null) && (mSingleCeaseRestDaySunday.isChecked())) {
            return WorkDayTypeUtils.WORKDAY_TYPE_SINGLE_CEASE_REST_DAY_SUNDAY;
        }
        if ((mSizeWorkRestDayOnlySunday != null) && (mSizeWorkRestDayOnlySunday.isChecked())) {
            return WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_ONLY_SUNDAY;
        }
        if ((mSizeWorkRestDayDouble != null) && (mSizeWorkRestDayDouble.isChecked())) {
            return WorkDayTypeUtils.WORKDAY_TYPE_SIZE_WORD_REST_DAY_DOUBLE;
        }
        return WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY;
    }

    private void resetWorkdayType() {
        Log.i(TAG, "resetWorkdayType  resetWorkdayType");
        Activity activity = getActivity();
        if ((activity == null) || activity.isFinishing()) {
            Log.i(TAG, "resetWorkdayType  activity is null ");
            return;
        }

        int workdayType = getCurrentCheckWorkDay();
        Log.i(TAG, "resetWorkdayType mCurrentCheck =" + mCurrentCheck + "   workdayType = " + workdayType + "  workdayTypeClockNum = " + mWorkdayTypeClockNum);
        if (shouldResetWorkdayType()) {
            mAlertDialog = getSetExitDialog(workdayType, mWorkdayTypeClockNum);
            if (mAlertDialog != null) {
                mAlertDialog.show();
            }
        } else {
            //set current workday type
            WorkDayTypeUtils.setWorkDayType(mContext, workdayType,Calendar.getInstance().getTimeInMillis());
            //Near me for change work day type
            ClockOplusCSUtils.setWorkdayType(mContext, workdayType, mWorkdayTypeClockNum);
            activity.finish();
        }
    }

    public void saveWorkdayType() {
        Activity activity = getActivity();
        if ((activity == null) || activity.isFinishing()) {
            Log.i(TAG, "resetWorkdayType  activity is null ");
            return;
        }
        int workdayType = getCurrentCheckWorkDay();
        //set current workday type
        WorkDayTypeUtils.setWorkDayType(mContext, workdayType, Calendar.getInstance().getTimeInMillis());
        //Near me for change work day type
        ClockOplusCSUtils.setWorkdayType(mContext, workdayType, mWorkdayTypeClockNum);
        activity.finish();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mWorkdayTypeClockNum == -1) {
            mWorkdayTypeClockNum = AlarmUtils.getWorkdayTypeClockNum(mContext.getApplicationContext());
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mWorkdayPreference = null;
        mSingleCeaseRestDaySunday = null;
        mSizeWorkRestDayOnlySunday = null;
        mSizeWorkRestDayDouble = null;
        if (mRadioPreferencesGroup != null) {
            mRadioPreferencesGroup.resetContent();
        }
        mRadioPreferencesGroup = null;

        if ((mAlertDialog != null)) {
            if (!mAlertDialog.isShowing()) {
                mAlertDialog.cancel();
            }
            mAlertDialog = null;
        }
    }

    @Override
    public void onCheckedStateChange(boolean state) {

        Log.i(TAG, "onCheckedStateChange onCheckedStateChange " + state);
        if (state) {
            Activity activity = getActivity();
            if ((activity != null) && !activity.isFinishing() && (activity instanceof AlarmWorkdaySettingsActivity)) {
                if (shouldResetWorkdayType()) {
                    Log.i(TAG, "onCheckedStateChange workdayTypeNum > 0 ");
                    resetWorkdayType();
                } else {
                    Log.i(TAG, "onCheckedStateChange workdayTypeNum <= 0 ");
                }
            }
        }
    }

    private boolean shouldResetWorkdayType() {
        return (mWorkdayTypeClockNum > 0) && (getCurrentCheckWorkDay() != WorkDayTypeUtils.getSetWorkDayType(mContext));
    }

}
