/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AddAlarmRingManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin    203/5/8     1.0            build this module
 ****************************************************************/
@file:Suppress("LongParameterList", "MaximumLineLength", "<PERSON><PERSON><PERSON>ber", "ComplexCondition", "LargeClass", "ParameterListWrapping", "CollapsibleIfStatements")

package com.oplus.alarmclock.alarmclock

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.AsyncQueryHandler
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.net.Uri
import android.os.Message
import android.os.Parcelable
import android.provider.MediaStore
import android.text.TextUtils
import android.view.View
import com.heytap.addon.media.MediaFile
import com.heytap.addon.os.WaveformEffect
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.RuntimePermissionAlert
import com.oplus.alarmclock.alarmclock.utils.SetAlarmAsyncQueryHandler
import com.oplus.alarmclock.alarmclock.utils.SetAlarmHandler
import com.oplus.alarmclock.utils.AlarmRingUtils
import com.oplus.alarmclock.utils.AlarmSpotifyUtils
import com.oplus.alarmclock.utils.AlarmWeatherUtils
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log

class AddAlarmRingManager(private val alarmManager: AddAlarmManager, val mContext: Context) {

    companion object {
        const val TAG = "AddAlarmRingManager"
    }

    var mHandlerQuery: AsyncQueryHandler? = null
    var mSetAlarmHandler: SetAlarmHandler? = null

    fun initHandler() {
        alarmManager.apply {
            viewHolder.apply {
                if (mSetAlarmHandler == null) {
                    mSetAlarmHandler = SetAlarmHandler(mFragment, mHandlerThread?.looper)
                }
            }
        }
    }

    /**
     * 点击铃声
     */
    fun llRingClick() {
        alarmManager.apply {
            viewHolder.apply {
                mFragment.activity?.let { act ->
                    workdayManage.dismissDialog()
                    //内外销都需要获取铃声权限后再进入铃声选择界面
                    if (mRuntimePermissionAlert != null) {
                        mRuntimePermissionAlert.expCheckPermission(act, RuntimePermissionAlert.REQUEST_CODE_MEDIA_ALARM, false)
                    }
                }
            }
        }
    }

    /**
     * 进入振动选择界面
     */
    private fun enterAlarmVibrator() {
        alarmManager.viewHolder.apply {
            val intent = AlarmRingUtils.buildVibrateIntent(mAlarm.vibrate, mContext)
            startActivityForResultWithAttached(intent, AddAlarmViewHolder.VIBRATE_REQUEST_CODE)
        }
    }

    /**
     * 进入选择铃音界面
     */
    fun enterAlarmRing() {
        alarmManager.viewHolder.apply {
            Log.d(TAG, "onPreferenceTreeClick mRingPref  : " + mAlarm.alert + " isNewAlarm【" + mIsNewAlarm + "】")
            var showSilent = false
            if (!isSupportSettingDefaultAlarm) {
                showSilent = true
            }
            val intent = AlarmRingUtils.buildDefaultRingtonePickIntent(mContext, mAlarm.alert, mAlarm.vibrate, showSilent, AlarmRingUtils.RING_ALARM)

            if (isSupportSettingDefaultAlarm) {
                intent.putExtra(RingtoneManager.EXTRA_RINGTONE_TITLE,
                        AlarmClockApplication.getInstance().getString(R.string.oplus_ring))
            } else {
                if (FoldScreenUtils.isRealOslo() || !DeviceUtils.isLinearmotoSupport(mContext)) {
                    intent.putExtra(RingtoneManager.EXTRA_RINGTONE_TITLE,
                            AlarmClockApplication.getInstance().getString(R.string.oplus_ring))
                } else {
                    intent.putExtra(RingtoneManager.EXTRA_RINGTONE_TITLE,
                            AlarmClockApplication.getInstance().getString(R.string.ringtone_and_vibrate))
                }
            }
            intent.putExtra(Utils.NAVIGATE_UP_PACKAGE, mContext.packageName)
            if (mIsNewAlarm) {
                intent.putExtra(Utils.NAVIGATE_UP_TITLE_ID, R.string.new_alarm)
            } else {
                intent.putExtra(Utils.NAVIGATE_UP_TITLE_ID, R.string.set_alarm)
            }
            startToRingSetting(intent)
        }
    }

    private fun startToRingSetting(intent: Intent) {
        alarmManager.apply {
            viewHolder.apply {
                if (AlarmSpotifyUtils.isSupport() && mAlarm.alert != null) {
                    if (AlarmSpotifyUtils.isSpotifyRing(mAlarm.alert.toString())) {
                        intent.putExtra(AlarmRingUtils.EXTRA_SPOTIFY_RINGTONE_DISPLAY,
                                mAlarm.ringName)
                    } else {
                        intent.putExtra(AlarmRingUtils.EXTRA_SPOTIFY_RINGTONE_DISPLAY,
                                AlarmSpotifyUtils.getRingtoneDefaultTips(mContext))
                    }
                }
                try {
                    intent.putExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, mIsFromScreen)
                    startActivityForResultWithAttached(intent, AddAlarmViewHolder.RING_REQUEST_CODE)
                } catch (e: ActivityNotFoundException) {
                    Log.e(TAG, "startActivityForResult failed! e:" + e.message)
                }
            }
        }
    }

    private fun startActivityForResultWithAttached(intent: Intent, requestCode: Int) {
        alarmManager.viewHolder.apply {
            mFragment.activity?.let {
                if (mFragment.isAdded && !mFragment.isDetached && !it.isFinishing) {
                    it.startActivityForResult(intent, requestCode)
                    it.overridePendingTransition(R.anim.open_slide_enter, R.anim.open_slide_exit_noalpha)
                }
            }
        }
    }

    /**
     * 点击振动
     */
    fun llVibrateClick() {
        alarmManager.viewHolder.apply {
            val vibrateChecked: Boolean = !mVibrate.isChecked
            mVibrate.setShouldPlaySound(true)
            mVibrate.isChecked = vibrateChecked
            selectVibrate(vibrateChecked)
        }
    }

    /**
     * 选择振动类型
     */
    fun llVibrateTypeClick() {
        alarmManager.viewHolder.apply {
            enterAlarmVibrator()
        }
    }

    /**
     * 设置振动
     *
     * @param checked
     */
    fun selectVibrate(checked: Boolean) {
        alarmManager.viewHolder.apply {
            Log.i(TAG, "onPreferenceChange Vibrate switch changed  checked = $checked")
            mAlarm.vibrate = if (checked) WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE else WaveformEffect.EFFECT_RINGTONE_NOVIBRATE
        }
    }

    /**
     * Gets the default name and uri for the alarm clock
     *
     * @param alarm
     */
    fun setAutoRingNameAndUri(alarm: Alarm) {
        alarmManager.apply {
            alarmManager.viewHolder.run {
                if (isSupportSettingDefaultAlarm) {
                    if (AlarmRingUtils.isDefaultRing(alarm.alert)) {
                        mRingSummary.text = mContext.resources.getString(R.string.default_ringtone)
                    } else {
                        mSetAlarmHandler?.let {
                            val msg = Message.obtain(it, AddAlarmViewHolder.SET_ALARL_SET_RING_AND_URI, alarm)
                            it.sendMessage(msg)
                        }
                    }
                    setAlarmVibrateName()
                } else {
                    mSetAlarmHandler?.let {
                        val msg = Message.obtain(it, AddAlarmViewHolder.SET_ALARL_SET_RING_AND_URI, alarm)
                        it.sendMessage(msg)
                    }
                }
            }
        }
    }

    /**
     * 设置振动名称
     */
    private fun setAlarmVibrateName() {
        alarmManager.apply {
            alarmManager.viewHolder.run {

                val defaultVibrate = AlarmRingUtils.getDefaultVibrate(mContext)

                if (defaultVibrate == mAlarm.vibrate) {
                    mVibrateText.text = mContext.resources.getString(R.string.default_vibrate)
                } else {
                    //振动名称
                    val key = AlarmRingUtils.transTypeToTitleKey(mAlarm.vibrate)
                    mVibrateText.text = AlarmRingUtils.getStringFromOtherApp(mContext, key)
                }
            }
        }
    }

    /**
     * 设置默认铃声地址
     *
     * @param uri
     */
    fun setDefaultRingUri(uri: String) {
        PrefUtils.putString(mContext, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, AddAlarmManager.SP_NAME_SET_ALARM_RINGTONE, uri)
    }

    private fun getAudioTitle(uri: Uri?) {
        alarmManager.viewHolder.apply {
            var uri = uri
            if (uri != null && AlarmSpotifyUtils.isSpotifyRing(uri.toString())) {
                val spotifyAlertName = AlarmSpotifyUtils.getAlertName(mContext)
                if (spotifyAlertName != null && spotifyAlertName.isNotEmpty()) {
                    mAlarm.ringName = spotifyAlertName
                } else {
                    uri = null
                }
            } //when ring name is null, set the alert to default
            if (uri == null) {
                uri = MediaFile.getDefaultAlarmUri(mContext)
                mAlarm.alert = uri
                if (uri == null) {
                    mAlarm.ringName = ""
                    mHandler.sendEmptyMessage(AddAlarmViewHolder.SET_RING_NAME_PRE)
                    return
                }
            }
            if (uri.toString().startsWith("content://media/external/")) {
                startQuery(AddAlarmViewHolder.SET_ALARL_START_QUERY_AUTO, uri, uri, arrayOf(
                        AlarmRingUtils.getRingtoneTitleColumnsName(), MediaStore.Audio.AudioColumns.DATA), null,
                        null, null)
            } else {
                getDefaultTitle(uri)
            }
        }
    }

    fun startQuery(token: Int,
                   cookies: Any?,
                   uri: Uri?,
                   alarmQueryColumns: Array<String?>?,
                   arg1: String?,
                   arg2: Array<String?>?,
                   arg3: String?) {
        alarmManager.apply {
            viewHolder.apply {
                Log.i(TAG, "startQuery")
                if (mHandlerQuery == null) {
                    mHandlerQuery = SetAlarmAsyncQueryHandler(mContext.contentResolver, mHandler, mDefaultSummary, object : SetAlarmAsyncQueryHandler.SetAlarmRingCallBack {
                        override fun setAlarmRingName(ringName: String) {
                            if (mAlarm != null) {
                                mAlarm.ringName = ringName
                            }
                        }

                        override fun setAlarmAlert(uri: Uri) {
                            if (mAlarm != null) {
                                mAlarm.alert = uri
                            }
                        }

                        override fun getDefaultTitleCall(uri: Uri) {
                            getDefaultTitle(uri)
                        }
                    })
                }
                mHandlerQuery?.startQuery(token, cookies, uri, alarmQueryColumns, arg1, arg2, arg3)
            }
        }
    }

    /**
     * 获取默认铃声
     *
     * @param uri
     */
    private fun getDefaultTitle(uri: Uri) {
        alarmManager.apply {
            viewHolder.apply {
                mSetAlarmHandler?.let {
                    val msg = Message.obtain(it, AddAlarmViewHolder.SET_ALARL_GET_TITLE, uri)
                    it.sendMessage(msg)
                }
            }
        }
    }

    /**
     * 获取默认铃声地址
     *
     * @return
     */
    fun getDefaultRingUri(): String? {
        return PrefUtils.getString(mContext, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, AddAlarmManager.SP_NAME_SET_ALARM_RINGTONE, null)
    }

    /**
     * 设置铃声与震动文本信息
     */
    fun setRingToneText() {
        alarmManager.viewHolder.apply {
            var ringtoneText: String? = ""
            if (!isSupportSettingDefaultAlarm) {
                //是否显震动开关
                if (FoldScreenUtils.isRealOslo() || DeviceUtils.isLinearmotoSupport(mContext)) {
                    mVibrateLayout.visibility = View.GONE
                }
                ringtoneText = if (FoldScreenUtils.isRealOslo() || mVibrateLayout.visibility == View.VISIBLE) {
                    mContext.resources.getString(R.string.oplus_ring)
                } else {
                    mContext.resources.getString(R.string.ringtone_and_vibrate)
                }
                mRingToneText.text = ringtoneText
                mVibrateTypeLayout.visibility = View.GONE
            } else {
                mRingToneText.text = mContext.resources.getString(R.string.oplus_ring)
                if (DeviceUtils.isLinearmotoSupport(mContext)) {
                    mVibrateLayout.visibility = View.GONE
                } else {
                    mVibrateTypeLayout.visibility = View.GONE
                }
                if (FoldScreenUtils.isRealOslo()) {
                    mVibrateLayout.visibility = View.GONE
                    mVibrateTypeLayout.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 恢复铃声选择数据
     */
    fun resumeRingData() {
        alarmManager.viewHolder.apply {
            mFragment.activity?.let {
                if (!it.isFinishing) {
                    val alarmClock = it as AlarmClock
                    if (alarmClock.mRequestCode == AddAlarmViewHolder.RING_REQUEST_CODE && alarmClock.mResultCode == Activity.RESULT_OK) {
                        Log.d(TAG, "resumeRingData requestCode:" + alarmClock.mRequestCode + "resultCode:" + alarmClock.mResultCode)
                        ringResultOK(alarmClock.mResultData)
                        alarmClock.clearResultData()
                    }
                }
            }
        }
    }

    /**
     * 振动
     */
    fun vibrateResultOK(data: Intent) {
        alarmManager.viewHolder.apply {
            val vibrate = data.getIntExtra(ClockConstant.FINAL_VIBRATE_TYPE, WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE)
            val vibrateName = data.getStringExtra(ClockConstant.FINAL_VIBRATE_TITLE_RES_KEY)
            mAlarm.vibrate = vibrate
            setAlarmVibrateName()
        }
    }


    /**
     * 铃声
     */
    fun ringResultOK(data: Intent) {
        alarmManager.viewHolder.apply {
            val prickUri = data.getParcelableExtra<Parcelable>(RingtoneManager.EXTRA_RINGTONE_PICKED_URI)
            if (prickUri != null) {
                mAlarm.alert = data
                        .getParcelableExtra<Parcelable>(RingtoneManager.EXTRA_RINGTONE_PICKED_URI) as Uri
            } else {
                mAlarm.alert = null
            }
            if (mAlarm.alert != null && AlarmWeatherUtils.isDynamicWeatherAlert(mAlarm.alert.toString())) {
                val map = HashMap<String, String>()
                map["dynamic_sound"] = "1"
                ClockOplusCSUtils.onCommon(mContext, ClockOplusCSUtils.EVENT_CHOOSE_DYNAMIC_SOUND, map)
            }
            val vibrate = data.getIntExtra(ClockConstant.EXTRA_VIBRATE_TYPE, WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE)
            mAlarm.vibrate = vibrate
            Log.d(TAG, "onActivityResult() EXTRA_RINGTONE_PICKED_URI: " + mAlarm.alert + " vibrateType: " + vibrate)
            if (mAlarm.alert == null) {
                mAlarm.ringName = ""
                mAlarm.isSilent = true
                mRingSummary.text = mContext.resources.getString(R.string.alert_no_ring)
            } else {
                setDefaultRingName(data)
            }
        }
    }

    private fun setDefaultRingName(data: Intent) {
        alarmManager.viewHolder.apply {
            val dfltAlarmUri = AlarmRingUtils.getDefaultRingtoneUri(AlarmClockApplication.getInstance(), true)
            if (dfltAlarmUri != null && (TextUtils.equals(mAlarm.alert.toString(), dfltAlarmUri.toString())
                            || dfltAlarmUri.toString().startsWith(mAlarm.alert.toString()))
                    && isSupportSettingDefaultAlarm) {
                mRingSummary.text = mContext.resources.getString(R.string.default_ringtone)
            } else {
                if (data.hasExtra(RingtoneManager.EXTRA_RINGTONE_TITLE)
                        || data.hasExtra(ClockConstant.ALARM_SPOTIFY_RINGTONE_NAME)) {
                    var ringtoneName = data.getStringExtra(RingtoneManager.EXTRA_RINGTONE_TITLE)
                    Log.d(TAG, "ringtoneName:$ringtoneName")
                    if (TextUtils.isEmpty(ringtoneName)) {
                        ringtoneName = data.getStringExtra(ClockConstant.ALARM_SPOTIFY_RINGTONE_NAME)
                        Log.d(TAG, "ringtoneName spotify:$ringtoneName")
                    }
                    mAlarm.ringName = ringtoneName
                    Log.d(TAG, "hasExtra EXTRA_RINGTONE_TITLE: " + mAlarm.ringName)
                    mRingSummary.text = mAlarm.ringName
                } else {
                    getAudioTitle(mAlarm.alert)
                    Log.d(TAG, "not Extra EXTRA_RINGTONE_TITLE: " + mAlarm.ringName)
                }
            }
            mAlarm.isSilent = false
        }
    }
}