package com.oplus.alarmclock.alarmclock

import android.annotation.SuppressLint
import android.content.ContentUris
import android.content.Context
import android.text.TextUtils
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.edittext.COUIEditText
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AddAlarmManager.Companion.TOAST_DELAYED_TIME
import com.oplus.alarmclock.alarmclock.AddAlarmViewHolder.THREE
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_10M
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_1M
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_2M
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_30S
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_3M
import com.oplus.alarmclock.alarmclock.utils.GarbAlarmDialogUtils.GARB_ALARM_RING_5M
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmDialogUtils.BUTTON_POSITIVE
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.alarmclock.utils.ClockConstant.ALARM_WORKDAY_SWITCH_OFF
import com.oplus.alarmclock.utils.ClockConstant.ALARM_WORKDAY_SWITCH_ON
import com.oplus.alarmclock.utils.Formatter
import com.oplus.alarmclock.utils.GarbAlarmUtils
import com.oplus.alarmclock.utils.GarbAlarmUtils.buildItemAlarm
import com.oplus.alarmclock.utils.GarbAlarmUtils.buildSubAlarmTime
import com.oplus.alarmclock.utils.LoopAlarmUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.loadAsync
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

/**
 * 添加秒抢闹钟
 */
class GarbAlarmManager(private val alarmManager: AddAlarmManager, val mContext: Context) {

    companion object {
        private const val TAG = "GarbAlarmManager"
        const val GARB_ALARM_RING_0 = 0
        const val GARB_ALARM_RING_1 = 1
        const val GARB_ALARM_RING_2 = 2
        const val GARB_ALARM_RING_3 = 3
        const val GARB_ALARM_RING_4 = 4
        const val GARB_ALARM_RING_5 = 5
    }

    /**
     * 响铃提醒文本
     */
    @SuppressLint("StringFormatMatches")
    val ringTimeTitleArray = arrayOf(
        mContext.resources.getString(R.string.grab_alarm_second_ago),
        mContext.resources.getString(R.string.grab_alarm_minutes_ago),
        mContext.resources.getString(R.string.grab_alarm_minutes_ago_2),
        mContext.resources.getString(R.string.grab_alarm_minutes_ago_3),
        mContext.resources.getString(R.string.grab_alarm_minutes_ago_5),
        mContext.resources.getString(R.string.grab_alarm_minutes_ago_10)
    )

    /**
     * 响铃提醒下标映射
     */
    private val ringTimeTitleMap: Map<Int, Int> = mapOf(
        GARB_ALARM_RING_30S to GARB_ALARM_RING_0,
        GARB_ALARM_RING_1M to GARB_ALARM_RING_1,
        GARB_ALARM_RING_2M to GARB_ALARM_RING_2,
        GARB_ALARM_RING_3M to GARB_ALARM_RING_3,
        GARB_ALARM_RING_5M to GARB_ALARM_RING_4,
        GARB_ALARM_RING_10M to GARB_ALARM_RING_5
    )

    /**
     * 秒抢闹钟日期
     */
    var mGarbAlarmDate: Calendar = Calendar.getInstance()

    /**
     * 子闹钟列表
     */
    val mGarbAlarmRingTimeArray: ArrayList<Alarm> = ArrayList()

    /**
     * 响铃时间多选拼接
     */
    var mGarbAlarmRingString: String = GARB_ALARM_RING_3M.toString() + DatePickerUtils.SPLIT

    /**
     * 秒抢闹钟响铃提醒
     */
    private var mGarbAlarmRingTimeArr: ArrayList<Int> = ArrayList()

    /**
     * 秒抢闹钟日期弹窗
     */
    private var mGarbAlarmDateDialog: AlertDialog? = null


    /**
     * 响铃时间文本
     */
    private var mGarbAlarmRingSubText: String = ""


    /**
     * 切换至秒抢闹钟
     */
    fun onTabGarbAlarm(isClick: Boolean) {
        alarmManager.run {
            viewHolder.run {
                if (viewHolder.mGarbAlarmDateSub == null) {
                    val success = initGarbAlarmLayout(mGarbAlarmViewStub.inflate())
                    if (success) {
                        mGarbAlarmDateLayout.setOnClickListener(mFragment)
                        mGarbAlarmRingLayout.setOnClickListener(mFragment)
                    }
                }
                mWorkDayLayout.visibility = View.GONE
                mGarbAlarmDateLayout.visibility = View.VISIBLE
                mGarbAlarmRingLayout.visibility = View.VISIBLE
                mGarbAlarmDescription.visibility = View.VISIBLE
                mSnoozeLayout.visibility = View.GONE
                mCustomLayout.visibility = View.GONE
                mVibrateTypeLine.visibility = View.GONE
                mAlarm.apply {
                    setmGarbSwitch(ALARM_WORKDAY_SWITCH_ON)
                    workdaySwitch = ALARM_WORKDAY_SWITCH_OFF
                    holidaySwitch = ALARM_WORKDAY_SWITCH_OFF
                    setmLoopSwitch(ALARM_WORKDAY_SWITCH_OFF)
                }
                mLoopAlarmListLayout.visibility = View.GONE
                mTimePickerLayout.visibility = View.VISIBLE
                dimissLoopAlarmTips()
                setViewBackGround(viewHolder)
                parserGarbAlarm(isClick)
                mGarbAlarmRingSub.text = mGarbAlarmRingSubText
                setGarbAlarmDateTitle(mGarbAlarmDate)
                mAlarmLabel.setBoxBackgroundMode(COUIEditText.MODE_BACKGROUND_NONE)
                mAnimatorUtil.changeToGarb()
                updateLeftTimeInfo()
                if (!isClick) {
                    //设置默认值
                    mAlarm.setmSpecialAlarmDays(DatePickerUtils.SPLIT)
                    mAlarm.setmLoopRestDays(DatePickerUtils.SPLIT)
                    mAlarm.loopAlarmList.clear()
                }
                if (Utils.isRtl()) {
                    mGarbAlarmImage.setImageResource(R.drawable.add_alarm_garb_info_rtl)
                } else {
                    mGarbAlarmImage.setImageResource(R.drawable.add_alarm_garb_info)
                }
            }
        }
    }

    /**
     * 设置选项背景
     */
    fun setViewBackGround(viewHolder: AddAlarmViewHolder) {
        viewHolder.run {
            COUICardListHelper.setItemCardBackground(
                mAlarmLabelLayout,
                COUICardListHelper.getPositionInGroup(1, 1)
            )
            COUICardListHelper.setItemCardBackground(
                mGarbAlarmDateLayout,
                COUICardListHelper.getPositionInGroup(THREE, 0)
            )
            COUICardListHelper.setItemCardBackground(
                mGarbAlarmRingLayout,
                COUICardListHelper.getPositionInGroup(THREE, 1)
            )
            COUICardListHelper.setItemCardBackground(
                mGarbAlarmDescription,
                COUICardListHelper.getPositionInGroup(THREE, 2)
            )
            COUICardListHelper.setItemCardBackground(
                mRingLayout,
                COUICardListHelper.getPositionInGroup(THREE, 0)
            )
            COUICardListHelper.setItemCardBackground(
                mVibrateTypeLayout,
                COUICardListHelper.getPositionInGroup(THREE, 2)
            )
            COUICardListHelper.setItemCardBackground(
                mVibrateLayout,
                COUICardListHelper.getPositionInGroup(THREE, 2)
            )
            mVibrateLine.visibility = View.GONE
        }
    }

    /**
     * 设置秒抢下次响铃信息
     * @param ringCalendar 下次响铃时间
     */
    fun setGarbAlarmNextRing(ringCalendar: Calendar, alarm: Alarm) {
        kotlin.runCatching {
            alarmManager.viewHolder.run {
                val ringInfo =
                    GarbAlarmUtils.garbAlarmTypeString(mContext, alarm, ringCalendar.timeInMillis)
                mGarbAlarmRingDex.text = ringInfo.third
                if (ringCalendar.timeInMillis - System.currentTimeMillis() <= 0) {
                    mGarbAlarmRingSec.text = "--"
                    mGarbAlarmInfo.text = ""
                    return
                }
                val ringTimeInfo = GarbAlarmUtils.garbAlarmRingTimeInfo(
                    mContext,
                    Formatter.getTimeFormatWithoutAMPM(mContext, false),
                    ringCalendar.timeInMillis, false
                )
                val sdf = SimpleDateFormat("ss", Locale.getDefault())
                val ringSec = sdf.format(ringCalendar.time)
                mGarbAlarmRingSec.text = ringSec
                mGarbAlarmInfo.text = "$ringTimeInfo:"
            }
        }
    }

    /**
     * 解析闹钟数据
     */
    private fun parserGarbAlarm(isClick: Boolean) {
        alarmManager.viewHolder.run {
            if (mAlarm.id == 0L) {
                //新建闹钟
                mGarbAlarmRingSubText = parserGarbAlarmRingText()
            } else {
                //编辑闹钟
                if (!isClick) {
                    mGarbAlarmDate = GarbAlarmUtils.parseGarbAlarmDate(mAlarm)
                    if (mGarbAlarmRingString == GARB_ALARM_RING_3M.toString() + DatePickerUtils.SPLIT
                        && mAlarm.getmLoopRestDays() != DatePickerUtils.SPLIT
                    ) {
                        mGarbAlarmRingString = mAlarm.getmLoopRestDays()
                    }
                }
                mGarbAlarmRingSubText = parserGarbAlarmRingText()
            }
        }
    }

    /**
     * 解析秒抢闹钟响铃信息
     */
    fun parserGarbAlarmRingText(): String {
        val titleBuilder = StringBuilder()
        kotlin.runCatching {
            mGarbAlarmRingTimeArr.clear()
            val ringArr = mGarbAlarmRingString.split(DatePickerUtils.SPLIT.toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray()
            val ringSpilt = mContext.getString(R.string.grab_alarm_already_separation)
            for ((index, value) in ringArr.withIndex()) {
                val ringIndex: Int? = ringTimeTitleMap[value.toInt()]
                //拼接选中的响铃时间字符串
                var str = ringTimeTitleArray[0]
                ringIndex?.let {
                    str = ringTimeTitleArray[it]
                }
                titleBuilder.append(str)
                if (index != ringArr.size - 1) {
                    titleBuilder.append(ringSpilt)
                }
                mGarbAlarmRingTimeArr.add(value.toInt())
                //子闹钟
                mGarbAlarmRingTimeArray.clear()
                alarmManager.viewHolder.run {
                    val ringTime = buildSubAlarmTime(
                        value.toInt(),
                        mOplusTimePicker.currentMinute,
                        mOplusTimePicker.currentHour
                    )
                    val item = buildItemAlarm(0, 0, ringTime)
                    mGarbAlarmRingTimeArray.add(item)
                }
            }
        }.onFailure {
            Log.e(TAG, "parserGarbAlarmRingText ERROR")
        }
        return titleBuilder.toString()
    }

    /**
     * 秒抢闹钟日期选择
     */
    fun showGarbAlarmDate(context: Context?) {
        context?.let {
            mGarbAlarmDateDialog = GarbAlarmDialogUtils.showGarbAlarmDateDialog(
                mGarbAlarmDate.timeInMillis,
                it
            ) { click, year, month, day ->
                if (click == BUTTON_POSITIVE) {
                    mGarbAlarmDate.apply {
                        set(Calendar.YEAR, year)
                        set(Calendar.MONTH, month)
                        set(Calendar.DAY_OF_MONTH, day)
                        set(Calendar.MILLISECOND, 0)
                        set(Calendar.SECOND, 0)
                    }
                    setGarbAlarmDateTitle(mGarbAlarmDate)
                    alarmManager.updateLeftTimeInfo()
                }
            }
        }
    }

    /**
     * 秒抢响铃时间选择弹窗
     */
    fun showGarbAlarmRing(context: Context?) {
        context?.let {
            alarmManager.viewHolder.run {
                mGarbAlarmDateDialog =
                    GarbAlarmDialogUtils.showGarbAlarmRingDialog(
                        mGarbAlarmRingString,
                        ringTimeTitleArray,
                        it
                    ) { click, ringStr, ringArr, ringBuilder ->
                        if (click == BUTTON_POSITIVE) {
                            mGarbAlarmRingTimeArr.clear()
                            mGarbAlarmRingTimeArr.addAll(ringArr)
                            mGarbAlarmRingString = ringBuilder
                            mGarbAlarmRingSubText = parserGarbAlarmRingText()
                            mGarbAlarmRingSub.text = mGarbAlarmRingSubText
                            alarmManager.updateLeftTimeInfo()
                        }
                    }
            }
        }
    }

    /**
     * 秒抢闹钟日期时间是否过期
     */
    fun canSaveGarbAlarm(alarm: Alarm, context: Context): Boolean {
        return GarbAlarmUtils.canSaveGarbAlarm(
            alarm,
            context,
            mGarbAlarmDate,
            mGarbAlarmRingTimeArr
        )
    }

    /**
     * 保存秒抢闹钟
     */
    fun asyncGarbAlarm() {
        alarmManager.viewHolder.run {
            if (mAlarm.label == null) {
                mAlarm.label = mContext.getString(R.string.grab_alarm_title)
            }
            mAlarm.setmGarbSwitch(ALARM_HOLIDAY_SWITCH_ON)
            mAlarm.isEnabled = true
            //响铃日期
            val days = DatePickerUtils.todayAfter1970days(mGarbAlarmDate)
            mAlarm.setmSpecialAlarmDays(days.toString())
            //响铃信息
            mAlarm.setmLoopRestDays(buildGarbRingText())
            mAlarm.setmWorkDayType(0)
            mAlarm.workdaySwitch = 0
            mAlarm.setRepeat(0)
            mAlarm.setmLoopSwitch(0)
            //响铃次数
            mAlarm.ringNum = ClockConstant.SNOOZE_RING_NUM
            Log.d(TAG, "mAlarm:$mAlarm")
            mFragment.activity?.let { act ->
                if (!act.isFinishing) {
                    act.loadAsync {
                        if (mIsNewAlarm) {
                            //保存主闹钟
                            val values = AlarmUtils.createContentValues(mContext, mAlarm, false)
                            values.put(ClockContract.Alarm.ENABLED, 1)
                            val uri = AlarmUtils.alarmResolverInsert(
                                mContext.contentResolver,
                                ClockContract.ALARM_CONTENT_URI,
                                values
                            )
                            var alarmId = 0L
                            if (uri != null) {
                                alarmId = ContentUris.parseId(uri)
                                if (alarmId != -1L) {
                                    mAlarm.id = alarmId
                                }
                                //添加子闹钟
                                addSubAlarm(alarmId)
                                AlarmUtils.enableAlarm(mContext, mAlarm, false)
                            } else {
                                false
                            }
                        } else {
                            //更新闹钟信息
                            LoopAlarmUtils.deleteLoopAlarm(act, mAlarm)
                            //添加子闹钟
                            addSubAlarm(mAlarm.id)
                            AlarmUtils.enableAlarm(mContext, mAlarm, false)
                            AlarmUtils.updateAlarmInfo(
                                AlarmClockApplication.getInstance(),
                                mAlarm,
                                false
                            )
                        }
                        val context = mFragment.context
                        alarmManager.run {
                            mToastHandler.postDelayed(
                                { AlarmUtils.popAlarmSetToast(context, mAlarm) },
                                TOAST_DELAYED_TIME.toLong()
                            )
                        }
                        //秒抢闹钟创建完成后更新提前响铃通知，排除秒抢
                        AlarmStateManager.showNextAlarmNotices(mContext)
                    }
                }
            }
        }
    }


    /**
     * 添加响铃子闹钟
     */
    private fun addSubAlarm(alarmId: Long) {
        alarmManager.viewHolder.run {
            val loopAlarmList: ArrayList<Alarm> = ArrayList()
            for ((index, value) in mGarbAlarmRingTimeArr.withIndex()) {
                val ringTime = buildSubAlarmTime(value, mAlarm.minutes, mAlarm.hour)
                val itemAlarm = buildItemAlarm(alarmId, index, ringTime)
                loopAlarmList.add(buildItemAlarm(alarmId, index, ringTime))
                LoopAlarmUtils.saveSubLoopAlarm(mContext, itemAlarm)
            }
            mAlarm.loopAlarmList = loopAlarmList
            mAlarm.id = alarmId
        }
    }


    /**
     * 设置秒抢副标题
     */
    private fun setGarbAlarmDateTitle(date: Calendar) {
        alarmManager.viewHolder.run {
            val dataStr = Utils.getLocaleDateAllFormat(mContext, date.timeInMillis)
            mGarbAlarmDateSub.text = dataStr
        }
    }

    /**
     * 包装响铃信息文本
     */
    private fun buildGarbRingText(): String {
        val str = StringBuilder()
        for ((index, value) in mGarbAlarmRingTimeArr.withIndex()) {
            str.append(value.toString() + DatePickerUtils.SPLIT)
        }
        return str.toString()
    }
}