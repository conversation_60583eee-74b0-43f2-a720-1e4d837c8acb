/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * Description : activity for set workday type
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-02-20, xiaolong,yu, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;

public class AlarmWorkdaySettingsActivity extends BaseActivity {
    private final String TAG = "AlarmWorkdaySettingsActivity";

    private AlarmWorkdaySettingsFragment mAlarmWorkdaySettingsFragment;
    private FrameLayout mListView;
    private AppBarLayout mAppBarLayout;
    private boolean mIsFromScreen;

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "Receive action ACTION_SCREEN_OFF.");
            if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                getWindow().setFlags(ClockConstant.FLAG_UNREGISTER_SHOW_WHEN_LOCKED, WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.alarm_workday_settings_activity);
        COUIToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.d(TAG, "toolbar  onClick");
                onBackPressed();
            }
        });
        toolbar.setNavigationIcon(getResources().getDrawable(R.drawable.color_icon_cancel));
        mAppBarLayout = findViewById(R.id.app_bar);
        mListView = findViewById(R.id.fragment_container);
        int padding = 0;
        switch (obtainUiMode()) {
            case LARGE_HORIZONTAL:
                padding = getResources().getDimensionPixelSize(R.dimen.settings_oslo_land_padding);
                mListView.setPadding(padding, mAppBarLayout.getMeasuredHeight(), padding, 0);
                break;
            case LARGE_VERTICAL:
                padding = getResources().getDimensionPixelSize(R.dimen.settings_oslo_port_padding);
                mListView.setPadding(padding, mAppBarLayout.getMeasuredHeight(), padding, 0);
                break;
            case MIDDLE:
            case NORMAL:
            case SMALL:
            default:
                mListView.setPadding(0, mAppBarLayout.getMeasuredHeight(), 0, 0);
                break;
        }
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView);

        mAlarmWorkdaySettingsFragment = new AlarmWorkdaySettingsFragment();
        getSupportFragmentManager().beginTransaction().replace(R.id.fragment_container, mAlarmWorkdaySettingsFragment).commit();
        Intent intent = getIntent();
        if (intent != null) {
            mIsFromScreen = intent.getBooleanExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, false);
            Log.d(TAG, "onCreate mIsFromScreen = " + mIsFromScreen);
            if (mIsFromScreen) {
                final IntentFilter intentFilter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
                registerReceiver(mReceiver, intentFilter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
                getWindow().setFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED,
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            finish();
        } else if (itemId == R.id.save) {
            if ((mAlarmWorkdaySettingsFragment != null) && mAlarmWorkdaySettingsFragment.isAdded()) {
                mAlarmWorkdaySettingsFragment.saveWorkdayType();
            }
        }

        return true;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        Log.d(TAG, "onCreateOptionsMenu");
        getMenuInflater().inflate(R.menu.activity_edit_workday_type, menu);
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mIsFromScreen) {
            unregisterReceiver(mReceiver);
        }
        if (mAlarmWorkdaySettingsFragment != null) {
            mAlarmWorkdaySettingsFragment.onDestroy();
            mAlarmWorkdaySettingsFragment = null;
        }
    }

    @Override
    public void onOsloLandOrientation() {
        super.onOsloLandOrientation();
        mListView.setPadding(
                getResources().getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                mAppBarLayout.getMeasuredHeight(),
                getResources().getDimensionPixelSize(R.dimen.settings_oslo_land_padding),
                0);
    }

    @Override
    public void onOsloPortOrientation() {
        super.onOsloPortOrientation();
        mListView.setPadding(getResources().getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                mAppBarLayout.getMeasuredHeight(),
                getResources().getDimensionPixelSize(R.dimen.settings_oslo_port_padding),
                0);
    }

    @Override
    protected void onOsloOtherOrientation() {
        super.onOsloOtherOrientation();
        mListView.setPadding(0, mAppBarLayout.getMeasuredHeight(), 0, 0);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.coui_fade_in_fast, R.anim.anim_push_down_exit);
    }
}
