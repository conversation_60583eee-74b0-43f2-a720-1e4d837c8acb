/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 *  AlarmRepeat info
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-5-20, yuxiaolong, create
 *
 ************************************************************/


package com.oplus.alarmclock.alarmclock;

import android.os.Parcel;
import android.os.Parcelable;


public class AlarmRepeat implements Parcelable {

    /**
     * 间隔分钟数默认值
     */
    public static final int[] REPEAT_ALERT_INTERVAL = {5, 10, 15, 20, 25, 30};
    /**
     * 响铃时长默认值
     */
    public static final int[] REPEAT_ALERT_LENGTH = {1, 5, 10, 15, 20, 30};
    /**
     * 响铃时长默认5分钟
     */
    public static final int DEFAULT_ALERT_LENGTH_INDEX = 1;
    /**
     * 响铃次数默认值
     */
    static final int[] REPEAT_ALERT_NUM = {2, 3, 5, 10};


    static final int DEFAULT_ALERT_INTERVAL_INDEX = 0;
    /**
     * 响铃次数默认3次
     */
    static final int DEFAULT_ALERT_NUM_INDEX = 1;

    private long mId = -1;
    private int mAlarmDuration;//[1,5,10,15,20,30]
    private int mAlarmInterval;//[5,10,15,20,25,30]
    private int mAlarmNum;//[1,2,3,5,10]
    private int mAlarmPrompt; // 1,0

    static int getLengthIndexByValue(int value) {
        for (int i = 0; i < REPEAT_ALERT_LENGTH.length; i++) {
            if (value == REPEAT_ALERT_LENGTH[i]) {
                return i;
            }
        }
        return DEFAULT_ALERT_LENGTH_INDEX;
    }

    static int getIntervalIndexByValue(int value) {
        for (int i = 0; i < REPEAT_ALERT_INTERVAL.length; i++) {
            if (value == REPEAT_ALERT_INTERVAL[i]) {
                return i;
            }
        }
        return DEFAULT_ALERT_INTERVAL_INDEX;
    }

    public AlarmRepeat() {
    }

    protected AlarmRepeat(Parcel in) {
        if (in != null) {
            mId = in.readLong();
            mAlarmDuration = in.readInt();
            mAlarmInterval = in.readInt();
            mAlarmNum = in.readInt();
            mAlarmPrompt = in.readInt();
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        if (dest != null) {
            dest.writeLong(mId);
            dest.writeInt(mAlarmDuration);
            dest.writeInt(mAlarmInterval);
            dest.writeInt(mAlarmNum);
            dest.writeInt(mAlarmPrompt);
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<AlarmRepeat> CREATOR = new Creator<AlarmRepeat>() {
        @Override
        public AlarmRepeat createFromParcel(Parcel in) {
            return new AlarmRepeat(in);
        }

        @Override
        public AlarmRepeat[] newArray(int size) {
            return new AlarmRepeat[size];
        }
    };

    public static AlarmRepeat build(long mId, int mAlarmDuration, int mAlarmInterval, int mAlarmNum, int mAlarmPrompt) {
        AlarmRepeat repeat = new AlarmRepeat();
        repeat.setmId(mId);
        repeat.setmAlarmDuration(mAlarmDuration);
        repeat.setmAlarmInterval(mAlarmInterval);
        repeat.setmAlarmNum(mAlarmNum);
        repeat.setmAlarmPrompt(mAlarmPrompt);
        return repeat;
    }

    public long getmId() {
        return mId;
    }

    public void setmId(long mId) {
        this.mId = mId;
    }


    public int getmAlarmDuration() {
        return mAlarmDuration;
    }

    public void setmAlarmDuration(int mAlarmDuration) {
        this.mAlarmDuration = mAlarmDuration;
    }

    public int getmAlarmInterval() {
        return mAlarmInterval;
    }

    public void setmAlarmInterval(int mAlarmInterval) {
        this.mAlarmInterval = mAlarmInterval;
    }

    public int getmAlarmNum() {
        return mAlarmNum;
    }

    public void setmAlarmNum(int mAlarmNum) {
        this.mAlarmNum = mAlarmNum;
    }

    public int getmAlarmPrompt() {
        return mAlarmPrompt;
    }

    public void setmAlarmPrompt(int mAlarmPrompt) {
        this.mAlarmPrompt = mAlarmPrompt;
    }

    public void initAlarmRepeat() {
        this.setmAlarmPrompt(0);
        this.setmAlarmDuration(REPEAT_ALERT_LENGTH[DEFAULT_ALERT_LENGTH_INDEX]);
        this.setmAlarmInterval(REPEAT_ALERT_INTERVAL[DEFAULT_ALERT_INTERVAL_INDEX]);
        this.setmAlarmNum(REPEAT_ALERT_NUM[DEFAULT_ALERT_NUM_INDEX]);
    }

    @Override
    public String toString() {
        return "AlarmRepeat{"
                + "mId=" + mId
                + ", mAlarmDuration=" + mAlarmDuration
                + ", mAlarmInterval=" + mAlarmInterval
                + ", mAlarmNum=" + mAlarmNum
                + ", mAlarmPrompt=" + mAlarmPrompt
                + '}';
    }

    public static void checkAlarmRepeat(AlarmRepeat repeat) {
        if (!contains(REPEAT_ALERT_LENGTH, repeat.getmAlarmDuration())) {
            repeat.setmAlarmDuration(REPEAT_ALERT_LENGTH[DEFAULT_ALERT_LENGTH_INDEX]);
        }
        if (!contains(REPEAT_ALERT_INTERVAL, repeat.getmAlarmInterval())) {
            repeat.setmAlarmInterval(REPEAT_ALERT_INTERVAL[DEFAULT_ALERT_INTERVAL_INDEX]);
        }
        if (!contains(REPEAT_ALERT_NUM, repeat.getmAlarmNum())) {
            repeat.setmAlarmNum(REPEAT_ALERT_INTERVAL[DEFAULT_ALERT_NUM_INDEX]);
        }
    }

    private static boolean contains(int[] array, int value) {
        if (array == null) {
            return false;
        }
        for (int element : array) {
            if (element == value) {
                return true;
            }
        }
        return false;
    }
}
