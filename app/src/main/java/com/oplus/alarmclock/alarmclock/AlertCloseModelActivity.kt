/*********************************************************************************
 ** Copyright (C), 2008-2030, Oplus, All rights reserved.
 **
 ** File: - AlertCloseModelActivity.kt
 ** Description:
 **    AlertCloseModelActivity.
 **
 ** Version: 1.0
 ** Date: 2022-06-30
 ** Author: RongWenYang.Clock
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** RongWenYang    2022-06-30   1.0    Create this module
 ********************************************************************************/

package com.oplus.alarmclock.alarmclock

import android.content.res.Configuration
import android.os.Bundle
import android.view.MenuItem
import android.widget.FrameLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.Utils
import com.oplus.utils.ActivityUtils

class AlertCloseModelActivity : BaseActivity() {
    private var mAppBarLayout: AppBarLayout? = null
    private var mListView: FrameLayout? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_alert_close_model)
        val toolbar = findViewById<COUIToolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar!!.setDisplayHomeAsUpEnabled(true)
        mAppBarLayout = findViewById<AppBarLayout>(R.id.app_bar)
        mListView = findViewById<FrameLayout>(R.id.fragment_container)
        setLayoutPadding(mListView, mAppBarLayout)
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView)
        supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, AlertCloseModelFragment()).commit()
        setFlexibleWindowBg()
        setFinishOnTouchOutside(false)
        addActivityWeakAndSetOnTouch()
    }

    override fun onDestroy() {
        super.onDestroy()
        ActivityUtils.sSettingActivity.remove(mActivityWeak)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        setFlexibleWindowBg()
    }

    override fun onScreenFold() {
        super.onScreenFold()
        setLayoutPadding(mListView, mAppBarLayout)
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> finish()
            else -> {
            }
        }
        return true
    }
}