/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AlarmFixService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/6/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/6/5     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.Context
import androidx.lifecycle.LifecycleService
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder
import com.oplus.alarmclock.provider.SPContentHelper
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.loadAsync
import com.oplus.clock.common.utils.then
import java.util.Calendar

class AlarmFixService : LifecycleService() {

    companion object {
        private const val TAG = "AlarmFixService"
        const val KEY_FIX_ALARM_TIME = "key_fix_alarm_time"
        private const val TIMEOUT_OFFSET = 1000L
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate")
        fixAlarmIfNeed()
    }

    /**
     *
     */
    private fun fixAlarmIfNeed() {
        var recordFixTime = true
        val context = AlarmClockApplication.getInstance()
        loadAsync {
            val alarms = AlarmUtils.getStatusAllAlarms(context, "1")
            val currentTime = Calendar.getInstance().timeInMillis
            if (alarms.isNullOrEmpty()) {
                return@loadAsync
            }
            for (alarm in alarms) {
                Log.d(TAG, "fix alarm: $alarm")
                //修复非秒抢闹钟
                if (alarm.getmGarbSwitch() != 1) {
                    ScheduleUtils.getSchedulesOfAlarmNoExecutor(context, alarm)
                        ?.forEach { schedule ->
                            val (needFixed, needRecord) = needFix(alarm, schedule, currentTime)
                            if (needFixed) {
                                fixAlarm(context, alarm)
                                AlarmRingStatisticUtils.statisticsFixedAlarm(
                                    context,
                                    schedule,
                                    alarm
                                )
                            }
                            recordFixTime = needRecord
                        } ?: also {
                        fixAlarm(context, alarm)
                        AlarmRingStatisticUtils.statisticsFixedAlarm(context, null, alarm)
                    }
                }
            }
        } then {
            Log.d(TAG, "fix alarm stop self:$recordFixTime ${Calendar.getInstance().timeInMillis}")
            if (recordFixTime) {
                SPContentHelper.init(context)
                SPContentHelper.save(
                    KEY_FIX_ALARM_TIME,
                    Calendar.getInstance().timeInMillis
                )
            }
            stopSelf()
        }
    }

    /**
     * 判断是否需要修复schedule表数据
     * 1. enable的alarm没有在schedule表中，schedule = null
     * 2. alarmTime < currentTime, 即将响铃闹钟已过期
     */
    private fun needFix(
        alarm: Alarm?,
        schedule: AlarmSchedule?,
        currentTime: Long
    ): Pair<Boolean, Boolean> {
        if (alarm == null) {
            Log.e(TAG, "not need fix, result alarm is null")
            return Pair(false, true)
        }
        if (schedule == null) {
            Log.e(TAG, "need fix, schedule is null!")
            return Pair(true, true)
        }
        if (schedule.alarmId != alarm.id) {
            Log.e(TAG, "not need fix, alarm id is different!")
            return Pair(false, true)
        }
        val alarmTime = ScheduleUtils.getAlarmTimeInMills(schedule)
        // Start the alarm and schedule timeout timer for it
        val alarmMills = ScheduleUtils.getTimeoutInMills(
            alarmTime,
            AlarmStateManager.getAlarmRepeat(this).getmAlarmDuration().toLong()
        )
        if (CurrentAlarmScheduleHolder.getAlarmSchedule() == null) {
            return if (currentTime > (alarmMills + TIMEOUT_OFFSET)) {
                Log.d(TAG, "need fix, alarmTime($alarmTime) < currentTime($currentTime)")
                Pair(true, true)
            } else {
                Pair(false, true)
            }
        }
        return Pair(false, true)
    }

    private fun fixAlarm(context: Context?, alarm: Alarm?) {
        if (context == null || alarm == null) {
            return
        }
        if (alarm.isRepeatAlarm) {
            ScheduleUtils.clearAllSchedulesOfAlarm(context, alarm.id)
            AlarmUtils.setupAlarmInstance(context, alarm)
            AlarmStateManager.showNextAlarmNotices(context)
        } else {
            AlarmUtils.disableAlarm(context, alarm.id, false)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
    }
}