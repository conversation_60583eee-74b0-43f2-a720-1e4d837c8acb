/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :add a alarm or edit a alarm
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-07-26, Yuxiaolong, create
 ************************************************************/
// OPLUS Java File Skip Rule:LineLength
package com.oplus.alarmclock.alarmclock;

import static com.oplus.alarmclock.alarmclock.AddAlarmViewHolder.RING_REQUEST_CODE;
import static com.oplus.alarmclock.alarmclock.AddAlarmViewHolder.VIBRATE_REQUEST_CODE;
import static com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper.isSupportGarbAlarm;
import static com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_FINISH_PAGE;
import static com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_ADD_RESET;
import static com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_RING_TIME_DATA;
import static com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_WORK_DATA_CLICK_EX;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.widget.CompoundButton;
import android.widget.FrameLayout;

import com.coui.appcompat.panel.COUIBottomSheetDialogFragment;
import com.coui.appcompat.uiutil.UIUtil;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.ClockEventDispatcher;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.adapter.AddAlarmTypeAdapter;
import com.oplus.alarmclock.utils.AiTripHelper;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.HapticsStyleController;
import com.oplus.alarmclock.utils.LoopAlarmUtils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.OnClockInflateFinishedListener;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.WindowInsetsCompat;

public class AddAlarmFragment extends BaseFragment implements View.OnClickListener, CompoundButton.OnCheckedChangeListener,
        AddAlarmTypeAdapter.OnTabChangeListener, HapticsStyleController.OnTactileSteplessChangeListener,
        HapticsStyleController.OnTactileStyleChangeListener {


    public static final int TAB_TYPE_INDEX_0 = 0;
    public static final int TAB_TYPE_INDEX_1 = 1;
    public static final int TAB_TYPE_INDEX_2 = 2;
    public static final int TAB_TYPE_INDEX_3 = 3;

    private static final String TAG = "AddAlarmFragment";
    private static final int SET_ALERT_TYPE_DIALOG = 6;
    public static boolean sCanClickDelay = false;
    public AddAlarmManager mAddAlarmManager;
    public AddAlarmViewHolder mViewHolder;
    private Context mContext;
    private View.OnApplyWindowInsetsListener mWindowInsetsListener;
    private Intent mIntent;
    private OnClockInflateFinishedListener<AddAlarmFragment> mOnInflateFinishedListener;

    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();

    public static AddAlarmFragment getInstance(Bundle bundle, Intent intent, OnClockInflateFinishedListener<AddAlarmFragment> inflateFinishedListener) {
        AddAlarmFragment fragment = new AddAlarmFragment();
        fragment.setParamIntent(intent);
        fragment.setOnInflateFinishedListener(inflateFinishedListener);
        fragment.setArguments(bundle);
        return fragment;
    }

    public void setParamIntent(Intent mIntent) {
        this.mIntent = mIntent;
    }

    public void setOnInflateFinishedListener(OnClockInflateFinishedListener<AddAlarmFragment> mOnInflateFinishedListener) {
        this.mOnInflateFinishedListener = mOnInflateFinishedListener;
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.i(TAG, " onCreateView");
        mViewHolder = new AddAlarmViewHolder();
        mContext = AlarmClockApplication.getInstance();
        FrameLayout containerView = new FrameLayout(mContext);
        mViewHolder.inflate(containerView, savedInstanceState, this, (view, resid, parent) -> {
            if (mViewHolder.init(view)) {
                containerView.removeAllViews();
                containerView.addView(view);
                if (savedInstanceState != null) {
                    setArguments(savedInstanceState);
                }
                mAddAlarmManager = new AddAlarmManager(mViewHolder, mContext);
                mAddAlarmManager.initData(mIntent);
                initMenu();
                initEvent();
                initWindowInsertListener();
                HapticsStyleController.registerContentObserver(mContext, this, this);
                if (mOnInflateFinishedListener != null) {
                    mOnInflateFinishedListener.onInflateFinished(null);
                }
                sCanClickDelay = false;
                mAddAlarmManager.onResume();
            }
        });
        return containerView;
    }

    private void initEvent() {
        //轮班闹钟设置页面返回
        LiteEventBus.Companion.getInstance().with(EVENT_ADD_LOOP_ALARM_WORK_FINISH_PAGE, String.valueOf(hashCode())).observe(this, o -> {
            if (o instanceof Alarm) {
                mViewHolder.mAlarm = (Alarm) o;
            }
            mAddAlarmManager.changeLoopAlarm();
        });
        //下次响铃时间计算完成，保存下次响铃时间到主闹钟
        LiteEventBus.Companion.getInstance().with(EVENT_LOOP_ALARM_RING_TIME_DATA, String.valueOf(hashCode())).observe(this, o -> {
            if (o instanceof Pair) {
                Pair<Integer, Integer> ringTime = (Pair<Integer, Integer>) o;
                mViewHolder.mAlarm.setHour(ringTime.first);
                mViewHolder.mAlarm.setMinutes(ringTime.second);
            }
        });
        //轮班闹钟详情页取消，需重置回修改前的闹钟数据
        LiteEventBus.Companion.getInstance().with(EVENT_LOOP_ALARM_ADD_RESET, String.valueOf(hashCode())).observe(this, o -> {
            if (o instanceof Alarm) {
                mViewHolder.mAlarm = ((Alarm) o).deepCopy();
                if (DeviceUtils.isExpVersion(mContext)) {
                    //外销刷新列表，同步数据
                    mAddAlarmManager.notifyData(mViewHolder.mAlarm.getLoopAlarmList(), mViewHolder.mAlarm);
                }
            }
        });

        //外销轮班闹钟详情页保存
        LiteEventBus.Companion.getInstance().with(EVENT_LOOP_ALARM_WORK_DATA_CLICK_EX, String.valueOf(hashCode())).observe(this, o -> {
            if (DeviceUtils.isExpVersion(mContext)) {
                mAddAlarmManager.expChangeLoopAlarm();
            }
        });

    }

    /**
     * 是否可以关闭面板
     */
    public boolean canClosePanel() {
        Rect rect = new Rect();
        mViewHolder.mAddAlarmRootView.getGlobalVisibleRect(rect);
        if (rect.top > LoopAlarmUtils.PANEL_TOUCH_DOWN_DIS) {
            return false;
        }
        return true;
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        LiteEventBus.Companion.getInstance().releaseEvent(String.valueOf(hashCode()));
    }

    /**
     * 软键盘弹出监听
     */
    private void initWindowInsertListener() {
        if ((getActivity() == null) || (getActivity().getWindow() == null) || (mWindowInsetsListener != null)) {
            return;
        }
        View decorView = getActivity().getWindow().getDecorView();
        mWindowInsetsListener = (view, windowInsets) -> {
            int mImeHeight = 0;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                mImeHeight = windowInsets.getInsets(WindowInsetsCompat.Type.ime()).bottom;
            }
            if (mImeHeight > 0) {
                if (mAddAlarmManager != null) {
                    mAddAlarmManager.dimissLoopAlarmTips();
                }
                //键盘弹出
                Rect rect = new Rect();
                int realSize = UIUtil.getScreenHeightRealSize(mContext) - mImeHeight;
                mViewHolder.mAlarmLabel.getGlobalVisibleRect(rect);
                ViewGroup.LayoutParams lay = mViewHolder.mBottomView.getLayoutParams();
                //输入框位置比键盘高度低
                if (rect.bottom - realSize > 0) {
                    lay.height = mImeHeight;
                    mViewHolder.mBottomView.setLayoutParams(lay);
                    //滚动到键盘高度位置
                    mViewHolder.mBottomView.post(() -> mViewHolder.mScrollPanel.scrollBy(0, rect.bottom - realSize));
                }
            } else {
                //键盘隐藏
                ViewGroup.LayoutParams lay = mViewHolder.mBottomView.getLayoutParams();
                lay.height = 0;
                mViewHolder.mBottomView.setLayoutParams(lay);
            }
            WindowInsets mApplyWindowInsets = windowInsets;
            view.onApplyWindowInsets(mApplyWindowInsets);
            return mApplyWindowInsets;
        };
        decorView.setOnApplyWindowInsetsListener(mWindowInsetsListener);
    }

    /**
     * 设置工具栏
     */
    private void initMenu() {
        setHasOptionsMenu(true);
    }


    /**
     * 权限返回结果
     *
     * @param requestCode
     * @param permissions
     * @param grantResults
     */
    public void requestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (mViewHolder.mRuntimePermissionAlert != null) {
            if ((permissions != null) && (permissions.length > 0) && (grantResults != null) && (grantResults.length > 0)) {
                mViewHolder.mRuntimePermissionAlert.requestPermissionsResult(requestCode, permissions, grantResults, false);
            }
        }
    }


    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mAddAlarmManager != null) {
            mAddAlarmManager.updateMargin();
        }
    }

    @Override
    public void onClick(View view) {

        int id = view.getId();
        mAddAlarmManager.dimissLoopAlarmTips();
        if (id == R.id.ll_ring) {
            mAddAlarmManager.getAlarmRingManager().llRingClick();
        } else if (id == R.id.ll_ring_type) {
            if ((getActivity() != null) && (!getActivity().isFinishing())) {
                getActivity().showDialog(SET_ALERT_TYPE_DIALOG);
            }
        } else if (id == R.id.ll_vibrate) {
            mAddAlarmManager.getAlarmRingManager().llVibrateClick();
        } else if (id == R.id.ll_snooze_switch) { //稍后提醒
            mAddAlarmManager.showSnoozePanle(false);
        } else if (id == R.id.add_alarm_work_day_layout) { //工作日类型
            mAddAlarmManager.showWorkPanel(false);
        } else if (id == R.id.add_alarm_repeat_date_pick) {
            mAddAlarmManager.getCustomAlarmManager().showDataPicker();
        } else if (id == R.id.expand || id == R.id.date_picker_header_month_layout) {
            mAddAlarmManager.getCustomAlarmManager().setCalendarCurrentView();
        } else if (id == R.id.add_alarm_holiday_switch_layout) {
            mAddAlarmManager.getCustomAlarmManager().clickHolidaySwitch();
        } else if (id == R.id.ll_vibrate_type) {
            mAddAlarmManager.getAlarmRingManager().llVibrateTypeClick();
        } else if (id == R.id.garb_alarm_date) {
            if (mDoubleClickHelper.canClick()) {
                mAddAlarmManager.getGarbAlarmManager().showGarbAlarmDate(getContext());
            }
        } else if (id == R.id.garb_alarm_ring) {
            if (mDoubleClickHelper.canClick()) {
                mAddAlarmManager.getGarbAlarmManager().showGarbAlarmRing(getContext());
            }
        }
    }

    /**
     * 开关回调事件
     *
     * @param compoundButton
     * @param checked
     */
    @Override
    public void onCheckedChanged(CompoundButton compoundButton, boolean checked) {

        int id = compoundButton.getId();
        if (id == R.id.vibrate_switch) {
            mAddAlarmManager.getAlarmRingManager().selectVibrate(checked);
        } else if (id == R.id.add_alarm_holiday_switch) {
            mAddAlarmManager.getCustomAlarmManager().selectHoliday(checked);
            mViewHolder.setAlarmHolidayTalkback();
        }
    }

    /**
     * 触感振动风格
     *
     * @param tactileStyle
     */
    @Override
    public void onStyleChange(int tactileStyle) {
        mAddAlarmManager.updateVibrateLevel(tactileStyle);
    }

    @Override
    public void onSteplessChange(float tactileStepless) {
        mAddAlarmManager.updateVibrateIntensity(tactileStepless);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        Log.i(TAG, "onActivityResult requestCode = " + requestCode + "    resultCode = " + resultCode);
        if ((data == null) || (mViewHolder == null)) {
            Log.e(TAG, "onActivityResult (data == null) or (mViewHolder == null)");
            return;
        }
        switch (requestCode) {
            case RING_REQUEST_CODE:
                if (resultCode == Activity.RESULT_OK) {
                    mAddAlarmManager.getAlarmRingManager().ringResultOK(data);
                    Log.d(TAG, "RING_REQUEST_CODE OK");
                }
                break;
            case VIBRATE_REQUEST_CODE:
                mAddAlarmManager.getAlarmRingManager().vibrateResultOK(data);
                break;
            default:
                break;
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (mViewHolder != null && mViewHolder.mAlarmLabel != null) {
            mViewHolder.mAlarmLabel.clearFocus();
        }
    }

    @Override
    public void onResume() {
        Log.i(TAG, "onResume");
        super.onResume();
        sCanClickDelay = false;
        if (mAddAlarmManager != null) {
            mAddAlarmManager.onResume();
        }

    }

    @Override
    public void onPause() {
        Log.i(TAG, "onPause");
        super.onPause();
        sCanClickDelay = false;
        if (mAddAlarmManager != null) {
            mAddAlarmManager.hideSoftInput();
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAddAlarmManager != null) {
            mAddAlarmManager.onDestroy();
        }
        HapticsStyleController.unregisterContentObserver(mContext, this, this);
        if (getActivity() != null && getActivity().getWindow() != null) {
            getActivity().getWindow().getDecorView().setOnApplyWindowInsetsListener(null);
            mWindowInsetsListener = null;
        }
    }

    public void closeAlarmSetSetPage() {
        if ((getActivity() != null) && (!getActivity().isFinishing())) {
            if ((getParentFragment() != null) && (getParentFragment().getParentFragment() != null) && (getParentFragment().getParentFragment() instanceof COUIBottomSheetDialogFragment)) {
                ((COUIBottomSheetDialogFragment) getParentFragment().getParentFragment()).dismiss();
            }
        }
    }

    public void finishWithAction() {
        AlarmClock context = (AlarmClock) getActivity();
        if ((context != null) && ClockEventDispatcher.Companion.shouldFinishMain(context.getIntent())) {
            closeAlarmSetSetPage();
        } else if (AiTripHelper.isFromAiTrip(context)) {
            ClockOplusCSUtils.statisticsRouteAlarm(ClockOplusCSUtils.EVENT_SET_ROUTE_ALARM_CANCEL);
            closeAlarmSetSetPage();
        } else {
            closeAlarmSetSetPage();
        }

    }

    @Override
    public void onTabChanged(int toType, int fromType) {
        if (mAddAlarmManager.getCurrentSelectedItem() == toType) {
            return;
        }
        int clickType = toType;
        if (!isSupportGarbAlarm() && clickType == TAB_TYPE_INDEX_2) {
            clickType = TAB_TYPE_INDEX_3;
        }
        switch (clickType) {
            case TAB_TYPE_INDEX_0:
                mAddAlarmManager.onTabRingOnce(!mViewHolder.isInitTab);
                break;
            case TAB_TYPE_INDEX_1:
                mAddAlarmManager.getWorkdayManage().onTabWorkDay(!mViewHolder.isInitTab, this);
                break;
            case TAB_TYPE_INDEX_2:
                //秒抢闹钟
                mAddAlarmManager.getGarbAlarmManager().onTabGarbAlarm(!mViewHolder.isInitTab);
                break;
            case TAB_TYPE_INDEX_3:
                mAddAlarmManager.getCustomAlarmManager().onTabCustomize(!mViewHolder.isInitTab);
                if (DeviceUtils.isExpVersion(mContext)) {
                    Log.e(TAG, "onTabChanged ExpVersion Exception");
                }
                break;
            default:
                break;
        }
        if (mViewHolder.mScrollPanel != null) {
            //开启滑动嵌套，解决切换轮班闹钟页后关闭滑动嵌套导致页面无法滑动
            mViewHolder.mScrollPanel.mCurrentTab = clickType;
            mViewHolder.mScrollPanel.setNestedScrollingEnabled(true);
        }
        mViewHolder.isInitTab = false;
        if ((mViewHolder.mAlarmLabel != null)) {
            mViewHolder.mAlarmLabel.clearFocus();
        }
    }

    /**
     * 二级菜单回调接口
     */
    public interface SnoozePreferenceCallBack {
        void backTo();
    }

}
