/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :handle settings of ring from app Music and manager AlarmRingForOther
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Toast;

import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.ToastManager;

public class AlarmRingForOtherHandle extends BaseActivity {
    private static final String TAG = "AlarmRingForOtherHandle";

    private static final int REQUEST_CODE = 11166;

    @Override
    protected void onCreate(Bundle icicle) {
        super.onCreate(icicle);
        dealIntent();
    }

    private void dealIntent() {
        Intent intent = getIntent();
        if (intent == null) {
            finish();
            return;
        }

        try {
            String ringUri = intent.getStringExtra(AlarmRingForOther.RING_URI);
            Log.d(TAG, "onCreate: ring uri: " + ringUri);
            if (TextUtils.isEmpty(ringUri)) {
                ToastManager.showToast("RingUri is null.", Toast.LENGTH_SHORT);
                finish();
            } else {
                Intent startIntent = new Intent(this, AlarmRingForOther.class);
                startIntent.putExtra(AlarmRingForOther.RING_URI, ringUri);
                startActivityForResult(startIntent, REQUEST_CODE);
                overridePendingTransition(R.anim.coui_push_up_enter_activitydialog, R.anim.coui_zoom_fade_enter);
            }
        } catch (Exception e) {
            Log.e(TAG, "onCreate e : " + e.getMessage());
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        dealIntent();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE) {
            switch (resultCode) {
                case RESULT_OK:
                    setResult(RESULT_OK);
                    ToastManager.showToast(R.string.set_clock_ring_suc, Toast.LENGTH_SHORT);
                    finish();
                    break;
                case RESULT_CANCELED:
                    try {
                        boolean isExitByCancel = (data != null) && data.getBooleanExtra(AlarmRingForOther.EXTRA_EXIT_BY_CANCEL, false);
                        Log.d(TAG, "RESULT_CANCELED isExitByCancel:" + isExitByCancel);
                        if (isExitByCancel) {
                            finish();
                        }
                    } catch (Exception e) {
                        Log.d(TAG, "RESULT_CANCELED error " + e.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
