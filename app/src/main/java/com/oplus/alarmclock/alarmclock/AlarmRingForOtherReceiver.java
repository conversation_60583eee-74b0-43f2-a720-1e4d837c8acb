/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :AlarmRingForOtherReceiver
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.oplus.clock.common.utils.Log;

public class AlarmRingForOtherReceiver extends BroadcastReceiver {

    private static final String TAG = "AlarmRingForOtherReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            String ringUri = intent.getStringExtra(AlarmRingForOther.RING_URI);

            Log.d(TAG, "onReceive: Action: " + intent.getAction()
                    + ", ring uri: " + ringUri);

            Intent startIntent = new Intent(context, AlarmRingForOther.class);
            startIntent.putExtra(AlarmRingForOther.RING_URI, ringUri);
            startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(startIntent);
        } catch (Exception e) {
            Log.e(TAG, "onReceive e : " + e.getMessage());
        }
    }
}
