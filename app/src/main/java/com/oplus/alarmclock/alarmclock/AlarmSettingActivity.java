/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 *  setting alarm
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-5-20, yuxiaolong, create
 *
 ************************************************************/


package com.oplus.alarmclock.alarmclock;

import static com.oplus.alarmclock.alarmclock.setting.SearchIndexablesContract.EXTRA_FRAGMENT_ARG_KEY;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.utils.ActivityUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;


public class AlarmSettingActivity extends BaseActivity {

    public static final String ACTION_CHANGE_MORNING_STATUS = "action_change_morning_status";
    public static final String EXTRA_MORNING_SWITCH_STATUS = "morning_switch_status";

    public static final int TO_SETTING_CODE = 1101;
    private final String TAG = "AlarmSettingActivity";
    private final BroadcastReceiver mChangeMorningStatusReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "Receive action " + action);
            if (ACTION_CHANGE_MORNING_STATUS.equals(action)) {
                boolean open = intent.getBooleanExtra(EXTRA_MORNING_SWITCH_STATUS, false);
                if (mAlarmSettingFragment != null) {
                    mAlarmSettingFragment.changeMorningSwitchStatusByVoice(open);
                }
            }
        }
    };

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.i(TAG, "Receive action " + action);
            if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                getWindow().setFlags(ClockConstant.FLAG_UNREGISTER_SHOW_WHEN_LOCKED, WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
    };
    private AlarmSettingFragment mAlarmSettingFragment;
    private FrameLayout mListView;
    private AppBarLayout mAppBarLayout;
    private boolean mIsFromScreen;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.fragment_alarm_setting);
        COUIToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        setSupportActionBarStyle();
        mAppBarLayout = findViewById(R.id.app_bar);
        mListView = findViewById(R.id.fragment_container);
        setLayoutPadding(mListView, mAppBarLayout);
        FoldScreenUtils.updateUIOrientation(this, false);
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView);
        String arg = getIntent().getStringExtra(EXTRA_FRAGMENT_ARG_KEY);
        Bundle bundle = new Bundle();
        bundle.putString(EXTRA_FRAGMENT_ARG_KEY, arg);
        mAlarmSettingFragment = new AlarmSettingFragment();
        mAlarmSettingFragment.setArguments(bundle);
        getSupportFragmentManager().beginTransaction().replace(R.id.fragment_container, mAlarmSettingFragment).commit();
        Intent intent = getIntent();
        if (intent != null) {
            mIsFromScreen = intent.getBooleanExtra(AlarmClock.START_ACTIVITY_FROM_SCREEN, false);
            Log.d(TAG, "onCreate mIsFromScreen = " + mIsFromScreen);
            if (mIsFromScreen) {
                final IntentFilter intentFilter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
                registerReceiver(mReceiver, intentFilter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
                getWindow().setFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED,
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            }
        }
        IntentFilter filter = new IntentFilter(ACTION_CHANGE_MORNING_STATUS);
        LocalBroadcastManager.getInstance(AlarmClockApplication.getInstance()).registerReceiver(mChangeMorningStatusReceiver, filter);
        if (FlexibleWindowUtils.isSupportFlexibleActivity()
                && FlexibleWindowUtils.isFlexibleActivitySuitable(getResources().getConfiguration())) {
            FlexibleWindowUtils.setsIsFlexibleWindow(true);
        }
        ActivityUtils.INSTANCE.getSSettingActivity().clear();
        setFlexibleWindowBg();
        setFinishOnTouchOutside(false);
        addActivityWeakAndSetOnTouch();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (getSupportActionBar() != null && mListView != null) {
            Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView);
        }
        setFlexibleWindowBg();
        setSupportActionBarStyle();
        if (FlexibleWindowUtils.isSupportFlexibleActivity()
                && FlexibleWindowUtils.isFlexibleActivitySuitable(getResources().getConfiguration())) {
            FlexibleWindowUtils.setsIsFlexibleWindow(true);
        } else {
            FlexibleWindowUtils.setsIsFlexibleWindow(false);
        }
    }

    private void setSupportActionBarStyle() {
        if (getSupportActionBar() != null) {
            if (FlexibleWindowUtils.isSupportFlexibleActivity()
                    && FlexibleWindowUtils.isFlexibleActivitySuitable(getResources().getConfiguration())) {
                getSupportActionBar().setHomeAsUpIndicator(R.drawable.coui_menu_ic_cancel);
            } else {
                getSupportActionBar().setHomeAsUpIndicator(R.drawable.coui_back_arrow);
            }
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == TO_SETTING_CODE && requestCode == RESULT_CANCELED) {
            finish();
        } else {
            if (mAlarmSettingFragment != null) {
                mAlarmSettingFragment.onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    @Override
    protected void onScreenFold() {
        super.onScreenFold();
        setLayoutPadding(mListView, mAppBarLayout);
        Utils.setupAnimToolbarAndBlurView(this, mAppBarLayout, mListView);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                break;
            default:
                break;
        }

        return true;
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, int[] grantResults) {
        Log.i(TAG, "onRequestPermissionsResult");
        if (permissions != null) {
            Log.i(TAG, "onRequestPermissionsResult  permissions.length = " + permissions.length);
        }
        if (grantResults != null) {
            Log.i(TAG, "onRequestPermissionsResult  grantResults.length = " + grantResults.length);
        }
        if (mAlarmSettingFragment != null) {
            mAlarmSettingFragment.requestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mIsFromScreen) {
            unregisterReceiver(mReceiver);
        }
        LocalBroadcastManager.getInstance(AlarmClockApplication.getInstance()).unregisterReceiver(mChangeMorningStatusReceiver);
        FlexibleWindowUtils.setsIsFlexibleWindow(false);
        ActivityUtils.INSTANCE.getSSettingActivity().clear();
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {

    }

}
