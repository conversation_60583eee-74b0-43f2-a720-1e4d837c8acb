/*********************************************************************************
 ** Copyright (C), 2008-2030, Oplus, All rights reserved.
 **
 ** File: - AlertCloseModelFragment.kt
 ** Description:
 **    AlertCloseModelFragment.
 **
 ** Version: 1.0
 ** Date: 2022-06-30
 ** Author: RongWenYang.Clock
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** RongWenYang    2022-06-30   1.0    Create this module
 ********************************************************************************/

package com.oplus.alarmclock.alarmclock

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.preference.Preference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.alarmclock.view.AlarmCloseModelPreferenceCategory

class AlertCloseModelFragment : COUIPreferenceFragment(), Preference.OnPreferenceChangeListener {

    companion object {
        private const val TAG = "AlertCloseModelFragment"
        private const val KEY_CLOSE_MODEL = "pref_key_close_model"
    }

    private var mListPrePosition: AlarmCloseModelPreferenceCategory? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initPrefs()
        return view
    }

    private fun initPrefs() {
        addPreferencesFromResource(R.xml.alarm_close_model_prefs)
        mListPrePosition = findPreference(KEY_CLOSE_MODEL)
        mListPrePosition?.onPreferenceChangeListener = this
    }

    override fun onPreferenceChange(preference: Preference?, newValue: Any?): Boolean {
        val key = preference?.key
        Log.d(TAG, "onPreferenceChange clicked key: $key")
        if (KEY_CLOSE_MODEL == key) {
            val position = newValue as? Int ?: 0
            if (AlarmCloseModelUtils.sInstance.mCloseModel != position) {
                mListPrePosition?.setPositionValue(position)
                AlarmCloseModelUtils.sInstance.updateAlertCloseModel(activity, position)
            }
        }
        return true
    }
}