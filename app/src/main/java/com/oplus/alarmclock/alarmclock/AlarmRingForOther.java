/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :AlarmRingForOther:Views of the set ring from app Music
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.coui.appcompat.checkbox.COUICheckBox;
import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.RuntimePermissionAlert;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FbeRingUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.WindowUtil;
import com.oplus.alarmclock.view.DigitalClock;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;

import java.util.ArrayList;
import java.util.Calendar;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class AlarmRingForOther extends BaseActivity {

    public static final String EXTRA_EXIT_BY_CANCEL = "extra_exit_by_finish";
    public static final String RING_URI = "ring_uri";
    private static final String TAG = "AlarmRingForOther";
    private static final String ALARM_SELECTED_ARRAY = "alarm_selected_array";
    private static final int ALPHA = 255;
    private static final float ALPHA_ONE = 1.0F;
    private static final int DELAY = 100;
    private AppBarLayout mAppBarLayout;
    private COUIRecyclerView mRecylerView;
    private COUIToolbar mToolbar;
    private View mHeaderView;
    private int mTopPadding;
    private int mEndChangeOffset;
    private String mRingUri;
    private boolean[] mCheckSet;
    private boolean mIsSelect = false;
    private boolean mIsFirstLoad = true;
    private int mSelectedCount = 0;
    /**
     * 是否首次进入
     */
    private boolean mIsFirst = false;
    private ArrayList<Alarm> mAlarmCache;
    private MenuItem mDoneItem;
    private Context mContext;
    private RuntimePermissionAlert mRuntimePermissionAlert;
    private DeleteAdapter mDeleteAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.ring_set_for_others_list_view);
        mContext = this;
        mAppBarLayout = findViewById(R.id.app_bar);
        mToolbar = findViewById(R.id.toolbar);
        mToolbar.setTitleTextColor(Color.argb(ALPHA, 0, 0, 0));
        mRecylerView = findViewById(R.id.listView_toolbar_options);
        mRecylerView.setLayoutManager(new LinearLayoutManager(this));
        mHeaderView = new View(this);
        mHeaderView.setVisibility(View.INVISIBLE);
        ViewCompat.setNestedScrollingEnabled(mRecylerView, true);
        mEndChangeOffset = this.getResources().getDimensionPixelOffset(R.dimen.standard_scroll_height) / 2;
        setSupportActionBar(mToolbar);
        mIsFirst = PrivacyPolicyAlert.isFirstEntry(this);
        //外销机器第一次进入SP中写入数据
        if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance()) && mIsFirst) {
            PrefUtils.putBoolean(AlarmClockApplication.getInstance(),
                    PrivacyPolicyAlert.SP_NAME, PrivacyPolicyAlert.SP_KEY_PRIVACY_POLICY_ALERT, false);
        }
        mIsFirstLoad = true;
        mRuntimePermissionAlert = new RuntimePermissionAlert(this,
                new RuntimePermissionAlert.RuntimePermissionCallBack() {

                    @Override
                    public void doAfterGranted(boolean isNeedOpenAddView) {
                        if ((mCheckSet != null) && (mCheckSet.length > 0)) {
                            boolean isHaveAlarmSelected = false;
                            for (boolean b : mCheckSet) {
                                if (b) {
                                    isHaveAlarmSelected = b;
                                    break;
                                }
                            }
                            Log.d(TAG, "doAfterGranted isHaveAlarmSelected = " + isHaveAlarmSelected);
                            if (isHaveAlarmSelected) {
                                saveRingSetResults();
                                FbeRingUtils.copyRingFileToInternalStorage(mContext, Uri.parse(mRingUri));
                                FbeRingUtils.deleteUnusedRingtoneFile(mContext);
                                finish();
                            }
                        }
                    }

                    @Override
                    public void doAfterDenieD() {
                        Log.d(TAG, "doAfterDenieD");
                    }

                    @Override
                    public void onExitClick() {
                        setNormalCancelExit();
                    }

                    @Override
                    public void onClickOutside() {
                        Log.d(TAG, "onClickOutside");
                    }
                });
        //2788955 外销版用户须知去掉
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            PrivacyPolicyAlert mPrivacyPolicyAlert = new PrivacyPolicyAlert(this, new PrivacyPolicyAlert.PrivacyPolicyCallback() {
                @Override
                public void doAfterPermitted() {
                }

                @Override
                public void onExitClick() {
                    setNormalCancelExit();
                }
            });
            mPrivacyPolicyAlert.checkPermitPrivacyPolicy(false);
        }
        if (mIsFirst) {
            AlarmUtils.asyncSetFirstEntryAlarm(this);
        }
        Intent intent = getIntent();
        mRingUri = intent.getStringExtra(RING_URI);
        mAppBarLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mAppBarLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                mTopPadding = mAppBarLayout.getMeasuredHeight() + getResources().getDimensionPixelSize(R.dimen.category_top_padding);
                mHeaderView.setLayoutParams(new RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, mTopPadding));
                updateListLayout(mRingUri == null);
                setSelectedCount(0);
            }
        });
        View place = getStatusBarView();
        if (place != null) {
            mAppBarLayout.addView(place, 0, place.getLayoutParams());
        }
    }

    private void setSelectedCount(int count) {
        String title = (count == 0) ? getString(R.string.select_zero_text)
                : getString(R.string.select_count_text, count);

        if (mToolbar != null) {
            mToolbar.setTitle(title);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.action_menu_ring_for_other, menu);
        mDoneItem = menu.findItem(R.id.done);
        MenuItem cancelItem = menu.findItem(R.id.cancel);
        if ((mDeleteAdapter != null) && (mDeleteAdapter.getItemCount() != 0)) {
            if (mSelectedCount == 0) {
                mDoneItem.setEnabled(false);
            } else {
                mDoneItem.setEnabled(true);
            }
        } else if ((mDeleteAdapter == null) || (mDeleteAdapter.getItemCount() == 0)) {
            mDoneItem.setVisible(false);
            cancelItem.setVisible(false);
        }
        supportInvalidateOptionsMenu();
        return true;
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
        if (!mIsFirstLoad) {
            updateListLayout(mRingUri == null);
        } else {
            mIsFirstLoad = false;
        }
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            if (PrivacyPolicyAlert.isFirstEntry(this)) {
                statementDismiss(View.INVISIBLE);
            }
        }
        //2788955 外销机用户须知去掉，存储权限时机后移。
    }

    public void statementDismiss(int visibility) {
        if (mRecylerView != null) {
            mRecylerView.setVisibility(visibility);
        }
        if (mAppBarLayout != null) {
            mAppBarLayout.setVisibility(visibility);
        }
    }


    private void updateListLayout(boolean other) {
        mAlarmCache = AlarmUtils.getAllAlarms(mContext);
        if ((mCheckSet == null) || (mAlarmCache.size() != mCheckSet.length)) {
            mCheckSet = new boolean[mAlarmCache.size()];
            for (int i = 0; i < mCheckSet.length; i++) {
                mCheckSet[i] = false;
            }
            if (mDoneItem != null) {
                mDoneItem.setEnabled(false);
                supportInvalidateOptionsMenu();
            }
        }
        Log.i(TAG, "other = " + other);
        if (other) {
            mRecylerView.setAdapter(null);
        } else {
            if ((mDeleteAdapter == null) || (mRecylerView.getAdapter() == null)) {
                mDeleteAdapter = new DeleteAdapter();
                mRecylerView.setAdapter(mDeleteAdapter);
                mDeleteAdapter.setOnItemClickListener(new OnItemClickListener() {
                    @Override
                    public void onItemClick(int position, boolean isChecked, boolean isNeedNotify) {
                        changeCheckBoxState(position, isChecked, isNeedNotify);
                    }
                });
            }
            mDeleteAdapter.notifyDataSetChanged();
        }
        mIsSelect = true;
        ViewCompat.setNestedScrollingEnabled(mRecylerView, mAlarmCache.size() > 0);
    }


    private View getStatusBarView() {
        //add for immsersive theme. statusbar placeholder view
        int statusHeight = WindowUtil.INSTANCE.getStatusBarHeight(getBaseContext());
        ImageView imageView = new ImageView(getBaseContext());
        imageView.setImageResource(R.drawable.color_statusbar_bg);
        imageView.setScaleType(ImageView.ScaleType.FIT_XY);
        imageView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, statusHeight));

        return imageView;
    }

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        if (mCheckSet != null) {
            outState.putBooleanArray(ALARM_SELECTED_ARRAY, mCheckSet);
        }
    }

    @Override
    protected void onRestoreInstanceState(@NonNull final Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        mCheckSet = savedInstanceState.getBooleanArray(ALARM_SELECTED_ARRAY);
        mRecylerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                mToolbar.setTitleTextColor(Color.argb(ALPHA, 0, 0, 0));
                updateActionBarMenu();
            }
        }, DELAY);
    }

    @Override
    @SuppressLint("InlinedApi")
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.coui_zoom_fade_enter,
                R.anim.coui_push_down_exit);
    }

    @Override
    public void onBackPressed() {
        setNormalCancelExit();
        super.onBackPressed();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.done) {
            getCheckRuntimePermissions();
        } else if (itemId == R.id.cancel) {
            setNormalCancelExit();
            finish();
        }
        return super.onOptionsItemSelected(item);
    }

    private void setNormalCancelExit() {
        Intent intent = new Intent();
        intent.putExtra(EXTRA_EXIT_BY_CANCEL, true);
        setResult(RESULT_CANCELED, intent);
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions,
                                           int[] grantResults) {
        if (mRuntimePermissionAlert != null) {
            mRuntimePermissionAlert.resetCheckingPermissions();
        }
        Log.i(TAG, "onRequestPermissionsResult");
        if (permissions != null) {
            Log.i(TAG, "onRequestPermissionsResult  permissions.length = " + permissions.length);
        }
        if (grantResults != null) {
            Log.i(TAG, "onRequestPermissionsResult  grantResults.length = " + grantResults.length);
        }

        if ((mRuntimePermissionAlert != null) && (permissions != null) && (permissions.length > 0)
                && (grantResults != null) && (grantResults.length > 0)) {
            mRuntimePermissionAlert.requestPermissionsResult(requestCode, permissions, grantResults, false);
        } else {
            getCheckRuntimePermissions();
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == RuntimePermissionAlert.GUIDE_PERMISSIONS_CODE) {
            getCheckRuntimePermissions();
        }
        super.onActivityResult(requestCode, resultCode, data);
    }


    public void getCheckRuntimePermissions() {
        if (mRuntimePermissionAlert != null) {
            mRuntimePermissionAlert.getCheckRuntimePermissions(false);
        }
    }

    private void saveRingSetResults() {
        for (int i = 0; i < mCheckSet.length; i++) {
            if (i > mAlarmCache.size()) {
                break;
            }
            if (mCheckSet[i]) {
                Log.i(TAG, "onShortCutButtonClick ringUri =" + mRingUri);

                boolean isUriValid = false;
                if (!TextUtils.isEmpty(mRingUri)) {
                    isUriValid = AlarmRingUtils.isMediaUriValid(this, Uri.parse(mRingUri));
                }
                String ringTitle = "";
                if (!isUriValid) {
                    Uri result = AlarmRingUtils.getDefaultRingtoneUri(mContext, true);
                    if (result != null) {
                        mRingUri = result.toString();
                    } else {
                        mRingUri = "";
                    }
                }

                if (!TextUtils.isEmpty(mRingUri)) {
                    ringTitle = getAudioTitle(Uri.parse(mRingUri));
                    Alarm alarm = mAlarmCache.get(i);
                    alarm.setAlert(Uri.parse(mRingUri));
                    alarm.setRingName(ringTitle);
                    AlarmUtils.updateAlarmRing(AlarmRingForOther.this, alarm.getId(), mRingUri, ringTitle);
                }
            }

            setResult(RESULT_OK);
        }
    }

    public String getAudioTitle(Uri uri) {
        String defaultTitle = getResources().getString(R.string.default_alarm_summary);
        String silentTitle = getResources().getString(R.string.alert_no_ring);

        if (uri == null) {
            return silentTitle;
        }
        if (uri.toString().startsWith("content://media/external/")) {
            Cursor cursor = null;
            String title = "";
            try {
                cursor = getContentResolver().query(uri, new String[]{
                                AlarmRingUtils.getRingtoneTitleColumnsName(), MediaStore.Audio.AudioColumns.DATA},
                        null, null, null);
                if ((null == cursor) || (cursor.getCount() == 0)) {
                    Log.w(TAG, "cursor == null");
                    if (null != cursor) {
                        cursor.close();
                        cursor = null;
                    }
                    return defaultTitle;
                }

                cursor.moveToFirst();
                title = cursor.getString(0);
            } catch (Exception e) {
                Log.e(TAG, "getAudioTitle() e: " + e);
            } finally {
                if (null != cursor) {
                    cursor.close();
                }
            }
            Log.v(TAG, "getAudioTitle() title: " + title);
            return title;
        }
        return defaultTitle;
    }

    private void updateActionBarMenu() {
        mSelectedCount = 0;
        for (boolean b : mCheckSet) {
            if (b) {
                mSelectedCount++;
            }
        }
        setSelectedCount(mSelectedCount);
        if (mDoneItem != null) {
            if (mSelectedCount == 0) {
                mDoneItem.setEnabled(false);
            } else {
                mDoneItem.setEnabled(true);
            }

            supportInvalidateOptionsMenu();
        }
    }


    private void changeCheckBoxState(int position, boolean isChecked, boolean isNeedNotify) {
        mCheckSet[position] = isChecked;
        if (isNeedNotify) {
            if (mDeleteAdapter != null) {
                mDeleteAdapter.notifyItemChanged(position + 1);
            }
        }
        updateActionBarMenu();
    }


    private class HeaderViewHolder extends COUIRecyclerView.ViewHolder {
        HeaderViewHolder(View itemView) {
            super(itemView);
        }
    }

    private static class MViewHolder extends COUIRecyclerView.ViewHolder {
        COUICheckBox mOnButton;
        DigitalClock mDigitalClock;
        TextView mLabelView;

        MViewHolder(@NonNull View itemView) {
            super(itemView);
            mOnButton = itemView.findViewById(R.id.oplus_listview_scrollchoice_checkbox);
            mDigitalClock = itemView.findViewById(R.id.alarm_delete_digitalClock);
            mLabelView = itemView.findViewById(R.id.label);

            mDigitalClock.setTimeViewSuitableFontSize(COUIChangeTextUtil.G3);
            mDigitalClock.setAmPmSuitableFontSize(COUIChangeTextUtil.G3);
        }
    }

    private class DeleteAdapter extends COUIRecyclerView.Adapter<RecyclerView.ViewHolder> {
        private static final int TYPE_HEADER_VIEW = -1;
        private static final int TYPE_NORMAL_VIEW = 0;
        private OnItemClickListener mOnItemClickListener;

        void setOnItemClickListener(OnItemClickListener onItemClickListener) {
            this.mOnItemClickListener = onItemClickListener;
        }


        @Override
        public int getItemViewType(int position) {
            if (position == 0) {
                return TYPE_HEADER_VIEW;
            }
            return TYPE_NORMAL_VIEW;
        }

        @NonNull
        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            if ((viewType == TYPE_HEADER_VIEW) && (mHeaderView != null)) {
                return new HeaderViewHolder(mHeaderView);
            }
            return new MViewHolder(LayoutInflater.from(mContext).inflate(R.layout.ring_set_for_others_list_item_view, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder holder, int position) {


            if (getItemViewType(holder.getAdapterPosition()) != TYPE_HEADER_VIEW) {
                final MViewHolder viewHolder = (MViewHolder) holder;
                final Alarm alarm = mAlarmCache.get(holder.getAdapterPosition() - 1);

                holder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (mOnItemClickListener != null) {
                            mOnItemClickListener.onItemClick(viewHolder.getAdapterPosition() - 1, !viewHolder.mOnButton.isChecked(), true);
                        }
                    }
                });

                if (mIsSelect) {
                    viewHolder.mOnButton.setChecked(mCheckSet[holder.getAdapterPosition() - 1]);
                }

                viewHolder.mOnButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        mCheckSet[holder.getAdapterPosition() - 1] = viewHolder.mOnButton.isChecked();
                        if (mOnItemClickListener != null) {
                            mOnItemClickListener.onItemClick(viewHolder.getAdapterPosition() - 1, viewHolder.mOnButton.isChecked(), false);
                        }
                    }
                });

                // set the alarm text
                final Calendar c = Calendar.getInstance();
                c.set(Calendar.HOUR_OF_DAY, alarm.getHour());
                c.set(Calendar.MINUTE, alarm.getMinutes());
                viewHolder.mDigitalClock.updateTime(c);
                viewHolder.mDigitalClock.setAmPmViewColor(ContextCompat.getColor(mContext, R.color.list_item_title_text_color));
                viewHolder.mDigitalClock.setTextColor(mContext.getColor(R.color.list_item_title_text_color), ALPHA_ONE);

                // Display the label
                String label = alarm.getLabel();
                if (alarm.getmDefaultAlarm() == 1) {
                    label = mContext.getResources().getString(R.string.wake_up_alarm);
                }
                StringBuilder sb = new StringBuilder();

                if (!TextUtils.isEmpty(label)) {
                    sb.append(label);
                } else {
                    sb.append(getResources().getString(R.string.default_label));
                }
                // Set the repeat text or leave it blank if it does not repeat.
                int repeatSet = alarm.getRepeatSet();
                int wSwitch = alarm.getWorkdaySwitch();
                int hSwitch = alarm.getHolidaySwitch();
                String daysOfWeekStr = RepeatSet.getDescription(AlarmRingForOther.this, repeatSet, wSwitch, hSwitch, false, alarm, true);
                if (!TextUtils.isEmpty(daysOfWeekStr)) {
                    sb.append(" | ").append(daysOfWeekStr);
                }
                viewHolder.mLabelView.setText(sb.toString());
            }
        }

        @Override
        public long getItemId(int arg0) {
            if ((mAlarmCache != null) && (arg0 - 1 >= 0) && (arg0 - 1 < mAlarmCache.size())) {
                return mAlarmCache.get(arg0 - 1).getId();
            }
            return 0;
        }

        @Override
        public int getItemCount() {
            if (mAlarmCache != null) {
                return mAlarmCache.size() + 1;
            }
            return 0;
        }

    }

    public interface OnItemClickListener {
        void onItemClick(int position, boolean isChecked, boolean isNeedNotify);
    }
}
