/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :WeekDayStartFromHelper.java  query witch day is the start of a week
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2017-12-28, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;


import com.oplus.clock.common.utils.Log;

import java.util.Calendar;
import java.util.Locale;

public class WeekDayStartFromHelper {
    public static final String BENGALI = "bn_BD";//for bengali
    public static final String PORTU_PORTU = "pt_PT";//for Portuguese(Portugal)
    public static final String KISWAHILI = "sw_KE";//for Kenya

    private static final String TAG = "WeekStartFromHelper";


    public static int getLocationWeekStartDay() {

        int startDay = Calendar.getInstance().getFirstDayOfWeek() - 1;
        String language = Locale.getDefault().toString();
        if (BENGALI.equals(language)) {
            startDay = RepeatSet.FIRST_DAY_IS_FRIDAY;
        } else if (PORTU_PORTU.equals(language) || KISWAHILI.equals(language)) {
            startDay = RepeatSet.FIRST_DAY_IS_MONDAY;
        }
        Log.i(TAG, "getLctWeekStartDay startDay = " + startDay + ", language = " + language);

        return startDay;
    }

}
