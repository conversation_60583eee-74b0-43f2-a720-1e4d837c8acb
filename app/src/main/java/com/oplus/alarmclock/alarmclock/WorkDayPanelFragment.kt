/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorkDayPanelFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/08
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2023/11/08     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AddAlarmManager.Companion.KEY_SHOW_LOOP_PREFERENCE_PANEL
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME
import com.oplus.alarmclock.utils.ClockConstant.EVENT_ADD_ALARM_FINISH_PAGE
import com.oplus.alarmclock.utils.ClockConstant.EVENT_SET_WORK_DAY_TYPE
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log

/**
 * 工作日类型Panel
 * @Date 2023年11月8日10:42:20
 */
class WorkDayPanelFragment() : COUIPanelFragment() {

    companion object {
        private const val TAG = "WorkDayPanelFragment"
        fun newInstance(
            alarm: Alarm,
            reloadAlarm: Alarm?,
            isShowLoopPreference: Boolean,
            call: WorkDayPanelBack
        ): WorkDayPanelFragment {
            val bundle = Bundle().apply {
                putParcelable(LOOP_ALARM_PAGE_DATA_NAME, alarm)
                putParcelable(LOOP_ALARM_RELOAD_DATA_NAME, reloadAlarm)
                putBoolean(KEY_SHOW_LOOP_PREFERENCE_PANEL, isShowLoopPreference)
            }
            return WorkDayPanelFragment().apply {
                arguments = bundle
                mCall = call
            }
        }
    }

    private var mWorkdayType = -1
    private var mIsShowLoopPreference: Boolean = false
    private var mWorkPreferenceFragment: WorkPreferenceFragment? = null
    private var mAlarm: Alarm? = null
    private var mReloadAlarm: Alarm? = null
    private var mCall: WorkDayPanelBack? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        arguments?.let {
            mAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME)
            }
            mReloadAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                it.getParcelable(LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME, Alarm::class.java)
            } else {
                it.getParcelable<Alarm>(LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME)
            }
            mIsShowLoopPreference = it.getBoolean(KEY_SHOW_LOOP_PREFERENCE_PANEL)
            mAlarm?.let { alarm ->
                mWorkdayType = alarm.getmWorkDayType()
            }
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        initToolbar()
        initPreference()
        initListener()
    }

    /**
     * 初始化状态栏
     */
    private fun initToolbar() {
        hideDragView()
        toolbar?.apply {
            visibility = View.VISIBLE
            title = context.getString(R.string.weekdays_type)
            isTitleCenterStyle = false
            setTitleTextAppearance(
                    requireContext(),
                    R.style.TextAppearance_COUI_AppCompatSupport_Toolbar_Title_Panel_Second
            )
            navigationIcon = ContextCompat.getDrawable(
                    context,
                    R.drawable.coui_back_arrow)
            setNavigationOnClickListener {
                finishPage(mWorkdayType)
            }
        }
    }

    private fun initPreference() {
        mAlarm?.let { alarm ->
            mWorkPreferenceFragment = WorkPreferenceFragment.newInstance(alarm, mReloadAlarm, mIsShowLoopPreference)
            mWorkPreferenceFragment?.let {
                childFragmentManager.beginTransaction().replace(contentResId, it).commit()
            }
            mIsShowLoopPreference = false
        }
    }

    /**
     * 是否已展示轮班闹钟详情界面
     */
    fun isShowLoopPreferencePanel(): Boolean {
        mWorkPreferenceFragment?.let {
            return it.isShowLoopPreferencePanel
        }
        return false
    }

    fun saveTempAlarm(): Alarm? {
        mWorkPreferenceFragment?.let {
            return it.saveTempAlarm()
        }
        return null
    }

    /**
     * 设置事件
     */
    private fun initListener() {
        //手势返回
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                finishPage(mWorkdayType)
            }
            false
        }

        setPanelDragListener {
            mWorkPreferenceFragment?.canClosePanel() == true
        }
        //工作日类型选择非轮班闹钟
        LiteEventBus.instance.with(EVENT_ADD_ALARM_FINISH_PAGE, hashCode().toString())
                .observe(this) {
                    if (it is Int) {
                        mWorkdayType = it
                        finishPage(mWorkdayType)
                    }
                }
        //工作日类型选择轮班闹钟
        LiteEventBus.instance.with(EVENT_SET_WORK_DAY_TYPE, hashCode().toString())
                .observe(this) {
                    if (it is Int) {
                        mWorkdayType = it
                    }
                }
    }

    /**
     * 销毁页面
     */
    fun finishPage(workType: Int) {
        Log.e(TAG, "finishPage work day panel")
        (parentFragment as? COUIBottomSheetDialogFragment)?.backToFirstPanel()
        mCall?.backTo(workType)
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
                ?.setPanelBackgroundTintColor(ContextCompat.getColor(
                        requireContext(),
                        R.color.coui_color_background_elevatedWithCard
                ))
    }

    /**
     * 销毁
     */
    override fun onDestroyView() {
        super.onDestroyView()
        LiteEventBus.instance.releaseObserver(hashCode().toString())
    }

    /**
     * 二级菜单回调接口
     */
    interface WorkDayPanelBack {
        fun backTo(workType: Int)
    }
}