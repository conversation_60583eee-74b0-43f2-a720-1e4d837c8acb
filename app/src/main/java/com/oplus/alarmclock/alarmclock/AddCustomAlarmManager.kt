/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AddCustomAlarmManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin    203/5/10     1.0            build this module
 ****************************************************************/
@file:Suppress(
    "LongParameterList",
    "Maximum<PERSON>ineLength",
    "<PERSON><PERSON><PERSON>ber",
    "ComplexCondition",
    "LargeClass",
    "ParameterListWrapping",
    "CollapsibleIfStatements"
)

package com.oplus.alarmclock.alarmclock

import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.view.View
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AddAlarmFragment.TAB_TYPE_INDEX_3
import com.oplus.alarmclock.alarmclock.datepick.AlarmDatePicker
import com.oplus.alarmclock.alarmclock.utils.DatePickerUtils
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.utils.ChannelManager
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.IBaseChannel
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import java.util.Calendar

class AddCustomAlarmManager(private val alarmManager: AddAlarmManager, val mContext: Context) {

    /**
     * 周选择数组
     */
    private val mWeekCheckArray = BooleanArray(DatePickerUtils.DAYS_IN_A_WEEK)
    private val mWeekAddNum = intArrayOf(1, 2, 4, 8, 16, 32, 64, 128)
    private var mAlarmSpecialStr = DatePickerUtils.SPLIT

    /**
     * 自定义闹钟类型
     */
    private val mWeekPickAddNum = intArrayOf(
        RepeatSet.SUNDAY,
        RepeatSet.MONDAY,
        RepeatSet.TUESDAY,
        RepeatSet.WEDNESDAY,
        RepeatSet.THURSDAY,
        RepeatSet.FRIDAY,
        RepeatSet.SATURDAY
    )

    /**
     * 节假日开关
     *
     * @param checked
     */
    fun selectHoliday(checked: Boolean) {
        alarmManager.apply {
            viewHolder.apply {
                mAlarmHolidaySwitch =
                    if (checked) ClockConstant.ALARM_HOLIDAY_SWITCH_ON else ClockConstant.ALARM_HOLIDAY_SWITCH_OFF
                if (mAlarmDatePicker != null) {
                    mAlarmDatePicker.setHolidaySwitch(mAlarmHolidaySwitch)
                    mAlarmSpecialStr = mAlarmDatePicker.curSpecialDays
                    mAlarm.setmSpecialAlarmDays(mAlarmDatePicker.curSpecialDays)
                }
                mAlarm.holidaySwitch = mAlarmHolidaySwitch
                updateDescription(mAlarm.repeatSet, false)
                updateLeftTimeInfo()
            }
        }
    }

    /**
     * 初始化日历选择
     */
    fun initDatePicker() {
        alarmManager.apply {
            viewHolder.apply {
                if (mRootView != null && mFragment != null && mFragment.activity != null) {
                    //自定义日期选择器
                    mAlarmDatePicker = AlarmDatePicker(
                        mRootView,
                        mFragment.activity,
                        mAlarm,
                        mWeekCheckArray
                    ) { _: Boolean, _: Int, specDays: String? ->
                        //日期点击事件
                        mAlarm.setmSpecialAlarmDays(specDays)
                        mAlarmSpecialStr = specDays.toString()
                        updateLeftTimeInfo()
                        updateDescription(mAlarm.repeatSet, false)
                        updateHolidaySwitch()
                    }
                }
            }
        }
    }

    /**
     * 更新节假日Switch开关
     */
    fun updateHolidaySwitch() {
        alarmManager.viewHolder.run {
            if (mAlarmRepeatSet == 0 && mAlarm.getmSpecialAlarmDays() == DatePickerUtils.SPLIT) {
                mHolidaySwitch.isChecked = false
                mHolidaySwitch.isEnabled = false
                mAlarmHolidayLayout.isClickable = false
            } else {
                mHolidaySwitch.isChecked =
                    mAlarmHolidaySwitch == ClockConstant.ALARM_HOLIDAY_SWITCH_ON
                mHolidaySwitch.isEnabled = true
                mAlarmHolidayLayout.isClickable = true
            }
        }
    }

    /**
     * 周选择器点击事件
     *
     * @param day
     */
    fun onWeekPickClick(day: Int, isCheck: Boolean) {
        alarmManager.apply {
            viewHolder.run {
                if (mWeekCheckArray.size > day && day >= 0) {
                    mWeekCheckArray[day] = isCheck
                }
                val daysSet: Int = getDaysSet()
                mAlarmRepeatSet = daysSet
                mAlarm.setRepeat(daysSet)
                if (mAlarmDatePicker != null) {
                    mAlarmDatePicker.setCurrentItemViewWeekCheck(isCheck, day, daysSet)
                }
                updateHolidaySwitch()
                updateDescription(daysSet, false)
                updateLeftTimeInfo()
            }
        }
    }

    /**
     * 获取重复日期
     *
     * @return
     */
    fun getDaysSet(): Int {
        var set = 0
        for (j in mWeekCheckArray.indices) {
            if (mWeekCheckArray[j]) {
                set += mWeekPickAddNum[j]
            }
        }
        return set
    }

    /**
     * 更新节假日
     *
     * @param total
     */
    private fun updateStates(total: Int) {
        for (j in mWeekCheckArray.indices) {
            mWeekCheckArray[j] = total and mWeekPickAddNum[j] == mWeekPickAddNum[j]
        }
    }

    /**
     * 切换至自定义闹钟
     */
    fun onTabCustomize(isClick: Boolean) {
        alarmManager.apply {
            if (DeviceUtils.isExpVersion(mContext) && ChannelManager.getChannelUtils()
                    .getChannel() == IBaseChannel.CHANNEL_WPLUS
            ) {
                if (isClick) {
                    viewHolder.mAlarm.setmSpecialAlarmDays(mAlarmSpecialStr)
                } else {
                    mAlarmSpecialStr = viewHolder.mAlarm.getmSpecialAlarmDays()
                }
                if (viewHolder.mAlarmDataPicker == null) {
                    viewHolder.apply {
                        val boolean = initDataPicker(mAlarmDataPickerViewStub.inflate())
                        if (boolean) {
                            mAlarmDataPicker.setOnClickListener(mFragment)
                            mExpandLayout.setOnClickListener(mFragment)
                            mExpandButton.setOnClickListener(mFragment)
                            mAnimatorUtil.configAnimator(mPrevButton, mNextButton)
                        }
                    }
                }
                initDatePicker()
            }
            viewHolder.apply {
                mGarbAlarmDateLayout?.visibility = View.GONE
                mGarbAlarmRingLayout?.visibility = View.GONE
                mGarbAlarmDescription?.visibility = View.GONE
                mSnoozeLayout.visibility = View.VISIBLE
                setItemCardBackground()
                COUICardListHelper.setItemCardBackground(
                    mAlarmLabelLayout,
                    COUICardListHelper.getPositionInGroup(3, 0)
                )
                dimissLoopAlarmTips()
                LiteEventBus.instance.send(
                    LoopAlarmEvent.EVENT_ADD_LOOP_ALARM_WORK_CHANGE_TITLE,
                    false
                )
                mLoopAlarmListLayout.visibility = View.GONE
                mTimePickerLayout.visibility = View.VISIBLE
                setCommonLayoutMargin(
                    AlarmClockApplication.getInstance().resources.getDimensionPixelSize(
                        R.dimen.layout_dp_12
                    )
                )
                mCustomLayout.alpha = 0f
                mCustomLayout.visibility = View.VISIBLE
                if (mShowDatePicker) {
                    mAlarmDataPicker?.visibility = View.VISIBLE
                }
                if (isClick) {
                    //执行动画
                    mAnimatorUtil.changeToCustom(mCustomLayout, mWorkDayLayout, mShowDatePicker)
                } else {
                    mCustomLayout.alpha = 1f
                    mWorkDayLayout.visibility = View.GONE
                }
                updatePrefLayoutParams()
                mAlarm.apply {
                    workdaySwitch = 0
                    setmGarbSwitch(0)
                    //读取上次保存记录
                    setRepeat(mAlarmRepeatSet)
                    holidaySwitch = mAlarmHolidaySwitch
                    setmLoopSwitch(0)
                    hour = mOplusTimePicker.currentHour
                    minutes = mOplusTimePicker.currentMinute
                }
                updateStates(mAlarmRepeatSet)
                mWeekPick.update(mWeekCheckArray)
                updateDescription(mAlarmRepeatSet, false)
                //更新节假日开关
                updateHolidaySwitch()
                //隐藏软键盘
                hideSoftInput()
                //更新顶部响铃信息
                updateLeftTimeInfo()
            }
        }
    }

    /**
     * 展开年月选择器
     */
    fun setCalendarCurrentView() {
        alarmManager.viewHolder.apply {
            var viewIndex = AddAlarmViewHolder.VIEW_YEAR
            if (mCurrentView == AddAlarmViewHolder.VIEW_MONTH_DAY) {
                viewIndex = AddAlarmViewHolder.VIEW_YEAR
            } else if (mCurrentView == AddAlarmViewHolder.VIEW_YEAR) {
                viewIndex = AddAlarmViewHolder.VIEW_MONTH_DAY
            }
            when (viewIndex) {
                AddAlarmViewHolder.VIEW_MONTH_DAY -> {
                    if (mCurrentView != viewIndex) {
                        changToMonthDay(viewIndex)
                    }
                }

                AddAlarmViewHolder.VIEW_YEAR -> {
                    if (mCurrentView != viewIndex) {
                        changeToYear(viewIndex)
                    }
                }
            }
        }
    }

    /**
     * 自定义时间选择
     */
    fun showDataPicker() {
        alarmManager.viewHolder.mAlarmDataPicker?.let {
            alarmManager.viewHolder.apply {
                if (mShowDatePicker) {
                    mAlarmRepeatMore.isExpanded = false
                    mAnimatorUtil.hideDatePick(mAlarmDataPicker, mCustomLayout)
                } else {
                    mAlarmRepeatMore.isExpanded = true
                    mAlarmDataPicker.visibility = View.VISIBLE
                    mAnimatorUtil.showDatePick(mAlarmDataPicker, mCustomLayout)
                }
                mShowDatePicker = !mShowDatePicker
            }
        }
    }

    /**
     * 切换至年月选择控件
     *
     * @param viewIndex
     */
    private fun changeToYear(viewIndex: Int) {
        alarmManager.viewHolder.apply {
            mFragment.activity?.let {
                mHeaderMonthView.setTextColor(
                    COUIContextUtil.getAttrColor(
                        it,
                        R.attr.couiColorPrimary
                    )
                )
                mExpandButton.imageTintList = ColorStateList.valueOf(
                    COUIContextUtil.getAttrColor(
                        it,
                        R.attr.couiColorPrimary
                    )
                )
                mAnimator.postDelayed(Runnable {
                    mAnimator.displayedChild = AddAlarmViewHolder.VIEW_YEAR
                }, AddAlarmManager.WAITE_ANIMATION_MILLISECOND)
                mAnimatorUtil.changeToYear()
                mExpandButton.isExpanded = true
                mCurrentView = viewIndex
                mYearAndMonthView.setDate(mAlarmDatePicker.year, mAlarmDatePicker.month + 1)
            }
        }
    }

    /**
     * 切换至日期选择控件
     *
     * @param viewIndex
     */
    fun changToMonthDay(viewIndex: Int) {
        alarmManager.viewHolder.apply {
            mFragment.activity?.let {
                val secColor = COUIContextUtil.getAttrColor(it, R.attr.couiColorSecondNeutral)
                mHeaderMonthView?.setTextColor(
                    COUIContextUtil.getAttrColor(
                        it,
                        R.attr.couiColorPrimaryNeutral
                    )
                )
                mExpandButton?.imageTintList = ColorStateList.valueOf(secColor)
                mAnimator?.displayedChild = AddAlarmViewHolder.VIEW_MONTH_DAY
                mExpandButton?.isExpanded = false
                mCurrentView = viewIndex
                mPrevButton?.let { mAnimatorUtil.changeToMonthDay(it) }
                mAlarmDatePicker?.setCurrentItem(mYearAndMonthView.year, mYearAndMonthView.month)
            }
        }
    }

    /**
     * 设置节假日内容
     */
    fun setAlarmHoliday() {
        alarmManager.apply {
            viewHolder.run {
                updateStates(mAlarmRepeatSet)
                mWeekPick.update(mWeekCheckArray)
                updateLeftTimeInfo()
                updateDescription(mAlarmRepeatSet, false)
                updateHolidaySwitch()
            }
        }
    }

    /**
     * 更新日期选择
     */
    fun updateDatePicker() {
        alarmManager.viewHolder.apply {
            if (DeviceUtils.isExpVersion(mContext) && ChannelManager.getChannelUtils()
                    .getChannel() == IBaseChannel.CHANNEL_WPLUS
            ) {
                mAlarmDatePicker = null
                initDatePicker()
                mYearAndMonthView.setMinDate(Calendar.getInstance().timeInMillis)
            }
        }
    }

    /**
     * 点击节假日开关
     */
    fun clickHolidaySwitch() {
        alarmManager.viewHolder.apply {
            mHolidaySwitch.isChecked = !mHolidaySwitch.isChecked
        }
    }

    /**
     * 通过intent 获取周数据
     *
     * @param intent
     * @return
     */
    fun getDaysFromIntent(intent: Intent): Int {
        alarmManager.viewHolder.run {
            var repeatSet = 0
            val days = intent.getIntegerArrayListExtra(android.provider.AlarmClock.EXTRA_DAYS)
            if (days != null) {
                val daysArray = IntArray(days.size)
                var sum = 0
                for (i in days.indices) {
                    daysArray[i] = days[i]
                    Log.d(AddAlarmManager.TAG, "" + i + ": " + daysArray[i])
                    sum += if (daysArray[i] - 2 >= 0) {
                        mWeekAddNum[daysArray[i] - 2]
                    } else {
                        mWeekAddNum[daysArray[i] + 5]
                    }
                }
                repeatSet = sum
            } else {
                // API says to use an ArrayList<Integer> but we allow the user to use a int[] too.
                val daysArray = intent.getIntArrayExtra(android.provider.AlarmClock.EXTRA_DAYS)
                if (daysArray != null && daysArray.size > 0) {
                    repeatSet = daysArray[0]
                }
            }
            return repeatSet
        }
    }


    /**
     * 是否为过期时间
     */
    fun isExpireTime(): Boolean {
        alarmManager.viewHolder.run {
            val isSpec = mAlarm.getmSpecialAlarmDays().split(DatePickerUtils.SPLIT.toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray().size == 2
            if (!DatePickerUtils.isEmptySpecialDay(mAlarm.getmSpecialAlarmDays()) && isSpec) {
                val specialDay: String =
                    mAlarm.getmSpecialAlarmDays().split(DatePickerUtils.SPLIT.toRegex())
                        .dropLastWhile { it.isEmpty() }.toTypedArray().get(1)
                val days = DatePickerUtils.todayAfter1970days(Calendar.getInstance())
                //选中日期为今天
                if (specialDay.toLong() == days) {
                    val clockCalendar = Calendar.getInstance()
                    if (mOplusTimePicker != null) {
                        clockCalendar[Calendar.HOUR_OF_DAY] = mOplusTimePicker.currentHour
                        clockCalendar[Calendar.MINUTE] = mOplusTimePicker.currentMinute
                    }
                    return clockCalendar.time.time <= System.currentTimeMillis()
                }
            }
            return false
        }
    }

    /**
     * 是否选择了自定义tab
     *
     * @return
     */
    fun isTabCustomSelected(): Boolean {
        alarmManager.viewHolder.apply {
            return if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                mAlarmTypeTabAdapter.getCurrentSelectedItem() == AddAlarmFragment.TAB_TYPE_INDEX_1
            } else {
                mAlarmTypeTabAdapter.getCurrentSelectedItem() == AddAlarmFragment.TAB_TYPE_INDEX_3
            }
        }
    }
}