/****************************************************************
 ** Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmNotify.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/13
 ** Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangChenGuang  2024/11/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.AppPlatformUtils.cancelPendingIntent
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.Formatter
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.clock.common.utils.Log.d
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.Calendar

class AlarmNotify {
    companion object {
        private const val TAG = "AlarmNotify"
        const val ACTION_NEXT_WORKDAY_NOTICES = "action.next.workday.notices"
        private val mutex = Mutex()
    }
    private val alarmWorkdayNoticeTag = "ALARM_WORKDAY_NOTICE_TAG"
    private val lastWorkdayNoticeTime = "last_workday_notice_time"

    fun updateNextWorkdayNotice(context: Context?, isFixAlarmManager: Boolean?) {
        if (context == null || isFixAlarmManager == null) {
            return
        }
        if (DeviceUtils.isExpVersion(context)) {
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            mutex.withLock {
                val calendar = Calendar.getInstance()
                val currentTime = calendar.timeInMillis
                val nextWorkdayNoticeTime = PrefUtils.getLong(
                    context,
                    AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    lastWorkdayNoticeTime, -1
                )
                //1.修改时间，fixAlarmManager
                if (nextWorkdayNoticeTime != -1L) {
                    if (isFixAlarmManager) {
                        fixNextWorkdayNotify(context, nextWorkdayNoticeTime)
                    }
                }
                //2.updateAlarmManager
                setNextWorkdayNotice(context, calendar, currentTime, isFixAlarmManager, nextWorkdayNoticeTime)
            }
        }
    }

    private fun setNextWorkdayNotice(
        context: Context,
        calendar: Calendar,
        currentTime: Long,
        isFixAlarmManager: Boolean,
        lastWorkdayNotifyTime: Long
    ) {
        val sendTimeInMillis = LegalHolidayUtil.queryNextWorkday(
            calendar,
            currentTime,
            isFixAlarmManager
        )
        if (sendTimeInMillis == lastWorkdayNotifyTime && !isFixAlarmManager) {
            d(TAG, "AlarmManager already exists: $sendTimeInMillis")
            return
        }
        if (sendTimeInMillis < 0) {
            d(TAG, "Invalid time: $sendTimeInMillis")
            setNextWorkdayValue(context, sendTimeInMillis)
            return
        }
        d(TAG, "nextWorkdayNoticeTime: ${Formatter.formatTime(sendTimeInMillis)}")
        val intent = createNextWorkdayNotice(context, sendTimeInMillis)
        val am = context.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        val pendingIntent = PendingIntent.getBroadcast(
            context, 0,
            intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT)
        )
        setNextWorkdayValue(context, sendTimeInMillis)
        am?.setExact(AlarmManager.RTC_WAKEUP, sendTimeInMillis, pendingIntent)
    }

    /**
     * 调休工作日通知
     *
     * @param context
     * @param timeInMillis
     */
    fun showNextWorkdayNotices(context: Context?, timeInMillis: Long?) {
        if (context == null || timeInMillis == null) {
            return
        }
        val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager
        val entryApkFromScreen = Intent(context, AlarmClock::class.java)
        entryApkFromScreen.setAction(ClockConstant.ENTER_APK_FROM_SCREEN)
        entryApkFromScreen.addFlags(
            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
        val broadcastEnterApkFromScreen = PendingIntent.getActivity(
            context, 0,
            entryApkFromScreen, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT)
        )
        val label = context.getString(R.string.default_label)
        val b = nm?.let { createNextWorkdayBuilder(context, it, label, timeInMillis) }
        b?.setContentIntent(broadcastEnterApkFromScreen)
        val notification = b?.build()
        nm?.notify(timeInMillis.toInt(), notification)
    }

    private fun createNextWorkdayBuilder(
        context: Context,
        nm: NotificationManager,
        label: String,
        sendTimeInMillis: Long
    ): Notification.Builder {
        val b = Notification.Builder(context, AlarmStateManager.CHANNEL_ID)
        b.apply {
            setContentTitle(label)
            setContentText(context.getString(R.string.work_day_notify_text))
            setSmallIcon(R.drawable.ic_launcher_clock)
            setWhen(sendTimeInMillis)
            setShowWhen(false)
            setAutoCancel(true)
            setOngoing(false)
        }
        val name: CharSequence = context.getString(R.string.clock_notification_label)
        val channel = NotificationChannel(
            AlarmStateManager.CHANNEL_ID, name, NotificationManager.IMPORTANCE_DEFAULT
        )
        channel.setSound(null, null)
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        nm.createNotificationChannel(channel)
        return b
    }

    private fun createNextWorkdayNotice(context: Context, sendTimeInMillis: Long): Intent {
        val intent = Intent(context, AlarmStateManager::class.java)
        intent.apply {
            addCategory(alarmWorkdayNoticeTag)
            putExtra(ACTION_NEXT_WORKDAY_NOTICES, sendTimeInMillis)
            setAction(ACTION_NEXT_WORKDAY_NOTICES)
            setPackage(ClockConstant.CLOCK_PACKAGE)
            addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
        }
        return intent
    }

    private fun setNextWorkdayValue(context: Context, value: Long) {
        PrefUtils.putLong(
            context,
            AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
            lastWorkdayNoticeTime, value, false
        )
    }

    private fun fixNextWorkdayNotify(context: Context, lastWorkdayNotifyTime: Long) {
        val intent = createNextWorkdayNotice(context, lastWorkdayNotifyTime)
        val pendingIntent = PendingIntent.getBroadcast(
            context, 0,
            intent, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT)
        )
        val am = context.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
        if ((am != null)) {
            am.cancel(pendingIntent)
            cancelPendingIntent(pendingIntent)
        }
    }
}