/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - LoopPreferenceFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/10
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/11/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.adapter.LoopAlarmListAdapter
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_WORK_DATA_CLICK
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.EVENT_LOOP_ALARM_WORK_DATA_CLICK_EX
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME
import com.oplus.alarmclock.alarmclock.utils.LoopAlarmEvent.LOOP_ALARM_WORK_TYPE
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_OFF
import com.oplus.alarmclock.utils.ClockConstant.ALARM_HOLIDAY_SWITCH_ON
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils
import com.oplus.alarmclock.utils.LoopAlarmUtils.DEFAULT_LOOP_CYCLE
import com.oplus.alarmclock.utils.LoopAlarmUtils.DEFAULT_LOOP_WORK_DAY
import com.oplus.alarmclock.utils.LoopAlarmUtils.PANEL_TOUCH_DOWN_DIS
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.utils.ToastManager
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import java.util.Calendar

/**
 * 轮班闹钟
 * <AUTHOR>
 * @date 2023年11月10日10:30:11
 */
class LoopPreferenceFragment : COUIPanelFragment() {

    companion object {
        private const val TAG = "LoopPreferenceFragment"

        /**
         * 创建实例
         */
        fun newInstance(alarm: Alarm, reloadAlarm: Alarm?): LoopPreferenceFragment {
            val bundle = Bundle().apply {
                putParcelable(LOOP_ALARM_PAGE_DATA_NAME, alarm)
                putParcelable(LOOP_ALARM_RELOAD_DATA_NAME, reloadAlarm)
            }
            return LoopPreferenceFragment().apply {
                arguments = bundle
            }
        }
    }

    var mTempAlarm: Alarm? = null
    var mLoopAlarmList: COUIRecyclerView? = null


    /**
     * 是否为新建闹钟
     */
    private var mIsNewAlarm: Boolean = false
    private var mAlarm: Alarm? = null
    private var mReloadAlarm: Alarm? = null

    /**
     * 节假日开端
     */
    private var mAlarmHolidaySwitch: Int = ALARM_HOLIDAY_SWITCH_OFF

    /**
     * 根布局
     */
    private lateinit var mRootView: View

    /**
     * 闹钟数据
     */
    private var mAlarmList: ArrayList<Alarm> = ArrayList()

    /**
     * 适配器
     */
    private var mListAdapter: LoopAlarmListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mRootView = inflater.inflate(R.layout.add_alarm_loop_preference, container, false)
        mAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME, Alarm::class.java)
        } else {
            arguments?.getParcelable<Alarm>(LoopAlarmEvent.LOOP_ALARM_PAGE_DATA_NAME)
        }
        mReloadAlarm = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME, Alarm::class.java)
        } else {
            arguments?.getParcelable<Alarm>(LoopAlarmEvent.LOOP_ALARM_RELOAD_DATA_NAME)
        }
        init()
        return mRootView
    }

    private fun init() {
        initEvent()
        initAlarmList()
        initRecycleList()
    }

    /**
     * 初始化监听器
     */
    private fun initEvent() {
        //面板点击完成
        LiteEventBus.instance.with(
            LoopAlarmEvent.EVENT_LOOP_ALARM_LIST_SAVE_CLICK,
            hashCode().toString()
        )
            .observe(this) {
                Log.d(TAG, "loop detail save")
                saveAlarm()
            }
        //面板点击取消
        LiteEventBus.instance.with(
            LoopAlarmEvent.EVENT_LOOP_ALARM_LIST_CANCEL_CLICK,
            hashCode().toString()
        )
            .observe(this) {
                LiteEventBus.instance.send(LoopAlarmEvent.EVENT_LOOP_ALARM_ADD_RESET, mTempAlarm)
            }
    }

    /**
     * 是否可以保存数据
     */
    fun canSaveAlarm(): Boolean {
        mListAdapter?.let {
            if (LoopAlarmUtils.checkLoopAlarmResetDays(it.mData, 0)) {
                ToastManager.showToast(
                    mLoopAlarmList?.context,
                    AlarmClockApplication.getInstance().getString(R.string.loop_alarm_cycle_tips)
                )
                return false
            }
        }
        return true
    }

    /**
     * 保存数据
     */
    fun saveAlarm() {
        mAlarm?.apply {
            mListAdapter?.let {
                loopAlarmList = it.mData
                setmLoopCycleDays(it.mData.size)
                val data = LoopAlarmUtils.computeLoopDays(it.mData)
                setmLoopWorkDays(data.first)
                setmLoopRestDays(data.second)
                setmWorkDayType(LOOP_ALARM_WORK_TYPE)
                LiteEventBus.instance.send(EVENT_LOOP_ALARM_WORK_DATA_CLICK, "")
                if (DeviceUtils.isExpVersion(context)) {
                    LiteEventBus.instance.send(EVENT_LOOP_ALARM_WORK_DATA_CLICK_EX, "")
                }
            }
        }
    }

    /**
     * 初始化闹钟数据
     */
    private fun initAlarmList() {
        context?.let { contextIt ->
            mIsNewAlarm = mAlarm?.id == 0L
            mAlarmHolidaySwitch = PrefUtils.getInt(
                contextIt,
                PrefUtils.ALARM_HOLIDAY_SWITCH,
                PrefUtils.ALARM_HOLIDAY_SWITCH_KEY,
                ALARM_HOLIDAY_SWITCH_OFF
            )
            mAlarm?.apply {
                if (mIsNewAlarm || 0 == getmLoopCycleDays()) {
                    //新建闹钟
                    if (loopAlarmList.size != 0 && 0 != getmLoopCycleDays()) {
                        mAlarmList.addAll(loopAlarmList)
                        setmLoopCycleDays(loopAlarmList.size)
                    } else {
                        setmLoopSwitch(ALARM_HOLIDAY_SWITCH_ON)
                        setmLoopCycleDays(DEFAULT_LOOP_CYCLE)
                        setmLoopWorkDays(DEFAULT_LOOP_WORK_DAY)
                        setmLoopDay(1)
                        holidaySwitch = mAlarmHolidaySwitch
                        mAlarmList.addAll(LoopAlarmUtils.addDefaultLoopAlarm(DEFAULT_LOOP_CYCLE))
                    }
                } else {
                    //编辑
                    mAlarmList.addAll(loopAlarmList)
                    setmLoopCycleDays(loopAlarmList.size)
                }
            }
            mTempAlarm = mAlarm?.deepCopy()
            mReloadAlarm?.let {
                mTempAlarm = it.deepCopy()
            }
        }
    }

    /**
     * 初始化列表
     */
    private fun initRecycleList() {
        mLoopAlarmList = mRootView.findViewById(R.id.loop_alarm_cycle_list)
        mLoopAlarmList?.apply {
            mAlarm?.let {
                adapter = null
                mListAdapter = LoopAlarmListAdapter(context, it)
                mListAdapter?.let { ad ->
                    ad.updateData(mAlarmList)
                    itemAnimator = null
                    val linearLayoutManager = LinearLayoutManager(context)
                    layoutManager = linearLayoutManager
                    adapter = ad
                }
            }
        }
    }

    /**
     * 是否可以关闭面板
     */
    fun canClosePanel(): Boolean {
        val location = Rect()
        mLoopAlarmList?.getGlobalVisibleRect(location)
        if (location.top > PANEL_TOUCH_DOWN_DIS) {
            return false
        }
        return true
    }

    /**
     * 销毁
     */
    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "onDestroyView")
        LiteEventBus.instance.releaseObserver(hashCode().toString())
        mListAdapter?.clearDialogView()
    }
}