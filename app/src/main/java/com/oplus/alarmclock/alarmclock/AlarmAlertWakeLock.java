/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :handle AlarmAlertWakeLock for fullScreen and dialog. Utility class to hold wake lock
 * in app.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alarmclock;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.PowerManager;

import com.oplus.clock.common.utils.Log;

public class AlarmAlertWakeLock {

    private static final String TAG = "AlarmAlertWakeLock";
    private static final int RELEASE_WAKELOCK = 1;
    private static final int RELEASE_PARTIAL_WAKELOCK = 2;
    private static final int ACQUIRE_WAKELOCK_DIM = 3;
    private static final int RELEASE_WAKELOCK_CPU = 4;
    private static PowerManager.WakeLock sCpuWakeLock;
    private static PowerManager.WakeLock sCpuWakeLockPartial;
    private static PowerManager.WakeLock sCpuWakeLockFull;
    private static PowerManager.WakeLock sCpuWake;

    public static PowerManager.WakeLock getsCpuWake() {
        return sCpuWake;
    }

    private static Handler sHandler = new Handler(Looper.getMainLooper()) {
        @SuppressLint("InvalidWakeLockTag")
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case RELEASE_WAKELOCK_CPU:
                    synchronized (AlarmAlertWakeLock.class) {
                        if (sCpuWake != null) {
                            try {
                                if (sCpuWake.isHeld()) {
                                    sCpuWake.release();
                                    sCpuWake = null;
                                    Log.d(TAG, "RELEASE_WAKELOCK_CPU releaseCpuLockCpu completed");
                                }
                            } catch (Exception e) {
                                Log.d(TAG, "RELEASE_WAKELOCK_CPU error: " + e.getMessage());
                            }
                            Log.d(TAG, "sHandler locking");
                        }
                    }
                    break;
                case RELEASE_WAKELOCK:
                    if ((sCpuWakeLock != null) && sCpuWakeLock.isHeld()) {
                        Log.i(TAG, "releaseCpuWakeLockDim handle");
                        sCpuWakeLock.release();
                        sCpuWakeLock = null;
                    }
                    break;
                case RELEASE_PARTIAL_WAKELOCK:
                    if ((sCpuWakeLockPartial != null) && sCpuWakeLockPartial.isHeld()) {
                        sCpuWakeLockPartial.release();
                        sCpuWakeLockPartial = null;
                    }
                    break;
                case ACQUIRE_WAKELOCK_DIM:
                    if ((sCpuWakeLock != null) && sCpuWakeLock.isHeld()) {
                        return;
                    }
                    if (!(msg.obj instanceof Context)) {
                        return;
                    }
                    Context context = (Context) msg.obj;
                    PowerManager pm = (PowerManager) context
                            .getSystemService(Context.POWER_SERVICE);
                    sCpuWakeLock = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK
                                    | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE,
                            "AlarmAlertWakeLockDim");
                    int alarmLength = msg.arg1;
                    long timeout = (15 * 60 + 1) * 1000L;// alarm will ring for 15 mins.
                    if (alarmLength > 0) {
                        timeout = (alarmLength * 60 + 1) * 1000L;
                    }
                    Log.i(TAG, "acquireCpuWakeLockDim:" + timeout);
                    sCpuWakeLock.acquire();
                    sHandler.sendEmptyMessageDelayed(RELEASE_WAKELOCK, timeout);
                    break;
                default:
                    break;
            }
        }
    };

    @SuppressLint("InvalidWakeLockTag")
    public static PowerManager.WakeLock createPartialWakeLock(Context context) {
        if (context != null) {
            PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                sCpuWake = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, TAG);
                return sCpuWake;
            }
        }
        return null;
    }

    @SuppressLint("InvalidWakeLockTag")
    public static void acquirePartialWakeLock(Context context) {
        if (context != null) {
            PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (pm != null) {
                if (sCpuWake == null) {
                    sCpuWake = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, TAG);
                }
                long timeout = 60 * 1000; //响铃和初始化闹钟状态下持锁，1分钟超时，保证锁可以被释放
                sCpuWake.acquire(timeout);
                Log.i(TAG, "acquirePartialWakeLock:" + timeout);
                sHandler.sendEmptyMessageDelayed(RELEASE_WAKELOCK_CPU, timeout);
            }
        }
    }

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuWakeLockPartial(Context context, int wakeLockLength) {
        if (sCpuWakeLockPartial != null) {
            return;
        }

        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        sCpuWakeLockPartial = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK
                        | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE,
                "AlarmAlertWakeLockPartial");
        long timeout = (wakeLockLength * 60 + 1) * 1000L;// alarm will ring for 15 mins.
        sCpuWakeLockPartial.acquire();
        sHandler.sendEmptyMessageDelayed(RELEASE_PARTIAL_WAKELOCK, timeout);
    }

    @SuppressLint("InvalidWakeLockTag")
    public static void acquireCpuWakeLockFull(Context context) {
        if (sCpuWakeLockFull != null && sCpuWakeLockFull.isHeld()) {
            return;
        }
        if (context == null) {
            return;
        }
        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        sCpuWakeLockFull = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK
                        | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE,
                "AlarmAlertWakeLockFull");
        long timeout = 200;// alarm will ring for 200ms.
        sCpuWakeLockFull.acquire(timeout);
    }

    public static void acquireCpuWakeLockDim(Context context, int alarmLength) {
        if (context == null) {
            return;
        }
        Message msg = Message.obtain();
        msg.what = ACQUIRE_WAKELOCK_DIM;
        msg.obj = context;
        msg.arg1 = alarmLength;
        sHandler.removeMessages(ACQUIRE_WAKELOCK_DIM);
        sHandler.sendMessage(msg);
    }

    public static void releaseCpuLock() {
        releaseCpuLockPartial();
        releaseCpuLockDim();
        releaseCpuLockCpu(TAG + "releaseCpuLock");
    }

    public static void releaseCpuLockPartial() {
        Log.i(TAG, "releaseCpuLockPartial");
        sHandler.removeMessages(RELEASE_PARTIAL_WAKELOCK);
        sHandler.sendEmptyMessage(RELEASE_PARTIAL_WAKELOCK);
    }

    public static void releaseCpuLockDim() {
        Log.i(TAG, "releaseCpuLockDim");
        sHandler.removeMessages(RELEASE_WAKELOCK);
        sHandler.sendEmptyMessage(RELEASE_WAKELOCK);
    }

    public static void releaseCpuLockCpu(String tag) {
        Log.i(TAG, "releaseCpuLockCpu:" + tag);
        synchronized (AlarmAlertWakeLock.class) {
            if (sCpuWake != null && sCpuWake.isHeld()) {
                sCpuWake.release();
                sCpuWake = null;
                Log.d(TAG, tag + " : releaseCpuLockCpu completed");
            }
        }
    }

    public static void releasePartialWakelock() {
        if ((sCpuWakeLockPartial != null) && sCpuWakeLockPartial.isHeld()) {
            Log.i(TAG, "releasePartialWakelock");
            sCpuWakeLockPartial.release();
            sCpuWakeLockPartial = null;
        }
    }
}
