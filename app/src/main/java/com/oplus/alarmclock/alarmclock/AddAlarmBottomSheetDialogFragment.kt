/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AddAlarmBottomSheetDialogFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/2/19
 ** Author: ********
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ********  2024/2/19     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.View
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.clock.common.utils.Log

class AddAlarmBottomSheetDialogFragment(
    private val mSavedInstanceState: Bundle?,
    private val mIntent: Intent,
    private val mUiMode: UiMode
) : COUIBottomSheetDialogFragment() {

    companion object {
        private const val TAG = "AddAlarmBottomSheetDialogFragment"
    }
    private var mReceiver: TimeBroadcastReceiver? = null
    private var mAddAlarmPanelFragment = mSavedInstanceState?.let {
        AddAlarmPanelFragment.newInstance(mSavedInstanceState, mIntent)
    } ?: AddAlarmPanelFragment(mIntent)

    init {
        if (isTabletMode()) {
            //平板设置固定高度
            setHeight(AlarmClockApplication.getInstance().resources.getDimensionPixelOffset(R.dimen.layout_dp_800))
        } else {
            //其他设置最大高度
            setIsShowInMaxHeight(true)
        }
        setMainPanelFragment(mAddAlarmPanelFragment)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        registerReceiver()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unregisterReceiver()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        bottomSheetDialog?.apply {
            //禁用拖动关闭和点击空白关闭
            setCanceledOnTouchOutside(false)
            setDraggable(false)
            setAnimationListener(object : COUIBottomSheetDialog.OnAnimationListener {
                override fun onShowAnimationEnd() {
                    super.onShowAnimationEnd()
                    //动画执行完成后启动拖动和点击空白关闭
                    setCanceledOnTouchOutside(true)
                    setDraggable(true)
                }
            })
        }
        return dialog
    }

    /**
     * 注册Receiver
     */
    private fun registerReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF)
        intentFilter.addAction(Intent.ACTION_TIME_TICK)
        intentFilter.addAction(Intent.ACTION_TIMEZONE_CHANGED)
        intentFilter.addAction(Intent.ACTION_TIME_CHANGED)
        activity?.let {
            mReceiver = TimeBroadcastReceiver(it, mAddAlarmPanelFragment)
            it.registerReceiver(
                mReceiver,
                intentFilter,
                ClockConstant.OPLUS_SAFE_PERMISSION,
                null,
                Context.RECEIVER_EXPORTED
            )
            Log.d(TAG, "registerReceiver:$mReceiver")
        }
    }

    private fun unregisterReceiver() {
        Log.d(TAG, "unregisterReceiver $activity $mReceiver")
        if (mReceiver != null) {
            activity?.unregisterReceiver(mReceiver)
            mReceiver = null
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mAddAlarmPanelFragment.getAddAlarmFragment()
            ?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        Log.i(TAG, "onRequestPermissionsResult")
        mAddAlarmPanelFragment.getAddAlarmFragment()
            ?.requestPermissionsResult(requestCode, permissions, grantResults)
    }

    fun isTabletMode(): Boolean {
        return UiMode.LARGE_HORIZONTAL === mUiMode || UiMode.LARGE_VERTICAL === mUiMode
    }

    override fun dismiss() {
        super.dismiss()
        unregisterReceiver()
        onDestroy()
    }

    fun getAddAlarmPanelFragment(): AddAlarmPanelFragment {
        return mAddAlarmPanelFragment
    }
}