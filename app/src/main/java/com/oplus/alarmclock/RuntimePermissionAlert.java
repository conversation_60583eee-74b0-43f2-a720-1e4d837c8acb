/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
// OPLUS Java File Skip Rule:LineLength,MethodComplexity
package com.oplus.alarmclock;

import static android.Manifest.permission.READ_MEDIA_AUDIO;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnKeyListener;
import android.content.Intent;
import android.content.OplusBaseIntent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build.VERSION;
import android.os.Bundle;
import android.provider.Settings;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.coui.appcompat.button.COUIButton;
import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.statement.StatementDialogUtils;
import com.oplus.alarmclock.timer.mini.OplusTimerMiniActivity;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.modelview.IRestoreSavedState;
import com.oplus.alarmclock.view.modelview.ISaveInstanceState;
import com.oplus.clock.common.osdk.IntentNativeUtils;
import com.oplus.clock.common.utils.Log;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;

import androidx.annotation.VisibleForTesting;
import androidx.appcompat.app.AlertDialog;


public class RuntimePermissionAlert implements ISaveInstanceState, IRestoreSavedState {

    public static final int GUIDE_PERMISSIONS_CODE = 1002;
    public static final int GUIDE_PERMISSIONS_NOTIFY_CODE = 1004;
    public static final int PERMISSIONS_REQUEST = 1001;
    public static final int REQUEST_CODE_MEDIA_ALARM = 1098;
    public static final int REQUEST_CODE_MEDIA_TIMER = 1099;
    /**
     * 默认铃声音频权限
     */
    public static final int REQUEST_CODE_MEDIA_DEFAULT_RING = 1100;
    public static final String SP_KEY_CTA_DIALOG = "cta_dialog_should_show";
    public static final String SP_NAME = "local_config";
    public static final String KEY_SHOW_PERMISSION_DIALOG = "key_show_permission_dialog";
    public static final String KEY_NEED_OPEN_ADD_ALARM = "key_need_open_add_alarm";
    public static final String KEY_HAVE_PERMISSION_DATA = "key_have_permission_data";
    public static final String POST_NOTIFICATIONS = "android.permission.POST_NOTIFICATIONS";

    private static final String TAG = "RuntimePerssionAlert";
    private static final String KEY_PACKAGE = "package:";
    private static final String ERROR_MSG_BY_PACKAGE = "package not found e:";
    private static final String EXTRA_PACKAGE_NAME = "android.intent.extra.PACKAGE_NAME";
    private static final String ACTION_SAFE_CENTER_PERMISSION = "oplus.intent.action.PERMISSION_APP_DETAIL";
    private static final String PACKAGE_NAME_SECURITY_PERMISSION = "com.oplus.securitypermission";
    private static final String META_DATA_NAVIGATE_TO_APP_PERMISSION_KEY = "navigateToAppPermissions";
    private static final String KEY_PERMISSION_LIST = "permissionList";
    private static final String KEY_PACKAGE_NAME = "packageName";

    private Activity mActivity;
    private RuntimePermissionCallBack mCallBack;
    private boolean mIsCheckingPermissions = false;
    private boolean mIsNeedOpenAddClockView = false;
    private boolean mIsAddTimer = false;
    private AlertDialog mDialog;

    public RuntimePermissionAlert(final Activity activity, RuntimePermissionCallBack callbackup) {
        mActivity = activity;
        mCallBack = callbackup;
    }

    /**
     * 检查申请存储权限
     *
     * @param isNeedOpenAddClockView
     */
    public void getCheckRuntimePermissions(boolean isNeedOpenAddClockView) {
        Log.i(TAG, "getCheckRuntimePermissions mIsNeedOpenAddClockView = "
                + isNeedOpenAddClockView + "  [mIsCheckingPermissions]==" + mIsCheckingPermissions);
        mIsNeedOpenAddClockView = isNeedOpenAddClockView;
        if (!mIsCheckingPermissions) {
            Log.d(TAG, "Re-apply for storage permissions ");
            if (mActivity != null) {
                checkPermission(mActivity, -1);
            }
        }
    }

    /**
     * 申请存储权限 根据返回结果处理，  内外销机型都执行。
     * 允许进入  不允许退出的方式不一样。
     */
    public void requestPermissionsResult(int requestCode, String[] permissions,
                                         int[] grantResults, boolean isFromMiniApp) {
        requestPermissionsResult(requestCode, permissions, grantResults, isFromMiniApp, false);
    }

    /**
     * 申请存储权限 根据返回结果处理，  内外销机型都执行。
     * 允许进入  不允许退出的方式不一样。
     *
     * @param requestCode
     * @param permissions
     * @param grantResults
     */
    public void requestPermissionsResult(int requestCode, String[] permissions, int[] grantResults, boolean isFromMiniApp, boolean isTimer) {
        Log.i(TAG, "requestPermissionsResult requestCode = " + requestCode);
        if (VERSION.SDK_INT < Utils.VERSION_CODES_M) {
            return;
        }
        boolean granted = true;
        if ((PERMISSIONS_REQUEST == requestCode) || (REQUEST_CODE_MEDIA_ALARM == requestCode) || (REQUEST_CODE_MEDIA_TIMER == requestCode)
                || REQUEST_CODE_MEDIA_DEFAULT_RING == requestCode) {
            ArrayList<String> guidePermissions = new ArrayList<String>();
            ArrayList<String> noGrantedPermissions = new ArrayList<String>();
            int index = 0;
            if (grantResults != null) {
                Log.i(TAG, "requestPermissionsResult result != null , length = " + grantResults.length);
                for (int result : grantResults) {
                    Log.i(TAG, "requestPermissionsResult result = " + result);
                    if (result != PackageManager.PERMISSION_GRANTED) {
                        granted = false;
                        if ((permissions != null) && (index <= permissions.length - 1)) {
                            String permission = permissions[index];
                            Log.i(TAG, "requestPermissionsResult permission = " + permission);
                            noGrantedPermissions.add(permission);
                            if ((mActivity != null) && !mActivity.shouldShowRequestPermissionRationale(permission)) {
                                guidePermissions.add(permission);
                            }
                        }
                    }
                    index++;
                }
            }
            if (!noGrantedPermissions.isEmpty() && noGrantedPermissions.contains(POST_NOTIFICATIONS)) {
                cancelNextAlarmNotices(mActivity);
            }
            if (!guidePermissions.isEmpty()) {
                Log.d(TAG, "guidePermissions is not null");
                showGuideDialog(guidePermissions.contains(POST_NOTIFICATIONS), isFromMiniApp, isTimer);
            } else {
                Log.d(TAG, "guidePermissions is null  granted = " + granted);
                if (!granted) {
                    if ((mActivity != null) && !noGrantedPermissions.contains(POST_NOTIFICATIONS)) {
                        //新建闹钟申请存储权限时 如果不允许， 外销和内销执行逻辑不一样。内销会在第二次申请拒绝时和外销表现一致。
                        if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                            showGuideSettingsDialog();
                        } else {
                            if (!mActivity.isDestroyed() && (mActivity instanceof AlarmClock)) {
                                ((AlarmClock) mActivity).resetAlarmItemCurrentCheckByPermission();
                            }
                        }
                    }
                    if (mCallBack != null) {
                        mCallBack.doAfterDenieD();
                    }
                }
            }
        }
        if (granted && mCallBack != null) {
            Log.i(TAG, "requestPermissionsResult  doAfterGranted");
            mCallBack.doAfterGranted(mIsNeedOpenAddClockView);
            mIsCheckingPermissions = false;
        }
    }

    public void expCheckPermission(final Activity activity, final int requestCode, boolean isFromTimer) {
        Log.d(TAG, "ExpCheckPermission  requestPermissionList");
        mIsCheckingPermissions = false;
        mIsAddTimer = isFromTimer;
        checkPermission(activity, requestCode);
    }

    public boolean hasNotificationPermission() {
        if (mActivity != null) {
            Log.d(TAG, "hasNotificationPermission");
            return mActivity.checkSelfPermission(POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED;
        }
        return false;
    }

    public void requestNotificationPermission() {
        if (!mIsCheckingPermissions) {
            Log.d(TAG, "requestNotificationPermission");
            if (mActivity != null) {
                Log.d(TAG, "Re-apply for Notifications permissions");
                mActivity.requestPermissions(new String[]{POST_NOTIFICATIONS}, PERMISSIONS_REQUEST);
            }
        }
    }

    /**
     * 第一次新建闹钟 申请存储权限   （内外机型都执行）
     *
     * @param activity
     */
    @SuppressLint("NewApi")
    private void checkPermission(final Activity activity, final int requestCode) {
        ArrayList<String> requestPermissionList = new ArrayList<String>();
        StatementDialogUtils.Companion.checkSelfPermission(activity, requestPermissionList);
        Log.d(TAG, "checkPermission  requestPermissionList");
        if (!requestPermissionList.isEmpty()) {
            Log.i(TAG, "requestPermissionList is not null  mIsNeedOpenAddClockView = " + mIsNeedOpenAddClockView);
            String[] p = new String[requestPermissionList.size()];
            for (int i = 0; i < requestPermissionList.size(); i++) {
                p[i] = requestPermissionList.get(i);
            }
            if ((mActivity != null) && !mIsCheckingPermissions) {
                Log.d(TAG, "checkPermission p.length = " + p.length);
                mIsCheckingPermissions = true;
                mActivity.requestPermissions(p, (requestCode > 0) ? requestCode : PERMISSIONS_REQUEST);
            }
        } else {
            Log.i(TAG, "requestPermissionList is null,all permission is requested");
            if (mCallBack != null) {
                Log.i(TAG, "checkPermission  doAfterGranted");
                mCallBack.doAfterGranted(mIsNeedOpenAddClockView);
                mIsCheckingPermissions = false;
            }
        }
    }

    private void cancelNextAlarmNotices(Context context) {
        Log.d(TAG, "cancelNextAlarmNotices");
        if (AlarmUtils.isOpenNextAlarmNotices(context)) {
            AlarmUtils.setOpenNextAlarmNotices(context, false);
            AlarmStateManager.cancelNextAlarmNotices(context);
        }
    }

    public void showGuideDialog(boolean isNofifyGuide, boolean isFromMiniApp, boolean isTimer) {
        if (isNofifyGuide) {
            if (isFromMiniApp) {
                showMiniGuideNotifyDialog(isTimer);
            } else {
                showGuideNotifyDialog();
            }
        } else {
            showGuideSettingsDialog();
        }
    }

    @SuppressLint("NewApi")
    public void showMiniGuideNotifyDialog(boolean isTimer) {
        if ((mActivity == null) || ((mDialog != null) && mDialog.isShowing())) {
            return;
        }
        closeAppPermissionDialog();
        mDialog = new COUIAlertDialogBuilder(mActivity, R.style.COUIAlertDialog_Center_Tiny)
                .setTitle(R.string.clock_runtime_warning_notify_title)
                .setMessage(R.string.clock_runtime_warning_notify_content)
                .setPositiveButton(R.string.settings, (dialog, which) -> {
                    boolean isSuccess = false;
                    if (isCanNavigateToAppPermissions()) {
                        isSuccess = miniNavigateToSafePermission(POST_NOTIFICATIONS, isTimer);
                        if (isSuccess) {
                            return;
                        }
                    }
                    Intent intent = new Intent();
                    String packageName = mActivity.getApplicationContext().getPackageName();
                    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse(KEY_PACKAGE + packageName));
                    intent.putExtra(EXTRA_PACKAGE_NAME, packageName);
                    try {
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        if (isTimer) {
                            intent.putExtra(ClockConstant.EXTRA_DESCRIPTION, mActivity.getResources().getString(R.string.authorization_continue_description,
                                    mActivity.getResources().getString(R.string.hour_meter)));
                        } else {
                            intent.putExtra(ClockConstant.EXTRA_DESCRIPTION, mActivity.getResources().getString(R.string.authorization_continue_description,
                                    mActivity.getResources().getString(R.string.Alarm_Clock_app_label)));
                        }
                        IntentNativeUtils.setOplusFlags(intent, OplusBaseIntent.OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED | OplusBaseIntent.OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY);
                        if (mActivity instanceof OplusTimerMiniActivity) {
                            ((OplusTimerMiniActivity) mActivity).unRegisterScreenChangeListener();
                        }
                        mActivity.startActivity(intent);
                    } catch (Exception e) {
                        Log.e(TAG, ERROR_MSG_BY_PACKAGE + e);
                    }
                })
                .setNegativeButton(R.string.cancel, (dialog, which) -> mCallBack.onExitClick())
                .setCancelable(false).create();
        mDialog.setCanceledOnTouchOutside(false);
        mDialog.show();
        COUIButton button = (COUIButton) mDialog.getButton(DialogInterface.BUTTON_POSITIVE);
        if (button != null) {
            button.setDrawableColor(COUIContextUtil.getAttrColor(mActivity, R.attr.couiColorPrimary));
            button.setTextColor(COUIContextUtil.getColor(mActivity, R.color.coui_btn_default_text_normal_color));
            button.invalidate();
        }
    }

    @SuppressLint("NewApi")
    public void showGuideNotifyDialog() {
        if ((mActivity == null) || ((mDialog != null) && mDialog.isShowing())) {
            return;
        }
        closeAppPermissionDialog();
        mDialog = new COUIAlertDialogBuilder(mActivity)
                .setTitle(R.string.clock_runtime_warning_notify_title)
                .setMessage(R.string.clock_runtime_warning_notify_content)
                .setBlurBackgroundDrawable(false)
                .setPositiveButton(R.string.settings,
                        (dialog, which) -> {
                            if (isCanNavigateToAppPermissions()) {
                                boolean isSuccess = isNavigateToSafePermission(POST_NOTIFICATIONS, GUIDE_PERMISSIONS_NOTIFY_CODE);
                                if (isSuccess) {
                                    return;
                                }
                            }
                            Intent intent = new Intent();
                            String packageName = mActivity.getApplicationContext().getPackageName();
                            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                            intent.setData(Uri.parse(KEY_PACKAGE + packageName));
                            intent.putExtra(EXTRA_PACKAGE_NAME, packageName);
                            try {
                                mActivity.startActivityForResult(intent, GUIDE_PERMISSIONS_NOTIFY_CODE);
                            } catch (Exception e) {
                                Log.e(TAG, ERROR_MSG_BY_PACKAGE + e);
                            }
                        })
                .setNegativeButton(R.string.cancel,
                        (dialog, which) -> mCallBack.onExitClick())
                .setCancelable(false).create();
        if (mDialog.getWindow() != null && mDialog.getWindow().getDecorView() != null) {
            mDialog.getWindow().getDecorView().setOnTouchListener((view, motionEvent) -> {
                if (Utils.isOutOfBounds(mActivity, motionEvent) && FlexibleWindowUtils.issIsFlexibleWindow()) {
                    mCallBack.onClickOutside();
                }
                return false;
            });
        }
        mDialog.show();
    }

    @SuppressLint("InlinedApi")
    public void showGuideSettingsDialog() {
        if ((mActivity == null) || ((mDialog != null) && mDialog.isShowing())) {
            return;
        }
        closeAppPermissionDialog();
        mDialog = new COUIAlertDialogBuilder(mActivity)
                .setTitle(Utils.isAboveT() ? R.string.enable_audio_file_permissions_v1 : R.string.color_runtime_warning_write_title)
                .setMessage(Utils.isAboveT() ? R.string.audio_file_permissions_introduction : R.string.color_runtime_warning_write_content)
                .setBlurBackgroundDrawable(false)
                .setPositiveButton(R.string.settings,
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                if (mIsAddTimer) {
                                    if (!mActivity.isDestroyed() && (mActivity instanceof AlarmClock)) {
                                        ((AlarmClock) mActivity).closeAppPermissionDialog();
                                    }
                                }
                                if (isCanNavigateToAppPermissions()) {
                                    boolean isSuccess = isNavigateToSafePermission(READ_MEDIA_AUDIO, GUIDE_PERMISSIONS_CODE);
                                    if (isSuccess) {
                                        return;
                                    }
                                }
                                Intent intent = new Intent();
                                String packageName = mActivity.getApplicationContext().getPackageName();
                                intent.setAction(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                intent.setData(Uri.parse(KEY_PACKAGE + packageName));
                                intent.putExtra(EXTRA_PACKAGE_NAME, packageName);
                                try {
                                    mActivity.startActivityForResult(intent, GUIDE_PERMISSIONS_CODE);
                                } catch (Exception e) {
                                    Log.e(TAG, ERROR_MSG_BY_PACKAGE + e);
                                }
                            }
                        })
                .setNegativeButton(R.string.cancel,
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                                    mCallBack.onExitClick();
                                    if (!mActivity.isDestroyed() && (mActivity instanceof AlarmClock)) {
                                        ((AlarmClock) mActivity).dismissTimerModeView();
                                    }
                                } else {
                                    if (!mActivity.isDestroyed() && (mActivity instanceof AlarmClock)) {
                                        if (mIsAddTimer) {
                                            ((AlarmClock) mActivity).dismissTimerModeView();
                                        } else {
                                            ((AlarmClock) mActivity).resetAlarmItemCurrentCheckByPermission();
                                        }
                                    }
                                }
                            }
                        })
                .setCancelable(false).setOnKeyListener(new OnKeyListener() {
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        if ((keyCode == KeyEvent.KEYCODE_BACK)
                                && (event.getAction() == KeyEvent.ACTION_DOWN)) {
                            if (DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
                                closeAppPermissionDialog();
                                if (!mActivity.isDestroyed() && (mActivity instanceof AlarmClock)) {
                                    ((AlarmClock) mActivity).dismissTimerModeView();
                                }
                            }
                            return true;
                        }
                        return false;
                    }
                }).create();
        mDialog.show();
        View view = mDialog.findViewById(android.R.id.message);
        if (view instanceof TextView) {
            ((TextView) view).setGravity(Gravity.CENTER_HORIZONTAL);
        }
    }

    public void closeAppPermissionDialog() {
        if ((mDialog != null) && mDialog.isShowing()) {
            mDialog.cancel();
        }
    }

    @Nullable
    @Override
    public Bundle getBundleWhenSaveInstanceState() {
        Bundle bundle = null;
        if (mIsNeedOpenAddClockView) {
            bundle = new Bundle();
            bundle.putBoolean(KEY_NEED_OPEN_ADD_ALARM, true);
        }
        if ((mDialog != null) && mDialog.isShowing()) {
            if (bundle == null) {
                bundle = new Bundle();
            }
            bundle.putBoolean(KEY_SHOW_PERMISSION_DIALOG, true);
        }
        if (bundle != null) {
            bundle.putBoolean(KEY_HAVE_PERMISSION_DATA, true);
        }
        return bundle;
    }

    @Override
    public void onRestoreSavedState(@androidx.annotation.Nullable Bundle bundle) {
        if (bundle.getBoolean(KEY_SHOW_PERMISSION_DIALOG)) {
            showGuideSettingsDialog();
        }
        if (bundle.getBoolean(KEY_NEED_OPEN_ADD_ALARM)) {
            mIsNeedOpenAddClockView = true;
        }
    }

    public interface RuntimePermissionCallBack {
        void doAfterGranted(boolean isNeedOpenAddView);

        void doAfterDenieD();

        void onExitClick();

        /**
         * 点击外部
         */
        void onClickOutside();
    }

    public void resetCheckingPermissions() {
        this.mIsCheckingPermissions = false;

    }

    boolean getCheckingPermissions() {
        return this.mIsCheckingPermissions;

    }

    void resetNeedOpenAddClockView() {
        this.mIsNeedOpenAddClockView = false;
    }

    private Intent safePermissionIntent(String permission) {
        String packageName = mActivity.getApplicationContext().getPackageName();
        Intent actionIntent = new Intent(ACTION_SAFE_CENTER_PERMISSION);
        Bundle bundle = new Bundle();
        //传入受阻的权限列表（原生权限名）
        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add(permission);
        bundle.putStringArrayList(KEY_PERMISSION_LIST, arrayList);
        //传入待修改权限的包名
        bundle.putString(KEY_PACKAGE_NAME, packageName);
        actionIntent.putExtras(bundle);
        return actionIntent;
    }

    /**
     * mini 跳转权限界面
     */
    private Boolean miniNavigateToSafePermission(String permission, boolean isTimer) {
        Intent actionIntent = safePermissionIntent(permission);
        try {
            actionIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            if (isTimer) {
                actionIntent.putExtra(ClockConstant.EXTRA_DESCRIPTION, mActivity.getResources().getString(R.string.authorization_continue_description,
                        mActivity.getResources().getString(R.string.hour_meter)));
            } else {
                actionIntent.putExtra(ClockConstant.EXTRA_DESCRIPTION, mActivity.getResources().getString(R.string.authorization_continue_description,
                        mActivity.getResources().getString(R.string.Alarm_Clock_app_label)));
            }
            IntentNativeUtils.setOplusFlags(actionIntent, OplusBaseIntent.OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED | OplusBaseIntent.OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY);
            if (mActivity instanceof OplusTimerMiniActivity) {
                ((OplusTimerMiniActivity) mActivity).unRegisterScreenChangeListener();
            }
            mActivity.startActivity(actionIntent);
            return true;
        } catch (Exception e) {
            Log.e(TAG, ACTION_SAFE_CENTER_PERMISSION + "mini permission error" + e);
            return false;
        }
    }

    /**
     * 权限受阻跳转优化
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    public Boolean isNavigateToSafePermission(String permission, int requestCode) {
        Intent actionIntent = safePermissionIntent(permission);
        try {
            mActivity.startActivityForResult(actionIntent, requestCode);
            return true;
        } catch (Exception e) {
            Log.e(TAG, ACTION_SAFE_CENTER_PERMISSION + " permission error" + e);
            return false;
        }
    }

    /**
     * meta-data判断结果为true时使用新特性实现代码
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    public boolean isCanNavigateToAppPermissions() {
        boolean canNavigateToAppPermissions = false;
        ApplicationInfo applicationInfo = null;
        try {
            applicationInfo = mActivity.getPackageManager().getApplicationInfo(PACKAGE_NAME_SECURITY_PERMISSION, PackageManager.GET_META_DATA);
        } catch (PackageManager.NameNotFoundException nameNotFoundException) {
            Log.e(TAG, "security permission not found");
        }
        if (applicationInfo != null) {
            canNavigateToAppPermissions = applicationInfo.metaData.getBoolean(META_DATA_NAVIGATE_TO_APP_PERMISSION_KEY, false);
        }
        return canNavigateToAppPermissions;
    }

}
