/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :handle CTS request.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-5-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.cts;

import static android.provider.AlarmClock.EXTRA_DAYS;
import static android.provider.AlarmClock.EXTRA_RINGTONE;
import static com.oplus.alarmclock.utils.AlarmRingUtils.getRingNameFromOld;

import android.annotation.SuppressLint;
import android.app.Service;
import android.app.VoiceInteractor;
import android.app.VoiceInteractor.CompleteVoiceRequest;
import android.app.VoiceInteractor.Prompt;
import android.content.ComponentName;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.media.AudioManager;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.IBinder;
import android.text.TextUtils;
import android.widget.Toast;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ai.AiAlarmUtils;
import com.oplus.alarmclock.ai.AiSupportContentProvider;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.timer.TimerAlertReceiver;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.timer.TimerService.TimerBinder;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.ToastManager;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Calendar;

//TODO: Clean this class later.
public class HandleApiActivity extends BaseActivity {

    public static final String IS_FROM_HANDLE_API_KEY = "is_from_handle_api";
    public static final String CTS_TIMER_NAME = "Start Timer Test";
    public static final int FROM_HANDLE_API = 0x01;

    private static final String TAG = "HandleApiActivity";
    private static final long TIMER_MIN_LENGTH = 1000;
    private static final long TIMER_MAX_LENGTH = 24 * 60 * 60 * 1000;
    private static final String SHARED_PREFS_NAME_SET_ALARM_RINGTONE = "set_alram_ringtone";
    private static final String SHARED_PREFS_NAME_SET_ALARM_RING_NAME = "set_alram_ring_name";
    private static final String ACTION_DISMISS_TIMER = "android.intent.action.DISMISS_TIMER";
    private static final String BLACK_APP_URI = "android-app://ru.yandex.weatherplugin";

    private TimerService mService;
    private int[] mWeekAddNum = {1, 2, 4, 8, 16, 32, 64, 128};

    private TimerServiceConnection mTimerServiceConnection;

    @Override
    @SuppressLint("InlinedApi")
    protected void onCreate(Bundle icicle) {
        try {
            super.onCreate(icicle);
            Intent intent = getIntent();
            if (intent != null) {
                Log.d(TAG, "Intent: " + intent + ", Action: " + intent.getAction());
                if (AlarmClock.ACTION_SET_ALARM.equals(intent.getAction())) {
                    handleSetAlarm(intent);
                } else if (AlarmClock.ACTION_SET_TIMER.equals(intent.getAction())) {
                    handleSetTimer(intent);
                    exitClockExitMode(getApplicationContext());
                } else if (AlarmClock.ACTION_SHOW_ALARM.equals(intent.getAction())) {
                    handleShowAlarms(intent);
                    exitClockExitMode(getApplicationContext());
                } else if (AlarmClock.ACTION_SHOW_TIMER.equals(intent.getAction())) {
                    handleShowTimers(intent);
                } else if (AlarmClock.ACTION_DISMISS_ALARM.equals(intent.getAction())) {
                    handleDismissAlarm(intent);
                } else if (AlarmClock.ACTION_SNOOZE_ALARM.equals(intent.getAction())) {
                    handleSnoozeAlarm(intent);
                } else if (ACTION_DISMISS_TIMER.equals(intent.getAction())) {
                    handleDismissTimer(intent);
                }
            }
        } finally {
            finish();
        }
    }

    private void handleSnoozeAlarm(Intent intent) {
        // not a CTS compulsive test. just show UI, in case alarm is snoozed by others
        intent.setAction(AlarmClock.ACTION_SHOW_ALARM);
        handleShowAlarms(intent);
        voiceNotifySuccess("");
    }

    private void exitClockExitMode(Context context) {
        Intent intent = new Intent();
        intent.setAction(AiSupportContentProvider.ACTION_EXIT_ALARM_EDIT_BY_BREENO);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        context.sendBroadcast(intent);
    }

    private void handleDismissAlarm(Intent intent) {
        // not a CTS compulsive test. just show UI, in case alarm is dismissed by others
        intent.setAction(AlarmClock.ACTION_SHOW_ALARM);
        handleShowAlarms(intent);
        voiceNotifySuccess("");
    }


    private void handleShowTimers(Intent intent) {
        AlarmClock.setCurrentTab(AlarmClock.TAB_INDEX_OPLUSTIME);
        Intent intentTimers = new Intent(this, AlarmClock.class);
        startActivity(intentTimers);
        voiceNotifySuccess("");
    }

    private void handleShowAlarms(Intent intent) {
        String action = intent.getAction();
        Intent intentShowAlarms = new Intent(this, AlarmClockCTS.class);
        if (!TextUtils.isEmpty(action)) {
            intentShowAlarms.setAction(action);
            // add parameter for UI System.
            try {
                String intentComeFrom = intent.getStringExtra(AlarmClock.COME_FROM);
                intentShowAlarms.putExtra(AlarmClock.COME_FROM, intentComeFrom);
            } catch (Exception e) {
                Log.e(TAG, "handleShowAlarms e : " + e.getMessage());
            }

        }
        startActivity(intentShowAlarms);
        voiceNotifySuccess("");
    }

    /***
     * Processes the SET_ALARM intent
     *
     * @param intent
     */
    private void handleSetAlarm(Intent intent) {
        AlarmClock.setCurrentTab(AlarmClock.TAB_INDEX_ALARMCLOCK);
        if (AiAlarmUtils.getAlarmsCount(this) >= AlarmClockFragment.MAX_ALARM_COUNT) {
            Log.d(TAG, "alarm limit max count");
            voiceNotifyAbort(getString(R.string.add_alarm_limit));
            ToastManager.showToast(R.string.add_alarm_limit, Toast.LENGTH_LONG);
            return;
        }

        Bundle bundle = intent.getExtras();
        Log.d(TAG, "bundle: " + bundle);
        int hour = -1;
        int minutes = -1;
        byte dayOfWeek = 0;
        String message = "";
        if (bundle == null) {
            Calendar c = Calendar.getInstance();
            hour = c.HOUR_OF_DAY;
            minutes = c.MINUTE;
            dayOfWeek = 0;
        } else {
            hour = bundle.getInt(AlarmClock.EXTRA_HOUR, -1);
            minutes = bundle.getInt(AlarmClock.EXTRA_MINUTES, 0);
            dayOfWeek = bundle.getByte(AlarmClock.EXTRA_DAYS, (byte) -1);
            message = bundle.getString(AlarmClock.EXTRA_MESSAGE);
        }

        if (TextUtils.isEmpty(message)) {
            message = getString(R.string.default_label);
        }

        try {
            String alert = intent.getStringExtra(EXTRA_RINGTONE);

            int alertType = 1; // vibrate
            if (!android.provider.AlarmClock.VALUE_RINGTONE_SILENT.equals(alert)) {
                alertType = 2; // vibrate & ring
            }
            boolean mShipUi = intent.getBooleanExtra(AlarmClock.EXTRA_SKIP_UI, false);
            Log.d(TAG, "hour: " + hour + " minutes: " + minutes + " message: " + message + " alert: "
                    + alert + " dayOfWeek: " + dayOfWeek + " mShipUi = " + mShipUi);

            if (mShipUi) {
                setAlarmFromIntent(intent, hour, minutes, dayOfWeek, message, alert, alertType);
                voiceNotifySuccess(message);
                return;
            }
            if (hour == -1) {
                Intent setAlarmIntent = new Intent(HandleApiActivity.this, AlarmClock.class);
                setAlarmIntent.putExtras(intent);
                setAlarmIntent.putExtra(IS_FROM_HANDLE_API_KEY, FROM_HANDLE_API);
                startActivity(setAlarmIntent);
            } else {
                if (!interceptBlackUri()) {
                    setAlarmFromIntent(intent, hour, minutes, dayOfWeek, message, alert, alertType);
                }
                Intent enterApk = new Intent(this, AlarmClockCTS.class);
                startActivity(enterApk);
            }
        } catch (Exception e) {
            Log.e(TAG, "handleSetAlarm e : " + e.getMessage());
        }
        voiceNotifySuccess(message);
    }

    /**
     * to resolve bug 2987691
     */
    private boolean interceptBlackUri() {
        try {
            Uri uri = getReferrer();
            Log.d(TAG, "interceptBlackUri:" + uri);
            return (uri != null) && TextUtils.equals(uri.toString(), BLACK_APP_URI);
        } catch (Exception e) {
            Log.e(TAG, "interceptBlackUri e:" + e);
        }
        return false;
    }

    private void voiceNotifySuccess(String message) {
        final VoiceInteractor voiceInteractor = getVoiceInteractor();
        if (voiceInteractor != null) {
            final Prompt prompt = new Prompt(message);
            voiceInteractor.submitRequest(new CompleteVoiceRequest(prompt, null));
        }
    }

    private void voiceNotifyAbort(String message) {
        final VoiceInteractor voiceInteractor = getVoiceInteractor();
        if (voiceInteractor != null) {
            final Prompt prompt = new Prompt(message);
            voiceInteractor.submitRequest(new VoiceInteractor.AbortVoiceRequest(prompt, null));
        }
    }

    private void setAlarmFromIntent(Intent intent, int hour, int minutes, byte dayOfWeek,
                                    String message, String alert, int alertType) {
        int repeatSet = 0;
        if (dayOfWeek == (byte) -1) {
            repeatSet = getDaysFromIntent(intent);
        } else {
            repeatSet = dayOfWeek;
        }

        try {
            // isDelete = 1 : delete alarm in alarm list after use
            // isDelete = 0 : don't delete alarm after use
            int isDelete = intent.getIntExtra(AlarmClock.EXTRA_DELETE_AFTER_USE, -1);
            if (isDelete == -1) {
                if (RepeatSet.isRepeat(repeatSet)) {
                    isDelete = 0;
                } else {
                    isDelete = 1;
                }
            }

            AudioManager mAudioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
            int mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_ALARM);
            String defaultRingUri = alert;
            if (TextUtils.isEmpty(defaultRingUri)) {
                //获取默认铃声地址
                Uri uri = AlarmRingUtils.getDefaultRingtoneUri(this, true);
                if (uri != null) {
                    defaultRingUri = uri.toString();
                } else {
                    defaultRingUri = "";
                }
            }
            Log.i(TAG, "setAlarmFromIntent  alert = " + defaultRingUri);
            String defaultRing = getDefaultRingName();
            if (TextUtils.isEmpty(defaultRing)) {
                RingtoneManager ringtoneManager = new RingtoneManager(this);
                ringtoneManager.setType(RingtoneManager.TYPE_ALARM);
                int index = -1;
                try {
                    index = ringtoneManager.getRingtonePosition(Uri.parse(defaultRingUri));
                } catch (Exception e) {
                    Log.d(TAG, "e: " + e.getMessage());
                }
                if (index > 0) {
                    String title = ringtoneManager.getRingtone(index).getTitle(this);
                    Log.d(TAG, "title = " + title);
                    defaultRing = getRingNameFromOld(this, title);
                }
            }

            Alarm alarm = Alarm.build(true, hour, minutes, repeatSet, alertType, message,
                    TextUtils.isEmpty(defaultRingUri) ? null : Uri.parse(defaultRingUri), defaultRing, mVolume,
                    // 1 will delete the alarm when dismissed
                    isDelete,// if it is a repeat alarm, not delete
                    AlarmRingUtils.getDefaultVibrate(getApplicationContext()),
                    0, 0);

            boolean success = AlarmUtils.addNewAlarm(this, alarm, true) > 0;
            if (success) {
                AlarmPreferenceUtils.Companion.getInstance().addAlarmInfo(System.currentTimeMillis(), alarm);
                AlarmUtils.updateAlarmNextTime(this);
                AlarmUtils.popAlarmSetToast(this, alarm);
            }
        } catch (Exception e) {
            Log.e(TAG, "setAlarmFromIntent e : " + e.getMessage());
        }
    }

    private void handleSetTimer(Intent intent) {
        // final SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        // If no length is supplied , show the timer setup view
        if (!intent.hasExtra(AlarmClock.EXTRA_LENGTH)) {
            startActivity(new Intent(this, AlarmClockCTS.class)
                    .setAction(AlarmClock.ACTION_CTS_START_TIMER));
            return;
        }

        try {
            final long length = (long) 1000 * intent.getIntExtra(AlarmClock.EXTRA_LENGTH, 0);
            if ((length < TIMER_MIN_LENGTH) || (length > TIMER_MAX_LENGTH)) {
                Log.i(TAG, "Invalid timer length requested: " + length);
                return;
            }

            boolean skipUi = intent.getBooleanExtra(AlarmClock.EXTRA_SKIP_UI, false);

            TimerClass timerClass = new TimerClass();
            timerClass.mSecond = length;
            Intent timeIntent = new Intent(this, TimerService.class);
            if (mTimerServiceConnection == null) {
                mTimerServiceConnection = new TimerServiceConnection(timerClass, skipUi);
            }
            bindService(timeIntent, mTimerServiceConnection, Service.BIND_AUTO_CREATE);
            startService(timeIntent);
        } catch (Exception e) {
            Log.e(TAG, "handleSetTimer e : " + e.getMessage());
        }
    }

    @Override
    protected void onDestroy() {
        try {
            if (mTimerServiceConnection != null) {
                unbindService(mTimerServiceConnection);
            }
        } catch (Exception e) {
            Log.e(TAG, "onDestroy error: " + e.getMessage());
        }
        super.onDestroy();
    }

    private int getDaysFromIntent(Intent intent) {
        int repeatSet = 0;
        try {
            final ArrayList<Integer> days = intent.getIntegerArrayListExtra(EXTRA_DAYS);
            if (days != null) {
                final int[] daysArray = new int[days.size()];
                int sum = 0;
                for (int i = 0; i < days.size(); i++) {
                    daysArray[i] = days.get(i);
                    Log.d(TAG, "" + i + ": " + daysArray[i]);
                    if (daysArray[i] - 2 >= 0) {
                        sum = sum + mWeekAddNum[daysArray[i] - 2];
                    } else {
                        sum = sum + mWeekAddNum[daysArray[i] + 5];
                    }

                }
                repeatSet = sum;
            } else {
                // API says to use an ArrayList<Integer> but we allow the user to use a int[] too.
                final int[] daysArray = intent.getIntArrayExtra(EXTRA_DAYS);
                if (daysArray != null) {
                    // repeatSet.setRepeat(true, daysArray);
                } else {
                    repeatSet = 0;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getDaysFromIntent e : " + e.getMessage());
        }
        Log.d(TAG, "repeatSet = " + repeatSet);
        return repeatSet;
    }

    private String getDefaultRingUri() {
        return PrefUtils.getString(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, SHARED_PREFS_NAME_SET_ALARM_RINGTONE, null);
    }

    private String getDefaultRingName() {
        return PrefUtils.getString(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, SHARED_PREFS_NAME_SET_ALARM_RING_NAME, null);
    }

    //------------AndroidP CTS-------------//
    private void handleDismissTimer(Intent intent) {
        final Uri dataUri = intent.getData();
        if (dataUri != null) {
            int timerId = -1;
            try {
                timerId = (int) ContentUris.parseId(dataUri);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }

            if (timerId > 0) {
                resetOrDeleteTimer(timerId);
                voiceNotifySuccess("Success!");
                Log.i("Timer dismissed: " + timerId);
            } else {
                voiceNotifySuccess("Invalid Timers!");
                Log.e("Could not dismiss timer: invalid URI");
            }
        } else {
            voiceNotifySuccess("Success!");
        }
    }

    //TODO: Finish this later.
    private void resetOrDeleteTimer(int timerId) {

    }
    //----------------end-----------------//

    private static class TimerClass {
        long mSecond;
        int mIndex;
    }

    private class TimerServiceConnection implements ServiceConnection {
        private TimerClass mTimerClass;
        private boolean mIsSkipUi;

        TimerServiceConnection(TimerClass ts, boolean skipUi) {
            mTimerClass = ts;
            this.mIsSkipUi = skipUi;
        }

        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "onServiceConnected Bind successfully, mIsSkipUi: " + mIsSkipUi);
            TimerBinder mIBinder = (TimerBinder) service;
            mService = mIBinder.getService();
            AlarmClock.setCurrentTab(AlarmClock.TAB_INDEX_OPLUSTIME);
            if (mService != null) {
                if (mIsSkipUi) {
                    //cts start without UI
                    boolean isStart = mService.isStart(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
                    boolean isPause = mService.isPause(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
                    Log.d(TAG, "newCountdown startTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);
                    TimerAlertReceiver.sendMessageToCloseFullScreenAlert(mService);
                    if (isStart || isPause) {
                        mService.stopTimer(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
                    }
                    mService.setCtsTimerName(CTS_TIMER_NAME, AiSupportContentProvider.DEFAULT_TIMER_INDEX);
                    mService.setTotalTime(mTimerClass.mSecond, mTimerClass.mSecond, mTimerClass.mIndex);
                    mService.setTimerId(AiSupportContentProvider.DEFAULT_TIMER_INDEX, AiSupportContentProvider.AI_TIMER_ID);
                    mService.startTimer(mTimerClass.mIndex);

                } else {
                    //cts has UI start
                    AiSupportContentProvider.sAiStartTimerMark = true;
                    if (mService.isStart(0) || mService.isPause(0)) {
                        replaceCountdown(mTimerClass.mSecond);
                    } else {
                        newCountdown(mService, mTimerClass.mSecond);
                    }
                }
                recordTimerNeedToAlarm(true);
            }
        }


        public void onServiceDisconnected(ComponentName name) {
            Log.i(TAG, "onServiceDisconnected");
        }
    }

    public void recordTimerNeedToAlarm(boolean status) {
        PrefUtils.putBoolean(this, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_NEED_TO_ALARM_PREFERENCE, status);
    }

    private void replaceCountdown(long seconds) {
        Log.d(TAG, "setAndStartTimer");
        seconds = seconds / TimerController.ONE_SECOND;
        Intent intent = new Intent(HandleApiActivity.this, AlarmClock.class);
        intent.setAction(AlarmClock.HANDLE_API__SET_TIMER);
        intent.putExtra(AlarmClock.EXTRA_TIMER_START, true);
        intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, seconds);
        intent.putExtra(AlarmClock.EXTRA_TIMER_SPARE_SECONDS, seconds);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        HandleApiActivity.this.startActivity(intent);
    }


    private void newCountdown(TimerService timerService, long seconds) {
        if (timerService != null) {
            seconds = seconds / TimerController.ONE_SECOND;
            Log.d(TAG, "newCountdown seconds" + seconds);
            boolean isStart = timerService.isStart(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
            boolean isPause = timerService.isPause(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
            TimerAlertReceiver.sendMessageToCloseFullScreenAlert(mService);
            Log.d(TAG, "newCountdown startTimer: Timer 0 state: isStart: " + isStart + ", isPause: " + isPause);
            if (isStart || isPause) {
                timerService.stopTimer(AiSupportContentProvider.DEFAULT_TIMER_INDEX);
                mService.setTotalTime(seconds, seconds, AiSupportContentProvider.DEFAULT_TIMER_INDEX);
            }
            timerService.setCtsTimerName(CTS_TIMER_NAME, AiSupportContentProvider.DEFAULT_TIMER_INDEX);
            Intent intent = new Intent(HandleApiActivity.this, AlarmClock.class);
            intent.setAction(AlarmClock.ACTION_AI_SET_TIMER);
            intent.putExtra(AlarmClock.EXTRA_TIMER_START, true);
            intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, seconds);
            intent.putExtra(AlarmClock.EXTRA_TIMER_SPARE_SECONDS, seconds);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            HandleApiActivity.this.startActivity(intent);
        }
    }
}
