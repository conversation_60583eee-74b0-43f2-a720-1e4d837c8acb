/***********************************************************
** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.oplus.alarmclock.cts;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClock;

import java.util.List;

public class AlarmClockCTS extends AlarmClock {

    private static final String TAG = "AlarmClockCTS";
    private boolean mFirstOnResume = true;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        onStatusBarClick(getIntent(), "onCreate");
        Intent intent = new Intent(this, AlarmClock.class);
        ComponentName cmpName = intent.resolveActivity(getPackageManager());
        if (cmpName != null) {
            ActivityManager am = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> runningTasks = am.getRunningTasks(10);
            if ((runningTasks != null) && !runningTasks.isEmpty()) {
                for (ActivityManager.RunningTaskInfo runningTask : runningTasks) {
                    if (runningTask.baseActivity.equals(cmpName)) {
                        finish();
                        break;
                    }
                }
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mFirstOnResume) {
            mFirstOnResume = false;
            onStatusBarClick(getIntent(), "onResume");
        }
    }

    @Override
    @SuppressLint("CommitTransaction")
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        onStatusBarClick(intent, "onNewIntent");
        String action = intent.getAction();
        if ((action != null) && (action.endsWith(ACTION_START_BY_NOTIFICATION))) {
            sCurrentTab = TAB_INDEX_ALARMCLOCK;
        }
    }

    private void onStatusBarClick(Intent intent, String tag) {
        String action = intent.getAction();
        if ((action != null) && (action.endsWith(AlarmClock.ACTION_CTS_START_TIMER))) {
            sCurrentTab = TAB_INDEX_OPLUSTIME;
        } else if ((action != null) && (action.endsWith(AlarmClock.ACTION_SHOW_ALARM))) {
            sCurrentTab = TAB_INDEX_ALARMCLOCK;
        } else if ((action != null) && (action.endsWith(AlarmClock.ACTION_SHOW_TIMER))) {
            sCurrentTab = TAB_INDEX_OPLUSTIME;
        }
        Log.i(TAG, "[" +  tag + "] Intent: " + intent +  ",  Action: " + action
                + ", sCurrentTab: " + sCurrentTab);
        setCurrentPage();
    }
}
