/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - UIConfigMonitor.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/7/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/7/12     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.base

import android.content.res.Configuration
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.ArraySet
import androidx.activity.ComponentActivity
import androidx.annotation.MainThread
import androidx.annotation.RequiresApi
import androidx.lifecycle.*
import com.oplus.alarmclock.BaseActivity
import com.oplus.clock.common.utils.Log
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import java.lang.ref.WeakReference

class UIConfigMonitor {

    companion object {
        private const val TAG = "UIConfigMonitor"
        private const val DELAY_NOTIFY_TIME = 100L
        val instance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            UIConfigMonitor()
        }
    }

    interface OnUIConfigChangeListener {
        fun onUIConfigChanged(configList: MutableCollection<IUIConfig>)
    }

    private val mHandler: Handler by lazy { Handler(Looper.getMainLooper()) }
    private var mNotifyChangeRunnable: Runnable? = null
    private var mResponsiveUIConfig: ResponsiveUIConfig? = null
    private var mMonitorActivityCount: Int = 0
    private var mChangeListenerSet: ArraySet<OnUIConfigChangeListener>? = null
    private val mChangeConfigMap = hashMapOf<Class<IUIConfig>, IUIConfig>()

    @MainThread
    fun attachActivity(activity: BaseActivity) {

        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get()
        if (activityReference == null) {
            Log.w(TAG, "activity memory has been recycled and cannot be attached")
            return
        }
        if (mResponsiveUIConfig == null) {
            mResponsiveUIConfig = ResponsiveUIConfig.getDefault(activityReference)
        }
        mMonitorActivityCount++
        activityReference.lifecycle.addObserver(object : LifecycleObserver {

            @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
            fun onCreate() {
                observeResponsiveUIConfig(activityReference)
                if (activityReference is OnUIConfigChangeListener) {
                    addOnUIConfigChangeListener(activityReference)
                }
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                observeResponsiveUIConfig(activityReference, false)
                if (activityReference is OnUIConfigChangeListener) {
                    removeOnUIConfigChangeListener(activityReference)
                }
                mMonitorActivityCount--
                Log.d(TAG, "onDestroy mMonitorActivityCount: $mMonitorActivityCount")
                if (mMonitorActivityCount == 0) {
                    recycle()
                }
            }
        })
    }

    fun onActivityConfigChanged(config: Configuration) {
        mResponsiveUIConfig?.onActivityConfigChanged(config)
    }

    @MainThread
    fun addOnUIConfigChangeListener(listener: OnUIConfigChangeListener) {
        if (mChangeListenerSet == null) {
            mChangeListenerSet = ArraySet()
        }
        mChangeListenerSet!!.add(listener)
    }

    @MainThread
    fun removeOnUIConfigChangeListener(listener: OnUIConfigChangeListener) {
        mChangeListenerSet?.remove(listener)
    }

    private fun observeResponsiveUIConfig(activity: ComponentActivity, observe: Boolean = true) {
        mResponsiveUIConfig?.apply {
            if (observe) {
                uiStatus.observe(activity, object : UIConfigObserver<UIConfig.Status>() {
                    @RequiresApi(Build.VERSION_CODES.Q)
                    override fun onConfigChanged(value: UIConfig.Status) {
                        Log.d(TAG, "uiStatus observe $value")
                        notifyConfigChanged(FoldScreenConfig(value))
                    }
                })
                uiOrientation.observe(activity, object : UIConfigObserver<Int>() {
                    @RequiresApi(Build.VERSION_CODES.Q)
                    override fun onConfigChanged(value: Int) {
                        Log.d(TAG, "uiOrientation observe $value")
                        notifyConfigChanged(ScreenOrientationConfig(value))
                    }
                })
            } else {
                uiStatus.removeObservers(activity)
                uiOrientation.removeObservers(activity)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun notifyConfigChanged(config: IUIConfig) {
        Log.d(TAG, "notifyConfigChanged, $config")
        synchronized(mChangeConfigMap) {
            Log.d(TAG, "notifyConfigChanged, put in: $config")
            mChangeConfigMap.put(config.javaClass, config)
        }

        if (mNotifyChangeRunnable == null) {
            mNotifyChangeRunnable = Runnable {
                synchronized(mChangeConfigMap) {
                    Log.d(
                        TAG,
                        "notifyConfigChanged, start notify, configSize=${mChangeConfigMap.size}"
                    )
                    if (mChangeConfigMap.isNotEmpty()) {
                        val configArray = mChangeConfigMap.values
                        mChangeListenerSet?.forEach {
                            it.onUIConfigChanged(configArray)
                        }
                        mChangeConfigMap.clear()
                    }
                }
            }
        } else if (mHandler.hasCallbacks(mNotifyChangeRunnable!!)) {
            Log.d(TAG, "notifyConfigChanged, ignore by callback exist")
            return
        }
        Log.d(TAG, "notifyConfigChanged, post runnable: $config")
        mHandler.postDelayed(mNotifyChangeRunnable!!, DELAY_NOTIFY_TIME)
    }

    private fun recycle() {
        mResponsiveUIConfig = null
        mChangeListenerSet?.clear()
        mChangeListenerSet = null
        mHandler.removeCallbacksAndMessages(null)
        mChangeConfigMap.clear()
    }

    //去除监听时首次的回调
    private abstract class UIConfigObserver<T> : Observer<T> {
        private var mInit = true

        final override fun onChanged(t: T) {
            // We not care the change in the first time, because it is the init state
            if (mInit) {
                mInit = false
            } else if (t != null) {
                onConfigChanged(t)
            }
        }

        abstract fun onConfigChanged(value: T)
    }
}