/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseUiModeFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.oplus.alarmclock.BaseFragment
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.clock.common.utils.Log

abstract class BaseUiModeFragment<T : ViewDataBinding> : BaseFragment() {

    companion object {
        const val TAG = "BaseScreenFragment"
    }

    protected var mViewBinding: T? = null

    var uiMode: UiMode = UiMode.NORMAL

    override fun onCreateView(inflater: LayoutInflater, group: ViewGroup?, bundle: Bundle?): View? {
        initViewBinding(inflater, group)
        initView(inflater, group)
        initData()
        return mViewBinding?.root
    }

    private fun initViewBinding(inflater: LayoutInflater, container: ViewGroup?) {
        layoutId().let {
            if (it > 0) {
                mViewBinding = DataBindingUtil.inflate<T>(inflater, it, container, attachToParent())
            }
        }
    }

    open fun onScreenOrientationChanged(orientation: Int) {
        context?.let {
            uiMode = FoldScreenUtils.uiMode(it, isInMultiWindowMode)
            Log.d(TAG, "onScreenOrientationChanged uiMode:$uiMode")
        }
    }

    open fun onHoverPostureChanged(hover: Boolean) {}

    /**
     * layout id
     * @return layout
     */
    @LayoutRes
    protected open fun layoutId(): Int {
        return -1
    }

    @Suppress("FunctionOnlyReturningConstant")
    protected fun attachToParent(): Boolean {
        return false
    }

    protected fun getRootView(): View? {
        return mViewBinding?.root
    }

    protected open fun initView(inflater: LayoutInflater, group: ViewGroup?) {}

    protected open fun initData() {}
}