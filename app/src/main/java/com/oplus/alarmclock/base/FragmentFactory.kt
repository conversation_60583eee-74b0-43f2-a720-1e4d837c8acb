/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - FragmentFactory.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/21
 ** Author: Zhao<PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/3/21     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.base

import android.content.Context
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.BaseActivity
import com.oplus.alarmclock.BaseFragment
import com.oplus.alarmclock.alarmclock.AlarmClockFragment
import com.oplus.alarmclock.globalclock.WorldClockLargeFragment
import com.oplus.alarmclock.globalclock.WorldClockNormalFragment
import com.oplus.alarmclock.globalclock.WorldClockMidFragment
import com.oplus.alarmclock.globalclock.WorldClockSmallFragment
import com.oplus.alarmclock.stopwatch.StopWatchLargeFragment
import com.oplus.alarmclock.stopwatch.StopWatchMidFragment
import com.oplus.alarmclock.stopwatch.StopWatchNormalFragment
import com.oplus.alarmclock.stopwatch.StopWatchSmallFragment
import com.oplus.alarmclock.timer.TimerLargeFragment
import com.oplus.alarmclock.timer.TimerMidFragment
import com.oplus.alarmclock.timer.TimerNormalFragment
import com.oplus.alarmclock.timer.TimerSmallFragment
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.FoldScreenUtils.UiMode
import com.oplus.clock.common.utils.Log

object FragmentFactory {

    private const val TAG = "FragmentFactory"

    @JvmStatic
    fun create(context: Context, type: Int): BaseFragment? {
        var uiMode = (context as? BaseActivity)?.obtainUiMode() ?: UiMode.NORMAL
        Log.d(TAG, "create fragment uiMode: $uiMode, type: $type")
        if (uiMode == UiMode.SMALL && FoldScreenUtils.isFlexibleScenario(context)) {
            //浮窗状态下无SMALL模式，则使用NORMAL模式
            uiMode = UiMode.NORMAL
        }
        return when (type) {
            AlarmClock.TAB_INDEX_ALARMCLOCK -> AlarmClockFragment()
            AlarmClock.TAB_INDEX_GLOBALCITY -> {
                when (uiMode) {
                    UiMode.SMALL -> WorldClockSmallFragment()
                    UiMode.NORMAL -> WorldClockNormalFragment()
                    UiMode.MIDDLE -> WorldClockMidFragment()
                    UiMode.LARGE_VERTICAL -> WorldClockLargeFragment()
                    UiMode.LARGE_HORIZONTAL -> WorldClockLargeFragment()
                }
            }

            AlarmClock.TAB_INDEX_STOPWATCH -> {
                when (uiMode) {
                    UiMode.SMALL -> StopWatchSmallFragment()
                    UiMode.NORMAL -> StopWatchNormalFragment()
                    UiMode.MIDDLE -> StopWatchMidFragment()
                    UiMode.LARGE_VERTICAL -> StopWatchLargeFragment()
                    UiMode.LARGE_HORIZONTAL -> StopWatchLargeFragment()
                }
            }

            AlarmClock.TAB_INDEX_OPLUSTIME -> {
                when (uiMode) {
                    UiMode.SMALL -> TimerSmallFragment()
                    UiMode.NORMAL -> TimerNormalFragment()
                    UiMode.MIDDLE -> TimerMidFragment()
                    UiMode.LARGE_VERTICAL -> TimerLargeFragment()
                    UiMode.LARGE_HORIZONTAL -> TimerLargeFragment()
                }
            }

            else -> {
                Log.e(TAG, "create fragment failed, invalid class: $type")
                null
            }
        }?.apply { this.uiMode = uiMode }
    }
}