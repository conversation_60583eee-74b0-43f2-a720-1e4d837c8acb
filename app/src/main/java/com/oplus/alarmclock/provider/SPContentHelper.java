package com.oplus.alarmclock.provider;


import static com.oplus.alarmclock.provider.SPContentProvider.SEPARATOR;
import static com.oplus.alarmclock.provider.SPContentProvider.CONTENT_URI;
import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_NAME;
import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_TYPE;
import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_VALUE;
import static com.oplus.alarmclock.utils.ClockConstant.NULL_STRING;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_BOOLEAN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_CLEAN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_CONTAIN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_FLOAT;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_GET_ALL;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_INT;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_LONG;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_STRING;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_STRING_SET;
import static com.oplus.alarmclock.utils.ClockConstant.VALUE;

import android.annotation.TargetApi;
import android.app.Application;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;

import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class SPContentHelper {
    private static final String COMMA_REPLACEMENT = "__COMMA__";
    private static final Long VALUE_DEFAULT_ID = 1L;
    private static Context context;
    private static final String TAG = "SPContentHelper";

    private static boolean checkContext() {
        if (context == null) {
            Log.e("context has not been initialed");
            return false;
        }
        return true;
    }

    public static void init(Application application) {
        context = Utils.getDeviceContext(application);
    }

    public synchronized static void save(String name, Boolean t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_BOOLEAN + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                cv.put(VALUE, t);
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name  Boolean :" + e.getMessage());
            }
        }
    }

    public synchronized static void save(String name, String t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                cv.put(VALUE, t);
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name  String :" + e.getMessage());
            }
        }
    }

    public synchronized static void save(String name, Integer t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_INT + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                cv.put(VALUE, t);
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name  Integer :" + e.getMessage());
            }
        }
    }

    public synchronized static void save(String name, Long t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_LONG + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                cv.put(VALUE, t);
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name Long :" + e.getMessage());
            }
        }
    }

    public synchronized static void save(String name, Float t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_BOOLEAN + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                cv.put(VALUE, t);
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name Float :" + e.getMessage());
            }
        }
    }

    public synchronized static void save(String name, Set<String> t) {
        if (checkContext()) {
            try {
                ContentResolver cr = context.getContentResolver();
                Uri uri = ContentUris.withAppendedId(Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING_SET + SEPARATOR + name), VALUE_DEFAULT_ID);
                ContentValues cv = new ContentValues();
                Set<String> convert = new HashSet<>();
                for (String string : t) {
                    convert.add(string.replace(",", COMMA_REPLACEMENT));
                }
                cv.put(VALUE, convert.toString());
                cr.update(uri, cv, null, null);
            } catch (Exception e) {
                Log.d(TAG, "save name Set string :" + e.getMessage());
            }
        }
    }

    public static String getString(String name, String defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            return rtn;
        } else {
            return defaultValue;
        }
    }

    public static int getInt(String name, int defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_INT + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            return Integer.parseInt(rtn);
        } else {
            return defaultValue;
        }
    }

    public static float getFloat(String name, float defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_FLOAT + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            return Float.parseFloat(rtn);
        } else {
            return defaultValue;
        }
    }

    public static boolean getBoolean(String name, boolean defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_BOOLEAN + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            return Boolean.parseBoolean(rtn);
        } else {
            return defaultValue;
        }
    }

    public static long getLong(String name, long defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_LONG + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            return Long.parseLong(rtn);
        } else {
            return defaultValue;
        }
    }

    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
    public static Set<String> getStringSet(String name, Set<String> defaultValue) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING_SET + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return defaultValue;
            }
            if (!rtn.matches("\\[.*\\]")) {
                return defaultValue;
            }
            String sub = rtn.substring(1, rtn.length() - 1);
            String[] spl = sub.split(", ");
            Set<String> returns = new HashSet<>();
            for (String t : spl) {
                returns.add(t.replace(COMMA_REPLACEMENT, ", "));
            }
            return returns;
        } else {
            return defaultValue;
        }
    }

    public static boolean contains(String name) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_CONTAIN + SEPARATOR + name);
            String rtn = cr.getType(uri);
            if (rtn == null || rtn.equals(NULL_STRING)) {
                return false;
            } else {
                return Boolean.parseBoolean(rtn);
            }
        } else {
            return false;
        }
    }

    public static void remove(String name) {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_LONG + SEPARATOR + name);
            cr.delete(uri, null, null);
        }
    }

    public static void clear() {
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_CLEAN);
            cr.delete(uri, null, null);
        }
    }

    public static Map<String, ?> getAll() {
        Map<String, Object> resultMap = new HashMap<>();
        if (checkContext()) {
            ContentResolver cr = context.getContentResolver();
            Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_GET_ALL);
            Cursor cursor = cr.query(uri, null, null, null, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    int nameIndex = cursor.getColumnIndex(CURSOR_COLUMN_NAME);
                    int typeIndex = cursor.getColumnIndex(CURSOR_COLUMN_TYPE);
                    int valueIndex = cursor.getColumnIndex(CURSOR_COLUMN_VALUE);
                    do {
                        String key = cursor.getString(nameIndex);
                        String type = cursor.getString(typeIndex);
                        Object value = null;
                        if (type.equalsIgnoreCase(TYPE_STRING)) {
                            value = cursor.getString(valueIndex);
                            if (((String) value).contains(COMMA_REPLACEMENT)) {
                                String str = (String) value;
                                if (str.matches("\\[.*\\]")) {
                                    String sub = str.substring(1, str.length() - 1);
                                    String[] spl = sub.split(", ");
                                    Set<String> returns = new HashSet<>();
                                    for (String t : spl) {
                                        returns.add(t.replace(COMMA_REPLACEMENT, ", "));
                                    }
                                    value = returns;
                                }
                            }
                        } else if (type.equalsIgnoreCase(TYPE_BOOLEAN)) {
                            value = cursor.getString(valueIndex);
                        } else if (type.equalsIgnoreCase(TYPE_INT)) {
                            value = cursor.getInt(valueIndex);
                        } else if (type.equalsIgnoreCase(TYPE_LONG)) {
                            value = cursor.getLong(valueIndex);
                        } else if (type.equalsIgnoreCase(TYPE_FLOAT)) {
                            value = cursor.getFloat(valueIndex);
                        } else if (type.equalsIgnoreCase(TYPE_STRING_SET)) {
                            value = cursor.getString(valueIndex);
                        }
                        resultMap.put(key, value);
                    }
                    while (cursor.moveToNext());
                }
                cursor.close();
            }
        }
        return resultMap;
    }
}

