/*******************************************************
 * Copyright 2008 - 2018 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description  :
 * History      :
 * (ID, Date, Author, Description)
 * 2018.04.19   liukun build
 *******************************************************/
package com.oplus.alarmclock.provider;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.globalclock.CityUtils;
import com.oplus.alarmclock.globalclock.DefaultCityConfig;
import com.oplus.alarmclock.globalclock.romupdate.CitiesDatabaseCreator;
import com.oplus.alarmclock.globalclock.romupdate.CitiesRomupdate;
import com.oplus.alarmclock.globalclock.romupdate.LegacySupport;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.Utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CityDatabaseHelper extends SQLiteOpenHelper {

    // Database name
    public static final String DATABASE_NAME = "cities.db";
    /**
     * add new cities table.
     */
    public static final int DB_VERSION = 1037;
    // FBE: /data/user_de/0/com.oplus.alarmclock/databases
    // NONE-FBE: /data/data/com.oplus.alarmclock/databases
    public static String sDbDIR;
    public static String sDbPATH;
    private static final String TAG = "CityDatabaseHelper";
    private static final boolean DB_CHECK_DEBUG = false;

    // If new cities database need recreate, set this to true.
    private static final boolean CREATE_CITIES_DATABASE = false;

    private static final int BUFFER_SIZE = 4096 * 2;    //8K.

    private static final String SQL_UPDATE =
            "UPDATE " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " SET "
                    + ClockContract.City.FLAG + " = ?, "
                    + ClockContract.City.FLAG2 + "= ?, "
                    + ClockContract.City.POS + " = ?, "
                    + ClockContract.City.SORT_ORDER + "= ? "
                    + " WHERE "
                    + ClockContract.City.CITY_ID + " = ?";

    private static final String SQL_INIT_DEFAULT_CITY =
            "UPDATE " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " SET "
                    + ClockContract.City.FLAG + "=1, "
                    + ClockContract.City.SORT_ORDER + "=0 "
                    + " WHERE "
                    + ClockContract.City.CITY_ID + "=?";

    private static final String SQL_HIDE_CITY_CHINA_CITY =
            "UPDATE " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " SET "
                    + ClockContract.City.FLAG2 + "=1 "    //1: hide
                    + " WHERE " + CityUtils.SQL_SELECTION_ALL_CHINA_CITY_DOMESTIC;

    private static final String SQL_HIDE_CITY_CHINA_CITY_EX =
            "UPDATE " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " SET "
                    + ClockContract.City.FLAG2 + "=1 "   //1: hide
                    + " WHERE " + CityUtils.SQL_SELECTION_ALL_CHINA_CITY_EXP;

    /*Hebrew (language) locale */
    private static final String LOCALE_SR_LATN_RS = "sr_RS_#Latn";

    private static final String SQL_CHECK_QUERY =
            "SELECT " + ClockContract.City._ID + ", "
                    + ClockContract.City.CITY_ID + ", "
                    + ClockContract.City.CITY_NAME + ", "
                    + ClockContract.City.FIRST_LETTER + ", "
                    + ClockContract.City.CITY_INDEX + ", "
                    + ClockContract.City.TIMEZONE_ID + ", "
                    + ClockContract.City.RAW_OFFSET + ", "
                    + ClockContract.City.LATITUDE + ", "
                    + ClockContract.City.LONGITUDE + ", "
                    + ClockContract.City.FIRST_SPELL + ", "
                    + ClockContract.City.FULL_SPELL + ", "
                    + ClockContract.City.FLAG + ", "
                    + ClockContract.City.FLAG2 + ", "
                    + ClockContract.City.SORT_ORDER + ", "
                    + ClockContract.City.POS + ", "
                    + ClockContract.City.LOCALE_LAN + ", "
                    + ClockContract.City.REGION + ", "
                    + ClockContract.City.CONTINENT + ", "
                    + ClockContract.City.CITY_COUNTRY
                    + " FROM " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " WHERE " + ClockContract.City.FLAG2 + "=0"
                    + " AND " + ClockContract.City.LOCALE_LAN + "='" + LOCALE_SR_LATN_RS + "'";

    private static final String SQL_QUERY_SELECTED_CITY =
            "SELECT " + ClockContract.City._ID + ", "
                    + ClockContract.City.CITY_ID + ", "
                    + ClockContract.City.SORT_ORDER + " "
                    + " FROM " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                    + " WHERE " + ClockContract.City.FLAG + "=1"
                    + " AND " + ClockContract.City.LOCALE_LAN + "='" + ClockContract.City.EN_NAME + "'";

    private Context mContext;
    private SQLiteDatabase mClockDatabase;

    private static void setDbDir(String dbDir) {
        sDbDIR = dbDir;
    }

    CityDatabaseHelper(Context context, SQLiteDatabase clockDatabase) {
        super(Utils.getDeviceContext(context), DATABASE_NAME, null, DB_VERSION);
        mContext = Utils.getDeviceContext(context);
        mClockDatabase = clockDatabase;
        setDbDir(mClockDatabase.getPath().replace(ClockDatabaseHelper.DATABASE_NAME, ""));
        sDbPATH = sDbDIR + DATABASE_NAME;
        Log.d(TAG, "CityDatabaseHelper: clock db file path: "
                + mClockDatabase.getPath() + ", city: " + sDbPATH);
        synchronized (CityDatabaseHelper.class) {
            initDefaultDatabase();
            CitiesRomupdate.prepareDatabase(context);
        }
        if (CREATE_CITIES_DATABASE) {
            CitiesDatabaseCreator.createCitiesDatabase(context);
        }
    }

    private void initDefaultDatabase() {
        File file = new File(sDbPATH);
        if (!file.exists()) {
            Log.d(TAG, "initDefaultDatabase: Db file not exist, create it.");
            boolean success = init();
            //Try again if failed.
            if (!success) {
                Log.d(TAG, "initDefaultDatabase: init failed, try again.");
                init();
            }
        }

        //Check the db file, if failed try init again.
        if (!checkDbFile() || isUpgrade(mContext)) {
            ArrayList<City> selectedCityList = getSelectedCity();
            Log.w(TAG, "initDefaultDatabase: Db file error, recreate the db.");
            deleteOldCityDb();
            boolean success = init();

            boolean isInitSelected = (selectedCityList != null) && (selectedCityList.size() > 0);
            if (success) {
                Log.d(TAG, "initDefaultDatabase: Db file error, recreate success, init selected city.");
                if (isInitSelected) {
                    initSelectedCity(selectedCityList);
                } else {
                    CityUtils.addDefaultCity(mContext);
                }
            } else {
                Log.e(TAG, "initDefaultDatabase: Db file error, cannot recreate success,"
                        + " check if there is something wrong with the system!");
            }
        }
    }

    private void updateDbVersion(Context context, int newVersion) {
        Log.i(TAG, "updateDbVersion newVersion:" + newVersion);
        PrefUtils.putInt(context, PrefUtils.CITY_DB_VERSION, PrefUtils.CITY_DB_VERSION_KEY, newVersion);
    }

    private int getOldVersion(Context context) {
        return PrefUtils.getInt(context, PrefUtils.CITY_DB_VERSION, PrefUtils.CITY_DB_VERSION_KEY, 0);
    }

    private boolean isUpgrade(Context context) {

        int oldVersion = getOldVersion(context);
        Log.i(TAG, "isUpgrade oldVersion:" + oldVersion + ",newVersion:" + DB_VERSION);
        return oldVersion != DB_VERSION;
    }

    private void deleteOldCityDb() {
        String shm = sDbPATH + "-shm";
        String wal = sDbPATH + "-wal";
        CitiesRomupdate.deleteFile(sDbPATH);
        CitiesRomupdate.deleteFile(shm);
        CitiesRomupdate.deleteFile(wal);
    }

    private void initDefaultCity(Context context) {
        SQLiteDatabase db = null;
        try {
            db = getWritableDatabase();
            if (db != null) {
                if (DeviceUtils.isExpVersion(context)) {
                    db.execSQL(SQL_HIDE_CITY_CHINA_CITY);
                } else {
                    db.execSQL(SQL_HIDE_CITY_CHINA_CITY_EX);
                }
                if (!PrefUtils.hasInitDefaultCity(context)) {
                    String cityId = DefaultCityConfig.getLocationCity(context);
                    Log.i(TAG, "initDefaultCity: " + cityId);
                    if (cityId != null) {
                        String[] params = new String[1];
                        params[0] = cityId;
                        db.execSQL(SQL_INIT_DEFAULT_CITY, params);
                        PrefUtils.setDefaultCityInitMark(context);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "initDefaultCity error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (db != null) {
                db.close();
            }
        }
    }

    @SuppressLint("Range")
    private ArrayList<City> getSelectedCity() {
        Log.d(TAG, "getSelectedCity start");
        Cursor cursor = null;
        SQLiteDatabase db = null;
        ArrayList<City> selectCityList = new ArrayList<>();
        try {
            db = SQLiteDatabase.openDatabase(
                    sDbPATH, null, SQLiteDatabase.OPEN_READONLY);
            cursor = db.rawQuery(SQL_QUERY_SELECTED_CITY, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    City city = new City();
                    city.setCityId(cursor.getInt(cursor.getColumnIndex(ClockContract.City.CITY_ID)));
                    city.setSortPos(cursor.getInt(cursor.getColumnIndex(ClockContract.City.SORT_ORDER)));
                    if (DeviceUtils.isExpVersion(mContext)) {
                        HashMap<Integer, Integer> changedCityNamePairExp = CityUtils.getChangedCityNamePairExp();
                        if (changedCityNamePairExp.containsKey(city.getCityId())) {
                            city.setCityId(changedCityNamePairExp.get(city.getCityId()));
                        }
                    }
                    Log.d(TAG, "getSelectedCity add city : " + city.getCityId());
                    selectCityList.add(city);
                }
            }
        } catch (SQLException e) {
            Log.d(TAG, "getSelectedCity error " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
        return selectCityList;
    }

    private void initSelectedCity(ArrayList<City> selectCityList) {
        Log.d(TAG, "initSelectedCity start");
        if ((selectCityList == null) || (selectCityList.size() == 0)) {
            return;
        }
        SQLiteDatabase db = null;
        try {
            db = getWritableDatabase();
            if (db != null) {
                for (int i = 0; i < selectCityList.size(); i++) {
                    int cityId = selectCityList.get(i).getCityId();
                    String sqlInitSelectedCity =
                            "UPDATE " + ClockDatabaseHelper.NEW_CITIES_TABLE_NAME
                                    + " SET "
                                    + ClockContract.City.FLAG + "=1, "
                                    + ClockContract.City.SORT_ORDER + " = " + selectCityList.get(i).getSortPos()
                                    + " WHERE "
                                    + ClockContract.City.CITY_ID + " = " + cityId;
                    Log.d(TAG, "initSelectedCity SQL: " + sqlInitSelectedCity);
                    db.execSQL(sqlInitSelectedCity);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "initSelectedCity error: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (db != null) {
                db.close();
            }
        }
    }

    private boolean checkDbFile() {
        Cursor cursor = null;
        SQLiteDatabase db = null;
        boolean hasData = false;
        try {
            db = SQLiteDatabase.openDatabase(
                    sDbPATH, null, SQLiteDatabase.OPEN_READONLY);
            cursor = db.rawQuery(SQL_CHECK_QUERY, null);
            hasData = (cursor != null) && (cursor.getCount() > 0);
            Log.d(TAG, "checkDbFile END, hasData: " + hasData);
        } catch (SQLException e) {
            Log.d(TAG, "checkDbFile:[" + SQL_CHECK_QUERY + "]");
            e.printStackTrace();
            if (DB_CHECK_DEBUG) {
                throw e;
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }

            if (db != null) {
                db.close();
            }
        }
        return hasData;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // No need to create tables here, database is created from asset file.
    }

    //1.load from table:cities in alarms.db.
    //2.create the db file by coping from asset.
    //3.update default info.
    //4.drop old table.
    //5.update newest db version
    private boolean init() {
        boolean success = copyDefaultDbFileFromAsset(mContext);
        if (success) {
            List<KeyInfo> list = LegacySupport.getDefaultCities(mContext, mClockDatabase);
            updateDefaultCitiesInfo(getWritableDatabase(), list);
            ClockDatabaseHelper.deleteCitiesTable(mClockDatabase);
        } else {
            // The file may be corrupt, delete it.
            File file = new File(sDbPATH);
            if (file.exists()) {
                if (!file.delete()) {
                    Log.e(TAG, "Delete corrupt db file: " + sDbPATH + " failed!");
                }
            }
        }
        updateDbVersion(mContext, DB_VERSION);
        return success;
    }

    public static boolean copyDefaultDbFileFromAsset(Context context) {
        boolean success = false;
        if (context == null) {
            return success;
        }
        try {
            long start = System.currentTimeMillis();
            success = copyDataFile(Utils.getDeviceContext(context));
            long end = System.currentTimeMillis();
            Log.d(TAG, "CopyDefaultDbFile cost: " + (end - start) + " ms, success: " + success);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return success;
    }

    private static boolean copyDataFile(Context context) throws IOException {
        boolean success = false;
        InputStream is = context.getAssets().open(DATABASE_NAME);
        if (is != null) {
            byte[] buf = new byte[BUFFER_SIZE];
            File dbfile = new File(sDbPATH);
            OutputStream os = null;
            try {
                if (dbfile.createNewFile()) {
                    Log.d(TAG, "copy database file.");
                    os = new FileOutputStream(dbfile);
                    int readCount = 0;
                    while ((readCount = is.read(buf)) > 0) {
                        os.write(buf, 0, readCount);
                    }
                }
            } catch (Exception e) {
                Log.d(TAG, "copyDataFile error!");
                e.printStackTrace();
                success = false;
            } finally {
                if (os != null) {
                    os.flush();
                    os.close();
                }
                is.close();
            }
            success = true;
        }
        return success;
    }

    public static void updateDefaultCitiesInfo(SQLiteDatabase db, List<KeyInfo> list) {
        if ((list != null) && (!list.isEmpty()) && (db != null)) {
            long start = System.currentTimeMillis();
            try {
                db.beginTransaction();
                for (KeyInfo info : list) {
                    db.execSQL(SQL_UPDATE, buildValues(info));
                }
                db.setTransactionSuccessful();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (db != null) {
                    db.endTransaction();
                }
            }
            long end = System.currentTimeMillis();
            Log.d(TAG, "UpdateDefaultCitiesInfo cost: " + (end - start) + " ms.");
        }
    }

    /* Get value from Keyinfo */
    //0. CitiesColumns.FLAG
    //1. CitiesColumns.FLAG2
    //2. CitiesColumns.POS
    //3. CitiesColumns.SORTORDER2
    //4. CitiesColumns.CITY_NAME
    private static String[] buildValues(KeyInfo keyInfo) {
        String[] values = new String[5];
        values[0] = String.valueOf(keyInfo.getFlag());
        values[1] = String.valueOf(keyInfo.getFlag2());
        values[2] = String.valueOf(keyInfo.getPos());
        values[3] = String.valueOf(keyInfo.getSortOrder());
        values[4] = String.valueOf(keyInfo.getCityId());
        return values;
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int currentVersion) {
        Log.v(TAG, "Upgrading cities database from version " + oldVersion + " to " + currentVersion);
    }


    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.v(TAG, "onDowngrade cities database from version " + oldVersion + " to " + newVersion);
    }

    public static class KeyInfo {
        private String mEnName;
        private int mCityId;
        private int mFlag;
        private int mFlag2;

        private int mSortOrder;
        private int mPos;

        public KeyInfo(String name, int flag, int flag2, int sortOrder, int pos) {
            mEnName = name;
            mFlag = flag;
            mFlag2 = flag2;
            mSortOrder = sortOrder;
            mPos = pos;
        }

        public KeyInfo(int cityId, int flag, int flag2, int sortOrder, int pos) {
            mCityId = cityId;
            mFlag = flag;
            mFlag2 = flag2;
            mSortOrder = sortOrder;
            mPos = pos;
        }


        public KeyInfo(Context context, SharedPreferences preferences, int index) {
            mEnName = preferences.getString("city_name_" + index, "");
            String opcityId = preferences.getString("city_id_" + index, "");
            int resId = context.getResources().getIdentifier(opcityId, "string", context.getPackageName());
            if (resId != 0) {
                int tempCityId = -1;
                try {
                    tempCityId = Integer.parseInt(context.getString(resId));
                } catch (Exception e) {
                    Log.e(TAG, "op city error " + e.getMessage());
                }
                int cityId = CityUtils.changeToShowDomesticOrExp(context, tempCityId);
                if (cityId != -1) {
                    mCityId = cityId;
                } else {
                    mCityId = tempCityId;
                }
            }
            mFlag2 = 0;
            mPos = 0;
            mFlag = 0;
            mSortOrder = 0;
        }

        public String getEnName() {
            return mEnName;
        }

        public int getCityId() {
            return mCityId;
        }

        public int getFlag() {
            return mFlag;
        }

        public int getFlag2() {
            return mFlag2;
        }

        int getSortOrder() {
            return mSortOrder;
        }

        public int getPos() {
            return mPos;
        }
    }

}
