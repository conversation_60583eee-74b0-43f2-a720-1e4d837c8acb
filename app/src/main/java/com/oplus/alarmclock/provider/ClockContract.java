/*******************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :The contract between the clock provider and alarm clock. Contains definitions for
 * the supported URIs and data columns.
 * <p>
 * The AlarmsColumns table holds the user created alarms The ScheduleColumns table holds the current
 * state of each alarm in the AlarmsColumn table. The CitiesColumns table holds all user selected
 * cities
 * <p>
 * Date:2016-03-17 Author:Amy
 *******************************************************/
package com.oplus.alarmclock.provider;

import android.net.Uri;
import android.provider.BaseColumns;

public final class ClockContract {
    /**
     * This authority is used for writing to or querying from the clock provider.
     */
    public static final String AUTHORITY = "com.coloros.alarmclock.alarmclock";

    public static final String AUTHORITY_OPLUS = "com.oplus.alarmclock.alarmclock";

    /**
     * The content:// style URL for this table.
     */
    public static final Uri ALARM_CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/alarm");

    public static final Uri TIMER_CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/timers");

    public static final Uri ALARMS_REPEAT_URI = Uri.parse("content://" + AUTHORITY + "/alarms_repeat");

    public static final Uri ALARMS_HOLIDAY_URI = Uri.parse("content://" + AUTHORITY + "/alarm_holiday");

    public static final Uri ALARM_RING_URI = Uri.parse("content://" + AUTHORITY + "/alarm_ring");

    public static final Uri CLOCK_SETTINGS_URI = Uri.parse("content://" + AUTHORITY + "/settings");

    public static final String ALARMS_REPEAT_ORDER_LIMIT = AlarmsRepeat._ID + " desc limit 0,1";

    public interface TimerTableColumns extends BaseColumns {
        String DURATION = "duration";
        String DESCRIPTION = "description";
        String FLAG = "flag";
        String SELECTED = "selected";
        String RING = "ring";
        String RINGNAME = "ringName";
    }


    /**
     * Constants for the Alarms table, which contains the user created alarms.
     */
    public static final class Alarm {

        public static final String ID = "_id";

        /**
         * Hour in 24-hour localtime 0 - 23.
         * <p>
         * Type: INTEGER
         * </p>
         */
        public static final String HOUR = "hour";

        /**
         * Minutes in localtime 0 - 59.
         * <p>
         * Type: INTEGER
         * </p>
         */
        public static final String MINUTES = "minutes";

        /**
         * Days of the week encoded as a bit set.
         * <p>
         * Type: INTEGER
         * </p>
         * <p>
         * {@see RepeatSet}
         */
        public static final String DAYS_OF_WEEK = "daysofweek";

        /**
         * Alarm time in UTC milliseconds from the epoch.
         * <p>
         * Type: INTEGER
         * </P>
         */
        public static final String ALARM_TIME = "alarmtime";

        /**
         * True if alarm is active.
         * <p>
         * Type: BOOLEAN
         * </p>
         */
        public static final String ENABLED = "enabled";

        /**
         * True if alarm should vibrate
         * <p>
         * Type: BOOLEAN
         * </P>
         */
        public static final String VIBRATE = "vibrate";

        /*
         * alarm add environment user id
         * */
        public static final String OWNER_USER_ID = "ownerUserId";

        /**
         * True if alarm should vibrate
         * <p>
         * Type: int
         * </P>
         */
        public static final String ALERTTYPE = "alerttype";

        /**
         * Message to show when alarm triggers Note: not currently used
         * <p>
         * Type: STRING
         * </P>
         */
        public static final String MESSAGE = "message";

        /**
         * Time to snooze.
         * <p>
         * Type:INTEGER
         * </P>
         **/
        public static final String SNOOZE = "snooze";

        /**
         * Audio alert to play when alarm triggers
         * <p>
         * Type: STRING
         * </P>
         */
        public static final String ALERT = "alert";

        /**
         * Name of Audio alert to play when alarm triggers
         * <p>
         * Type: STRING
         * </P>
         */
        public static final String ALERT_RINGNAME = "ringName";

        /***
         * the alarm's volume.
         * <p>
         * it and background are not used in the recent version. keep it to stay in step with old
         * version.
         * <p>
         * Type: INTEGER
         * </P>
         */
        public static final String VOLUME = "volume";

        public static final String BACKGROUND = "backGround";
        /**
         * Determine if alarm is deleted after it has been used.
         * <p>
         * Type: INTEGER
         * </p>
         */
        public static final String DELETE_AFTER_USE = "deleteAfterUse";

        /*
         * workday switch
         * */
        public static final String WORKDAY_SWITCH = "workdaySwitch";

        /*
         * holiday switch
         * */
        public static final String HOLIDAY_SWITCH = "holidaySwitch";

        /**
         * alarm close once
         */
        public static final String CLOSE_ONCE_NEXT_TIME = "closeOnceNextTime";

        /**
         * alarm close once time
         */
        public static final String CLOSE_ONCE_PREVIOUS_TIME = "closeOncePreviousTime";

        public static final String ENABLE_ASSOCIATE = "enableAssociate";

        public static final String ALARM_UUID = "uuid";

        /**
         * 稍后提醒时长
         */
        public static final String SNOOZE_TIME = "snoozeTime";

        /**
         * 工作日类型
         */
        public static final String WORKDAY_TYPE = "workdayType";

        /**
         * 创建时间
         */
        public static final String WORKDAY_UPDATE_TIME = "workdayUpdateTime";

        /**
         * 自定义闹钟排除的特殊日期
         */
        public static final String SPECIAL_ALARM_DAYS = "specialAlarmDays";

        /**
         * 是否为默认闹钟
         */
        public static final String DEFAULT_ALARM = "defaultAlarm";

        /**
         * 响铃次数
         */
        public static final String RING_NUMBER = "ringNum";

        /**
         * 轮班闹钟开关
         */
        public static final String LOOP_SWITCH = "loopSwitch";
        /**
         * 轮班闹钟周期
         */
        public static final String LOOP_CYCLE_DAYS = "loopCycleDays";
        /**
         * 轮班闹钟ID
         */
        public static final String LOOP_ID = "loopID";

        /**
         * 轮班闹钟工作日天数
         */
        public static final String LOOP_WORK_DAYS = "loopWorkDays";
        /**
         * 轮班闹钟下标
         */
        public static final String LOOP_ALARM_NUMBER = "loopAlarmNumber";
        /**
         * 轮班闹钟第几天
         */
        public static final String LOOP_DAY = "loopDay";

        public static final String LOOP_RESET_DAYS = "loopResetDays";

        /**
         * 铃声绝对路径
         */
        public static final String RING_ABSOLUTE_PATH = "ringAbsolutePath";

        /**
         * 秒抢闹钟开关
         */
        public static final String GARB_ALARM_SWITCH = "garbSwitch";
        public static final String GARB_ALARM_DATE = "garbAlarmDate";

        /**
         * The default sort order for this table
         */
        public static final String DEFAULT_SORT_ORDER = HOUR + ", " + MINUTES + " ASC" + ", " + ID + " DESC";
        /**
         * 轮班闹钟排序
         */
        public static final String LOOP_ALARM_SORT_ORDER = LOOP_DAY + " ASC";

        public static final String LOOP_ALARM_FILTER = LOOP_ID + " = -1";

        /**
         * These save calls to cursor.getColumnIndexOrThrow() THEY MUST BE KEPT IN SYNC WITH ABOVE
         * QUERY COLUMNS
         */
        public static final int ALARM_ID_INDEX = 0;
        public static final int ALARM_HOUR_INDEX = 1;
        public static final int ALARM_MINUTES_INDEX = 2;
        public static final int ALARM_DAYS_OF_WEEK_INDEX = 3;
        public static final int ALARM_TIME_INDEX = 4;
        public static final int ALARM_ENABLED_INDEX = 5;
        public static final int ALARM_ALERTTYPE_INDEX = 6;
        public static final int ALARM_MESSAGE_INDEX = 7;
        public static final int ALARM_SOONZE_INDEX = 8;
        public static final int ALARM_ALERT_INDEX = 9;
        public static final int ALARM_ALERT_RINGNAME_INDEX = 10;
        public static final int ALARM_VOLUME_INDEX = 11;
        public static final int ALARM_BACK_GROUND_INDEX = 12;
        public static final int ALARM_DELETE_AFTER_USE_INDEX = 13;
        public static final int ALARM_VIBRATE_INDEX = 14;
        public static final int ALARM_WORKDAY_SWITCH_INDEX = 15;
        public static final int ALARM_HOLIDAY_SWITCH_INDEX = 16;
        public static final int ALARM_USER_ID_INDEX = 17;
        public static final int ALARM_CLOSE_ONCE_NEXT_TIME_INDEX = 18;
        public static final int ALARM_CLOSE_ONCE_PREVIOUS_TIME_INDEX = 19;
        public static final int ALARM_ENABLE_ASSOCIATE_INDEX = 20;
        public static final int ALARM_UUID_INDEX = 21;
        public static final int ALARM_SNOOZE_TIME_INDEX = 22;
        public static final int ALARM_WORKDAY_TYPE_INDEX = 23;
        public static final int ALARM_WORKDAY_UPDATE_TIME_INDEX = 24;
        public static final int ALARM_SPECIAL_ALARM_DAYS_INDEX = 25;
        public static final int ALARM_DEFAULT_ALARM_INDEX = 26;
        public static final int ALARM_RING_NUMBER_INDEX = 27;
        public static final int ALARM_LOOP_SWITCH_INDEX = 28;
        public static final int ALARM_LOOP_CYCLE_DAYS_INDEX = 29;
        public static final int ALARM_LOOP_ID_INDEX = 30;
        public static final int ALARM_LOOP_WORK_DAYS_INDEX = 31;
        public static final int ALARM_LOOP_ALARM_NUMBER_INDEX = 32;
        public static final int ALARM_LOOP_DAY_INDEX = 33;
        public static final int ALARM_LOOP_RESET_DAYS_INDEX = 34;
        public static final int ALARM_RING_ABSOLUTE_PATH_INDEX = 35;
        public static final int ALARM_GARB_SWITCH_INDEX = 36;
    }


    /**
     * Constants for the Instance table, which contains the state of each alarm.
     */
    private interface ScheduleColumns extends BaseColumns {

        /**
         * Alarm state when to show no notification.
         * <p>
         * Can transitions to: LOW_NOTIFICATION_STATE
         */
        int SILENT_STATE = 0;

        /**
         * Alarm state when alarm is in snooze.
         * <p>
         * Can transitions to: DISMISSED_STATE FIRED_STATE
         */
        int SNOOZE_STATE = 1;

        /**
         * Alarm state when alarm is being fired.
         * <p>
         * Can transitions to: DISMISSED_STATE SNOOZED_STATE MISSED_STATE
         */
        int FIRED_STATE = 2;

        /**
         * Alarm state when alarm is done.
         */
        int DISMISSED_STATE = 3;

        /**
         * AlarmStateManager 秒抢闹钟状态
         */
        int GARB_ALARM_STATE = 4;

        /**
         * Alarm year.
         * <p>
         * <p>
         * Type: INTEGER
         * </p>
         */
        String YEAR = "year";

        /**
         * Alarm month in year.
         * <p>
         * <p>
         * Type: INTEGER
         * </p>
         */
        String MONTH = "month";

        /**
         * Alarm day in month.
         * <p>
         * <p>
         * Type: INTEGER
         * </p>
         */
        String DAY = "day";

        /**
         * Alarm hour in 24-hour localtime 0 - 23.
         * <p>
         * Type: INTEGER
         * </p>
         */
        String HOUR = "hour";

        /**
         * Alarm minutes in localtime 0 - 59
         * <p>
         * Type: INTEGER
         * </p>
         */
        String MINUTES = "minutes";
        /**
         * Alarm time in UTC milliseconds from the epoch.
         * <p>
         * Type: INTEGER
         * </P>
         */
        String ALARM_TIME = "alarmtime";

        String SNOOZETIME = "snooze_time";

        String ALARM_ID = "alarm_id";

        /**
         * Alarm state
         * <p>
         * Type: INTEGER
         * </p>
         */
        String ALARM_STATE = "alarm_state";


        /**
         * The content:// style URL for this table.
         */
        Uri ALARM_SCHEDULE_CONTENT_URI = Uri
                .parse("content://" + ClockContract.AUTHORITY + "/schedules");

    }

    public static final class Schedule implements ScheduleColumns {
        // Schedule table.
    }

    public static final class ScheduleView {
        public static final String ALARM_HOUR = "alarm_hour";
        public static final String ALARM_MINUTE = "alarm_minute";
        public static final String ALARM_MILLS = "alarm_mills";
    }

    /**
     * Constants for the Cities table, which contains all selectable cities.
     */
    private interface CitiesColumns extends BaseColumns {

        /**
         * English name of the city.
         * <p>
         * Type: TEXT
         * </P>
         */
        String EN_NAME = "en_US";  //"enName";

        /**
         * Simplified Chinese of the city
         * <p>
         * Type: TEXT
         * </P>
         */
        String ZH_NAME = "zh_CN";  //"zhName";

        /**
         * Simplified  HK  of the city
         * <p>
         * Type: TEXT
         * </P>
         */
        String HK_NAME = "zh_HK";   //"hkName";
        /**
         * the time zone id of a city
         * <p>
         * Type: TEXT
         * </P>
         */
        String TW_NAME = "zh_TW";   //"twName";
        /**
         * the time zone id of a city
         * <p>
         * Type: TEXT
         * </P>
         */
        String TIMEZONE_ID = "timezone_id";    //"idCity";

        /**
         * The offset of time zone.
         * <p>
         * Type: INTEGER
         * </P>
         */
        String RAW_OFFSET = "raw_offset";//"rawOffest";

        /**
         * A city's latitude
         * <p>
         * Type: FLOAT
         * </P>
         */
        String LATITUDE = "latitude";

        /**
         * A city's longitude
         * <p>
         * Type: FLOAT
         * </P>
         */
        String LONGITUDE = "longitude";

        /**
         * first spell for a city chinese name
         * <p>
         * Type: TEXT
         * </P>
         */
        String FIRST_SPELL = "first_spell"; //"fisrtSpell";

        /**
         * full spell for a city chinese name
         * <p>
         * Type: TEXT
         * </P>
         */
        String FULL_SPELL = "full_spell";    //"fullSpell";

        /**
         * weather the city should show in choose list. if user has already add this city, keep it
         * in Global City list,but not show it in add city choose list.
         * <p>
         * Type: TEXT
         * </P>
         */
        String VISIBLE = "visible";

        /**
         * Whether the city has been added into ;
         * <p>
         * Type: INTEGER
         * </P>
         */
        String FLAG = "flag";
        /**
         * Whether the city has been added into ;
         * <p>
         * Type: INTEGER
         * </P>
         */
        String FLAG2 = "flag2";     // 0: visible, 1: hide.

        /**
         * the city's order to display. sort by the order selected in.
         * <p>
         * Type: INTEGER
         * </P>
         */
        String SORT_ORDER = "sort_order";   //"sortOrder2";

        /**
         * the city's position to display.firstly it equals the city_id.
         * Not used, and do not modify it.
         * <p>
         * Type: INTEGER
         * </P>
         */
        //TODO: Check if delete this later.
        String POS = "pos";

        //new COLS.
        String CITY_NAME = "name";
        String CITY_COUNTRY = "country";
        String FIRST_LETTER = "first_letter";
        String CITY_INDEX = "city_index";
        String LOCALE_LAN = "locale";
        String REGION = "region";
        String CONTINENT = "continent";
        String CITY_ID = "city_id";
        String CITY_ID_ = "_id";
        /**
         * City unique identifier
         */
        String ID = "id";

        //end.

        /**
         * The content:// style URL for this table.
         */
        Uri NEW_CITY_CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/new_cities");
    }

    public static final class City implements CitiesColumns {
        // City table.
    }


    private interface AlarmsRepeatColumns extends BaseColumns {

        /**
         * the alarms ,alert duration  from [1,5,10,15,20,30] ,default 5min
         * <p>
         * Type: INTEGER
         * </P>
         */
        String ALARM_DURATION = "alarm_duration";

        /**
         * the alarms ,alert interval  from [5,10,15,20,25,30] ,default 5min
         * <p>
         * Type: INTEGER
         * </P>
         */
        String ALARM_INTERVAL = "alarm_interval";
        /**
         * the alarms , most alert number  from [1,2,3,5,10] ,default 5min
         * <p>
         * Type: INTEGER
         * </P>
         */
        String ALARM_NUM = "alarm_num";
        /**
         * the alarms , one hour before the alarm rings
         * <p>
         * Type: BOOLEAN
         * </P>
         */
        String ALARM_PROMPT = "alarm_prompt";
        String[] QUERY_COLUMNS = {
                _ID,
                ALARM_DURATION,
                ALARM_INTERVAL,
                ALARM_NUM,
                ALARM_PROMPT
        };
    }

    public static final class AlarmsRepeat implements AlarmsRepeatColumns {

    }

    private interface AlarmsHolidayColumns extends BaseColumns {
        String ID = "_id";
        String SIGN_TITLE = "sign_title";
        String YEAR = "year";
        String YEAR_DAY = "year_day";
        String LOCATION = "location";
        String COLOR_TYPE = "color_type";
        int UP_RIGHT_LOCATION = 3;
        int COLOR1 = 1;
        int COLOR2 = 2;
        String[] QUERY_COLUMNS = {
                _ID,
                SIGN_TITLE,
                YEAR,
                YEAR_DAY,
                LOCATION,
                COLOR_TYPE
        };

    }

    public static final class AlarmHoliday implements AlarmsHolidayColumns {

    }

    public static final class Settings implements BaseColumns {

        /**
         * alarm close model
         * type: boolean
         */
        public static final String ALARM_CLOSE_MODEL = "alarm_close_model";

        /**
         * the default ringtone of alarm
         * type: String
         */
        public static final String ALARM_DEFAULT_RINGTONE = "alarm_default_ringtone";

        /**
         * the default ringtone of alarm
         * type: Int
         */
        public static final String ALARM_DEFAULT_VIBRATE = "alarm_default_vibrate";

        /**
         * the early morning notification of alarm
         * type: boolean
         */
        public static final String ALARM_EARLY_MORNING_NOTIFY = "alarm_early_morning_notify";

        /**
         * the morning report enable of alarm
         * type: boolean
         */
        public static final String ALARM_MORNING_REPORT = "alarm_morning_report";

        /**
         * the bell gradually rings
         */
        public static final String ALARM_GRADUALLY_RINGS = "alarm_gradually_rings";

        public static final int COLUMN_ALARM_CLOSE_MODEL = 1;
        public static final int COLUMN_ALARM_DEFAULT_RINGTONE = 2;
        public static final int COLUMN_ALARM_DEFAULT_VIBRATE = 3;
        public static final int COLUMN_EARLY_MORNING_NOTIFY = 4;
        public static final int COLUMN_MORNING_REPORT = 5;
        public static final int COLUMN_GRADUALLY_RINGS = 6;
        public static final String[] QUERY_COLUMNS = {
                _ID,
                ALARM_CLOSE_MODEL,
                ALARM_DEFAULT_RINGTONE,
                ALARM_DEFAULT_VIBRATE,
                ALARM_EARLY_MORNING_NOTIFY,
                ALARM_MORNING_REPORT,
                ALARM_GRADUALLY_RINGS
        };
    }
}
