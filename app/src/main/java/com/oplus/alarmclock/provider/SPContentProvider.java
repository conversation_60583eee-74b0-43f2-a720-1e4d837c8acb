package com.oplus.alarmclock.provider;

import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_NAME;
import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_TYPE;
import static com.oplus.alarmclock.utils.ClockConstant.CURSOR_COLUMN_VALUE;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_BOOLEAN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_CLEAN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_CONTAIN;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_FLOAT;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_GET_ALL;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_INT;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_LONG;
import static com.oplus.alarmclock.utils.ClockConstant.TYPE_STRING;
import static com.oplus.alarmclock.utils.ClockConstant.VALUE;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.net.Uri;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import java.util.Map;
import java.util.Set;
public class SPContentProvider extends ContentProvider {
    private static final String AUTHORITY = "com.oplus.alarmclock.main.sphelper";
    private static final String AUTHORITY_PURE = "com.oplus.alarmclock.sphelper";
    private static final String CONTENT = "content://";
    public static final String SEPARATOR = "/";
    public static final String CONTENT_URI = CONTENT + (DeviceUtils.isWPlusPhone() ? AUTHORITY_PURE : AUTHORITY);

    @Override
    public boolean onCreate() {
        return true;
    }
    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        String[] path = uri.getPath().split(SEPARATOR);
        String type = path[1];
        if (type.equals(TYPE_GET_ALL)) {
            Map<String, ?> all = PrefUtils.getAll(getContext(), AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP);
            if (all != null) {
                MatrixCursor cursor = new MatrixCursor(new String[]{CURSOR_COLUMN_NAME, CURSOR_COLUMN_TYPE, CURSOR_COLUMN_VALUE});
                Set<String> keySet = all.keySet();
                for (String key : keySet) {
                    Object[] rows = new Object[3];
                    rows[0] = key;
                    rows[2] = all.get(key);
                    if (rows[2] instanceof Boolean) {
                        rows[1] = TYPE_BOOLEAN;
                    } else if (rows[2] instanceof String) {
                        rows[1] = TYPE_STRING;
                    } else if (rows[2] instanceof Integer) {
                        rows[1] = TYPE_INT;
                    } else if (rows[2] instanceof Long) {
                        rows[1] = TYPE_LONG;
                    } else if (rows[2] instanceof Float) {
                        rows[1] = TYPE_FLOAT;
                    }
                    cursor.addRow(rows);
                }
                return cursor;
            }
        }
        return null;
    }
    @Override
    public String getType(Uri uri) {
        // 用这个来取数值
        String[] path = uri.getPath().split(SEPARATOR);
        String type = path[1];
        String key = path[2];
        if (type.equals(TYPE_CONTAIN)) {
            return PrefUtils.contains(getContext(), AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, key) + "";
        }
        return "" + PrefUtils.get(getContext(), key, type);
    }
    @Override
    public Uri insert(Uri uri, ContentValues values) {
        String[] path = uri.getPath().split(SEPARATOR);
        String type = path[1];
        String key = path[2];
        Object obj = (Object) values.get(VALUE);
        if (obj != null) {
            PrefUtils.save(getContext(), key, obj);
        }
        return null;
    }
    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        String[] path = uri.getPath().split(SEPARATOR);
        String type = path[1];
        if (type.equals(TYPE_CLEAN)) {
            PrefUtils.clear(getContext());
            return 0;
        }
        String key = path[2];
        if (PrefUtils.contains(getContext(), AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, key)) {
            PrefUtils.remove(getContext(), key);
        }
        return 0;
    }
    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        insert(uri, values);
        return 0;
    }
}
