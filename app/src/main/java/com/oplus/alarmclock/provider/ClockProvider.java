/*******************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :contentProvider.
 * <p>
 * Date:2016-05-10 Author:Amy
 *******************************************************/
// OPLUS Java File Skip Rule:ParameterAssignment
package com.oplus.alarmclock.provider;

import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.OperationApplicationException;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.IOTUtil;
import com.oplus.alarmclock.provider.alarmring.AlarmRingDatabaseUtils;
import com.oplus.alarmclock.provider.settings.SettingDbUtils;
import com.oplus.alarmclock.timer.data.TimerTableCreator;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.DialClockUtil;
import com.oplus.clock.common.osdk.ContextNativeUtils;
import com.oplus.clock.common.osdk.IntentNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;
import com.coloros.alarmclock.widget.DigitalAppWidgetProvider;
import com.oplus.clock.common.utils.VersionUtils;

import java.time.Clock;
import java.util.ArrayList;

import androidx.annotation.NonNull;

public class ClockProvider extends ContentProvider {
    private static final String TAG = "ClockProvider";

    private static final String PARAM_NOTIFY = "PARAM_NOTIFY";
    private static final String UNNOTIFY = "UNNOTIFY";

    private static final String AND_STRING = " AND (";

    private static final int ALARMS = 1;
    private static final int ALARMS_ID = 2;
    private static final int SCHEDULES = 3;
    private static final int SCHEDULES_ID = 4;
    private static final int CITIES = 5;
    private static final int CITIES_ID = 6;
    private static final int DEFAULT_VOLUME = 5;
    private static final int NEW_CITIES = 7;
    private static final int TIMERS = 8;
    private static final int ALARM_REPEAT = 9;
    private static final int ALARM_REPEAT_ID = 10;
    private static final int ALARM_HOLIDAY = 11;
    private static final int ALARM_HOLIDAY_ID = 12;
    private static final int ALARM_RING = 13;
    private static final int SETTINGS = 14;
    private static final long IOT_MSG_DELAY = 1000;

    private static final UriMatcher sURLMatcher = new UriMatcher(UriMatcher.NO_MATCH);

    static {
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm", ALARMS);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm/#", ALARMS_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "schedules", SCHEDULES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "schedules/#", SCHEDULES_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "cities", CITIES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "cities/*", CITIES_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "new_cities", NEW_CITIES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "timers", TIMERS);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarms_repeat", ALARM_REPEAT);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarms_repeat/#", ALARM_REPEAT_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm_holiday", ALARM_HOLIDAY);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm_holiday/#", ALARM_HOLIDAY_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm_ring", ALARM_RING);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "settings", SETTINGS);
    }

    private Context mContext;
    private ClockDatabaseHelper mOpenHelper;
    private SQLiteDatabase mDatabase;

    private CityDatabaseHelper mCityDatabaseHelper;
    private Handler mHandler;

    public ClockProvider() {
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "onCreate");
        mContext = this.getContext();
        boolean isFbeEnabled = DeviceUtils.isFbeEnabled();
        Context storageContext = null;
        if ((!isFbeEnabled) && (Utils.isNOrLater())) {
            Log.d(TAG, "Context: " + mContext.getDataDir().getAbsolutePath());
            storageContext = ContextNativeUtils.createCredentialProtectedStorageContext(mContext);
            Log.d(TAG, "storageContext: " + storageContext);
            if (storageContext != null) {
                Log.d(TAG, "storageContext: " + storageContext.getDataDir().getAbsolutePath());
                if (!mContext.moveDatabaseFrom(storageContext, ClockDatabaseHelper.DATABASE_NAME)) {
                    Log.w(TAG, "Failed to migrate clock database: " + ClockDatabaseHelper.DATABASE_NAME);
                } else {
                    Log.i(TAG, "(FBE)Migrate clock database: " + ClockDatabaseHelper.DATABASE_NAME + " OK!");
                }

                if (!mContext.moveDatabaseFrom(storageContext, CityDatabaseHelper.DATABASE_NAME)) {
                    Log.w(TAG, "Failed to migrate city database: " + CityDatabaseHelper.DATABASE_NAME);
                } else {
                    Log.i(TAG, "(FBE)Migrate city database: " + CityDatabaseHelper.DATABASE_NAME + " OK!");
                }
            }
        }

        if (mOpenHelper == null) {
            mOpenHelper = new ClockDatabaseHelper(mContext);
        }
        mDatabase = mOpenHelper.getWritableDatabase();
        mCityDatabaseHelper = new CityDatabaseHelper(mContext, mDatabase);
        return true;
    }

    @NonNull
    @Override
    public ContentProviderResult[] applyBatch(@NonNull ArrayList<ContentProviderOperation> operations)
            throws OperationApplicationException {
        if ((operations.isEmpty()) || (null == mDatabase)) {
            return new ContentProviderResult[0];
        }
        try {
            mDatabase.beginTransaction();
            int size = operations.size();
            ContentProviderResult[] result = new ContentProviderResult[size];
            for (int i = 0; i < size; i++) {
                ContentProviderOperation opertaion = operations.get(i);
                result[i] = opertaion.apply(this, result, i);
            }
            mDatabase.setTransactionSuccessful();
            return result;
        } finally {
            try {
                mDatabase.endTransaction();
            } catch (Exception e) {
                Log.e(TAG, "endTransaction error e= " + e);
            }
        }
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projectionIn, String selection, String[] selectionArgs,
                        String sort) {
        SQLiteQueryBuilder qb = new SQLiteQueryBuilder();
        SQLiteDatabase db = mOpenHelper.getReadableDatabase();

        Log.d(TAG, "query uri: " + uri + ", selection: " + selection);

        // Generate the body of the query
        int match = sURLMatcher.match(uri);
        switch (match) {
            case ALARMS:
                qb.setTables(ClockDatabaseHelper.ALARMS_TABLE_NAME);
                break;
            case ALARMS_ID:
                qb.setTables(ClockDatabaseHelper.ALARMS_TABLE_NAME);
                qb.appendWhere(ClockContract.Alarm.ID + "=");
                qb.appendWhere(uri.getLastPathSegment());
                break;
            case SCHEDULES:
                qb.setTables(ClockDatabaseHelper.SCHEDULES_VIEW_NAME);
                break;
            case SCHEDULES_ID:
                qb.setTables(ClockDatabaseHelper.SCHEDULES_VIEW_NAME);
                qb.appendWhere(ClockContract.Schedule._ID + "=" + uri.getLastPathSegment());
                break;
            case CITIES:
            case CITIES_ID:
                Log.e(TAG, "The uri[" + uri + "] is not available anymore!");
                return null;
            case NEW_CITIES:
                db = mCityDatabaseHelper.getReadableDatabase();
                qb.setTables(ClockDatabaseHelper.NEW_CITIES_TABLE_NAME);
                break;
            case TIMERS:
                qb.setTables(TimerTableCreator.TIMER_TABLE_NAME);
                break;
            case ALARM_REPEAT:
                qb.setTables(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME);
                break;
            case ALARM_REPEAT_ID:
                qb.setTables(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME);
                qb.appendWhere(ClockContract.AlarmsRepeat._ID + "=");
                qb.appendWhere(uri.getLastPathSegment());
                break;
            case ALARM_HOLIDAY:
                qb.setTables(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME);
                break;
            case ALARM_HOLIDAY_ID:
                qb.setTables(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME);
                qb.appendWhere(ClockContract.AlarmHoliday._ID + "=");
                qb.appendWhere(uri.getLastPathSegment());
                break;
            case ALARM_RING:
                AlarmRingDatabaseUtils.setTable(qb);
                break;
            case SETTINGS:
                qb.setTables(ClockDatabaseHelper.SETTING_TABLE_NAME);
                break;
            default:
                throw new IllegalArgumentException("Unknown URI " + uri);
        }

        Cursor ret = qb.query(db, projectionIn, selection, selectionArgs, null, null, sort);

        if (ret == null) {
            Log.e(TAG, "Alarms.query: failed");
        } else if (match == ALARMS || match == ALARMS_ID) {
            Bundle bundle = new Bundle();
            bundle.putInt("max_alarm_count", AlarmClockFragment.MAX_ALARM_COUNT);
            ret.setExtras(bundle);
            Log.i(TAG, "Alarms.query end: " + ret.getExtras());
        }

        return ret;
    }

    @Override
    public String getType(@NonNull Uri uri) {
        int match = sURLMatcher.match(uri);
        switch (match) {
            case ALARMS:
                return "vnd.android.cursor.dir/alarms";
            case ALARMS_ID:
                return "vnd.android.cursor.item/alarms";
            case SCHEDULES:
                return "vnd.android.cursor.dir/schedules";
            case SCHEDULES_ID:
                return "vnd.android.cursor.item/schedules";
            case NEW_CITIES:
                return "vnd.android.cursor.dir/new_cities";
            case ALARM_REPEAT:
                return "vnd.android.cursor.dir/alarm_repeat";
            case ALARM_REPEAT_ID:
                return "vnd.android.cursor.item/alarm_repeat";
            case ALARM_HOLIDAY:
                return "vnd.android.cursor.dir/alarm_holiday";
            case ALARM_HOLIDAY_ID:
                return "vnd.android.cursor.item/alarm_holiday";
            default:
                throw new IllegalArgumentException("Unknown URI");
        }
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String where, String[] whereArgs) {
        int count = 0;
        String id = "";
        if (!DeviceUtils.isExpVersion(AlarmClockApplication.getInstance())) {
            Log.d(TAG, "update uri: " + uri + ", selection: " + where + ", values: " + values);
        }
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        switch (sURLMatcher.match(uri)) {
            case ALARMS_ID:
                id = uri.getLastPathSegment();
                count = db.update(ClockDatabaseHelper.ALARMS_TABLE_NAME, values,
                        ClockContract.Alarm.ID + "=" + id, null);
                Log.d(TAG, "updateType: " + PlatformUtils.sUpdateType);
                if (PlatformUtils.sUpdateType != PlatformUtils.UPDATE_TYPE_IOT) {
                    getHandler().postDelayed(() -> IOTUtil.sendNotifyReceiver(getContext(), uri, ""), IOT_MSG_DELAY);
                }
                break;
            case SCHEDULES_ID:
                id = uri.getLastPathSegment();
                count = db.update(ClockDatabaseHelper.SCHEDULES_TABLE_NAME, values,
                        ClockContract.Schedule._ID + "=" + id, null);
                break;
            case CITIES:
                Log.e(TAG, "The uri[" + uri + "] is not available anymore!");
                return 0;
            case NEW_CITIES: {
                db = mCityDatabaseHelper.getWritableDatabase();
                count = db.update(ClockDatabaseHelper.NEW_CITIES_TABLE_NAME, values, where, whereArgs);
                DialClockUtil.postDialWorldClockData(mContext);
                final Context context = getContext();
                if ((context != null)) {
                    Intent intent = new Intent(DigitalAppWidgetProvider.ACTION_CITIES_DATA_CHANGED);
                    intent.addFlags(IntentNativeUtils.getFLAG_RECEIVER_INCLUDE_BACKGROUND());
                    context.sendBroadcast(intent);
                    Intent intentOld = new Intent(DigitalAppWidgetProvider.ACTION_CITIES_DATA_CHANGED_OLD);
                    intentOld.addFlags(IntentNativeUtils.getFLAG_RECEIVER_INCLUDE_BACKGROUND());
                    context.sendBroadcast(intentOld);
                }
            }
            break;
            case TIMERS:
                count = db.update(TimerTableCreator.TIMER_TABLE_NAME, values, where, whereArgs);
                break;
            case ALARM_REPEAT:
                count = db.update(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME, values, where, whereArgs);
                break;

            case ALARM_REPEAT_ID:
                id = uri.getLastPathSegment();
                count = db.update(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME, values,
                        ClockContract.AlarmsRepeat._ID + "=" + id, null);
                break;
            case ALARM_HOLIDAY:
                count = db.update(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME, values, where, whereArgs);
                break;

            case ALARM_HOLIDAY_ID:
                id = uri.getLastPathSegment();
                count = db.update(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME, values,
                        ClockContract.AlarmHoliday._ID + "=" + id, null);
                break;
            case ALARM_RING:
                AlarmRingDatabaseUtils.updateAlarmRing(db, values);
                break;

            case SETTINGS:
                count = db.update(ClockDatabaseHelper.SETTING_TABLE_NAME, values, where, whereArgs);
                break;
            case ALARMS:
                count = db.update(ClockDatabaseHelper.ALARMS_TABLE_NAME,values,where,whereArgs);
                break;
            default: {
                throw new UnsupportedOperationException("Cannot update URI: " + uri);
            }
        }

        boolean doNotify = shouldDoNotify(uri);
        if (doNotify) {
            Log.d(TAG, "update notifyChange uri: " + uri);
            notifyBothProvider(uri);
        }
        return count;
    }

    @Override
    public Uri insert(@NonNull Uri uri, ContentValues initialValues) {
        long rowId = -1;
        Log.d(TAG, "insert uri: " + uri);
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        Uri uriResult = null;
        switch (sURLMatcher.match(uri)) {
            case ALARMS:
                ContentValues values = fixAlarmInsertValues(initialValues);
                if (values != null) {
                    rowId = db.insert(ClockDatabaseHelper.ALARMS_TABLE_NAME,
                            ClockContract.Alarm.ALERT, values);
                    uriResult = ContentUris.withAppendedId(ClockContract.ALARM_CONTENT_URI, rowId);
                    IOTUtil.sendNotifyReceiver(getContext(), rowId, "");
                }

                break;
            case SCHEDULES:
                rowId = db.insert(ClockDatabaseHelper.SCHEDULES_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(
                        ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, rowId);
                break;
            case CITIES:
                Log.e(TAG, "The uri[" + uri + "] is not available anymore!");
                return null;
            case NEW_CITIES:
                db = mCityDatabaseHelper.getWritableDatabase();
                rowId = db.insert(ClockDatabaseHelper.NEW_CITIES_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(ClockContract.City.NEW_CITY_CONTENT_URI, rowId);
                break;
            case TIMERS:
                rowId = db.insert(TimerTableCreator.TIMER_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(ClockContract.TIMER_CONTENT_URI, rowId);
                break;
            case ALARM_REPEAT:
                rowId = db.insert(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(ClockContract.ALARMS_REPEAT_URI, rowId);
                break;
            case ALARM_HOLIDAY:
                rowId = db.insert(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(ClockContract.ALARMS_HOLIDAY_URI, rowId);
                break;
            case ALARM_RING:
                AlarmRingDatabaseUtils.insertAlarmRing(db, initialValues);
                break;
            case SETTINGS:
                rowId = db.insert(ClockDatabaseHelper.SETTING_TABLE_NAME, null, initialValues);
                uriResult = ContentUris.withAppendedId(ClockContract.CLOCK_SETTINGS_URI, rowId);
                break;
            default:
                throw new IllegalArgumentException("Cannot insert from URI: " + uri);
        }

        if (uriResult != null) {
            boolean doNotify = shouldDoNotify(uri);
            if (doNotify) {
                Log.d(TAG, "insert notifyChange uri: " + uriResult);
                notifyBothProvider(uri);
            }
        }
        return uriResult;
    }

    @Override
    public int delete(@NonNull Uri uri, String where, String[] whereArgs) {
        int count = 0;
        Log.d(TAG, "delete uri: " + uri + ", where: " + where);
        String primaryKey = "";
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        switch (sURLMatcher.match(uri)) {
            case ALARMS:
                count = db.delete(ClockDatabaseHelper.ALARMS_TABLE_NAME, where, whereArgs);
                break;
            case ALARMS_ID:
                primaryKey = uri.getLastPathSegment();

                if (TextUtils.isEmpty(where)) {
                    where = ClockContract.Alarm.ID + "=" + primaryKey;
                } else {
                    where = ClockContract.Alarm.ID + "=" + primaryKey + AND_STRING + where + ")";
                }
                count = db.delete(ClockDatabaseHelper.ALARMS_TABLE_NAME, where, whereArgs);
                break;
            case SCHEDULES:
                count = db.delete(ClockDatabaseHelper.SCHEDULES_TABLE_NAME, where, whereArgs);
                break;
            case SCHEDULES_ID:
                primaryKey = uri.getLastPathSegment();
                if (TextUtils.isEmpty(where)) {
                    where = ClockContract.Schedule._ID + "=" + primaryKey;
                } else {
                    where = ClockContract.Schedule._ID + "=" + primaryKey + AND_STRING + where + ")";
                }
                count = db.delete(ClockDatabaseHelper.SCHEDULES_TABLE_NAME, where, whereArgs);
                break;
            case CITIES:
            case CITIES_ID:
                Log.e(TAG, "The uri[" + uri + "] is not available anymore!");
                return 0;
            case NEW_CITIES:
                db = mCityDatabaseHelper.getWritableDatabase();
                count = db.delete(ClockDatabaseHelper.NEW_CITIES_TABLE_NAME, where, whereArgs);
                break;
            case TIMERS:
                count = db.delete(TimerTableCreator.TIMER_TABLE_NAME, where, null);
                break;

            case ALARM_REPEAT:
                count = db.delete(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME, where, whereArgs);
                break;

            case ALARM_REPEAT_ID:
                primaryKey = uri.getLastPathSegment();
                if (TextUtils.isEmpty(where)) {
                    where = ClockContract.AlarmsRepeat._ID + "=" + primaryKey;
                } else {
                    where = ClockContract.AlarmsRepeat._ID + "=" + primaryKey + AND_STRING + where + ")";
                }
                count = db.delete(ClockDatabaseHelper.ALARM_REPEAT_TABLE_NAME, where, whereArgs);
                break;
            case ALARM_HOLIDAY:
                count = db.delete(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME, where, whereArgs);
                break;

            case ALARM_HOLIDAY_ID:
                primaryKey = uri.getLastPathSegment();
                if (TextUtils.isEmpty(where)) {
                    where = ClockContract.AlarmHoliday._ID + "=" + primaryKey;
                } else {
                    where = ClockContract.AlarmHoliday._ID + "=" + primaryKey + AND_STRING + where + ")";
                }
                count = db.delete(ClockDatabaseHelper.ALARM_HOLIDAY_TABLE_NAME, where, whereArgs);
                break;
            case ALARM_RING:
                AlarmRingDatabaseUtils.deleteAlarmRing(db, where, whereArgs);
                break;
            case SETTINGS:
                count = db.delete(ClockDatabaseHelper.SETTING_TABLE_NAME, where, whereArgs);
                break;
            default:
                throw new IllegalArgumentException("Cannot delete from URI: " + uri);
        }

        boolean doNotify = shouldDoNotify(uri);
        if (doNotify) {
            Log.d(TAG, "delete notifyChange uri: " + uri);
            notifyBothProvider(uri);
        }
        return count;
    }

    private static ContentValues fixAlarmInsertValues(ContentValues initialValues) {
        if (initialValues == null) {
            return null;
        }

        ContentValues values = new ContentValues(initialValues);
        values.remove(ClockContract.Alarm.ID);

        if (!values.containsKey(ClockContract.Alarm.HOUR)) {
            values.put(ClockContract.Alarm.HOUR, 0);
        }

        if (!values.containsKey(ClockContract.Alarm.MINUTES)) {
            values.put(ClockContract.Alarm.MINUTES, 0);
        }

        if (!values.containsKey(ClockContract.Alarm.DAYS_OF_WEEK)) {
            values.put(ClockContract.Alarm.DAYS_OF_WEEK, 0);
        }

        if (!values.containsKey(ClockContract.Alarm.ALARM_TIME)) {
            values.put(ClockContract.Alarm.ALARM_TIME, 0);
        }

        if (!values.containsKey(ClockContract.Alarm.ENABLED)) {
            values.put(ClockContract.Alarm.ENABLED, 0);
        }

        if (!values.containsKey(ClockContract.Alarm.ALERTTYPE)) {
            if (!values.containsKey(ClockContract.Alarm.VIBRATE)) {
                values.put(ClockContract.Alarm.ALERTTYPE, 2);
                values.put(ClockContract.Alarm.VIBRATE, WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE);
            } else {
                Integer asInteger = values.getAsInteger(ClockContract.Alarm.VIBRATE);
                if ((asInteger != null) && (asInteger != WaveformEffect.EFFECT_RINGTONE_NOVIBRATE)) {
                    values.put(ClockContract.Alarm.ALERTTYPE, 2/* ring and vibrate */);
                } else {
                    values.put(ClockContract.Alarm.ALERTTYPE, 0/* ring only */);
                }
            }
        }

        if (!values.containsKey(ClockContract.Alarm.VIBRATE)) {
            values.put(ClockContract.Alarm.VIBRATE, WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE);
        }

        if (!values.containsKey(ClockContract.Alarm.MESSAGE)) {
            values.put(ClockContract.Alarm.MESSAGE, "");
        }

        if (!values.containsKey(ClockContract.Alarm.ALERT)) {
            values.put(ClockContract.Alarm.ALERT, "");
            values.put(ClockContract.Alarm.RING_ABSOLUTE_PATH, "");
        } else if (values.containsKey(ClockContract.Alarm.ALERT)) {
            Object obj = values.get(ClockContract.Alarm.ALERT);
            Uri alert = null;
            try {
                alert = Uri.parse(obj.toString());
            } catch (Exception e) {
                Log.e(TAG, "fixAlarmInsertValues parse uri error:" + e.getMessage());
            }
            values.put(ClockContract.Alarm.RING_ABSOLUTE_PATH,
                    AlarmRingUtils.getRingAbsolutePath(AlarmClockApplication.getInstance(), alert));
        }
        if (!values.containsKey(ClockContract.Alarm.ALERT_RINGNAME)) {
            values.put(ClockContract.Alarm.ALERT_RINGNAME, "");
        }
        if (!values.containsKey(ClockContract.Alarm.SNOOZE)) {
            values.put(ClockContract.Alarm.SNOOZE, 1);
        }
        if (!values.containsKey(ClockContract.Alarm.VOLUME)) {
            values.put(ClockContract.Alarm.VOLUME, DEFAULT_VOLUME);
        }
        if (!values.containsKey(ClockContract.Alarm.BACKGROUND)) {
            values.put(ClockContract.Alarm.BACKGROUND, "");
        }

        if (!values.containsKey(ClockContract.Alarm.DELETE_AFTER_USE)) {
            values.put(ClockContract.Alarm.DELETE_AFTER_USE, 0);
        }

        return values;
    }

    public static Uri buildUnNotifyUri(Uri baseUri) {
        if (baseUri == null) {
            return null;
        }
        return baseUri.buildUpon().appendQueryParameter(PARAM_NOTIFY, UNNOTIFY).build();
    }

    private static boolean shouldDoNotify(Uri uri) {
        String val = uri.getQueryParameter(PARAM_NOTIFY);
        if (!TextUtils.isEmpty(val)) {
            return !UNNOTIFY.equals(val);
        }
        return true;
    }

    private Uri replaceClockUri(Uri uri) {
        if (uri != null) {
            Uri newUri = Uri.parse(uri.toString().replace(ClockContract.AUTHORITY, ClockContract.AUTHORITY_OPLUS));
            Log.d(TAG, "replaceClockUri uri : " + uri + " newUri : " + newUri);
            if (uri.equals(newUri)) {
                return null;
            }
            return newUri;
        }
        return null;
    }

    private void notifyBothProvider(Uri uri) {
        ContentResolver cr = mContext.getContentResolver();
        cr.notifyChange(uri, null);
        if (VersionUtils.isOsVersion11_3()) {
            Uri oplusUri = replaceClockUri(uri);
            if (oplusUri != null) {
                cr.notifyChange(oplusUri, null);
            }
        }
    }

    private Handler getHandler() {
        if (mHandler == null) {
            mHandler = new Handler(Looper.getMainLooper());
        }
        return mHandler;
    }

}
