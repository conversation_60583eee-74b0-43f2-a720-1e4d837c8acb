/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockProvider2.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/2/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/2/1     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.provider;

import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.clock.common.utils.Log;

public class ClockProviderOPlus extends ClockProvider {

    private static final String TAG = "ClockProviderOPlus";

    private static final String METHOD_GET_WIDGET_COMPONENT_NAME = "getWidgetComponentName";

    private static final String PARA_WIDGET_COMPONENT = "widgetComponentName";
    private static final String PARA_RESULT = "success";

    private static final String WORLD_CLOCK_WIDGET_COMPONENT_NAME =
            "com.coloros.alarmclock/com.oplus.alarmclock.widget.DigitalAppWidgetProvider";
    private static final int RESULT_SUCCESS = 1;


    private Uri replaceOPlusUri(Uri uri) {
        if (uri != null) {
            Uri newUri = Uri.parse(uri.toString().replace(ClockContract.AUTHORITY_OPLUS, ClockContract.AUTHORITY));
            Log.d(TAG, "query uri : " + uri + " newUri : " + newUri);
            return newUri;
        }
        return null;
    }

    @Override
    public boolean onCreate() {
        //do nothing
        return true;
    }

    @Override
    public Cursor query(@NonNull Uri uri, String[] projectionIn, String selection, String[] selectionArgs,
                        String sort) {
        return getContext().getContentResolver().query(replaceOPlusUri(uri), projectionIn, selection, selectionArgs, sort);
    }

    @Override
    public String getType(@NonNull Uri uri) {
        return super.getType(replaceOPlusUri(uri));
    }

    @Override
    public int update(@NonNull Uri uri, ContentValues values, String where, String[] whereArgs) {
        return getContext().getContentResolver().update(replaceOPlusUri(uri), values, where, whereArgs);
    }

    @Override
    public Uri insert(@NonNull Uri uri, ContentValues initialValues) {
        return getContext().getContentResolver().insert(replaceOPlusUri(uri), initialValues);
    }

    @Override
    public int delete(@NonNull Uri uri, String where, String[] whereArgs) {
        return getContext().getContentResolver().delete(replaceOPlusUri(uri), where, whereArgs);
    }

    @Nullable
    @Override
    public Bundle call(@NonNull String method, @Nullable String arg, @Nullable Bundle extras) {

        Log.d(TAG, "call:" + method);
        Bundle result = new Bundle();
        if (METHOD_GET_WIDGET_COMPONENT_NAME.equals(method)) {
            result.putInt(PARA_RESULT, RESULT_SUCCESS);
            result.putString(PARA_WIDGET_COMPONENT, WORLD_CLOCK_WIDGET_COMPONENT_NAME);
        }

        return result;
    }
}
