/*******************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :The SQLiteOpenHelper.Helper class for opening the database from multiple providers.
 * Also provides some common functionality.
 * <p>
 * Date:2016-05-10 Author:Amy
 * Date:2018-07-06 Author:<PERSON><PERSON><PERSON>, Recode this class.
 *******************************************************/

package com.oplus.alarmclock.provider;

import static com.oplus.alarmclock.provider.ClockContract.Alarm.LOOP_ID;
import static com.oplus.alarmclock.timer.data.TimerTableCreator.TIMER_TABLE_NAME;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.text.TextUtils;

import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.WorkDayTypeUtils;
import com.oplus.alarmclock.appfunctions.ClockAppSearchManager;
import com.oplus.alarmclock.provider.ClockContract.AlarmsRepeat;
import com.oplus.alarmclock.provider.alarmring.AlarmRingDatabaseUtils;
import com.oplus.alarmclock.timer.data.TimerTableCreator;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.osdk.UserNativeUtils;
import com.oplus.clock.common.utils.Log;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ClockDatabaseHelper extends SQLiteOpenHelper {

    public static final String INTEGER_STRING = " INTEGER";
    public static final String INTEGER_DEFAULT_STRING = "  INTEGER  default ";
    public static final String ALTER_TABLE_STRING = "alter table ";
    public static final String ADD_TABLE_STRING = " add column ";

    //Old and new city table names.
    public static final String CITIES_TABLE_NAME = "cities";
    public static final String NEW_CITIES_TABLE_NAME = "all_cities";
    //alarm repeat table name
    public static final String ALARM_REPEAT_TABLE_NAME = "alarm_repeat";

    public static final String ALARM_HOLIDAY_TABLE_NAME = "alarm_holiday";

    public static final String SETTING_TABLE_NAME = "settings";

    //Database file name.
    static final String DATABASE_NAME = "alarms.db";
    //Tables names
    static final String ALARMS_TABLE_NAME = "alarms";
    static final String SCHEDULES_TABLE_NAME = "alarm_schedule";
    //View name
    static final String SCHEDULES_VIEW_NAME = "view_schedules";
    static final String PRIMARY_KEY = " INTEGER PRIMARY KEY AUTOINCREMENT, ";
    static final String FROM_STRING = " FROM ";

    private final static String TAG = "ClockDatabaseHelper";

    //Temp tables names.
    private static final String TMP_ALARMS_TABLE_NAME = "tmp_alarms";
    private static final String TMP_SCHEDULES_TABLE_NAME = "tmp_alarm_schedule";

    /**
     * Original Clock Database.
     **/
    @SuppressWarnings("unused")
    private static final int VERSION_5 = 5;
    /**
     * Introduce: Added alarm_instances table Added selected_cities table Added DELETE_AFTER_USE
     * column to alarms table
     */
    @SuppressWarnings("unused")
    private static final int VERSION_6 = 6;
    /**
     * Added alarm settings to instance table.
     */
    @SuppressWarnings("unused")
    private static final int VERSION_7 = 7;
    private static final int VERSION_9 = 9;
//    private static final int VERSION_10 = 10;
//    private static final int VERSION_11 = 11;
    // this version only update a lot of city names translation
//    private static final int VERSION_12 = 12;
    // update for city name sort in all languages
//    private static final int VERSION_13 = 13;
    // update city Merida id:
//    private static final int VERSION_14 = 14;
    // update city names for 17 countries:
//    private static final int VERSION_15 = 15;
    // update City of Chongqing :
//    private static final int VERSION_16 = 16;
    // update FirstSpell for Arabic :
//    private static final int VERSION_17 = 17;

    // update Country for taipei:
//    private static final int VERSION_18 = 18;

    // update some cities are not shown bug, CityDeletedIndex is imported wrong:
//    private static final int VERSION_19 = 19;

    // update city.xml , and visible:
//    private static final int VERSION_20 = 20;

    // add timer table.
    private static final int VERSION_21 = 21;

    // set alarm table primary key autoincrement and add schedule view.
    private static final int VERSION_22 = 22;

    //add workday switch and holiday switch in alarm table
    private static final int VERSION_23 = 23;

    //add alarm setting repeat attr table
    private static final int VERSION_24 = 24;

    //add alarm table ownerUserId for multi-user
    private static final int VERSION_25 = 25;

    //add alarm_holiday table
    private static final int VERSION_26 = 26;

    //add close once column
    private static final int VERSION_27 = 27;

    //add enableAssociate for iot
    private static final int VERSION_28 = 28;

    //add uuid for iot
    private static final int VERSION_29 = 29;

    //add diagnosis mode
    private static final int VERSION_30 = 30;

    /**
     * alarm表添加
     * snooze_time(稍后提醒时间)
     * workdayType(工作日类型)
     * workdayUpdateTime(工作日更新时间)
     * timer表添加
     * ring 铃声
     * ringName 铃声名称
     */
    private static final int VERSION_40 = 40;

    /**
     * 增加响铃次数
     */
    private static final int VERSION_41 = 41;
    /**
     * 修复BUG 4385003 搬家问题所留下的有问题闹钟修复
     */
    private static final int VERSION_42 = 42;
    /**
     * 新增轮班闹钟
     */
    private static final int VERSION_50 = 50;
    /**
     * add ringAbsolutePath
     */
    private static final int VERSION_51 = 51;
    /**
     * 新增设置表
     */
    private static final int VERSION_60 = 60;
    /**
     * 设置表EAP问题修改
     */
    private static final int VERSION_61 = 61;

    /**
     * 新增秒抢闹钟字段
     */
    private static final int VERSION_70 = 70;

    /**
     * 支持Gemini AppFunction功能
     */
    private static final int VERSION_71 = 71;

    /**
     * 新增铃声渐强开关
     */
    private static final int VERSION_72 = 72;
    private static final int VERSION = VERSION_72;
    private static final String[] PROJECTION = new String[]{
            ClockContract.Schedule.YEAR,
            ClockContract.Schedule.MONTH,
            ClockContract.Schedule.DAY,
            ClockContract.Schedule.HOUR,
            ClockContract.Schedule.MINUTES,
            ClockContract.Schedule.ALARM_TIME,
            ClockContract.Schedule.SNOOZETIME,
            ClockContract.Schedule.ALARM_STATE,
            ClockContract.Schedule.ALARM_ID
    };

    private static final int YEAR_INDEX = 0;
    private static final int MONTH_INDEX = 1;
    private static final int DAY_INDEX = 2;
    private static final int HOUR_INDEX = 3;
    private static final int MINUTES_INDEX = 4;
    private static final int ALARM_TIME_INDEX = 5;
    private static final int SNOOZETIME_INDEX = 6;
    private static final int ALARM_STATE_INDEX = 7;
    private static final int ALARM_ID_INDEX = 8;

    private static final String SCHEME_ALARMS_TABLE = " ("
            + ClockContract.Alarm.ID + PRIMARY_KEY
            + ClockContract.Alarm.HOUR + " INTEGER NOT NULL, "
            + ClockContract.Alarm.MINUTES + " INTEGER NOT NULL, "
            + ClockContract.Alarm.DAYS_OF_WEEK + " INTEGER, "
            + ClockContract.Alarm.ALARM_TIME + " INTEGER NOT NULL, "
            + ClockContract.Alarm.ENABLED + " INTEGER NOT NULL, "
            + ClockContract.Alarm.ALERTTYPE + " TEXT, "
            + ClockContract.Alarm.MESSAGE + " TEXT NOT NULL, "
            + ClockContract.Alarm.SNOOZE + " INTEGER, "
            + ClockContract.Alarm.ALERT + " TEXT, "
            + ClockContract.Alarm.ALERT_RINGNAME + " TEXT, "
            + ClockContract.Alarm.VOLUME + " INTEGER, "
            + ClockContract.Alarm.VIBRATE + " INTEGER, "
            + ClockContract.Alarm.BACKGROUND + " TEXT, "
            + ClockContract.Alarm.DELETE_AFTER_USE + " INTEGER, "
            + ClockContract.Alarm.WORKDAY_SWITCH + " INTEGER,"
            + ClockContract.Alarm.HOLIDAY_SWITCH + " INTEGER,"
            + ClockContract.Alarm.OWNER_USER_ID + " INTEGER,"
            + ClockContract.Alarm.CLOSE_ONCE_NEXT_TIME + " INTEGER,"
            + ClockContract.Alarm.CLOSE_ONCE_PREVIOUS_TIME + " INTEGER,"
            + ClockContract.Alarm.ENABLE_ASSOCIATE + " INTEGER,"
            + ClockContract.Alarm.ALARM_UUID + " TEXT,"
            + ClockContract.Alarm.SNOOZE_TIME + " INTEGER,"
            + ClockContract.Alarm.WORKDAY_TYPE + " INTEGER,"
            + ClockContract.Alarm.WORKDAY_UPDATE_TIME + " INTEGER,"
            + ClockContract.Alarm.SPECIAL_ALARM_DAYS + " TEXT,"
            + ClockContract.Alarm.DEFAULT_ALARM + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.RING_NUMBER + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_SWITCH + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_CYCLE_DAYS + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_ID + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_WORK_DAYS + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_ALARM_NUMBER + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_DAY + " " + INTEGER_STRING + " ,"
            + ClockContract.Alarm.LOOP_RESET_DAYS + " TEXT,"
            + ClockContract.Alarm.RING_ABSOLUTE_PATH + " TEXT,"
            + ClockContract.Alarm.GARB_ALARM_SWITCH + " " + INTEGER_STRING + " "
            + ");";

    private static final String SCHEME_SCHEDULE_TABLE = " ("
            + ClockContract.Schedule._ID + PRIMARY_KEY
            + ClockContract.Schedule.YEAR + " INTEGER NOT NULL, "
            + ClockContract.Schedule.MONTH + " INTEGER NOT NULL, "
            + ClockContract.Schedule.DAY + " INTEGER NOT NULL, "
            + ClockContract.Schedule.HOUR + " INTEGER NOT NULL, "
            + ClockContract.Schedule.MINUTES + " INTEGER NOT NULL, "
            + ClockContract.Schedule.ALARM_TIME + " INTEGER NOT NULL, "
            + ClockContract.Schedule.SNOOZETIME + " INTEGER, "
            + ClockContract.Schedule.ALARM_STATE + " INTEGER NOT NULL, "
            + ClockContract.Schedule.ALARM_ID + " INTEGER REFERENCES "
            + ALARMS_TABLE_NAME + "(" + ClockContract.Alarm.ID + ") "
            + "ON UPDATE CASCADE ON DELETE CASCADE" + ");";

    private static final String SQL_CREATE_ALARM_TABLE =
            "CREATE TABLE IF NOT EXISTS " + ALARMS_TABLE_NAME + SCHEME_ALARMS_TABLE;

    private static final String SQL_CREATE_SCHEDULE_TABLE =
            "CREATE TABLE IF NOT EXISTS " + SCHEDULES_TABLE_NAME + SCHEME_SCHEDULE_TABLE;

    private static final String SQL_CREATE_SCHEDULE_VIEW =
            "CREATE VIEW IF NOT EXISTS " + SCHEDULES_VIEW_NAME
                    + " AS SELECT "
                    + SCHEDULES_TABLE_NAME + "." + ClockContract.Schedule._ID + " AS " + ClockContract.Schedule._ID + ", "
                    + ClockContract.Schedule.YEAR + ", "
                    + ClockContract.Schedule.MONTH + ", "
                    + ClockContract.Schedule.DAY + ", "
                    + SCHEDULES_TABLE_NAME + "." + ClockContract.Schedule.HOUR + " AS " + ClockContract.Schedule.HOUR + ", "
                    + SCHEDULES_TABLE_NAME + "." + ClockContract.Schedule.MINUTES + " AS " + ClockContract.Schedule.MINUTES + ", "
                    + SCHEDULES_TABLE_NAME + "." + ClockContract.Schedule.ALARM_TIME + " AS " + ClockContract.Schedule.ALARM_TIME + ", "
                    + ClockContract.Schedule.SNOOZETIME + ", "
                    + ClockContract.Schedule.ALARM_STATE + ", "
                    + ClockContract.Schedule.ALARM_ID + ", "
                    + ALARMS_TABLE_NAME + "." + ClockContract.Alarm.HOUR + " AS " + ClockContract.ScheduleView.ALARM_HOUR + ", "
                    + ALARMS_TABLE_NAME + "." + ClockContract.Alarm.MINUTES + " AS " + ClockContract.ScheduleView.ALARM_MINUTE + ", "
                    //TODO: Check if this is needed.
                    + ALARMS_TABLE_NAME + "." + ClockContract.Alarm.ALARM_TIME + " AS " + ClockContract.ScheduleView.ALARM_MILLS + ", "
                    + ClockContract.Alarm.DAYS_OF_WEEK + ", "
                    + ClockContract.Alarm.ENABLED + ", "
                    + ClockContract.Alarm.ALERTTYPE + ", "
                    + ClockContract.Alarm.MESSAGE + ", "
                    + ClockContract.Alarm.SNOOZE + ", "
                    + ClockContract.Alarm.ALERT + ", "
                    + ClockContract.Alarm.ALERT_RINGNAME + ", "
                    + ClockContract.Alarm.VOLUME + ", "
                    + ClockContract.Alarm.VIBRATE + ", "
                    + ClockContract.Alarm.BACKGROUND + ", "
                    + ClockContract.Alarm.DELETE_AFTER_USE
                    + FROM_STRING + SCHEDULES_TABLE_NAME + " JOIN " + ALARMS_TABLE_NAME
                    + " ON (" + SCHEDULES_TABLE_NAME + "." + ClockContract.Schedule.ALARM_ID
                    + "=" + ALARMS_TABLE_NAME + "." + ClockContract.Alarm.ID + ");";

    private static final String SQL_CREATE_TMP_ALARM_TABLE =
            "CREATE TABLE " + TMP_ALARMS_TABLE_NAME + SCHEME_ALARMS_TABLE;

    private static final String SQL_CREATE_TMP_SCHEDULE_TABLE =
            "CREATE TABLE " + TMP_SCHEDULES_TABLE_NAME + SCHEME_SCHEDULE_TABLE;

    private static final String SQL_RENAME_TMP_ALARM_TABLE =
            "ALTER TABLE " + TMP_ALARMS_TABLE_NAME + " RENAME TO " + ALARMS_TABLE_NAME;

    private static final String SQL_RENAME_TMP_SCHEDULE_TABLE =
            "ALTER TABLE " + TMP_SCHEDULES_TABLE_NAME + " RENAME TO " + SCHEDULES_TABLE_NAME;

    private static final String SQL_INSERT_SCHEDULE =
            "INSERT INTO " + SCHEDULES_TABLE_NAME + " ("
                    + ClockContract.Schedule.YEAR + ", "
                    + ClockContract.Schedule.MONTH + ", "
                    + ClockContract.Schedule.DAY + ", "
                    + ClockContract.Schedule.HOUR + ", "
                    + ClockContract.Schedule.MINUTES + ", "
                    + ClockContract.Schedule.ALARM_TIME + ", "
                    + ClockContract.Schedule.SNOOZETIME + ", "
                    + ClockContract.Schedule.ALARM_STATE + ", "
                    + ClockContract.Schedule.ALARM_ID + ") "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);";


    private static final String ALARMS_REPEAT_TABLE = " ("
            + AlarmsRepeat._ID + PRIMARY_KEY
            + AlarmsRepeat.ALARM_DURATION + " INTEGER NOT NULL, "
            + AlarmsRepeat.ALARM_INTERVAL + " INTEGER NOT NULL, "
            + AlarmsRepeat.ALARM_NUM + " INTEGER NOT NULL, "
            + AlarmsRepeat.ALARM_PROMPT + " INTEGER"
            + ");";

    private static final String ALARMS_HOLIDAY_TABLE = " ("
            + ClockContract.AlarmHoliday._ID + PRIMARY_KEY
            + ClockContract.AlarmHoliday.YEAR + " TEXT NOT NULL, "
            + ClockContract.AlarmHoliday.YEAR_DAY + " TEXT NOT NULL, "
            + ClockContract.AlarmHoliday.LOCATION + " TEXT NOT NULL, "
            + ClockContract.AlarmHoliday.COLOR_TYPE + " INTEGER NOT NULL, "
            + ClockContract.AlarmHoliday.SIGN_TITLE + " TEXT NOT NULL"
            + ");";

    private static final String SETTING_TABLE = " ("
            + ClockContract.Settings._ID + PRIMARY_KEY
            + ClockContract.Settings.ALARM_CLOSE_MODEL + " INTEGER, "
            + ClockContract.Settings.ALARM_DEFAULT_RINGTONE + " TEXT, "
            + ClockContract.Settings.ALARM_DEFAULT_VIBRATE + " INTEGER, "
            + ClockContract.Settings.ALARM_EARLY_MORNING_NOTIFY + " INTEGER, "
            + ClockContract.Settings.ALARM_MORNING_REPORT + " INTEGER, "
            + ClockContract.Settings.ALARM_GRADUALLY_RINGS + " TEXT"
            + ");";

    private static final String SQL_CREATE_ALARM_REPEAT_TABLE =
            "CREATE TABLE IF NOT EXISTS " + ALARM_REPEAT_TABLE_NAME + ALARMS_REPEAT_TABLE;

    private static final String SQL_CREATE_ALARM_HOLIDAY_TABLE =
            "CREATE TABLE IF NOT EXISTS " + ALARM_HOLIDAY_TABLE_NAME + ALARMS_HOLIDAY_TABLE;

    private static final String SQL_CREATE_SETTING_TABLE =
            "CREATE TABLE IF NOT EXISTS " + SETTING_TABLE_NAME + SETTING_TABLE;

    private static final String SQL_UPDATE_ALARM_UUID =
            "UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.ALARM_UUID + " = ?"
                    + " WHERE "
                    + ClockContract.Alarm.ID + " = ?";

    /**
     * 更新旧版本数据库响铃次数
     */
    private static final String SQL_UPDATE_ALARM_RING_NUM =
            "UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.RING_NUMBER + " = ?";

    /**
     * 更新旧版本数据库稍后提醒时间
     */
    private static final String SQL_UPDATE_ALARM_SNOOZE_TIME =
            " UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.SNOOZE_TIME + " = ?";

    /**
     * 更新旧版版本数据库工作日类型
     */
    private static final String SQL_UPDATE_ALARM_WORKDAY_TYPE =
            " UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.WORKDAY_TYPE + " = ?";

    /**
     * 更新旧版本数据库工作日更新时间
     */
    private static final String SQL_UPDATE_ALARM_CREATE_TIME =
            " UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.WORKDAY_UPDATE_TIME + " = ?"
                    + " WHERE "
                    + ClockContract.Alarm.WORKDAY_SWITCH + " = 1";


    private static final String SQL_UPDATE_ALARM_SNOOZE_TIME_FIX =
            " UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.SNOOZE_TIME + " = " + ClockConstant.SNOOZE_AFTER_MIN
                    + " WHERE "
                    + ClockContract.Alarm.SNOOZE_TIME + " < ?";

    private static final String SQL_UPDATE_ALARM_RING_ABSOLUTE_PATH =
            "UPDATE " + ClockDatabaseHelper.ALARMS_TABLE_NAME
                    + " SET "
                    + ClockContract.Alarm.RING_ABSOLUTE_PATH + " = ?"
                    + " WHERE "
                    + ClockContract.Alarm.ID + " = ?";

    private Context mContext;

    public ClockDatabaseHelper(Context context) {
        super(Utils.getDeviceContext(context), DATABASE_NAME, null, VERSION);
        mContext = Utils.getDeviceContext(context);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "onCreate Alarm database.");
        try {
            bootstrapDB(db);
            TimerTableCreator.insertInitialTimers(db, mContext);
        } catch (SQLException e) {
            Log.e(TAG, "onCreate Exception: " + e.getMessage());
        }
    }

    private static void bootstrapDB(SQLiteDatabase db) {
        createAlarmsTable(db);
        createScheduleTable(db);
        createSchedulesView(db);
        createAlarmRepeatTable(db);
        createAlarmHolidayTable(db);
        createAlarmRingTable(db);
        createSettingsTable(db);
        TimerTableCreator.creatTimerTable(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int currentVersion) {
        Log.v(TAG, "Upgrade Alarm database from version " + oldVersion + " to " + currentVersion);
        int oldDataVersion = oldVersion;
        try {
            if (oldDataVersion < VERSION_9) {
                deleteScheduleTable(db);
                createScheduleTable(db);
                fixScheduleInfo(db);
                return;
            }

            if (oldDataVersion < VERSION_21) {
                TimerTableCreator.creatTimerTable(db);
                TimerTableCreator.insertInitialTimers(db, mContext);
                oldDataVersion = VERSION_21;
            }

            if (oldDataVersion == VERSION_21) {
                Log.d(TAG, "upgrading alarm from 21 and before to 22, add workday switch and holiday switch in alarm table");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + " add column workdaySwitch INTEGER");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + " add column holidaySwitch INTEGER");
                addScheduleView(db);
                oldDataVersion = VERSION_22;
            }

            if (oldDataVersion < VERSION_23) {
                addColumn(db);
                oldDataVersion = VERSION_23;
            }


            if (oldDataVersion < VERSION_24) {
                createAlarmRepeatTable(db);
                oldDataVersion = VERSION_24;
            }

            if (oldDataVersion < VERSION_25) {

                int userId = UserNativeUtils.getIdentifier();

                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + " add column ownerUserId INTEGER default '" + userId + "'");
                if (Utils.isAboveQ()) {
                    Log.d(TAG, "from before 24 update to 25 add ownerUserId,and default :" + userId
                            + ",current user:" + AppPlatformUtils.getCurrentUser());
                }
                oldDataVersion = VERSION_25;
            }

            if (oldDataVersion < VERSION_26) {
                createAlarmHolidayTable(db);
                Log.d(TAG, "from before 25 update to 26");
                //Must be added the oldVersion = the new version, otherwise the database data will be cleared !!!
                oldDataVersion = VERSION_26;
            }

            if (oldDataVersion < VERSION_27) {
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.CLOSE_ONCE_NEXT_TIME + INTEGER_STRING);
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.CLOSE_ONCE_PREVIOUS_TIME + INTEGER_STRING);
                Log.d(TAG, "from before 26 update to 27");
                //Must be added the oldVersion = the new version, otherwise the database data will be cleared !!!
                oldDataVersion = VERSION_27;
            }


            if (oldDataVersion < VERSION_28) {
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.ENABLE_ASSOCIATE + " INTEGER default 1");
                oldDataVersion = VERSION_28;
                Log.d(TAG, "from before 27 update to 28");
            }

            if (oldDataVersion < VERSION_29) {
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.ALARM_UUID + " TEXT");
                Log.d(TAG, "from before 28 update to 29");
                oldDataVersion = VERSION_29;
                upgradeAlarmUUID(db);
            }

            if (oldDataVersion < VERSION_30) {
                Log.d(TAG, "from before 29 update to 30");
                oldDataVersion = VERSION_30;
                createAlarmRingTable(db);
            }

            if (oldDataVersion < VERSION_40) {
                Log.d(TAG, "from before 30 update to 40");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.SNOOZE_TIME + INTEGER_DEFAULT_STRING + ClockConstant.SNOOZE_AFTER_MIN);
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.WORKDAY_TYPE + "  INTEGER ");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.WORKDAY_UPDATE_TIME + "  INTEGER  default -1 ");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.SPECIAL_ALARM_DAYS + "  TEXT   default  \"#\" ");
                if (!checkColumnExists(db, ALARMS_TABLE_NAME, ClockContract.Alarm.DEFAULT_ALARM)) {
                    db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                            + ClockContract.Alarm.DEFAULT_ALARM + "  INTEGER  default 0 ");
                }
                db.execSQL(ALTER_TABLE_STRING + TIMER_TABLE_NAME + " add column ring TEXT");
                db.execSQL(ALTER_TABLE_STRING + TIMER_TABLE_NAME + " add column ringName TEXT");
                oldDataVersion = VERSION_40;
                upgradeAlarmWorkDay(db);
                upgradeAlarmSnoozeTime(db);
                upgradeAlarmCreateTime(db);
            }

            if (oldDataVersion < VERSION_41) {
                Log.d(TAG, "from before 40 update to 41");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.RING_NUMBER + "  "
                        + INTEGER_DEFAULT_STRING + ClockConstant.SNOOZE_RING_NUM);
                oldDataVersion = VERSION_41;
                upgradeAlarmRingNum(db);
            }

            if (oldDataVersion < VERSION_42) {
                Log.d(TAG, "from before 41 update to 42");
                updateAlarmSnoozeTime(db);
                oldDataVersion = VERSION_42;
            }

            if (oldDataVersion < VERSION_50) {
                Log.d(TAG, "from before 42 update to 50");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.LOOP_SWITCH + "  INTEGER  default 0");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.LOOP_CYCLE_DAYS
                        + INTEGER_DEFAULT_STRING + ClockConstant.LOOP_DEFAULT_CYCLE_DAYS);
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + LOOP_ID + INTEGER_DEFAULT_STRING
                        + ClockConstant.LOOP_DEFAULT_ID);
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.LOOP_WORK_DAYS + INTEGER_DEFAULT_STRING
                        + ClockConstant.LOOP_DEFAULT_WORK_DAYS);
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.LOOP_ALARM_NUMBER + "  INTEGER  default 1");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.LOOP_DAY + "  INTEGER  default 1");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.LOOP_RESET_DAYS + "  TEXT  default \"#\" ");
                oldDataVersion = VERSION_50;
            }

            if (oldDataVersion < VERSION_51) {
                Log.d(TAG, "from before 50 update to 51");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING + ClockContract.Alarm.RING_ABSOLUTE_PATH + "  TEXT");
                oldDataVersion = VERSION_51;
                upgradeRingAbsolutePath(db);
            }

            if (oldDataVersion < VERSION_60) {
                createSettingsTable(db);
                oldDataVersion = VERSION_60;
            }

            if (oldDataVersion < VERSION_61) {
                Log.d(TAG, "reset setting table with eap");
                resetSettingsTable(db);
                oldDataVersion = VERSION_61;
            }

            if (oldDataVersion < VERSION_70) {
                Log.d(TAG, "from before 62 update to 70");
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + ADD_TABLE_STRING
                        + ClockContract.Alarm.GARB_ALARM_SWITCH + "  INTEGER  default 0");
                oldDataVersion = VERSION_70;
            }

            if (oldDataVersion < VERSION_71) {
                Log.d(TAG, "from before 70 update to 71");
                oldDataVersion = VERSION_71;
                ExecutorService executor = Executors.newSingleThreadExecutor();
                executor.execute(() -> {
                    if (mContext != null) {
                        ClockAppSearchManager.initAlarms(mContext, false);
                    }
                });
            }

            if (oldDataVersion < VERSION_72) {
                Log.d(TAG, "from before 71 update to 72");
                if (!isColumnExist(db, SETTING_TABLE_NAME, ClockContract.Settings.ALARM_GRADUALLY_RINGS)) {
                    db.execSQL(ALTER_TABLE_STRING + SETTING_TABLE_NAME + ADD_TABLE_STRING
                            + ClockContract.Settings.ALARM_GRADUALLY_RINGS + " TEXT");
                } else {
                    Log.d(TAG, "column " + ClockContract.Settings.ALARM_GRADUALLY_RINGS + " already exists");
                }
                oldDataVersion = VERSION_72;
            }

            if (oldDataVersion != VERSION) {
                recreateDb(db);
            }
        } catch (SQLiteException e) {
            Log.e(TAG, "Upgrading alarms database failed: " + e.getMessage());
            // Recreate database.
            recreateDb(db);
        }
    }

    /**
     * 修复稍后提醒脏数据
     *
     * @param db
     */
    private void updateAlarmSnoozeTime(SQLiteDatabase db) {
        Integer[] values = new Integer[1];
        //修复稍后提醒为-1的闹钟
        values[0] = ClockConstant.SNOOZE_AFTER_MIN;
        updateAlarmData(db, values, SQL_UPDATE_ALARM_SNOOZE_TIME_FIX);
    }

    /**
     * 存量闹钟赋值铃声绝对路径
     *
     * @param db
     */
    private void upgradeRingAbsolutePath(SQLiteDatabase db) {
        Map<Long, String> data = new HashMap<>();
        //获取存量闹钟alert
        Cursor cursor = db.rawQuery(" SELECT " + ClockContract.Alarm.ID + ", "
                + ClockContract.Alarm.ALERT + FROM_STRING + ALARMS_TABLE_NAME, null);
        if ((cursor != null) && (cursor.getCount() > 0) && (cursor.getColumnIndex(ClockContract.Alarm.ALERT) != -1)) {
            while (cursor.moveToNext()) {
                long alarmId = cursor.getLong(0);
                String alert = cursor.getString(1);
                if (AlarmRingUtils.isMediaRingtone(alert)) {
                    Log.i(TAG, "upgradeRingAbsolutePath: " + alert + ", alarmId:" + alarmId);
                    data.put(alarmId, alert);
                }
            }
        }
        if (cursor != null) {
            cursor.close();
        }
        if (data.isEmpty()) {
            return;
        }
        String[] values = new String[2];
        try {
            db.beginTransaction();
            for (Map.Entry<Long, String> set : data.entrySet()) {
                values[0] = AlarmRingUtils.getMusicPathFromUriString(mContext, set.getValue());
                values[1] = String.valueOf(set.getKey());
                db.execSQL(SQL_UPDATE_ALARM_RING_ABSOLUTE_PATH, values);
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "upgradeRingAbsolutePath error:" + e);
        } finally {
            db.endTransaction();
        }
    }

    /**
     * 判断表中的某列是否存在
     *
     * @param db
     * @param tableName
     * @param columnName
     * @return
     */
    private boolean checkColumnExists(SQLiteDatabase db, String tableName, String columnName) {
        Cursor cursor = db.rawQuery("select * from sqlite_master where name = ? and sql like ?", new String[]{tableName, "%" + columnName + "%"});
        boolean result = (null != cursor) && (cursor.moveToFirst());
        if ((null != cursor)) {
            cursor.close();
        }
        Log.e(TAG, " columnName: " + columnName + " Exists: " + result);
        return result;
    }

    private static void upgradeAlarmUUID(SQLiteDatabase db) {
        try {
            HashMap<Long, String> updateData = new HashMap();

            Cursor cursor = db.rawQuery("SELECT " + ClockContract.Alarm.ALARM_UUID + ", "
                    + ClockContract.Alarm.ID + FROM_STRING + ALARMS_TABLE_NAME, null);
            if ((null != cursor) && (cursor.getCount() > 0) && (cursor.getColumnIndex(ClockContract.Alarm.ALARM_UUID) != -1)) {
                while (cursor.moveToNext()) {
                    String uuid = cursor.getString(0);
                    long alarmId = cursor.getLong(1);
                    Log.i(TAG, "upgradeAlarmUUID: " + uuid + ",alarmId:" + alarmId);
                    if (TextUtils.isEmpty(uuid)) {
                        updateData.put(alarmId, UUID.randomUUID().toString().replace("-", ""));
                    }
                }
            }
            if (cursor != null) {
                cursor.close();
            }

            if (!updateData.isEmpty()) {
                handleUpgradeAlarmUUID(db, updateData);
            }
        } catch (Exception e) {
            Log.e(TAG, "upgradeAlarmUUID error:" + e);
        }

    }

    private static void handleUpgradeAlarmUUID(SQLiteDatabase db, HashMap<Long, String> data) {

        String[] values = new String[2];

        long start = System.currentTimeMillis();
        try {
            db.beginTransaction();

            for (Map.Entry<Long, String> set : data.entrySet()) {
                values[0] = set.getValue();
                values[1] = String.valueOf(set.getKey());
                db.execSQL(SQL_UPDATE_ALARM_UUID, values);
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "handUpgradeAlarmUUID error:" + e);
        } finally {
            if (db != null) {
                db.endTransaction();
            }
        }
        long end = System.currentTimeMillis();
        Log.d(TAG, "handUpgradeAlarmUUID cost: " + (end - start) + " ms.");
    }

    private synchronized void addColumn(SQLiteDatabase db) {

        if (!isColumnExist(db, ALARMS_TABLE_NAME, "workdaySwitch")) {
            Log.d(TAG, "upgrading alarm from 22 and before to 23, add holiday switch in alarm table");
            try {
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + " add column workdaySwitch INTEGER");
            } catch (Exception e) {
                Log.e(TAG, "add column workdayswitch exception :" + e.getMessage());
            }

        } else {
            Log.d(TAG, "workdaySwitch is exist");
        }

        if (!isColumnExist(db, ALARMS_TABLE_NAME, "holidaySwitch")) {
            Log.d(TAG, ALARMS_TABLE_NAME + " table have no column  holidaySwitch  so  add holidaySwitch in alarm table");
            try {
                db.execSQL(ALTER_TABLE_STRING + ALARMS_TABLE_NAME + " add column holidaySwitch INTEGER");
            } catch (Exception e) {
                Log.e(TAG, "add column holidaySwitch exception :" + e.getMessage());
            }
        } else {
            Log.d(TAG, "holidaySwitch is exist");
        }

    }

    private boolean isColumnExist(SQLiteDatabase db, String tableName, String columnName) {
        boolean result = false;
        Cursor cursor = null;
        try {
            cursor = db.rawQuery("SELECT * FROM " + tableName + " LIMIT 0", null);
            if ((null != cursor) && (cursor.getColumnIndex(columnName) != -1)) {
                result = true;
            }
        } catch (Exception e) {
            Log.e(TAG, "isColumnExist error: " + e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
            }
        }
        return result;
    }

    private void recreateDb(SQLiteDatabase db) {
        Log.e(TAG, "recreateDb");
        deleteAlarmsTable(db);
        deleteScheduleTable(db);
        deleteScheduleView(db);
        deleteTimerTable(db);
        deleteAlarmRepeatTable(db);
        deleteAlarmHolidayTable(db);
        deleteAlarmRingTable(db);
        deleteSettingsTable(db);
        onCreate(db);
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.v(TAG, "Downgrade alarms database from version " + oldVersion + " to " + newVersion
                + ", which will destroy all old data");
        try {
            recreateDb(db);
        } catch (SQLException e) {
            Log.e(TAG, "onDowngrade Exception: " + e.getMessage());
        }
    }


    /**
     * 更新旧数据的workdayUpdateTime字段
     *
     * @param db
     */
    private void upgradeAlarmCreateTime(SQLiteDatabase db) {
        Log.i(TAG, "upgradeAlarmCreateTime");
        Long[] values = new Long[1];
        long setWorkdayTime = WorkDayTypeUtils.getSetWorkDayTypeTime(mContext);
        values[0] = setWorkdayTime;
        updateAlarmData(db, values, SQL_UPDATE_ALARM_CREATE_TIME);
    }

    /**
     * 更新旧数据的work_day字段
     *
     * @param db
     */
    private void upgradeAlarmWorkDay(SQLiteDatabase db) {
        Log.i(TAG, "upgradeAlarmWorkDay");
        Integer[] values = new Integer[1];
        int workdayType = WorkDayTypeUtils.getSetWorkDayType(mContext);
        values[0] = workdayType;
        updateAlarmData(db, values, SQL_UPDATE_ALARM_WORKDAY_TYPE);
    }

    /**
     * 更新旧数据库的snooze_time字段
     *
     * @param db
     */
    private void upgradeAlarmSnoozeTime(SQLiteDatabase db) {
        Log.i(TAG, "upgradeAlarmSnoozeTime");
        Integer[] values = new Integer[1];
        //默认稍后提醒为5分钟
        values[0] = ClockConstant.SNOOZE_AFTER_MIN;
        //获取旧版本闹钟系统设置稍后提醒时间
        Cursor cursor = db.rawQuery(" SELECT " + AlarmsRepeat.ALARM_INTERVAL + FROM_STRING + ALARM_REPEAT_TABLE_NAME + " LIMIT 1", null);
        if ((cursor != null) && (cursor.getCount() > 0) && (cursor.getColumnIndex(AlarmsRepeat.ALARM_INTERVAL) != -1)) {
            while (cursor.moveToNext()) {
                values[0] = cursor.getInt(0);
            }
        }
        if (cursor != null) {
            cursor.close();
        }
        updateAlarmData(db, values, SQL_UPDATE_ALARM_SNOOZE_TIME);
    }

    /**
     * 更新旧数据的响铃次数
     *
     * @param db
     */
    private void upgradeAlarmRingNum(SQLiteDatabase db) {
        Log.i(TAG, "upgradeAlarmRingNum");
        Integer[] values = new Integer[1];
        //默认响铃次数为3次
        values[0] = ClockConstant.SNOOZE_RING_NUM;
        //获取旧版本闹钟系统设置的响铃次数
        Cursor cursor = db.rawQuery(" SELECT " + AlarmsRepeat.ALARM_NUM + FROM_STRING + ALARM_REPEAT_TABLE_NAME + " LIMIT 1", null);
        if ((cursor != null) && (cursor.getCount() > 0) && (cursor.getColumnIndex(AlarmsRepeat.ALARM_NUM) != -1)) {
            while (cursor.moveToNext()) {
                int ringNum = cursor.getInt(0);
                if (ringNum == 1) {
                    // OS13.0响铃次数取消1次，如果为1次则修改为2次
                    ringNum = 2;
                }
                values[0] = ringNum;
            }
        }
        if (cursor != null) {
            cursor.close();
        }
        updateAlarmData(db, values, SQL_UPDATE_ALARM_RING_NUM);
    }

    /**
     * 更新数据
     *
     * @param db
     * @param bindArgs
     */
    private void updateAlarmData(SQLiteDatabase db, Object[] bindArgs, String sql) {
        try {
            db.beginTransaction();
            db.execSQL(sql, bindArgs);
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.e(TAG, "updateAlarmData error:" + e);
        } finally {
            if (db != null) {
                db.endTransaction();
            }
        }
    }

    private static void createAlarmsTable(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_ALARM_TABLE);
    }

    private static void createScheduleTable(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_SCHEDULE_TABLE);
    }

    private static void createSchedulesView(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_SCHEDULE_VIEW);
    }

    private static void createAlarmRepeatTable(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_ALARM_REPEAT_TABLE);
    }

    private static void createAlarmHolidayTable(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_ALARM_HOLIDAY_TABLE);
    }

    private static void createAlarmRingTable(SQLiteDatabase db) {
        AlarmRingDatabaseUtils.createAlarmRingTable(db);
    }

    private static void createSettingsTable(SQLiteDatabase db) {
        execSql(db, SQL_CREATE_SETTING_TABLE);
        try {
            ContentValues values = new ContentValues();
            values.put(ClockContract.Settings._ID, 0);
            db.insert(SETTING_TABLE_NAME, null, values);
        } catch (Exception e) {
            Log.e(TAG, "createSettingsTable insert default setting failed:" + e.getMessage());
        }
    }

    private static void deleteAlarmRingTable(SQLiteDatabase db) {
        AlarmRingDatabaseUtils.deleteAlarmRingTable(db);
    }

    private static void deleteAlarmsTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + ALARMS_TABLE_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteScheduleTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + SCHEDULES_TABLE_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteScheduleView(SQLiteDatabase db) {
        String sql = "DROP VIEW IF EXISTS " + SCHEDULES_VIEW_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteTimerTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + TIMER_TABLE_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteAlarmRepeatTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + ALARM_REPEAT_TABLE_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteAlarmHolidayTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + ALARM_HOLIDAY_TABLE_NAME + ";";
        execSql(db, sql);
    }

    private static void deleteSettingsTable(SQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS " + SETTING_TABLE_NAME + ";";
        execSql(db, sql);
    }

    static void deleteCitiesTable(SQLiteDatabase db) {
        try {
            String sql = "DROP TABLE IF EXISTS " + CITIES_TABLE_NAME + ";";
            execSql(db, sql);
        } catch (SQLException e) {
            Log.e(TAG, "deleteCitiesTable Exception: " + e.getMessage());
        }
    }

    private static void execSql(SQLiteDatabase db, String sql) {
        if ((db != null) && (!TextUtils.isEmpty(sql))) {
            db.execSQL(sql);
        }
    }

    private static void addScheduleView(SQLiteDatabase db) {
        transferAlarmsData(db);
    }

    //If there are alarms in the table, then:
    //1.load from alarms and schedules tables.
    //2.create temp alarms and schedules tables.
    //3.insert alarms loaded into the temp tables.
    //4.delete alarms and schedules tables.
    //5.rename temp tables.
    private static void transferAlarmsData(SQLiteDatabase db) {
        ArrayList<AlarmRecord> list = loadAllAlarms(db);
        Log.d(TAG, "transferAlarmsData count: " + list.size());
        if (list.isEmpty()) {
            //No alarms, just delete old tables and create new ones.
            deleteAlarmsTable(db);
            deleteScheduleTable(db);
            createAlarmsTable(db);
            createScheduleTable(db);
            createSchedulesView(db);
        } else {
            //Create temp tables.
            execSql(db, SQL_CREATE_TMP_ALARM_TABLE);
            execSql(db, SQL_CREATE_TMP_SCHEDULE_TABLE);
            insertAlarmsToTmpTables(db, list);
            deleteAlarmsTable(db);
            deleteScheduleTable(db);
            renameTmpTables(db);

            //Add schedule view.
            createSchedulesView(db);
        }
    }

    private static void renameTmpTables(SQLiteDatabase db) {
        execSql(db, SQL_RENAME_TMP_ALARM_TABLE);
        execSql(db, SQL_RENAME_TMP_SCHEDULE_TABLE);
    }

    private static void fixScheduleInfo(SQLiteDatabase db) {
        List<Alarm> list = new ArrayList<>();

        Cursor cursor = null;
        try {
            cursor = db.query(ALARMS_TABLE_NAME, AlarmContract.INSTANCE.getQUERY_COLUMNS(),
                    ClockContract.Alarm.ENABLED + "=1", null, null, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    list.add(AlarmUtils.createAlarmFromCur(cursor, null));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        try {
            if (!list.isEmpty()) {
                final Calendar now = Calendar.getInstance();
                String[] args = new String[9];
                for (Alarm alarm : list) {
                    if (alarm.getTime() != 0) {
                        now.setTimeInMillis(alarm.getTime());
                    }
                    int year = now.get(Calendar.YEAR);
                    int month = now.get(Calendar.MONTH);
                    int day = now.get(Calendar.DAY_OF_MONTH);

                    args[0] = String.valueOf(year);
                    args[1] = String.valueOf(month);
                    args[2] = String.valueOf(day);
                    args[3] = String.valueOf(alarm.getHour());
                    args[4] = String.valueOf(alarm.getMinutes());
                    args[5] = String.valueOf(alarm.getTime());
                    args[6] = "0";
                    args[7] = "0";
                    args[8] = String.valueOf(alarm.getId());
                    db.execSQL(SQL_INSERT_SCHEDULE, args);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void resetSettingsTable(SQLiteDatabase db) {
        Cursor cursor = null;
        ContentValues values = null;
        try {
            cursor = db.query(SETTING_TABLE_NAME, ClockContract.Settings.QUERY_COLUMNS,
                    ClockContract.Settings._ID + " IS NOT NULL", null, null, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                if (cursor.moveToFirst()) {
                    values = new ContentValues();
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_ALARM_CLOSE_MODEL)) {
                        values.put(ClockContract.Settings.ALARM_CLOSE_MODEL,
                                cursor.getInt(ClockContract.Settings.COLUMN_ALARM_CLOSE_MODEL));
                    }
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_ALARM_DEFAULT_RINGTONE)) {
                        values.put(ClockContract.Settings.ALARM_DEFAULT_RINGTONE,
                                cursor.getString(ClockContract.Settings.COLUMN_ALARM_DEFAULT_RINGTONE));
                    }
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_ALARM_DEFAULT_VIBRATE)) {
                        values.put(ClockContract.Settings.ALARM_DEFAULT_VIBRATE,
                                cursor.getInt(ClockContract.Settings.COLUMN_ALARM_DEFAULT_VIBRATE));
                    }
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_EARLY_MORNING_NOTIFY)) {
                        values.put(ClockContract.Settings.ALARM_EARLY_MORNING_NOTIFY,
                                cursor.getInt(ClockContract.Settings.COLUMN_EARLY_MORNING_NOTIFY));
                    }
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_MORNING_REPORT)) {
                        values.put(ClockContract.Settings.ALARM_MORNING_REPORT,
                                cursor.getInt(ClockContract.Settings.COLUMN_MORNING_REPORT));
                    }
                    if (!cursor.isNull(ClockContract.Settings.COLUMN_GRADUALLY_RINGS)) {
                        values.put(ClockContract.Settings.ALARM_GRADUALLY_RINGS,
                                cursor.getString(ClockContract.Settings.COLUMN_GRADUALLY_RINGS));
                    }
                }
                cursor.close();
            }
            db.delete(SETTING_TABLE_NAME, null, null);
            if (values == null) {
                values = new ContentValues();
            }
            values.put(ClockContract.Settings._ID, 0);
            db.insert(SETTING_TABLE_NAME, null, values);
        } catch (Exception e) {
            Log.e(TAG, "resetSettingsTable failed:" + e.getMessage());
            try {
                db.delete(SETTING_TABLE_NAME, null, null);
                ContentValues defValues = new ContentValues();
                defValues.put(ClockContract.Settings._ID, 0);
                db.insert(SETTING_TABLE_NAME, null, defValues);
            } catch (Exception e1) {
                Log.e(TAG, "resetSettingsTable default failed:" + e1.getMessage());
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    private static class ScheduleRecord {
        int mYear;
        int mMonth;
        int mDay;
        int mHour;
        int mMinutes;
        long mAlarmtime;
        int mSnoozeTime;
        int mAlarmState;
        long mAlarmId;
    }

    private static class AlarmRecord {
        long mId;
        int mHour;
        int mMinutes;
        int mDaysofweek;
        long mAlarmtime;
        int mEnabled;
        String mAlerttype;
        String mMessage;
        int mSnooze;
        String mAlert;
        String mRingName;
        int mVolume;
        String mBackGround;
        int mDeleteAfterUse;
        int mVibrate;

        List<ScheduleRecord> mScheduleRecords;

        AlarmRecord() {
            mScheduleRecords = new ArrayList<>();
        }

        void addSchedule(ScheduleRecord schedule) {
            mScheduleRecords.add(schedule);
        }
    }

    private static ArrayList<AlarmRecord> loadAllAlarms(SQLiteDatabase db) {
        ArrayList<AlarmRecord> alarmList = getAllAlarms(db);
        if (!alarmList.isEmpty()) {
            ArrayList<ScheduleRecord> scheduleList = getAllSchedules(db);
            if (!scheduleList.isEmpty()) {
                for (ScheduleRecord record : scheduleList) {
                    AlarmRecord target = getAlarmRecordById(alarmList, record.mAlarmId);
                    if (target != null) {
                        target.addSchedule(record);
                    }
                }
            }
        }
        return alarmList;
    }

    private static void insertAlarmsToTmpTables(SQLiteDatabase db, ArrayList<AlarmRecord> list) {
        if ((db != null) && (list != null) && (!list.isEmpty())) {
            try {
                db.beginTransaction();
                for (AlarmRecord record : list) {
                    ContentValues values = toValues(record);
                    if (values != null) {
                        long id = db.insert(TMP_ALARMS_TABLE_NAME, null, values);
                        if (id != -1) {
                            if (!record.mScheduleRecords.isEmpty()) {
                                for (ScheduleRecord r : record.mScheduleRecords) {
                                    values = toValues(r, id);
                                    if (values != null) {
                                        db.insert(TMP_SCHEDULES_TABLE_NAME, null, values);
                                    }
                                }
                            }
                        }
                    }
                }
                db.setTransactionSuccessful();
            } catch (Exception e) {
                Log.d(TAG, "insertAlarmsToTmpTables Exception: " + e.getMessage());
            } finally {
                db.endTransaction();
            }
        }
    }

    private static AlarmRecord getAlarmRecordById(ArrayList<AlarmRecord> list, long id) {
        AlarmRecord target = null;
        if ((list != null) && (!list.isEmpty())) {
            for (AlarmRecord record : list) {
                if (record.mId == id) {
                    target = record;
                    break;
                }
            }
        }
        return target;
    }

    private static ArrayList<AlarmRecord> getAllAlarms(SQLiteDatabase db) {
        ArrayList<AlarmRecord> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = db.query(ALARMS_TABLE_NAME, AlarmContract.INSTANCE.getQUERY_COLUMNS(),
                    null, null, null, null, ClockContract.Alarm.ID + " ASC");
            if (cursor != null) {
                AlarmRecord alarmRecord = null;
                while (cursor.moveToNext()) {
                    alarmRecord = new AlarmRecord();
                    alarmRecord.mId = cursor.getLong(ClockContract.Alarm.ALARM_ID_INDEX);
                    alarmRecord.mHour = cursor.getInt(ClockContract.Alarm.ALARM_HOUR_INDEX);
                    alarmRecord.mMinutes = cursor.getInt(ClockContract.Alarm.ALARM_MINUTES_INDEX);
                    alarmRecord.mDaysofweek = cursor.getInt(ClockContract.Alarm.ALARM_DAYS_OF_WEEK_INDEX);
                    alarmRecord.mAlarmtime = cursor.getLong(ClockContract.Alarm.ALARM_TIME_INDEX);
                    alarmRecord.mEnabled = cursor.getInt(ClockContract.Alarm.ALARM_ENABLED_INDEX);
                    alarmRecord.mAlerttype = cursor.getString(ClockContract.Alarm.ALARM_ALERTTYPE_INDEX);
                    alarmRecord.mMessage = cursor.getString(ClockContract.Alarm.ALARM_MESSAGE_INDEX);
                    if (alarmRecord.mMessage == null) {
                        alarmRecord.mMessage = "";
                    }
                    alarmRecord.mSnooze = cursor.getInt(ClockContract.Alarm.ALARM_SOONZE_INDEX);
                    alarmRecord.mAlert = cursor.getString(ClockContract.Alarm.ALARM_ALERT_INDEX);
                    alarmRecord.mRingName = cursor.getString(ClockContract.Alarm.ALARM_ALERT_RINGNAME_INDEX);
                    alarmRecord.mVolume = cursor.getInt(ClockContract.Alarm.ALARM_VOLUME_INDEX);
                    alarmRecord.mBackGround = cursor.getString(ClockContract.Alarm.ALARM_BACK_GROUND_INDEX);
                    alarmRecord.mDeleteAfterUse = cursor.getInt(ClockContract.Alarm.ALARM_DELETE_AFTER_USE_INDEX);
                    alarmRecord.mVibrate = cursor.getInt(ClockContract.Alarm.ALARM_VIBRATE_INDEX);
                    list.add(alarmRecord);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getAllAlarms error: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return list;
    }

    private static ArrayList<ScheduleRecord> getAllSchedules(SQLiteDatabase db) {
        ArrayList<ScheduleRecord> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = db.query(SCHEDULES_TABLE_NAME, PROJECTION,
                    null, null, null, null, ClockContract.Schedule.ALARM_ID + " ASC");
            if (cursor != null) {
                ScheduleRecord record = null;
                while (cursor.moveToNext()) {
                    record = new ScheduleRecord();
                    record.mYear = cursor.getInt(YEAR_INDEX);
                    record.mMonth = cursor.getInt(MONTH_INDEX);
                    record.mDay = cursor.getInt(DAY_INDEX);
                    record.mHour = cursor.getInt(HOUR_INDEX);
                    record.mMinutes = cursor.getInt(MINUTES_INDEX);
                    record.mAlarmtime = cursor.getLong(ALARM_TIME_INDEX);
                    record.mSnoozeTime = cursor.getInt(SNOOZETIME_INDEX);
                    record.mAlarmState = cursor.getInt(ALARM_STATE_INDEX);
                    record.mAlarmId = cursor.getLong(ALARM_ID_INDEX);
                    list.add(record);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getAllAlarms error: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return list;
    }

    private static ContentValues toValues(AlarmRecord record) {
        ContentValues values = null;
        if (record != null) {
            values = new ContentValues();
            values.put(ClockContract.Alarm.HOUR, record.mHour);
            values.put(ClockContract.Alarm.MINUTES, record.mMinutes);
            values.put(ClockContract.Alarm.DAYS_OF_WEEK, record.mDaysofweek);
            values.put(ClockContract.Alarm.ALARM_TIME, record.mAlarmtime);
            values.put(ClockContract.Alarm.ENABLED, record.mEnabled);
            values.put(ClockContract.Alarm.ALERTTYPE, record.mAlerttype);
            values.put(ClockContract.Alarm.MESSAGE, record.mMessage);
            values.put(ClockContract.Alarm.SNOOZE, record.mSnooze);
            values.put(ClockContract.Alarm.ALERT, record.mAlert);
            values.put(ClockContract.Alarm.ALERT_RINGNAME, record.mRingName);
            values.put(ClockContract.Alarm.VOLUME, record.mVolume);
            values.put(ClockContract.Alarm.BACKGROUND, record.mBackGround);
            values.put(ClockContract.Alarm.DELETE_AFTER_USE, record.mDeleteAfterUse);
        }
        return values;
    }

    private static ContentValues toValues(ScheduleRecord record, long alarmId) {
        ContentValues values = null;
        if (record != null) {
            values = new ContentValues();
            values.put(ClockContract.Schedule.YEAR, record.mYear);
            values.put(ClockContract.Schedule.MONTH, record.mMonth);
            values.put(ClockContract.Schedule.DAY, record.mDay);
            values.put(ClockContract.Schedule.HOUR, record.mHour);
            values.put(ClockContract.Schedule.MINUTES, record.mMinutes);
            values.put(ClockContract.Schedule.ALARM_TIME, record.mAlarmtime);
            values.put(ClockContract.Schedule.SNOOZETIME, record.mSnoozeTime);
            values.put(ClockContract.Schedule.ALARM_STATE, record.mAlarmState);
            values.put(ClockContract.Schedule.ALARM_ID, alarmId);
        }
        return values;
    }


    private static ContentValues toValues(AlarmRepeat repeat) {
        ContentValues values = null;
        if (repeat != null) {
            values = new ContentValues();
            if (repeat.getmId() > 0) {
                values.put(AlarmsRepeat._ID, repeat.getmId());
            }
            values.put(AlarmsRepeat.ALARM_DURATION, repeat.getmAlarmDuration());
            values.put(AlarmsRepeat.ALARM_INTERVAL, repeat.getmAlarmInterval());
            values.put(AlarmsRepeat.ALARM_NUM, repeat.getmAlarmNum());
            values.put(AlarmsRepeat.ALARM_PROMPT, repeat.getmAlarmPrompt());
        }
        return values;
    }

}
