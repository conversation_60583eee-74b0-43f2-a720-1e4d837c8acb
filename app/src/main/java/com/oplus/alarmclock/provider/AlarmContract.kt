/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmContract.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/4
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2024/1/4     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.provider

object AlarmContract {
    val QUERY_COLUMNS = arrayOf(
            ClockContract.Alarm.ID,
            ClockContract.Alarm.HOUR,
            ClockContract.Alarm.MINUTES,
            ClockContract.Alarm.DAYS_OF_WEEK,
            ClockContract.Alarm.ALARM_TIME,
            ClockContract.Alarm.ENABLED,
            ClockContract.Alarm.ALERTTYPE,
            ClockContract.Alarm.MESSAGE,
            ClockContract.Alarm.SNOOZE,
            ClockContract.Alarm.ALERT,
            ClockContract.Alarm.ALERT_RINGNAME,
            ClockContract.Alarm.VOLUME,
            ClockContract.Alarm.BACKGROUND,
            ClockContract.Alarm.DELETE_AFTER_USE,
            ClockContract.Alarm.VIBRATE,
            ClockContract.Alarm.WORKDAY_SWITCH,
            ClockContract.Alarm.HOLIDAY_SWITCH,
            ClockContract.Alarm.OWNER_USER_ID,
            ClockContract.Alarm.CLOSE_ONCE_NEXT_TIME,
            ClockContract.Alarm.CLOSE_ONCE_PREVIOUS_TIME,
            ClockContract.Alarm.ENABLE_ASSOCIATE,
            ClockContract.Alarm.ALARM_UUID,
            ClockContract.Alarm.SNOOZE_TIME,
            ClockContract.Alarm.WORKDAY_TYPE,
            ClockContract.Alarm.WORKDAY_UPDATE_TIME,
            ClockContract.Alarm.SPECIAL_ALARM_DAYS,
            ClockContract.Alarm.DEFAULT_ALARM,
            ClockContract.Alarm.RING_NUMBER,
            ClockContract.Alarm.LOOP_SWITCH,
            ClockContract.Alarm.LOOP_CYCLE_DAYS,
            ClockContract.Alarm.LOOP_ID,
            ClockContract.Alarm.LOOP_WORK_DAYS,
            ClockContract.Alarm.LOOP_ALARM_NUMBER,
            ClockContract.Alarm.LOOP_DAY,
            ClockContract.Alarm.LOOP_RESET_DAYS,
            ClockContract.Alarm.RING_ABSOLUTE_PATH,
            ClockContract.Alarm.GARB_ALARM_SWITCH
    )

    val QUERY_IOT_COLUMNS = arrayOf(
            ClockContract.Alarm.ID,
            ClockContract.Alarm.ENABLED,
            ClockContract.Alarm.ENABLE_ASSOCIATE
    )
}