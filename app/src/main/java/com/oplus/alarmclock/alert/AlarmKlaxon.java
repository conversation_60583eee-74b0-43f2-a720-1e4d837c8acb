/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Manages playing ringtone and vibrating the device.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 ************************************************************/
// OPLUS Java File Skip Rule:LineLength,NestedBranchDepth,MethodComplexity,MethodLength
package com.oplus.alarmclock.alert;

import static android.content.Context.RECEIVER_EXPORTED;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Pair;

import com.heytap.addon.os.LinearmotorVibrator;
import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.alert.player.RingtonePlayerApi;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.AlarmWeatherUtils;
import com.oplus.alarmclock.utils.ChannelManager;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FbeRingUtils;
import com.oplus.alarmclock.utils.IBaseChannel;
import com.oplus.alarmclock.utils.LinearMotorVibratorManager;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.Utils;

import java.lang.ref.WeakReference;

import androidx.annotation.RequiresApi;

//TODO: Clean this class later.
public final class AlarmKlaxon {
    private static final String TAG = "AlarmKlaxon";
    private static final long[] VIBRATE_PATTERN = {500, 500};
    private static final long[] RAPID_STRONG_WAVEFORM_TIME = new long[]{500, 500, 500, 500};
    public static AlarmWeatherUtils sAlarmWeatherUtils = null;
    private static boolean sStarted = false;
    private static boolean sIsRegister = false;
    private static boolean sStopedByVolumeKey = false;
    private static RingtonePlayerApi sAsyncRingtonePlayerApi;
    private static WaveformEffect sWaveformEffect;
    private static BroadcastReceiver sStopRingReceiver = null;
    private static boolean sRingLoop = true;

    public static boolean isRingLoop() {
        return sRingLoop;
    }

    public static void setRingLoop(boolean ringLoop) {
        sRingLoop = ringLoop;
    }


    private AlarmKlaxon() {
    }


    public static void stop(Context context) {
        Log.i(TAG, "AlarmKlaxon.stop() sStarted = " + sStarted);
        try {
            if (sAlarmWeatherUtils != null) {
                sAlarmWeatherUtils.freed();
                sAlarmWeatherUtils = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "AlarmKlaxon.stop() error: " + e.getMessage());
        }
        if (sStarted) {
            sStarted = false;
            setRingLoop(true);
            stopVibrator(context);
            getAsyncRingtonePlayer(context).stop();
        }
        unregister(context.getApplicationContext());
    }

    private static void unregister(Context context) {
        if (sIsRegister) {
            sIsRegister = false;
            Log.i(TAG, "unregister");
            context.unregisterReceiver(sStopRingReceiver);
            sStopRingReceiver = null;
        }
    }

    /**
     * 停止振动
     *
     * @param context
     */
    public static void stopVibrator(Context context) {
        Vibrator vibrator = ((Vibrator) context.getApplicationContext().getSystemService(
                Context.VIBRATOR_SERVICE));
        vibrator.cancel();
        if ((sWaveformEffect != null) && (DeviceUtils.isLinearmotoSupport(context))) {
            LinearmotorVibrator linearmotorVibrator = LinearMotorVibratorManager.INSTANCE.getLinearMotorVibrator();
            if (linearmotorVibrator != null) {
                Log.i(TAG, "linearmotorVibrator != null");
                linearmotorVibrator.cancelVibrate(sWaveformEffect);
            } else {
                Log.i(TAG, "linearmotorVibrator == null");
            }
        }
    }


    @SuppressLint("InlinedApi, NewApi")
    public static void start(final Context context, final AlarmSchedule alarmSchedule) {
        Log.v(TAG, "AlarmKlaxon.start() alarmSchedule:" + alarmSchedule);
        sStopedByVolumeKey = false;
        // Make sure we are stopped before starting
        stop(context.getApplicationContext());
        sStarted = true;
        Log.d(TAG, "start alarm");
        AlarmStateManager.cancelNextAlarmNotices(context);
        registerReceiver(context.getApplicationContext());
        if (alarmSchedule != null) {
            Uri alertUri = alarmSchedule.getAlarmAlert();
            if ((alertUri != null) && AlarmWeatherUtils.isDynamicWeatherAlert(alertUri.toString())
                    && ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
                if (sAlarmWeatherUtils == null) {
                    sAlarmWeatherUtils = new AlarmWeatherUtils(new AlarmWeatherUtils.LoadWeatherTypeListener() {
                        @RequiresApi(api = Build.VERSION_CODES.Q)
                        @Override
                        public void onLoadComplete(boolean success, Object weatherAlertResOrName) {
                            Log.d(TAG, "getWeatherInfo onLoadComplete success: "
                                    + success + " weatherAlertResOrName: " + weatherAlertResOrName);
                            startPlayAndVibrate(context, alarmSchedule, true, weatherAlertResOrName);
                        }
                    });
                }
                sAlarmWeatherUtils.getWeatherInfo(context.getApplicationContext());
            } else {
                startPlayAndVibrate(context, alarmSchedule, false, null);
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static void startPlayAndVibrate(Context context, AlarmSchedule alarmSchedule,
                                            boolean useDynamicWeatherAlert, Object weatherAlertResOrName) {

        if (alarmSchedule != null) {
            if (useDynamicWeatherAlert && !DeviceUtils.isHapticRingtoneSupport(context)) {
                doVibrator(context, false, alarmSchedule, false);
                doRing(context, alarmSchedule, true, weatherAlertResOrName, false);
            } else {
                new CheckLinearMotorAsyncTask(context, alarmSchedule, useDynamicWeatherAlert, weatherAlertResOrName).execute();
            }
        }
    }

    private static void doRing(Context context, AlarmSchedule alarmSchedule,
                               boolean useDynamicWeatherAlert, Object weatherAlertResOrName,
                               boolean useHapticRingtoneVibrate) {

        if (alarmSchedule != null) {
            final int alertType = alarmSchedule.getAlarmAlertType();
            Uri alertUri = alarmSchedule.getAlarmAlert();
            Log.d(TAG, "doRing alertType " + alertType + ",sStarted==" + sStarted);
            if ((alertType != 1) && (alertUri != null)) {
                if ((!alarmSchedule.isAlarmSilent()) && (CurrentAlarmScheduleHolder.getAlarmSchedule() != null) && sStarted) {
                    Log.v(TAG, "AlarmKlaxon.start() play: " + alertUri + " useDynamicWeatherAlert : "
                            + useDynamicWeatherAlert + " weatherAlertResOrName: " + weatherAlertResOrName
                            + " useHapticRingtoneVibrate: " + useHapticRingtoneVibrate);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        getAsyncRingtonePlayer(context).play(alertUri, useDynamicWeatherAlert, weatherAlertResOrName,
                                useHapticRingtoneVibrate, alarmSchedule);
                        getAsyncRingtonePlayer(context).setVolume(context, AlarmUtils.isOpenBellGraduallyRings(context));
                    }
                }
            }
        }
    }

    public static void volumeReduceByTime(Context context) {
        getAsyncRingtonePlayer(context).setVolumeReduceByTime(context);
    }

    private static class CheckLinearMotorAsyncTask extends AsyncTask<Void, Void, Boolean> {

        private final AlarmSchedule mAlarmSchedule;
        private final boolean mUseDynamicWeatherAlert;
        private final Object mWeatherAlertResOrName;
        private final WeakReference<Context> mWeakRef;

        CheckLinearMotorAsyncTask(Context context, AlarmSchedule alarmSchedule, boolean useDynamicWeatherAlert, Object weatherAlertResOrName) {
            mAlarmSchedule = alarmSchedule;
            mUseDynamicWeatherAlert = useDynamicWeatherAlert;
            mWeatherAlertResOrName = weatherAlertResOrName;
            mWeakRef = new WeakReference<>(context);
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            Context context = (mWeakRef == null) ? null : mWeakRef.get();
            if (context != null) {
                //check if can use Linearmotor vibrate
                return canUseLinearmotoVibrator(context, mAlarmSchedule, mWeatherAlertResOrName);
            }
            return false;
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        @Override
        protected void onPostExecute(Boolean canUseLinearmotor) {
            Context context = (mWeakRef == null) ? null : mWeakRef.get();
            if (context != null) {
                boolean useHapticRingtoneVibrate = getUseHapticRingtoneVibrate(context, mAlarmSchedule, canUseLinearmotor);
                doVibrator(context, canUseLinearmotor, mAlarmSchedule, useHapticRingtoneVibrate);
                doRing(context, mAlarmSchedule, mUseDynamicWeatherAlert, mWeatherAlertResOrName, useHapticRingtoneVibrate);
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static void doVibrator(Context context, boolean canUseLinearmotor, AlarmSchedule alarmSchedule, boolean useHapticRingtoneVibrate) {
        if (useHapticRingtoneVibrate) {
            Log.d(TAG, "doVibrator useHapticRingtoneVibrate and do not call vibrate self");
            return;
        }
        if (!sStarted) {
            Log.d(TAG, "doVibrator sStarted is false");
            return;
        }
        final Vibrator vibrator = (Vibrator) context.getApplicationContext()
                .getSystemService(Context.VIBRATOR_SERVICE);

        VibrationEffect vibrationEffectStrong = null;
        if (Utils.isHardwareLinermotorSupport(context)) {
            vibrationEffectStrong = (VibrationEffect) VibrationEffect.createWaveform(
                    RAPID_STRONG_WAVEFORM_TIME, ClockConstant.RAPID_STRONG_WAVEFORM_AMPLITUDE, 0);
        }
        /* Start the vibrator after everything is ok with the media player */
        // Get the common set vabrate value.
        // CMCC version no common vabrate set, so use the own set.
        // for CMCC test must have choice of vibrate、Ring and Ring with vibrate
        if ((alarmSchedule != null) && (alarmSchedule.getVibrate() != WaveformEffect.EFFECT_RINGTONE_NOVIBRATE)) {
            if (DeviceUtils.isCmccVersion(context)) {
                if (DeviceUtils.isPowerSaveEnabled(context)) {
                    cancelVibrator(vibrator);
                    Log.d(TAG, " PowerSave mode, vibrator.cancel();");
                } else {
                    int alertType = alarmSchedule.getAlarmAlertType();
                    if (alertType >= 1) {
                        shockRepeat(vibrator, context, canUseLinearmotor, vibrationEffectStrong, alarmSchedule);
                    } else {
                        cancelVibrator(vibrator);
                    }
                }
            } else {
                shockRepeat(vibrator, context, canUseLinearmotor, vibrationEffectStrong, alarmSchedule);
            }
        } else {
            Log.d(TAG, "doVibrator alarmSchedule is null or vibrate type is EFFECT_RINGTONE_NOVIBRATE");
        }

        // for CTS test "Create Alarm Test" and "Start Alarm Test" must have vibrate
        if ((alarmSchedule != null) && alarmSchedule.isCreatedInCTS()) {
            shockRepeat(vibrator, context, canUseLinearmotor, vibrationEffectStrong, alarmSchedule);
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static void shockRepeat(Vibrator vibrator, Context context, boolean canUseLinearmotor,
                                   VibrationEffect vibrationEffectStrong, AlarmSchedule schedule) {
        if (CurrentAlarmScheduleHolder.getAlarmSchedule() != null) {
            Log.d(TAG, " shockRepeat");
            if (canUseLinearmotor && checkWeatherAlertFeature(context, schedule)) {
                Log.d(TAG, " shockRepeat canUseLinearmotor");
                vibrateWithLinearmotor(context, schedule);
            } else {
                Log.d(TAG, " shockRepeat vibrateRepeat");
                Utils.vibrateRepeat(vibrator, context, vibrationEffectStrong, VIBRATE_PATTERN, schedule);
            }
        } else {
            Log.d(TAG, " CurrentAlarmScheduleHolder.getAlarmSchedule() == null");
        }
    }

    /**
     * 是否是动态天气闹钟
     *
     * @param schedule
     * @return
     */
    private static boolean isDynamicWeatherAlert(AlarmSchedule schedule) {
        if (schedule == null) {
            return false;
        }
        Uri alertUri = schedule.getAlarmAlert();
        return (alertUri != null)
                && AlarmWeatherUtils.isDynamicWeatherAlert(alertUri.toString())
                && ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert();
    }

    /**
     * aw方案是否支持铃声随振
     * 1、天气闹钟；2、配置了版本隔离
     *
     * @param context
     * @param schedule
     * @return
     */
    private static boolean checkWeatherAlertFeature(Context context, AlarmSchedule schedule) {
        if (isDynamicWeatherAlert(schedule)) {
            return DeviceUtils.isChimesWithSound(context);
        }
        return true;
    }


    private static void cancelVibrator(Vibrator vibrator) {
        if (vibrator != null) {
            vibrator.cancel();
        }
    }

    private static void vibrateWithLinearmotor(Context context, AlarmSchedule schedule) {
        Log.d(TAG, "vibrateWithLinearmotor sWaveformEffect: " + sWaveformEffect);
        if (DeviceUtils.isLinearmotoSupport(context)) {
            try {
                LinearmotorVibrator linearmotorVibrator = LinearMotorVibratorManager.INSTANCE.getLinearMotorVibrator();
                if ((linearmotorVibrator != null) && (sWaveformEffect != null)) {
                    linearmotorVibrator.vibrate(sWaveformEffect);
                    Log.d(TAG, "vibrateWithLinearmotor != null : success");
                } else {
                    Log.d(TAG, "linearmotorVibrator == null");
                    AlarmRingStatisticUtils.statisticsAlarmException(context, AlarmRingStatisticUtils.EVENT_VIBRATE_ERROR,
                            schedule, AlarmRingStatisticUtils.VibrateErrorType.NULL_LINEAR_MOTOR);
                }
            } catch (Throwable e) {
                sWaveformEffect = null;
                Log.e(TAG, "vibrateWithLinearmotor error " + e.getMessage());
                AlarmRingStatisticUtils.statisticsAlarmException(context, AlarmRingStatisticUtils.EVENT_VIBRATE_ERROR,
                        schedule, AlarmRingStatisticUtils.VibrateErrorType.LINEAR_MOTOR_ERROR);
            }
        }
    }

    private static boolean canUseLinearmotoVibrator(
            Context context,
            AlarmSchedule alarmSchedule,
            Object weatherAlertResOrName
    ) {
        sWaveformEffect = null;
        if (alarmSchedule != null) {
            Uri ringUri = alarmSchedule.getAlarmAlert();
            int vibrateType = alarmSchedule.getVibrate();
            int alarmType = alarmSchedule.getAlarmAlertType();
            String ringFilePath = "";
            if (alarmType == 0) {
                return false;
            }
            if (!DeviceUtils.isLinearmotoSupport(context)) {
                Log.d(TAG, "checkUseLinearmotoVibrator not support linearmoto");
                return false;
            }
            if (WaveformEffect.EFFECT_RINGTONE_NOVIBRATE == vibrateType) {
                Log.d(TAG, "checkUseLinearmotoVibrator vibrateType is no vibrate");
                return false;
            }
            if (WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE == vibrateType) {
                //响铃时如果电话处理接听状态，则不支持线性马达随铃声振动,因为会播放内置的一个铃声
                if (getAsyncRingtonePlayer(context).isInTelephoneInCall(context)) {
                    Log.d(TAG, "checkUseLinearmotoVibrator in telephone call");
                    return false;
                }
                if (ringUri == null) {
                    Log.d(TAG, "checkUseLinearmotoVibrator uri == null");
                    return false;
                }
                Pair<Boolean, String> pair = getRingFilePath(context, ringUri, weatherAlertResOrName);
                if (pair.first) {
                    ringFilePath = pair.second;
                } else {
                    return false;
                }
                setRingLoop(false);
                WaveformEffect.Builder weBuild = new WaveformEffect.Builder()
                        .setEffectLoop(isRingLoop())
                        .setRingtoneVibrateType(vibrateType)
                        .setIsRingtoneCustomized(false)
                        .setUsageHint(AudioAttributes.USAGE_ALARM)
                        .setRingtoneFilePath(ringFilePath);
                sWaveformEffect = weBuild.build();
            } else {
                WaveformEffect.Builder weBuild = new WaveformEffect.Builder()
                        .setEffectLoop(true)
                        .setUsageHint(AudioAttributes.USAGE_ALARM)
                        .setRingtoneVibrateType(vibrateType)
                        .setIsRingtoneCustomized(false);
                sWaveformEffect = weBuild.build();
            }
            Log.d(TAG, "checkUseLinearmotoVibrator success," + " ringFilePath: " + ringFilePath
                    + " vibrateType: " + vibrateType);
        } else {
            Log.e(TAG, "alarmSchedule is null !");
        }
        return true;
    }

    private static Pair<Boolean, String> getRingFilePath(Context context, Uri ringUri, Object weatherAlertResOrName) {
        //天气闹钟aw方案需要传递铃声物理地址,匹配线性马达的振动波形
        String ringFilePath = "";
        if (AlarmWeatherUtils.isDynamicWeatherAlert(ringUri.toString()) && ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
            if (weatherAlertResOrName != null) {
                ringFilePath = AlarmRingUtils.queryInternalPathforAudio(context, weatherAlertResOrName.toString());
                Log.d(TAG, "ringFilePath : " + ringFilePath);
            }
        } else {
            ringFilePath = AlarmRingUtils.getMusicPathFromUriString(context, ringUri.toString());
            if (TextUtils.isEmpty(ringFilePath)) {
                if (FbeRingUtils.checkFBESupport(context) && Utils.isUserKeyUnlocked(context)) {
                    ringFilePath = FbeRingUtils.getInternalRingPathFromUri(context, ringUri);
                }
            }
            if (TextUtils.isEmpty(ringFilePath)) {
                Log.d(TAG, "checkUseLinearmotoVibrator ringFilePath == null");
                return new Pair<>(false, ringFilePath);
            } else {
                Log.d(TAG, "ringFilePath : " + ringFilePath);
                if (ChannelManager.INSTANCE.getChannelUtils().getChannel() == IBaseChannel.CHANNEL_WPLUS) {
                    if (!ringFilePath.startsWith("/system_ext/media/audio/ringtones/")
                            && !ringFilePath.startsWith(FbeRingUtils.getRingFilePath(context))
                            && !ringFilePath.startsWith("/data/oplus/multimedia/ringtones/")) {
                        Log.d(TAG, "one plus not internal ringtone");
                        return new Pair<>(false, ringFilePath);
                    }
                } else {
                    String ringFileName = Uri.parse(ringFilePath).getLastPathSegment();
                    if (!"alarm_master.ogg".equals(ringFileName) && !TextUtils.isEmpty(ringFileName) && !ringFileName.startsWith("ringtone_")) {
                        Log.d(TAG, "ringFileName " + ringFileName + " is not internal ringtone");
                        return new Pair<>(false, ringFilePath);
                    }
                }
            }
            boolean isInternalRingtone = AlarmRingUtils.isInternalRingtone(ringUri);
            if (!isInternalRingtone) {
                Log.d(TAG, "checkUseLinearmotoVibrator not internal ringtone");
                return new Pair<>(false, ringFilePath);
            }
        }
        return new Pair<>(true, ringFilePath);
    }

    private static void registerReceiver(Context context) {
        if (sStopRingReceiver == null) {
            sStopRingReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    Log.i(TAG, "StopRingReceiver action: " + action);
                    if (ClockConstant.ACTION_STOP_RING.equals(action)) {
                        sStopedByVolumeKey = true;
                        stop(context);
                    }
                }
            };
        }
        if (!sIsRegister) {
            sIsRegister = true;
            IntentFilter ifilter = new IntentFilter();
            ifilter.addAction(ClockConstant.ACTION_STOP_RING);
            context.registerReceiver(sStopRingReceiver, ifilter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
        }
    }

    private static synchronized RingtonePlayerApi getAsyncRingtonePlayer(Context context) {
        if (sAsyncRingtonePlayerApi == null) {
            sAsyncRingtonePlayerApi = new RingtonePlayerApi(
                    context.getApplicationContext(),
                    RingtonePlayerApi.TYPE_ALARM
            );
        }
        return sAsyncRingtonePlayerApi;
    }

    static boolean isStopedByVolumeKey() {
        return sStopedByVolumeKey;
    }

    /**
     * @return 是否使用铃声随振接口
     * 在8350基线支持铃声随振的机器上，如果振动类型是随铃声振动的，需要从调用线性马达改成调用多媒体的接口，其他类型的不变
     * isHapticRingtoneSupport是调用多媒体接口方案
     * 其他情况是aw方案(通过天气铃声ogg匹配线性马达波形)
     */
    private static boolean getUseHapticRingtoneVibrate(Context context, AlarmSchedule alarmSchedule, boolean useLinearMotor) {
        return useLinearMotor
                && DeviceUtils.isHapticChannelSupport(context)
                && checkWeatherAlertFeature(context, alarmSchedule)
                && (alarmSchedule != null)
                && (alarmSchedule.getVibrate() == WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE);
    }

    /**
     * 检测当前闹钟是否运行
     */
    public static boolean isRunningStart() {
        return sStarted;
    }

}