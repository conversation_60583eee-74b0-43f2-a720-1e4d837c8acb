/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : * This service is in charge of starting/stopping the alarm. It will bring up and
 * manage the {@link AlarmActivity} as well as {@link AlarmKlaxon}.
 * <p>
 * Registers a broadcast receiver to listen for snooze/dismiss intents. The broadcast receiver exits
 * early if AlarmActivity is bound to prevent double-processing of the snooze/dismiss intents.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alert;

import android.app.ActivityOptions;
import android.app.KeyguardManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.UserManager;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.view.ContextThemeWrapper;

import com.oapm.perftest.PerfTest;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.aidl.PlatformUtils;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmAlertWakeLock;
import com.oplus.alarmclock.alarmclock.AlarmReceiver;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmStateManager;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.IOTUtil;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper;
import com.oplus.alarmclock.alarmclock.fluid.GarbAlarmSeedlingHelper;
import com.oplus.alarmclock.alarmclock.utils.AlarmPreferenceUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.provider.ClockContract.Schedule;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.AsyncHandler;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.GarbAlarmUtils;
import com.oplus.alarmclock.utils.HighPriorityHeadsUpServiceProxy;
import com.oplus.alarmclock.utils.HighPriorityHeadsUpServiceProxy.OnBindSystemUISuccessListener;
import com.oplus.alarmclock.utils.NotificationUtils;
import com.oplus.alarmclock.utils.PhonyManagerExtensionKt;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.osdk.SettingNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.clock.common.utils.VersionUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import static com.oplus.alarmclock.alarmclock.AlarmReceiver.NOTIFYCATION_CANCEL_KEY;
import static com.oplus.alarmclock.alarmclock.AlarmRepeat.DEFAULT_ALERT_LENGTH_INDEX;
import static com.oplus.alarmclock.alarmclock.AlarmRepeat.REPEAT_ALERT_LENGTH;
import static com.oplus.alarmclock.utils.DeviceUtils.registerSuperPowerObserver;
import static com.oplus.alarmclock.utils.DeviceUtils.unRegisterSuperPowerObserver;

public class AlarmService extends Service {

    public static final String TAG = "AlarmService";

    public static final String KEY_RINGTONE_INFO = "RINGTONE_INFO";
    public static final String KEY_RINGTONE_IMG = "RINGTONE_IMG";
    public static final String KEY_SHOW_RINGTONE_ICON = "RINGTONE_ICON";

    /**
     * Private action used to start an alarm with this service.
     */
    public static final String START_ALARM_ACTION = "START_ALARM";
    /**
     * Private action used to stop an alarm with this service.
     */
    public static final String STOP_ALARM_ACTION = "STOP_ALARM";
    public static final String IS_ALARM_DISMISSED = "IS_ALARM_DISMISSED";

    /**
     * Private action used to deal with alarm when use speech assit voice.
     */
    private static final String ACTION_SPEECH_ASSIST_START_RECOGNIZE_OLD = "coloros.intent.action.SPEECH_ASSIST_START_RECOGNIZE";
    private static final String ACTION_SPEECH_ASSIST_START_RECOGNIZE = "oplus.intent.action.SPEECH_ASSIST_START_RECOGNIZE";

    private static final String KEY_ALARM_STATE = "ALARM_STATE";
    private static final String OPLUS_COMPONENT_SAFE_PERMISSION = "oppo.permission.OPPO_COMPONENT_SAFE";
    public static boolean sIsServiceAlive = false;
    private static AlarmFloatingWindowManager sAlarmFloatingWindowManager;
    /**
     * Binder given to AlarmActivity
     */
    private final IBinder mBinder = new Binder();
    private int mInitialCallState;
    private Context mContext;
    private TelephonyManager mTelephonyManager;
    private AlarmSchedule mCurrentAlarmSchedule = null;
    private HighPriorityHeadsUpServiceProxy mHighPriorityHeadsUpServiceProxy;

    private boolean mIsBindSystemUI = false;
    private boolean mIsReceiverRegistered = false;

    private boolean mIsRegistered = false;
    private LocalBroadcastManager mLocalBroadcastManager;
    private boolean mIsBound = false;

    private AlarmRepeat mAlarmRepeat;

    private boolean mIsSnooze = false;

    private ContentObserver mSuperPowerSaveObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            boolean isSuperPowerSaveMode = DeviceUtils.isSuperPowerSaveMode(AlarmService.this);
            Log.d(TAG, "onChange isSuperPowerSaveMode:" + isSuperPowerSaveMode + " selfChange:" + selfChange);
            if (mCurrentAlarmSchedule != null) {
                if (mCurrentAlarmSchedule.getAlarm().getmGarbSwitch() == 1) {
                    if (isSuperPowerSaveMode) {
                        GarbAlarmSeedlingHelper.closeSeedlingCard(AlarmService.this);
                    }
                    GarbAlarmSeedlingHelper.showGarbAlarmCardSecure(AlarmService.this, mCurrentAlarmSchedule, isSuccess -> {
                        if (isSuccess) {
                            if (sAlarmFloatingWindowManager != null) {
                                sAlarmFloatingWindowManager.hideFloatingWindow();
                            }
                        } else {
                            startFloatingViewOld(AlarmService.this, mCurrentAlarmSchedule, false);
                        }
                    });
                } else {
                    if (isSuperPowerSaveMode) {
                        AlarmSnoozeSeedingHelper.hideAlarmSeedlingCard(AlarmService.this);
                    }
                    AlarmSnoozeSeedingHelper.showAlarmRingPageSecure(AlarmService.this, mCurrentAlarmSchedule, isSuccess -> {
                        if (isSuccess) {
                            if (sAlarmFloatingWindowManager != null) {
                                sAlarmFloatingWindowManager.hideFloatingWindow();
                            }
                        } else {
                            //失败需要重新弹出响铃通知
                            startFloatingViewOld(AlarmService.this, mCurrentAlarmSchedule, false);
                        }
                    });
                }
            }
        }
    };

    private final BroadcastReceiver mActionsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "AlarmService received action: " + action
                    + ", mCurrentAlarmSchedule: " + mCurrentAlarmSchedule);
            if ((mCurrentAlarmSchedule == null) || (mCurrentAlarmSchedule.getAlarmState() != Schedule.FIRED_STATE)) {
                Log.e("No valid firing alarm");
                return;
            }

            if (mIsBound) {
                Log.i("AlarmActivity bound; AlarmService no-op");
                return;
            }

            if (action == null) {
                Log.e("Action is null, AlarmService no-op!");
                return;
            }
            final Context ctx = context;
            switch (action) {
                case ClockConstant.ALARM_SNOOZE_ACTION:
                    // Set the alarm state to snoozed.
                    // If this broadcast receiver is handling the snooze intent then AlarmActivity
                    // must not be showing, so always show snooze toast.
                    Log.i(TAG, "[snooze] ALARM_SNOOZE_ACTION.");
                    mIsSnooze = true;
                    AsyncHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            AlarmStateManager.setSnoozeState(ctx, mCurrentAlarmSchedule);
                        }
                    });
                    break;
                case ClockConstant.ALARM_DISMISS_ACTION:
                    Log.i(TAG, "[dismiss] ALARM_DISMISS_ACTION.");
                    // Set the alarm state to dismissed.
                    AlarmRingStatisticUtils.statisticsReceivedBroadCast(ctx, ClockConstant.ALARM_DISMISS_ACTION);
                    AlarmStateManager.setDismissState(ctx, mCurrentAlarmSchedule);
                    int type = intent.getIntExtra(NOTIFYCATION_CANCEL_KEY, 0);
                    if (type != 0) {
                        AlarmRingOperateUtils.closeAlarm(mCurrentAlarmSchedule.getAlarm(), type);
                    }
                    break;
                case ClockConstant.SHOW_FLOATING_WINDOW:
                    //显示旧的通知
                    startFloatingViewOld(context, mCurrentAlarmSchedule, false);
                    break;
                case ClockConstant.HIDE_FLOATING_WINDOW:
                    if (sAlarmFloatingWindowManager != null) {
                        sAlarmFloatingWindowManager.hideFloatingWindow();
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private final BroadcastReceiver mSystemActionsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "AlarmService received intent:" + action);
            if (action == null) {
                return;
            }
            switch (action) {
                case ACTION_SPEECH_ASSIST_START_RECOGNIZE_OLD:
                case ACTION_SPEECH_ASSIST_START_RECOGNIZE:
                    Log.i(TAG, "Stop alrm klaxon!");
//                    AlarmKlaxon.stop(AlarmClockApplication.getInstance());
                    break;
                case Intent.ACTION_USER_PRESENT:
                    boolean isSmallScreen = FoldScreenUtils.isDragonflySmallScreen(context);
                    Log.i(TAG, "ACTION_USER_PRESENT: isSnooze" + mIsSnooze + ";" + isSmallScreen);
                    if (!mIsSnooze && !isSmallScreen && DeviceUtils.isKeyGuardEnable()) {
                        chaneToFloatingAlarmView();
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private PhoneStateListener mPhoneStateListener = new PhoneStateListener() {
        @Override
        public void onCallStateChanged(int state, String ignored) {
            // The user might already be in a call when the alarm fires. When
            // we register onCallStateChanged, we get the initial in-call state
            // which kills the alarm. Check against the initial call state so
            // we don't kill the alarm during a call.
            int stateTemp = TelephonyManager.CALL_STATE_IDLE;
            if (mTelephonyManager != null) {
                stateTemp = PhonyManagerExtensionKt.getTelephonyCallState(mTelephonyManager);
            }
            Log.d(TAG,
                    "onCallStateChanged state = " + state + "stateTemp = " + stateTemp
                            + "AlarmAlert.mIsAlive " + AlarmAlert.sIsViewAlive
                            + " mInitialCallState = " + mInitialCallState);
            if ((mInitialCallState != TelephonyManager.CALL_STATE_IDLE)
                    && (stateTemp != TelephonyManager.CALL_STATE_IDLE)
                    && (stateTemp != TelephonyManager.CALL_STATE_RINGING)) {
                //ringing to offHock : restart alarm ring
                mInitialCallState = stateTemp;
                Log.i(TAG, "mInitialCallState not CALL_STATE_IDLE , stateTemp is " + stateTemp + "and mInitialCallState is " + mInitialCallState + AlarmKlaxon.isStopedByVolumeKey());
                if (!AlarmKlaxon.isStopedByVolumeKey()) {
                    AlarmKlaxon.start(AlarmClockApplication.getInstance(), mCurrentAlarmSchedule);
                }
            } else if ((mInitialCallState == TelephonyManager.CALL_STATE_IDLE)
                    && (stateTemp != TelephonyManager.CALL_STATE_IDLE)) {
                //idle to ringing : alarm snooze or dismiss
                Log.i(TAG, "stop alarm by phone");
                if ((mCurrentAlarmSchedule != null) && AlarmAlert.sIsViewAlive) {
                    snoozeOrDismissCurrentAlarm();
                    mCurrentAlarmSchedule = null;
                }
            } else if ((mInitialCallState == TelephonyManager.CALL_STATE_OFFHOOK)
                    && (stateTemp == TelephonyManager.CALL_STATE_IDLE)) {
                //offHock to idle : restart alarm ring
                mInitialCallState = stateTemp;
                Log.i(TAG, "mInitialCallState not CALL_STATE_OFFHOOK and  stateTemp is CALL_STATE_IDLE " + AlarmKlaxon.isStopedByVolumeKey());
                if (!AlarmKlaxon.isStopedByVolumeKey() && (mCurrentAlarmSchedule != null)) {
                    AlarmKlaxon.start(AlarmClockApplication.getInstance(), mCurrentAlarmSchedule);
                }
            } else {
                mInitialCallState = stateTemp;
            }
        }
    };

    /**
     * Utility method to help start alarm properly. If alarm is already firing, it will mark it as
     * missed and start the new one.
     *
     * @param context    application context
     * @param scheduleId to trigger alarm
     */
    public static void startAlarm(Context context, long scheduleId) {
        Uri uri = ContentUris.withAppendedId(Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        // Maintain a cpu wake lock until the service can get it
        sIsServiceAlive = true;
        if (context != null) {
            Log.i(TAG, "startAlarm scheduleId: " + scheduleId);
            final Intent intent = new Intent(context, AlarmService.class);
            intent.setData(uri);
            intent.setAction(START_ALARM_ACTION);
            intent.setPackage(ClockConstant.CLOCK_PACKAGE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        }
    }

    /**
     * Utility method to help stop an alarm properly. Nothing will happen, if alarm is not firing or
     * using a different alarmSchedule.
     *
     * @param context    application context
     * @param scheduleId you are trying to stop
     */
    public static void stopAlarm(Context context, long scheduleId, boolean isAlarmDismissed) {
        Log.i(TAG, "stopAlarm() scheduleId:" + scheduleId + "　isAlarmDismissed:" + isAlarmDismissed);
        Uri uri = ContentUris.withAppendedId(Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);

        if (context != null) {
            final Intent intent = new Intent(context, AlarmService.class);
            intent.setData(uri);
            intent.setAction(STOP_ALARM_ACTION);
            intent.setPackage(ClockConstant.CLOCK_PACKAGE);
            intent.putExtra(IS_ALARM_DISMISSED, isAlarmDismissed);

            // We don't need a wake lock here, since we are trying to kill an alarm
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent);
            } else {
                context.startService(intent);
            }
        }
    }

    private boolean isCurrentUser(AlarmSchedule alarmSchedule) {
        int alarmUserId = alarmSchedule.getAlarm().getOwnerUserId();
        int currentUserId = AppPlatformUtils.getCurrentUser();
        return (alarmUserId == currentUserId)
                || (!UserManager.supportsMultipleUsers());
    }

    private void startAlertAsUser(Context context, AlarmSchedule alarmSchedule) {
        if ((alarmSchedule == null) || (alarmSchedule.getAlarm() == null)) {
            Log.e(TAG, "startAlertAsUser alarmSchedule or alarm is null!, return.");
            return;
        }

        int alarmUserId = alarmSchedule.getAlarm().getOwnerUserId();
        int currentUserId = AppPlatformUtils.getCurrentUser();
        Log.d(TAG, "startAlertAsUser alarm user:" + alarmUserId
                + ",current user" + currentUserId);
        if (isCurrentUser(alarmSchedule)) {
            //alarm belong to current user
            startAlertActivityOrFloatingView(context, alarmSchedule);
        } else {
            //other user always show floatingView 切换到子用户响铃
            startFloatingView(context, alarmSchedule, true);
        }
    }

    private void startAlertActivityOrFloatingView(Context context, AlarmSchedule alarmSchedule) {

        KeyguardManager km = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
        boolean isKeyguardLock = ((km != null) && (km.isKeyguardLocked()));
        Log.i(TAG, "show AlertDialog alarm mInitialCallState = " + mInitialCallState
                + ", alarm km = " + km + ", isKeyguardLock = " + isKeyguardLock
                + ", sIsScreenOffToOn = " + AlarmAlert.sIsScreenOffToOn);
        boolean isSmall = FoldScreenUtils.isDragonflySmallScreen(context);
        if ((mInitialCallState == TelephonyManager.CALL_STATE_IDLE)
                && (isKeyguardLock || (!AlarmAlert.sIsScreenOffToOn) || isSmall)) {
            try {
                Intent alarmAlert = new Intent(context, AlarmAlertFullScreen.class);
                Bundle intentBundle = new Bundle();
                intentBundle.putParcelable(ClockConstant.ALARM_INTENT_EXTRA, alarmSchedule);
                alarmAlert.putExtras(intentBundle);
                alarmAlert.setFlags(
                        Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET
                                | Intent.FLAG_ACTIVITY_NO_USER_ACTION);
                Log.i(TAG, "startActivity(alarmAlert) alarmAlert = " + alarmAlert);
                finishFloatingAlarmView();
                if (isSmall) {
                    alarmAlert.putExtra(AlarmAlert.IS_DRAGONFLY, true);
                    ActivityOptions options = ActivityOptions.makeBasic();
                    options.setLaunchDisplayId(1);
                    context.startActivity(alarmAlert, options.toBundle());
                } else {
                    context.startActivity(alarmAlert);
                }
                if (alarmSchedule.getAlarm().getmGarbSwitch() == 1) {
                    GarbAlarmSeedlingHelper.showGarbAlarmCapsuleSecure(context.getApplicationContext(), alarmSchedule);
                }
                DeviceCaseAlarmAlertView.INSTANCE.setShow(true);
                SettingNativeUtils.System.putInt(AlarmClockApplication.getInstance(), KEY_ALARM_STATE, 1);
                AlarmRingOperateUtils.ringAlarm(alarmSchedule.getAlarm(), true);
            } catch (Exception e) {
                Log.e(TAG, "startActivity error! release wakelock.e:" + e.getMessage());
                AlarmAlertWakeLock.releaseCpuLock();
            }
        } else {
            startFloatingView(context, alarmSchedule, false);
        }
    }


    /**
     * 弹出旧版响铃通知
     *
     * @param context
     * @param alarmSchedule
     * @param otherUser
     */
    private void startFloatingViewOld(Context context, AlarmSchedule alarmSchedule, Boolean otherUser) {
        sendAlarmRingingNotification(alarmSchedule);
        setHighPriorityHeadsUp(true);
        initFloatingViewManager(context, alarmSchedule);
        if (sAlarmFloatingWindowManager != null) {
            sAlarmFloatingWindowManager.showFloatingWindow(alarmSchedule);
        }
    }

    private void startFloatingView(Context context, AlarmSchedule alarmSchedule, Boolean otherUser) {
        if (otherUser) {
            //子用户下弹出老的通知
            Log.d(TAG, "subUser alarm ring");
            startFloatingViewOld(context, alarmSchedule, otherUser);
        } else {
            if (alarmSchedule.getAlarm().getmGarbSwitch() == 1) {
                GarbAlarmSeedlingHelper.showGarbAlarmCardSecure(context.getApplicationContext(), alarmSchedule, isSuccess -> {
                    if (isSuccess) {
                        if (sAlarmFloatingWindowManager != null) {
                            sAlarmFloatingWindowManager.hideFloatingWindow();
                        }
                        //响铃后需要保持亮屏
                        AlarmRepeat alarmRepeat = AlarmUtils.getAlarmsRepeatInfo(context);
                        int duration = (alarmRepeat == null) ? REPEAT_ALERT_LENGTH[DEFAULT_ALERT_LENGTH_INDEX]
                                : alarmRepeat.getmAlarmDuration();
                        AlarmAlertWakeLock.acquireCpuWakeLockDim(mContext, duration);
                    } else {
                        startFloatingViewOld(context, alarmSchedule, otherUser);
                    }
                });
            } else {
                AlarmSnoozeSeedingHelper.showAlarmRingPageSecure(context.getApplicationContext(), alarmSchedule, isSuccess -> {
                    if (isSuccess) {
                        if (sAlarmFloatingWindowManager != null) {
                            sAlarmFloatingWindowManager.hideFloatingWindow();
                        }
                        //响铃后需要保持亮屏
                        AlarmRepeat alarmRepeat = AlarmUtils.getAlarmsRepeatInfo(context);
                        int duration = (alarmRepeat == null) ? REPEAT_ALERT_LENGTH[DEFAULT_ALERT_LENGTH_INDEX]
                                : alarmRepeat.getmAlarmDuration();
                        AlarmAlertWakeLock.acquireCpuWakeLockDim(mContext, duration);
                    } else {
                        //失败需要重新弹出响铃通知
                        startFloatingViewOld(context, alarmSchedule, otherUser);
                    }
                });
            }
        }
        SettingNativeUtils.System.putInt(AlarmClockApplication.getInstance(), KEY_ALARM_STATE, 1);
        AlarmAlertWakeLock.releaseCpuLockCpu(TAG + " startFloatingView");
        AlarmRingOperateUtils.ringAlarm(alarmSchedule.getAlarm(), false);
    }

    public static void initFloatingViewManager(Context context, AlarmSchedule alarmSchedule) {
        Log.i(TAG, "initFloatingVireManager");
        if ((sAlarmFloatingWindowManager != null)
                && (sAlarmFloatingWindowManager.floatingWindowIsShowing())) {
            sAlarmFloatingWindowManager.tearDown();
            return;
        }
        Log.i(TAG, "initFloatingVireManager end");
        sAlarmFloatingWindowManager = new AlarmFloatingWindowManager(context, alarmSchedule);
    }

    @Override
    public IBinder onBind(Intent intent) {
        mIsBound = true;
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        mIsBound = false;
        mAlarmRepeat = null;
        return super.onUnbind(intent);
    }

    AlarmRepeat getmAlarmRepeat() {
        mAlarmRepeat = AlarmUtils.getAlarmsRepeatInfo(this);
        if (null == mAlarmRepeat) {
            mAlarmRepeat = new AlarmRepeat();
            mAlarmRepeat.initAlarmRepeat();
        }
        Log.i(TAG, " alarm repeat : " + mAlarmRepeat);
        return mAlarmRepeat;
    }

    private void startAlarm(final AlarmSchedule alarmSchedule) {
        Log.i(TAG, "AlarmService.start with alarmSchedule: " + alarmSchedule + "mCurrentAlarmSchedule:" + mCurrentAlarmSchedule);
        // Maintain a cpu wake lock until the service can get it

        ClockOplusCSUtils.statisticsAlarmSettingInfo(getApplicationContext(), getmAlarmRepeat());
        if (alarmSchedule == null) {
            Log.e(TAG, "startAlarm alarmSchedule is null,return!");
            return;
        }
        Alarm alarm = alarmSchedule.getAlarm();
        //mCurrentAlarmSchedule不为null，说明现在有正在响铃的闹钟，其他的闹钟一律不响
        if ((mCurrentAlarmSchedule != null) && (mCurrentAlarmSchedule.getAlarm() != null)) {
            Log.w(TAG, "startAlarm multi same time alarm ignore follow-up");

            if (mCurrentAlarmSchedule.getAlarm().getmGarbSwitch() == 1) {
                // 当前响铃的为秒抢闹钟
                if (alarm.getmGarbSwitch() == 1) {
                    // 新的响铃也是秒抢
                    AlarmStateManager.setDismissState(this, alarmSchedule);
                    if (GarbAlarmUtils.isGarbAlarmRingTimeExpired(alarm)) {
                        //无响铃，发送错过闹钟
                        AlarmStateManager.sendMissAlarmNotification(this, alarmSchedule);
                    }
                } else {
                    stopUnnecessaryAlarm(alarm, alarmSchedule);
                }
                return;
            } else {
                if (alarm.getmGarbSwitch() == 1) {
                    //新响铃的为秒抢闹钟，关闭当前响铃的闹钟展示秒抢信息
                    CurrentAlarmScheduleHolder.setAlarmSchedule(alarmSchedule);
                    stopUnnecessaryAlarm(mCurrentAlarmSchedule.getAlarm(), mCurrentAlarmSchedule);
                    //普通闹钟销卡
                    AlarmSnoozeSeedingHelper.hideAlarmSeedlingCard(mContext);
                } else {
                    stopUnnecessaryAlarm(alarm, alarmSchedule);
                    return;
                }
            }
        }
        mTelephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        mInitialCallState = PhonyManagerExtensionKt.getTelephonyCallState(mTelephonyManager);
        PhonyManagerExtensionKt.listen(mTelephonyManager, mPhoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
        // Register a system broadcast receiver
        IntentFilter intentFilter = new IntentFilter(ACTION_SPEECH_ASSIST_START_RECOGNIZE);
        intentFilter.addAction(ACTION_SPEECH_ASSIST_START_RECOGNIZE_OLD);
        intentFilter.addAction(Intent.ACTION_USER_PRESENT);
        if (!mIsReceiverRegistered) {
            registerReceiver(mSystemActionsReceiver, intentFilter, OPLUS_COMPONENT_SAFE_PERMISSION, null, RECEIVER_EXPORTED);
            mIsReceiverRegistered = true;
        }
        Log.i(TAG, "startAlarm mInitialCallState = " + mInitialCallState);

        mCurrentAlarmSchedule = alarmSchedule;
        CurrentAlarmScheduleHolder.setAlarmSchedule(mCurrentAlarmSchedule);
        if (mCurrentAlarmSchedule != null) {
            mCurrentAlarmSchedule.setAlarmState(Schedule.FIRED_STATE);  //Mark the state.
        }

        ContextThemeWrapper themeContext = new ContextThemeWrapper(mContext, R.style.AppNoTitleTheme);
        if (Utils.isAboveQ()) {
            startAlertAsUser(themeContext, alarmSchedule);
        } else {
            startAlertActivityOrFloatingView(themeContext, alarmSchedule);
        }
        AlarmKlaxon.start(this.getApplicationContext(), mCurrentAlarmSchedule);
        mLocalBroadcastManager.sendBroadcast(new Intent(ClockConstant.ALARM_ALERT_ACTION));
        boolean isGarbAlarm = alarm.getmGarbSwitch() == 1;
        if (!isGarbAlarm || DeviceUtils.canSendGarbRingToHeath(mContext)) {
            PlatformUtils.getInstance().handleClockRingFoHealth();
        }
        AlarmPreferenceUtils.Companion.getInstance().addRingInfo(mCurrentAlarmSchedule.getAlarm());
        AlarmRingStatisticUtils.statisticsAlarmRingAction(mContext, mCurrentAlarmSchedule);
        AlarmRingOperateUtils.checkDatabase();
    }

    /**
     * 关闭响铃的闹钟
     *
     * @param alarm
     * @param alarmSchedule
     */
    private void stopUnnecessaryAlarm(Alarm alarm, AlarmSchedule alarmSchedule) {
        if (!alarm.isRepeatAlarm()) {
            AlarmUtils.disableAlarmNoNeedSetNextAlarm(this, alarmSchedule.getAlarmId(), false);
            AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.CLOSE_ALARM_TIME_SAME);
            /*相同时间的闹钟，未响铃的闹钟发送错过通知,通知消除规则未定义，暂时删除*/
            AlarmStateManager.sendMissAlarmNotification(this, alarmSchedule);
        } else {
            if (alarmSchedule.isSnoozeAvailble(alarmSchedule.getAlarm())) {
                Log.i(TAG, "startAlarm setSnoozeState");
                AlarmStateManager.setSnoozeState(this, alarmSchedule);
                AlarmRingOperateUtils.snoozeAlarm(alarm, AlarmRingOperateUtils.SNOOZE_ALARM_TIME_SAME);
            } else {
                Log.i(TAG, "startAlarm setDismissState");
                AlarmStateManager.setDismissState(this, alarmSchedule);
                AlarmRingOperateUtils.closeAlarm(alarm, AlarmRingOperateUtils.CLOSE_ALARM_TIME_SAME);
                /*相同时间的闹钟，未响铃的闹钟发送错过通知,通知消除规则未定义，暂时删除*/
                AlarmStateManager.sendMissAlarmNotification(this, alarmSchedule);
            }
        }
    }

    private void stopCurrentAlarm(String action) {
        Log.w(TAG, "stopCurrentAlarm stop ring and vibrate ");
        AlarmKlaxon.stop(AlarmClockApplication.getInstance());
        if (mCurrentAlarmSchedule == null) {
            Log.e(TAG, "stopCurrentAlarm There is no current alarm to stop");
            return;
        }

        Log.w(TAG, "stopCurrentAlarm cancel alarm notification, schedule: " + mCurrentAlarmSchedule.getId() + "  action = " + action);
        if (mIsReceiverRegistered) {
            unregisterReceiver(mSystemActionsReceiver);
            mIsReceiverRegistered = false;
        }
        PhonyManagerExtensionKt.listen(mTelephonyManager, mPhoneStateListener, 0);
        mLocalBroadcastManager.sendBroadcast(new Intent(action));
        boolean isGarbAlarm = mCurrentAlarmSchedule.getAlarm().getmGarbSwitch() == 1;
        if (!isGarbAlarm || DeviceUtils.canSendGarbRingToHeath(mContext)) {
            if (TextUtils.equals(action, ClockConstant.ALARM_CANCEL_NOTIFICATION_ACTION)) {
                PlatformUtils.getInstance().snoozeClock(AlarmClockApplication.getInstance(), mCurrentAlarmSchedule.getId(), PlatformUtils.SEND_ALL);
                notifyIOTDataChange(true);
            } else if (TextUtils.equals(action, ClockConstant.ALARM_SNOOZE_OR_DISMISS_ACTION)
                    || TextUtils.equals(action, ClockConstant.ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION)) {
                if (mCurrentAlarmSchedule.isSnoozeAvailble(mCurrentAlarmSchedule.getAlarm())) {
                    PlatformUtils.getInstance().snoozeClock(AlarmClockApplication.getInstance(),
                            mCurrentAlarmSchedule.getId(), PlatformUtils.SEND_ALL);
                } else {
                    PlatformUtils.getInstance().dismissClock(AlarmClockApplication.getInstance(),
                            mCurrentAlarmSchedule.getId(), PlatformUtils.SEND_ALL);
                }
                notifyIOTDataChange(true);
            } else if (TextUtils.equals(action, ClockConstant.ALARM_DISMISS_SERVICE_ACTION)) {
                PlatformUtils.getInstance().dismissClock(AlarmClockApplication.getInstance(), mCurrentAlarmSchedule.getId(), PlatformUtils.SEND_ALL);
                notifyIOTDataChange(false);
            }
        }
        mCurrentAlarmSchedule = null;
        CurrentAlarmScheduleHolder.setAlarmSchedule(null);

        AlarmStateManager.showNextAlarmNotices(mContext);

        AlarmAlertWakeLock.releaseCpuLock();
        SettingNativeUtils.System.putInt(AlarmClockApplication.getInstance(), KEY_ALARM_STATE, 0);
    }

    private void snoozeOrDismissCurrentAlarm() {
        if (mCurrentAlarmSchedule != null) {
            if (mCurrentAlarmSchedule.isSnoozeAvailble(mCurrentAlarmSchedule.getAlarm())) {
                AlarmStateManager.setSnoozeState(AlarmClockApplication.getInstance(), mCurrentAlarmSchedule);
            } else {
                AlarmStateManager.setDismissState(mContext, mCurrentAlarmSchedule);
            }
        }
        stopCurrentAlarm(ClockConstant.ALARM_SNOOZE_OR_DISMISS_ACTION);
    }

    private void notifyIOTDataChange(boolean isSnooze) {
        PlatformUtils.sUpdateType = isSnooze ? PlatformUtils.UPDATE_TYPE_CLOCK_SNOOZE : PlatformUtils.UPDATE_TYPE_CLOCK_CLOSE;
        if (mCurrentAlarmSchedule == null) {
            Log.d(TAG, "mCurrentAlarmSchedule is null");
        } else {
            Alarm alarm = mCurrentAlarmSchedule.getAlarm();
            if (alarm == null) {
                Log.d(TAG, "mCurrentAlarmSchedule.getAlarm() is null");
            } else {
                IOTUtil.notifyIOTDataChange(alarm);
            }
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();

        // Register the broadcast receiver
        final IntentFilter filter = new IntentFilter(ClockConstant.ALARM_SNOOZE_ACTION);
        filter.addAction(ClockConstant.ALARM_DISMISS_ACTION);
        filter.addAction(ClockConstant.SHOW_FLOATING_WINDOW);
        filter.addAction(ClockConstant.HIDE_FLOATING_WINDOW);
        if (!mIsRegistered) {
            mLocalBroadcastManager = LocalBroadcastManager.getInstance(AlarmClockApplication.getInstance());
            mLocalBroadcastManager.registerReceiver(mActionsReceiver, filter);
            mIsRegistered = true;
        }
        if (TimerSeedlingHelper.isSupportFluidCloud()) {
            registerSuperPowerObserver(this, mSuperPowerSaveObserver);
        }
        mContext = this;
        Log.i(TAG, "onCreate alarm service");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "AlarmService.onStartCommand() with " + " " + intent);
        if (intent != null) {
            Uri data = intent.getData();
            if (data != null) {
                final long scheduleId = ContentUris.parseId(data);
                if ((scheduleId > 0) && (!TextUtils.isEmpty(intent.getAction()))) {
                    final AlarmSchedule alarmSchedule = ScheduleUtils.getSchedule(AlarmClockApplication.getInstance(), scheduleId);
                    NotificationUtils notificationUtils = new NotificationUtils();
                    switch (intent.getAction()) {
                        case START_ALARM_ACTION:
                            Log.i(TAG, "[START_ALARM_ACTION]Current Schedule: " + mCurrentAlarmSchedule
                                    + ", Target Schedule: " + alarmSchedule);
                            if (alarmSchedule == null) {
                                Log.e(TAG, "No alarmSchedule found to start alarm: " + scheduleId);
                                if (mCurrentAlarmSchedule != null) {
                                    sendAlarmRingingNotification(mCurrentAlarmSchedule);
                                } else {
                                    notificationUtils.sendDefaultForegroundNotification(mContext, this);
                                    stopSelf();
                                }
                                // Only release lock if we are not firing alarm
                                AlarmAlertWakeLock.releaseCpuLock();
                                break;
                            }
                            if (mCurrentAlarmSchedule != null) {
                                if (mCurrentAlarmSchedule.getId() == scheduleId) {
                                    Log.e(TAG, "Alarm already started for alarmSchedule: "
                                            + mCurrentAlarmSchedule);
                                    break;
                                }

                                if (mCurrentAlarmSchedule.getTime() == alarmSchedule.getTime()) {
                                    Log.e(TAG, "Same time Alarm already started for alarmSchedule: "
                                            + mCurrentAlarmSchedule);
                                }
                            }
                            startAlarm(alarmSchedule);
                            //如果支持流体云卡则不弹出响铃通知
                            if (!VersionUtils.isOsVersion15() || !TimerSeedlingHelper.isSupportFluidCloud()) {
                                sendAlarmRingingNotification(alarmSchedule);
                            } else {
                                notificationUtils.sendDefaultForegroundNotification(mContext, this);
                            }
                            break;
                        case STOP_ALARM_ACTION:
                            notificationUtils.sendDefaultForegroundNotification(mContext, this);
                            Log.i(TAG, "[STOP_ALARM_ACTION]Current Schedule: " + mCurrentAlarmSchedule
                                    + ", Target Schedule: " + alarmSchedule);
                            if ((mCurrentAlarmSchedule != null) && (mCurrentAlarmSchedule.getId() != scheduleId)) {
                                Log.e(TAG, "Can't stop alarm for alarmSchedule: " + scheduleId
                                        + ", because current alarm is: " + mCurrentAlarmSchedule.getId());
                                break;
                            }
                            boolean isAlarmDismissed = intent.getBooleanExtra(IS_ALARM_DISMISSED,
                                    false);
                            Alarm alarm = null;
                            if (alarmSchedule != null) {
                                alarm = alarmSchedule.getAlarm();
                            }
                            if ((alarm == null) && (mCurrentAlarmSchedule != null)) {
                                alarm = mCurrentAlarmSchedule.getAlarm();
                            }
                            boolean isGarbAlarm = (alarm != null && alarm.getmGarbSwitch() == 1);
                            if (isGarbAlarm) {
                                GarbAlarmSeedlingHelper.reTractSeedlingCard(mContext, false);
                            }
                            Log.i(TAG, "[STOP_ALARM_ACTION] isAlarmDismissed: " + isAlarmDismissed);
                            if (isAlarmDismissed) {
                                stopCurrentAlarm(ClockConstant.ALARM_DISMISS_SERVICE_ACTION);
                            } else {
                                //TODO: Check this.
                                // this come from AlarmStateManager, alarm state already set.
                                stopCurrentAlarm(ClockConstant.ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION);
                            }
                            //支持流体云，当闹钟被关闭时，如果支持流体云，则发送延迟提醒的广播
                            if (!isGarbAlarm && VersionUtils.isOsVersion15()) {
                                AlarmSnoozeSeedingHelper.cancelOrSnoozeAlarm(mContext, isAlarmDismissed);
                            }
                            mIsSnooze = false;
                            sIsServiceAlive = false;
                            stopSelf();
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        return Service.START_NOT_STICKY;
    }

    private void sendAlarmRingingNotification(final AlarmSchedule alarmSchedule) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.e(TAG, "start alarm, show Notification alarm_is_ringing ");
            Notification.Builder builder = new Notification.Builder(AlarmClockApplication.getInstance(), AlarmStateManager.CHANNEL_ID);
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            /*14.3.4版本将声音放开，需要删除旧的channel id，更新通知通道*/
            if (notificationManager != null) {
                notificationManager.deleteNotificationChannel(AlarmStateManager.OLD_CHANNEL_ID);
            }
            NotificationChannel channel = new NotificationChannel(AlarmStateManager.CHANNEL_ID,
                    getString(R.string.clock_notification_label), NotificationManager.IMPORTANCE_DEFAULT);
            channel.enableVibration(false);
            channel.setVibrationPattern(null);
            channel.setSound(null, null);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }

            /**当前闹钟时间 用于对通知标题的设置*/
            String label = AlarmUtils.getTimeStringShort(mContext, alarmSchedule.getTime());
            /**点击通知进入闹钟页的广播*/
            Intent enterApp = new Intent(mContext, AlarmClock.class);
            enterApp.setAction(ClockConstant.ENTER_APK);
            enterApp.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP
                    | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            enterApp.putExtra(AlarmClock.ACTION_PART_TAB_INDEX, AlarmClock.TAB_INDEX_ALARMCLOCK);
            PendingIntent enterAppIntent = PendingIntent.getActivity(mContext, 0,
                    enterApp, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            builder.setChannelId(AlarmStateManager.CHANNEL_ID)
                    .setContentTitle(label)
                    .setSmallIcon(R.drawable.ic_launcher_clock)
                    .setContentText(getString(R.string.alarm_is_ringing))
                    .setContentIntent(enterAppIntent);

            /**稍后提醒和关闭按钮*/
            if (alarmSchedule.isSnoozeAvailble(alarmSchedule.getAlarm())) {
                final Intent snooze = new Intent(mContext, AlarmReceiver.class);
                snooze.setAction(ClockConstant.SNOOZE_ALARM_FROM_NOTIFICATION);
                final PendingIntent snoozeBroadcast = PendingIntent.getBroadcast(mContext, 0,
                        snooze, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
                builder.addAction(new Notification.Action.Builder(
                        R.drawable.ic_launcher_clock,
                        getString(R.string.snooze_alarm),
                        snoozeBroadcast
                ).build());
            }
            final Intent dismiss = new Intent(mContext, AlarmReceiver.class);
            dismiss.setAction(ClockConstant.ALARM_GO_STOP);
            final PendingIntent dismissBroadcast = PendingIntent.getBroadcast(mContext, 0,
                    dismiss, Utils.getPendingIntentFlagAboveS(PendingIntent.FLAG_UPDATE_CURRENT));
            builder.addAction(new Notification.Action.Builder(
                    R.drawable.ic_launcher_clock,
                    getString(R.string.close),
                    dismissBroadcast
            ).build());
            startForeground(Integer.MIN_VALUE, builder.build());
        }
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "AlarmService.onDestroy() called");
        super.onDestroy();
        if (mCurrentAlarmSchedule != null) {
            stopCurrentAlarm(ClockConstant.ALARM_CANCEL_NOTIFICATION_ACTION);
        }
        if (TimerSeedlingHelper.isSupportFluidCloud()) {
            unRegisterSuperPowerObserver(this, mSuperPowerSaveObserver);
        }
        if (mIsRegistered) {
            mLocalBroadcastManager.unregisterReceiver(mActionsReceiver);
            mIsRegistered = false;
        }
        AlarmAlert.setIsViewAlive(false);
        finishFloatingAlarmView();
        sIsServiceAlive = false;
        setHighPriorityHeadsUp(false);
        sAlarmFloatingWindowManager = null;
        PerfTest.leakWatch(this);
    }

    private void setHighPriorityHeadsUp(boolean enabled) {
        if (mHighPriorityHeadsUpServiceProxy == null) {
            mHighPriorityHeadsUpServiceProxy = new HighPriorityHeadsUpServiceProxy(
                    this);
        }
        Log.d(TAG, "setHighPriorityHeadsUp, enabled=" + enabled + " mIsBindSystemUI=" + mIsBindSystemUI);
        if (enabled) {
            mHighPriorityHeadsUpServiceProxy
                    .show(new OnBindSystemUISuccessListener() {
                        @Override
                        public void onBindSystemUISuccess() {
                            mIsBindSystemUI = true;
                        }
                    });
        } else {
            if (mIsBindSystemUI) {
                mHighPriorityHeadsUpServiceProxy.dismiss();
                mIsBindSystemUI = false;
            }
        }
    }

    private void finishFloatingAlarmView() {
        if (sAlarmFloatingWindowManager != null) {
            sAlarmFloatingWindowManager.hideFloatingWindow();
        }
    }


    /**
     * 用户解锁时切换到强提醒
     */
    private void chaneToFloatingAlarmView() {
        if (mCurrentAlarmSchedule == null || mContext == null) {
            Log.e(TAG, "chaneToFloatingAlarmView There is no current alarm to stop");
            return;
        }
        LiteEventBus.Companion.getInstance().send(ClockConstant.EVENT_FINISH_ALARM_FULLSCREEN, "");
        ContextThemeWrapper themeContext = new ContextThemeWrapper(mContext, R.style.AppNoTitleTheme);
        startAlertAsUser(themeContext, mCurrentAlarmSchedule);
        AlarmKlaxon.start(themeContext, mCurrentAlarmSchedule);
    }
}
