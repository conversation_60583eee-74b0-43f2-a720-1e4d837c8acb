/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.alert;

import static android.content.Context.RECEIVER_NOT_EXPORTED;

import static com.oplus.alarmclock.alert.AlarmAlert.ACTION_DIVE_CASE;
import static com.oplus.alarmclock.alert.AlarmAlert.ACTION_DIVE_CON;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.ProcessGuard;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.ContinueUtils;
import com.oplus.utils.DragonflyUtils;

import java.util.Calendar;

@SuppressWarnings("all")
public class AlarmFloatingWindowViewWrapper extends AlarmFloatingWindowView {

    private static final String TAG = "AlarmFloatingWindowViewWrapper";
    private static final boolean DEBUG = true;
    private static final int PRIORITY = 1000;

    private boolean mIsRegister = false;
    private boolean mIsSnoozeAvailble = false;
    private long mAlarmSheduleId;
    private AlarmSchedule mAlarmSchedule;
    private LocalBroadcastManager mLocalBroadcastManager;
    private AlarmFloatingWindowManager mAlarmFloatingWindowManager;
    private DragonflyUtils mDragonflyUtils;
    private final BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "Received broadcast:" + action);
            if (action == null) {
                return;
            }

            switch (action) {
                case TimerService.STOP_ALARM_ACTION: {
                    hideFloatingWindowFromTimer();
                    stopOrDismiss(action);
                    break;
                }
                case ClockConstant.ALARM_RINGTONE_INFO_ACTION: {
                    String ringtoneInfo = intent.getStringExtra(AlarmService.KEY_RINGTONE_INFO);
                    updateRingtoneInfo(ringtoneInfo);
                    break;
                }
                default:
                    break;
            }
        }

    };

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "Received broadcast:" + action);
            if (action == null) {
                return;
            }

            switch (action) {

                case Intent.ACTION_SCREEN_OFF: {
                    if (Utils.screenChangedByPowerButton() || Utils.screenChangedByApplication()) {
                        Log.i(TAG, "screen off by user return.");
                        hideFloatingWindow();
                        stopOrDismiss(action);
                    }
                    break;
                }
                case Intent.ACTION_TIME_CHANGED:
                case Intent.ACTION_TIME_TICK: {
                    if (mAlarmFloatingWindowManager != null) {
                        mAlarmTimeStr = Formatter.getCurrentTime(mContext, Calendar.getInstance().getTimeInMillis());
                        setAlarmTimeLabel(mAlarmNameStr, mAlarmTimeStr);
                    }
                    Log.d(TAG, "start protect process for one minute ");
                    ProcessGuard.startProtect(context, AlarmFloatingWindowViewWrapper.TAG);
                    break;
                }
                case ClockConstant.CLEAR_NOTIFICATION:
                case ClockConstant.CLEAR_NOTIFICATION_OLD: {
                    if (mContext != null) {
                        ScheduleUtils.cancelNotification(mContext, mAlarmSheduleId);
                    }
                    break;
                }
                case TimerService.TIMER_ALERT_ACTION: {
                    hideFloatingWindow();
                    stopOrDismiss(action);
                    break;
                }
                case Intent.ACTION_SHUTDOWN:
                    Log.d(TAG, "receive shutdown and stop alarm");
                    hideFloatingWindow();
                    dismiss();
                    break;
                default:
                    break;
            }
        }

    };

    public AlarmFloatingWindowViewWrapper(Context context, int orientation,
                                          boolean isSnoozeAvailble, String name, String time, long id,
                                          android.view.WindowManager.LayoutParams param, AlarmFloatingWindowManager manager, boolean isWorkDaySwitchOpened, boolean isGarbAlarm, AlarmSchedule schedule) {
        super(context, orientation, isSnoozeAvailble, time, name, param, isWorkDaySwitchOpened, isGarbAlarm, AlarmFloatingWindowView.FLOATING_VIEW_TYPE_ALARM);
        AlarmAlert.setIsViewAlive(true);
        mContext = context;
        mIsSnoozeAvailble = isSnoozeAvailble;
        mAlarmFloatingWindowManager = manager;
        mAlarmSheduleId = id;
        mAlarmSchedule = schedule;

        // Register the broadcast receiver
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(mContext);
        final IntentFilter filter = new IntentFilter(Intent.ACTION_TIME_TICK);
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        filter.addAction(ClockConstant.CLEAR_NOTIFICATION);
        filter.addAction(ClockConstant.CLEAR_NOTIFICATION_OLD);
        filter.addAction(TimerService.TIMER_ALERT_ACTION);
        filter.addAction(Intent.ACTION_SHUTDOWN);
        filter.setPriority(PRIORITY);
        final IntentFilter filterLocal = new IntentFilter();
        filterLocal.addAction(TimerService.STOP_ALARM_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_RINGTONE_INFO_ACTION);
        if (!mIsRegister) {
            mLocalBroadcastManager.registerReceiver(mLocalReceiver, filterLocal);
            mContext.registerReceiver(mReceiver, filter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_NOT_EXPORTED);
            mIsRegister = true;
        }

        setBroadcastToOthers();
        Log.d(TAG, "start protect process for one minute");
        ProcessGuard.startProtect(getContext(), TAG);
        mDragonflyUtils = ContinueUtils.registerListener(context, mDragonflyUtils, isSmallScreen -> continueActivity(context));
    }

    private void setBroadcastToOthers() {
        final Intent intentStopTimer = new Intent(TimerConstant.STOP_TIMERALERT);
        mLocalBroadcastManager.sendBroadcast(intentStopTimer);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        if (DEBUG) {
            Log.v(TAG, "onConfigurationChanged newConfig = " + newConfig + " ori = "
                    + newConfig.orientation);
        }
        switch (newConfig.orientation) {
            case Configuration.ORIENTATION_LANDSCAPE:
            case Configuration.ORIENTATION_PORTRAIT:
                shouldToRecreateView(newConfig.orientation);
                break;
            default:
                break;
        }
        super.onConfigurationChanged(newConfig);
    }

    private void stopOrDismiss(String action) {
        if (mIsSnoozeAvailble) {
            snooze();
            AlarmRingOperateUtils.snoozeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_USER);
        } else {
            dismiss();
            if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_POWER);
            } else if (TimerService.TIMER_ALERT_ACTION.equals(action) || TimerService.STOP_ALARM_ACTION.equals(action)) {
                AlarmUtils.asynSendMissAlarmNotification(mContext,mAlarmSchedule);
                AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_TIMER);
            } else if (ACTION_DIVE_CON.equals(action)) {
                AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_CON);
            } else if (ACTION_DIVE_CASE.equals(action)) {
                AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_DEVICE_CASE);
            } else {
                AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.CLOSE_ALARM_USER);
            }
        }
        if (TimerService.TIMER_ALERT_ACTION.equals(action) || TimerService.STOP_ALARM_ACTION.equals(action)) {
            AlarmRingStatisticUtils.statisticsUserAlarmAction(mContext, mAlarmSchedule,
                    AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.TIMER_IS_ALERT);
        } else if (Intent.ACTION_SCREEN_OFF.equals(action)) {
            AlarmRingStatisticUtils.statisticsUserAlarmAction(mContext, mAlarmSchedule,
                    AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.POWER_KEY_CLICKED);
        }
    }

    @Override
    public void updateView(String name, String time) {
        updateAlarmSchedule(name, time);
        setAlarmTimeLabel(name, time);
    }

    void shouldToRecreateView(int orientation) {
        if ((mAnimatingState == ANIM_STATE_IDLE) && (!mIrremovable)) {
            if (((mContext != null) && DeviceUtils.isAbnormalScreen(mContext))
                    || AlarmFloatingWindowView.sIsBigRadians) {
                if (mAlarmFloatingWindowManager != null) {
                    updateWindowView(mAlarmFloatingWindowManager.getWindowParams());
                }
            }
            recreateView(orientation);
        }
    }

    @Override
    protected void hideFloatingWindow() {
        if (mAlarmFloatingWindowManager != null) {
            mAlarmFloatingWindowManager.hideFloatingWindow();
        }
        Log.d(TAG, "end protect process");
        ProcessGuard.endProtect(getContext(), TAG);
    }

    protected void hideFloatingWindowFromTimer() {
        if (mAlarmFloatingWindowManager != null) {
            mAlarmFloatingWindowManager.hideFloatingWindowFromTimer();
        }
    }

    public void hideFloatingWindowWithAction(int action, int type) {
        Log.d(TAG, "hideFloatingWindowWithAction: " + action);
        if (action == ACTION_DISMISS) {
            if (mContext != null) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(mContext, mAlarmSchedule, ClockOplusCSUtils.STR_PRESS_ALARM_DISMISS_MENU, null);
            }
            AlarmRingOperateUtils.closeAlarm(mAlarmSchedule.getAlarm(), type);
            hideFloatingWindow();
            dismiss();
        } else if (action == ACTION_SNOOZE) {
            if (mContext != null) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(mContext, mAlarmSchedule, ClockOplusCSUtils.STR_PRESS_ALARM_SNOOZE_MENU, null);
            }
            AlarmRingOperateUtils.snoozeAlarm(mAlarmSchedule.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_USER);
            hideFloatingWindow();
            snooze();
        }
    }

    @Override
    protected void dismiss() {
        Log.d(TAG, "[dismiss]: cancelNotification: " + mAlarmSheduleId);
        if (mContext != null) {
            AlarmAlertUtilsKt.alarmDismiss(mContext);
            ScheduleUtils.cancelNotification(mContext, mAlarmSheduleId);
        }
    }

    @Override
    protected void snooze() {
        Log.d(TAG, "snooze.");
        if (mContext != null) {
            AlarmAlertUtilsKt.alarmSnooze(mContext);
            ScheduleUtils.cancelNotification(mContext, mAlarmSheduleId);
        }
    }

    @Override
    public void unregisterReceiver() {
        if (mIsRegister) {
            mLocalBroadcastManager.unregisterReceiver(mLocalReceiver);
            if (mContext != null) {
                mContext.unregisterReceiver(mReceiver);
            }
            mIsRegister = false;
        }
        ContinueUtils.unregisterListener(mContext, mDragonflyUtils);
    }

    @Override
    protected void onVolumeAndCameraKeyPressed() {
        if (mContext != null) {
            Intent intent = new Intent(ClockConstant.ACTION_STOP_RING);
            mContext.sendBroadcast(intent);
        }
    }

    @Override
    protected void removeFloatingWindowByFluidCloud() {
        mAlarmFloatingWindowManager.removeViewForWindowManager();
    }

    private void continueActivity(Context context) {
        Log.d(TAG, "continueActivity: ");
        ContinueUtils.unregisterListener(context, mDragonflyUtils);
        hideFloatingWindow();
        stopOrDismiss(ACTION_DIVE_CON);
    }
}