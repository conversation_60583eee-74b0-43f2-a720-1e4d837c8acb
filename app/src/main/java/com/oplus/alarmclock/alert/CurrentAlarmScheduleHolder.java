/************************************************************
 * Copyright 2019 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description : To hold the AlarmSchedule when alarm is ringing.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2019-2-23, yll, create
 ************************************************************/
package com.oplus.alarmclock.alert;

import com.oplus.alarmclock.alarmclock.AlarmSchedule;

public class CurrentAlarmScheduleHolder {

    /**
     * 当前正在响铃的闹钟
     */
    private static AlarmSchedule sAlarmSchedule = null;

    public static AlarmSchedule getAlarmSchedule() {
        return sAlarmSchedule;
    }

    public static void setAlarmSchedule(AlarmSchedule sAlarmSchedule) {
        CurrentAlarmScheduleHolder.sAlarmSchedule = sAlarmSchedule;
    }

    /** schedule 对应的闹钟是否正在响铃 */
    public static boolean isAlarmRinging(AlarmSchedule schedule) {
        if ((sAlarmSchedule == null) || (schedule == null)) {
            return false;
        }
        return sAlarmSchedule.getId() == schedule.getId();
    }
}
