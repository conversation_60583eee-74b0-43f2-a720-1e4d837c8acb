/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :Full screen alarm alert: pops visible indicator and plays alarm tone. This activity
 * displays the alert in full screen in order to be secure. The background is the current wallpaper.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 ************************************************************/
package com.oplus.alarmclock.alert;


import static com.oplus.alarmclock.utils.ClockConstant.DEVICE_CASE_OPEN;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Color;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.alarmclock.AlarmAlertWakeLock;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.holiday.LegalHolidayParser;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.alarmclock.utils.OplusDeviceCaseUtilsKt;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.hardware.devicecase.OplusDeviceCaseStateCallback;
import com.oplus.realweather.WeatherUtils;

import java.util.concurrent.Executors;

public class AlarmAlertFullScreen extends AlarmAlert implements DeviceCaseAlarmAlertView.DeviceCaseCallback {
    private static final String TAG = "AlarmAlertFullScreen";

    private final static int INVISBLE = 0x00200000 | 0x00400000 | 0x01000000;
    private final static int NAVIGATION_BAR_TRANSPARENT = 32768;

    private static final int MSG_HIDE_LAUNCHER_CONTENT = 1;
    private static final int MSG_SHOW_LAUNCHER_CONTENT = 2;
    private Messenger mLauncherMessenger;
    private boolean mFlagChangeVolume;
    private boolean mFistEnter = true;
    private OplusDeviceCaseStateCallback mOplusDeviceCaseStateCallBack;

    @Override
    @SuppressLint("NewApi")
    protected void onCreate(Bundle icicle) {
        init(AlarmClockApplication.getInstance());
        super.onCreate(icicle);
        AlarmUtils.setPowerKeyBroadcastSendable(AlarmClockApplication.getInstance(), true);
        mSlideFlag = true;
        mFistEnter = true;

        Window window = this.getWindow();
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | INVISBLE
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | NAVIGATION_BAR_TRANSPARENT);
        window.setStatusBarColor(Color.TRANSPARENT);
        window.setNavigationBarColor(Color.TRANSPARENT);


        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_HOME_MENU, Utils.DISABLE_STATUS_BAR);
        bindLauncherService();

        if (OplusDeviceCaseUtilsKt.isAvailable()) {
            registerCallBack();
        } else {
            brightScreen();
        }

        if (mBtnLockClose != null) {
            mBtnLockClose.setOnTouchListener((v, event) -> {
                Log.d(TAG, "mBtnLockClose TOUCH " + event.toString());
                if (MotionEvent.ACTION_DOWN == event.getAction()) {
                    startPressAnim(v, true);
                    sendMessageToLauncher(MSG_HIDE_LAUNCHER_CONTENT);
                } else if ((MotionEvent.ACTION_UP == event.getAction())
                        || (MotionEvent.ACTION_CANCEL == event.getAction())) {
                    startPressAnim(v, false);
                }
                return false;
            });
            mBtnLockClose.setOnClickListener(v -> {
                Log.d(TAG, "mBtnLockClose CLICKED ");
                closeAlarm();
            });
        }

        mView.setOnTouchListener((v, event) -> {
            Log.d(TAG, "mView TOUCH " + event.toString() + " mFlagChangeVolume:" + mFlagChangeVolume);
            if (!mFlagChangeVolume && (event.getAction() == MotionEvent.ACTION_DOWN)) {
                mFlagChangeVolume = true;
                AlarmKlaxon.volumeReduceByTime(this);
            }
            return false;
        });

        LegalHolidayParser.initializeHoliday();
    }

    @Override
    public void onResume() {
        super.onResume();
        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_HOME_MENU, Utils.DISABLE_STATUS_BAR);
    }

    /**
     * 亮屏
     */
    private void brightScreen() {
        mView.post(this::acquireCpuWakeLockPartial);
        try {
            if ((AlarmAlertWakeLock.getsCpuWake() != null)
                    && (AlarmAlertWakeLock.getsCpuWake().isHeld())) {
                AlarmAlertWakeLock.releaseCpuLockCpu(TAG + "AlarmAlertFullScreen onCreate");
            } else {
                Log.d(TAG, "null locking");
            }
        } catch (Exception e) {
            Log.e(TAG, "onCreate release wakelock error,e:" + e.getMessage());
        }
    }

    /**
     * 注册皮套模式监听
     */
    private void registerCallBack() {
        Log.i(TAG, "alarm registerOplusDeviceCase");
        if (OplusDeviceCaseUtilsKt.getManager() != null) {
            OplusDeviceCaseUtilsKt.getManager().registerCallback(
                    Executors.newSingleThreadExecutor(), mOplusDeviceCaseStateCallBack = new OplusDeviceCaseStateCallback() {
                        @Override
                        public void onStateChanged(int state) {
                            OplusDeviceCaseStateCallback.super.onStateChanged(state);
                            Log.i(TAG, "DeviceCaseState:" + state + "mFistEnter :" + mFistEnter);
                            runOnUiThread(() -> {
                                if (state == DEVICE_CASE_OPEN) {
                                    Log.i(TAG, "DEVICE_CASE_CLOSE");
                                    //皮套闭合
                                    if (mFistEnter) {
                                        View view = DeviceCaseAlarmAlertView.INSTANCE.createDeviceCaseView(
                                                AlarmAlertFullScreen.this, mAlarmInstance, AlarmAlertFullScreen.this);
                                        DeviceCaseAlarmAlertView.INSTANCE.showDeviceCaseView(view);
                                        brightScreen();
                                    } else {
                                        stopOrDismiss(ACTION_DIVE_CASE);
                                        DeviceCaseAlarmAlertView.INSTANCE.hideDeviceCaseView();
                                    }
                                } else {
                                    Log.i(TAG, "DEVICE_CASE_OPEN");
                                    //皮套打开
                                    if (!mFistEnter) {
                                        DeviceCaseAlarmAlertView.INSTANCE.hideDeviceCaseView();
                                    } else {
                                        brightScreen();
                                    }
                                }
                                mFistEnter = false;
                            });
                        }
                    });
        }
    }


    @Override
    public void deviceCaseSnooze() {
        ClockOplusCSUtils.statisticsDeviceCaseCloseAlarm(ClockOplusCSUtils.CLOSE_ALARM_TYPE_SNOOZE);
        snooze();
    }

    @Override
    public void deviceCaseClose() {
        ClockOplusCSUtils.statisticsDeviceCaseCloseAlarm(ClockOplusCSUtils.CLOSE_ALARM_TYPE_CLOSE);
        closeAlarm();
    }


    @Override
    protected boolean receivePowerKeyEvent() {
        return true;
    }

    @Override
    protected void onNewIntent(final Intent intent) {
        super.onNewIntent(intent);
        Log.i(TAG, "onNewIntent");
        acquireCpuWakeLockPartial();
        mFlagChangeVolume = false;
    }

    public void acquireCpuWakeLockPartial() {
        Log.i(TAG, "acquireCpuWakeLockPartial");
        if (null != getAlarmRepeat()) {
            AlarmAlertWakeLock.acquireCpuWakeLockPartial(AlarmClockApplication.getInstance(), getAlarmRepeat().getmAlarmDuration());
        } else {
            AlarmAlertWakeLock.acquireCpuWakeLockPartial(AlarmClockApplication.getInstance(), ScheduleUtils.DEFAULT_ALARM_TIMEOUT_SETTING);
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        int keyCode = event.getKeyCode();
        boolean up = event.getAction() == KeyEvent.ACTION_UP;

        Log.i(TAG, "dispatchKeyEvent KeyCode: " + keyCode + ", up = " + up);

        switch (keyCode) {
            case KeyEvent.KEYCODE_MENU:
                return true;
            case KeyEvent.KEYCODE_VOLUME_UP:
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_CAMERA:
                if (up) {
                    requestKeyGuard();
                    if (isSnoozeAvailble()) {
                        snooze();
                        AlarmRingOperateUtils.snoozeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_USER);
                    } else {
                        dismiss();
                        AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_VOLUME);
                    }
                    AlarmRingStatisticUtils.statisticsUserAlarmAction(AlarmAlertFullScreen.this, mAlarmInstance,
                            AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.VOLUME_KEY_CLICKED);
                }
                return true;
            case KeyEvent.KEYCODE_HEADSETHOOK:
                Log.d(TAG, "KEYCODE_HEADSETHOOK mIsPoweroffAlarm: " + mIsPoweroffAlarm);
                if (mIsPoweroffAlarm) {
                    return true;
                }
                break;
            default:
                break;
        }

        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_NORMAL, Utils.DEFAULT_STATUS_BAR);
    }

    @Override
    public void onDestroy() {
        mFlagChangeVolume = false;
        Utils.setHomeKeyLocked(this, Utils.KEY_LOCK_MODE_NORMAL, Utils.DEFAULT_STATUS_BAR);
        AlarmAlertWakeLock.releasePartialWakelock();
        super.onDestroy();
        AlarmUtils.setPowerKeyBroadcastSendable(AlarmClockApplication.getInstance(), false);

        sendMessageToLauncher(MSG_SHOW_LAUNCHER_CONTENT);
        unbindService(mLauncherServiceConnection);
        //隐藏皮套view
        DeviceCaseAlarmAlertView.INSTANCE.hideDeviceCaseView();
        if (OplusDeviceCaseUtilsKt.getManager() != null && (mOplusDeviceCaseStateCallBack != null)) {
            OplusDeviceCaseUtilsKt.getManager().unregisterCallback(mOplusDeviceCaseStateCallBack);
        }
        mFistEnter = false;
        DeviceCaseAlarmAlertView.INSTANCE.setShow(false);
    }

    @Override
    protected void onSlideStart() {
        super.onSlideStart();
        sendMessageToLauncher(MSG_HIDE_LAUNCHER_CONTENT);
    }

    private void sendMessageToLauncher(int what) {
        Log.d(TAG, "sendMessageToLauncher what: " + what);
        Message message = Message.obtain(null, what);
        message.arg1 = (what == MSG_HIDE_LAUNCHER_CONTENT) ? 1 : 0;
        if (mLauncherMessenger == null) {
            bindLauncherService();
        } else {
            try {
                mLauncherMessenger.send(message);
            } catch (RemoteException e) {
                Log.e(TAG, "sendMessageToLauncher error : " + e.getMessage());
            }
        }
    }

    private ServiceConnection mLauncherServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "onServiceConnected");
            mLauncherMessenger = new Messenger(service);
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "onServiceDisconnected");
            mLauncherMessenger = null;
        }
    };

    private void bindLauncherService() {
        Intent launcherService = new Intent();
        try {
            launcherService.setClassName("com.oplus.launcher", "com.oplus.keyguardservice.KeyGuardDismissedService");
            bindService(launcherService, mLauncherServiceConnection, Service.BIND_AUTO_CREATE);
            Log.i(TAG, "bindLauncherService");
        } catch (Exception e) {
            Log.e(TAG, "bindLauncherService error: " + e.getMessage());
            try {
                launcherService.setClassName("com.oppo.launcher", "com.oppo.keyguardservice.KeyGuardDismissedService");
                bindService(launcherService, mLauncherServiceConnection, Service.BIND_AUTO_CREATE);
            } catch (Exception e1) {
                Log.e(TAG, "bindLauncherService error: " + e1.getMessage());
            }
        }
    }

    @Override
    protected void onSlideEnd() {
        WeatherUtils.startWeatherService(this);
        super.onSlideEnd();
    }

    @Override
    protected void closeAlarm() {
        WeatherUtils.startWeatherService(this);
        super.closeAlarm();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        Log.d(TAG, "dispatchTouchEvent: " + ev.toString());
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.d(TAG, "onTouchEvent: " + event.toString());
        return super.onTouchEvent(event);
    }
}