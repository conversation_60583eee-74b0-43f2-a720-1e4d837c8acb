/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmAlertUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/10/13
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin   2022/10/13     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.alert

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AlarmSchedule
import com.oplus.alarmclock.utils.ClockConstant
import com.oplus.alarmclock.utils.Utils


/**
 * 稍后提醒
 */
fun alarmSnooze(context: Context) {
    Utils.sendLocalBroadcast(
        context,
        ClockConstant.ALARM_SNOOZE_ACTION
    )
}

/**
 * 取消
 */
fun alarmDismiss(context: Context) {
    Utils.sendLocalBroadcast(
        context,
        ClockConstant.ALARM_DISMISS_ACTION
    )
}

/**
 * 稍后提醒布局
 */
fun setAlarmSnooze(
    snoozeLayout: LinearLayout?,
    snoozeText: TextView?,
    alarmInstance: AlarmSchedule?,
    context: Context?
) {
    alarmInstance?.let {
        val alarm = it.alarm
        val item: Int = it.alarmSnooze
        context?.run {
            snoozeLayout?.apply {
                if (alarm?.getmGarbSwitch() == 1) {
                    visibility = View.INVISIBLE
                    isEnabled = false
                    return@apply
                }
                if (it.snoonzeTime >= it.alarm.ringNum - 1) {
                    visibility = View.INVISIBLE
                    isEnabled = false
                } else {
                    if (item != 0) {
                        visibility = View.VISIBLE
                        isEnabled = true
                        val interval: Int = it.alarm.getmSnoozeTime()
                        setSnoozeText(snoozeText, interval, this@run)
                    } else {
                        visibility = View.INVISIBLE
                        isEnabled = false
                    }
                }
            }
        }
    }
}

/**
 * 设置稍后提醒文本
 */
fun setSnoozeText(snoozeText: TextView?, interval: Int, context: Context) {
    context.run {
        snoozeText?.apply {
            text = when (interval) {
                ClockConstant.SNOOZE_DEFAULT_FIVE -> resources.getString(R.string.snooze_minutes_five)
                ClockConstant.SNOOZE_DEFAULT_TEN -> resources.getString(R.string.snooze_minutes_ten)
                ClockConstant.SNOOZE_DEFAULT_FIFTEEN -> resources.getString(R.string.snooze_minutes_fifteen)
                ClockConstant.SNOOZE_DEFAULT_TWENTY -> resources.getString(R.string.snooze_minutes_twenty)
                ClockConstant.SNOOZE_DEFAULT_TWENTY_FIVE -> resources.getString(R.string.snooze_minutes_twenty_five)
                ClockConstant.SNOOZE_DEFAULT_TWENTY_THRITY -> resources.getString(R.string.snooze_minutes_thrity)
                else -> resources.getString(R.string.snooze_alarm)
            }
        }
    }
}