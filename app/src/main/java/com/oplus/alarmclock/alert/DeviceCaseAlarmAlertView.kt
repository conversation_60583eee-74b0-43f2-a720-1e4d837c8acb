/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DeviceCaseAlertView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/10/13
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin   2022/10/13     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.alert

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.alarmclock.AlarmSchedule
import com.oplus.alarmclock.timer.DeviceCaseTimerAlertView
import com.oplus.alarmclock.utils.*
import com.oplus.alarmclock.utils.ClockOplusCSUtils.EVENT_DEVICE_CASE_ALARM_COUNT
import com.oplus.alarmclock.view.TimerTimeView
import com.oplus.clock.common.utils.Log
import com.oplus.hardware.devicecase.OplusDeviceCaseManager.FLAG_CONTENT_IN_VIEW_PORT
import java.util.Calendar
import java.util.Locale

/**
 * 皮套模式响铃
 */
object DeviceCaseAlarmAlertView : View.OnClickListener {

    private const val TAG = "DeviceCaseAlertView"
    private val mLargeWidth =
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_174)
    private val mLargeHeight =
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_40)
    var isShow = false
    private var mCaseView: View? = null
    private var mTimeView: TimerTimeView? = null
    private var mSnoozeTextView: TextView? = null
    private var mCloseTextView: TextView? = null
    private var mSnoozeLayout: LinearLayout? = null

    private var mAlarmInstance: AlarmSchedule? = null
    private var mContext: Context? = null
    private var mCallBack: DeviceCaseCallback? = null

    /**
     * 展示View
     */
    fun showDeviceCaseView(view: View?) {
        if (isAvailable()) {
            Log.i(TAG, "showDeviceCaseView")
            getManager()?.showContentView(view, FLAG_CONTENT_IN_VIEW_PORT)
            mCaseView = view
            //皮套模式响铃埋点
            ClockOplusCSUtils.onCommon(
                AlarmClockApplication.getInstance(),
                EVENT_DEVICE_CASE_ALARM_COUNT
            )
        }
    }

    /**
     * 展示皮套模式view
     */
    fun createDeviceCaseView(
        context: Context,
        alarmInstance: AlarmSchedule,
        callBack: DeviceCaseCallback
    ): View? {
        //设置中间稍后提醒和闹钟名字显示
        if (alarmInstance == null) {
            Log.e(TAG, "setAlertTime mAlarmInstance is null!")
            return null
        }
        mCallBack = callBack
        val inflater = LayoutInflater.from(context)
        mAlarmInstance = alarmInstance
        mContext = context
        mCaseView = inflater.inflate(R.layout.alarm_device_case_view, null)
        updateView(alarmInstance)
        return mCaseView
    }

    /**
     * 更新view
     */
    fun updateView(alarmInstance: AlarmSchedule) {
        mAlarmInstance = alarmInstance
        mCaseView?.let {
            mSnoozeTextView = it.findViewById(R.id.alarm_snooze_text)
            mSnoozeLayout = it.findViewById(R.id.layout_snooze)
            mTimeView = it.findViewById(R.id.time_view)
            mCloseTextView = it.findViewById(R.id.btn_lock_close)
            mCloseTextView?.let { close ->
                close.setOnClickListener(this)
                Utils.initPressFeedback(close, close)
            }
            Utils.initPressFeedback(mSnoozeTextView, mSnoozeTextView)
        }
        //设置时间
        setAlertTime()
    }

    /**
     * 隐藏皮套模式view
     */
    fun hideDeviceCaseView() {
        mTimeView?.removeAllViews()
        if (!DeviceCaseTimerAlertView.isShow) {
            getManager()?.hideContentView(mCaseView)
        }
        Log.i(TAG, "hideDeviceCaseView")
        isShow = false
    }

    /**
     * 设置时间和内容
     */
    fun setAlertTime() {
        mTimeView?.setDeviceCaseView()
        //设置头部时间日期星期几
        updateTimeView()
        val alarm = mAlarmInstance?.alarm
        //闹钟名称
        val alarmLabel = when {
            alarm?.getmGarbSwitch() == 1 -> {
                val calendar = Calendar.getInstance(Locale.getDefault())
                calendar[Calendar.HOUR_OF_DAY] = alarm.hour
                calendar[Calendar.MINUTE] = alarm.minutes
                calendar[Calendar.SECOND] = 0
                calendar[Calendar.MILLISECOND] = 0
                val timeInMillis = calendar.timeInMillis
                mContext?.resources?.getString(
                    R.string.grab_alarm_start_on_time,
                    Formatter.formatTimeToHourMinutes(AlarmClockApplication.getInstance(), timeInMillis)
                )
            }
            else -> mAlarmInstance?.alarm?.label
        }
        val textLabel = mCaseView?.findViewById<TextView>(R.id.alarm_label)
        if (FoldScreenUtils.screenDisplayModel() == FoldScreenUtils.SCREEN_DISPLAY_LARGE) {
            val layout = mSnoozeTextView?.layoutParams;
            layout?.width = mLargeWidth
            layout?.height = mLargeHeight
            mSnoozeTextView?.layoutParams = layout
            val closeLayout = mCloseTextView?.layoutParams
            closeLayout?.width = mLargeWidth
            closeLayout?.height = mLargeHeight
            mCloseTextView?.layoutParams = closeLayout
        }
        if (!TextUtils.isEmpty(alarmLabel)) {
            textLabel?.text = alarmLabel
        }
        if (alarm?.getmGarbSwitch() == 1) {
            val label = mAlarmInstance?.alarm?.label
            val description = if (TextUtils.isEmpty(label)) {
                mContext?.resources?.getString(R.string.grab_alarm_title)
            } else {
                label
            }
            mCaseView?.findViewById<TextView>(R.id.alarm_description)?.text = description
            mCaseView?.findViewById<TextView>(R.id.layout_garb)?.visibility = View.VISIBLE
            mCloseTextView?.text = mContext?.resources?.getString(R.string.color_menu_activity_determine)
        }
        mSnoozeTextView?.setOnClickListener(this)
        setAlarmSnooze(mSnoozeLayout, mSnoozeTextView, mAlarmInstance, mContext)
        //只有关闭按钮时，调整底部间距
        if (mSnoozeLayout?.visibility == View.INVISIBLE) {
            mCloseTextView?.apply {
                val lay = layoutParams as MarginLayoutParams
                lay.bottomMargin =
                    AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_24)
                layoutParams = lay
            }
        }
    }

    /**
     * 更新时间
     */
    fun updateTimeView() {
        mTimeView?.update()
    }

    /**
     * 点击事件
     */
    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_lock_close -> {
                Log.i(TAG, "deviceCaseClose")
                mCallBack?.deviceCaseClose()
            }
            R.id.alarm_snooze_text -> {
                Log.i(TAG, "deviceCaseSnooze")
                mCallBack?.deviceCaseSnooze()
            }
        }
    }

    interface DeviceCaseCallback {
        fun deviceCaseSnooze()
        fun deviceCaseClose()
    }
}