/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.alarmclock.alert;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.PixelFormat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.WindowManager.LayoutParams;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmAlertWakeLock;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.timer.TimerFloatingWindowViewWrapper;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.timer.TimerWakeLock;
import com.oplus.alarmclock.utils.AlarmReflect;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.utils.Log;

import java.util.Calendar;
import java.util.Locale;

public class AlarmFloatingWindowManager {
    private static final String TAG = "AlarmFloatingWindowManager";

    //    private WindowManager mWindowManager;
    private AlarmFloatingWindowView mAlarmFloatingWindowView;
    private Context mContext;
    private boolean mWindowIsShow = false;
    private boolean mIsAlarmAlert;
    private AlarmRepeat mAlarmRepeat;

    AlarmFloatingWindowManager(Context context, AlarmSchedule alarmSchedule) {
        this(context, alarmSchedule, "", "");
    }

    public AlarmFloatingWindowManager(Context context, AlarmSchedule alarmSchedule, String timerIndex, String timerName) {
        mContext = context;
        if (mContext != null) {
//            mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);

            Configuration configuration = mContext.getResources().getConfiguration();
            int ori = configuration.orientation;
            mAlarmFloatingWindowView = getAlarmViewInstance(context, alarmSchedule,  timerIndex, timerName, ori);
            getAlarmRepeat(context);
        }
    }

    private AlarmRepeat getAlarmRepeat(Context context) {
        mAlarmRepeat = AlarmUtils.getAlarmsRepeatInfo(context);
        if (null == mAlarmRepeat) {
            mAlarmRepeat = new AlarmRepeat();
            mAlarmRepeat.initAlarmRepeat();
        }
        Log.i(TAG, " alarm repeat :" + mAlarmRepeat);
        return mAlarmRepeat;
    }

    private AlarmFloatingWindowView getAlarmViewInstance(Context context,
                                                         AlarmSchedule alarmSchedule, String timerIndex, String timerName, int ori) {
        Log.d(TAG, "getAlarmViewInstance alarmSchedule: " + alarmSchedule);
        if (alarmSchedule != null) {
            mIsAlarmAlert = true;
            //稍后提醒点击最大次数为50次
            Alarm alarm = alarmSchedule.getAlarm();
            if (alarm.getmGarbSwitch() == 1) {
                String label = alarm.getLabel();
                if (TextUtils.isEmpty(label)) {
                    label = context.getResources().getString(R.string.grab_alarm_title);
                }
                mAlarmFloatingWindowView = new AlarmFloatingWindowViewWrapper(context,
                        ori,
                        false,
                        context.getResources().getString(R.string.grab_alarm_start_on_time,
                                Formatter.formatTimeToHourMinutes(context, AlarmUtils.getTimeInMillisWithAlarm(alarm))),
                        label,
                        alarmSchedule.getId(),
                        getWindowParams(),
                        this,
                        alarmSchedule.getWorkdaySwitch() == 1,
                        true,
                        alarmSchedule);
            } else {
                mAlarmFloatingWindowView = new AlarmFloatingWindowViewWrapper(context, ori,
                        alarmSchedule.isSnoozeAvailble(alarmSchedule.getAlarm().getRingNum()), alarmSchedule.getAlarmLabel(),
                        Formatter.getCurrentTime(context, Calendar.getInstance().getTimeInMillis()), alarmSchedule.getId(),
                        getWindowParams(), this, alarmSchedule.getWorkdaySwitch() == 1, false, alarmSchedule);
            }
        } else {
            mIsAlarmAlert = false;
            mAlarmFloatingWindowView = new TimerFloatingWindowViewWrapper(context, ori, timerName,
                    getWindowParams(), this, timerIndex.equals(TimerService.DEFAULT_TIMER_INDEX));
        }
        return mAlarmFloatingWindowView;
    }

    public LayoutParams getWindowParams() {
        LayoutParams floatingWindowParams = new LayoutParams(
                LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT,
                mIsAlarmAlert ? LayoutParams.TYPE_SYSTEM_ERROR : LayoutParams.TYPE_APPLICATION_OVERLAY,
                (LayoutParams.FLAG_LAYOUT_IN_SCREEN | LayoutParams.FLAG_SPLIT_TOUCH
                        | LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | LayoutParams.FLAG_FULLSCREEN
                        | LayoutParams.FLAG_NOT_FOCUSABLE),
                (PixelFormat.TRANSLUCENT));
        floatingWindowParams.gravity = Gravity.TOP;
        if (mContext != null) {
            if (TimerSeedlingHelper.isSupportFluidCloud()) {
                floatingWindowParams.y = Utils.getStatusBarHeight(mContext);
            } else {
                floatingWindowParams.y = mContext.getResources().getDimensionPixelOffset(R.dimen.world_clock_notification_top);
            }
            if (!FoldScreenUtils.isScreenRealUnfold() || FoldScreenUtils.isDragonfly()) {
                floatingWindowParams.width = LayoutParams.MATCH_PARENT;
            } else {
                floatingWindowParams.width = mContext.getResources().getDimensionPixelOffset(R.dimen.float_view_width_in_fold_screen);
            }
        }
        floatingWindowParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_BEHIND;

        AlarmReflect.setWindowLayoutShowAllUser(floatingWindowParams);

        if ((mContext != null) && DeviceUtils.isAbnormalScreen(mContext)) {
            if (!FoldScreenUtils.isScreenRealUnfold()) {
                floatingWindowParams.layoutInDisplayCutoutMode = LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_NEVER;
            } else {
                floatingWindowParams.layoutInDisplayCutoutMode = LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS;
            }
        }

        return floatingWindowParams;
    }

    @SuppressLint("InvalidWakeLockTag")
    public void showFloatingWindow(AlarmSchedule alarmSchedule) {
        Log.i(TAG, "showFloatingWindow mWindowIsShow = " + mWindowIsShow + "  View = "
                + mAlarmFloatingWindowView + " alarmSchedule:" + alarmSchedule);
        if (mIsAlarmAlert) {
            mAlarmRepeat = getAlarmRepeat(mContext);
            Log.i(TAG, "showFloatingWindow alarmRepeat:" + mAlarmRepeat);
            AlarmAlertWakeLock.acquireCpuWakeLockDim(mContext, mAlarmRepeat.getmAlarmDuration());
        } else {
            TimerWakeLock.acquireCpuWakeLockFull(mContext);
        }
        if ((!mWindowIsShow) && (null != mAlarmFloatingWindowView)) {
            AppPlatformUtils.addViewForWindowManager(mContext, mAlarmFloatingWindowView, getWindowParams());
            mAlarmFloatingWindowView.setFocusableInTouchMode(true);
            mAlarmFloatingWindowView.setFloatingWindowViewVis(true);
            mWindowIsShow = true;
            Log.i(TAG, "showFloatingWindow window.addView completed");
        } else if (mWindowIsShow && (null != mAlarmFloatingWindowView)) {
            if (alarmSchedule != null) {
                AppPlatformUtils.removeViewForWindowManager(mContext, mAlarmFloatingWindowView);
                Configuration configuration = mContext.getResources().getConfiguration();
                int ori = configuration.orientation;
                mAlarmFloatingWindowView = getAlarmViewInstance(mContext, alarmSchedule, "", "", ori);
                AppPlatformUtils.addViewForWindowManager(mContext, mAlarmFloatingWindowView, getWindowParams());
                mAlarmFloatingWindowView.setFocusableInTouchMode(true);
                Log.i(TAG, "showFloatingWindow remove and window.addView completed");
            }
        }
    }

    public void hideFloatingWindow() {
        Log.i(TAG, "hideAlertFloatingWindow mWindowIsShow = " + mWindowIsShow + "  View = "
                + mAlarmFloatingWindowView);
        if (mWindowIsShow && (null != mAlarmFloatingWindowView)) {
            Log.i(TAG, "hidePopupWindow");
            mAlarmFloatingWindowView.unregisterReceiver();
            mAlarmFloatingWindowView.tearDownView();
            if (TimerSeedlingHelper.isSupportFluidCloud()) {
                mAlarmFloatingWindowView.playFloatingWindowDismissAnim();
            } else {
                removeViewForWindowManager();
            }
            mWindowIsShow = false;
        }
        if (mIsAlarmAlert) {
            AlarmAlertWakeLock.releaseCpuLockDim();
        } else {
            TimerWakeLock.releaseCpuLockFull();
        }
        mAlarmRepeat = null;
    }

    public void removeViewForWindowManager() {
        if (mAlarmFloatingWindowView != null) {
            AppPlatformUtils.removeViewForWindowManager(mContext, mAlarmFloatingWindowView);
        }
    }

    void hideFloatingWindowFromTimer() {
        hideFloatingWindow();
    }

    public void hideFloatingWindowFromAlarmAlert() {
        hideFloatingWindow();
    }

    public void tearDown() {
        mAlarmFloatingWindowView.unregisterReceiver();
    }

    public boolean floatingWindowIsShowing() {
        return mWindowIsShow;
    }

}
