/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
//OPLUS Java File Skip Rule:JavadocMethod,LineLength,ParameterNumber,DeclarationOrder
package com.oplus.alarmclock.alert;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.ColorKeyEventManager;
import android.os.OplusKeyEventManager;
import android.os.RemoteException;
import android.text.TextUtils;
import android.view.ContextThemeWrapper;
import android.view.InputEvent;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.coui.appcompat.animation.COUIMoveEaseInterpolator;
import com.coui.appcompat.button.COUIButton;
import com.coui.appcompat.textutil.COUIChangeTextUtil;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.heytap.addon.confinemode.OplusConfineModeManager;
import com.heytap.addon.os.IOplusExInputCallBack;
import com.heytap.addon.os.IOplusExService;
import com.heytap.addon.os.OplusExManager;
import com.heytap.addon.view.animation.OplusBezierInterpolator;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerAlertUtilsKt;
import com.oplus.alarmclock.timer.TimerSeedlingHelper;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.AppPlatformUtils;
import com.oplus.alarmclock.utils.BitmapUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.OplusDeviceCaseUtilsKt;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.clock.common.osdk.CompatUtils;
import com.oplus.clock.common.osdk.SystemPropNativeUtils;
import com.oplus.clock.common.utils.Log;
import com.oplus.clock.common.utils.VersionUtils;
import com.oplus.compat.os.ServiceManagerNative;
import com.oplus.hardware.devicecase.OplusDeviceCaseStateCallback;
import com.oplus.wrapper.os.ServiceManager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.Executors;

import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;

import static com.oplus.alarmclock.alarmclock.fluid.AlarmSnoozeSeedingHelper.SUPPORT_ALARM_SNOOZE_FLUID;
import static com.oplus.alarmclock.utils.ClockConstant.DEVICE_CASE_OPEN;

public abstract class AlarmFloatingWindowView extends FrameLayout implements View.OnClickListener {

    public static final String SCREEN_CORNER_RADIANS_167 = "167";
    public static final String SCREEN_CORNER_RADIANS_105_4 = "105,105,105,105";
    public static final String SCREEN_CORNER_RADIANS_108 = "108,108,111,111";
    public static final String SCREEN_CORNER_RADIANS_110_4 = "110,110,110,110";
    public static final String SCREEN_CORNER_RADIANS_81 = "81,81,66,66";
    public static final int FLOATING_VIEW_TYPE_ALARM = 1;
    public static final int FLOATING_VIEW_TYPE_TIMER = 2;
    public static final float FLOATING_VIEW_WIDTH_PERCENT = 0.67f;
    protected static final int ACTION_DISMISS = 1;
    protected static final int ACTION_SNOOZE = 2;
    protected static final int ACTION_RESTART = 3;
    protected static final int ANIM_STATE_IDLE = 0;
    protected static final int ANIM_STATE_HIDING = 1;
    protected static final int ANIM_STATE_DISPLAY = 2;
    protected static final int ANIM_STATE_EXPAND = 3;
    private static final int SPRING_START_DELAY = 83;
    private static final int GUIDE_ANIMATION_DURATION = 250;
    private static final int HIDE_ANIMATION_DURATION = 300;
    private static final int ACTION_HIDE_WINDOW_VIEW = 0;
    private static final int ACTION_BACK_ORI_POS = 4;
    private static final float SPRING_START_VELOCITY = 0f;
    private static final float SPRING_DAMPING_RATIO = 0.7f;
    private static final float SPRING_START_VALUE = 0.8f;
    private static final float SPRING_FINAL_POSITION = 1f;
    private static final float DUMP = 0.5f;
    private static final boolean DEBUG = true;
    private static final String TAG = "AlarmFloatingWindowView";
    private static final int BUTTON_TRANSPARENT = 15;
    public static boolean sIsBigRadians;
    protected int mAnimatingState = ANIM_STATE_IDLE;
    protected TextView mRingtoneInfo;
    protected ImageView mRingtoneIcon;
    protected Context mContext;
    protected boolean mIrremovable;
    protected String mAlarmNameStr;
    protected String mAlarmTimeStr;
    protected boolean mIsGarbAlarm;

    static {
        try {
            // Device that has big corner reaians screen, like 17107.
            String rcSizeProp = SystemPropNativeUtils.get("ro.display.rc.size", "");
            sIsBigRadians = SCREEN_CORNER_RADIANS_167.equals(rcSizeProp)
                    || SCREEN_CORNER_RADIANS_105_4.equals(rcSizeProp)
                    || SCREEN_CORNER_RADIANS_108.equals(rcSizeProp)
                    || SCREEN_CORNER_RADIANS_81.equals(rcSizeProp)
                    || SCREEN_CORNER_RADIANS_110_4.equals(rcSizeProp);
        } catch (Exception e) {
            Log.e("AlarmFloatingWindowView", "ro.display.rc.size error:" + e);
        }
    }

    private int mCurrentFloatingViewType = FLOATING_VIEW_TYPE_ALARM;
    private View mWindowView = null;
    private View mFloatingWindowView;
    private View mFloatingBg;
    private TextView mTitle;
    private TextView mDescription;
    private AnimationBundle mFloatingWindowViewAnimations = new AnimationBundle();
    private WindowManager mWindowManager;
    private IOplusExService mExManager = null;
    private float mGestureStartY;
    private float mGestureLastTouchY;
    private int mWindowHeight;
    private int mViewExpandHeight;
    private int mViewHideHeight;
    private boolean mIsFling;
    private boolean mIsSnoozeAvalible;
    private boolean mIsWordDaySwitchOpened;
    private ClockOplusExInputCallBack mIOplusExInputCallBack;
    private ClockConfineModeObserver mConfineModeObserver;
    private boolean mIsNotCTS;
    private Interpolator mInterpolator;

    private OplusDeviceCaseStateCallback mOplusDeviceCaseStateCallBack;
    private boolean mFistEnter = true;

    private float mFontScale;
    private float mDafOffsetTextSizeSp12;
    private float mDafOffsetTextSizeSp13;
    private float mDafOffsetTextSizeSp14;
    private boolean mCanUseFluidCloudStyle = true;


    private AnimatorUpdateListener mUpdateListener = new AnimatorUpdateListener() {
        public void onAnimationUpdate(ValueAnimator animation) {
            Object animatedValue = animation.getAnimatedValue("y");
            if (animatedValue != null) {
                int ty = (Integer) animatedValue;
                if (mAnimatingState == ANIM_STATE_HIDING) {
                    ty = -ty;
                }
                if (DEBUG) {
                    Log.v(TAG, "onAnimationUpdate  ty = " + ty);
                }
                updateViewAnimPosition(ty);
            }
        }
    };

    private static class ClockOplusExInputCallBack extends IOplusExInputCallBack.Stub {
        private WeakReference<AlarmFloatingWindowView> mWeakRef;

        ClockOplusExInputCallBack(AlarmFloatingWindowView view) {
            mWeakRef = new WeakReference<>(view);
        }

        @Override
        public void onInputEvent(InputEvent event) throws RemoteException {
            AlarmFloatingWindowView view = (mWeakRef == null) ? null : mWeakRef.get();
            if (view != null) {
                if (event instanceof KeyEvent) {
                    final KeyEvent keyEvent = (KeyEvent) event;
                    int keyCode = keyEvent.getKeyCode();
                    boolean up = keyEvent.getAction() == KeyEvent.ACTION_UP;

                    if (DEBUG) {
                        Log.i(TAG, "IOplusExInputCallBack KeyCode: " + keyCode + ", up = " + up);
                    }

                    switch (keyCode) {
                        case KeyEvent.KEYCODE_VOLUME_UP:
                        case KeyEvent.KEYCODE_VOLUME_DOWN:
                        case KeyEvent.KEYCODE_CAMERA:
                            if (up) {
                                view.onVolumeAndCameraKeyPressed();
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    /*
 2020-05-06 The system removes the original service（IOplusExInputCallBack） of listening to mobile phone keys
 because the IOplusExInputCallBack will listen to all keys, and the modified manager will only listen to registered keys
  */
    private List<ClockKeyEventCallBackR> mKeyEventManagerListR = new ArrayList<>();

    private static class ClockKeyEventCallBackR implements OplusKeyEventManager.OnKeyEventObserver {
        private WeakReference<AlarmFloatingWindowView> mWeakRef;

        ClockKeyEventCallBackR(AlarmFloatingWindowView view) {
            mWeakRef = new WeakReference<>(view);
        }

        @Override
        public void onKeyEvent(KeyEvent event) {
            dealKeyEvent(mWeakRef, event);
        }
    }

    private List<ClockKeyEventCallBackQ> mKeyEventManagerListQ = new ArrayList<>();

    private static class ClockKeyEventCallBackQ implements ColorKeyEventManager.OnKeyEventObserver {
        private WeakReference<AlarmFloatingWindowView> mWeakRef;

        ClockKeyEventCallBackQ(AlarmFloatingWindowView view) {
            mWeakRef = new WeakReference<>(view);
        }

        @Override
        public void onKeyEvent(KeyEvent event) {
            dealKeyEvent(mWeakRef, event);
        }
    }

    private static void dealKeyEvent(WeakReference<AlarmFloatingWindowView> weakRef, KeyEvent event) {
        Log.d(TAG, "ClockKeyEventCallBack onKeyEvent, keyCode: " + event.getKeyCode() + ", action: " + event.getAction());

        AlarmFloatingWindowView view = (weakRef == null) ? null : weakRef.get();
        if (view != null) {
            if (event instanceof KeyEvent) {
                final KeyEvent keyEvent = (KeyEvent) event;
                int keyCode = keyEvent.getKeyCode();
                boolean up = keyEvent.getAction() == KeyEvent.ACTION_UP;

                if (DEBUG) {
                    Log.i(TAG, "ClockKeyEventCallBack onKeyEvent KeyCode: " + keyCode + ", up = " + up);
                }

                switch (keyCode) {
                    case KeyEvent.KEYCODE_VOLUME_UP:
                    case KeyEvent.KEYCODE_VOLUME_DOWN:
                    case KeyEvent.KEYCODE_CAMERA:
                        if (up) {
                            view.onVolumeAndCameraKeyPressed();
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }


    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action == null) {
                Log.e(TAG, "switch user action is null");
                return;
            }
            Log.d(TAG, "switch user onReceive:" + action);
            if (Utils.isAboveQ()) {
                Log.d(TAG, "current user " + AppPlatformUtils.getCurrentUser());
            }
            switch (action) {
                case Intent.ACTION_USER_BACKGROUND:
                case Intent.ACTION_USER_FOREGROUND:
                    int alarmAction = mIsSnoozeAvalible ? ACTION_SNOOZE : ACTION_DISMISS;
                    hideFloatingWindowWithAction(alarmAction, AlarmRingOperateUtils.CLOSE_ALARM_USER);
                    break;
                case Intent.ACTION_LOCALE_CHANGED:

                    if (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_TIMER) {
                        mAlarmTimeStr = mIsNotCTS ? TimerAlertUtilsKt.getTimeMsgByCondition(mContext) : "";
                        updateView(mAlarmNameStr, mAlarmTimeStr);
                    } else if ((mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM)
                            && (mDescription != null) && TextUtils.isEmpty(mAlarmNameStr)) {
                        mAlarmTimeStr = Formatter.getCurrentTime(mContext, Calendar.getInstance().getTimeInMillis());
                        updateView(mAlarmNameStr, mAlarmTimeStr);
                    }
                    Log.i(TAG, "mAlarmNameStr:" + mAlarmNameStr + ",mAlarmTimeStr:" + mAlarmTimeStr);

                    break;
                default:
                    break;
            }
        }
    };

    public AlarmFloatingWindowView(
            Context context,
            int orientation,
            boolean isSnoozeAvalible,
            String time,
            String alertName,
            WindowManager.LayoutParams layoutParams,
            boolean isWordDaySwitchOpened,
            int floatingViewType,
            boolean isGarbAlarm,
            boolean isNotCTS
    ) {
        this(context, orientation, isSnoozeAvalible, time, alertName, layoutParams, isWordDaySwitchOpened, isGarbAlarm, floatingViewType);
        mIsNotCTS = isNotCTS;
    }

    public AlarmFloatingWindowView(
            Context context,
            int orientation,
            boolean isSnoozeAvalible,
            String time,
            String alertName,
            WindowManager.LayoutParams layoutParams,
            boolean isWordDaySwitchOpened,
            boolean isGarbAlarm,
            int floatingViewType
    ) {
        super(context);
        mContext = context;
        mIsGarbAlarm = isGarbAlarm;
        try {
            String serviceName = VersionUtils.isOsVersion11_3() ? android.os.OplusExManager.SERVICE_NAME : OplusExManager.SERVICE_NAME;
            Log.d(TAG, "exServiceName : " + serviceName);
            if (CompatUtils.supportOsdk()) {
                mExManager = IOplusExService.Stub
                        .asInterface(ServiceManager.getService(serviceName));
            } else {
                mExManager = IOplusExService.Stub
                        .asInterface(ServiceManagerNative.getService(serviceName));
            }
        } catch (NoClassDefFoundError | NoSuchFieldError | NoSuchMethodError | Exception e) {
            Log.e(TAG, "get mExManager error " + e.getMessage());
        }
        registerKeyEvent();
        mAlarmTimeStr = time;
        mAlarmNameStr = alertName;
        Log.d(TAG, "AlarmFloatingWindowView mAlarmTimeStr:" + mAlarmTimeStr + " mAlarmNameStr:" + mAlarmNameStr);
        mCurrentFloatingViewType = floatingViewType;
        mIsSnoozeAvalible = isSnoozeAvalible;
        mIsWordDaySwitchOpened = isWordDaySwitchOpened;
        mCanUseFluidCloudStyle = canUseFluidCloudStyle(context);
        initTextFormatSizeInfo();
        initParameters(context, orientation);
        initView(orientation);
        registerBroadcast(context);
        registerConfineModeObserver(context);
        registerDeviceCallBack();
    }

    private void registerDeviceCallBack() {
        if (OplusDeviceCaseUtilsKt.isAvailable() && OplusDeviceCaseUtilsKt.getManager() != null) {
            OplusDeviceCaseUtilsKt.getManager().registerCallback(
                    Executors.newSingleThreadExecutor(), mOplusDeviceCaseStateCallBack = new OplusDeviceCaseStateCallback() {
                        @Override
                        public void onStateChanged(int state) {
                            OplusDeviceCaseStateCallback.super.onStateChanged(state);
                            Log.i(TAG, "DeviceCaseState:" + state);
                            if (state == DEVICE_CASE_OPEN && !mFistEnter) {
                                //皮套闭合
                                closeDevice();
                                OplusDeviceCaseUtilsKt.getManager().unregisterCallback(mOplusDeviceCaseStateCallBack);
                            }
                            mFistEnter = false;
                        }
                    });
        }

    }

    private void closeDevice() {
        if (mIsSnoozeAvalible && (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM)) {
            hideFloatingWindowWithAction(ACTION_SNOOZE, AlarmRingOperateUtils.ALARM_CLOSE_USER_DEVICE_CASE);
        } else {
            hideFloatingWindowWithAction(ACTION_DISMISS, AlarmRingOperateUtils.ALARM_CLOSE_USER_DEVICE_CASE);
        }
    }

    private void registerConfineModeObserver(Context context) {

        try {
            if (Utils.isAboveR()) {
                mConfineModeObserver = new ClockConfineModeObserver();
                boolean result = OplusConfineModeManager.getInstance()
                        .registerConfineModeObserver(context, mConfineModeObserver);
                Log.i(TAG, "registerConfineModeObserver success:" + result);
            }
        } catch (Exception e) {
            Log.e(TAG, "registerConfineModeObserver error:" + e);
        }

    }

    private void unregisterConfineModeObserver() {
        try {
            if (Utils.isAboveR() && (mConfineModeObserver != null)) {
                OplusConfineModeManager.getInstance()
                        .unregisterConfineModeObserver(mContext, mConfineModeObserver);
            }
        } catch (Exception e) {
            Log.e(TAG, "tearDownView unregisterConfineModeObserver error:" + e);
        }
    }


    protected static long getTimerSetTime(Context context) {
        return PrefUtils.getLong(context, AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP, TimerConstant.TIMER_SET_TIME_PREFERENCE, 0L);
    }

    public class ClockConfineModeObserver extends OplusConfineModeManager.ConfineModeObserver {

        @Override
        public void onChange(int oldMode, int newMode, int userId) {
            if (oldMode != newMode) {

                hideFloatingWindow();
                if (mIsSnoozeAvalible) {
                    snooze();
                    Log.d(TAG, "ClockConfineModeObserver snooze by confine mode:");
                } else {
                    Log.d(TAG, "ClockConfineModeObserver dismiss by confine mode");
                    dismiss();
                }
                Log.d(TAG, "ClockConfineModeObserver confine mode change to:" + newMode + ",userId:" + userId);
            }
        }
    }

    private void initTextFormatSizeInfo() {
        Resources r = mContext.getResources();
        mFontScale = r.getConfiguration().fontScale;
        mDafOffsetTextSizeSp12 = r.getDimension(R.dimen.text_size_sp_12);
        mDafOffsetTextSizeSp13 = r.getDimension(R.dimen.text_size_sp_13);
        mDafOffsetTextSizeSp14 = r.getDimension(R.dimen.text_size_sp_14);
    }

    private void setViewTextSizeForButton(TextView view, float dfltSize) {
        Utils.setSuitableFontSize(view, dfltSize, mFontScale, COUIChangeTextUtil.G2);
    }

    private void setViewTextSizeForAppName(TextView view) {
        if ((mCurrentFloatingViewType == FLOATING_VIEW_TYPE_TIMER) && mCanUseFluidCloudStyle) {
            Utils.setSuitableFontSize(view, mDafOffsetTextSizeSp13, mFontScale, COUIChangeTextUtil.G2);
        } else {
            Utils.setSuitableFontSize(view, mDafOffsetTextSizeSp14, mFontScale, COUIChangeTextUtil.G2);
        }
    }

    private void registerBroadcast(Context context) {
        if (mReceiver != null) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_USER_FOREGROUND);
            filter.addAction(Intent.ACTION_USER_BACKGROUND);
            filter.addAction(Intent.ACTION_LOCALE_CHANGED);
            if (context != null) {
                context.registerReceiver(mReceiver, filter);
            }
        }
    }

    private void unRegisterBroadcast() {
        if (mReceiver != null) {
            mContext.unregisterReceiver(mReceiver);
        }
    }

    private void initParameters(Context context, int orientation) {
        if (context == null) {
            return;
        }
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            if (DeviceUtils.isAbnormalScreen(context)) {
                initAbnormalScreenFloatingWindowHeight();
            } else {
                initDefaultFloatingWindowHeight();
            }
        } else {
            initDefaultFloatingWindowHeight();
        }
        mWindowManager = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
    }

    private void initAbnormalScreenFloatingWindowHeight() {
        mWindowHeight = (int) mContext.getResources()
                .getDimension(R.dimen.alarm_floating_window_half_height_abnormal);
        mViewExpandHeight = (int) mContext.getResources()
                .getDimension(R.dimen.alarm_floating_window_expand_height_abnormal);
        mViewHideHeight = (int) mContext.getResources()
                .getDimension(R.dimen.alarm_floating_window_hide_height_abnormal);
    }

    private void initDefaultFloatingWindowHeight() {
        if (sIsBigRadians) {
            mWindowHeight = (int) mContext.getResources()
                    .getDimension(R.dimen.alarm_floating_window_half_height_big_radians);
        } else {
            mWindowHeight = (int) mContext.getResources()
                    .getDimension(R.dimen.alarm_floating_window_half_height);
        }
        mViewExpandHeight = (int) mContext.getResources()
                .getDimension(R.dimen.alarm_floating_window_expand_height);
        mViewHideHeight = (int) mContext.getResources()
                .getDimension(R.dimen.alarm_floating_window_hide_height);
    }

    @SuppressLint("CutPasteId")
    private void initView(int orientation) {
        Log.d(TAG, "initView: mCanUseFluidCloudStyle:" + mCanUseFluidCloudStyle  + " mIsGarbAlarm:" + mIsGarbAlarm);
        checkAndSetNightTheme();
        ContextThemeWrapper themeContext = new ContextThemeWrapper(mContext, R.style.AppNoTitleTheme);
        COUIThemeOverlay.getInstance().applyThemeOverlays(themeContext);
        if (mCanUseFluidCloudStyle) {
            if (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_TIMER) {
                mWindowView = LayoutInflater.from(themeContext).inflate(R.layout.floating_view_timer_land_horizontal_fluid_cloud, null);
                mFloatingWindowView = mWindowView.findViewById(R.id.alarm_floating_window);
                mTitle = mWindowView.findViewById(R.id.timer_finished_title);
                mDescription = mWindowView.findViewById(R.id.timer_finished_des);
                mFloatingBg = mWindowView.findViewById(R.id.float_bg);
                mWindowView.findViewById(R.id.button_dismiss).setOnClickListener(this);
                mWindowView.findViewById(R.id.button_restart).setOnClickListener(this);
            } else if (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM) {
                if (mIsGarbAlarm) {
                    mWindowView = LayoutInflater.from(themeContext).inflate(R.layout.floating_view_alarm_garb_land_horizontal_fluid_cloud, null);
                } else {
                    mWindowView = LayoutInflater.from(themeContext).inflate(R.layout.floating_view_alarm_land_horizontal_fluid_cloud, null);
                    mRingtoneInfo = (TextView) mWindowView.findViewById(R.id.ringtone_info);
                    mRingtoneIcon = (ImageView) mWindowView.findViewById(R.id.ringtone_icon);
                    mWindowView.findViewById(R.id.button_snooze).setOnClickListener(this);
                    if (!mIsSnoozeAvalible) {
                        mWindowView.findViewById(R.id.button_snooze).setVisibility(GONE);
                    }
                }
                mFloatingWindowView = mWindowView.findViewById(R.id.alarm_floating_window);
                mTitle = mWindowView.findViewById(R.id.alarm_time);
                mDescription = (TextView) mWindowView.findViewById(R.id.alarm_ring_des);
                mFloatingBg = mWindowView.findViewById(R.id.float_bg);
                mWindowView.findViewById(R.id.button_dismiss).setOnClickListener(this);
            }
            setFloatingFluid();
        } else {
            mWindowView = LayoutInflater.from(themeContext).inflate(R.layout.floating_view_alarm_land_horizontal, null);
            COUIButton snoozeButton = mWindowView.findViewById(R.id.button_snooze);
            COUIButton dismissButton = mWindowView.findViewById(R.id.button_dismiss);

            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) dismissButton.getLayoutParams();

            if (mIsSnoozeAvalible && (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM)) {
                snoozeButton.setVisibility(VISIBLE);
                snoozeButton.setOnClickListener(this);
                lp.setMarginStart(mContext.getResources().getDimensionPixelSize(R.dimen.layout_dp_12));
            } else {
                snoozeButton.setVisibility(GONE);
                lp.setMarginStart(0);
            }
            mFloatingBg = mWindowView.findViewById(R.id.float_bg);
            mFloatingWindowView = mWindowView.findViewById(R.id.alarm_floating_window);
            ImageView appIconView = (ImageView) mWindowView.findViewById(R.id.app_icon);
            TextView appNameView = (TextView) mWindowView.findViewById(R.id.app_name);

            mTitle = (TextView) mWindowView.findViewById(R.id.alarm_name);
            mDescription = (TextView) mWindowView.findViewById(R.id.alarm_description);
            mRingtoneIcon = (ImageView) mWindowView.findViewById(R.id.ringtone_icon);

            appIconView.setBackground(BitmapUtils.getAppIconBitmap(mContext));
            setViewTextSizeForButton(appNameView, mDafOffsetTextSizeSp12);

            dismissButton.setOnClickListener(this);
            TextWeightUtils.setTextWeightNoChange(mTitle, TextWeightUtils.WEIGHT_MEDIUM);
            setViewTextSizeForAppName(mTitle);

            setViewTextSizeForButton(dismissButton, mDafOffsetTextSizeSp14);
            setViewTextSizeForButton(snoozeButton, mDafOffsetTextSizeSp14);
        }

        setViewTextSizeForAppName(mDescription);
        setAlarmTimeLabel(mAlarmNameStr, mAlarmTimeStr);
        updateRingtoneInfo(AlarmSpotifyUtils.getRingtoneInfo());
        this.addView(mWindowView);
    }

    /**
     * 设置流体云卡片样式数据
     */
    private void setFloatingFluid() {
        TextWeightUtils.setTextWeightNoChange(mTitle, TextWeightUtils.WEIGHT_NINE);
        TextWeightUtils.setTextWeightNoChange(mDescription, TextWeightUtils.WEIGHT_MEDIUM);
        if (FoldScreenUtils.isRealOslo()) {
            setFloatingWindowViewWidth(mFloatingWindowView, R.dimen.timer_float_window_large_width);
        } else if (DeviceUtils.isLandscapeScreen()) {
            setFloatingWindowViewWidth(mFloatingWindowView, R.dimen.timer_float_window_normal_landspace_width);
        }
    }

    private void setFloatingWindowViewWidth(View floatingWindowView, int dimensionsId) {
        MarginLayoutParams layoutParams = (MarginLayoutParams) floatingWindowView.getLayoutParams();
        layoutParams.width = getResources().getDimensionPixelSize(dimensionsId);
        floatingWindowView.setLayoutParams(layoutParams);
    }

    private int setAlpha(int c, int a) {
        int sc = Math.min(255, Math.max(0, (255 * a / 100))) << 24;
        return sc + c;
    }

    public void setAlarmTimeLabel(String alarmNameStr, String alarmTimeStr) {
        this.mAlarmNameStr = alarmNameStr;
        this.mAlarmTimeStr = alarmTimeStr;
        Log.d(TAG, "setAlarmTimeLabel mAlarmNameStr:" + mAlarmNameStr + " mAlarmTimeStr:" + mAlarmTimeStr);
        if (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM) {
            mTitle.setText(mAlarmTimeStr);
            if (mCanUseFluidCloudStyle && SUPPORT_ALARM_SNOOZE_FLUID) {
                if (mIsGarbAlarm) {
                    mDescription.setText(mAlarmNameStr);
                } else {
                    mDescription.setText(getAlarmScheduleLabel());
                }
            } else {
                mDescription.setText(getAlarmLabelWithTime());
            }
        } else {
            mTitle.setText(mContext.getString(R.string.timer_finished));
            if (mCanUseFluidCloudStyle) {
                mDescription.setText(alarmTimeStr);
            } else {
                mDescription.setText(getTimerLabelString(alarmTimeStr));
            }
        }
    }

    public void updateRingtoneInfo(String ringtoneInfo) {
        if (mCurrentFloatingViewType != FLOATING_VIEW_TYPE_ALARM) {
            setVisibilitySecure(mRingtoneInfo, GONE);
            setVisibilitySecure(mRingtoneIcon, GONE);
            return;
        }
        Log.d(TAG, "updateRingtoneInfo:" + ringtoneInfo);
        if (!TextUtils.isEmpty(ringtoneInfo)) {
            if (mCanUseFluidCloudStyle) {
                setTextSecure(mRingtoneInfo, ringtoneInfo);
                setVisibilitySecure(mRingtoneInfo, VISIBLE);
            } else {
                setTextSecure(mDescription, getAlarmLabelWithTime());
            }
            setVisibilitySecure(mRingtoneIcon, VISIBLE);
        } else {
            if (mCanUseFluidCloudStyle) {
                setVisibilitySecure(mRingtoneInfo, GONE);
            } else {
                setTextSecure(mDescription, getAlarmScheduleLabel());
            }
            setVisibilitySecure(mRingtoneIcon, GONE);
        }
    }

    private void setVisibilitySecure(View view, int visibility) {
        if (view != null) {
            view.setVisibility(visibility);
        }
    }

    private void setTextSecure(TextView textView, String txt) {
        if (textView != null) {
            textView.setText(txt);
        }
    }

    private String getTimerLabelString(String alarmTimeStr) {
        return getAlarmScheduleLabel() + " • " + alarmTimeStr;

    }

    private String getAlarmScheduleLabel() {
        if (TextUtils.isEmpty(mAlarmNameStr)) {
            if (mContext != null) {
                if (mCurrentFloatingViewType == FLOATING_VIEW_TYPE_ALARM) {
                    return mContext.getString(R.string.default_label);
                } else {
                    return mContext.getString(R.string.timer_title);
                }
            } else {
                return "";
            }
        }
        return mAlarmNameStr;
    }

    protected String getAlarmLabelWithTime() {
        String ringToneInfo = AlarmSpotifyUtils.getRingtoneInfo();
        String str = getAlarmScheduleLabel();
        if (!TextUtils.isEmpty(ringToneInfo)) {
            str += "｜" + ringToneInfo;
        }
        return str;
    }

    protected void recreateView(int orientation) {
        Log.i(TAG, "recreateView");
        this.removeView(mWindowView);
        initParameters(mContext, orientation);
        initView(orientation);
    }

    protected void updateAlarmSchedule(String name, String time) {
        mAlarmTimeStr = time;
        mAlarmNameStr = name;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (DEBUG) {
            Log.d(TAG, "onTouchEvent");
        }
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_POINTER_DOWN:
            case MotionEvent.ACTION_DOWN:
                if (mIrremovable) {
                    // hideFloatingWindowWithAction(ACTION_DISMISS);
                } else {
                    mGestureStartY = event.getRawY();
                    mGestureLastTouchY = event.getRawY();
                }
                if (DEBUG) {
                    Log.v(TAG, "*** DOWN ***");
                }
                break;
            case MotionEvent.ACTION_MOVE:
                int slideDistanceY = (int) (event.getRawY() - mGestureStartY);
                int moveDistanceY = (int) (event.getRawY() - mGestureLastTouchY);
                mGestureLastTouchY = event.getRawY();
                if (slideDistanceY > 0) {
                    break;
                }
                startDragWindowView(slideDistanceY, moveDistanceY);
                if (DEBUG) {
                    Log.v(TAG, "*** MOVE *** slideDistanceY = " + slideDistanceY);
                }
                break;
            case MotionEvent.ACTION_POINTER_UP:
            case MotionEvent.ACTION_UP:
                if (DEBUG) {
                    Log.v(TAG, "*** UP ***");
                }
                mIsFling = false;
                break;
            case MotionEvent.ACTION_CANCEL:
                if (DEBUG) {
                    Log.v(TAG, "*** CANCEL ***");
                }
                break;
            default:
                break;
        }

        if (!mIsFling && (action == MotionEvent.ACTION_UP) && !mIrremovable) {
            if (!shouldDragFloatingView()) {
                startFloatingWindowViewAnimation(ACTION_BACK_ORI_POS);
            }
        }
        return false;
    }

    protected void updateWindowView(WindowManager.LayoutParams windowLayoutParams) {
        if (isAttachedToWindow()) {
            mWindowManager.updateViewLayout(this, windowLayoutParams);
        }
    }

    private boolean shouldDragFloatingView() {
        int tansY = (int) mFloatingWindowView.getTranslationY();
        int partHeight = mViewExpandHeight;
        Log.i(TAG, "partHeight = " + partHeight + "tansY = " + tansY);
        if (tansY <= -mViewHideHeight) {
            mIrremovable = true;
            startFloatingWindowViewAnimation(ACTION_HIDE_WINDOW_VIEW);
            return true;
        }
        return false;
    }

    private void startDragWindowView(int slideDistance, int moveDistance) {
        if (!shouldDragFloatingView() && !mIrremovable) {
            mFloatingWindowView.setTranslationY((int) (slideDistance * DUMP));
        } else {
            mIrremovable = true;
        }
    }

    public void setFloatingWindowViewVis(boolean visible) {
        if (visible) {
            mWindowView.setVisibility(View.VISIBLE);
            playFloatingWindowDisplayAnim(mContext, mFloatingBg);
        } else {
            mWindowView.setVisibility(View.GONE);
        }
    }

    /**
     * 是否支持使用流体云样式
     * @param context
     * @return
     */
    private boolean canUseFluidCloudStyle(Context context) {
        boolean result = TimerSeedlingHelper.canUseFluidCloudStyle(context);
        if (mIsGarbAlarm) {
            return result && !DeviceUtils.isBreathMode(context);
        }
        return result;
    }

    private void playFloatingWindowDisplayAnim(Context context, final View floatView) {
        boolean isTimer = mCurrentFloatingViewType == FLOATING_VIEW_TYPE_TIMER;
        if (mCanUseFluidCloudStyle && (isTimer || (!isTimer && SUPPORT_ALARM_SNOOZE_FLUID))) {
            SpringForce springForce = new SpringForce(SPRING_FINAL_POSITION).setDampingRatio(SPRING_DAMPING_RATIO).setStiffness(SpringForce.STIFFNESS_LOW);
            SpringAnimation springScaleX = new SpringAnimation(floatView, SpringAnimation.SCALE_X).setStartValue(SPRING_START_VALUE).setStartVelocity(SPRING_START_VELOCITY).setSpring(springForce);
            SpringAnimation springScaleY = new SpringAnimation(floatView, SpringAnimation.SCALE_Y).setStartValue(SPRING_START_VALUE).setStartVelocity(SPRING_START_VELOCITY).setSpring(springForce);
            floatView.postDelayed(() -> {
                springScaleX.start();
                springScaleY.start();

            }, SPRING_START_DELAY);
            Animation animation = (Animation) AnimationUtils.loadAnimation(context, R.anim.anim_fluid_cloud_enter);
            if (animation != null) {
                animation.setInterpolator(new COUIMoveEaseInterpolator());
                animation.setFillEnabled(true);
                animation.setAnimationListener(new Animation.AnimationListener() {
                    private boolean mLayerTypeChanged = false;

                    @Override
                    public void onAnimationStart(Animation animation) {
                        if (floatView.getScaleX() >= SPRING_FINAL_POSITION || floatView.getScaleY() >= SPRING_FINAL_POSITION) {
                            floatView.setScaleX(SPRING_START_VALUE);
                            floatView.setScaleY(SPRING_START_VALUE);
                        }
                        if (floatView.hasOverlappingRendering() && floatView.getLayerType() == View.LAYER_TYPE_NONE) {
                            mLayerTypeChanged = true;
                            floatView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        if (mLayerTypeChanged) {
                            floatView.setLayerType(View.LAYER_TYPE_NONE, null);
                        }
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {
                        Log.d(TAG, "anim_fluid_cloud_enter onAnimationRepeat");
                    }
                });
                floatView.startAnimation(animation);
            }
        } else {
            Animation animation = (Animation) AnimationUtils.loadAnimation(context,
                    R.anim.alarm_floating_window_display_animation);
            float float1f = 1f;
            float float022f = 0.22f;
            float float034f = 0.34f;
            float float005f = 0.05f;
            if (animation != null) {
                animation.setInterpolator(new OplusBezierInterpolator(float022f, float034f, float005f, float1f, true));
                floatView.startAnimation(animation);
            }
        }
    }

    public void playFloatingWindowDismissAnim() {
        Animation animation = (Animation) AnimationUtils.loadAnimation(mContext, R.anim.anim_fluid_cloud_exit);
        if (animation != null) {
            animation.setInterpolator(new COUIMoveEaseInterpolator());
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                    Log.d(TAG, "playFloatingWindowDismissAnim onAnimationStart");
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    Log.d(TAG, "playFloatingWindowDismissAnim onAnimationEnd");
                    removeFloatingWindowByFluidCloud();
                }

                @Override
                public void onAnimationRepeat(Animation animation) {
                    Log.d(TAG, "playFloatingWindowDismissAnim onAnimationRepeat");
                }
            });
            if (mFloatingBg != null) {
                mFloatingBg.startAnimation(animation);
            } else {
                removeFloatingWindowByFluidCloud();
            }
        }
    }

    private void updateViewAnimPosition(int ty) {
        int translationY = (int) mFloatingWindowView.getTranslationY();
        if (DEBUG) {
            Log.d(TAG, "updateViewAnimPosition translationY = " + translationY + " ty =  " + ty);
        }
        mFloatingWindowView.setTranslationY(ty);
    }

    public void startFloatingWindowViewAnimation(final int action) {
        if (mAnimatingState != ANIM_STATE_IDLE) {
            if (DEBUG) {
                Log.v(TAG, "animation alredy start return  mAnimatingState = " + mAnimatingState
                        + " action = " + action);
            }
            return;
        }
        int viewHeight = mFloatingWindowView.getHeight();
        int statY = (int) mFloatingWindowView.getTranslationY();
        int endY = viewHeight;
        if (DEBUG) {
            Log.v(TAG, "startFloatingWindowViewAnimation  statY = " + statY + "  endY = " + endY
                    + "  action = " + action);
        }

        int distance = endY - Math.abs(statY);
        if (action == ACTION_BACK_ORI_POS) {
            setAnimatingState(ANIM_STATE_DISPLAY);
            endY = 0;
            distance = statY;
        } else if (action == ACTION_HIDE_WINDOW_VIEW) {
            setAnimatingState(ANIM_STATE_HIDING);
            endY = mWindowHeight;
            statY = -statY;
        } else {
            endY = 0;
            distance = statY;
        }
        float division = (float) distance / (float) viewHeight;

        long duration = 0;
        if (mAnimatingState == ANIM_STATE_HIDING) {
            duration = HIDE_ANIMATION_DURATION;
        } else {
            duration = Math.abs((long) (division * GUIDE_ANIMATION_DURATION));
        }
        if (DEBUG) {
            Log.v(TAG, "startFloatingWindowViewAnimation  duration = " + duration);
        }

        float floatOne = 1.0f;
        float float04f = 0.4f;
        if (mInterpolator == null) {
            mInterpolator = new OplusBezierInterpolator(float04f, 0f, float04f, floatOne, true);
        }
        mFloatingWindowViewAnimations.cancel();
        mFloatingWindowViewAnimations.add(Tweener.to(mFloatingWindowView, duration,
                "ease", mInterpolator,
                "delay", 0,
                "y", new int[]{statY, endY},
                "onUpdate", mUpdateListener, "onComplete",
                new AnimatorListenerAdapter() {
                    public void onAnimationEnd(Animator animator) {
                        if (DEBUG) {
                            Log.v(TAG, "onAnimationEnd ...");
                        }
                        if (mAnimatingState == ANIM_STATE_HIDING) {
                            if (mIsSnoozeAvalible) {
                                hideFloatingWindowWithAction(ACTION_SNOOZE, AlarmRingOperateUtils.ALARM_CLOSE_USER_FLOATING_SLIDE);
                            } else {
                                hideFloatingWindowWithAction(ACTION_DISMISS, AlarmRingOperateUtils.ALARM_CLOSE_USER_FLOATING_SLIDE);
                            }
                        }
                        setAnimatingState(ANIM_STATE_IDLE);
                    }
                }));
        mFloatingWindowViewAnimations.start();
    }

    void setAnimatingState(int state) {
        mAnimatingState = state;
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.button_dismiss) {
            Log.d(TAG, "onClick button_dismiss.");
            hideFloatingWindowWithAction(ACTION_DISMISS, AlarmRingOperateUtils.ALARM_CLOSE_USER_FLOATING_BUTTON);
        } else if (id == R.id.button_snooze) {
            Log.d(TAG, "onClick button_snooze.");
            hideFloatingWindowWithAction(ACTION_SNOOZE, AlarmRingOperateUtils.ALARM_CLOSE_USER_FLOATING_BUTTON);
        } else if (id == R.id.button_restart) {
            Log.d(TAG, "onClick button_restart.");
            hideFloatingWindowWithAction(ACTION_RESTART, AlarmRingOperateUtils.ALARM_CLOSE_USER_FLOATING_BUTTON);
        }
    }

    private void registerKeyEvent() {
        try {
            if (getKeyEventManagerVersion() == -1) {
                Log.i(TAG, "registerKeyEvent ColorKeyEventManager.VERSION is -1 , key event use old logic");
                if (mIOplusExInputCallBack == null) {
                    mIOplusExInputCallBack = new ClockOplusExInputCallBack(this);
                }
                if (mExManager != null) {
                    mExManager.registerInputEvent(mIOplusExInputCallBack);
                }
            } else {
                registerNewKeyEvent();
            }
        } catch (Exception e) {
            Log.e(TAG, "registerKeyEvent e : " + e.getMessage());
        }
    }

    private void unregisterKeyEvent() {
        try {
            if (getKeyEventManagerVersion() == -1) {
                Log.i(TAG, "unregisterKeyEvent ColorKeyEventManager.VERSION is -1 , key event use old logic");
                if ((mIOplusExInputCallBack != null) && (mExManager != null)) {
                    mExManager.unregisterInputEvent(mIOplusExInputCallBack);
                }
            } else {
                unregisterNewKeyEvent();
            }
        } catch (Exception e) {
            Log.e(TAG, "unregisterKeyEvent e : " + e.getMessage());
        }
    }

    private int getKeyEventManagerVersion() {
        if (Utils.isAboveR()) {
            return OplusKeyEventManager.getInstance().getVersion();
        } else {
            return ColorKeyEventManager.getInstance().getVersion();
        }
    }

    private void registerNewKeyEvent() {
        Log.i(TAG, "registerKeyEvent ColorKeyEventManager.VERSION is not -1 , key event use new logic");
        if (Utils.isAboveR()) {
            ClockKeyEventCallBackR cb = new ClockKeyEventCallBackR(this);
            mKeyEventManagerListR.add(cb);
            int flag = OplusKeyEventManager.LISTEN_VOLUME_UP_KEY_EVENT
                    | OplusKeyEventManager.LISTEN_VOLUME_DOWN_KEY_EVENT
                    | OplusKeyEventManager.LISTEN_CAMERA_KEY_EVENT;
            boolean b2b = OplusKeyEventManager.getInstance().registerKeyEventObserver(mContext, cb, flag);
            Log.d(TAG, "registerKeyEvent register file b2b " + b2b);
        } else {
            ClockKeyEventCallBackQ cb = new ClockKeyEventCallBackQ(this);
            mKeyEventManagerListQ.add(cb);
            int flag = ColorKeyEventManager.LISTEN_VOLUME_UP_KEY_EVENT
                    | ColorKeyEventManager.LISTEN_VOLUME_DOWN_KEY_EVENT
                    | ColorKeyEventManager.LISTEN_CAMERA_KEY_EVENT;
            boolean b2b = ColorKeyEventManager.getInstance().registerKeyEventObserver(mContext, cb, flag);
            Log.d(TAG, "registerKeyEvent register file b2b " + b2b);
        }
    }

    private void unregisterNewKeyEvent() {
        Log.i(TAG, "unregisterKeyEvent ColorKeyEventManager.VERSION is not -1 , key event use new logic");
        if (Utils.isAboveR()) {
            for (ClockKeyEventCallBackR cb : mKeyEventManagerListR) {
                boolean b2b = OplusKeyEventManager.getInstance().unregisterKeyEventObserver(mContext, cb);
                Log.d(TAG, "unregisterKeyEvent unregister clear b2b " + b2b);
            }
            mKeyEventManagerListR.clear();
        } else {
            for (ClockKeyEventCallBackQ cb : mKeyEventManagerListQ) {
                boolean b2b = ColorKeyEventManager.getInstance().unregisterKeyEventObserver(mContext, cb);
                Log.d(TAG, "unregisterKeyEvent unregister clear b2b " + b2b);
            }
            mKeyEventManagerListQ.clear();
        }
    }

    public void tearDownView() {
        unregisterKeyEvent();
        unRegisterBroadcast();
        unregisterConfineModeObserver();
    }

    /**
     * 超省模式检查设置暗色模式
     */
    private void checkAndSetNightTheme() {
        if (!DeviceUtils.isSuperPowerSaveMode(mContext)) {
            return;
        }
        Configuration configuration = mContext.getResources().getConfiguration();
        Log.d(TAG, "checkAndSetNightTheme uiMode=" + configuration.uiMode);
        if (configuration.uiMode == Configuration.UI_MODE_NIGHT_YES || configuration.uiMode == Configuration.UI_MODE_NIGHT_MASK) {
            return;
        }
        configuration.uiMode = Configuration.UI_MODE_NIGHT_YES;
        mContext.getResources().updateConfiguration(configuration, mContext.getResources().getDisplayMetrics());
    }

    abstract protected void updateView(String name, String time);

    abstract protected void dismiss();

    abstract protected void snooze();

    abstract protected void hideFloatingWindow();

    abstract protected void hideFloatingWindowWithAction(int action, int type);

    abstract protected void unregisterReceiver();

    abstract protected void onVolumeAndCameraKeyPressed();

    abstract protected void removeFloatingWindowByFluidCloud();

    private static class AnimationBundle extends ArrayList<Tweener> {
        private static final long serialVersionUID = 0xA84D78726F127468L;
        private boolean mSuspended;

        public void start() {
            if (mSuspended) {
                return; // ignore attempts to start animations
            }
            final int count = size();
            for (int i = 0; i < count; i++) {
                Tweener anim = get(i);
                anim.startAnimator();
            }
        }

        public void cancel() {
            final int count = size();
            for (int i = 0; i < count; i++) {
                Tweener anim = get(i);
                anim.cancelAnimator();
            }
            clear();
        }

        public void stop() {
            final int count = size();
            for (int i = 0; i < count; i++) {
                Tweener anim = get(i);
                anim.endAnimator();
            }
            clear();
        }

        public void setSuspended(boolean suspend) {
            mSuspended = suspend;
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    protected void onConfigurationChanged(Configuration configuration) {

    }

}
