/***********************************************************
** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.oplus.alarmclock.alert;

import java.util.Map.Entry;
import android.animation.PropertyValuesHolder;
import java.util.Iterator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import java.util.ArrayList;
import android.animation.TimeInterpolator;
import android.animation.ObjectAnimator;
import android.animation.Animator.AnimatorListener;

import java.util.HashMap;
import com.oplus.clock.common.utils.Log;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;


public class Tweener {

    private static final String TAG = "Tweener";
    private static final boolean IS_DEBUG = false;
    private static final String KEY_SIMULTANEOUS_TWEEN = "simultaneousTween";
    private static final String KEY_EASE = "ease";
    private static final String KEY_ON_UPDATE = "onUpdate";
    private static final String KEY_ON_UPDATE_LISTENER = "onUpdateListener";
    private static final String KEY_ON_COMPLETE = "onComplete";
    private static final String KEY_ON_COMPLETE_LISTENER = "onCompleteListener";
    private static final String KEY_DELAY = "delay";
    private static final String KEY_SYNC_WITH = "syncWith";

    private ObjectAnimator mAnimator;

    private static HashMap<Object, Tweener> sTweeners = new HashMap<>();
    // Listener to watch for completed animations and remove them.
    private static AnimatorListener sCleanupListener = new AnimatorListenerAdapter() {

        @Override
        public void onAnimationEnd(Animator animation) {
            remove(animation);
        }

        @Override
        public void onAnimationCancel(Animator animation) {
            remove(animation);
        }
    };

    public Tweener(ObjectAnimator anim) {
        mAnimator = anim;
    }

    void startAnimator() {
        if (mAnimator != null) {
            mAnimator.start();
        }
    }

    void cancelAnimator() {
        if (mAnimator != null) {
            mAnimator.cancel();
        }
    }

    void endAnimator() {
        if (mAnimator != null) {
            mAnimator.end();
        }
    }

    private static void remove(Animator animator) {
        Iterator<Entry<Object, Tweener>> iterator = sTweeners.entrySet().iterator();
        while (iterator.hasNext()) {
            Entry<Object, Tweener> item = iterator.next();
            if (animator == item.getValue().mAnimator) {
                iterator.remove();
                if (IS_DEBUG) {
                    Log.v(TAG, "remove tweener " + sTweeners.get(item.getKey()) + " size is: " + sTweeners.size());
                }
                break;
            }
        }
    }

    public static Tweener to(Object obj, long durationTime, Object... vars) {
        AnimatorUpdateListener updateListener = null;
        TimeInterpolator interpolator = null;
        long delayTime = 0;
        AnimatorListener listener = null;

        int varsLength = vars == null ? 0 : vars.length;

        ArrayList<PropertyValuesHolder> propList = new ArrayList<>(varsLength / 2);
        for (int j = 0; j < varsLength; j = j + 2) {
            if (!(vars[j] instanceof String)) {
                throw new IllegalArgumentException("the Key should be string " + vars[j]);
            }
            String theKey = (String) vars[j];
            Object theValue = vars[j + 1];

            if (KEY_EASE.equals(theKey)) {
                interpolator = (TimeInterpolator) theValue;
            } else if (KEY_SIMULTANEOUS_TWEEN.equals(theKey)) {

            } else if (KEY_ON_UPDATE.equals(theKey) || KEY_ON_UPDATE_LISTENER.equals(theKey)) {
                updateListener = (AnimatorUpdateListener) theValue;
            } else if (KEY_ON_COMPLETE.equals(theKey) || KEY_ON_COMPLETE_LISTENER.equals(theKey)) {
                listener = (AnimatorListener) theValue;
            } else if (KEY_DELAY.equals(theKey)) {
                delayTime = ((Number) theValue).longValue();
            } else if (KEY_SYNC_WITH.equals(theKey)) {

            } else if (theValue instanceof Number) {
                float floatValue = ((Number) theValue).floatValue();
                propList.add(PropertyValuesHolder.ofFloat(theKey, floatValue));
            }else if (theValue instanceof float[]) {
                float one = ((float[]) theValue)[0];
                float two = ((float[]) theValue)[1];
                propList.add(PropertyValuesHolder.ofFloat(theKey, one, two));
            } else if (theValue instanceof int[]) {
                int one = ((int[]) theValue)[0];
                int two = ((int[]) theValue)[1];
                propList.add(PropertyValuesHolder.ofInt(theKey,one, two));
            } else {
                throw new IllegalArgumentException(
                        "argument error key is \"" + theKey + "\" value is " + theValue.getClass());
            }
        }

        ObjectAnimator animator = null;
        Tweener tweener = sTweeners.get(obj);
        if (tweener != null) {
            if (sTweeners.get(obj) != null) {
                animator = sTweeners.get(obj).mAnimator;
            }
            replace(propList, obj);
        } else {
            if (IS_DEBUG) {
                Log.d(TAG, "Add Tweener " + tweener);
            }
            animator = ObjectAnimator.ofPropertyValuesHolder(obj,
                    propList.toArray(new PropertyValuesHolder[propList.size()]));
            tweener = new Tweener(animator);
            sTweeners.put(obj, tweener);

        }

        if (animator != null) {

            animator.setDuration(durationTime);
            animator.setStartDelay(delayTime);

            if (interpolator != null) {
                animator.setInterpolator(interpolator);
            }

            if (updateListener != null) {
                animator.removeAllUpdateListeners();
                animator.addUpdateListener(updateListener);
            }
            if (listener != null) {
                animator.removeAllListeners();
                animator.addListener(listener);
            }
            animator.addListener(sCleanupListener);
        }

        return tweener;
    }

    public static void reset() {
        if (IS_DEBUG) {
            if (sTweeners.size() > 0) {
                Log.d(TAG, "reset " + sTweeners.size() + " animations");
            }
        }
        sTweeners.clear();
    }

    private static void replace(ArrayList<PropertyValuesHolder> props, Object... args) {
        for (final Object killobject : args) {
            Tweener tweener = sTweeners.get(killobject);
            if (tweener != null) {
                tweener.mAnimator.cancel();
                if (props != null) {
                    tweener.mAnimator
                            .setValues(props.toArray(new PropertyValuesHolder[props.size()]));
                } else {
                    sTweeners.remove(tweener);
                }
            }
        }
    }
}
