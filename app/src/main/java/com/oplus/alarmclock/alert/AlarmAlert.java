/************************************************************
 * Copyright 2016 OPLUS Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :<PERSON>arm<PERSON><PERSON><PERSON>: activity to show alarm ring view.
 * <p>
 * History :( ID, Date, Author, Description) v1.0, 2016-6-20, <PERSON>, create
 * OPLUS Java File Skip Rule:MethodLength,MethodComplexity,JavadocStyle
 ************************************************************/

package com.oplus.alarmclock.alert;

import android.app.ActivityOptions;
import android.app.WallpaperInfo;
import android.app.WallpaperManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.drawable.Animatable2;
import android.graphics.drawable.AnimatedVectorDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.imageview.COUIRoundImageView;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmCloseModelUtils;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.alarmclock.utils.AlarmRingStatisticUtils;
import com.oplus.alarmclock.provider.alarmring.AlarmRingOperateUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.utils.AlarmSpotifyUtils;
import com.oplus.alarmclock.utils.AppFeatureUtils;
import com.oplus.alarmclock.utils.BitmapUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FoldScreenUtils;
import com.oplus.alarmclock.utils.Formatter;
import com.oplus.alarmclock.utils.TextWeightUtils;
import com.oplus.alarmclock.utils.TimerConstant;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.MultiFingerView;
import com.oplus.clock.common.event.LiteEventBus;
import com.oplus.clock.common.utils.Log;
import com.oplus.utils.ContinueUtils;
import com.oplus.utils.DragonflyUtils;
import com.oplus.vfx.watergradient.VFXFrameLayout;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Locale;

import static android.view.View.VISIBLE;

/**
 * Alarm Clock alarm alert: pops visible indicator and plays alarm tone
 */
public class AlarmAlert extends AbsSliderActivity implements MultiFingerView.Callback {
    public static final String IS_DRAGONFLY = "is_dragonfly";
    public static final String ACTION_POWER_KEY_BROADCAST_OLD = "oppo.intent.action.POWER_BUTTON_ENDS_ALARMCLOCK";
    public static final String ACTION_POWER_KEY_BROADCAST = "oplus.intent.action.POWER_BUTTON_ENDS_ALARMCLOCK";
    /**
     * 皮套
     */
    public static final String ACTION_DIVE_CASE = "oplus.intent.action.DEVICE_CASE";
    /**
     * 内外屏接续
     */
    public static final String ACTION_DIVE_CON = "oplus.intent.action.CON";
    private static final boolean DEBUG = true;
    private static final String TAG = "AlarmAlert";
    private static final String POWER_OFF_FROM_ALARM = "isPoweroffAlarm";
    private static final float HALF_ZERO = 0.5f;
    private static final float ZERO_POINT_SEVEN = 0.7f;
    private static final float SNOOZE_CORNER_RADIUS = 31f;
    public static boolean sIsAlarmAlertRing = false;
    // This parameter is used to solve the screen off can stop the alarm by gotoSlepping().
    public static boolean sIsScreenOffToOn = true;
    public static boolean sIsViewAlive = false;

    protected AlarmSchedule mAlarmInstance;
    protected boolean mIsPoweroffAlarm;
    protected TextView mBtnLockClose;

    private boolean mIsStopped = false;
    private boolean mIsRegister = false;
    private boolean mAlarmHandled;
    // private View mView;
    private BitmapDrawable mLockBgBitmap;
    private Intent mIntent;
    private LocalBroadcastManager mLocalBroadcastManager;
    private AlarmRepeat mAlarmRepeat;
    private ImageView mIvLockAlarm;
    private Animatable2.AnimationCallback mIvLockAlarmCallback;
    private AnimatedVectorDrawable mIvLockAlarmDrawable;
    private AnimatedVectorDrawable mAnimatedVectorDrawable;
    private TextView mRingtoneInfo;
    private COUIRoundImageView mRingtoneImage;
    private ConstraintLayout mRingtoneGroup;
    /**
     * 稍后提醒
     */
    private TextView mSnoozeText;
    private DragonflyUtils mDragonflyUtils;
    /**
     * mIsContinue用于蜻蜓主屏和副屏的接续 true表示需要接续，只关闭界面不关闭功能
     */
    private boolean mIsContinue = false;

    public static void setIsViewAlive(boolean alive) {
        sIsViewAlive = alive;
    }

    private final BroadcastReceiver mLocalReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            Log.i(TAG, "mLocalReceiver Received broadcast:" + action);
            if (action == null) {
                return;
            }

            if (!mAlarmHandled) {
                switch (action) {
                    case ClockConstant.CLOCK_ALARM_SNOOZE_ACTION:
                        snooze();
                        break;
                    case ClockConstant.ALARM_DISMISS_ACTION:
                        dismiss();
                        break;
                    case TimerService.STOP_ALARM_ACTION:
                        stopOrDismiss(action);
                        break;
                    case ClockConstant.ALARM_CANCEL_NOTIFICATION_ACTION:
                        cancelCurrentNotification();
                        break;
                    case ClockConstant.ALARM_SNOOZE_OR_DISMISS_ACTION:
                    case ClockConstant.ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION:
                        stopOrDismissWithoutSetState();
                        break;
                    case ClockConstant.ALARM_DISMISS_SERVICE_ACTION:
                        dismiss(false);
                        break;
                    case ClockConstant.ALARM_RINGTONE_INFO_ACTION:
                        updateRingtoneInfo(intent);
                        break;
                    default:
                        Log.i(TAG, "Unknown broadcast: " + action);
                        break;
                }
            } else {
                Log.i(TAG, "Ignored broadcast: " + action);
            }
        }
    };
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (action == null) {
                return;
            }

            Log.d(TAG, "Received broadcast: " + action + ", mAlarmHandled: " + mAlarmHandled);
            if (!mAlarmHandled) {
                switch (action) {
                    case ACTION_POWER_KEY_BROADCAST_OLD:
                    case ACTION_POWER_KEY_BROADCAST:
                        stopOrDismiss(action);
                        AlarmUtils.setPowerKeyBroadcastSendable(context, false);
                        break;
                    case Intent.ACTION_SCREEN_OFF:
                        final boolean isPowerButtonAct = Utils.screenChangedByPowerButton();
                        Log.i(TAG, "screenChangedByPowerButton: " + isPowerButtonAct);
                        if (isPowerButtonAct) {
                            stopOrDismiss(action);
                        }
                        break;

                    case Intent.ACTION_TIME_TICK:
                        if (mAlarmTimeView != null) {
                            mAlarmTimeView.update();
                            DeviceCaseAlarmAlertView.INSTANCE.updateTimeView();
                        }
                        /**时间变化时重新设置一次动画效果*/
                        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS() && mVFXFrameLayout != null) {
                            setBackgroundAndCircleColor();
                        }
                        break;
                    case TimerService.TIMER_ALERT_ACTION:
                        stopOrDismiss(action);
                        break;
                    case ClockConstant.CLEAR_NOTIFICATION:
                    case ClockConstant.CLEAR_NOTIFICATION_OLD:
                        snooze();
                        break;
                    default:
                        Log.i(TAG, "Unknown broadcast: " + action);
                        break;
                }
            } else {
                Log.i(TAG, "Ignored broadcast: " + action);
            }
        }
    };


    @Override
    protected void onCreate(Bundle icicle) {
        requestWindowFeature(android.view.Window.FEATURE_NO_TITLE);
        super.onCreate(icicle);
        if (DEBUG) {
            Log.i(TAG, "onCreate(): Configuration: " + getResources().getConfiguration());
        }
        mIntent = getIntent();
        mAlarmInstance = mIntent.getParcelableExtra(ClockConstant.ALARM_INTENT_EXTRA);
        if (mAlarmInstance == null) {
            Log.e(TAG, "onCreate: Error: Got mAlarmInstance null!");
            return;
        }

        Log.d(TAG, "mAlarmInstance = " + mAlarmInstance);
        if (DeviceUtils.isMtkPlatform(AlarmClockApplication.getInstance())) {
            if (getIntent().hasExtra(POWER_OFF_FROM_ALARM)) {
                mIsPoweroffAlarm = getIntent().getBooleanExtra(POWER_OFF_FROM_ALARM, false);
            }
        } else {
            mIsPoweroffAlarm = false;
        }

        sIsAlarmAlertRing = true;

        mLocalBroadcastManager = LocalBroadcastManager.getInstance(AlarmClockApplication.getInstance());
        final Intent intentStopTimer = new Intent(TimerConstant.STOP_TIMERALERT);
        mLocalBroadcastManager.sendBroadcast(intentStopTimer);
        Window window = getWindow();
        int flags = WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_FULLSCREEN
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        if (!DeviceUtils.isSuperPowerSaveMode(this.getApplicationContext())) {
            flags |= WindowManager.LayoutParams.FLAG_SHOW_WALLPAPER;
        }
        window.addFlags(flags);
        updateLayout();
        // Register to get the alarm done/snooze/dismiss intent.
        final IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        filter.addAction(Intent.ACTION_TIME_TICK);
        filter.addAction(TimerService.TIMER_ALERT_ACTION);
        filter.addAction(ClockConstant.CLEAR_NOTIFICATION);
        filter.addAction(ClockConstant.CLEAR_NOTIFICATION_OLD);
        if (receivePowerKeyEvent()) {
            filter.addAction(ACTION_POWER_KEY_BROADCAST_OLD);
            filter.addAction(ACTION_POWER_KEY_BROADCAST);
        }

        final IntentFilter filterLocal = new IntentFilter(
                ClockConstant.ALARM_CANCEL_NOTIFICATION_ACTION);
        filterLocal.addAction(ClockConstant.CLOCK_ALARM_SNOOZE_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_DISMISS_ACTION);
        filterLocal.addAction(TimerService.STOP_ALARM_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_SNOOZE_OR_DISMISS_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_DISMISS_SERVICE_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_SNOOZE_OR_DISMISS_SERVICE_ACTION);
        filterLocal.addAction(ClockConstant.ALARM_RINGTONE_INFO_ACTION);
        if (!mIsRegister) {
            mLocalBroadcastManager.registerReceiver(mLocalReceiver, filterLocal);
            registerReceiver(mReceiver, filter, ClockConstant.OPLUS_SAFE_PERMISSION, null, RECEIVER_NOT_EXPORTED);
            mIsRegister = true;
        }

        observeFinishEvent();
        /**响铃开始时间，用于响铃时长埋点*/
        mStartRingTime = System.currentTimeMillis();
    }

    protected boolean receivePowerKeyEvent() {
        return false;
    }

    @Override
    public void onResume() {
        super.onResume();
        setIsViewAlive(true);
        Log.i(TAG, "onResume");
    }

    AlarmRepeat getAlarmRepeat() {
        if (null == mAlarmRepeat) {
            mAlarmRepeat = AlarmUtils.getAlarmsRepeatInfo(AlarmClockApplication.getInstance());
            if (null == mAlarmRepeat) {
                mAlarmRepeat = new AlarmRepeat();
                mAlarmRepeat.initAlarmRepeat();
            }
            Log.d(TAG, " alarm repeat :" + mAlarmRepeat);
        }
        return mAlarmRepeat;
    }

    private void setAlertTime() {
        //设置头部时间日期星期几
        if (mAlarmTimeView != null) {
            mAlarmTimeView.update();
        }
        //设置中间稍后提醒和闹钟名字显示
        if (mAlarmInstance == null) {
            Log.e(TAG, "setAlertTime mAlarmInstance is null!");
            return;
        }
        String alarmLabel = mAlarmInstance.getAlarmLabel();
        Alarm alarm = mAlarmInstance.getAlarm();
        if (alarm != null) {
            if (alarm.getmDefaultAlarm() == 1) {
                alarmLabel = getResources().getString(R.string.wake_up_alarm);
            } else if (alarm.getmGarbSwitch() == 1) {
                Calendar calendar = Calendar.getInstance(Locale.getDefault());
                calendar.set(Calendar.HOUR_OF_DAY, alarm.getHour());
                calendar.set(Calendar.MINUTE, alarm.getMinutes());
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long timeInMillis = calendar.getTimeInMillis();
                alarmLabel = getResources().getString(R.string.grab_alarm_start_on_time,
                        Formatter.formatTimeToHourMinutes(AlarmClockApplication.getInstance(), timeInMillis));
            }
        }
        if (TextUtils.isEmpty(alarmLabel)) {
            mLabelView.setText(getResources().getString(R.string.default_label));
        } else {
            mLabelView.setText(alarmLabel);
        }
        if (alarm != null && alarm.getmGarbSwitch() == 1) {
            mLabelView.setAlpha(ZERO_POINT_SEVEN);
            TextWeightUtils.setTextWeightNoChange(mLabelView, TextWeightUtils.WEIGHT_NINE);
            String alarmName = alarm.getLabel();
            if (TextUtils.isEmpty(alarmName)) {
                mLableDescription.setText(getResources().getString(R.string.grab_alarm_title));
            } else {
                mLableDescription.setText(alarmName);
            }
            mLayoutGarb.setVisibility(VISIBLE);
            mBtnLockClose.setText(getResources().getString(R.string.color_menu_activity_determine));
        }
        if (!mIsDragonfly) {
            dealAlarmTextSize(mLabelView);
        }
        AlarmAlertUtilsKt.setAlarmSnooze(mLayoutSnooze, mSnoozeText, mAlarmInstance, this);
        if (isSnoozeAvailble()) {
            mLayoutSnooze.setEnabled(true);
            mLayoutSnooze.setOnClickListener(arg0 -> {
                Log.d(TAG, "mLayoutSnooze CLICKED ");
                if (mDragging || mIsMoveBottomProcessing) {
                    Log.d(TAG, "invail Click Snooze");
                    return;
                }
                AlarmRingStatisticUtils.statisticsUserAlarmAction(AlarmClockApplication.getInstance(), mAlarmInstance, ClockOplusCSUtils.STR_PRESS_ALARM_SNOOZE_MENU, null);
                AlarmRingOperateUtils.snoozeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_USER);
                requestKeyGuard();
                snooze();
            });
            mLayoutSnooze.setOnTouchListener((v, event) -> {
                Log.d(TAG, "mLayoutSnooze TOUCH " + event.toString());
                if (MotionEvent.ACTION_DOWN == event.getAction()) {
                    startPressAnim(v, true);
                } else if ((MotionEvent.ACTION_UP == event.getAction())
                        || (MotionEvent.ACTION_CANCEL == event.getAction())) {
                    startPressAnim(v, false);
                }
                return false;
            });
        } else {
            mLayoutSnooze.setEnabled(false);
        }
    }


    // This method is overwritten in AlarmAlertFullScreen in order to show a
    // full activity with the wallpaper as the background.
    protected View inflateView() {
        int id = mIsDragonfly ? R.layout.alarm_fullscreen_alert_view_dragonfly : R.layout.alarm_fullscreen_alert_view;
        return LayoutInflater.from(AlarmClockApplication.getInstance()).inflate(id, null);
    }

    protected void updateLayout() {
        if (getIntent().hasExtra(IS_DRAGONFLY)) {
            mIsDragonfly = getIntent().getBooleanExtra(IS_DRAGONFLY, false);
        }
        if (mRoot == null) {
            mRoot = inflateView();
            setContentView(mRoot);
        }
        mFingerControl = findViewById(R.id.mfv_finger_control);
        mFingerControl.registOnTouchEventCallback(this);
        /**设定水生效果或背景*/
        setAquaticAnimationEffects();
        mView = findViewById(R.id.rl_content);
        mCoverView = findViewById(R.id.cover_view);
        mAlarmTimeView = findViewById(R.id.time_view);
        mIvLockAlarm = findViewById(R.id.iv_lock_alarm);
        mLabelView = findViewById(R.id.alarm_label);
        TextWeightUtils.setTextBold(mLabelView);
        mLayoutGarb = findViewById(R.id.layout_garb);
        mLableDescription = findViewById(R.id.alarm_description);
        mLayoutSnooze = findViewById(R.id.layout_snooze);
        mBtnLockClose = findViewById(R.id.btn_lock_close);
        mSnoozeText = findViewById(R.id.alarm_snooze_text);
        mLLSlide = findViewById(R.id.ll_slide);
        mRingtoneGroup = findViewById(R.id.lock_ringtone_group);
        mRingtoneImage = findViewById(R.id.lock_ringtone_img);
        mRingtoneInfo = findViewById(R.id.lock_ringtone_info);
        if (mIsDragonfly) {
            mRingtoneGroup.setVisibility(View.GONE);
        } else {
            mRingtoneGroup.setVisibility(View.INVISIBLE);
        }
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setColor(COUIContextUtil.getAttrColor(this, R.attr.couiColorPrimary));
        gradientDrawable.setCornerRadius(dip2px(this, SNOOZE_CORNER_RADIUS));
        mLayoutSnooze.setBackground(gradientDrawable);
        setIsStopped(false);
        //设置时间
        setAlertTime();
        //开启摇摆动画
        startAlarmAnim();

        //滑块动画初始化
        if (AlarmCloseModelUtils.Companion.getSInstance().getMCloseModel() == AlarmCloseModelUtils.CLOSE_MODEL_SLIDE) {
            if (mCoverView != null) {
                mCoverView.setVisibility(VISIBLE);
            }
            if (mLLSlide != null) {
                mLLSlide.setVisibility(VISIBLE);
            }
            if (mBtnLockClose != null) {
                mBtnLockClose.setVisibility(View.INVISIBLE);
            }
            //处理蜻蜓副屏滑动模式的稍后提醒被遮挡问题
            if (mIsDragonfly) {
                setSnoozeMargin();
            }
            startSlideAlarmAnim();
        } else {
            if (mCoverView != null) {
                mCoverView.setVisibility(View.GONE);
            }
            if (mLLSlide != null) {
                mLLSlide.setVisibility(View.GONE);
            }
            if (mBtnLockClose != null) {
                mBtnLockClose.setVisibility(VISIBLE);
            }
        }
        mDragonflyUtils = ContinueUtils.registerListener(this, mDragonflyUtils, isSmallScreen -> continueActivity(mIntent, mIsDragonfly));
        setViewSpacing();
    }

    private void setViewSpacing() {
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            ViewGroup.MarginLayoutParams timeViewLp = (ViewGroup.MarginLayoutParams) mAlarmTimeView.getLayoutParams();
            ViewGroup.LayoutParams layoutSnoozeLp = mLayoutSnooze.getLayoutParams();
            ViewGroup.LayoutParams btnLockCloseLp = mBtnLockClose.getLayoutParams();
            if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
                if (mIsDragonfly) {
                    /**副屏文字上下间距调整*/
                    timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_45);
                    if (AlarmCloseModelUtils.Companion.getSInstance().getMCloseModel() == AlarmCloseModelUtils.CLOSE_MODEL_SLIDE) {
                        //外屏下上滑关闭调整按钮和文字间距
                        ViewGroup.MarginLayoutParams labelViewLp = (ViewGroup.MarginLayoutParams) mLabelView.getLayoutParams();
                        labelViewLp.topMargin = labelViewLp.topMargin - getResources().getDimensionPixelSize(R.dimen.layout_dp_20);
                        mLabelView.setLayoutParams(labelViewLp);
                    }
                } else if (FoldScreenUtils.UiMode.LARGE_VERTICAL == obtainUiMode()) {
                    /**平板竖屏计时器响铃设置顶部间距*/
                    timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_152);
                    /**调整平板下按钮的宽度*/
                    layoutSnoozeLp.width = getResources().getDimensionPixelSize(R.dimen.layout_dp_280);
                    mLayoutSnooze.setLayoutParams(layoutSnoozeLp);
                    btnLockCloseLp.width = getResources().getDimensionPixelSize(R.dimen.layout_dp_160);
                    mBtnLockClose.setLayoutParams(btnLockCloseLp);
                } else if (FoldScreenUtils.UiMode.LARGE_HORIZONTAL == obtainUiMode()) {
                    /**平板横屏计时器响铃设置顶部间距*/
                    timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_100);
                    /**调整平板下按钮的宽度*/
                    layoutSnoozeLp.width = getResources().getDimensionPixelSize(R.dimen.layout_dp_344);
                    mLayoutSnooze.setLayoutParams(layoutSnoozeLp);
                    btnLockCloseLp.width = getResources().getDimensionPixelSize(R.dimen.layout_dp_220);
                    mBtnLockClose.setLayoutParams(btnLockCloseLp);
                } else if (FoldScreenUtils.UiMode.MIDDLE == obtainUiMode()) {
                    /**中屏文字上下间距调整*/
                    timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_54);
                } else {
                    /**直板机文字上下间距调整*/
                    timeViewLp.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_74);
                }
                mAlarmTimeView.setLayoutParams(timeViewLp);
            }
        } else {
            if (mIsDragonfly) {
                ViewGroup.MarginLayoutParams params = (RelativeLayout.LayoutParams) mAlarmTimeView.getLayoutParams();
                params.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_48);
                mAlarmTimeView.setLayoutParams(params);
            }
            //平板竖屏响铃设置顶部间距
            if (FoldScreenUtils.UiMode.LARGE_VERTICAL == obtainUiMode()) {
                ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mAlarmTimeView.getLayoutParams();
                layoutParams.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_120);
                mAlarmTimeView.setLayoutParams(layoutParams);
            }
        }

    }

    private void setAquaticAnimationEffects() {
        ImageView ivMainBg = findViewById(R.id.iv_main_bg);
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            /**去除以前的背景*/
            ivMainBg.setVisibility(View.GONE);
            if (!mIsDragonfly) {
                /**去除黑色背景*/
                mFingerControl.setBackground(null);
            }
            if (mVFXFrameLayout == null) {
                mVFXFrameLayout = new VFXFrameLayout(this);
            }
            setVFXFrameLayout();
            changeAquaticState(AQUATIC_STATE_01);
            RelativeLayout aquaticAnimationBg = mRoot.findViewById(R.id.aquatic_animation_bg);
            aquaticAnimationBg.setVisibility(VISIBLE);
            ViewParent parent = mVFXFrameLayout.getParent();
            if ((parent != null) && (parent instanceof ViewGroup)) {
                ((ViewGroup) parent).removeView(mVFXFrameLayout);
            }
            aquaticAnimationBg.addView(mVFXFrameLayout);
            /**设定重力加速度监听事件*/
            mVFXHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    doRegisterSensor();
                }
            }, DELAY_REGISTER_TIME);
        } else {
            if (mIsDragonfly) {
                ivMainBg.setBackgroundResource(R.drawable.alert_bg);
            } else {
                Log.d(TAG, "updateLayout: mLockBgBitmap: " + mLockBgBitmap);
                mLockBgBitmap = BitmapUtils.getLockBackground(AlarmClockApplication.getInstance());
                WallpaperManager wallpaperManager = WallpaperManager.getInstance(this);
                WallpaperInfo wallpaperInfo = (wallpaperManager == null) ? null : wallpaperManager.getWallpaperInfo();
                if (wallpaperInfo != null) {
                    // have dynamic wallpaper
                } else if (mLockBgBitmap != null) {
                    ivMainBg.setImageDrawable(mLockBgBitmap);
                }
            }
        }
    }


    public int dip2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + HALF_ZERO);
    }

    private void startSlideAlarmAnim() {
        Log.d(TAG, "mAnimatorSet slide start");
        ImageView upArrow = findViewById(R.id.up_arrow);

        mAnimatedVectorDrawable = (AnimatedVectorDrawable) upArrow.getDrawable();
        if (mAnimatedVectorDrawable != null) {
            mAnimatedVectorDrawable.registerAnimationCallback(new Animatable2.AnimationCallback() {
                @Override
                public void onAnimationEnd(Drawable drawable) {
                    if (mAnimatedVectorDrawable != null) {
                        mAnimatedVectorDrawable.start();
                    }
                }
            });
            mAnimatedVectorDrawable.start();
        }
    }

    private void updateRingtoneInfo(Intent intent) {
        String imageUrl = intent.getStringExtra(AlarmService.KEY_RINGTONE_IMG);
        String ringtone = intent.getStringExtra(AlarmService.KEY_RINGTONE_INFO);
        Log.d(TAG, "updateRingtoneInfo:" + imageUrl + ";" + ringtone);
        boolean showFlag = false;
        if (!TextUtils.isEmpty(ringtone)) {
            mRingtoneInfo.setText(ringtone);
            showFlag = true;
        }
        if (!TextUtils.isEmpty(imageUrl)) {
            AlarmSpotifyUtils.loadSpotifyImage(imageUrl, mRingtoneImage);
        }

        if (showFlag) {
            if (getIntent().hasExtra(IS_DRAGONFLY)) {
                if (getIntent().getBooleanExtra(IS_DRAGONFLY, false)) {
                    ViewGroup.MarginLayoutParams params = (RelativeLayout.LayoutParams) mAlarmTimeView.getLayoutParams();
                    params.topMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_24);
                    mAlarmTimeView.setLayoutParams(params);
                }
            }
            mRingtoneGroup.setVisibility(VISIBLE);
        }
    }

    private void startAlarmAnim() {
        Log.d(TAG, "mAnimatorSet start");
        if ((mLayoutSnooze == null) || (mLayoutSnooze.getVisibility() != VISIBLE)) {
            return;
        }
        if (mIvLockAlarm != null) {
            if (mIvLockAlarm.getDrawable() instanceof AnimatedVectorDrawable) {
                mIvLockAlarmDrawable = (AnimatedVectorDrawable) mIvLockAlarm.getDrawable();
                if (mIvLockAlarmCallback == null) {
                    mIvLockAlarmCallback = new Animatable2.AnimationCallback() {
                        @Override
                        public void onAnimationEnd(Drawable drawable) {
                            if (mIvLockAlarmDrawable != null) {
                                mIvLockAlarmDrawable.start();
                            }
                        }
                    };
                }
                mIvLockAlarmDrawable.registerAnimationCallback(mIvLockAlarmCallback);
                mIvLockAlarmDrawable.start();
            }
        }
    }

    protected void snooze() {
        snooze(true);
    }

    protected void dismiss() {
        dismiss(true);
    }

    protected void snooze(boolean needSetState) {
        mAlarmHandled = true;
        if (mAlarmInstance == null) {
            Log.e(TAG, "snooze: The alarm is ended otherway?");
            return;
        }

        Log.i(TAG, "snooze mAlarmInstance: " + mAlarmInstance);
        if (needSetState) {
            AlarmAlertUtilsKt.alarmSnooze(AlarmClockApplication.getInstance());
        }

//        cancelCurrentNotification();
        finish();
    }

    protected void dismiss(boolean needSetState) {
        mAlarmHandled = true;
        if (mAlarmInstance == null) {
            Log.e(TAG, "dismiss: The alarm is ended otherway?");
            return;
        }
        if (!mIsContinue) {
            Log.d(TAG, "[dismiss]: needSetState: " + needSetState + ", cancelNotification: " + mAlarmInstance.getId());
            if (needSetState) {
                AlarmAlertUtilsKt.alarmDismiss(AlarmClockApplication.getInstance());
            }
            if (mAlarmInstance.getAlarm() != null && mAlarmInstance.getAlarm().getmGarbSwitch() == 0) {
                AlarmUtils.stopAlarm(AlarmClockApplication.getInstance(), mAlarmInstance.getAlarmId());
            }
            ScheduleUtils.cancelNotification(AlarmClockApplication.getInstance(), mAlarmInstance.getId());
        }
        finish();
    }

    @Override
    protected void onSlideEnd() {
        super.onSlideEnd();
        Context context = AlarmClockApplication.getInstance();
        if (mAlarmInstance != null) {
            AlarmRingStatisticUtils.statisticsUserAlarmAction(context, mAlarmInstance,
                    ClockOplusCSUtils.STR_PRESS_ALARM_DISMISS_MENU, null);
            AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.CLOSE_ALARM_USER_SLIDE);
        }
        cancelCurrentNotification();
        dismiss();
        if (!DeviceUtils.isExpVersion(context)) {
            boolean morningClockStatus = MorningAlarmClock.ifSupportMorningBroadcast(context);
            if ((mAlarmInstance != null) && morningClockStatus) {
                Log.d(TAG, "onSlideEnd:  " + "Sliding starts to call the morning broadcast");
                PlayMorningTools.playMorning(context, mAlarmInstance);
            }
        }
        finish();
    }

    protected void closeAlarm() {
        requestKeyGuard();
        Context context = AlarmClockApplication.getInstance();
        if (mAlarmInstance != null) {
            AlarmRingStatisticUtils.statisticsUserAlarmAction(context, mAlarmInstance, ClockOplusCSUtils.STR_PRESS_ALARM_DISMISS_MENU, null);
            AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_BUTTON);
        }
        cancelCurrentNotification();
        dismiss();
        if (!DeviceUtils.isExpVersion(context)) {
            boolean morningClockStatus = MorningAlarmClock.ifSupportMorningBroadcast(context);
            if ((mAlarmInstance != null) && morningClockStatus) {
                Log.d(TAG, "onSlideEnd:  " + "Sliding starts to call the morning broadcast");
                PlayMorningTools.playMorning(context, mAlarmInstance);
            }
        }
        finish();
    }

    /**
     * this is called when a second alarm is triggered while a previous alert window is still
     * active.
     */
    @Override
    protected void onNewIntent(final Intent intent) {
        Log.i(TAG, "onNewIntent: " + intent.getAction());
        super.onNewIntent(intent);

        if (mIntent != intent) {
            final AlarmSchedule alarm = intent.getParcelableExtra(ClockConstant.ALARM_INTENT_EXTRA);
            if (alarm == null) {
                Log.w(TAG, "onNewIntent: AlarmSchedule is null.");
                return;
            }

            if ((mAlarmInstance != null) && (alarm.getId() == mAlarmInstance.getId())) {
                // no need to update
                return;
            } else {
                mAlarmInstance = alarm;
            }
            updateParaAndLayout(intent);
        }
    }

    private void updateParaAndLayout(Intent intent) {
        if (mAlarmInstance == null) {
            mAlarmInstance = intent.getParcelableExtra(ClockConstant.ALARM_INTENT_EXTRA);
        }
        Log.v(TAG, "updateParaAndLayout: " + mAlarmInstance);
        updateLayout();
        DeviceCaseAlarmAlertView.INSTANCE.updateView(mAlarmInstance);
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.d(TAG, "onStop: sIsScreenOffToOn: " + sIsScreenOffToOn + ", mIsStopped: " + mIsStopped);

        if (!sIsScreenOffToOn) {
            sIsScreenOffToOn = true;
            return;
        }

        if (mIsStopped) {
            finish();
        }
    }

    protected void stopOrDismiss(String action) {
        if (mAlarmInstance != null) {
            if (isSnoozeAvailble()) {
                snooze(true);
                AlarmRingOperateUtils.snoozeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.SNOOZE_ALARM_USER);
            } else {
                dismiss(true);
                if (ACTION_DIVE_CASE.equals(action)) {
                    AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_DEVICE_CASE);
                } else if (ACTION_DIVE_CON.equals(action)) {
                    AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_CON);
                }
            }
            if (TimerService.TIMER_ALERT_ACTION.equals(action) || TimerService.STOP_ALARM_ACTION.equals(action)) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(this, mAlarmInstance,
                        AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.TIMER_IS_ALERT);
                if (!isSnoozeAvailble()) {
                    AlarmUtils.asynSendMissAlarmNotification(this,mAlarmInstance);
                    AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_TIMER);
                }
            } else if (Intent.ACTION_SCREEN_OFF.equals(action) || ACTION_POWER_KEY_BROADCAST.equals(action) || ACTION_POWER_KEY_BROADCAST_OLD.equals(action)) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(this, mAlarmInstance,
                        AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.POWER_KEY_CLICKED);
                if (!isSnoozeAvailble()) {
                    AlarmRingOperateUtils.closeAlarm(mAlarmInstance.getAlarm(), AlarmRingOperateUtils.ALARM_CLOSE_USER_POWER);
                }
            } else if (Intent.ACTION_USER_PRESENT.equals(action)) {
                AlarmRingStatisticUtils.statisticsUserAlarmAction(AlarmAlert.this, mAlarmInstance,
                        AlarmRingStatisticUtils.EVENT_ALARM_AUTO_STOP, AlarmRingStatisticUtils.AlarmAutoStopExtraReason.USER_PRESENT);
            }
        }
    }

    private void stopOrDismissWithoutSetState() {
        if (mAlarmInstance != null) {
            if (isSnoozeAvailble()) {
                snooze(false);
            } else {
                dismiss(false);
            }
        }
    }

    private void cancelCurrentNotification() {
        if (mAlarmInstance != null) {
            ScheduleUtils.cancelNotification(AlarmClockApplication.getInstance(), mAlarmInstance.getId());
        }
    }

    protected boolean isSnoozeAvailble() {
        if ((mAlarmInstance == null) || (mAlarmInstance.getAlarm() == null) || (mAlarmInstance.getAlarm().getmGarbSwitch() == 1)) {
            return false;
        }
        return mAlarmInstance.isSnoozeAvailble(mAlarmInstance.getAlarm().getRingNum());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "onDestroy()");
        ContinueUtils.unregisterListener(this, mDragonflyUtils);
        setIsViewAlive(false);

        if ((mAnimatedVectorDrawable != null) && (mAnimatedVectorDrawable.isRunning())) {
            mAnimatedVectorDrawable.stop();
            mAnimatedVectorDrawable = null;
        }

        if (mIvLockAlarmDrawable != null) {
            mIvLockAlarmDrawable.unregisterAnimationCallback(mIvLockAlarmCallback);
            mIvLockAlarmDrawable.clearAnimationCallbacks();
            mIvLockAlarmCallback = null;
        }

        mAlarmRepeat = null;
        if (mAlarmTimeView != null) {
            mAlarmTimeView.removeAllViews();
            mAlarmTimeView = null;
        }

        // If the alarm instance is null the receiver was never registered and calling
        // unregisterReceiver will throw an exception.
        if (mIsRegister) {
            mLocalBroadcastManager.unregisterReceiver(mLocalReceiver);
            unregisterReceiver(mReceiver);
            mIsRegister = false;
        }
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            if (mTimer != null) {
                mTimer.cancel();
                mTimer = null;
            }
            if (mSensorManager != null) {
                mSensorManager.unregisterListener(this);
            }
        }
        unObserveFinishEvent();
        uploadRingTime();
    }

    private void uploadRingTime() {
        /**埋点*/
        mEndRingTime = System.currentTimeMillis();
        String ringTime = (mEndRingTime - mStartRingTime) + MS;
        HashMap<String, String> map = new HashMap();
        map.put(ClockOplusCSUtils.EVENT_LOCK_SCREEN_ALARM_RING_DURATION_KEY, ringTime);
        ClockOplusCSUtils.onCommon(this, ClockOplusCSUtils.EVENT_LOCK_SCREEN_ALARM_RING_DURATION, map);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (Utils.isAboveOS14() && !AppFeatureUtils.isLightOS()) {
            if (mVFXFrameLayout == null) {
                mVFXFrameLayout = new VFXFrameLayout(this);
            }
            setVFXFrameLayout();
        }
        setViewSpacing();
    }

    private void setIsStopped(boolean stopped) {
        if (DEBUG) {
            Log.i(TAG, "mIsStopped is set to " + stopped);
        }
        mIsStopped = stopped;
    }

    //判断文字大小时30dp时一行是否能显示得下，显示不下则设置字体为24dp
    private void dealAlarmTextSize(TextView labelView) {
        float layoutWidth = getResources().getDisplayMetrics().widthPixels - 2 * getResources().getDimensionPixelSize(R.dimen.layout_dp_30);
        float textSize30 = getResources().getDimensionPixelSize(R.dimen.text_size_dp_30);

        TextPaint textPaint = new TextPaint();
        textPaint.setTextSize(textSize30);
        float alarmLabelWidth = StaticLayout.getDesiredWidth(labelView.getText(), textPaint);

        if (alarmLabelWidth > layoutWidth) {
            labelView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelSize(R.dimen.layout_dp_24));
        } else {
            labelView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelSize(R.dimen.text_size_dp_30));
        }
        Log.d(TAG, "dealAlarmTextSize: layoutWidth : " + layoutWidth + " alarmLabelWidth: " + alarmLabelWidth);
    }

    private void setSnoozeMargin() {
        if (mLayoutSnooze != null) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mLayoutSnooze.getLayoutParams();
            layoutParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.layout_dp_54);
            mLayoutSnooze.setLayoutParams(layoutParams);
        }
    }

    /**
     * 蜻蜓界面接续
     * 主屏到副屏,稍后提醒/关闭
     * 副屏到主屏,接续
     */
    private void continueActivity(Intent intent, boolean isDragonflySmall) {
        ContinueUtils.unregisterListener(AlarmAlert.this, mDragonflyUtils);
        //为避免系统获取的isSmallScreen可能不准确，直接使用isDragonflySmall
        boolean isSmall = !isDragonflySmall;
        if (isSmall) {
            stopOrDismiss(ACTION_DIVE_CON);
        } else {
            mIsContinue = true;
            finish();
            intent.putExtra(IS_DRAGONFLY, false);
            ActivityOptions options = ActivityOptions.makeBasic();
            options.setLaunchDisplayId(0);
            startActivity(intent, options.toBundle());
        }
    }

    private void observeFinishEvent() {
        LiteEventBus.Companion.getInstance().with(
                ClockConstant.EVENT_FINISH_ALARM_FULLSCREEN,
                String.valueOf(this.hashCode())
        ).observe(this, o -> {
            Log.i(TAG, "finish event");
            finish();
        });
    }

    private void unObserveFinishEvent() {
        LiteEventBus.Companion.getInstance().releaseObserver(String.valueOf(this.hashCode()));
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        Log.d(TAG, "dispatchTouchEvent: " + ev.toString());
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.d(TAG, "onTouchEvent: " + event.toString());
        return super.onTouchEvent(event);
    }
}