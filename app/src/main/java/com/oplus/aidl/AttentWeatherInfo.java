/************************************************************
 * Copyright 2000-2014 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description     :weather widget to access weather provider
 * History        :( ID, Date, Author, Description)
 * v1.0, 2014-12-19,  shijie.liu, create
 ************************************************************/
package com.oplus.aidl;

import android.os.Parcel;
import android.os.Parcelable;

public class AttentWeatherInfo implements Parcelable {
    private static final String TAG = "AttentWeatherInfo";

    private int mWeatherId;
    private int mNightTemp;
    private int mDayTemp;
    private String mCurrentTemp;
    private String mDayWeather;

    public AttentWeatherInfo() {
    }

    public AttentWeatherInfo(Parcel source) {
        readFromParcel(source);
    }

    public static final Creator<AttentWeatherInfo> CREATOR = new Creator<AttentWeatherInfo>() {

        public AttentWeatherInfo createFromParcel(Parcel source) {
            return new AttentWeatherInfo(source);
        }

        public AttentWeatherInfo[] newArray(int size) {
            return new AttentWeatherInfo[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(mWeatherId);
        dest.writeInt(mNightTemp);
        dest.writeInt(mDayTemp);
        dest.writeString(mCurrentTemp);
        dest.writeString(mDayWeather);
    }

    public void readFromParcel(Parcel source) {
        mWeatherId = source.readInt();
        mNightTemp = source.readInt();
        mDayTemp = source.readInt();
        mCurrentTemp = source.readString();
        mDayWeather = source.readString();
    }

    public void setWeatherId(int weatherId) {
        mWeatherId = weatherId;
    }

    public int getWeatherId() {
        return mWeatherId;
    }

    public int getNightTemp() {
        return mNightTemp;
    }

    public void setNightTemp(int nightTemp) {
        mNightTemp = nightTemp;
    }

    public int getDayTemp() {
        return mDayTemp;
    }

    public void setDayTemp(int dayTemp) {
        mDayTemp = dayTemp;
    }

    public String getCurrentTemp() {
        return mCurrentTemp;
    }

    public void setCurrentTemp(String currentTemp) {
        mCurrentTemp = currentTemp;
    }

    public String getDayWeather() {
        return mDayWeather;
    }

    public void setDayWeather(String dayWeather) {
        mDayWeather = dayWeather;
    }
}
