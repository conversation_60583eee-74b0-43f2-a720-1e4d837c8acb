/************************************************************
 * Copyright 2000-2014 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description     :weather widget to access weather provider
 * History        :( ID, Date, Author, Description)
 * v1.0, 2014-12-19,  shijie.liu, create
 ************************************************************/

package com.oplus.aidl;

import com.oplus.aidl.AttentWeatherInfo;
import com.oplus.aidl.IExternalWeatherLocationListener;
import com.oplus.aidl.IPhoneStateListener;

interface IExternalWeatherWidgetService {
    float getTimeZoneOfAttendCity(long cityId);
    int getAttentCityCount(boolean includeLocalWeather);
    String getCurrentAttentCityName(long cityId);
    String getCurrentAttentProvinceName(long cityId);
    String getCurrentAttentCountryName(long cityId);
    boolean isLocationCity(long cityId);

    String getWeatherTypeById(int weatherId);

    long getCurrentCityId();

    long getCityIdByAttentCityId(long attentCityId);

    AttentWeatherInfo getOneAttentCityWeatherInfoList(long cityId, float timeZone);

    long getAdjacentCityId(long curCityId, boolean isNext);

    void updateWeatherinfo(boolean isNeedLocation);

    void registerLocationListener(IExternalWeatherLocationListener listener);

    void unRegisterLocationListener();

    long getFirstAttentCityId();

    long getLocationCityId();
    void listenPhoneState(IPhoneStateListener listener, int events);
}
