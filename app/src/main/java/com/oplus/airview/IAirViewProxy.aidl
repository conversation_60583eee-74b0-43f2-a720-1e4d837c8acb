/*
 ** Copyright (C), 2008-2018, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: IAirViewProxy.java
 ** Version: V 1.0
 ** Date : 2020-01-16
 ** Author: hegai
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.airview;

oneway interface IAirViewProxy {

    /**
     * display the view
     *
     * @param jasonData The type of jsonData is JSON, it is the content
     * what is used to display on the air view.
     */
    void display(String packageName, String jsonData);

    /**
     * close the view, if it is requested by the package
     */
    void cancel(String packageName);
}
