<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.oplus.alarmclock.view.NestedScrollableHost
        android:id="@+id/world_clock_big_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layoutDirection="locale"
        android:splitMotionEvents="false"
        tools:ignore="UnusedAttribute">


        <com.oplus.alarmclock.view.NestedScrollableHost
            android:id="@+id/world_clock_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <androidx.recyclerview.widget.COUIRecyclerView
                android:id="@+id/world_clock_big_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="@dimen/layout_dp_30"
                android:layout_marginEnd="8dp"
                android:scrollbars="none"
                android:fadingEdge="vertical"
                android:fadingEdgeLength="@dimen/layout_dp_33"
                android:requiresFadingEdge="vertical"
                android:forceDarkAllowed="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dial_clock_cl" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/dial_clock_cl"
                android:layout_width="@dimen/layout_dp_468"
                android:layout_height="@dimen/layout_dp_468"
                android:layout_marginTop="@dimen/layout_dp_30"
                android:onClick="@{clickListener::onClick}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <RelativeLayout
                    android:id="@+id/dial_clock_big_rl"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.oplus.alarmclock.view.dial.OOSWaterClockView
                        android:id="@+id/dial_clock_bg_big"
                        android:layout_width="@dimen/app_dial_shadow_big_width"
                        android:layout_height="@dimen/app_dial_shadow_big_width"
                        android:layout_centerInParent="true"
                        android:forceDarkAllowed="false"
                        app:water_circle_width="@dimen/layout_dp_333"
                        app:water_circle_height="@dimen/layout_dp_333"
                        app:scale_value="1.26" />

                    <com.oplus.alarmclock.view.dial.OOSAlarmDialClockTable
                        android:id="@+id/dial_clock_big_table"
                        android:layout_width="@dimen/layout_dp_333"
                        android:layout_height="@dimen/layout_dp_333"
                        android:layout_centerInParent="true"
                        android:forceDarkAllowed="false"
                        app:scale_value="1.26" />

                    <com.oplus.alarmclock.view.dial.OOSAlarmDialClockGlowTable
                        android:id="@+id/dial_clock_big_glow_table"
                        android:layout_width="@dimen/layout_dp_333"
                        android:layout_height="@dimen/layout_dp_333"
                        android:layout_centerInParent="true"
                        android:visibility="@{AppFeatureUtils.isLightOS() ? View.GONE : View.VISIBLE}"
                        android:forceDarkAllowed="false"
                        app:scale_value="1.26" />

                    <com.oplus.alarmclock.view.dial.AlarmDialClockHour
                        android:id="@+id/dial_clock_big_hour"
                        android:layout_width="@dimen/layout_dp_333"
                        android:layout_height="@dimen/layout_dp_333"
                        android:layout_centerInParent="true"
                        android:forceDarkAllowed="false"
                        app:scale_value="1.26" />

                    <com.oplus.alarmclock.view.dial.AlarmDialClockMinute
                        android:id="@+id/dial_clock_big_minute"
                        android:layout_width="@dimen/layout_dp_333"
                        android:layout_height="@dimen/layout_dp_333"
                        android:layout_centerInParent="true"
                        android:forceDarkAllowed="false"
                        app:scale_value="1.26" />

                    <com.oplus.alarmclock.view.dial.AlarmDialClockSecond
                        android:id="@+id/dial_clock_big_second"
                        android:layout_width="@dimen/layout_dp_333"
                        android:layout_height="@dimen/layout_dp_333"
                        android:layout_centerInParent="true"
                        android:forceDarkAllowed="false"
                        app:scale_value="1.26" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/dial_msg_big_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/layout_dp_10"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="2"
                    android:text="@{timeInfo}"
                    android:textColor="?attr/couiColorHintNeutral"
                    android:textDirection="locale"
                    android:textFontWeight="400"
                    android:textSize="@dimen/text_size_sp_14"
                    app:layout_constraintBottom_toBottomOf="@+id/dial_clock_big_rl"
                    app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                    app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl" />

                <com.oplus.alarmclock.view.dial.AlarmDialClockTextView
                    android:id="@+id/dial_word_time_big_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="@+id/dial_clock_big_rl"
                    app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                    app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl"
                    app:layout_constraintTop_toTopOf="@+id/dial_clock_big_rl" />

                <TextView
                    android:id="@+id/dial_word_msg_big_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/layout_dp_4"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="2"
                    android:text="@{timeInfo}"
                    android:textColor="?attr/couiColorHintNeutral"
                    android:textDirection="locale"
                    android:textFontWeight="400"
                    android:textSize="@dimen/text_size_sp_14"
                    app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                    app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl"
                    app:layout_constraintTop_toBottomOf="@+id/dial_word_time_big_tv" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.oplus.alarmclock.view.NestedScrollableHost>


        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/coordinator"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:fitsSystemWindows="false"
            android:orientation="vertical">

            <include
                android:id="@+id/world_clock_toolbar_include"
                layout="@layout/collapsing_clock_appbar_divider_layout" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <RelativeLayout
            android:id="@+id/button_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_layout_height"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.coui.appcompat.floatingactionbutton.COUIFloatingButton
                android:id="@+id/button"
                android:layout_width="@dimen/layout_dp_64"
                android:layout_height="@dimen/layout_dp_64"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/layout_dp_24"
                android:overScrollMode="always"
                android:transitionName="shared_element_fab"
                android:visibility="gone"
                app:fabExpandAnimationEnable="true" />

        </RelativeLayout>

    </com.oplus.alarmclock.view.NestedScrollableHost>

    <data>

        <import type="android.view.View" />
        <import type="com.oplus.alarmclock.utils.AppFeatureUtils" />

        <variable
            name="clickListener"
            type="View.OnClickListener" />

        <variable
            name="timeInfo"
            type="String" />
    </data>
</layout>