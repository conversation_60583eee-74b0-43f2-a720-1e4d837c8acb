<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <com.oplus.alarmclock.view.NestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/world_clock_big_cl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:splitMotionEvents="false"
        tools:ignore="UnusedAttribute">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5" />

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/world_clock_big_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/layout_dp_12"
            android:layout_marginTop="@dimen/layout_dp_60"
            android:layout_marginEnd="@dimen/layout_dp_12"
            android:layout_marginBottom="111dp"
            android:fadingEdge="vertical"
            android:fadingEdgeLength="@dimen/layout_dp_33"
            android:requiresFadingEdge="vertical"
            android:scrollbars="none"
            android:forceDarkAllowed="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/dial_clock_big_cl"
            android:layout_width="@dimen/layout_dp_360"
            android:layout_height="@dimen/layout_dp_360"
            android:layout_marginTop="@dimen/layout_dp_20"
            android:onClick="@{clickListener::onClick}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:id="@+id/dial_clock_big_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.oplus.alarmclock.view.dial.OOSWaterClockView
                    android:id="@+id/dial_clock_bg_big"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:forceDarkAllowed="false" />

                <com.oplus.alarmclock.view.dial.OOSAlarmDialClockTable
                    android:id="@+id/dial_clock_big_table"
                    android:layout_width="@dimen/app_dial_stand_width"
                    android:layout_height="@dimen/app_dial_stand_height"
                    android:layout_centerInParent="true"
                    android:forceDarkAllowed="false" />

                <com.oplus.alarmclock.view.dial.OOSAlarmDialClockGlowTable
                    android:id="@+id/dial_clock_big_glow_table"
                    android:layout_width="@dimen/app_dial_stand_width"
                    android:layout_height="@dimen/app_dial_stand_height"
                    android:layout_centerInParent="true"
                    android:visibility="@{AppFeatureUtils.isLightOS() ? View.GONE : View.VISIBLE}"
                    android:forceDarkAllowed="false" />

                <com.oplus.alarmclock.view.dial.AlarmDialClockHour
                    android:id="@+id/dial_clock_big_hour"
                    android:layout_width="@dimen/app_dial_stand_width"
                    android:layout_height="@dimen/app_dial_stand_height"
                    android:layout_centerInParent="true"
                    android:forceDarkAllowed="false" />

                <com.oplus.alarmclock.view.dial.AlarmDialClockMinute
                    android:id="@+id/dial_clock_big_minute"
                    android:layout_width="@dimen/app_dial_stand_width"
                    android:layout_height="@dimen/app_dial_stand_height"
                    android:layout_centerInParent="true"
                    android:forceDarkAllowed="false" />

                <com.oplus.alarmclock.view.dial.AlarmDialClockSecond
                    android:id="@+id/dial_clock_big_second"
                    android:layout_width="@dimen/app_dial_stand_width"
                    android:layout_height="@dimen/app_dial_stand_height"
                    android:layout_centerInParent="true"
                    android:forceDarkAllowed="false" />

            </RelativeLayout>

            <TextView
                android:id="@+id/dial_msg_big_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:text="@{timeInfo}"
                android:textColor="@color/black_transparent_30"
                android:textDirection="locale"
                android:textSize="@dimen/text_size_sp_12"
                app:layout_constraintBottom_toBottomOf="@+id/dial_clock_big_rl"
                app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl" />

            <com.oplus.alarmclock.view.dial.AlarmDialClockTextView
                android:id="@+id/dial_word_time_big_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@+id/dial_clock_big_rl"
                app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl"
                app:layout_constraintTop_toTopOf="@+id/dial_clock_big_rl" />

            <TextView
                android:id="@+id/dial_word_msg_big_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/layout_dp_4"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="2"
                android:text="@{timeInfo}"
                android:textColor="@color/black_transparent_30"
                android:textDirection="locale"
                android:textSize="@dimen/text_size_sp_12"
                app:layout_constraintEnd_toEndOf="@+id/dial_clock_big_rl"
                app:layout_constraintStart_toStartOf="@+id/dial_clock_big_rl"
                app:layout_constraintTop_toBottomOf="@+id/dial_word_time_big_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <RelativeLayout
            android:id="@+id/button_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_layout_height"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.coui.appcompat.floatingactionbutton.COUIFloatingButton
                android:id="@+id/button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/layout_dp_24"
                android:overScrollMode="always"
                android:transitionName="shared_element_fab"
                app:fabExpandAnimationEnable="true" />

        </RelativeLayout>

    </com.oplus.alarmclock.view.NestedScrollableHost>

    <data>

        <import type="android.view.View" />
        <import type="com.oplus.alarmclock.utils.AppFeatureUtils" />

        <variable
            name="clickListener"
            type="View.OnClickListener" />

        <variable
            name="timeInfo"
            type="String" />
    </data>
</layout>