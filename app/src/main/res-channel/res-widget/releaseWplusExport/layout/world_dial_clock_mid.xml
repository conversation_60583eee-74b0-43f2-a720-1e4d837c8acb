<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dial_clock_cl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="UnusedAttribute">

        <RelativeLayout
            android:id="@+id/dial_clock_rl"
            android:layout_width="@dimen/layout_dp_330"
            android:layout_height="@dimen/layout_dp_330"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.oplus.alarmclock.view.dial.OOSWaterClockView
                android:id="@+id/dial_clock_bg"
                android:layout_width="@dimen/app_dial_shadow_mid_width"
                android:layout_height="@dimen/app_dial_shadow_mid_width"
                android:layout_centerInParent="true"
                app:water_circle_width="@dimen/layout_dp_226"
                app:water_circle_height="@dimen/layout_dp_226"
                android:forceDarkAllowed="false"
                app:scale_value="0.86" />

            <com.oplus.alarmclock.view.dial.OOSAlarmDialClockTable
                android:id="@+id/dial_clock_big_table"
                android:layout_width="@dimen/layout_dp_226"
                android:layout_height="@dimen/layout_dp_226"
                android:layout_centerInParent="true"
                app:scale_value="0.86"
                android:forceDarkAllowed="false" />

            <com.oplus.alarmclock.view.dial.OOSAlarmDialClockGlowTable
                android:id="@+id/dial_clock_big_glow_table"
                android:layout_width="@dimen/layout_dp_226"
                android:layout_height="@dimen/layout_dp_226"
                android:layout_centerInParent="true"
                android:visibility="@{AppFeatureUtils.isLightOS() ? View.GONE : View.VISIBLE}"
                app:scale_value="0.86"
                android:forceDarkAllowed="false" />

            <com.oplus.alarmclock.view.dial.AlarmDialClockHour
                android:id="@+id/dial_clock_hour"
                android:layout_width="@dimen/layout_dp_226"
                android:layout_height="@dimen/layout_dp_226"
                app:scale_value="0.86"
                android:layout_centerInParent="true"
                android:forceDarkAllowed="false" />

            <com.oplus.alarmclock.view.dial.AlarmDialClockMinute
                android:id="@+id/dial_clock_minute"
                android:layout_width="@dimen/layout_dp_226"
                android:layout_height="@dimen/layout_dp_226"
                android:layout_centerInParent="true"
                app:scale_value="0.86"
                android:forceDarkAllowed="false" />

            <com.oplus.alarmclock.view.dial.AlarmDialClockSecond
                android:id="@+id/dial_clock_second"
                android:layout_width="@dimen/layout_dp_226"
                android:layout_height="@dimen/layout_dp_226"
                app:scale_value="0.86"
                android:layout_centerInParent="true"
                android:forceDarkAllowed="false" />

        </RelativeLayout>

        <com.oplus.alarmclock.view.dial.AlarmDialClockMsgTextView
            android:id="@+id/dial_msg_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="2"
            android:maxLines="2"
            android:text="@{timeInfo}"
            android:textColor="?attr/couiColorHintNeutral"
            android:layout_marginTop="298dp"
            android:textDirection="locale"
            android:textIsSelectable="true"
            android:textSize="@dimen/text_size_sp_16"
            android:textFontWeight="400"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/dial_word_time_tv"
            app:layout_constraintStart_toStartOf="@+id/dial_word_time_tv" />

        <com.oplus.alarmclock.view.dial.AlarmDialClockTextView
            android:id="@+id/dial_word_time_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/layout_dp_24"
            android:layout_marginTop="@dimen/layout_dp_52"
            android:layout_marginEnd="@dimen/layout_dp_24"
            android:gravity="center_horizontal"
            android:onClick="@{clickListener::onClick}"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.oplus.alarmclock.view.dial.AlarmDialClockMsgTextView
            android:id="@+id/dial_word_msg_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/layout_dp_4"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="2"
            android:maxLines="2"
            android:text="@{timeInfo}"
            android:textColor="?attr/couiColorHintNeutral"
            android:textDirection="locale"
            android:textSize="@dimen/text_size_sp_16"
            android:textFontWeight="400"
            app:layout_constraintEnd_toEndOf="@+id/dial_word_time_tv"
            app:layout_constraintStart_toStartOf="@+id/dial_word_time_tv"
            app:layout_constraintTop_toBottomOf="@+id/dial_word_time_tv" />

        <View
            android:id="@+id/world_clock_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_marginTop="@dimen/layout_dp_15"
            android:alpha="0"
            android:background="@color/line_color"
            android:forceDarkAllowed="false"
            app:layout_constraintTop_toBottomOf="@+id/dial_word_msg_tv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <import type="android.view.View" />
        <import type="com.oplus.alarmclock.utils.AppFeatureUtils" />

        <variable
            name="clickListener"
            type="View.OnClickListener" />

        <variable
            name="timeInfo"
            type="String" />
    </data>
</layout>