<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:title="@string/default_alarm_summary"
        app:top_margin_type="small"
        android:key="key_alarm_first_title"
        app:isFirstCategory="true">

        <com.coui.appcompat.preference.COUIMenuPreference
            android:key="ring_length"
            android:persistent="false"
            android:title="@string/alert_ring_length"/>

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="alert_close_model"
            android:title="@string/alert_close_model" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="default_ringtone"
            android:title="@string/default_ringtone" />

        <com.oplus.alarmclock.view.ColorSetSwitchPreference
            android:key="next_alarm_notice"
            android:title="@string/next_alarm_notice" />

        <com.oplus.alarmclock.view.ColorSetSwitchPreference
            android:key="bell_gradually_rings"
            android:title="@string/bell_gradually_rings" />

        <com.oplus.alarmclock.view.MorningSwitchPreference
            android:key="morning_switch"
            android:layout="@layout/item_detail_floating_layer"
            android:title="@string/morning_play" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="set_date_time_container"
        android:title="@string/setting_other">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="date_time_setting"
            android:summary="@string/system_time_dual_clock_label"
            android:title="@string/date_and_time_settings_title" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIPreference
            android:key="version_name"
            android:persistent="false"
            android:title="@string/version_name"
           app:isBackgroundAnimationEnabled="false"/>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="key_about"
            android:title="@string/setting_about" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="key_privacy"
            android:title="@string/setting_privacy" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="help_and_feedback"
            android:title="@string/help_and_feedback_settings_title" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIBottomPreference />
</androidx.preference.PreferenceScreen>