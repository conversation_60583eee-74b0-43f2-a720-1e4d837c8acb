<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.oplus.alarmclock.alarmclock.OplusRadioPreferencesGroup
        android:key="oplus_radio_preferences_group"
        app:isFirstCategory="true">
        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="oplus_workday"
            android:title="@string/oplus_workday_switch" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="single_cease_rest_day_sunday"
            android:title="@string/single_dayoff_on_sunday" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="size_word_rest_day_only_sunday"
            android:title="@string/work_six_days_this_week" />
        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="size_word_rest_day_double"
            android:title="@string/work_five_days_this_week" />
    </com.oplus.alarmclock.alarmclock.OplusRadioPreferencesGroup>

    <com.coui.appcompat.preference.COUIPreferenceCategory android:selectable="false">
        <Preference
            android:layout="@layout/coui_pager_footer_preference"
            android:selectable="false"
            android:summary="@string/easy_to_create_weekdays_clock" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>