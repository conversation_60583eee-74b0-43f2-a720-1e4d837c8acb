<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/digital_widget"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_whole_bg_stroke"
    android:baselineAligned="false"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:minHeight="@dimen/min_digital_widget_height"
    android:orientation="vertical"
    tools:ignore="SpUsage">

    <LinearLayout
        android:id="@+id/system_time_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/widget_top_bg"
        android:gravity="center_vertical"
        android:minHeight="70dp"
        android:orientation="horizontal"
        android:paddingStart="20dp"
        android:paddingEnd="14dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:orientation="horizontal">

                <TextClock
                    android:id="@+id/vertical_time_clock"
                    style="@style/clock_num_text_size_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sys-sans-en"
                    android:fontFeatureSettings="ss01"
                    android:gravity="center_vertical|start"
                    android:maxLines="1"
                    android:minHeight="@dimen/widget_clock_num_min_height"
                    android:textAllCaps="true"
                    android:textStyle="bold"
                    tools:text="19:30" />

                <TextClock
                    android:id="@+id/am_pm"
                    style="@style/ampm_text_size_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:fontFamily="sys-sans-en"
                    android:format12Hour="@string/vertical_digital_clock_24_hour_format_am"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingTop="@dimen/top_padding"
                    android:paddingBottom="@dimen/bottom_padding"
                    android:singleLine="true"
                    android:textColor="#90ffffff"
                    tools:text="下午" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <TextClock
                    android:id="@+id/date"
                    style="@style/date_weather_text_size_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sys-sans-en"
                    android:maxLines="1"
                    android:paddingTop="@dimen/top_padding"
                    android:paddingBottom="@dimen/bottom_padding"
                    android:textColor="@color/white"
                    tools:text="10月26日星期六" />

                <TextView
                    android:id="@+id/weather_info"
                    style="@style/date_weather_text_size_color"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:fontFamily="sys-sans-en"
                    android:maxLines="1"
                    android:paddingTop="@dimen/top_padding"
                    android:paddingBottom="@dimen/bottom_padding"
                    android:textAlignment="viewStart"
                    android:textColor="@color/white"
                    tools:text="阴 19°C" />

            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_add_city"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:contentDescription="@string/oplus_add"
            android:scaleType="centerInside"
            android:src="@drawable/color_menu_ic_add_normal_white"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/list_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/widget_bottom_bg"
        android:gravity="center"
        android:orientation="vertical">

        <ListView
            android:id="@+id/world_city_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="6dp"
            android:divider="@null"
            android:fadeScrollbars="false"
            android:listSelector="@android:color/transparent"
            android:overScrollMode="always"
            android:scrollbarFadeDuration="0"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/widget_list_thumb"
            android:scrollbars="vertical" />

        <LinearLayout
            android:id="@+id/empty_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:layout_marginTop="-15dp">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/oplus_add"
                android:scaleType="centerInside"
                android:src="@drawable/color_menu_ic_add_normal_white"/>

            <TextView
                android:id="@+id/list_empty_textview"
                style="@style/empty_text_size_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/widget_clock_empty_margin_top"
                android:minHeight="@dimen/widget_clock_empty_min_height"
                android:fontFamily="sys-sans-en"
                android:gravity="center"
                android:text="@string/add_worldclock_widget_tip"
                android:textColor="@color/white" />
        </LinearLayout>

    </RelativeLayout>

</LinearLayout>