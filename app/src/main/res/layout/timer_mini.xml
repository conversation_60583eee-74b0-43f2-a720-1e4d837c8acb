<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/timer_mini_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="invisible"
    android:background="@color/mini_main_pager_background">

    <com.oplus.alarmclock.timer.TimerTextView
        android:id="@+id/timer_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:default_text_color="@color/mini_app_black"
        app:text_font_weight="900"
        app:text_size_mini="70"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.30" />

    <TextView
        android:id="@+id/timer_name"
        android:layout_width="@dimen/layout_dp_140"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/layout_dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/mini_app_black"
        android:textSize="@dimen/layout_dp_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timer_text" />

    <include
        layout="@layout/button_layout_timer_mini"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_layout_height"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>