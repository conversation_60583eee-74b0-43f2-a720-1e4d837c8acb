/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WeatherInfoTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 ****************************************************************/
package com.oplus.realweather

import android.util.Log
import com.oplus.alarmclock.TestParent
import org.junit.Assert
import org.junit.Test

class WeatherInfoTest : TestParent() {
    @Test
    fun should_no_exception_when_use_weather_info() {
        val weatherInfo = getWeatherInfo()
        weatherInfo.run {
            Assert.assertEquals(id, 0)
            Assert.assertEquals(cityId, 0)
            Assert.assertEquals(weatherId, 0)
            Assert.assertEquals(dayWeatherId, 0)
            Assert.assertEquals(nightWeatherId, 0)
            Assert.assertEquals(date, 0)
            Assert.assertEquals(currentWeather, "currentWeather")
            Assert.assertEquals(currentTemp, "currentTemp")
            Assert.assertEquals(currentWindDirect, "currentWindDirect")
            Assert.assertEquals(currentWindPower, "currentWindPower")
            Assert.assertEquals(currentHumidity, "currentHumidity")
            Assert.assertEquals(currentUvIndex, "currentUvIndex")
            Assert.assertEquals(currentUvDesc, "currentUvDesc")
            Assert.assertEquals(dayWeather, "dayWeather")
            Assert.assertEquals(dayTemp, 0)
            Assert.assertEquals(nightWindDirect, "nightWindDirect")
            Assert.assertEquals(nightWindPower, "nightWindPower")
            Assert.assertEquals(nightWeather, "nightWeather")
            Assert.assertEquals(nightTemp, 0)
            Assert.assertEquals(nightWindDirect, "nightWindDirect")
            Assert.assertEquals(nightWindPower, "nightWindPower")
            Assert.assertEquals(alert, "alert")
            Assert.assertEquals(pic, "pic")
            Assert.assertEquals(url, "url")
            Assert.assertEquals(remark, "remark")
            Assert.assertEquals(remark2, "remark2")
            val msg = toString()
            Log.d("WeatherInfoTest", msg)
        }
    }

    private fun getWeatherInfo(): WeatherInfo {
        return WeatherInfo().apply {
            id = 0
            cityId = 0
            weatherId = 0
            dayWeatherId = 0
            nightWeatherId = 0
            date = 0
            currentWeather = "currentWeather"
            currentTemp = "currentTemp"
            currentWindDirect = "currentWindDirect"
            currentWindPower = "currentWindPower"
            currentHumidity = "currentHumidity"
            currentUvIndex = "currentUvIndex"
            currentUvDesc = "currentUvDesc"
            dayWeather = "dayWeather"
            dayTemp = 0
            dayWindDirect = "dayWindDirect"
            dayWindPower = "dayWindPower"
            nightWeather = "nightWeather"
            nightTemp = 0
            nightWindDirect = "nightWindDirect"
            nightWindPower = "nightWindPower"
            alert = "alert"
            pic = "pic"
            url = "url"
            remark = "remark"
            remark2 = "remark2"
        }
    }
}