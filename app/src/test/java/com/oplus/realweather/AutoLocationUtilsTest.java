/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AutoLocationUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/19     1.0            add file
 ****************************************************************/
package com.oplus.realweather;

import static com.oplus.realweather.AutoLocationUtils.CHINA_TIME_ZONE;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;

public class AutoLocationUtilsTest extends TestParent {

    private AutoLocationUtils mAutoLocationUtils;
    private IWeatherServiceCallback mCallback;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mCallback = Mockito.mock(IWeatherServiceCallback.class);
        mAutoLocationUtils = AutoLocationUtils.getInstance(mContext, mCallback);
    }

    @Test
    public void should_mAlreadyStartLocation_is_true_when_startAutoLocation() throws NoSuchFieldException, IllegalAccessException {
        mAutoLocationUtils.startAutoLocation();
        boolean mAlreadyStartLocation = (boolean) ReflectUtil.getFieldValue(AutoLocationUtils.class, "mAlreadyStartLocation", mAutoLocationUtils);
        Assert.assertTrue(mAlreadyStartLocation);
    }

    @Ignore
    @Test
    public void should_sInstance_be_null_when_recycleAll() throws NoSuchFieldException, IllegalAccessException {
        mAutoLocationUtils.startAutoLocation();
        mAutoLocationUtils.recycleAll();
        AutoLocationUtils instance = (AutoLocationUtils) ReflectUtil.getFieldValue(AutoLocationUtils.class, "sInstance", null);
        Assert.assertNull(instance);
    }

    @Test
    public void should_return_target_value_when_getSomeDate_when_mWeatherDBHelper_is_not_null() throws NoSuchFieldException, IllegalAccessException {
        WeatherDataHelper weatherDataHelper = Mockito.mock(WeatherDataHelper.class);
        ReflectUtil.setFieldValue(AutoLocationUtils.class, "mWeatherDBHelper", mAutoLocationUtils, weatherDataHelper);
        Mockito.when(weatherDataHelper.checkExternalWeatherService()).thenReturn(true);
        boolean checkService = mAutoLocationUtils.checkExternalWeatherService();
        Assert.assertTrue(checkService);

        final int attentCityCount = 1;
        Mockito.when(weatherDataHelper.getAttentCityCount(Mockito.anyBoolean())).thenReturn(attentCityCount);
        int resultAttentCityCount = mAutoLocationUtils.getAttentCityCount(true);
        Assert.assertEquals(attentCityCount, resultAttentCityCount);

        final long cityId = 1;
        Mockito.when(weatherDataHelper.getCityIdByAttentCityId(Mockito.anyLong())).thenReturn(cityId);
        long resultCityId = mAutoLocationUtils.getCityIdByAttentCityId(1);
        Assert.assertEquals(cityId, resultCityId);

        final long currentCityId = 1;
        Mockito.when(weatherDataHelper.getCurrentCityId(mContext)).thenReturn(currentCityId);
        long resultCurrentCityId = mAutoLocationUtils.getCurrentCityId(mContext);
        Assert.assertEquals(currentCityId, resultCurrentCityId);

        final long locationCityId = 1;
        Mockito.when(weatherDataHelper.getLocationCityId(mContext)).thenReturn(locationCityId);
        long resultLocationCityId = mAutoLocationUtils.getLocationCityId(mContext);
        Assert.assertEquals(locationCityId, resultLocationCityId);

        final long firstAttentCidyId = 1;
        Mockito.when(weatherDataHelper.getFirstAttentCityId(mContext)).thenReturn(firstAttentCidyId);
        long resultFirstAttentCityId = mAutoLocationUtils.getFirstAttentCityId(mContext);
        Assert.assertEquals(firstAttentCidyId, resultFirstAttentCityId);

        final long preCityId = 1;
        Mockito.when(weatherDataHelper.getPreCityId(Mockito.anyLong())).thenReturn(preCityId);
        long resultPreCityId = mAutoLocationUtils.getPreCityId(1);
        Assert.assertEquals(preCityId, resultPreCityId);

        final long nextCityId = 1;
        Mockito.when(weatherDataHelper.getNextCityId(Mockito.anyLong())).thenReturn(nextCityId);
        long resultNextCityId = mAutoLocationUtils.getNextCityId(1);
        Assert.assertEquals(nextCityId, resultNextCityId);

        final String cityName = "cd";
        Mockito.when(weatherDataHelper.getCurrentAttentCityName(Mockito.anyLong(),Mockito.eq(mContext))).thenReturn(cityName);
        String resultCityName = mAutoLocationUtils.getCurrentAttentCityName(1, mContext);
        Assert.assertEquals(cityName, resultCityName);

        final String provinceName = "sc";
        Mockito.when(weatherDataHelper.getCurrentAttentProvinceName(Mockito.anyLong(), Mockito.eq(mContext))).thenReturn(provinceName);
        String resultProvinceName = mAutoLocationUtils.getCurrentAttentProvinceName(1, mContext);
        Assert.assertEquals(provinceName, resultProvinceName);

        final String countryName = "cn";
        Mockito.when(weatherDataHelper.getCurrentAttentCountryName(Mockito.anyLong(), Mockito.eq(mContext))).thenReturn(countryName);
        String resultCountryName = mAutoLocationUtils.getCurrentAttentCountryName(1, mContext);
        Assert.assertEquals(countryName, resultCountryName);

        final WeatherInfo weatherInfo = new WeatherInfo();
        weatherInfo.setWeatherId(1);
        Mockito.when(weatherDataHelper.getCurrentCityWeather(Mockito.anyLong())).thenReturn(weatherInfo);
        WeatherInfo resultWeatherInfo = mAutoLocationUtils.getCurrentCityWeather(1);
        Assert.assertEquals(1, resultWeatherInfo.getWeatherId());

        final float timezone = 1.0f;
        Mockito.when(weatherDataHelper.getTimeZoneOfAttendCity(Mockito.anyLong())).thenReturn(timezone);
        float resultTimezone = mAutoLocationUtils.getTimeZoneOfAttendCity(1);
        Assert.assertEquals(timezone, resultTimezone, 0.01f);
    }

    @Test
    public void should_return_default_value_when_getSomeDate_when_mWeatherDBHelper_is_null() throws NoSuchFieldException, IllegalAccessException {
        WeatherDataHelper weatherDataHelper = null;
        ReflectUtil.setFieldValue(AutoLocationUtils.class, "mWeatherDBHelper", mAutoLocationUtils, weatherDataHelper);
        boolean checkService = mAutoLocationUtils.checkExternalWeatherService();
        Assert.assertFalse(checkService);

        int resultAttentCityCount = mAutoLocationUtils.getAttentCityCount(true);
        Assert.assertEquals(0, resultAttentCityCount);

        long resultCityId = mAutoLocationUtils.getCityIdByAttentCityId(1);
        Assert.assertEquals(-1, resultCityId);

        long resultCurrentCityId = mAutoLocationUtils.getCurrentCityId(mContext);
        Assert.assertEquals(-1, resultCurrentCityId);

        long resultLocationCityId = mAutoLocationUtils.getLocationCityId(mContext);
        Assert.assertEquals(-1, resultLocationCityId);

        long resultFirstAttentCityId = mAutoLocationUtils.getFirstAttentCityId(mContext);
        Assert.assertEquals(-1, resultFirstAttentCityId);

        long resultPreCityId = mAutoLocationUtils.getPreCityId(1);
        Assert.assertEquals(-1, resultPreCityId);

        long resultNextCityId = mAutoLocationUtils.getNextCityId(1);
        Assert.assertEquals(-1, resultNextCityId);

        String resultCityName = mAutoLocationUtils.getCurrentAttentCityName(1, mContext);
        Assert.assertNull(resultCityName);

        String resultProvinceName = mAutoLocationUtils.getCurrentAttentProvinceName(1, mContext);
        Assert.assertNull(resultProvinceName);

        String resultCountryName = mAutoLocationUtils.getCurrentAttentCountryName(1, mContext);
        Assert.assertNull(null, resultCountryName);

        WeatherInfo resultWeatherInfo = mAutoLocationUtils.getCurrentCityWeather(1);
        Assert.assertNull(resultWeatherInfo);

        float resultTimezone = mAutoLocationUtils.getTimeZoneOfAttendCity(1);
        Assert.assertEquals(AutoLocationUtils.CHINA_TIME_ZONE, resultTimezone, 0.01f);
    }
}