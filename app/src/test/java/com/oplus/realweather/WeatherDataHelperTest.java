/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WeatherDataHelperTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/17     1.0            add file
 ****************************************************************/

package com.oplus.realweather;

import android.os.RemoteException;

import com.oplus.aidl.AttentWeatherInfo;
import com.oplus.aidl.IExternalWeatherWidgetService;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Calendar;
import java.util.TimeZone;

public class WeatherDataHelperTest extends TestParent {

    private WeatherDataHelper mWeatherDataHelper;
    private IWeatherServiceCallback mCallback;
    private IExternalWeatherWidgetService mService;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mCallback = Mockito.mock(IWeatherServiceCallback.class);
        mWeatherDataHelper = new WeatherDataHelper(mContext, mCallback);
        mService = Mockito.mock(IExternalWeatherWidgetService.class);
        mWeatherDataHelper.mExternalWeatherService = mService;
    }

    @Test
    public void should_return_weatherInfo_when_getCurrentCityWeather_with_any_id() throws RemoteException {
        final int weatherId = 100;
        AttentWeatherInfo attentWeatherInfo = new AttentWeatherInfo();
        attentWeatherInfo.setWeatherId(weatherId);
        Mockito.when(mService.getOneAttentCityWeatherInfoList(Mockito.anyLong(), Mockito.anyFloat())).thenReturn(attentWeatherInfo);
        WeatherInfo weatherInfo = mWeatherDataHelper.getCurrentCityWeather(-1);
        Assert.assertEquals(weatherId, weatherInfo.getWeatherId());
    }

    @Test
    public void should_isCurrentDay_correct_when_isCurrentDay_with_timezone_and_tempInfo() {
        final int timezoneId = 8;
        String timeZoneStr = "GMT+" + timezoneId;
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(timeZone);

        WeatherInfo tempInfo = new WeatherInfo();
        tempInfo.setDate(calendar.getTimeInMillis());

        boolean isCurrentDay = mWeatherDataHelper.isCurrentDay(timezoneId, tempInfo);
        Assert.assertTrue(isCurrentDay);

        calendar.add(Calendar.DAY_OF_YEAR, 1);
        tempInfo.setDate(calendar.getTimeInMillis());
        isCurrentDay = mWeatherDataHelper.isCurrentDay(timezoneId, tempInfo);
        Assert.assertFalse(isCurrentDay);

    }
}