/****************************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - WeatherUtilsTest.java
 * Description:
 * Version: 1.0
 * Date : 2021/12/17
 * Author: <EMAIL>
 *
 * ---------------------Revision History: -----------------------
 * <author>    <data>       <version>     <desc>
 * YangLinlong   2021/12/17     1.0            add file
</desc></version></data></author> */
package com.oplus.realweather

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.content.pm.ServiceInfo
import com.oplus.alarmclock.TestParent
import org.junit.Assert
import org.junit.Test
import org.mockito.Mockito
import java.lang.reflect.InvocationTargetException

class WeatherUtilsTest : TestParent() {
    @Test
    fun should_return_correct_result_when_isInfoNone_with_different_infor() {
        var info: String? = null
        var result = WeatherUtils.isInfoNone(info)
        Assert.assertTrue(result)
        info = "None"
        result = WeatherUtils.isInfoNone(info)
        Assert.assertTrue(result)
        info = "anyString"
        result = WeatherUtils.isInfoNone(info)
        Assert.assertFalse(result)
    }

    @Test
    fun should_return_null_when_getExplicitIntent_with_resolveInfo_is_null() {
        val context = Mockito.mock(Context::class.java)
        val pm = Mockito.mock(
            PackageManager::class.java
        )
        Mockito.`when`(context.packageManager).thenReturn(pm)
        Mockito.`when`(pm.queryIntentServices(Mockito.anyObject(), Mockito.eq(0))).thenReturn(null)
        var result = WeatherUtils.getExplicitIntent(context, Intent())
        Assert.assertNull(result)
        val resolveInfo = ResolveInfo()
        val packageName = "com.test.test"
        resolveInfo.serviceInfo = ServiceInfo()
        resolveInfo.serviceInfo.packageName = packageName
        resolveInfo.serviceInfo.name = "alarmclock"
        val list = ArrayList<ResolveInfo>()
        list.add(resolveInfo)
        Mockito.`when`(pm.queryIntentServices(Mockito.any(), Mockito.eq(0))).thenReturn(list)
        result = WeatherUtils.getExplicitIntent(context, Intent())
        Assert.assertEquals(packageName, result.component?.packageName)
    }

    @Test
    @Throws(
        NoSuchMethodException::class,
        IllegalAccessException::class,
        InvocationTargetException::class
    )
    fun should_no_exception_when_use_start_weather_service() {
        WeatherUtils.startWeatherService(mContext)
        val method =
            WeatherUtils::class.java.getDeclaredMethod("getPackageWeather", Context::class.java)
        method.isAccessible = true
        method.invoke(null, mContext)
    }
}