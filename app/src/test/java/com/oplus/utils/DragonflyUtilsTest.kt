/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - DragonflyUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/6
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaokang  2023/5/6    1.0            build this module
 ****************************************************************/
package com.oplus.utils

import android.content.Context
import android.util.Log
import com.oplus.alarmclock.TestParent
import org.junit.Test

class DragonflyUtilsTest : TestParent() {

    @Test
    fun should_no_exception_when_use_dragonfly_utils() {
        val utils = DragonflyUtils()
        val listener = object : DragonflyUtils.ScreenListener {
            override fun onScreenChange(isSmallScreen: Boolean) {
                Log.d("DragonflyUtilsTest", "onScreenChange: $isSmallScreen")
            }
        }
        utils.register(mContext, listener)
        val isMallScreen = isSmallScreen(mContext, utils)
        assert(!isMallScreen)
        utils.unregister(mContext)
    }

    private fun isSmallScreen(context: Context, utils: DragonflyUtils): Boolean {
        val method = DragonflyUtils::class.java.getDeclaredMethod("isSmallScreen", Context::class.java)
        method.isAccessible = true
        val result = method.invoke(utils, context)
        return result as Boolean
    }
}