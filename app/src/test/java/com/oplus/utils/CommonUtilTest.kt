/****************************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - CommonUtilTest.java
 * Description:
 * Version: 1.0
 * Date : 2021/12/19
 * Author: <EMAIL>
 *
 * ---------------------Revision History: -----------------------
 * <author>    <data>       <version>     <desc>
 * YangLinlong   2021/12/19     1.0            add file
</desc></version></data></author> */
package com.oplus.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Point
import android.hardware.display.DisplayManager
import android.provider.Settings
import android.util.Pair
import android.view.Display
import android.view.View
import com.coloros.widget.entity.TimeInfo
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil
import com.oplus.utils.CommonUtil.adapterRtlText
import com.oplus.utils.CommonUtil.convertNumberToLocal
import com.oplus.utils.CommonUtil.getFormatHour
import com.oplus.utils.CommonUtil.isLocationEnabled
import com.oplus.utils.CommonUtil.isRtl
import com.oplus.utils.CommonUtil.isScreenPortrait
import com.oplus.utils.CommonUtil.isZh
import com.oplus.utils.CommonUtil.setDefaultDisplay
import org.junit.Assert
import org.junit.Test
import org.mockito.Mockito
import org.robolectric.annotation.Config
import java.util.Locale

class CommonUtilTest : TestParent() {
    @Test
    fun should_return_true_when_isZh_when_language_endsWith_zh() {
        val context = Mockito.mock(Context::class.java)
        val resources = Mockito.mock(
            Resources::class.java
        )
        val configuration = Mockito.mock(
            Configuration::class.java
        )
        Mockito.`when`(context.resources).thenReturn(resources)
        Mockito.`when`(resources.configuration).thenReturn(configuration)
        configuration.locale = Locale.CHINESE
        //        Mockito.when(configuration.locale).thenReturn(Locale.CHINESE);
        val isZh = isZh(context)
        Assert.assertTrue(isZh)
    }

    @Config(shadows = [ShadowGetInitialDisplayDensityUtil::class])
    @Test
    fun should_resources_updateConfiguration_when_setDefaultDisplay() {
        val context = Mockito.mock(Context::class.java)
        val resources = Mockito.mock(
            Resources::class.java
        )
        Mockito.`when`(context.resources).thenReturn(resources)
        setDefaultDisplay(context)
        //to do
        Mockito.verify(resources, Mockito.atLeastOnce()).configuration
    }

    @Test
    fun should_return_correct_pair_when_getFormatHour_with_different_timeinfo() {
        val map = HashMap<String, Pair<Int, Int>>()
        map[""] = Pair(0, 0)
        map["2"] = Pair(0, 2)
        map["5"] = Pair(0, 5)
        map["10"] = Pair(1, 0)
        map["18"] = Pair(1, 8)
        map["21"] = Pair(2, 1)
        for (hour in map.keys) {
            val timeInfo = TimeInfo()
            timeInfo.hour = hour
            val exceptedPair = map[hour]!!
            val resultPair = getFormatHour(timeInfo)
            Assert.assertEquals(exceptedPair.first, resultPair.first)
            Assert.assertEquals(exceptedPair.second, resultPair.second)
        }
    }

    @Test
    fun should_return_true_or_false_when_isRtl_when_different_layout_direction() {
        val context = Mockito.mock(Context::class.java)
        val resources = Mockito.mock(
            Resources::class.java
        )
        val configuration = Mockito.mock(
            Configuration::class.java
        )
        Mockito.`when`(context.resources).thenReturn(resources)
        Mockito.`when`(resources.configuration).thenReturn(configuration)
        Mockito.`when`(configuration.layoutDirection).thenReturn(View.LAYOUT_DIRECTION_RTL)
        var isRtl = isRtl(context)
        Assert.assertTrue(isRtl)
        Mockito.`when`(configuration.layoutDirection).thenReturn(View.LAYOUT_DIRECTION_LTR)
        isRtl = isRtl(context)
        Assert.assertFalse(isRtl)
    }

    @Test
    fun should_text_corrent_when_adapterRtlText_with_different_layout_direction() {
        val context = Mockito.mock(Context::class.java)
        val resources = Mockito.mock(
            Resources::class.java
        )
        val configuration = Mockito.mock(
            Configuration::class.java
        )
        Mockito.`when`(context.resources).thenReturn(resources)
        Mockito.`when`(resources.configuration).thenReturn(configuration)
        Mockito.`when`(configuration.layoutDirection).thenReturn(View.LAYOUT_DIRECTION_RTL)
        val inputText = "test"
        var result = adapterRtlText(context, inputText)
        Assert.assertEquals(RTL_START + inputText + RTL_END, result)
        Mockito.`when`(configuration.layoutDirection).thenReturn(View.LAYOUT_DIRECTION_LTR)
        result = adapterRtlText(context, inputText)
        Assert.assertEquals(inputText, result)
    }

    @Test
    fun should_return_null_when_convertNumberToLocal_with_str_is_null_or_not_integer() {
        var str: String? = null
        var result = convertNumberToLocal(str)
        Assert.assertEquals("", result)
        str = "sii"
        result = convertNumberToLocal(str)
        Assert.assertEquals(str, result)
        str = "18"
        result = convertNumberToLocal(str)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_locationEnabled_correct_when_isLocationEnabled() {
        Settings.Secure.putInt(mContext.contentResolver, Settings.Secure.LOCATION_MODE, 0)
        var locationEnabled = isLocationEnabled(mContext)
        Assert.assertFalse(locationEnabled)
        Settings.Secure.putInt(mContext.contentResolver, Settings.Secure.LOCATION_MODE, 1)
        locationEnabled = isLocationEnabled(mContext)
        Assert.assertTrue(locationEnabled)
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_isPortrait_correct_when_isScreenPortrait_with_display_different() {
        val displayManager = Mockito.mock(
            DisplayManager::class.java
        )
        ReflectUtil.setFieldValue(CommonUtil::class.java, "sDisplayManager", null, displayManager)
        Mockito.`when`(displayManager.getDisplay(Mockito.anyInt())).thenReturn(null)
        var isPortrait = isScreenPortrait()
        Assert.assertTrue(isPortrait)
        val display = Mockito.mock(Display::class.java)
        Mockito.`when`(displayManager.getDisplay(Mockito.anyInt())).thenReturn(display)
        Mockito.doAnswer { invocation ->
            val args = invocation.arguments
            val point = args[0] as Point
            point.x = 1000
            point.y = 2000
            val mock = invocation.mock as Display
            null
        }.`when`(display).getRealSize(
            Mockito.any(
                Point::class.java
            )
        )
        isPortrait = isScreenPortrait()
        Assert.assertTrue(isPortrait)
        Mockito.doAnswer { invocation ->
            val args = invocation.arguments
            val point = args[0] as Point
            point.x = 2000
            point.y = 1000
            val mock = invocation.mock as Display
            null
        }.`when`(display).getRealSize(
            Mockito.any(
                Point::class.java
            )
        )
        isPortrait = isScreenPortrait()
        Assert.assertFalse(isPortrait)
    }

    companion object {
        private const val RTL_START = "\u202B"
        private const val RTL_END = "\u202C"
    }
}