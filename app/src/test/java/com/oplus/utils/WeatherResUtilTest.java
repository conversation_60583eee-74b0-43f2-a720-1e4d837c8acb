/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WeatherResUtilTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/20     1.0            add file
 ****************************************************************/
package com.oplus.utils;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;


public class WeatherResUtilTest extends TestParent {

    @Test
    public void should_weatherIcon_correct_when_getWeatherIcon_with_type_and_isDayTime() {
        String type = null;
        boolean isDayTime= false;
        int defaultResId = R.drawable.ic_hour_cloudy_day;

        int weatherIcon = WeatherResUtil.getWeatherIcon(type, isDayTime);
        Assert.assertEquals(defaultResId, weatherIcon);

        type = "abc";
        weatherIcon = WeatherResUtil.getWeatherIcon(type, isDayTime);
        Assert.assertEquals(defaultResId, weatherIcon);

        type = "1";
        weatherIcon = WeatherResUtil.getWeatherIcon(type, isDayTime);
        Assert.assertNotEquals(defaultResId, weatherIcon);
    }

    @Test
    public void should_weatherIcon_correct_when_getOplusWeatherIcon_with_type_and_isDayTime() {
        String type = null;
        boolean isDayTime= false;
        int defaultResId = R.drawable.ic_hour_cloudy_day;

        int weatherIcon = WeatherResUtil.getOplusWeatherIcon(type, isDayTime);
        Assert.assertEquals(defaultResId, weatherIcon);

        type = "abc";
        weatherIcon = WeatherResUtil.getOplusWeatherIcon(type, isDayTime);
        Assert.assertEquals(defaultResId, weatherIcon);

        type = "1";
        weatherIcon = WeatherResUtil.getOplusWeatherIcon(type, isDayTime);
        Assert.assertNotEquals(defaultResId, weatherIcon);
    }


}