/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - OplusDateUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/20     1.0            add file
 ****************************************************************/
package com.oplus.utils;

import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;

import java.util.Calendar;

public class OplusDateUtilsTest extends TestParent {

    @Test
    public void should_result_correct_when_getHourString_with_timemills() {
        Calendar calendar = Calendar.getInstance();
        int hour = 10;
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        String result = OplusDateUtils.getHourString(mContext, calendar.getTimeInMillis());
        Assert.assertEquals("10", result);
    }

    @Test
    public void should_isDay_when_isTimeMillisDayTime_with_hour_is_different() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 5);
        boolean isDay = OplusDateUtils.isTimeMillisDayTime(mContext, calendar.getTimeInMillis());
        Assert.assertFalse(isDay);

        calendar.set(Calendar.HOUR_OF_DAY, 12);
        isDay = OplusDateUtils.isTimeMillisDayTime(mContext, calendar.getTimeInMillis());
        Assert.assertTrue(isDay);

        calendar.set(Calendar.HOUR_OF_DAY, 20);
        isDay = OplusDateUtils.isTimeMillisDayTime(mContext, calendar.getTimeInMillis());
        Assert.assertFalse(isDay);
    }
}