/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ProcessCommunicateTimerTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 ****************************************************************/
package com.oplus.utils

import android.os.Bundle
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.timer.TimerService
import com.oplus.alarmclock.utils.ProcessCommunicateTimer
import org.junit.Ignore
import org.junit.Test
import org.robolectric.Robolectric

class ProcessCommunicateTimerTest : TestParent() {
    @Test
    @Ignore
    fun should_no_exception_when_use_process_communicate_timer() {
        val timer = ProcessCommunicateTimer(mContext)
        val service = Robolectric.buildService(TimerService::class.java).create().startCommand(0, 0).get()
        service.getTimerRingUri(0)
        val map = getField<HashMap<Int, TimerService.TimeObj>>(service, "mTimeObjMap")
        val timeObj = service.getTimeObj(0)
        timeObj.timerRingName = "timerRingName"
        timeObj.timerRingUri = "timerRingUri"
        map[0] = timeObj
        val bundle = Bundle()
        bundle.putInt("action_status", 0)
        bundle.putInt(ProcessCommunicateTimer.TIMER_STATUS, 0)
        bundle.putLong(ProcessCommunicateTimer.TIMER_TOTAL_TIME, 2)
        bundle.putLong(ProcessCommunicateTimer.TIMER_CURRENT_TIME, 1)
        bundle.putInt(ProcessCommunicateTimer.TIMER_SELECTED_POSITION, 2)
        timer.handleTimer(bundle)
        setField(timer, "mTimerService", service)
        timer.handleTimer(bundle)
        bundle.putInt(ProcessCommunicateTimer.TIMER_STATUS, 1)
        timer.handleTimer(bundle)
        bundle.putInt(ProcessCommunicateTimer.TIMER_STATUS, 2)
        timer.handleTimer(bundle)
        bundle.putInt("action_status", 1)
        bundle.putString("key_ring_path", "key_ring_path")
        bundle.putString("key_ring_title", "key_ring_title")
        timer.handleTimer(bundle)
    }

    private fun setField(obj: Any, name: String, value: Any) {
        val cls = obj::class.java
        val field = cls.getDeclaredField(name)
        field.isAccessible = true
        field.set(obj, value)
    }

    @Suppress("UNCHECKED_CAST")
    private fun <T> getField(obj: Any, name: String): T {
        val cls = obj::class.java
        val field = cls.getDeclaredField(name)
        field.isAccessible = true
        return field.get(obj) as T
    }
}