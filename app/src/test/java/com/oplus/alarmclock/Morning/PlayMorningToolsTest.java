/************************************************************
 * Copyright 2010-2019 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : PlayMorningToolsTest.java
 * Version Number : 1.0
 * Description    : Solve some problems that robolectric and powermockito when they can't coexist runner
 * Author         : hegai
 * Date           : 2020-09-28
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-07-12, hegai, create
 ************************************************************/

package com.oplus.alarmclock.Morning;

import android.content.Context;
import android.content.SharedPreferences;

import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.Morning.tools.PlayMorningTools;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.util.Calendar;

import static com.oplus.alarmclock.Morning.tools.PlayMorningTools.DEFAULT_END_TIME;
import static com.oplus.alarmclock.Morning.tools.PlayMorningTools.DEFAULT_START_TIME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.spy;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class PlayMorningToolsTest extends TestParent {
    private static long mTime = 15 * 60 * 60;
    private Context mPlayMorningToolsContext;

    //当时间小于10点正常播报
    @Ignore
    @Test
    public void should_call_playMorning_when_time_More_than_10_points_with_not_road_cast_true() {
        mPlayMorningToolsContext = spy(mContext);
        AlarmSchedule alarmSchedule = new AlarmSchedule();
        alarmSchedule.setTime(8 * 60 * 60);
        SharedPreferences sharedPreferences = mPlayMorningToolsContext.getSharedPreferences(MorningAlarmClock.MORNING_PREFERENCE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putBoolean(MorningAlarmClock.MORNING_STATE, true).commit();
        //设置没有播报过
        sharedPreferences.edit().putLong(MorningAlarmClock.MORNING_PLAY_STATUS, 0).commit();
        //调用播报
        PlayMorningTools.playMorning(mPlayMorningToolsContext, alarmSchedule);
        //判断播报时间
        long startTime = sharedPreferences.getLong(MorningAlarmClock.MORNING_START_TIME, PlayMorningTools.DEFAULT_START_TIME);
        long endTime = sharedPreferences.getLong(MorningAlarmClock.MORNING_END_TIME, PlayMorningTools.DEFAULT_END_TIME);
        Calendar calendar = Calendar.getInstance();
        long hour = calendar.get(Calendar.HOUR_OF_DAY);//hour
        long minute = calendar.get(Calendar.MINUTE);//minute
        long sumTime = hour * 60 * 60 + minute * 60;
        //Determine if the broadcast is in the specified time frame
        if ((sumTime >= startTime) && (sumTime <= endTime)) {
            //在播報时间范围会播报
            long time = sharedPreferences.getLong(MorningAlarmClock.MORNING_PLAY_STATUS, 0);
            assertEquals(time, 0);
        } else {
            //不会播报
            long time = sharedPreferences.getLong(MorningAlarmClock.MORNING_PLAY_STATUS, 0);
            assertEquals(time, 0);
        }
    }


    //调用getStringDate极限值为负数时不会为NULL
    @Test
    public void should_call_getStringDate_when_parameter_less_than_0_with_not_null() {
        String dateString = PlayMorningTools.getStringDate(-99);
        assertNotNull(dateString);
    }

}
