/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WPlusUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/11/15     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.migration;

import com.heytap.addon.os.WaveformEffect;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class WPlusUtilsTest extends TestParent {

    @Test
    public void should_return_correct_result_when_isSpecialDayAlarmOrOnceAlarm_with_different_specialDayStr() throws NoSuchMethodException, IllegalAccessException {

        HashMap<String, Boolean> map = new HashMap<>();
        map.put("#18911#", true);
        map.put("", false);
        map.put("#", false);
        map.put("#18922#18923#", true);
        map.put("checked#", false);
        map.put("##", true);
        map.put("#121da", false);

        for (String specialDayStr : map.keySet()) {
            System.out.println(specialDayStr);
            boolean isSpecialOrOnceAlarm = (boolean) ReflectUtil.invoke(WPlusUtils.class, "isSpecialDayAlarmOrOnceAlarm", new Object[]{specialDayStr}, null, String.class);
            Assert.assertEquals(map.get(specialDayStr), isSpecialOrOnceAlarm);
        }
    }

    @Test
    public void should_alarm_size_zero_when_convertToAlarms_with_list_is_null() throws NoSuchMethodException, IllegalAccessException {
        List<AlarmTemplates> list = null;
        List<Alarms> alarmsList = (List<Alarms>) ReflectUtil.invoke(WPlusUtils.class, "convertToAlarms", new Object[]{list}, null, List.class);
        Assert.assertNull(alarmsList);
    }

    @Test
    public void should_return_alarmList_with_vibrate_when_convertToAlarms_with_alarm_is_vibrate() throws NoSuchMethodException, IllegalAccessException {
        ArrayList<AlarmTemplates> list = new ArrayList<>();
        AlarmTemplates alarmTemplates = new AlarmTemplates();
        alarmTemplates.setVibrate(1);
        list.add(alarmTemplates);
        List<Alarms> alarmsList = (List<Alarms>) ReflectUtil.invoke(WPlusUtils.class, "convertToAlarms", new Object[]{list}, null, List.class);
        Alarms alarm = alarmsList.get(0);
        Assert.assertEquals(WaveformEffect.EFFECT_VIBRATE_WITH_RINGTONE, alarm.getVibrate());
    }
}