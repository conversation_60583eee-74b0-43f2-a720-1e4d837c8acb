/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-24, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alert;

import android.content.Context;
import android.content.res.Configuration;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.LinearLayout;

import com.heytap.addon.utils.VersionUtils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AlarmFloatingWindowViewTest extends TestParent {
    static final int ACTION_HIDE_WINDOW_VIEW = 0;
    static final int NUM_TEN = 10;
    AlarmFloatingWindowViewStub mView;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
    }

    @Config(shadows = {ShadowVersionUtils.class})
    @Test
    @Ignore
    public void shouldCallSetAnimatingStateWithAnimStateHidingAndAnimStateIdleInorderWhenOnTouchEventWithActionIsActionHideWindowViewAndIsSnoozeAvailableIsTrue()
            throws NoSuchFieldException, IllegalAccessException {
        int orientation = Configuration.ORIENTATION_PORTRAIT;
        boolean isSnoozeAvalible = true;
        String time = "10";
        String alertName = "alertName";
        int alertHour = 1;
        int alertMinute = 1;
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.width = NUM_TEN;
        layoutParams.height = NUM_TEN;
        mView = new AlarmFloatingWindowViewStub(mContext, orientation, isSnoozeAvalible,
                time, alertName, alertHour, alertMinute,
                layoutParams, false, AlarmFloatingWindowView.FLOATING_VIEW_TYPE_ALARM);
        mView = spy(mView);

        int mAnimatingState = AlarmFloatingWindowView.ANIM_STATE_IDLE;
        ReflectUtil.setFieldValue(AlarmFloatingWindowView.class, "mAnimatingState", mView, mAnimatingState);
        int action = ACTION_HIDE_WINDOW_VIEW;
        float statY = 10;
        LinearLayout mFloatingWindowView = mock(LinearLayout.class);
        when(mFloatingWindowView.getTranslationY()).thenReturn(statY);
        ReflectUtil.setFieldValue(AlarmFloatingWindowView.class, "mFloatingWindowView", mView, mFloatingWindowView);
        ReflectUtil.setFieldValue(AlarmFloatingWindowView.class, "mInterpolator", mView, new LinearInterpolator());

        //invoke
        mView.startFloatingWindowViewAnimation(action);
        //assert
        ArrayList<Tweener> mFloatingWindowViewAnimations = (ArrayList) ReflectUtil.getFieldValue(AlarmFloatingWindowView.class, "mFloatingWindowViewAnimations", mView);
        Tweener tweener = mFloatingWindowViewAnimations.get(0);
        tweener.endAnimator();
        verify(mView).hideFloatingWindowWithAction(AlarmFloatingWindowView.ACTION_SNOOZE, 2);
        InOrder inOrder = Mockito.inOrder(mView, mView);
        inOrder.verify(mView).setAnimatingState(AlarmFloatingWindowView.ANIM_STATE_HIDING);
        inOrder.verify(mView).setAnimatingState(AlarmFloatingWindowView.ANIM_STATE_IDLE);
    }

    public static class AlarmFloatingWindowViewStub extends AlarmFloatingWindowView {
        int mAction;

        public AlarmFloatingWindowViewStub(Context context, int orientation, boolean isSnoozeAvalible,
                                           String time, String alertName, int alertHour, int alertMinute,
                                           WindowManager.LayoutParams layoutParams, boolean isWorkDaySwitchOpened, int floatingViewType) {
            super(context, orientation, isSnoozeAvalible, time, alertName, layoutParams, isWorkDaySwitchOpened, false, floatingViewType);
        }

        @Override
        protected void updateView(String name, String time) {

        }

        @Override
        protected void dismiss() {

        }

        @Override
        protected void snooze() {

        }

        @Override
        protected void hideFloatingWindow() {

        }

        @Override
        public void hideFloatingWindowWithAction(int action, int type) {
            mAction = action;
        }

        @Override
        protected void unregisterReceiver() {

        }

        @Override
        protected void onVolumeAndCameraKeyPressed() {

        }

        @Override
        protected void removeFloatingWindowByFluidCloud() {
            //TODO
        }

        @Override
        public void setAnimatingState(int state) {
            mAnimatingState = state;
        }
    }

    @Implements(VersionUtils.class)
    public static class ShadowVersionUtils {

        @Implementation
        public static boolean greaterOrEqualsToR() {
            return false;
        }
    }

}
