/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-15, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alert;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.view.AlarmTimeView;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.view.AlarmTimeView;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.view.AlarmTimeView;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.view.TimerTimeView;

import org.checkerframework.checker.units.qual.A;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowActivity;
import org.robolectric.shadows.ShadowPhoneWindow;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {AlarmAlertTest.ShadowAbsSliderActivity.class, AlarmAlertTest.ShadowAlarmUtils.class, ShadowGetInitialDisplayDensityUtil.class,
        AlarmAlertTest.ShadowPhoneWindow.class})
public class AlarmAlertTest extends TestParent {
    AlarmAlert mAlarmAlert;
    AlarmAlert mSpyAlarmAlert;

    @BeforeClass
    public static void classSetUp(){
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback,null,null,null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mAlarmAlert = spy(Robolectric.buildActivity(AlarmAlert.class).create().get());
    }

    @Test
    @Ignore
    public void should_call_sendBroadcast_with_ALARM_SNOOZE_ACTION_when_onReceive_with_ALARM_SNOOZE_ACTION() throws NoSuchFieldException, IllegalAccessException {

        Intent intent = new Intent();
        AlarmSchedule schedule = new AlarmSchedule();
        schedule.setAlarm(new Alarm());
        intent.putExtra(ClockConstant.ALARM_INTENT_EXTRA, schedule);
        mAlarmAlert = Robolectric.buildActivity(AlarmAlert.class, intent).get();
        mSpyAlarmAlert = Mockito.spy(mAlarmAlert);
        TimerTimeView mAlarmTimeView = mock(TimerTimeView.class);
        when(mSpyAlarmAlert.findViewById(R.id.time_view)).thenReturn(mAlarmTimeView);
        when(mSpyAlarmAlert.requestWindowFeature(anyInt())).thenReturn(true);

        mSpyAlarmAlert.onCreate(new Bundle());
        mSpyAlarmAlert.onResume();


        AlarmSchedule mAlarmInstance = new AlarmSchedule();
        ReflectUtil.setFieldValue(AlarmAlert.class, "mAlarmInstance", mAlarmAlert, mAlarmInstance);
        BroadcastReceiver mLocalReceiver = (BroadcastReceiver) ReflectUtil.getFieldValue(AlarmAlert.class, "mLocalReceiver", mAlarmAlert);
        ReflectUtil.setFieldValue(AlarmAlert.class, "mAlarmHandled", mAlarmAlert, false);
        Intent intent1 = new Intent(ClockConstant.CLOCK_ALARM_SNOOZE_ACTION);
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(mContext);
        LocalBroadcastManager spyManager = spy(manager);
        ReflectUtil.setFieldValue(LocalBroadcastManager.class, "mInstance", null, spyManager);
        Answer answer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                //assert
                Intent i = invocation.getArgument(0);
                String action = i.getAction();
                assertEquals(ClockConstant.ALARM_SNOOZE_ACTION, action);
                return null;
            }
        };
        doAnswer(answer).when(spyManager).sendBroadcast(any(Intent.class));
        //invoke onReceive()
        mLocalReceiver.onReceive(mContext, intent1);

        ReflectUtil.setFieldValue(LocalBroadcastManager.class, "mInstance", null, manager);
    }

    @Test
    public void should_call_setVisibility_with_INVISIBLE_and_setEnable_with_false_when_setAlertTime_with_snoozeTime_greaterThan_alarmNum()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        Intent intent = new Intent();
        AlarmSchedule mAlarmInstance = new AlarmSchedule();
        mAlarmInstance.setAlarm(new Alarm());
        int snoozeTime = 100;
        mAlarmInstance.setSnoonzeTime(snoozeTime);
        intent.putExtra(ClockConstant.ALARM_INTENT_EXTRA, mAlarmInstance);
        mAlarmAlert = Robolectric.buildActivity(AlarmAlert.class, intent).get();
        mSpyAlarmAlert = Mockito.spy(mAlarmAlert);
        TimerTimeView mAlarmTimeView = mock(TimerTimeView.class);
        when(mSpyAlarmAlert.findViewById(R.id.time_view)).thenReturn(mAlarmTimeView);
        when(mSpyAlarmAlert.requestWindowFeature(anyInt())).thenReturn(true);

        LinearLayout mLayoutSnooze = mock(LinearLayout.class);
        when(mSpyAlarmAlert.findViewById(R.id.layout_snooze)).thenReturn(mLayoutSnooze);

        AlarmRepeat alarmRepeat = new AlarmRepeat();
        int alarmNum = snoozeTime / 2;
        alarmRepeat.setmAlarmNum(alarmNum);
        when(mSpyAlarmAlert.getAlarmRepeat()).thenReturn(alarmRepeat);

        mSpyAlarmAlert.onCreate(new Bundle());
        mSpyAlarmAlert.onResume();
        //assert && verify
        verify(mLayoutSnooze, atLeastOnce()).setVisibility(View.INVISIBLE);
        verify(mLayoutSnooze, atLeastOnce()).setEnabled(false);
    }


    @Test
    public void should_call_setVisibility_with_VISIBLE_when_setAlertTime_with_snoozeTime_lessThan_alarmNum()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        int snoozeTime = 100;
        Alarm alarm = new Alarm();
        alarm.setSnoonzeItem(snoozeTime);
        AlarmSchedule mAlarmInstance = new AlarmSchedule();
        mAlarmInstance.setSnoonzeTime(snoozeTime);
        ReflectUtil.setFieldValue(AlarmSchedule.class, "mAlarm", mAlarmInstance, alarm);

        Intent intent = new Intent();
        intent.putExtra(ClockConstant.ALARM_INTENT_EXTRA, mAlarmInstance);
        mAlarmAlert = Robolectric.buildActivity(AlarmAlert.class, intent).get();
        mSpyAlarmAlert = Mockito.spy(mAlarmAlert);
        TimerTimeView mAlarmTimeView = mock(TimerTimeView.class);
        when(mSpyAlarmAlert.findViewById(R.id.time_view)).thenReturn(mAlarmTimeView);
        when(mSpyAlarmAlert.requestWindowFeature(anyInt())).thenReturn(true);

        LinearLayout mLayoutSnooze = mock(LinearLayout.class);
        when(mSpyAlarmAlert.findViewById(R.id.layout_snooze)).thenReturn(mLayoutSnooze);

        AlarmRepeat alarmRepeat = new AlarmRepeat();
        int alarmNum = snoozeTime * 2;
        alarmRepeat.setmAlarmNum(alarmNum);
        when(mSpyAlarmAlert.getAlarmRepeat()).thenReturn(alarmRepeat);

        mSpyAlarmAlert.onCreate(new Bundle());
        mSpyAlarmAlert.onResume();

        //assert && verify
        verify(mLayoutSnooze, atLeastOnce()).setVisibility(View.INVISIBLE);
    }


    @Implements(AlarmTimeView.class)
    public static class ShadowAlarmTimeView{
        @Implementation
        public void update() {

        }
    }


    @Implements(AlarmUtils.class)
    public static class ShadowAlarmUtils{
        static AlarmRepeat sAlarmRepeat;
        public synchronized static AlarmRepeat getAlarmsRepeatInfo(Context context) {
            return sAlarmRepeat;
        }
    }



    @Implements(AbsSliderActivity.class)
    public static class ShadowAbsSliderActivity extends ShadowActivity {
        @Implementation
        public void __constructor__(){

        }
    }

    @Implements(className = "com.android.internal.policy.PhoneWindow")
    public static class ShadowPhoneWindow extends org.robolectric.shadows.ShadowPhoneWindow {

        @Implementation
        public boolean requestFeature(int featureId) {
            return true;
        }
    }

}
