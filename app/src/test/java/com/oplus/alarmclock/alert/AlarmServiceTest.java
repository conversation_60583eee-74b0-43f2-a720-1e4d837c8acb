/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-17, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alert;

import android.app.KeyguardManager;
import android.app.Notification;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.telephony.TelephonyManager;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.RepeatSet;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.shadows.ShadowAlarmFloatingWindowManager;
import com.oplus.alarmclock.shadows.ShadowAlarmKlaxon;
import com.oplus.alarmclock.shadows.ShadowScheduleUtils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.alarmclock.AlarmSchedule;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.alarmclock.ScheduleUtils;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowActivity;
import org.robolectric.shadows.ShadowService;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Calendar;

import static com.oplus.alarmclock.alert.AlarmService.IS_ALARM_DISMISSED;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import static org.robolectric.Shadows.shadowOf;
@Ignore
@Config(shadows = {ShadowScheduleUtils.class, ShadowAlarmKlaxon.class, ShadowAlarmFloatingWindowManager.class})
public class AlarmServiceTest extends TestParent {
    static final int INVOKE_TIMES_RECEIVER_NOT_REGISTERED = 2;
    static final int INVOKE_TIMES_RECEIVER_REGISTERED = 3;
    static final String KEY_ALARM_STATE = "ALARM_STATE";
    AlarmService mSpyAlarmService;
    AlarmService mService;
    PackageManager mPackageManager;

    @BeforeClass
    public static void classSetUp() {
        System.setProperty("robolectric.logging", "stdout");
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Before
    public void setUp() throws Exception {
        super.setUp();
        mService = Robolectric.buildService(AlarmService.class).get();
        mSpyAlarmService = Mockito.spy(mService);
        mPackageManager = mContext.getPackageManager();
        Field fInstance = LocalBroadcastManager.class.getDeclaredField("mInstance");
        fInstance.setAccessible(true);
        fInstance.set(null, null);
    }

    @Test
    public void testOnCreateSDKIntIsZero() throws NoSuchFieldException, IllegalAccessException {
        int currentSdk = (int) ReflectUtil.getFieldValue(Build.VERSION.class, "SDK_INT", null);
        ReflectUtil.setFieldValue(Build.VERSION.class, "SDK_INT", null, Build.VERSION_CODES.O);
        mSpyAlarmService.onCreate();
        verify(mSpyAlarmService).startForeground(anyInt(), any(Notification.class));
        Intent intent = spy(new Intent(ClockConstant.ALARM_SNOOZE_ACTION));
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(mContext);
        manager.sendBroadcast(intent);
        verify(intent, times(INVOKE_TIMES_RECEIVER_REGISTERED)).getAction();

        Intent intent1 = spy(new Intent(ClockConstant.ALARM_DISMISS_ACTION));
        manager.sendBroadcast(intent1);
        verify(intent1, times(INVOKE_TIMES_RECEIVER_REGISTERED)).getAction();

        Intent intent2 = spy(new Intent("test_action"));
        manager.sendBroadcast(intent2);
        verify(intent2, times(INVOKE_TIMES_RECEIVER_NOT_REGISTERED)).getAction();
        //recovery
        ReflectUtil.setFieldValue(Build.VERSION.class, "SDK_INT", null, currentSdk);
    }

    @Ignore
    @Config(shadows = {ShadowScheduleUtils.class})
    @Test
    public void testOnStartCommandActionIsStartAlarm() throws Exception {
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(mContext);
        BroadcastReceiver mockReceiver = Mockito.mock(BroadcastReceiver.class);
        manager.registerReceiver(mockReceiver, new IntentFilter(ClockConstant.ALARM_ALERT_ACTION));

        final int scheduleId = 1;
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        final Intent intent = new Intent(mContext, AlarmService.class);
        intent.setData(uri);
        intent.setAction(AlarmService.START_ALARM_ACTION);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        ShadowScheduleUtils.sSchedule = AlarmSchedule.build(Calendar.getInstance());
        ReflectUtil.setFieldValue(AlarmService.class, "mCurrentAlarmSchedule", mSpyAlarmService, null);
        mSpyAlarmService.onCreate();
        mSpyAlarmService.onStartCommand(intent, 0, 1);
        verify(mockReceiver).onReceive(any(Context.class), any(Intent.class));
    }


    @Ignore
    @Test
    public void testOnStartCommandActionIsStopAlarm() throws NoSuchFieldException, IllegalAccessException {
        final int scheduleId = 1;
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        final Intent intent = new Intent(mContext, AlarmService.class);
        intent.setData(uri);
        String action = AlarmService.STOP_ALARM_ACTION;
        intent.setAction(action);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        boolean isAlarmDismiss = true;
        intent.putExtra(AlarmService.IS_ALARM_DISMISSED, isAlarmDismiss);
        AlarmSchedule mCurrentAlarmSchedule = AlarmSchedule.build(Calendar.getInstance());
        mCurrentAlarmSchedule.setId(scheduleId);
        ReflectUtil.setFieldValue(AlarmService.class, "mCurrentAlarmSchedule", mSpyAlarmService, mCurrentAlarmSchedule);
        TelephonyManager mTelephonyManager = mock(TelephonyManager.class);
        ReflectUtil.setFieldValue(AlarmService.class, "mTelephonyManager", mSpyAlarmService, mTelephonyManager);
        BroadcastReceiver receiver = spy(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                //assert
                assertEquals(ClockConstant.ALARM_DISMISS_SERVICE_ACTION, intent.getAction());
            }
        });
        LocalBroadcastManager.getInstance(mContext).registerReceiver(receiver, new IntentFilter(ClockConstant.ALARM_DISMISS_SERVICE_ACTION));
        mSpyAlarmService.onCreate();
        //invoke onStartCommand()
        mSpyAlarmService.onStartCommand(intent, 0, 1);
        //verify
        verify(mSpyAlarmService).stopSelf();
        verify(receiver).onReceive(any(Context.class), any(Intent.class));
    }

    @Ignore
    @Test
    public void testOnDestroy() throws NoSuchFieldException, IllegalAccessException {
        final int scheduleId = 1;
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        final Intent intent = new Intent(mContext, AlarmService.class);
        intent.setData(uri);
        intent.setAction(AlarmService.START_ALARM_ACTION);
        intent.setPackage(ClockConstant.CLOCK_PACKAGE);
        mSpyAlarmService.onCreate();
        int flag = 0;
        int startId = 1;
        mSpyAlarmService.onStartCommand(intent, flag, startId);

        Intent intent1 = spy(new Intent(ClockConstant.ALARM_SNOOZE_ACTION));
        LocalBroadcastManager manager = LocalBroadcastManager.getInstance(mContext);
        manager.sendBroadcast(intent1);
        verify(intent1, times(INVOKE_TIMES_RECEIVER_REGISTERED)).getAction();

        mSpyAlarmService.onDestroy();
        Intent intent2 = spy(new Intent(ClockConstant.ALARM_SNOOZE_ACTION));
        manager.sendBroadcast(intent2);
        verify(intent2, times(INVOKE_TIMES_RECEIVER_NOT_REGISTERED)).getAction();
    }


    @Test
    public void shouldUpdateWithURIWhenStartAlarmWithCurrentAlarmScheduleGetAlarmEqualsToAlarmAndRepeatSetIsZeroAndUpdateCountIsOne()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        ContentResolver contentResolver = mock(ContentResolver.class);
        doReturn(contentResolver).when(mSpyAlarmService).getContentResolver();
        int updateCount = 1;
        when(contentResolver.update(any(Uri.class), any(ContentValues.class), (String) isNull(),
                (String[]) isNull())).thenReturn(updateCount);
        AlarmSchedule alarmSchedule = AlarmSchedule.build(Calendar.getInstance());
        int mRepeatSet = 0;
        int workDaySwitch = 0;
        int alarmId = 10;
        alarmSchedule.setAlarmId(alarmId);
        Alarm alarm = Alarm.build(false, 0, 0, mRepeatSet, 0, "",
                null, "", 0, 0, workDaySwitch, 0);
        ReflectUtil.setFieldValue(AlarmSchedule.class, "mAlarm", alarmSchedule, alarm);
        ReflectUtil.setFieldValue(AlarmService.class, "mCurrentAlarmSchedule", mSpyAlarmService, alarmSchedule);
        //invoke startAlarm()
        ReflectUtil.invoke(AlarmService.class, "startAlarm", new Object[]{alarmSchedule},
                mSpyAlarmService, AlarmSchedule.class);
        //verify
        Uri uri = ContentUris.withAppendedId(ClockContract.ALARM_CONTENT_URI, alarmId);
        verify(contentResolver).update(eq(uri), any(ContentValues.class), (String) isNull(), (String[]) isNull());
    }

    @Ignore
    @Test
    public void shouldCallDeleteWithURIAndCallRegisterReceiverWhenStartAlarmWithSnoozeTimeLessThanAlarmNumAndMisReceiverRegisteredIsFalseAndGetAlarmIsNull()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        ContentResolver contentResolver = mock(ContentResolver.class);
        doReturn(contentResolver).when(mSpyAlarmService).getContentResolver();
        AlarmSchedule alarmSchedule = AlarmSchedule.build(Calendar.getInstance());
        int alarmId = 10;
        alarmSchedule.setAlarmId(alarmId);
        AlarmSchedule mCurrentAlarmSchedule = new AlarmSchedule();
        int snoozeTime = 0;
        mCurrentAlarmSchedule.setSnoonzeTime(snoozeTime);
        int scheduleId = 1;
        mCurrentAlarmSchedule.setId(scheduleId);
        ReflectUtil.setFieldValue(AlarmService.class, "mCurrentAlarmSchedule", mSpyAlarmService, mCurrentAlarmSchedule);
        int alarmNum = 5;
        AlarmRepeat alarmRepeat = new AlarmRepeat();
        alarmRepeat.setmAlarmNum(alarmNum);
        boolean mIsReceiverRegistered = false;
        ReflectUtil.setFieldValue(AlarmService.class, "mIsReceiverRegistered", mSpyAlarmService, mIsReceiverRegistered);
        TelephonyManager mTelephonyManager = mock(TelephonyManager.class);
        ReflectUtil.setFieldValue(AlarmService.class, "mTelephonyManager", mSpyAlarmService, mTelephonyManager);
        mSpyAlarmService.onCreate();
        //invoke startAlarm()
        ReflectUtil.invoke(AlarmService.class, "startAlarm", new Object[]{alarmSchedule},
                mSpyAlarmService, AlarmSchedule.class);
        //verify
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        verify(contentResolver).delete(eq(uri), (String) isNull(), (String[]) isNull());
        verify(mSpyAlarmService).registerReceiver(any(BroadcastReceiver.class), any(IntentFilter.class), anyString(), (Handler) isNull());
    }

    @Test
    public void testStaticStopAlarm() {
        int scheduleId = 1;
        AlarmService.stopAlarm(mContext, scheduleId, false);
        ShadowService shadowService = shadowOf(mService);
        Intent intent = shadowService.getNextStartedService();
        String action = intent.getAction();
        assertEquals(action, AlarmService.STOP_ALARM_ACTION);
        Uri uri = ContentUris.withAppendedId(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI, scheduleId);
        assertEquals(uri, intent.getData());
        assertEquals(ClockConstant.CLOCK_PACKAGE, intent.getPackage());
        assertFalse(intent.getBooleanExtra(AlarmService.IS_ALARM_DISMISSED, true));
    }

    @Ignore
    @Test
    public void shouldStartActivityAlarmAlertFullScreenWhenStartAlertActivityOrFloatingViewWithMiniCallStateIsCallStateIdleAndInKeyGuardRestrictedInputModeReturnFalse() throws NoSuchFieldException, IllegalAccessException, Settings.SettingNotFoundException, NoSuchMethodException {
        int mInitialCallState = TelephonyManager.CALL_STATE_IDLE;
        ReflectUtil.setFieldValue(AlarmService.class, "mInitialCallState", mSpyAlarmService, mInitialCallState);
        boolean inKeyguardRestrictedInputMode = true;
        KeyguardManager keyguardManager = mock(KeyguardManager.class);
        doReturn(keyguardManager).when(mSpyAlarmService).getSystemService(Context.KEYGUARD_SERVICE);
        when(keyguardManager.isKeyguardLocked()).thenReturn(inKeyguardRestrictedInputMode);
        // invoke startAlertActivityOrFloatingView()
        ReflectUtil.invoke(AlarmService.class, "startAlertActivityOrFloatingView",
                new Object[]{mSpyAlarmService, new AlarmSchedule()}, mSpyAlarmService, Context.class, AlarmSchedule.class);
        //verify
        ShadowService shadowService = shadowOf(mSpyAlarmService);
        Intent intent = shadowService.getNextStartedActivity();
        assertEquals(AlarmAlertFullScreen.class.getName(), intent.getComponent().getClassName());
        int alarmState = Settings.System.getInt(mSpyAlarmService.getContentResolver(), KEY_ALARM_STATE);
        assertEquals(1, alarmState);
    }

    @Implements(AlarmUtils.class)
    public static class ShadowAlarmUtils {
        static AlarmRepeat sAlarmRepeat;

        public synchronized static AlarmRepeat getAlarmsRepeatInfo(Context context) {
            return sAlarmRepeat;
        }
    }


    @Implements(ScheduleUtils.class)
    public static class ShadowScheduleUtils {
        static AlarmSchedule sSchedule;

        public static AlarmSchedule getSchedule(Context context, long scheduleId) {
            return sSchedule;
        }
    }

}
