/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-2, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alert;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.util.ArrayMap;
import android.view.View;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;
import java.util.ArrayList;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

public class TweenerTest extends TestParent {

    @Test
    public void should_not_throw_IllegalArgumentException_when_to_with_specific_value() throws NoSuchFieldException, IllegalAccessException {
        ArrayMap map = new ArrayMap(8);
        TimeInterpolator timeInterpolator = mock(TimeInterpolator.class);
        map.put("ease", timeInterpolator);
        ValueAnimator.AnimatorUpdateListener  animatorUpdateListener = mock(ValueAnimator.AnimatorUpdateListener.class);
        map.put("onUpdate", animatorUpdateListener);
        map.put("onUpdateListener", animatorUpdateListener);
        Animator.AnimatorListener animatorListener = mock(Animator.AnimatorListener.class);
        map.put("onComplete", animatorListener);
        map.put("onCompleteListener", animatorListener);
        Long l = new Long(100L);
        map.put("delay", l);
        String propertyName1 = "alpha";
        float[] floats = new float[]{0.0f, 1.0f};
        map.put(propertyName1, floats);
        String propertyName2 = "x";
        int[] xOrdinary = new int[]{10, 20};
        map.put(propertyName2, xOrdinary);
        String propertyName3 = "a";
        Float number = 2.0f;
        map.put(propertyName3, number);
        View mockView = mock(View.class);
        long duration = 0L;
        //invoke Tweener
        Tweener tweener = Tweener.to(mockView, duration, map.keyAt(0), map.valueAt(0) ,
                map.keyAt(1), map.valueAt(1) , map.keyAt(2), map.valueAt(2),
                map.keyAt(3), map.valueAt(3) , map.keyAt(4), map.valueAt(4),
                map.keyAt(5), map.valueAt(5),map.keyAt(6), map.valueAt(6),
                map.keyAt(7), map.valueAt(7),map.keyAt(8), map.valueAt(8));
        ObjectAnimator animator = (ObjectAnimator) ReflectUtil.getFieldValue(Tweener.class, "mAnimator", tweener);
        assertEquals(timeInterpolator, animator.getInterpolator());
        ArrayList listeners = animator.getListeners();
        Assert.assertTrue(listeners.contains(animatorListener));
    }

}
