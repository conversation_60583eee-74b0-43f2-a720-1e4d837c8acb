/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerTextViewTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.view.ViewGroup
import com.oplus.alarmclock.TestParent
import org.junit.Test

class TimerTextViewTest : TestParent() {

    @Test
    fun test_text_timer_view() {
        val view = TimerTextView(mContext)
        view.run {
            layoutParams = ViewGroup.MarginLayoutParams(0, 0)
            update(1000)
            getTime()
            center(false)
            setTextSize(20F)
        }
    }
}