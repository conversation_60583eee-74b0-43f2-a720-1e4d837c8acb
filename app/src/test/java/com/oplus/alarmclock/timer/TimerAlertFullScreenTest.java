/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-23, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer;

import android.content.Intent;
import android.widget.TextView;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.view.TimerTimeView;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class TimerAlertFullScreenTest extends TestParent {
    TimerAlertFullScreen mSpyTimerAlertFullScreen;
    @Override
    public void setUp() throws Exception {
        super.setUp();
        mSpyTimerAlertFullScreen = spy(Robolectric.buildActivity(TimerAlertFullScreen.class).create().get());
    }

    @Test
    @Ignore
    public void should_countDownTimeTextView_text_equal_to_name_when_updateLayout_with_index_is_not_0(){
      /*  //init intent ensure index not equals to TimerService.DEFAULT_TIMER_INDEX
        Intent intent = new Intent();
        String name = "testName";
        String index = "1";
        intent.putExtra(TimerService.TIMER_NAME, name);
        intent.putExtra(TimerService.TIMER_INDEX, index);
        TimerTimeView mockTimeView = Mockito.mock(TimerTimeView.class);
        when(mSpyTimerAlertFullScreen.findViewById(R.id.time_view)).thenReturn(mockTimeView);
        doNothing().when(mockTimeView).update();
        //invoke updateLayout()
        mSpyTimerAlertFullScreen.updateLayout(intent);
        //verify countDownTimeTextView.setText(name) invoked
        TextView view = mSpyTimerAlertFullScreen.findViewById(R.id.count_down_time);
        String text = view.getText().toString();
        assertEquals(name, text);*/
    }


}
