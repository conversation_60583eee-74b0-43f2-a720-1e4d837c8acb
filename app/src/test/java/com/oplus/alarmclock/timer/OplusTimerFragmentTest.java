/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-14, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.alarmclock.timer.ui.TimerController;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Map;

import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.AlarmClockTest;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.ui.TimerSetFrament;
import com.oplus.alarmclock.utils.TimerConstant;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Ignore
@Config(shadows = {OplusTimerFragmentTest.ShadowColorDarkModeUtil.class, OplusTimerFragmentTest.ShadowTimerController.class, AlarmClockTest.ShadowPrivacyPolicyAlert.class})
public class OplusTimerFragmentTest extends TestParent {
    OplusTimerFragment mFragment;
    AlarmClock mAlarmClock;
    TimerController mTimerController;
    @BeforeClass
    public static void classSetUp(){
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback,null,null,null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mFragment = new TimerNormalFragment();
        Intent intent = new Intent();
        mAlarmClock = Robolectric.buildActivity(AlarmClock.class, intent).create().resume().get();
        mAlarmClock = spy(mAlarmClock);
        FragmentHostCallback host = new FragmentHostCallback(mAlarmClock, new Handler(), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }

            @Override
            public void onStartActivityFromFragment(@NonNull Fragment fragment, Intent intent,
                                                    int requestCode, @Nullable Bundle options) {
                fragment.getActivity().startActivityFromFragment(fragment, intent, requestCode, options);
            }

            @Override
            public void onStartIntentSenderFromFragment(
                    @NonNull Fragment fragment, IntentSender intent, int requestCode,
                    @Nullable Intent fillInIntent, int flagsMask, int flagsValues,
                    int extraFlags, Bundle options) throws IntentSender.SendIntentException {
                fragment.getActivity().startIntentSenderFromFragment(fragment, intent, requestCode,
                        fillInIntent, flagsMask, flagsValues, extraFlags, options);
            }
        };
        FragmentManager fragmentManager = mAlarmClock.getSupportFragmentManager();
        ReflectUtil.setFieldValue(Fragment.class, "mFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mChildFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        ReflectUtil.invoke(Fragment.class, "performCreate", new Object[]{null},
                mFragment, Bundle.class);
        LayoutInflater mLayoutInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        ViewGroup mViewGroup = new ViewGroup(mAlarmClock) {
            @Override
            protected void onLayout(boolean b, int i, int i1, int i2, int i3) {
            }
        };
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(10, 10);
        mViewGroup.setLayoutParams(params);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mLayoutInflater", mFragment, mLayoutInflater);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mViewGroup", mFragment, mViewGroup);

        ReflectUtil.invoke(OplusTimerFragment.class, "addDefaultTimer", null, mFragment);
        mTimerController = (TimerController) ReflectUtil.getFieldValue(OplusTimerFragment.class, "mTimerController", mFragment);
        OplusTimePickerCustomClock mOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", mTimerController, mOplusTimerPicker);
        ReflectUtil.invoke(Fragment.class, "performResume", null, mFragment);
    }

    @Test
    public void should_call_refreshLayout_and_refreshViewsFromAiSupport_inorder_when_onReceive_with_actions_inorder() throws NoSuchFieldException, IllegalAccessException {
        BroadcastReceiver receiver = (BroadcastReceiver) ReflectUtil.getFieldValue(OplusTimerFragment.class, "mLocalReceiver", mFragment);
        String[]  actions = new String[]{TimerService.REFRESH_TIMERS, TimerConstant.PAUSE_TIMER_BROADCAST, TimerConstant.RESUME_TIMER_BROADCAST, TimerService.STOP_TIMER};
        TimerController mTimerController = (TimerController) ReflectUtil.getFieldValue(OplusTimerFragment.class, "mTimerController", mFragment);
        mTimerController = spy(mTimerController);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mTimerController", mFragment, mTimerController);
        Intent intent = new Intent();
        for(int i=0; i<actions.length; i++){
            intent.setAction(actions[i]);
            receiver.onReceive(mContext, intent);
        }
        InOrder inOrder = Mockito.inOrder(mTimerController, mTimerController,mTimerController);
        inOrder.verify(mTimerController).refreshLayout();
        inOrder.verify(mTimerController, times(2)).refreshViewsFromAiSupport();
        inOrder.verify(mTimerController).refreshLayout();
    }

    @Test
    public void should_call_setCurrentItem_when_refreshCtsTimers_with_getIntent_not_null_and_mCtsViewPager_mCtsPagerAdapter_not_null() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        TimerService mLoadService = Mockito.spy(new TimerService());
        mLoadService.registerTimer0();
        PagerAdapter mCtsPagerAdapter = mock(PagerAdapter.class);
        ViewPager mCtsViewPager = mock(ViewPager.class);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mCtsPagerAdapter", mFragment, mCtsPagerAdapter);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mCtsViewPager", mFragment, mCtsViewPager);
        //invoke refreshCtsTimers()
        ReflectUtil.invoke(OplusTimerFragment.class, "refreshCtsTimers", null, mFragment);
        //verify
        verify(mCtsViewPager).setCurrentItem(anyInt());
    }

    @Config(shadows = {ShadowTimerDataHelper.class})
    @Test
    public void should_call_setCurrentSecond_with_time_when_onRestoreCompleted_with_TIMER_SET_TIME_PREFERENCE_notEqualsTo_zero_and_has_selected_timer_in_list() throws NoSuchFieldException, IllegalAccessException {
        SharedPreferences prefs = (SharedPreferences) ReflectUtil.getFieldValue(OplusTimerFragment.class, "mPreferences", mFragment);
        SharedPreferences.Editor editor = prefs.edit();
        long time = 10L;
        editor.putLong(TimerConstant.TIMER_SET_TIME_PREFERENCE, time);
        editor.apply();
        OplusTimer timer = new OplusTimer();
        timer.setSelected(1);
        ArrayList<OplusTimer> list = new ArrayList<>(1);
        list.add(timer);
        ShadowTimerDataHelper.sTimers = list;
        //invoke
        mFragment.onRestoreCompleted();
        //assert
        OplusTimePickerCustomClock timerPicker = mTimerController.getOplusTimerPicker();
        verify(timerPicker).setCurrentSecond((int)time);
    }

    @Config(shadows = {ShadowTimerDataHelper.class})
    @Test
    public void should_call_setEditItemVisible_with_true_when_onAlarmClockResult_with_getAllTimers_return_list_not_null_and_listSize_greaterThan_one() throws NoSuchFieldException, IllegalAccessException {
        OplusTimer timer = new OplusTimer();
        timer.setSelected(1);
        int listSize = 2;
        ArrayList<OplusTimer> list = new ArrayList<>(listSize);
        list.add(timer);
        list.add(timer);
        ShadowTimerDataHelper.sTimers = list;
        Intent data = new Intent();
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "sIsShowEditItem", mFragment, false);
        //invoke
        mFragment.onAlarmClockResult(data);
        //assert call setEditItemVisible
        assertTrue((Boolean) ReflectUtil.getFieldValue(OplusTimerFragment.class, "sIsShowEditItem", mFragment));
    }


    @Config(shadows = {ShadowTimerDataHelper.class})
    @Test
    public void should_call_setEditItemVisible_with_true_when_onActivityResult_with_requestCode_is_START_SET_TIMER_FOR_RESULT_and_listSize_greaterThan_one()
            throws NoSuchFieldException, IllegalAccessException {
        OplusTimer timer = new OplusTimer();
        timer.setSelected(1);
        int listSize = 2;
        ArrayList<OplusTimer> list = new ArrayList<>(listSize);
        list.add(timer);
        list.add(timer);
        ShadowTimerDataHelper.sTimers = list;
        Intent data = new Intent();
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "sIsShowEditItem", mFragment, false);
        //invoke
        mFragment.onActivityResult(TimerSetFrament.START_SET_TIMER_FOR_RESULT, Activity.RESULT_OK, data);
        //assert call setEditItemVisible
        assertTrue((Boolean) ReflectUtil.getFieldValue(OplusTimerFragment.class, "sIsShowEditItem", mFragment));
    }

    @Ignore
    @Test
    public void should_call_setVisible_with_false_twice_when_initViewMenu_with_mToolbar_notNull_and_sIsShowEditItem_is_false_and_listSize_is_one() throws NoSuchFieldException, IllegalAccessException {
        COUIToolbar toolbar = mock(COUIToolbar.class);
        Menu menu = mock(Menu.class);
        when(toolbar.getMenu()).thenReturn(menu);
        MenuItem mEditItem = mock(MenuItem.class);
        when(menu.findItem(R.id.edit)).thenReturn(mEditItem);
        int listSize = 1;
        ArrayList<OplusTimer> list = new ArrayList<>(listSize);
        OplusTimer timer = new OplusTimer();
        list.add(timer);
        mTimerController.setTimerList(list);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "sIsShowEditItem", mFragment, false);
        //invoke initViewMenu()
//        mFragment.onCreateOptionsMenu(toolbar);
        //verify
        verify(mEditItem, times(2)).setVisible(false);
    }

    @Test
    @Ignore
    public void should_call_setTitle_with_unselect_all_and_call_showNavigation_when_initEditMenu_with_timerList_isNull_and_mAlarmClock_is_active() throws NoSuchMethodException, IllegalAccessException {
        COUIToolbar toolbar = mock(COUIToolbar.class);
        mFragment.mToolbar = toolbar;
        Menu menu = mock(Menu.class);
        MenuItem mSelectItem = mock(MenuItem.class);
        when(toolbar.getMenu()).thenReturn(menu);
        mTimerController.setTimerList(null);
        //invoke initEditMenu()
        ReflectUtil.invoke(OplusTimerFragment.class, "initEditMenu", null, mFragment);
        //assert && verify
        verify(mSelectItem).setTitle(R.string.unselect_all);
        verify(mAlarmClock).showNavigation();
    }


    @Test
    public void should_call_dismissNavigation_when_changeMode_with_mode_is_false_and_mAlarmClock_is_active() throws NoSuchFieldException, IllegalAccessException {
        boolean mode = false;
        boolean mEditMode = true;
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mEditMode", mFragment, mEditMode);
        mTimerController = spy(mTimerController);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mTimerController", mFragment, mTimerController);
        Button mButtonStart = mock(Button.class);
        ReflectUtil.setFieldValue(TimerController.class, "mButtonStart", mTimerController, mButtonStart);
        //invoke changeMode()
        mFragment.changeMode(mode);
        //assert && verify
        verify(mTimerController,times(2)).disableEditStatus();
        verify(mAlarmClock).dismissNavigation();
        assertFalse((boolean)ReflectUtil.getFieldValue(OplusTimerFragment.class, "mEditMode", mFragment));
    }

    @Test
    public void should_call_recordTimerDesc_with_timer_title_when_isResetTag_with_selectedTimer_not_null_and_timerList_not_contains_selectedTimer(){
        int size = 1;
        ArrayList<OplusTimer> timerList = new ArrayList(size);
        OplusTimer timer = new OplusTimer();
        OplusTimer selectedTimer = new OplusTimer();
        timerList.add(timer);
        mTimerController.setSelectedTimer(selectedTimer);
        mFragment = Mockito.spy(mFragment);
        //invoke isResetTag()
        mFragment.isResetTag(timerList);
        //assert
    }

    @Test
    public void should_return_true_when_getTimerWhetherCheckAll_with_has_timer_isChecked_and_flag_notEqualsTo_FLAG_NEW_TIMER_ITEM_in_list(){
        int size = 5;
        ArrayList<OplusTimer> list = new ArrayList(size);
        for(int i=0; i<size-1; i++){
            OplusTimer timer = new OplusTimer();
            timer.setCheckBox(true);
            timer.setFlag(OplusTimer.FLAG_SYSYTEM_TIMER_ITEM);
            list.add(timer);
        }
        OplusTimer timer = new OplusTimer();
        timer.setCheckBox(false);
        timer.setFlag(OplusTimer.FLAG_SYSYTEM_TIMER_ITEM);
        list.add(timer);
        mTimerController.setTimerList(list);
        //invoke getTimerWhetherCheckAll()
        boolean isAllSelect = mFragment.getTimerWhetherCheckAll();
        //assert
        assertTrue(isAllSelect);
    }

    @Test
    public void should_call_setDeleteEnable_with_false_when_selectQuerySelected_with_timers_getCheckBox_are_false() throws NoSuchFieldException, IllegalAccessException {
        int size = 3;
        ArrayList<OplusTimer> list = new ArrayList(size);
        for(int i=0; i<size; i++){
            OplusTimer timer = new OplusTimer();
            timer.setCheckBox(false);
            list.add(timer);
        }
        mTimerController.setTimerList(list);
        MenuItem mSelectItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mSelectItem", mFragment, mSelectItem);
        //invoke selectQuerySelected()
        mFragment.selectQuerySelected();
        //verify
        verify(mSelectItem).setTitle(R.string.unselect_all);
        verify(mAlarmClock).setNavigationItemEnable(false,R.id.navigation_delete);
    }

    @Ignore
    @Test
    public void should_no_exception_when_call_all_nonPrivate_method_with_illegal_args() throws IllegalAccessException {
        boolean isClassObject = false;
        ReflectUtil.NameFilter filter = new ReflectUtil.NameFilter();
        boolean invokeSuperMethod = false;
        Map<Method, Throwable> map = ReflectUtil.invokeMethodsWithIllegalArgs(mFragment, isClassObject,
                null, filter, null, null, invokeSuperMethod);
        assertTrue(map.size() == 0);
    }

    @Implements(TimerDataHelper.class)
    public static class ShadowTimerDataHelper{
        static ArrayList<OplusTimer> sTimers;
        public static ArrayList<OplusTimer> getAllTimers(Context context) {
            return  sTimers;
        }
    }

    @Implements(TimerController.class)
    public static class ShadowTimerController{
        @Implementation
        public void inflate(LayoutInflater inflater, ViewGroup container, int resId) {
        }

        @Implementation
        public void setOplusTimerPickerMarginTop(int topSize) {
        }
    }

    @Implements(COUIDarkModeUtil.class)
    public static class ShadowColorDarkModeUtil {
        @Implementation
        public static void setForceDarkAllow(View view, boolean allow) {
        }
    }
}
