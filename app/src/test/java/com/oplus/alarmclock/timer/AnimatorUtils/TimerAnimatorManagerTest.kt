/****************************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - TimerAnimatorManagerTest.java
 * Description:
 * Version: 1.0
 * Date : 2020/2/20
 * Author: <EMAIL>
 *
 * ---------------------Revision History: -----------------------
 * <author>    <data>       <version>     <desc>
 * YangLinlong  2020/2/20     1.0            build this module
</desc></version></data></author> */
package com.oplus.alarmclock.timer.AnimatorUtils

import android.animation.ObjectAnimator
import android.animation.TimeInterpolator
import android.view.View
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.timer.TimerAnimationManager
import com.oplus.alarmclock.timer.anim.TimerAnimatorManager
import org.junit.Assert
import org.junit.Test
import org.mockito.Mockito

class TimerAnimatorManagerTest : TestParent() {
    private var mTimerAnimatorManager: TimerAnimatorManager? = null
    @Throws(Exception::class)
    override fun setUp() {
        super.setUp()
        mTimerAnimatorManager = TimerAnimatorManager()
    }

    @Test
    fun should_listenerSize_is_one_when_onAnimatorSet_with_view_and_animatorList() {
        val view = Mockito.mock(View::class.java)
        val ani1 = Mockito.mock(ObjectAnimator::class.java)
        val ani2 = Mockito.mock(ObjectAnimator::class.java)
        val list: MutableList<ObjectAnimator> = ArrayList()
        list.add(ani1)
        list.add(ani2)
        val set = mTimerAnimatorManager!!.onAnimatorSet(view, list, true)
        val listeners = set.listeners
        val listenerSize = listeners?.size ?: 0
        Assert.assertEquals(1, listenerSize.toLong())
    }

    @Test
    fun should_set_correct_duration_when_setAnimatorAlpha_duration_biggerThan_zero() {
        val view = Mockito.mock(View::class.java)
        val timeInterpolator = Mockito.mock(TimeInterpolator::class.java)
        var duration = -1
        var objectAnimator =
            mTimerAnimatorManager!!.setAnimatorAlpha(view, duration, timeInterpolator, 0f)
        val expectedDuration = 300
        Assert.assertEquals(expectedDuration.toLong(), objectAnimator.duration)
        duration = 500
        objectAnimator =
            mTimerAnimatorManager!!.setAnimatorAlpha(view, duration, timeInterpolator, 0f)
        Assert.assertEquals(duration.toLong(), objectAnimator.duration)
    }

    @Test
    fun should_set_correct_duration_when_setAnimatorTranslation_duration_biggerThan_zero() {
        val view = Mockito.mock(View::class.java)
        val timeInterpolator = Mockito.mock(TimeInterpolator::class.java)
        var duration = -1
        var objectAnimator = mTimerAnimatorManager!!.setAnimatorTranslation(
            view,
            duration,
            timeInterpolator,
            "tag",
            0f
        )
        val expectedDuration = 300
        Assert.assertEquals(expectedDuration.toLong(), objectAnimator.duration)
        duration = 500
        objectAnimator = mTimerAnimatorManager!!.setAnimatorTranslation(
            view,
            duration,
            timeInterpolator,
            "tag",
            0f
        )
        Assert.assertEquals(duration.toLong(), objectAnimator.duration)
    }

}