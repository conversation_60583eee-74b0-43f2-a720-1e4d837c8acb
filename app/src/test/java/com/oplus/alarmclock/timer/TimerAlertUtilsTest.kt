/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerAlertUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 ****************************************************************/
package com.oplus.alarmclock.timer

import android.util.Log
import com.oplus.alarmclock.TestParent
import org.junit.Test

class TimerAlertUtilsTest : TestParent() {
    @Test
    fun should_no_exception_when_use_timer_alert_utils() {
        val timeMsg = getTimeMsg(mContext)
        Log.d("TimerAlertUtilsTest", timeMsg)
    }
}