package com.oplus.alarmclock.timer.mini;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.data.OplusTimer;

import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

public class TimerMiniAdapterTest extends TestParent {

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    public void should_verify_when_onCreateViewHolder() throws NoSuchMethodException, IllegalAccessException {
        TimerMiniAdapter adapter = Mockito.mock(TimerMiniAdapter.class);
        RecyclerView recyclerView = Mockito.mock(RecyclerView.class);
        recyclerView.setAdapter(adapter);
        ReflectUtil.invoke(TimerMiniAdapter.class, "onCreateViewHolder", new Object[]{recyclerView, 0}, adapter, ViewGroup.class, int.class);
        verify(adapter).onCreateViewHolder(recyclerView, 0);
    }

    @Test
    public void should_verify_when_onBindViewHolder() throws NoSuchMethodException, IllegalAccessException {
        TimerMiniAdapter adapter = Mockito.mock(TimerMiniAdapter.class);
        RecyclerView recyclerView = Mockito.mock(RecyclerView.class);
        RecyclerView.ViewHolder holder = Mockito.mock(RecyclerView.ViewHolder.class);
        recyclerView.setAdapter(adapter);
        ReflectUtil.invoke(TimerMiniAdapter.class, "onBindViewHolder", new Object[]{holder, 0}, adapter, RecyclerView.ViewHolder.class, int.class);
        verify(adapter).onBindViewHolder(holder, 0);
    }

    @Test
    public void should_equals_when_getItemTypeRowTow() {
        List<OplusTimer> timerList = new ArrayList<>();
        TimerMiniAdapter.OnItemClick callBack = Mockito.mock(TimerMiniAdapter.OnItemClick.class);
        TimerMiniAdapter adapter = new TimerMiniAdapter(mContext, timerList, callBack);
        assertEquals(2, adapter.getItemTypeRowTow());
    }

    @Test
    public void should_equals_when_getItemViewType_with_position() {
        TimerMiniAdapter adapter = Mockito.mock(TimerMiniAdapter.class);
        RecyclerView recyclerView = Mockito.mock(RecyclerView.class);
        recyclerView.setAdapter(adapter);
        assertEquals(0, adapter.getItemViewType(0));
    }

    @Test
    public void should_equals_when_getItemCount_with_position() throws NoSuchFieldException, IllegalAccessException {
        TimerMiniAdapter adapter = Mockito.mock(TimerMiniAdapter.class);
        ReflectUtil.setFieldValue(TimerMiniAdapter.class, "mHeaderCount", adapter, 1);
        RecyclerView recyclerView = Mockito.mock(RecyclerView.class);
        recyclerView.setAdapter(adapter);
        assertEquals(0, adapter.getItemCount());
    }
}
