/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main mFragmentActivity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-3, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer.ui;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.tintimageview.COUITintImageView;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ai.AiAlarmUtilsTest;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.timer.OplusTimerFragment;
import com.oplus.alarmclock.timer.TimerPreUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.timer.TimerTextView;
import com.oplus.alarmclock.timer.TimerView;
import com.oplus.alarmclock.timer.TimerWakeLock;
import com.oplus.alarmclock.timer.anim.TimerAnimatorManager;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.view.TimerRecyclerView;
import com.oplus.alarmclock.view.modelview.TimerModelView;
import com.oplus.alarmclock.view.water.WaterClockView;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowLooper;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TimerControllerTest extends TestParent {
    private static Activity mFragmentActivity;
    private static OplusTimerFragment mOplusTimerFragment;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        if (mFragmentActivity == null) {
            mFragmentActivity = Robolectric.buildActivity(FragmentActivity.class).get();
            //init TimerController#mOuter
            FragmentHostCallback host = new FragmentHostCallback(mFragmentActivity, mock(Handler.class), 0) {
                @Nullable
                @Override
                public Object onGetHost() {
                    return null;
                }
            };
            mOplusTimerFragment = new OplusTimerFragment() {
                @Override
                protected int layoutId() {
                    return R.layout.timer_main_view;
                }

                @Override
                protected TimerController createTimerController(int index) {
                    return new TimerController(index, this);
                }

                @Override
                protected TextView timerAdd() {
                    return null;
                }

                @Override
                protected OplusTimePickerCustomClock oplusTimerPicker() {
                    return null;
                }

                @Override
                protected TimerView timerView() {
                    return null;
                }

                @Override
                protected TimerTextView timerTextView() {
                    return null;
                }

                @Override
                protected TextView titleName() {
                    return null;
                }

                @Override
                protected RelativeLayout addTimerLayout() {
                    return null;
                }

                @Override
                protected View timerProgressViewLayout() {
                    return null;
                }



                @Override
                protected WaterClockView shadowBg() {
                    return null;
                }

                @Override
                protected COUIFloatingButton buttonStart() {
                    return null;
                }

                @Override
                protected COUITintImageView buttonCancel() {
                    return null;
                }

                @Override
                protected ConstraintLayout timerLayout() {
                    return null;
                }

                @Override
                protected TimerRecyclerView timerRecyclerView() {
                    return null;
                }

                @Override
                protected COUIToolbar couiToolbar() {
                    return null;
                }

                @Override
                protected int clockSize() {
                    return 0;
                }
            };
            ReflectUtil.setFieldValue(Fragment.class, "mHost", mOplusTimerFragment, host);
            SharedPreferences preferences = mContext.getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                    Context.MODE_PRIVATE);
        }
    }

    @AfterClass
    public static void classTearDown() {
        mFragmentActivity = null;
        mOplusTimerFragment = null;
    }

    @Test
    public void should_setCurrentHour_0_setCurrentMinute_0_setCurrentSecond_1_when_handleMessage_with_SET_DEFAULT_TIME()
            throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        when(mockOplusTimerPicker.getCurrentMinute()).thenReturn(0);
        when(mockOplusTimerPicker.getCurrentSecond()).thenReturn(0);
        when(mockOplusTimerPicker.getCurrentHour()).thenReturn(0);
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mockOplusTimerPicker);

        Message msg = Message.obtain();
        msg.what = OplusTimerFragment.SET_DEFAULT_TIME;
        //invoke
        timerController.handleMessage(msg);
        //verify
        verify(mockOplusTimerPicker).setCurrentHour(0);
        verify(mockOplusTimerPicker).setCurrentMinute(0);
        verify(mockOplusTimerPicker).setCurrentSecond(1);
    }

    @Ignore
    @Test
    public void should_call_setTime_twice_and_call_stopTimer_once_when_handleMessage_with_SCOUNTDOWN_TIME_and_remainTime_is_positive_num()
            throws NoSuchFieldException, IllegalAccessException {
        //mock TimerService and mock remainTime,first time handleMessage use remainTime=100L,
        // the second time use remainTime=100L
        TimerService mockTimerService = mock(TimerService.class);
        long firstRemainTime = 0L;
        long secondRemainTime = 0L;
        when(mockTimerService.getRemainTime(anyInt())).thenReturn(firstRemainTime, secondRemainTime);
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        when(mockPrefs.edit()).thenReturn(mock(SharedPreferences.Editor.class));

        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        ReflectUtil.setFieldValue(TimerController.class, "mService", timerController,
                mockTimerService);
        OplusCountdownTimeView mockCountdownTime = mock(OplusCountdownTimeView.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mock(OplusTimePickerCustomClock.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout",
                timerController, mock(View.class));
        ReflectUtil.setFieldValue(TimerController.class, "mButtonStart",
                timerController, mock(COUIFloatingButton.class));
        ReflectUtil.setFieldValue(TimerController.class, "mButtonCancel", timerController,
                mock(COUITintImageView.class));
        ReflectUtil.setFieldValue(TimerController.class, "mAddTimerLayout", timerController, mock(RelativeLayout.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView", timerController, new TimerTextView(mContext));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerView", timerController, new TimerView(mContext));

        //int TimerController#mIsStart true
        boolean isStart = true;
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart", timerController,
                isStart);
        Message msg = Message.obtain();
        msg.what = (int) ReflectUtil.getFieldValue(TimerController.class, "COUNTDOWN_TIME", null);
        //invoke
        timerController.handleMessage(msg);
        //run the task in TimerController#mHandler which set by
        // mHandler.sendEmptyMessageDelayed(COUNTDOWN_TIME, INTERVAL_PERIOD) in TimerController#handleMessage
        StaticHandler<TimerController> handler = timerController.getTimerHandler();
        ShadowLooper shadowLooper = Shadows.shadowOf(handler.getLooper());
        shadowLooper.idleFor(100, TimeUnit.MILLISECONDS);
        ReflectUtil.getFieldValue(TimerController.class, "mIsStart", timerController);
    }

    @Config(shadows = {ShadowContextNative.class})
    @Test
    public void should_delete_selected_timer_when_deleteSelectAllTimer_with_mTimerList_contains_selected_timer() {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        OplusTimer mockOplusTimer = mock(OplusTimer.class);
        //set two timers selected, three timers unselected
        boolean[] bools = new boolean[]{false, false, true, true, false};
        int expectedListSize = 3;
        when(mockOplusTimer.getCheckBox()).thenReturn(bools[0], bools[1], bools[2], bools[3], bools[4]);
        int timerFlag = OplusTimer.FLAG_CUSTOM_TIMER_ITEM;
        when(mockOplusTimer.getFlag()).thenReturn(timerFlag);
        ArrayList<OplusTimer> list = new ArrayList(8);
        for (int i = 0; i < bools.length; i++) {
            list.add(mockOplusTimer);
        }
        timerController.setTimerList(list);
        //invoke deleteSelectAllTimer()
        int listSize = timerController.deleteSelectAllTimer();
        //verify delete two selected timer
        assertEquals(expectedListSize, listSize);
    }

    @Config(shadows = {ShadowContextNative.class})
    @Test
    public void should_return_RETURN_STATUS_ERROR_when_deleteSelectAllTimer_with_mTimerList_not_contains_selected_timer()
            throws NoSuchFieldException, IllegalAccessException {
        //init TimerController
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController#mTimerList
        OplusTimer mockOplusTimer = mock(OplusTimer.class);
        //set all timers unselected
        boolean[] bools = new boolean[]{false, false, false, false, false};
        int expectedListSize = (int) ReflectUtil.getFieldValue(TimerController.class,
                "RETURN_STATUS_ERROR", null);
        when(mockOplusTimer.getCheckBox()).thenReturn(bools[0], bools[1], bools[2], bools[3], bools[4]);
        int timerFlag = OplusTimer.FLAG_CUSTOM_TIMER_ITEM;
        when(mockOplusTimer.getFlag()).thenReturn(timerFlag);
        ArrayList<OplusTimer> list = new ArrayList(8);
        for (int i = 0; i < bools.length; i++) {
            list.add(mockOplusTimer);
        }
        timerController.setTimerList(list);
        //invoke deleteSelectAllTimer()
        int listSize = timerController.deleteSelectAllTimer();
        //verify delete two selected timer
        assertEquals(expectedListSize, listSize);
    }

    @Config(shadows = {ShadowContextNative.class})
    @Test
    public void should_add_a_blank_timer_when_deleteSelectAllTimer_with_mTimerList_contain_all_timers_are_selected() {
        //init TimerController
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController#mTimerList
        OplusTimer mockOplusTimer = mock(OplusTimer.class);
        //set all timers selected
        boolean[] bools = new boolean[]{true, true, true, true, true};
        int expectedListSize = 0;
        when(mockOplusTimer.getCheckBox()).thenReturn(bools[0], bools[1], bools[2], bools[3], bools[4]);
        int timerFlag = OplusTimer.FLAG_CUSTOM_TIMER_ITEM;
        when(mockOplusTimer.getFlag()).thenReturn(timerFlag);
        ArrayList<OplusTimer> list = new ArrayList(8);
        for (int i = 0; i < bools.length; i++) {
            list.add(mockOplusTimer);
        }
        timerController.setTimerList(list);
        //invoke deleteSelectAllTimer()
        int listSize = timerController.deleteSelectAllTimer();
        //verify delete two selected timer
        assertEquals(expectedListSize, listSize);
    }

    @Config(shadows = {ShadowContextNative.class})
    @Test
    public void should_return_RETURN_STATUS_ERROR_when_deleteSelectAllTimer_with_mTimerList_not_selected()
            throws NoSuchFieldException, IllegalAccessException {
        //init TimerController
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController#mTimerList
        OplusTimer mockOplusTimer = mock(OplusTimer.class);
        //set one timer
        boolean[] bools = new boolean[]{false};
        int expectedListSize = (int) ReflectUtil.getFieldValue(TimerController.class,
                "RETURN_STATUS_ERROR", null);
        when(mockOplusTimer.getCheckBox()).thenReturn(bools[0]);
        int timerFlag = OplusTimer.FLAG_CUSTOM_TIMER_ITEM;
        when(mockOplusTimer.getFlag()).thenReturn(timerFlag);
        ArrayList<OplusTimer> list = new ArrayList(8);
        for (int i = 0; i < bools.length; i++) {
            list.add(mockOplusTimer);
        }
        TimerController.setTimerList(list);
        //invoke deleteSelectAllTimer()
        int listSize = TimerController.deleteSelectAllTimer();
        //verify delete two selected timer
        assertEquals(expectedListSize, listSize);
    }

    @Config(shadows = {ShadowContextNative.class})
    @Test
    public void should_selected_timer_num_when_selectQuerySelectedItemNumber_with_mTimerList_contain_selected_timers() {
        //init TimerController
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController#mTimerList
        OplusTimer mockOplusTimer = mock(OplusTimer.class);
        //set four timers selected
        boolean[] bools = new boolean[]{false, true, true, true, true};
        int expectedNum = 4;
        when(mockOplusTimer.getCheckBox()).thenReturn(bools[0], bools[1], bools[2], bools[3], bools[4]);
        int timerFlag = OplusTimer.FLAG_CUSTOM_TIMER_ITEM;
        when(mockOplusTimer.getFlag()).thenReturn(timerFlag);
        ArrayList<OplusTimer> list = new ArrayList(8);
        for (int i = 0; i < bools.length; i++) {
            list.add(mockOplusTimer);
        }
        TimerController.setTimerList(list);
        //invoke deleteSelectAllTimer()
        int actualNum = TimerController.selectQuerySelectedItemNumber();
        //verify delete two selected timer
        assertEquals(expectedNum, actualNum);
    }

    @Config(shadows = {ShadowContextNative.class, ShadowTimerDatahelper.class})
    @Test
    public void should_call_timer_setSelected_with_0_five_times_when_updateTimeAfterEdit_with_mSelectedTimer_is_null_and_mTimeList_size_is_five()
            throws NoSuchFieldException, IllegalAccessException {
        SharedPreferences sharedPreferences = mock(SharedPreferences.class);
        when(sharedPreferences.edit()).thenReturn(mock(SharedPreferences.Editor.class));
        //init TimerController
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController#mOplusTimerPicker
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                TimerController, mockOplusTimerPicker);
        //init TimerController#mSelectedTimer
        OplusTimer selectedTimer = mock(OplusTimer.class);
        int selectedTimerIndex = 1;
        when(selectedTimer.getTimerIndex()).thenReturn(selectedTimerIndex);
        TimerController.setSelectedTimer(selectedTimer);
        OplusTimer mockTimer = mock(OplusTimer.class);
        when(mockTimer.getTimerIndex()).thenReturn(selectedTimerIndex);
        when(mockTimer.getSelected()).thenReturn(1);
        long duration = 10L;
        when(mockTimer.getDuration()).thenReturn(duration);
        ArrayList<OplusTimer> list = new ArrayList(1);
        list.add(mockTimer);

        //set TimerController#mTimerList list
        ShadowTimerDatahelper.sList = list;
        //invoke onGridItemDeleteCallback()
        TimerController.updateTimeAfterEdit(mContext);
        //verify
//        verify(mockOplusTimerPicker).setCurrentSecond((int) duration);
        //release
        ShadowTimerDatahelper.sList = null;
    }

    @Config(shadows = {ShadowTimerWakeLock.class, AiAlarmUtilsTest.ShadowMediaFile.class})
    @Test
    public void should_call_recordTimerSetTime_with_second_when_startTimer_with_mIsPause_and_mIsStart_is_false() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        SharedPreferences sharedPreferences = mock(SharedPreferences.class);

        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //set mIsPause and mIsStart is false
        boolean isPause = false;
        boolean isStart = false;
        ReflectUtil.setFieldValue(TimerController.class, "mIsPause", TimerController, isPause);
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart", TimerController, isStart);
        //init TimerController
        ReflectUtil.setFieldValue(TimerController.class, "mService", TimerController, mock(TimerService.class));
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        when(mockPrefs.edit()).thenReturn(mock(SharedPreferences.Editor.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout", TimerController,
                mock(View.class));
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", TimerController,
                mock(OplusTimePickerCustomClock.class));
        OplusTimerFragment spyOplusTimerFragment = spy(mOplusTimerFragment);
        ReflectUtil.setFieldValue(TimerController.class, "mOuter", TimerController,
                spyOplusTimerFragment);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView", TimerController,
                new TimerTextView(mContext));
        long second = 100L;
        boolean refresh = false;
        Object[] args = new Object[]{second, refresh};
        //invoke
        ReflectUtil.invoke(TimerController.class, "startTimer", args, TimerController, long.class, boolean.class);
        //verify
//        verify(spyOplusTimerFragment).recordTimerSetTime(second);
        long countTime = (long) ReflectUtil.getFieldValue(TimerController.class, "mCountTime", TimerController);
        assertEquals(second, countTime);
    }

    @Ignore
    @Test
    @Config(shadows = {AiAlarmUtilsTest.ShadowMediaFile.class})
    public void should_call_getRemainTime_and_mCountTime_equals_to_remainTime_when_startTimer_mIsStart_is_true() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        //set mIsStart is true
        boolean isStart = true;
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart", timerController, isStart);
        //init TimerController
        TimerService mockService = mock(TimerService.class);
        long remainTime = 10L;
        when(mockService.getRemainTime(anyInt())).thenReturn(remainTime);
        ReflectUtil.setFieldValue(TimerController.class, "mService", timerController, mockService);
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        when(mockPrefs.edit()).thenReturn(mock(SharedPreferences.Editor.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout", timerController,
                mock(View.class));
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", timerController,
                mock(OplusTimePickerCustomClock.class));
//        mCountTime = mService.getRemainTime(mIndex);
        COUITintImageView mButtonCancel = new COUITintImageView(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        layoutParams.setMarginEnd(1);
        mButtonCancel.setLayoutParams(layoutParams);
        mButtonCancel.setVisibility(View.VISIBLE);
        timerController.mTimerView = new TimerView(mContext);
        ReflectUtil.setFieldValue(TimerController.class, "mButtonCancel", timerController, mButtonCancel);
        ReflectUtil.setFieldValue(TimerController.class, "mButtonStart", timerController, new COUIFloatingButton(mContext));
        long second = 100L;
        boolean refresh = false;
        Object[] args = new Object[]{second, refresh};
        //invoke startTimer()
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView", timerController, new TimerTextView(mContext));
        ReflectUtil.invoke(TimerController.class, "startTimer", args, timerController, long.class, boolean.class);
        //verify&assert
//        verify(mockService).getRemainTime(anyInt());
        long countTime = (long) ReflectUtil.getFieldValue(TimerController.class, "mCountTime", timerController);
        assertEquals(remainTime, countTime);
    }

    @Ignore
    @Config(shadows = {ShadowTimerWakeLock.class, AiAlarmUtilsTest.ShadowMediaFile.class})
    @Test
    public void should_call_refreshDynamicViews_when_startTimer_refresh_is_true() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController
        TimerService timerService = mock(TimerService.class);
        when(timerService.isStart(0)).thenReturn(true);
        ReflectUtil.setFieldValue(TimerController.class, "mService", timerController, timerService);
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        when(mockPrefs.edit()).thenReturn(mock(SharedPreferences.Editor.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout", timerController,
                mock(View.class));
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", timerController,
                mock(OplusTimePickerCustomClock.class));
        ReflectUtil.setFieldValue(TimerController.class, "mButtonStart",
                timerController, mock(COUIFloatingButton.class));
        COUITintImageView mButtonCancel = new COUITintImageView(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        layoutParams.setMarginEnd(1);
        mButtonCancel.setLayoutParams(layoutParams);
        mButtonCancel.setVisibility(View.INVISIBLE);
        ReflectUtil.setFieldValue(TimerController.class, "mButtonCancel", timerController, mButtonCancel);
        ReflectUtil.setFieldValue(TimerController.class, "mAddTimerLayout", timerController, mock(RelativeLayout.class));
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView", timerController, new TimerTextView(mContext));
        long second = 100L;
        boolean refresh = true;
        Object[] args = new Object[]{second, refresh};
        //invoke startTimer()
        ReflectUtil.invoke(TimerController.class, "startTimer", args, timerController, long.class, boolean.class);
        //force TimerController#mHandler handle REFRESH_VIEWS message sent by startTimer() when refresh is true
        StaticHandler<TimerController> handler = timerController.getTimerHandler();
        ShadowLooper shadowLooper = Shadows.shadowOf(handler.getLooper());
        shadowLooper.idle();

        //verify&assert   call mButtonStart.setMainFabDrawable(mContext.getDrawable(R.drawable.button_pause)); in refreshBottomLayout()
//        verify(mButtonCancel).setVisibility(View.VISIBLE);
    }

    @Test
    public void should_set_mCountdownTime_gone_and_set_mOplusTimerPicker_visible_when_refreshVisible_with_mIsStart_mIsPause_are_false_and_mOplusTimerPicker_not_shown() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController
        boolean isStart = false;
        boolean isPause = false;
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart", TimerController, isStart);
        ReflectUtil.setFieldValue(TimerController.class, "mIsPause", TimerController, isPause);
        View timerProgressViewLayout = mock(View.class);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout",
                TimerController, timerProgressViewLayout);
        OplusTimePickerCustomClock mockOplusTimePicker = mock(OplusTimePickerCustomClock.class);
        boolean isShown = false;
        when(mockOplusTimePicker.isShown()).thenReturn(isShown);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", TimerController, mockOplusTimePicker);

        //ensure mTimerList.size() > 1
        OplusTimer timer = mock(OplusTimer.class);
        ArrayList<OplusTimer> timerList = TimerController.getTimerList();
        timerList.add(timer);
        timerList.add(timer);
        //invoke refreshVisible()
        ReflectUtil.invoke(TimerController.class, "refreshVisible", null, TimerController);
        //verify&assert
        verify(mOplusTimerFragment).setEditItemVisible(true);
    }

    @Test
    public void should_set_mCountdownTime_visible_and_set_mOplusTimerPicker_gone_when_refreshVisible_with_mIsStart_is_true()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //init TimerController
        boolean isStart = true;
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart", TimerController, isStart);
        OplusCountdownTimeView mockCountdownTime = mock(OplusCountdownTimeView.class);
        View timerProgressViewLayout = mock(View.class);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout",
                TimerController, timerProgressViewLayout);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView",
                TimerController, new TimerTextView(mContext));
        OplusTimePickerCustomClock mockOplusTimePicker = mock(OplusTimePickerCustomClock.class);
        boolean isShown = false;
        when(mockOplusTimePicker.isShown()).thenReturn(isShown);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker", TimerController, mockOplusTimePicker);
        //invoke refreshVisible()
        ReflectUtil.invoke(TimerController.class, "refreshVisible", null, TimerController);
        //verify
        verify(mOplusTimerFragment).setEditItemVisible(false);
    }

    @Implements(TimerDataHelper.class)
    public static class ShadowTimerDatahelper {
        static ArrayList sList;

        @Implementation
        public static ArrayList<OplusTimer> getAllTimers(Context context) {
            return sList;
        }
    }

    @Implements(TimerWakeLock.class)
    public static class ShadowTimerWakeLock {
        @Implementation
        public static void acquireCpuWakeLockPartial(Context context) {
        }
    }

    @Test
    public void should_set_timer_desc_to_normal_when_setSelectedTimerToNormal_with_OplusTimer_isNot_null() throws NoSuchFieldException, IllegalAccessException {
        //mock selected timer
        OplusTimer selectedTimer = mock(OplusTimer.class);
        View view = mock(View.class);
        View centerView = mock(View.class);
        TextView durationView = mock(TextView.class);
        TextView descriptionView = mock(TextView.class);
        OplusTimerFragment mOuter = mock(OplusTimerFragment.class);
        //init TimerController
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        ReflectUtil.setFieldValue(TimerController.class, "mOuter", TimerController, mOuter);
        //invoke
        TimerController.setSelectedTimerToNormal();
        //verify

    }

    @Test
    public void should_change_view_visible_status_when_setVisible_with_timer_status_change() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        //mock selected timer
        OplusCountdownTimeView mCountdownTime = mock(OplusCountdownTimeView.class);
        View mTimerProgressViewLayout = mock(View.class);

        boolean[][] visible = new boolean[][]{{true, false}, {false, true}, {true, false}, {false, false}};

        //init TimerController
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController timerController = new TimerController(0, mOplusTimerFragment);

        ReflectUtil.setFieldValue(TimerController.class, "mTimerProgressViewLayout", timerController, mTimerProgressViewLayout);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerTextView", timerController, new TimerTextView(mContext));

        for (boolean[] arr : visible) {
            //invoke
            ReflectUtil.setFieldValue(TimerController.class, "mIsStart", timerController, arr[0]);
            ReflectUtil.setFieldValue(TimerController.class, "mIsPause", timerController, arr[1]);
            //verify
            verifySetVisible(mCountdownTime, timerController, arr[0], arr[1]);
        }
    }

    private void verifySetVisible(OplusCountdownTimeView mCountdownTime, TimerController TimerController, boolean isStart, boolean isPause) throws NoSuchMethodException, IllegalAccessException {
        ReflectUtil.invoke(TimerController.class, "setVisible", null, TimerController);
        if (!isStart && !isPause) {
            verify(mCountdownTime, atLeast(0)).setVisibility(View.GONE);
        } else {
            verify(mCountdownTime, atLeast(0)).setVisibility(View.VISIBLE);
        }
    }

    @Test
    public void should_return_setting_timer_status_when_getTimerStatus_with() throws NoSuchFieldException, IllegalAccessException {
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        Context context = spy(mContext);

        //init TimerController
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //invoke
        ReflectUtil.setFieldValue(TimerController.class, "mContext", TimerController, context);

        //invoke
        int timerStatusInit = 11;
        doReturn(timerStatusInit).when(mockPrefs).getInt(TimerService.TIMER_STATUS_PREFERENCE, 0);
        doReturn(mockPrefs).when(context).getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                Context.MODE_PRIVATE);
        int timerStatus = TimerPreUtils.getTimerStatus();
        //verify
        assertNotEquals(timerStatusInit, timerStatus);
    }

    @Test
    public void should_return_setting_timer_Remain_when_getTimeRemained_with() throws NoSuchFieldException, IllegalAccessException {
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        Context context = spy(mContext);

        //init TimerController
        mOplusTimerFragment = spy(mOplusTimerFragment);
        TimerController TimerController = new TimerController(0, mOplusTimerFragment);
        //invoke
        ReflectUtil.setFieldValue(TimerController.class, "mContext", TimerController, context);

        long timerRemainInit = 1000L;
        String timerDataTotalTimePreference = "timer_total_time";
        doReturn(timerRemainInit).when(mockPrefs).getLong(timerDataTotalTimePreference, 0);
        doReturn(mockPrefs).when(context).getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                Context.MODE_PRIVATE);
        //invoke
        long isOpenNext = TimerPreUtils.getTimeRemained();
        //verify
        assertNotEquals(timerRemainInit, isOpenNext);
    }

    @Test
    public void should_mTimerModelView_dismiss_when_closedModelView() {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        TimerModelView mTimerModelView = Mockito.mock(TimerModelView.class);
        Whitebox.setInternalState(timerController, "mTimerModelView", mTimerModelView);
        timerController.closedModelView();
        Mockito.verify(mTimerModelView).dismiss();
    }

    @Test
    public void should_mTimerModelView_show_when_openModelView_with_timerFragment_not_null() {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        TimerModelView mTimerModelView = Mockito.mock(TimerModelView.class);
        Whitebox.setInternalState(timerController, "mTimerModelView", mTimerModelView);
        timerController.openModelView(new OplusTimer(), false,null);
        Mockito.verify(mTimerModelView).show(any(), anyBoolean(), any(), any());
    }

    @Test
    public void should_mTimerModelView_not_null_when_initModelView() {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        timerController.initModelView();
        TimerModelView mTimerModelView = Whitebox.getInternalState(timerController, "mTimerModelView");
        Assert.assertNotNull(mTimerModelView);
        mTimerModelView.onResultData(new Intent());
    }

    @Test
    @Ignore
    public void should_status_correct_when_update() throws Exception {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        Whitebox.setInternalState(timerController, "mService", new TimerService());
        timerController.mTimerView = new TimerView(mContext);
        timerController.mTimerTextView = new TimerTextView(mContext);
        long totalTime = 100L;
        Whitebox.invokeMethod(timerController, "setCountDownTotalTime", totalTime);

        long remainTime = 0;
        timerController.setCountDownTime(remainTime);

        OplusTimePickerCustomClock oplusTimerPicker = Mockito.mock(OplusTimePickerCustomClock.class);
        timerController.mOplusTimerPicker = oplusTimerPicker;
        timerController.enableEditStatus();
        Assert.assertTrue(timerController.isEditStatus());

        timerController.disableEditStatus();
        Assert.assertFalse(timerController.isEditStatus());

        timerController.onAnminalAllFadeOut(100);
        Whitebox.invokeMethod(timerController, "startFlashingAnimator");
        Whitebox.invokeMethod(timerController, "stopFlashingAnimator");
    }

    @Test
    public void should_timer_selected_when_selectAllTimer() {
        OplusTimer timer = new OplusTimer();
        ArrayList<OplusTimer> list = new ArrayList<>();
        list.add(timer);
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        Whitebox.setInternalState(timerController, "mTimerList", list);
        timerController.selectAllTimer(true);
        Assert.assertTrue(timer.getCheckBox());
    }

    @Test
    public void should_timer_selected_when_changeTableTopPosture() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mockOplusTimerPicker);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerAnimatorManager",
                timerController, new TimerAnimatorManager());
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, new TimerRecyclerView(mContext));
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart",
                timerController, true);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerLayout",
                timerController, new ConstraintLayout(mContext));
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mTimerAdapterDec",
                mOplusTimerFragment, new TimerAdapterSpaceItemDecoration());
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mTimerTextView = new TimerTextView(mContext);
        ViewGroup.MarginLayoutParams tLayoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerTextView.setLayoutParams(tLayoutParams);
        timerController.mTimerProgressViewLayout = new View(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerProgressViewLayout.setLayoutParams(layoutParams);
        timerController.mShadowBg = new WaterClockView(mContext);
        timerController.mTimerView = new TimerView(mContext);
        timerController.mIsHover = false;
        mOplusTimerFragment.changeToHover();
        assertFalse(timerController.mIsHover);
    }

    @Test
    public void should_timer_selected_when_changeTableTopPosture_isStart_with_false() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mockOplusTimerPicker);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerAnimatorManager",
                timerController, new TimerAnimatorManager());
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, new TimerRecyclerView(mContext));
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart",
                timerController, false);
        ReflectUtil.setFieldValue(TimerController.class, "mIsPause",
                timerController, false);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerLayout",
                timerController, new ConstraintLayout(mContext));
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mTimerAdapterDec",
                mOplusTimerFragment, new TimerAdapterSpaceItemDecoration());
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mTimerTextView = new TimerTextView(mContext);
        ViewGroup.MarginLayoutParams tLayoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerTextView.setLayoutParams(tLayoutParams);
        timerController.mTimerProgressViewLayout = new View(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerProgressViewLayout.setLayoutParams(layoutParams);
        timerController.mShadowBg = new WaterClockView(mContext);
        timerController.mTimerView = new TimerView(mContext);
        timerController.mIsHover = false;
        mOplusTimerFragment.changeToHover();
        assertFalse(timerController.mIsHover);
    }

    @Test
    public void should_timer_selected_when_changeNormal() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mockOplusTimerPicker);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerAnimatorManager",
                timerController, new TimerAnimatorManager());
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, new TimerRecyclerView(mContext));
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart",
                timerController, true);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerLayout",
                timerController, new ConstraintLayout(mContext));
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mTimerTextView = new TimerTextView(mContext);
        ViewGroup.MarginLayoutParams tLayoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerTextView.setLayoutParams(tLayoutParams);
        timerController.mTimerProgressViewLayout = new View(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerProgressViewLayout.setLayoutParams(layoutParams);
        timerController.mShadowBg = new WaterClockView(mContext);
        timerController.mTimerView = new TimerView(mContext);
        timerController.mIsHover = true;
        mOplusTimerFragment.changeNormal();
        Assert.assertTrue(timerController.mIsHover);
    }

    @Test
    public void should_timer_selected_when_changeNormal_with_is_start_false() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        OplusTimePickerCustomClock mockOplusTimerPicker = mock(OplusTimePickerCustomClock.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOplusTimerPicker",
                timerController, mockOplusTimerPicker);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerAnimatorManager",
                timerController, new TimerAnimatorManager());
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, new TimerRecyclerView(mContext));
        ReflectUtil.setFieldValue(TimerController.class, "mIsStart",
                timerController, false);
        ReflectUtil.setFieldValue(TimerController.class, "mIsPause",
                timerController, false);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerLayout",
                timerController, new ConstraintLayout(mContext));
        ReflectUtil.setFieldValue(OplusTimerFragment.class, "mTimerAdapterDec",
                mOplusTimerFragment, new TimerAdapterSpaceItemDecoration());
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mTimerTextView = new TimerTextView(mContext);
        ViewGroup.MarginLayoutParams tLayoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerTextView.setLayoutParams(tLayoutParams);
        timerController.mTimerProgressViewLayout = new View(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(0, 0);
        timerController.mTimerProgressViewLayout.setLayoutParams(layoutParams);
        timerController.mShadowBg = new WaterClockView(mContext);
        timerController.mTimerView = new TimerView(mContext);
        timerController.mIsHover = true;
        mOplusTimerFragment.changeNormal();
        Assert.assertTrue(timerController.mIsHover);
    }

    @Test
    public void should_timer_selected_when_setListAndAddMargin_with_is_start_false() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        TimerRecyclerView re = new TimerRecyclerView(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        re.setLayoutParams(layoutParams);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, re);
        ViewGroup.MarginLayoutParams addLayoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mAddTimerLayout.setLayoutParams(addLayoutParams);
        mOplusTimerFragment.setOsloTimerListPadding();
        ViewGroup.MarginLayoutParams listLayoutPar = (ViewGroup.MarginLayoutParams) timerController.mAddTimerLayout.getLayoutParams();
        Assert.assertNotEquals(listLayoutPar.leftMargin, 200);
    }

    @Test
    public void should_timer_selected_when_applyConstraintByOrientation_padding_0() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        TimerRecyclerView re = new TimerRecyclerView(mContext);
        ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        re.setLayoutParams(layoutParams);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerRecyclerView",
                timerController, re);
        ReflectUtil.setFieldValue(TimerController.class, "mTimerLayout",
                timerController, new ConstraintLayout(mContext));
        ViewGroup.MarginLayoutParams addLayoutParams = new ViewGroup.MarginLayoutParams(1, 1);
        timerController.mAddTimerLayout = new RelativeLayout(mContext);
        timerController.mAddTimerLayout.setLayoutParams(addLayoutParams);
        OplusTimerFragment mOuter = mock(OplusTimerFragment.class);
        ReflectUtil.setFieldValue(TimerController.class, "mOuter", timerController, mOuter);
        ViewGroup.MarginLayoutParams listLayoutPar = (ViewGroup.MarginLayoutParams) timerController.mAddTimerLayout.getLayoutParams();
        Assert.assertEquals(listLayoutPar.leftMargin, 0);
    }


    @Test
    public void should_timer_selected_whenTimerAdapterSpaceItemDecoration() throws NoSuchFieldException, IllegalAccessException {
        TimerController timerController = new TimerController(0, mOplusTimerFragment);
        TimerRecyclerView re = new TimerRecyclerView(mContext);
        TimerAdapterSpaceItemDecoration timerAdapterSpaceItemDecoration = new TimerAdapterSpaceItemDecoration();


    }

}