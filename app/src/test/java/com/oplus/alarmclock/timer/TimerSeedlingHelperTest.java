/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AiAlarmUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/11/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.timer;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class TimerSeedlingHelperTest extends TestParent {

    @Test
    public void should_return_false_when_isSupportFluidCloud() {
        assertFalse(TimerSeedlingHelper.isSupportFluidCloud());
    }

    @Test
    public void should_return_false_when_isSeedlingCardLoading() {
        assertFalse(TimerSeedlingHelper.isSeedlingCardLoading());
    }

    @Test
    public void should_return_false_when_isSeedlingCardLoadFail() {
        assertFalse(TimerSeedlingHelper.isSeedlingCardLoadFail());
    }

    @Test
    public void should_return_false_when_asynSupportFluidCloud() {
        assertFalse(TimerSeedlingHelper.isSupportFluidCloud());
    }

    @Test
    @Ignore
    public void should_equals_when_resetHelper() throws NoSuchFieldException, IllegalAccessException {
        TimerSeedlingHelper helper = Mockito.mock(TimerSeedlingHelper.class);
        helper.resetHelper();
        int timeIndex = (int) ReflectUtil.getFieldValue(TimerSeedlingHelper.class, "mCurrentTimeIndex", TimerSeedlingHelper.INSTANCE);
        assertEquals(0, timeIndex);
    }
}
