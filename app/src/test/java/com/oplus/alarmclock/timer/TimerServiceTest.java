/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-24, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Handler;
import android.util.SparseArray;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InOrder;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.mock;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class TimerServiceTest extends TestParent {
    TimerService mSpyTimerService;
    LocalBroadcastManager mLocalBroadcastManager;

    final int STATUS_PREPARE = 0;
    final int STATUS_START = 1;
    final int STATUS_PAUSE = 2;
    final String TIMER_DATA_PREFERENCE = "timer_data";
    final String TIMER_STATUS_PREFERENCE = "timer_status";
    final String TIMER_DATA_TOTAL_TIME_PREFERENCE = "timer_total_time";
    @Override
    public void setUp() throws Exception{
        super.setUp();
        Field f = LocalBroadcastManager.class.getDeclaredField("mInstance");
        f.setAccessible(true);
        f.set(null,null);
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(mContext);
        TimerService service = Robolectric.buildService(TimerService.class).get();
        mSpyTimerService = Mockito.spy(service);
    }

    @Ignore
    @Test
    public void testOncreat_StatusStart() throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        long recordTime = 1L;
        doReturn(recordTime).when(mockPrefs).getLong(TIMER_DATA_PREFERENCE, 0);
        doReturn(STATUS_START).when(mockPrefs).getInt(TIMER_STATUS_PREFERENCE, 0);
        doReturn(Long.MAX_VALUE).when(mockPrefs).getLong(TIMER_DATA_TOTAL_TIME_PREFERENCE, 0);
        doNothing().when(mSpyTimerService).setTotalTime(anyLong(),anyLong(),anyInt());
        doNothing().when(mSpyTimerService).startTimer(anyInt());
        doReturn(mockPrefs).when(mSpyTimerService).getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                Context.MODE_PRIVATE);
        BroadcastReceiver mockReceiver = mock(BroadcastReceiver.class);
        ReflectUtil.setFieldValue(TimerService.class,"mLocalReceiver",mSpyTimerService,mockReceiver);
        //invoke onCreate()
        mSpyTimerService.onCreate();
        Class c1 = Class.forName("com.oplus.alarmclock.timer.TimerService$TimerBroadCastReceiver");
        verify(mSpyTimerService).registerReceiver(any((Class<BroadcastReceiver>)c1), any(IntentFilter.class), any(String.class), nullable(Handler.class));
        verify(mSpyTimerService).registerReceiver(any(TimerAlertReceiver.class), any(IntentFilter.class), any(String.class), nullable(Handler.class));
        mLocalBroadcastManager.sendBroadcast(new Intent(TimerService.TIMER_STOP_TIMER));
        verify(mockReceiver).onReceive(any(Context.class),any(Intent.class));
        InOrder inOrder = Mockito.inOrder(mSpyTimerService,mSpyTimerService,mSpyTimerService);
        inOrder.verify(mSpyTimerService).registerTimer0();
        inOrder.verify(mSpyTimerService).setTotalTime(anyLong(),anyLong(),anyInt());
        inOrder.verify(mSpyTimerService).startTimer(0);
    }


    @Ignore
    @Test
    public void testOncreat_StatusPause() throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
        SharedPreferences mockPrefs = mock(SharedPreferences.class);
        long recordTime = 1L;
        doReturn(recordTime).when(mockPrefs).getLong(TIMER_DATA_PREFERENCE, 0);
        doReturn(STATUS_PAUSE).when(mockPrefs).getInt(TIMER_STATUS_PREFERENCE, 0);
        doReturn(Long.MAX_VALUE).when(mockPrefs).getLong(TIMER_DATA_TOTAL_TIME_PREFERENCE, 0);
        doReturn(mock(SharedPreferences.Editor.class)).when(mockPrefs).edit();
        doNothing().when(mSpyTimerService).setTotalTime(anyLong(),anyLong(),anyInt());
        doNothing().when(mSpyTimerService).startTimer(anyInt());
        doReturn(mockPrefs).when(mSpyTimerService).getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                Context.MODE_PRIVATE);
        BroadcastReceiver mockReceiver = mock(BroadcastReceiver.class);
        ReflectUtil.setFieldValue(TimerService.class,"mLocalReceiver",mSpyTimerService,mockReceiver);
        //invoke onCreate()
        mSpyTimerService.onCreate();
        Class c1 = Class.forName("com.oplus.alarmclock.timer.TimerService$TimerBroadCastReceiver");

        verify(mSpyTimerService).registerReceiver(any((Class<BroadcastReceiver>)c1), any(IntentFilter.class), any(String.class), nullable(Handler.class));
        verify(mSpyTimerService).registerReceiver(any(TimerAlertReceiver.class), any(IntentFilter.class), any(String.class), nullable(Handler.class));
        mLocalBroadcastManager.sendBroadcast(new Intent(TimerService.TIMER_STOP_TIMER));
        verify(mockReceiver).onReceive(any(Context.class),any(Intent.class));
        InOrder inOrder = Mockito.inOrder(mSpyTimerService,mSpyTimerService,mSpyTimerService);
        inOrder.verify(mSpyTimerService).registerTimer0();
        inOrder.verify(mSpyTimerService).setTotalTime(anyLong(),anyLong(),anyInt());
        inOrder.verify(mSpyTimerService).pauseTimer(0);
    }



    @Test
    public void testRegisterTimer0() throws NoSuchFieldException, IllegalAccessException {
        int[] sizes = new int[]{0,1};
        //mTimeObjMap.size() == 0
        HashMap mockMap1 = Mockito.mock(HashMap.class);
        when(mockMap1.size()).thenReturn(sizes[0]);
        ReflectUtil.setFieldValue(TimerService.class, "mTimeObjMap", mSpyTimerService, mockMap1);
        mSpyTimerService.registerTimer0();
        verify(mockMap1).put(eq(0), ArgumentMatchers.any(TimerService.TimeObj.class));
        mSpyTimerService.registerTimer0();

        //mTimeObjMap.size() == 1
        HashMap mockMap2 = Mockito.mock(HashMap.class);
        when(mockMap2.size()).thenReturn(sizes[1]);
        ReflectUtil.setFieldValue(TimerService.class, "mTimeObjMap", mSpyTimerService, mockMap2);
        mSpyTimerService.registerTimer0();
        verify(mockMap2,never()).put(eq(0), ArgumentMatchers.any(TimerService.TimeObj.class));
    }


    @Test
    public void testRemoveTimer(){
        mSpyTimerService.registerTimer0();
        //verify that TimeObj that index=0 can not be remove
        //by TimerService#removeTimer()
        assertTrue(mSpyTimerService.hasTimeObj(0));
        mSpyTimerService.removeTimer(0);
        assertTrue(mSpyTimerService.hasTimeObj(0));
        mSpyTimerService.registerTimer();
        assertTrue(mSpyTimerService.hasTimeObj(1));
        mSpyTimerService.removeTimer(1);
        //verify that TimeObj removed by TimerService#removeTimer()
        // is not within TimerService#mTimeObjMap
        assertFalse(mSpyTimerService.hasTimeObj(1));
    }


    @Test
    public void testRecordTimerStatus() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        mSpyTimerService.registerTimer0();
        assertTrue(mSpyTimerService.hasTimeObj(0));
        SharedPreferences preferences = mSpyTimerService.getSharedPreferences(AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
                Context.MODE_PRIVATE);
        //invoke recordTimerStatus()
        Method method = TimerService.class.getDeclaredMethod("recordTimerStatus",boolean.class,boolean.class,int.class);
        method.setAccessible(true);

        boolean isStart = true;
        boolean isPause = false;
        int index = 0;
        method.invoke(mSpyTimerService,isStart,isPause,index);
        long value = preferences.getInt(TimerService.TIMER_STATUS_PREFERENCE,-1);
        assertNotEquals(value,STATUS_START);

        isStart = false;
        isPause = true;
        index = 0;
        method.invoke(mSpyTimerService,isStart,isPause,index);
        value = preferences.getInt(TimerService.TIMER_STATUS_PREFERENCE,-1);
        assertNotEquals(value,STATUS_PAUSE);

        isStart = false;
        isPause = false;
        index = 0;
        method.invoke(mSpyTimerService,isStart,isPause,index);
        value = preferences.getInt(TimerService.TIMER_STATUS_PREFERENCE,-1);
        assertNotEquals(value,STATUS_PREPARE);
    }

}

