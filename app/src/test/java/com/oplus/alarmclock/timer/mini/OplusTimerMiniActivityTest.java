package com.oplus.alarmclock.timer.mini;

import static org.mockito.Mockito.verify;

import android.app.Activity;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Message;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmRingForOther;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.TimerService;

import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;

public class OplusTimerMiniActivityTest extends TestParent {

    OplusTimerMiniActivity mActivity;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        Intent intent = new Intent();
        intent.putExtra(AlarmRingForOther.RING_URI, "uriString");
        mActivity = Robolectric.buildActivity(OplusTimerMiniActivity.class, intent).create().get();
    }

    @Test
    public void should_verify_when_handleMessage() {
        OplusTimerMiniActivity spyActivity = Mockito.spy(mActivity);
        Message msg = Mockito.mock(Message.class);
        spyActivity.handleMessage(msg);
        verify(spyActivity).handleMessage(msg);
    }

    @Test
    public void should_verify_when_bindService() throws IllegalAccessException, NoSuchFieldException {
        OplusTimerMiniActivity spyActivity = Mockito.spy(mActivity);
        ReflectUtil.setFieldValue(OplusTimerMiniActivity.class, "mServiceConnect", mActivity, Mockito.mock(ServiceConnection.class));
        spyActivity.bindService();
        verify(spyActivity).bindService();
    }

    @Test
    public void should_verify_when_unBindService() throws IllegalAccessException, NoSuchFieldException {
        OplusTimerMiniActivity spyActivity = Mockito.spy(mActivity);
        ReflectUtil.setFieldValue(OplusTimerMiniActivity.class, "mServiceConnect", mActivity, Mockito.mock(ServiceConnection.class));
        ReflectUtil.setFieldValue(OplusTimerMiniActivity.class, "mHasBindService", mActivity, false);
        ReflectUtil.setFieldValue(OplusTimerMiniActivity.class, "mService", mActivity, Mockito.mock(TimerService.class));
        spyActivity.unBindService();
        verify(spyActivity).unBindService();
    }
}
