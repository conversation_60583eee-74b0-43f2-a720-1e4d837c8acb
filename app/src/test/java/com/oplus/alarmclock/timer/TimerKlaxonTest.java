/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-23, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer;

import android.media.MediaPlayer;
import android.os.Vibrator;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

public class TimerKlaxonTest extends TestParent {
    TimerKlaxon timerKlaxon;
    @Override
    public void setUp() throws Exception {
        super.setUp();
        timerKlaxon = new TimerKlaxon();
    }

    @Test
    @Ignore
    public void should_stop_mediaPlayer_and_cancel_vibrator_when_stop_with_mPlaying_is_true() throws NoSuchFieldException, IllegalAccessException {
        //init timerKlaxon
        MediaPlayer mockMediaPlayer = Mockito.mock(MediaPlayer.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mMediaPlayer", timerKlaxon, mockMediaPlayer);
        Vibrator mockVibrator = Mockito.mock(Vibrator.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mVibrator", timerKlaxon, mockVibrator);
        boolean isPlaying = true;
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mPlaying", timerKlaxon, isPlaying);
        //invoke stop()
        timerKlaxon.stop();

        //verify && assert
        boolean actualIsPlaying = (boolean) ReflectUtil.getFieldValue(TimerKlaxon.class, "mPlaying", timerKlaxon);
        boolean expectedIsPlaying = false;
        Assert.assertEquals(expectedIsPlaying, actualIsPlaying);
        InOrder inorder = Mockito.inOrder(mockMediaPlayer, mockMediaPlayer, mockVibrator);
        inorder.verify(mockMediaPlayer).stop();
        inorder.verify(mockMediaPlayer).release();
        inorder.verify(mockVibrator).cancel();
    }


    @Test
    @Ignore
    public void should_stop_mediaPlayer_and_cancel_vibrator_when_stop_with_mPlaying_is_false() throws NoSuchFieldException, IllegalAccessException {
        //init timerKlaxon
        MediaPlayer mockMediaPlayer = Mockito.mock(MediaPlayer.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mMediaPlayer", timerKlaxon, mockMediaPlayer);
        Vibrator mockVibrator = Mockito.mock(Vibrator.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mVibrator", timerKlaxon, mockVibrator);
        boolean isPlaying = false;
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mPlaying", timerKlaxon, isPlaying);
        //invoke stop()
        timerKlaxon.stop();

        //verify && assert
        verify(mockMediaPlayer, never()).stop();
        verify(mockMediaPlayer, never()).release();
        verify(mockVibrator, never()).cancel();
    }

    @Test
    @Ignore
    public void should_set_mPlaying_false_when_stop_with_mPlaying_is_true() throws NoSuchFieldException, IllegalAccessException {
        MediaPlayer mockMediaPlayer = Mockito.mock(MediaPlayer.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mMediaPlayer", timerKlaxon, mockMediaPlayer);
        Vibrator mockVibrator = Mockito.mock(Vibrator.class);
        ReflectUtil.setFieldValue(TimerKlaxon.class, "mVibrator", timerKlaxon, mockVibrator);

        ReflectUtil.setFieldValue(TimerKlaxon.class, "mPlaying", timerKlaxon, true);
        timerKlaxon.stop();

        boolean isPlaying = (boolean) ReflectUtil.getFieldValue(TimerKlaxon.class, "mPlaying", timerKlaxon);
        Assert.assertFalse(isPlaying);
    }

}
