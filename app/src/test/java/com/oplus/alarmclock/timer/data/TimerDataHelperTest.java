/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-31, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.timer.data;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.SystemClock;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowContextNative;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;
import java.util.Random;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {ShadowContextNative.class})
public class TimerDataHelperTest extends TestParent {
    static int sMockListSize;
    final int[] SIZES = new int[]{0, 1, 4, 6, 7, 10};
    //timers.xml
    final int TIMER_NUM_IN_XML = 3;
    final int DURATION = 10;
    final int FLAG = 1;
    final int SELECTED = 1;
    final String DESCRIPTION = "test";
    final int UNSELECTED = 0;
    final String RINGURI = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM).toString();
    final String RINGNAME = "ringName";

    @Override
    public void setUp() throws Exception {
        super.setUp();
    }

    @Config(shadows = {ShadowTimerDataHelper.class})
    @Test
    public void testGetAllTimers() {
        for (int i = 0; i < SIZES.length; i++) {
            sMockListSize = SIZES[i];
            ArrayList mockList = TimerDataHelper.getAllTimers(mContext);
            verify(mockList, never()).add(ArgumentMatchers.any(OplusTimer.class));
        }
    }

    @Test
    @Ignore
    public void testGetTimersInDB() {
        mContext = spy(mContext);
        ContentResolver contentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(contentResolver);
        final int cursorSize = 3;
        Answer cursorAnswer = new Answer() {
            int size = cursorSize;
            int pos = -1;

            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String method = invocation.getMethod().getName();
                switch (method) {
                    case "moveToFirst":
                        if (size > 0) {
                            pos = 0;
                            return true;
                        } else {
                            return false;
                        }
                    case "moveToNext":
                        if (++pos < size) {
                            return true;
                        } else {
                            return false;
                        }
                }
                return null;
            }
        };
        Cursor cursor = mock(Cursor.class, cursorAnswer);
        when(contentResolver.query(eq(ClockContract.TIMER_CONTENT_URI), (String[]) isNull(),
                (String) isNull(), (String[]) isNull(), (String) isNull())).thenReturn(cursor);

        //invoke getTimersInDB()
        List list = TimerDataHelper.getTimersInDB(mContext);
        //assert
        assertEquals(cursorSize, list.size());
    }


    @Test
    public void testGetTimerById_WithIdExist() {
        ContentValues values = new ContentValues();
        values.put(ClockContract.TimerTableColumns.DURATION, DURATION);
        values.put(ClockContract.TimerTableColumns.DESCRIPTION, DESCRIPTION);
        values.put(ClockContract.TimerTableColumns.FLAG, FLAG);
        values.put(ClockContract.TimerTableColumns.SELECTED, SELECTED);
        values.put(ClockContract.TimerTableColumns.RING, RINGURI);
        values.put(ClockContract.TimerTableColumns.RINGNAME, RINGNAME);
        Uri uri = mContext.getContentResolver().insert(ClockContract.TIMER_CONTENT_URI, values);
        int id = Integer.valueOf(uri.getLastPathSegment());
        OplusTimer timer = TimerDataHelper.getTimerById(mContext, id);
        assertNotNull(timer);
        Assert.assertEquals(DURATION, timer.getDuration());
        Assert.assertEquals(DESCRIPTION, timer.getDescription());
        Assert.assertEquals(FLAG, timer.getFlag());
        Assert.assertEquals(SELECTED, timer.getSelected());
        Assert.assertEquals(RINGURI, timer.getRing());
        Assert.assertEquals(RINGNAME, timer.getRingName());
    }

    @Test
    public void testGetTimerById_WithIdNotExist() {
        Cursor cursor = mContext.getContentResolver().query(ClockContract.TIMER_CONTENT_URI, null, null, null, null);
        System.out.println(cursor.getCount());
        BitSet bitSet = new BitSet(16);
        Random rand = new Random();

        cursor.moveToFirst();
        if (cursor.getCount() > 0) {
            do {
                bitSet.set(cursor.getInt(cursor.getColumnIndex(ClockContract.TimerTableColumns._ID)));
            } while (cursor.moveToNext());
        }
        //obtain an id that not in database
        int bound = 100;
        int id = rand.nextInt(bound);
        while (bitSet.get(id)) {
            id = rand.nextInt(bound);
        }
        OplusTimer timer = TimerDataHelper.getTimerById(mContext, id);
        assertNull(timer);
    }


    @Test
    public void testSaveTimerSelectedStateInDB() {
        ContentValues values = new ContentValues();
        values.put(ClockContract.TimerTableColumns.DURATION, DURATION);
        values.put(ClockContract.TimerTableColumns.DESCRIPTION, DESCRIPTION);
        values.put(ClockContract.TimerTableColumns.FLAG, FLAG);
        values.put(ClockContract.TimerTableColumns.SELECTED, UNSELECTED);
        values.put(ClockContract.TimerTableColumns.RING, RINGURI);
        values.put(ClockContract.TimerTableColumns.RINGNAME, RINGNAME);
        int[] indexs = new int[2];
        ArrayList<OplusTimer> list = new ArrayList(indexs.length);
        for (int i = 0; i < indexs.length; i++) {
            //insert unselected timers
            Uri uri = mContext.getContentResolver().insert(ClockContract.TIMER_CONTENT_URI, values);
            indexs[i] = Integer.valueOf(uri.getLastPathSegment());
            //create selected timers
            OplusTimer timer = new OplusTimer();
            timer.setTimerIndex(indexs[i]);
            timer.setSelected(SELECTED);
            list.add(timer);
        }
        //update select value
        TimerDataHelper.saveTimerSelectedStateInDB(mContext, list);
        SystemClock.sleep(200);
        Cursor cursor = mContext.getContentResolver().query(
                ClockContract.TIMER_CONTENT_URI, null,
                ClockContract.TimerTableColumns._ID + "=" + indexs[0] + " or " +
                        ClockContract.TimerTableColumns._ID + "=" + indexs[1],
                null, null);
        Assert.assertEquals(list.size(), cursor.getCount());
    }


    @Implements(TimerDataHelper.class)
    public static class ShadowTimerDataHelper {


        public static ArrayList<OplusTimer> getTimersInDB(Context context) {
            ArrayList<OplusTimer> mockList = mock(ArrayList.class);
            when(mockList.size()).thenReturn(sMockListSize);
            return mockList;
        }

    }

    @Test
    public void testGetSeletedTimer() {
        ArrayList<OplusTimer> list = new ArrayList(4);
        OplusTimer timer = Mockito.mock(OplusTimer.class);
        doReturn(0, 2, SELECTED, 3).when(timer).getSelected();
        for (int i = 0; i < 4; i++) {
            list.add(timer);
        }
        assertNotNull(TimerDataHelper.getSeletedTimer(list));
    }

}
