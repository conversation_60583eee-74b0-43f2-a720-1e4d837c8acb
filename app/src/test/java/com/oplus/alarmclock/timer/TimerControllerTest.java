package com.oplus.alarmclock.timer;

import static org.mockito.Mockito.verify;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.timer.ui.TimerController;

import org.junit.Test;
import org.mockito.Mockito;

public class TimerControllerTest extends TestParent {

    @Test
    public void should_verify_when_onResume() {
        TimerController timerController = Mockito.mock(TimerController.class);
        timerController.onResume();
        verify(timerController).onResume();
    }

    @Test
    public void should_verify_when_refreshLayout() {
        TimerController timerController = Mockito.mock(TimerController.class);
        timerController.refreshLayout();
        verify(timerController).refreshLayout();
    }
}
