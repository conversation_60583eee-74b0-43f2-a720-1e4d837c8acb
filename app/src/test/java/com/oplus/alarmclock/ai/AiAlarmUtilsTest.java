/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AiAlarmUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/11/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.ai;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;

import com.heytap.addon.media.MediaFile;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;

import org.junit.Test;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class AiAlarmUtilsTest extends TestParent {

    @Test
    public void should_return_expectedRepeatSet_when_getDaysFromIntent_with_specific_ArrayList() throws NoSuchMethodException, IllegalAccessException {
        Random rand = new Random();
        int repeatSetCount = 4;
        int repeat = 5;
        for (int j = 0; j < repeat; j++) {
            ArrayList<Integer> daysArray = new ArrayList(repeatSetCount);
            int sum = 0;
            for (int i = 0; i < repeatSetCount; i++) {
                //[1-7]
                int dayOfWeek = rand.nextInt(7) + 1;
                if (!daysArray.contains(dayOfWeek)) {
                    daysArray.add(dayOfWeek);
                    if (dayOfWeek == 1) {
                        dayOfWeek += 5;
                    } else {
                        dayOfWeek -= 2;
                    }
                    sum = sum | 1 << dayOfWeek;
                }
            }
            //invoke getDaysFromIntent()
            int repeatSet = (int) ReflectUtil.invoke(AiAlarmUtils.class, "getDaysFromIntent",
                    new Object[]{daysArray}, null, ArrayList.class);
            //assert
            assertEquals(sum, repeatSet);
        }
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_get_alarmCount_correct_when_getAlarmsCount(){
        ShadowAlarmUtils.sAlarmList = null;
        int alarmCount = AiAlarmUtils.getAlarmsCount(mContext);
        assertEquals(0, alarmCount);

        int count = 10;
        ArrayList<Alarm> alarms = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            Alarm alarm = new Alarm();
            alarms.add(alarm);
        }
        ShadowAlarmUtils.sAlarmList = alarms;
        alarmCount = AiAlarmUtils.getAlarmsCount(mContext);
        assertEquals(count, alarmCount);
    }

    @Config(shadows = {ShadowMediaFile.class})
    @Test
    public void should_get_alarm_correct_when_handleSetAlarm_with_bundle_is_null_or_not(){
        Bundle bundle = null;
        Alarm alarm = AiAlarmUtils.handleSetAlarm(mContext, bundle);
        assertNull(alarm);

        bundle = new Bundle();
        bundle.putInt(AlarmClock.EXTRA_HOUR, 10);
        bundle.putInt(AlarmClock.EXTRA_MINUTES, 30);

        alarm = AiAlarmUtils.handleSetAlarm(mContext, bundle);
        assertNotNull(alarm);
        assertEquals(10, alarm.getHour());
        assertEquals(30, alarm.getMinutes());
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_get_alarmList_when_getAlarms_with_target_hour_and_minute() {
        final int alarmCount = 10;
        final int targetCount = 2;
        final int hour = 10;
        final int minute = 30;
        int tempCount = targetCount;
        ArrayList<Alarm> alarmList = new ArrayList<>();
        for (int i = 0; i < alarmCount; i++) {
            Alarm alarm = new Alarm();
            if (tempCount > 0) {
                alarm.setHour(hour);
                alarm.setMinutes(minute);
                tempCount--;
            }
            alarmList.add(alarm);
        }
        ShadowAlarmUtils.sAlarmList = alarmList;
        List<Alarm> result = AiAlarmUtils.getAlarms(mContext, hour, minute);
        assertEquals(targetCount, result.size());
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_selectedAlarm_correct_when_getSectionAlarm_with_alarmList_is_null() {
        ShadowAlarmUtils.sAlarmList = new ArrayList<>();
        List<Alarm> selectedAlarm = AiAlarmUtils.getSectionAlarm(mContext, 0, 0, 0, 0, 0, 0);
        assertNull(selectedAlarm);
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_selectedAlarm_correct_when_getSectionAlarm_with_hour_and_minute() {
        int startHour = 10;
        int startMinute = 0;
        int endHour = 18;
        int endMinute = 0;

        int totalAlarmCount = 10;
        ArrayList<Alarm> totalAlarmList = new ArrayList<>();
        for (int i = 0; i < totalAlarmCount; i++) {
            Alarm alarm = new Alarm();
            alarm.setHour(9);
            alarm.setMinutes(0);
            totalAlarmList.add(alarm);
        }
        ShadowAlarmUtils.sAlarmList = totalAlarmList;
        List<Alarm> selectedAlarm = AiAlarmUtils.getSectionAlarm(mContext, 0, 0, startHour, startMinute, endHour, endMinute);
        assertEquals(0, selectedAlarm.size());
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_selectedAlarm_correct_when_getSectionAlarm_with_alarm_time() {
        int startHour = -1;
        int startMinute = 0;
        int endHour = -1;
        int endMinute = 0;

        Calendar calendar = Calendar.getInstance();

        long nowTime = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        long nextTime = calendar.getTimeInMillis();

        int totalAlarmCount = 1;
        ArrayList<Alarm> totalAlarmList = new ArrayList<>();
        for (int i = 0; i < totalAlarmCount; i++) {
            Alarm alarm = new Alarm();
            alarm.setHour(9);
            alarm.setMinutes(0);
            totalAlarmList.add(alarm);
        }
        ShadowAlarmUtils.sAlarmList = totalAlarmList;
        List<Alarm> selectedAlarm = AiAlarmUtils.getSectionAlarm(mContext, nowTime, nextTime,
                startHour, startMinute, endHour, endMinute);
        assertEquals(1, selectedAlarm.size());
    }





    @Implements(AlarmUtils.class)
    private static class ShadowAlarmUtils{

        static ArrayList<Alarm> sAlarmList;

        @Implementation
        public static ArrayList<Alarm> getAllAlarms(Context context) {
            return sAlarmList;
        }
    }

    @Implements(MediaFile.class)
    public static class ShadowMediaFile{

        @Implementation
        public static Uri getDefaultAlarmUri(Context context) {
            return Uri.parse("content:://abc");
        }
    }

}