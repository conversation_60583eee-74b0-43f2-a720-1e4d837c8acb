/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-29, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.ai;

import android.content.ComponentName;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Looper;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.timer.TimerService;
import com.oplus.alarmclock.Morning.tools.MorningAlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowContextWrapper;
import java.util.ArrayList;


public class AiSupportContentProviderTest extends TestParent {
    AiSupportContentProvider mSpyProvider;
    @BeforeClass
    public static void classSetUp(){
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback,null,null,null);
    }
    @Override
    public void setUp() throws Exception {
        super.setUp();
        mSpyProvider = Robolectric.buildContentProvider(AiSupportContentProvider.class).create().get();
    }

    @Config(shadows = {ShadowAiAlarmUtils.class})
    @Test
    public void should_putInt_MAX_ALARM_COUNT_and_RESULT_ERROR_COUNT_LIMIT_when_call_with_METHOD_ADD_ALARM_and_getAlarmsCount_equals_to_MAX_ALARM_COUNT() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        String methodAddAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_ADD_ALARM", null);
        ShadowAiAlarmUtils.sAlarmCount = AlarmClockFragment.MAX_ALARM_COUNT;
        //invoke call()
        Bundle result = mSpyProvider.call(methodAddAlarm, null, mockBundle);
        //verify
        String extraAlarmLimit = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_ALARM_LIMIT", null);
        Assert.assertEquals(AlarmClockFragment.MAX_ALARM_COUNT, result.getInt(extraAlarmLimit, -1));
        String extraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        int resultErrorCountLimit = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_ERROR_COUNT_LIMIT", null);
        Assert.assertEquals(resultErrorCountLimit, result.getInt(extraResult, -1));
    }


    @Config(shadows = {ShadowAiAlarmUtils2.class})
    @Test
    public void should_putInt_RESULT_NO_ALARM_FOUND_when_call_with_METHOD_ENABLE_ALARM_and_alarmList_is_Empty() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour>0,min>0
        int hour = 10;
        int min = 10;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_ENABLE_ALARM", null);
        ShadowAiAlarmUtils2.sIsEmpty = true;
        //invoke call()
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //verify
        int expectedValue = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_NO_ALARM_FOUND", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedValue,result.getInt(keyExtraResult));
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_ENABLE_ALARM_and_alarmList_size_is_one_and_and_enableAlarm_success() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour>0,min>0
        int hour = 10;
        int min = 10;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_ENABLE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_CLOSE_ALARM_and_alarmList_size_is_one_and_and_disableAlarm_success() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour>0,min>0
        int hour = 10;
        int min = 10;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CLOSE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_ERROR_when_call_with_METHOD_DELETE_ALARM_and_alarmList_size_is_one_and_and_deleteAlarm_fail() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour>0,min>0
        int hour = 10;
        int min = 10;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_DELETE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_ERROR", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_CLOSE_ALARM_and_minute_hour_is_NegativeNum__and_alarmId_is_positiveNum() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour<0,min<0
        int hour = -1;
        int min = -1;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);

        String extraAlarmId = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_ALARM_ID", null);
        long alarmId = 1L;
        Mockito.when(mockBundle.getLong(extraAlarmId, -1)).thenReturn(alarmId);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CLOSE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_ERROR_when_call_with_METHOD_DELETE_ALARM_and_minute_hour_is_NegativeNum_and_alarmId_is_positiveNum() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour<0,min<0
        int hour = -1;
        int min = -1;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        //init alarmId
        long alarmId = 1L;
        String extraAlarmId = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_ALARM_ID", null);
        Mockito.when(mockBundle.getLong(extraAlarmId, -1)).thenReturn(alarmId);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_DELETE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_ERROR", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }


    @Ignore
    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_ENABLE_ALARM_and_minute_hour_is_NegativeNum_and_alarm_relative_to_alarmId_not_null() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure hour<0,min<0
        int hour = -1;
        int min = -1;
        String extraTimeHour = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_HOUR", null);
        Mockito.when(mockBundle.getInt(extraTimeHour, -1)).thenReturn(hour);
        String extraTimeMin = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_TIME_MIN", null);
        Mockito.when(mockBundle.getInt(extraTimeMin, -1)).thenReturn(min);
        //init alarmId
        long alarmId = 1L;
        String extraAlarmId = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_ALARM_ID", null);
        Mockito.when(mockBundle.getLong(extraAlarmId, -1)).thenReturn(alarmId);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_ENABLE_ALARM", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }

    @Config(shadows = {ShadowAiAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_CLOSE_ALL_ALARMS_and_alarmCount_is_positive_integer() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure getAlarmsCount() return a positive number
        ShadowAiAlarmUtils.sAlarmCount = 1;
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CLOSE_ALL_ALARMS", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }

    @Config(shadows = {ShadowAiAlarmUtils.class})
    @Test
    public void should_putInt_RESULT_SUCCESS_when_call_with_METHOD_CLOSE_ALL_ALARMS_and_alarmCount_is_zero() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure getAlarmsCount() return a positive number
        ShadowAiAlarmUtils.sAlarmCount = 0;
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CLOSE_ALL_ALARMS", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_NO_ALARM_FOUND", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }


    @Ignore
    @Test
    public void should_start_TimerService_when_call_with_METHOD_CHECK_TIMER_and_mTimerService_is_null() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //set mTimerService null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, null);
        //Robolectric will provide null IBinder when call ServiceConnection#onServiceConnected() which cause NullPointException
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerServiceConnection", mSpyProvider, Mockito.mock(ServiceConnection.class));
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CHECK_TIMER", null);
        mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert
        Context context = (Context) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "mContext", mSpyProvider);
        ShadowContextWrapper shadowContextWrapper = Shadows.shadowOf(new ContextWrapper(context));
        Intent startServiceIntent = shadowContextWrapper.getNextStartedService();
        String actualServiceClassName = startServiceIntent.getComponent().getClassName();
        Assert.assertEquals(TimerService.class.getName(), actualServiceClassName);
    }


    @Test
    public void should_putInt_RESULT_TIMER_IS_RUNNING_when_call_with_METHOD_CHECK_TIMER_and_mTimerService_not_null_and_default_timer_isStart() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //set mTimerService not null
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        boolean hasTimeObj = true;
        Mockito.when(mockTimerService.hasTimeObj(ArgumentMatchers.anyInt())).thenReturn(hasTimeObj);
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(ArgumentMatchers.anyInt())).thenReturn(isStart);
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CHECK_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_TIMER_IS_RUNNING", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }

    @Test
    public void should_call_registerTimer0_and_putInt_RESULT_SUCCESS_when_call_with_METHOD_CHECK_TIMER_and_mTimerService_not_null_and_no_default_timer() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //set mTimerService not null
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        boolean hasTimeObj = false;
        Mockito.when(mockTimerService.hasTimeObj(ArgumentMatchers.anyInt())).thenReturn(hasTimeObj);
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CHECK_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);

        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        //assert&verify
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Mockito.verify(mockTimerService).registerTimer0();
    }


    @Test
    public void should_call_stopTimer_and_stopTimerAlert_and_start_AlarmClock_when_call_with_METHOD_START_TIMER_and_seconds_is_positive_num_and_mTimerService_not_null_and_default_timer_started() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        //ensure seconds>0
        int seconds = 10;
        Mockito.when(mockBundle.getInt(AlarmClock.EXTRA_LENGTH, 0)).thenReturn(seconds);
        //set override true
        boolean override = true;
        String extraOverriderCurrentTimer = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_OVERRIDE_CURRENT_TIMER", null);
        Mockito.when(mockBundle.getBoolean(extraOverriderCurrentTimer, false)).thenReturn(override);

        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        //set mTimerService not null
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        boolean hasTimeObj = true;
        Mockito.when(mockTimerService.hasTimeObj(defaultTimerIndex)).thenReturn(hasTimeObj);
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_START_TIMER", null);
        mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //verify
        Mockito.verify(mockTimerService).stopTimer(defaultTimerIndex);
        Mockito.verify(mockTimerService).stopTimerAlert(defaultTimerIndex);
    }


    @Test
    public void should_putInt_RESULT_SUCCESS_and_call_pauseTimer_when_call_with_METHOD_PAUSE_TIMER_and_mTimerService_not_null_and_defaultTimer_started_and_paused() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        //set mTimerService not null
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        //set isStart true
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        //set isPause false
        boolean isPause = false;
        Mockito.when(mockTimerService.isPause(defaultTimerIndex)).thenReturn(isPause);
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_PAUSE_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Mockito.verify(mockTimerService).pauseTimer(defaultTimerIndex);
    }



    @Test
    public void should_putInt_RESULT_TIMER_ALREADY_IN_STATE_when_call_with_METHOD_PAUSE_TIMER_and_mTimerService_not_null_and_defaultTimer_paused() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        //set isPause true
        boolean isPause = true;
        Mockito.when(mockTimerService.isPause(defaultTimerIndex)).thenReturn(isPause);
        //set mTimerService not null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_PAUSE_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_TIMER_ALREADY_IN_STATE", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Mockito.verify(mockTimerService, Mockito.never()).pauseTimer(defaultTimerIndex);
    }

    @Ignore
    @Test
    public void should_start_TimerService_call_pauseTimer_when_call_with_METHOD_PAUSE_TIMER_and_mTimerService_is_null_and_defaultTimer_started_and_paused() throws NoSuchFieldException, IllegalAccessException {
        final TimerService mockTimerService = Mockito.mock(TimerService.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        //set isStart true
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        //set isPause = false;
        boolean isPause = false;
        Mockito.when(mockTimerService.isPause(defaultTimerIndex)).thenReturn(isPause);
        Bundle mockBundle = Mockito.mock(Bundle.class);
        ServiceConnection timerServiceConnection = new ServiceConnection() {
            public void onServiceConnected(ComponentName name, IBinder service) {
                try {
                    ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                            "mTimerService", mSpyProvider, mockTimerService);
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            }

            public void onServiceDisconnected(ComponentName name) {
            }
        };
        //Robolectric will provide null IBinder when call ServiceConnection#onServiceConnected() which cause NullPointException
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerServiceConnection", mSpyProvider, timerServiceConnection);
        //set mTimerService null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, null);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_PAUSE_TIMER", null);
        mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        Context context = (Context) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "mContext", mSpyProvider);
        ShadowContextWrapper shadowContextWrapper = Shadows.shadowOf(new ContextWrapper(context));
        Intent startServiceIntent = shadowContextWrapper.getNextStartedService();
        String actualServiceClassName = startServiceIntent.getComponent().getClassName();
        Assert.assertEquals(TimerService.class.getName(), actualServiceClassName);

        Shadows.shadowOf(Looper.getMainLooper()).idle();
        Mockito.verify(mockTimerService).pauseTimer(defaultTimerIndex);
    }

    @Test
    public void should_call_startTimer_and_putInt_RESULT_SUCCESS_when_call_with_METHOD_RESUME_TIMER_and_mTimerService_not_null_and_defaultTimer_paused() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        //set isPause true
        boolean isPause = true;
        Mockito.when(mockTimerService.isPause(defaultTimerIndex)).thenReturn(isPause);
        //set mTimerService not null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_RESUME_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Mockito.verify(mockTimerService).startTimer(defaultTimerIndex);
    }

    @Test
    public void should_putInt_RESULT_TIMER_ALREADY_IN_STATE_when_call_with_METHOD_RESUME_TIMER_and_mTimerService_not_null_and_defaultTimer_started_and_paused() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        //set isPause false
        boolean isPause = false;
        Mockito.when(mockTimerService.isPause(defaultTimerIndex)).thenReturn(isPause);
        //set isStart true
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        //set mTimerService not null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_RESUME_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_TIMER_ALREADY_IN_STATE", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
    }

    @Test
    public void should_call_stopTimer_and_stopTimerAlert_and_putInt_RESULT_SUCCESS_when_call_with_METHOD_CANCEL_TIMER_and_mTimerService_not_null_and_defaultTimer_started() throws NoSuchFieldException, IllegalAccessException {
        Bundle mockBundle = Mockito.mock(Bundle.class);
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        TimerService mockTimerService = Mockito.mock(TimerService.class);
        //set isStart true
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        //set mTimerService not null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, mockTimerService);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CANCEL_TIMER", null);
        Bundle result = mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Mockito.verify(mockTimerService).stopTimer(defaultTimerIndex);
        Mockito.verify(mockTimerService).stopTimerAlert(defaultTimerIndex);
    }

    @Ignore
    @Test
    public void should_start_TimerService_call_stopTimer_stopTimerAlert_when_call_with_METHOD_CANCEL_TIMER_and_mTimerService_is_null_and_defaultTimer_started() throws NoSuchFieldException, IllegalAccessException {
        final TimerService mockTimerService = Mockito.mock(TimerService.class);
        Bundle mockBundle = Mockito.mock(Bundle.class);
        ServiceConnection timerServiceConnection = new ServiceConnection() {
                    public void onServiceConnected(ComponentName name, IBinder service) {
                        try {
                            ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                                    "mTimerService", mSpyProvider, mockTimerService);
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        }
            }

            public void onServiceDisconnected(ComponentName name) {
            }
        };
        int defaultTimerIndex = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "DEFAULT_TIMER_INDEX", null);
        //Robolectric will provide null IBinder when call ServiceConnection#onServiceConnected() which cause NullPointException
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerServiceConnection", mSpyProvider, timerServiceConnection);
        //set mTimerService null
        ReflectUtil.setFieldValue(AiSupportContentProvider.class,
                "mTimerService", mSpyProvider, null);
        //set isStart true
        boolean isStart = true;
        Mockito.when(mockTimerService.isStart(defaultTimerIndex)).thenReturn(isStart);
        //invoke call()
        String methodEnableAlarm = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_CANCEL_TIMER", null);
        mSpyProvider.call(methodEnableAlarm, null, mockBundle);
        //assert&verify
        Context context = (Context) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "mContext", mSpyProvider);
        ShadowContextWrapper shadowContextWrapper = Shadows.shadowOf(new ContextWrapper(context));
        Intent startServiceIntent = shadowContextWrapper.getNextStartedService();
        String actualServiceClassName = startServiceIntent.getComponent().getClassName();
        Assert.assertEquals(TimerService.class.getName(), actualServiceClassName);

        Mockito.verify(mockTimerService).stopTimer(defaultTimerIndex);
        Mockito.verify(mockTimerService).stopTimerAlert(defaultTimerIndex);
    }

    @Test
    public void should_get_morning_switch_status_when_call_with_METHOD_GET_MORNING_SWITCH() throws NoSuchFieldException, IllegalAccessException {
        //invoke call()
        String methodGetMorningSwitch = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "METHOD_GET_MORNING_SWITCH", null);
        Bundle result = mSpyProvider.call(methodGetMorningSwitch, null, new Bundle());
        //assert&verify
        int expectedResult = (int) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "RESULT_SUCCESS", null);
        String keyExtraResult = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_RESULT", null);
        String keyExtraMorningStatus = (String) ReflectUtil.getFieldValue(AiSupportContentProvider.class,
                "EXTRA_MORNING_SWITCH", null);

        SharedPreferences pre = mContext.getSharedPreferences(MorningAlarmClock.MORNING_PREFERENCE, Context.MODE_PRIVATE);
        boolean expectedSwitchStatus = pre.getBoolean(MorningAlarmClock.MORNING_STATE, false);

        Assert.assertEquals(expectedResult, result.getInt(keyExtraResult));
        Assert.assertEquals(expectedSwitchStatus, result.getBoolean(keyExtraMorningStatus));
    }



    @Implements(AiAlarmUtils.class)
    public static class ShadowAiAlarmUtils{
         static int sAlarmCount;
         @Implementation
         public static int getAlarmsCount(Context context) {
            return sAlarmCount;
        }
    }

    @Implements(AiAlarmUtils.class)
    public static class ShadowAiAlarmUtils2{
        static boolean sIsEmpty;
        static int sSize;
        public static ArrayList<Alarm> getAllAlarms(Context context) {
            ArrayList mockList = Mockito.mock(ArrayList.class);
            Mockito.when(mockList.isEmpty()).thenReturn(sIsEmpty);
            Mockito.when(mockList.size()).thenReturn(sSize);
            return Mockito.mock(ArrayList.class);
        }

        public static boolean enableAlarm(Context context, Alarm alarm) {
            return true;
        }
    }


    @Implements(AlarmUtils.class)
    public static class ShadowAlarmUtils{
        public static ArrayList<Alarm> getAllAlarms(Context context) {
            ArrayList<Alarm>  list = new ArrayList(1);
            Alarm alarm = new Alarm();
            int hour = 10;
            int minute = 10;
            alarm.setMinutes(minute);
            alarm.setHour(hour);
            list.add(alarm);
            return list;
        }

        public static boolean enableAlarm(Context context, Alarm alarm, boolean isNeedToUpdate) {
            return true;
        }

        public static boolean disableAlarm(Context context, long alarmId, boolean disableAllAlarm) {
            return true;
        }

        public static boolean deleteAlarm(Context context, long alarmId, boolean deleteAllAlarm) {
            return false;
        }

        public static Alarm getAlarm(Context context, long alarmId) {
            return new Alarm();
        }
    }

}
