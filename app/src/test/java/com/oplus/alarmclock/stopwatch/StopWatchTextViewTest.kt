/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchListManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import com.oplus.alarmclock.TestParent
import org.junit.Test

class StopWatchTextViewTest : TestParent() {

    @Test
    fun test_stop_watch_text_view() {
        val view = StopWatchTextView(mContext)
        view.run {
            update(1000)
            getTime()
            getMarginTop()
            setTextSize(1F)
        }
    }
}