/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-19, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.stopwatch;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Random;

public class OplusStopWatchTest extends TestParent {
    private final int MIN_IN_HOUR = 60;
    private final int SEC_IN_MIN = 60;
    private final int HOURS_OF_WHOLE_DAY = 24;
    private final int SEC_IN_MILLISEC = 1000;
    private final long MIN_IN_MILLISEC = SEC_IN_MIN * SEC_IN_MILLISEC;
    private final long HOUR_IN_MILLISEC = MIN_IN_HOUR * MIN_IN_MILLISEC;
    private final long DAY_IN_MILLISEC =HOURS_OF_WHOLE_DAY * HOUR_IN_MILLISEC;
    @Override
    public void setUp() throws Exception {
        super.setUp();
    }

    @Test
    public void should_returnCorrectTimeString_when_getTalkBackTime_with_specificEscapeTime() throws NoSuchFieldException, IllegalAccessException {
        Random rand = new Random();
        NumberFormat numberFormat = NumberFormat.getInstance();
        DecimalFormat decimalFormat = new DecimalFormat();
        decimalFormat.setRoundingMode(RoundingMode.FLOOR);
        int maxFractionDigits = 2;
        int minFractionDigits = 2;
        decimalFormat.setMaximumFractionDigits(maxFractionDigits);
        decimalFormat.setMinimumFractionDigits(minFractionDigits);

        int repeat = 50;
        for(int i=0; i<repeat; i++){
            //calculate elapseTime
            //ensure that the values are bigger than 0
            int day = rand.nextInt(100-1) + 1;
            int hour = rand.nextInt(HOURS_OF_WHOLE_DAY-1) + 1;
            int min = rand.nextInt(MIN_IN_HOUR-1) + 1;
            long sec = rand.nextInt(SEC_IN_MIN-1) + 1;
            float secFractionPart =  rand.nextFloat();
            //ensure that secFractionPart is not 0 after decimal.format()
            float minimum = 0.01f;
            while(secFractionPart < minimum){
                secFractionPart =  rand.nextFloat();
            }
//            long elapseTime = day*24L*60L*60L*1000L + hour*60L*60L*1000L + min*60L*1000L + sec*1000L + (long)(secFractionPart*1000f);
            long elapseTime = day*DAY_IN_MILLISEC + hour*HOUR_IN_MILLISEC + min*MIN_IN_MILLISEC + sec*SEC_IN_MILLISEC + (long)(secFractionPart*SEC_IN_MILLISEC);
            OplusStopWatch stopWatch = new OplusStopWatch(mContext);
            //init elapseTime
            stopWatch.setElapseTime(elapseTime);
            //invoke getTalkBackTime()
            String actualStr = stopWatch.getTalkBackTime();

            //build expected string
            String dayStr = getQuantityString(R.plurals.days_short, day, numberFormat);
            String hourStr = getQuantityString(R.plurals.hours_short, hour, numberFormat);
            String minStr = getQuantityString(R.plurals.minutes_plurals, min, numberFormat);
            String secStr = decimalFormat.format((double) sec + (double) secFractionPart)
                    + mContext.getString(R.string.timer_sec);
            StringBuilder strBuilder = new StringBuilder(32);
            strBuilder.append(dayStr).append(hourStr).append(minStr).append(secStr);
            String expectedStr = strBuilder.toString();

            //assert equals
            Assert.assertEquals(expectedStr,actualStr);
            System.out.println(strBuilder.toString());
            System.out.println("");
        }
    }

    private String getQuantityString(int id, int quantity,NumberFormat format) {
        final String localizedQuantity = format.format(quantity);
        return mContext.getResources().getQuantityString(id, quantity, localizedQuantity);
    }


}
