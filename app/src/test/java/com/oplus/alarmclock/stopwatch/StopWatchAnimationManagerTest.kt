/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchAnimationManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import android.animation.Animator
import android.view.View
import android.view.ViewGroup
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import org.junit.Test

@Suppress("Filename")
class StopWatchAnimationManagerTest : TestParent() {
    @Test
    @Suppress("LongMethod", "ArrayPrimitive")
    fun test_start_dial_animation() {
        val manager = StopWatchAnimationManager()
        val dial = View(mContext)
        val cancel = View(mContext)
        val rv = View(mContext)
        val argsArr = arrayOf(rv, true)
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startRvAnimation",
                argsArr,
                manager,
                View::class.java,
                Boolean::class.java
        )
        argsArr[1] = false
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startRvAnimation",
                argsArr,
                manager,
                View::class.java,
                Boolean::class.java
        )
        val animationArr = arrayOf(dial, true, true)
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startDialAnimation",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )
        animationArr[1] = false
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startDialAnimation",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )
        animationArr[2] = false
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startDialAnimation",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )
        animationArr[1] = true
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "startDialAnimation",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )
        animationArr[1] = true
        animationArr[2] = false
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "toScale",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )

        animationArr[1] = true
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "moveScale",
                animationArr,
                manager,
                View::class.java,
                Boolean::class.java,
                Boolean::class.java
        )

        argsArr[1] = true
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "displayButtonAnimation",
                argsArr,
                manager,
                View::class.java,
                Boolean::class.java,
        )

        val animationArrCancel = arrayOf(rv, true, cancel)
        ReflectUtil.invoke(
            StopWatchAnimationManager::class.java,
            "hideButtonAnimation",
            animationArrCancel,
            manager,
            View::class.java,
            Boolean::class.java,
            View::class.java,
        )
        val argsArrSign = arrayOf(rv, false)
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "resetButton",
                argsArrSign,
                manager,
                View::class.java,
                Boolean::class.java,
        )
        val anmi = arrayOf(0f, 1f, rv)
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "postureAnimator",
                anmi,
                manager,
                Float::class.java,
                Float::class.java,
                View::class.java,
        )

        val listAnmi = arrayOf(0, 1, rv, null, 122L)
        ReflectUtil.invoke(
                StopWatchAnimationManager::class.java,
                "listAnimator",
                listAnmi,
                manager,
                Int::class.java,
                Int::class.java,
                View::class.java,
                Animator.AnimatorListener::class.java,
                Long::class.java,
        )
    }

    @Test
    fun test_set_dial_layout() {
        val manager = StopWatchAnimationManager()
        val view = View(mContext)
        view.layoutParams = ViewGroup.MarginLayoutParams(100, 100)
        manager.setDialLayout(view, true, null)
        manager.setDialLayout(view, false, null)
    }
}