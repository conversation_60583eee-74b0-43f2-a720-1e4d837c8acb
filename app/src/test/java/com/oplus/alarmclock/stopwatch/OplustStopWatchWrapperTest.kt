/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchViewTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2022/7/7
 ** Author: niexiaokang
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  niexiaokang  2022/7/7     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.stopwatch.OplusStopWatchWrapper.getDisplayTimeStr
import com.oplus.alarmclock.stopwatch.OplusStopWatchWrapper.getFlashBackTimeStr
import com.oplus.alarmclock.stopwatch.OplusStopWatchWrapper.getTimeStr
import org.junit.Assert
import org.junit.Test

class OplustStopWatchWrapperTest : TestParent() {


    @Test
    fun should_not_null_when_getDisplayTimeStr() {
        //invoke call()
        val timerStr = getDisplayTimeStr()
        //verify
        Assert.assertNotNull(timerStr)
    }

    @Test
    fun should_not_null_when_getTimeStr() {
        //invoke call()
        val timerStr = getTimeStr(OplusStopWatch.SHORT_FORMAT, true)
        //verify
        Assert.assertNotNull(timerStr)
    }

    @Test
    fun should_not_null_when_getFlashBackTimeStr() {
        //invoke call()
        val timerStr = getFlashBackTimeStr(OplusStopWatch.SHORT_FORMAT)
        //verify
        Assert.assertNotNull(timerStr)
    }
}