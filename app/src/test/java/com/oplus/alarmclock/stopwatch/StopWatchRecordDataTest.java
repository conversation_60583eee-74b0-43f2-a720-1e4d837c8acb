/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-19, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.stopwatch;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Random;

public class StopWatchRecordDataTest extends TestParent {
    private final int MIN_IN_HOUR = 60;
    private final int SEC_IN_MIN = 60;
    private final int HOURS_OF_WHOLE_DAY = 24;
    private final int MILLISEC_IN_SEC = 1000;
    private final long MIN_IN_MILLISEC = SEC_IN_MIN * MILLISEC_IN_SEC;
    private final long HOUR_IN_MILLISEC = MIN_IN_HOUR * MIN_IN_MILLISEC;
    private final long DAY_IN_MILLISEC =HOURS_OF_WHOLE_DAY * HOUR_IN_MILLISEC;
    @Override
    public void setUp()throws Exception{
        super.setUp();
    }

    @Test
    public void should_returnCorrectString_when_getTimeRecord_with_elapseTime_bigger_than_oneDay(){
        Random rand = new Random();
        DecimalFormat decimalFormat = new DecimalFormat();
        int repeat = 50;
        for(int i=0; i<repeat; i++) {
            //calculate elapseTime
            //ensure that the day is bigger than 0
            int day = rand.nextInt(100 - 1) + 1;
            int hour = rand.nextInt(HOURS_OF_WHOLE_DAY);
            int min = rand.nextInt(MIN_IN_HOUR);
            long sec = rand.nextInt(SEC_IN_MIN);
            long elapseTime = day * DAY_IN_MILLISEC + hour * HOUR_IN_MILLISEC + min * MIN_IN_MILLISEC
                    + sec * MILLISEC_IN_SEC;

            StopWatchRecordData recordData = new StopWatchRecordData(elapseTime, mContext);
            //invoke getTimeRecord()
            String actualString = recordData.getTimeRecord();

            //build expected string
            String timeSeparator = mContext.getResources().getString(R.string.time_separator);
            StringBuilder sb  = new StringBuilder(32);
            decimalFormat.setMinimumIntegerDigits(0);
            sb.append(decimalFormat.format(day));
            sb.append(timeSeparator);
            int minIntDigits = 2;
            decimalFormat.setMinimumIntegerDigits(minIntDigits);
            sb.append(decimalFormat.format(hour));
            sb.append(timeSeparator);
            sb.append(decimalFormat.format(min));
            sb.append(timeSeparator);
            sb.append(decimalFormat.format(sec));
            String expectedStr = sb.toString();

            //assert equals
            Assert.assertEquals(expectedStr, actualString);
        }

    }

    @Test
    public void should_returnCorrectString_when_getTimeRecord_with_elapseTime_smaller_than_oneDay_and_bigger_than_oneHour() {
        Random rand = new Random();
        DecimalFormat decimalFormat = new DecimalFormat();
        int minIntDigits = 2;
        decimalFormat.setMinimumIntegerDigits(minIntDigits);
        int repeat = 50;
        for (int i = 0; i < repeat; i++) {
            //calculate elapseTime
            //ensure that the hour is bigger than 0
            int hour = rand.nextInt(HOURS_OF_WHOLE_DAY-1) + 1;
            int min = rand.nextInt(MIN_IN_HOUR);
            long sec = rand.nextInt(SEC_IN_MIN);
            long elapseTime = hour * HOUR_IN_MILLISEC + min * MIN_IN_MILLISEC + sec * MILLISEC_IN_SEC;

            System.out.println("hour: " + hour + " min: " + min + " sec: " + sec);
            StopWatchRecordData recordData = new StopWatchRecordData(elapseTime, mContext);
            //invoke getTimeRecord()
            String actualString = recordData.getTimeRecord();

            //build expected string
            String timeSeparator = mContext.getResources().getString(R.string.time_separator);
            StringBuilder sb = new StringBuilder(32);
            sb.append(decimalFormat.format(hour));
            sb.append(timeSeparator);
            sb.append(decimalFormat.format(min));
            sb.append(timeSeparator);
            sb.append(decimalFormat.format(sec));
            String expectedStr = sb.toString();

            //assert equals
            Assert.assertEquals(expectedStr, actualString);

        }
    }

    @Test
    public void should_returnCorrectString_when_getTimeRecord_with_elapseTime_smaller_than_oneHour() {
        Random rand = new Random();
        DecimalFormat decimalFormat = new DecimalFormat();
        int minIntDigits = 2;
        decimalFormat.setMinimumIntegerDigits(minIntDigits);
        int maxFractionDigits = 2;
        decimalFormat.setMaximumFractionDigits(maxFractionDigits);
        decimalFormat.setRoundingMode(RoundingMode.FLOOR);
        int repeat = 50;
        for (int i = 0; i < repeat; i++) {
            //calculate elapseTime
            int min = rand.nextInt(MIN_IN_HOUR);
            long sec = rand.nextInt(SEC_IN_MIN);
            float secFractionPart =  rand.nextFloat();
            long elapseTime = min * MIN_IN_MILLISEC + sec * MILLISEC_IN_SEC + (long)(secFractionPart * MILLISEC_IN_SEC);

            System.out.println("min: " + min + " sec: " + sec + " secFractionPart: " + secFractionPart);
            StopWatchRecordData recordData = new StopWatchRecordData(elapseTime, mContext);
            //invoke getTimeRecord()
            String actualString = recordData.getTimeRecord();

            //build expected string
            String timeSeparator = mContext.getResources().getString(R.string.time_separator);
            StringBuilder sb = new StringBuilder(32);
            int MinFractionDigits = 0;
            decimalFormat.setMinimumFractionDigits(MinFractionDigits);
            sb.append(decimalFormat.format(min));
            sb.append(timeSeparator);
            MinFractionDigits = 2;
            decimalFormat.setMinimumFractionDigits(MinFractionDigits);
            sb.append(decimalFormat.format((float)sec+secFractionPart));
            String expectedStr = sb.toString();

            //assert equals
//            Assert.assertEquals(expectedStr, actualString);
        }
    }
}
