/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - StopWatchRecordUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 ****************************************************************/
package com.oplus.alarmclock.stopwatch

import com.oplus.alarmclock.TestParent
import org.junit.Assert
import org.junit.Test

class StopWatchRecordUtilsTest : TestParent() {
    @Test
    fun should_no_exception_when_use_timer_alert_utils() {
        var time = StopWatchRecordUtils.getLastIntervalTime(mContext, "")
        Assert.assertEquals(time, 0)
        val day = 11
        val hour = 12
        val minute = 13
        val second = 14
        val millSecond = 15
        time = StopWatchRecordUtils.getLastIntervalTime(mContext, "$minute:$second.$millSecond")
        var correctTime = ((minute * 60 + second) * 1000 + millSecond * 10).toLong()
        Assert.assertEquals(time, correctTime)
        time = StopWatchRecordUtils.getLastIntervalTime(mContext, "$hour:$minute:$second")
        correctTime = ((hour * 60 * 60 + minute * 60 + second) * 1000).toLong()
        Assert.assertEquals(time, correctTime)
        time = StopWatchRecordUtils.getLastIntervalTime(mContext, "$day:$hour:$minute:$second")
        correctTime = ((day * 24 * 60 * 60 + hour * 60 * 60 + minute * 60 + second) * 1000).toLong()
        Assert.assertEquals(time, correctTime)
    }
}