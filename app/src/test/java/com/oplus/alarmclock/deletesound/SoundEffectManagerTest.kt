/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - SoundEffectManagerTestK.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/10/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/10/22     1.0            add file
 ****************************************************************/
package com.oplus.alarmclock.deletesound

import com.oplus.alarmclock.TestParent
import org.junit.Test
import com.oplus.alarmclock.ReflectUtil;
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class SoundEffectManagerTest : com.oplus.alarmclock.TestParent() {

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_invoke_play_when_playSoundEffect() {
        val mSoundEffectManager =
            com.oplus.alarmclock.deletesound.SoundEffectManager.Companion.instance
        val player: com.oplus.alarmclock.deletesound.AlarmSoundPool = mockk<com.oplus.alarmclock.deletesound.AlarmSoundPool>()
        every { player.getPlayer() } answers { callOriginal() }
        every { player.loadResource() } answers { nothing }
        every { player.play() } answers { nothing }

        com.oplus.alarmclock.ReflectUtil.setFieldValue(com.oplus.alarmclock.deletesound.SoundEffectManager::class.java, "mPlayer", mSoundEffectManager, player)

        mSoundEffectManager.playSoundEffect()
        verify { player.play() }
    }

}