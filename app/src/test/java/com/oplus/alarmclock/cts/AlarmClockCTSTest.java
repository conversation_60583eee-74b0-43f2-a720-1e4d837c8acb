/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmClockCTSTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/12/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/12/26     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.cts;

import android.content.Intent;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.view.LocalViewPager;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.view.LocalViewPager;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.view.LocalViewPager;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

import androidx.viewpager2.widget.ViewPager2;

@Config(shadows = {ShadowGetInitialDisplayDensityUtil.class})

public class AlarmClockCTSTest extends TestParent {


    AlarmClockCTS mActivity;
    AlarmClockCTS mSpymActivity;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mActivity = Robolectric.buildActivity(AlarmClockCTS.class).get();
        mSpymActivity = Mockito.spy(mActivity);

        ViewPager2 viewPager = mock(ViewPager2.class);
        ReflectUtil.setFieldValue(AlarmClock.class, "mColorViewPager", mSpymActivity, viewPager);
    }

    @Test
    @Ignore
    public void should_set_sCurrentTab_when_onStatusBarClick_with_CTS_START_TIMER_action() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent();
        intent.setAction(AlarmClock.ACTION_CTS_START_TIMER);

        ReflectUtil.invoke(AlarmClockCTS.class, "onStatusBarClick",
                new Object[]{intent, ""}, mSpymActivity, Intent.class, String.class);

        int currentTab = (int) ReflectUtil.getFieldValue(AlarmClock.class, "sCurrentTab", mSpymActivity);

        assertEquals(currentTab, AlarmClock.TAB_INDEX_OPLUSTIME);
    }


    @Test
    @Ignore
    public void should_set_sCurrentTab_when_onStatusBarClick_with_SHOW_ALARMS_action() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent();
        intent.setAction(AlarmClock.ACTION_SHOW_ALARM);

        ReflectUtil.invoke(AlarmClockCTS.class, "onStatusBarClick",
                new Object[]{intent, ""}, mSpymActivity, Intent.class, String.class);

        int currentTab = (int) ReflectUtil.getFieldValue(AlarmClock.class, "sCurrentTab", mSpymActivity);

        assertEquals(currentTab, AlarmClock.TAB_INDEX_ALARMCLOCK);
    }
}