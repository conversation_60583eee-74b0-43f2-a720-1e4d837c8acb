/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - HandleApiActivityTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/12/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/12/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.cts;

import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.res.Resources;
import android.os.Bundle;
import android.provider.AlarmClock;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.utils.StringUtils;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.Random;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {ShadowGetInitialDisplayDensityUtil.class})
public class HandleApiActivityTest extends TestParent {

    HandleApiActivity mActivity;
    HandleApiActivity mSpymActivity;

    String[] mSystemRingOldList = new String[]{"one", "two", "three"};
    String[] mSystemRingNewList = new String[]{"one", "two", "three"};

    @Override
    public void setUp() throws Exception {
        super.setUp();

        mActivity = Robolectric.buildActivity(HandleApiActivity.class).create().get();
        mSpymActivity = Mockito.spy(mActivity);

        Resources resources = spy(mSpymActivity.getResources());
        doReturn(mSystemRingOldList).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_OLD(), "array", "oplus"));
        doReturn(mSystemRingNewList).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_NEW(), "array", "oplus"));

        when(mSpymActivity.getResources()).thenReturn(resources);

//        mSpymActivity.onCreate(new Bundle());

    }

    @Test
    public void should_return_expectedRepeatSet_when_getDaysFromIntent_with_specific_intent() throws NoSuchMethodException, IllegalAccessException {

        Random rand = new Random();
        int repeatSetCount = 4;
        int repeat = 5;
        for (int j = 0; j < repeat; j++) {
            ArrayList<Integer> daysArray = new ArrayList(repeatSetCount);
            int sum = 0;
            for (int i = 0; i < repeatSetCount; i++) {
                //[1-7]
                int dayOfWeek = rand.nextInt(7) + 1;
                if (!daysArray.contains(dayOfWeek)) {
                    daysArray.add(dayOfWeek);
                    if (dayOfWeek == 1) {
                        dayOfWeek += 5;
                    } else {
                        dayOfWeek -= 2;
                    }
                    sum = sum | 1 << dayOfWeek;
                }
            }
            Intent intent = new Intent();
            intent.putIntegerArrayListExtra(AlarmClock.EXTRA_DAYS, daysArray);
            //invoke getDaysFromIntent()
            int repeatSet = (int) ReflectUtil.invoke(HandleApiActivity.class, "getDaysFromIntent",
                    new Object[]{intent}, mSpymActivity, Intent.class);
            //assert
            assertEquals(sum, repeatSet);
        }
    }

    @Test
    public void should_return_0_when_getDaysFromIntent_when_intent_parameter_is_null() throws NoSuchMethodException, IllegalAccessException {
        Intent intent = new Intent();
        int exceptedValue = 0;
        int repeatSet = (int) ReflectUtil.invoke(HandleApiActivity.class, "getDaysFromIntent",
                new Object[]{intent}, mSpymActivity, Intent.class);
        //assert
        assertEquals(exceptedValue, repeatSet);
    }

    @Test
    public void should_return_exceptedTitle_when_getRingNameFromOld_with_title_is_Null() throws NoSuchMethodException, IllegalAccessException {

        String title = "";
        String resultTitle = AlarmRingUtils.getRingNameFromOld(mContext, title);
        String exceptedTitle = mContext.getResources().getString(R.string.default_alarm_summary);
        assertEquals(exceptedTitle, resultTitle);
    }

    @Test
    public void should_not_startActivity_when_handleSetTimer_with_intent_without_EXTRA_LENGTH() throws NoSuchMethodException, IllegalAccessException {
        Intent intent = new Intent();
        ReflectUtil.invoke(HandleApiActivity.class, "handleSetTimer",
                new Object[]{intent}, mSpymActivity, Intent.class);
        verify(mSpymActivity).startActivity(any(Intent.class));
    }


    @Test
    public void should_not_startService_when_handleSetTimer_with_intent_with_EXTRA_LENGTH_not_in_range() throws NoSuchMethodException, IllegalAccessException {

        Intent intent = new Intent();
        intent.putExtra(com.oplus.alarmclock.AlarmClock.EXTRA_LENGTH, 0);
        ReflectUtil.invoke(HandleApiActivity.class, "handleSetTimer",
                new Object[]{intent}, mSpymActivity, Intent.class);
        verify(mSpymActivity, times(0)).startService(any(Intent.class));

        intent.putExtra(com.oplus.alarmclock.AlarmClock.EXTRA_LENGTH, 24 * 60 * 60 + 1);
        ReflectUtil.invoke(HandleApiActivity.class, "handleSetTimer",
                new Object[]{intent}, mSpymActivity, Intent.class);
        verify(mSpymActivity, times(0)).startService(any(Intent.class));
    }

    @Test
    public void should_startService_when_handleSetTimer_with_EXTRA_LENGTH_in_range() throws NoSuchMethodException, IllegalAccessException {
        Intent intent = new Intent();
        intent.putExtra(com.oplus.alarmclock.AlarmClock.EXTRA_LENGTH, 10);
        Context context = mock(Context.class);
        when(context.bindService(any(Intent.class), any(ServiceConnection.class), anyInt())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                System.out.println("bindService");
                return null;
            }
        });

        when(mSpymActivity.getApplicationContext()).thenReturn(context);
        ReflectUtil.invoke(HandleApiActivity.class, "handleSetTimer",
                new Object[]{intent}, mSpymActivity, Intent.class);
        verify(mSpymActivity).startService(any(Intent.class));
    }

}