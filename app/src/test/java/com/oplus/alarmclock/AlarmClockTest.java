/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-23, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock;

import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;

import com.oplus.alarmclock.ai.AiSupportContentProvider;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.alarmclock.AlarmSettingActivity;
import com.oplus.alarmclock.cts.HandleApiActivity;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;
import com.oplus.alarmclock.timer.OplusTimerFragment;
import com.oplus.alarmclock.timer.TimerAlertReceiver;
import com.oplus.alarmclock.timer.TimerNormalFragment;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.FlexibleWindowUtils;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.ai.AiSupportContentProvider;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.oplus.alarmclock.alarmclock.AlarmClockFragment;
import com.oplus.alarmclock.cts.HandleApiActivity;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.OplusTimerFragment;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.view.AlarmCloseModelPickLayout;
import com.oplus.alarmclock.view.PrivacyPolicyAlert;
import com.oplus.alarmclock.globalclock.WorldClockViewFragment;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowActivity;
import org.robolectric.shadows.ShadowLooper;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {AlarmClockTest.ShadowPrivacyPolicyAlert.class, ShadowGetInitialDisplayDensityUtil.class})
public class AlarmClockTest extends TestParent {
    final String ACTION_FLOAT_SPEECH_ASSIST_NEW = "heytap.intent.action.ACTIVATE_SPEECH_ASSIST";
    final int SHOW_SET_ALARM_ACTIVITY_DELAY = 100;
    final int TAB_INDEX_TIMER = 3;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    @Ignore
    public void should_invoke_putLong_when_setTimerArguments_with_start_is_true_and_sec_bigger_than_0() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock spyAlarmClock = Mockito.spy(new AlarmClock());
        Intent intent = new Intent();
        boolean start = true;
        long sec = 10L;
        intent.putExtra(AlarmClock.EXTRA_TIMER_START, start);
        intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, sec);
        doReturn(intent).when(spyAlarmClock).getIntent();
        OplusTimerFragment fragment = new TimerNormalFragment();
        AiSupportContentProvider.sAiStartTimerMark = true;
        //invoke setTimerArguments()
        spyAlarmClock.setTimerArguments(fragment);

        //assert
        Bundle bundle = (Bundle) ReflectUtil.getFieldValue(OplusTimerFragment.class, "mBundle", fragment);
        long actualSec = bundle.getLong(AlarmClock.EXTRA_TIMER_SECONDS);
        assertEquals(sec, actualSec);
    }

    @Ignore
    @Config(shadows = {ShadowColorDarkModeUtil.class, ShadowPrivacyPolicyAlert.class}, qualifiers = "en-normal-port-ldpi")
    @Test
    public void should_call_preloadFragments_when_onResume_with_mFirstOnResume_is_true_and_sCurrentTab_is_TAB_INDEX_ALARMCLOCK() throws NoSuchFieldException, IllegalAccessException {
        Intent intent = new Intent();
        intent.putExtra(HandleApiActivity.IS_FROM_HANDLE_API_KEY, HandleApiActivity.FROM_HANDLE_API);
        AlarmClock spyAlarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());
        doNothing().when(spyAlarmClock).onRestoreInstanceState(any(Bundle.class));
        Bundle bundle = new Bundle();
        spyAlarmClock.onCreate(bundle);
        boolean mFirstOnResume = true;
        ReflectUtil.setFieldValue(AlarmClock.class, "mFirstOnResume", spyAlarmClock, mFirstOnResume);
        AlarmClock.sCurrentTab = AlarmClock.TAB_INDEX_ALARMCLOCK;
        doNothing().when(spyAlarmClock).openAlarmSetPage(any(Intent.class), eq(false));
        //invoke onResume()
        spyAlarmClock.onResume();
        ShadowLooper shadowLooper = Shadows.shadowOf(spyAlarmClock.getMainLooper());
        shadowLooper.idleFor(1000, TimeUnit.MILLISECONDS);
        //assert
        HashMap<Integer, Fragment> mFragmentsCache = (HashMap) ReflectUtil.getFieldValue(AlarmClock.class, "mFragmentsCache", spyAlarmClock);
        int expectedSize = 4;
        assertEquals(mFragmentsCache.size(), expectedSize);
    }

    @Ignore
    @Config(shadows = {ShadowUtils.class})
    @Test
    public void should_startService_with_ACTION_ALARM_CLOCK_VOICE_SET_ALARM_when_dispatchIntentAction_with_action_is_ACTION_ALARM_CLOCK_VOICE_SET_ALARM() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_ALARM_CLOCK_VOICE_SET_ALARM);
        AlarmClock alarmClock = Robolectric.buildActivity(AlarmClock.class, intent).get();
        //invoke dispatchIntentAction()
        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        ShadowActivity shadowActivity = Shadows.shadowOf(alarmClock);
        Intent nextStartedServiceIntent = shadowActivity.getNextStartedService();
        assertEquals(ACTION_FLOAT_SPEECH_ASSIST_NEW, nextStartedServiceIntent.getAction());
    }

    @Test
    public void should_call_setAlarmByShortcut_when_dispatchIntentAction_with_action_is_ACTION_ALARM_CLOCK_SET_ALARM() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_ALARM_CLOCK_SET_ALARM);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());
        doNothing().when(alarmClock).openAlarmSetPage(any(Intent.class), eq(false));

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        ShadowLooper shadowLooper = Shadows.shadowOf(alarmClock.getMainLooper());
        shadowLooper.idleFor(SHOW_SET_ALARM_ACTIVITY_DELAY, TimeUnit.MILLISECONDS);
        verify(alarmClock).setAlarmByShortcut();
    }

    @Test
    public void should_call_setTimerByAi_when_dispatchIntentAction_with_action_is_ACTION_AI_SET_TIMER() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_AI_SET_TIMER);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).setTimerByAi();
    }

    @Test
    public void should_call_openAppByWorldWidget_when_dispatchIntentAction_with_action_is_ACTION_VIEW_WORLD_CLOCKS() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_VIEW_WORLD_CLOCKS);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).openAppByWorldWidget();
    }



    @Test
    public void should_call_openApp_when_dispatchIntentAction_with_action_is_ACTION_ENTER_AND_OPEN_TIMER() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(TimerAlertReceiver.ACTION_ENTER_AND_OPEN_TIMER);
        intent.putExtra(AlarmClock.ACTION_PART_TAB_INDEX,-1);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).openApp();
    }

    @Test
    public void should_call_openApp_when_dispatchIntentAction_with_action_is_ACTION_ALARM_CLOCK_IOT_SET_ALARM() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_ALARM_CLOCK_IOT_SET_ALARM);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        PrefUtils.putBoolean(mContext, PrivacyPolicyAlert.SP_NAME, PrivacyPolicyAlert.SP_KEY_PRIVACY_POLICY_ALERT, false);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).setAlarmByIOT();
    }

    @Test
    public void should_call_setAlarmByAi_when_dispatchIntentAction_with_action_is_ACTION_ALARM_CLOCK_CARD_SET_ALARM() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_ALARM_CLOCK_CARD_SET_ALARM);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);

        PrefUtils.putBoolean(mContext, PrivacyPolicyAlert.SP_NAME, PrivacyPolicyAlert.SP_KEY_PRIVACY_POLICY_ALERT, false);

        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).setAlarmByAi();
    }

    @Test
    public void should_call_handleApiSetTimer_when_dispatchIntentAction_with_action_is_HANDLE_API__SET_TIMER() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.HANDLE_API__SET_TIMER);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);


        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //assert
        verify(alarmClock).handleApiSetTimer();
    }


    @Test
    public void should_call_setTimerArguments_when_dispatchIntentAction_with_action_is_ACTION_AI_SET_TIMER() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        Intent intent = new Intent(AlarmClock.ACTION_AI_SET_TIMER);
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class, intent).get());
        AlarmClock.LocalFragmentPagerAdapter fragPagerAdapter = mock(AlarmClock.LocalFragmentPagerAdapter.class);
        when(fragPagerAdapter.getItem(anyInt())).thenReturn(mock(OplusTimerFragment.class));
        ReflectUtil.setFieldValue(AlarmClock.class, "mFragPagerAdapter", alarmClock, fragPagerAdapter);

        ClockEventDispatcher clockEventDispatcher = new ClockEventDispatcher();
        clockEventDispatcher.registerDispatcherAction(alarmClock);
        ReflectUtil.setFieldValue(AlarmClock.class, "mClockEventDispatcher", alarmClock, clockEventDispatcher);
        //invoke getShortcutEventIntent()
        ReflectUtil.invoke(AlarmClock.class, "dispatchIntentAction", null, alarmClock);
        //verify
        verify(alarmClock).setTimerArguments(any(OplusTimerFragment.class));
    }

    @Test
    public void should_call_OplusTimerFragment_onBackPressed_when_onBackPressed_with_sCurrentTab_is_TAB_INDEX_TIMER_and_fragment_instanceof_OplusTimerFragment() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock alarmClock = new AlarmClock();
        AlarmClock.LocalFragmentPagerAdapter fragPagerAdapter = mock(AlarmClock.LocalFragmentPagerAdapter.class);
        OplusTimerFragment oplusTimerFragment = mock(OplusTimerFragment.class);

        when(fragPagerAdapter.getItem(anyInt())).thenReturn(oplusTimerFragment);
        ReflectUtil.setFieldValue(AlarmClock.class, "mFragPagerAdapter", alarmClock, fragPagerAdapter);
        AlarmClock.sCurrentTab = TAB_INDEX_TIMER;
        //invoke onBackPressed()
        alarmClock.onBackPressed();
        //verify
        verify(oplusTimerFragment).onBackPressed();
    }

    @Config(shadows = {ShadowColorDarkModeUtil.class,}, qualifiers = "en-normal-port-ldpi")
    @Test
    public void should_call_setTimerArguments_when_getItem_with_TAB_INDEX_OPLUSTIME_and_sIsStartByAI_is_true() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class).get());
        alarmClock.onCreate(null);
        OplusTimerFragment fragment = mock(OplusTimerFragment.class);
        boolean isTimer0Running = false;
        when(fragment.isTimer0Running()).thenReturn(isTimer0Running);
        HashMap<Integer, Fragment> fragmentsCache = new HashMap<>();
        fragmentsCache.put(AlarmClock.TAB_INDEX_OPLUSTIME, fragment);
        ReflectUtil.setFieldValue(AlarmClock.class, "mFragmentsCache", alarmClock, fragmentsCache);
        boolean sIsStartByAI = true;
        ReflectUtil.setFieldValue(AlarmClock.class, "sIsStartByAI", alarmClock, sIsStartByAI);
        //invoke getItem()
        alarmClock.mFragPagerAdapter.getItem(AlarmClock.TAB_INDEX_OPLUSTIME);
        //verify
        verify(alarmClock).setTimerArguments(fragment);
    }

    @Config(shadows = {ShadowColorDarkModeUtil.class,}, qualifiers = "en-normal-port-ldpi")
    @Test
    public void should_put_index_and_fragment_into_mFragmentsCache_when_getItem_with_index_mFragmentsCache_not_contains() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class).get());
        alarmClock.onCreate(null);
        OplusTimerFragment fragment = mock(OplusTimerFragment.class);
        boolean isTimer0Running = false;
        when(fragment.isTimer0Running()).thenReturn(isTimer0Running);
        HashMap<Integer, Fragment> fragmentsCache = new HashMap<>();
        ReflectUtil.setFieldValue(AlarmClock.class, "mFragmentsCache", alarmClock, fragmentsCache);
        int[] indexs = new int[]{AlarmClock.TAB_INDEX_ALARMCLOCK, AlarmClock.TAB_INDEX_GLOBALCITY,
                AlarmClock.TAB_INDEX_STOPWATCH, AlarmClock.TAB_INDEX_OPLUSTIME};
        for (int i = 0; i < indexs.length; i++) {
            //invoke getItem()
            alarmClock.mFragPagerAdapter.getItem(indexs[i]);
            //verify
            assertTrue(fragmentsCache.containsKey(indexs[i]));
        }
    }

    @Test
    public void should_call_mAlarmSetModalView_onKeyDown_when_onKeyDown_with_sCurrentTab_is_TAB_INDEX_ALARMCLOCK() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class).get());
        AlarmClock.sCurrentTab = AlarmClock.TAB_INDEX_ALARMCLOCK;
        KeyEvent event = mock(KeyEvent.class);
        //invoke
        alarmClock.onKeyDown(KeyEvent.KEYCODE_BACK, event);
    }

    @Test
    public void should_call_mAlarmSetModalView_onKeyUp_when_onKeyUp_with_sCurrentTab_is_TAB_INDEX_ALARMCLOCK_and_keyCode_is_KEYCODE_BACK() throws NoSuchFieldException, IllegalAccessException {
        AlarmClock alarmClock = spy(Robolectric.buildActivity(AlarmClock.class).get());
        AlarmClock.sCurrentTab = AlarmClock.TAB_INDEX_ALARMCLOCK;
        KeyEvent event = mock(KeyEvent.class);
        boolean isSupport =  FlexibleWindowUtils.isSupportFlexibleActivity();
        FlexibleWindowUtils.setsIsFlexibleWindow(true);
        boolean isShow = FlexibleWindowUtils.issIsFlexibleWindow();
        //invoke
        alarmClock.onKeyUp(KeyEvent.KEYCODE_BACK, event);
        assertTrue(isShow);
        assertFalse(isSupport);
    }

    @Test
    public void should_call_setBundle_when_setTimerArguments_with_started_is_true_and_seconds_greaterThan_zero() {
        Intent intent = new Intent();
        boolean started = true;
        intent.putExtra(AlarmClock.EXTRA_TIMER_START, started);
        //ensure seconds > 0
        long seconds = 10L;
        intent.putExtra(AlarmClock.EXTRA_TIMER_SECONDS, seconds);
        AlarmClock alarmClock = Robolectric.buildActivity(AlarmClock.class, intent).get();
        OplusTimerFragment fragment = mock(OplusTimerFragment.class);
        AiSupportContentProvider.sAiStartTimerMark = true;
        //invoke setTimerArguments()
        alarmClock.setTimerArguments(fragment);
        //verify
        verify(fragment).setBundle(any(Bundle.class));
    }

    @Test
    public void should_call_setBundle_when_showModelView_with_started_is_true_and_seconds_greaterThan_zero() {
        AlarmCloseModelPickLayout alarmCloseModelPickLayout = new AlarmCloseModelPickLayout(mContext);
        alarmCloseModelPickLayout.setFlatConfig();
        alarmCloseModelPickLayout.setCheckChange(true);
        Assert.assertNotNull(alarmCloseModelPickLayout);
    }

    @Config(shadows = {ShadowColorDarkModeUtil.class,}, qualifiers = "en-normal-port-ldpi")
    @Test
    @Ignore
    public void should_call_setModeToNormal_when_switchModeToNormal_with_isAdded_is_true_and_specfic_fragment_notNull() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        AlarmClock alarmClock = Robolectric.buildActivity(AlarmClock.class).create().get();
        //init mFragmentsCache and fragment field
        ReflectUtil.invoke(AlarmClock.class, "preloadFragments", null, alarmClock);
        HashMap<Integer, Fragment> mFragmentsCache = (HashMap) ReflectUtil.getFieldValue(AlarmClock.class,
                "mFragmentsCache", alarmClock);
        int[] indexs = new int[]{AlarmClock.TAB_INDEX_ALARMCLOCK, AlarmClock.TAB_INDEX_GLOBALCITY, AlarmClock.TAB_INDEX_OPLUSTIME};
        for (int i = 0; i < indexs.length && indexs[i] < mFragmentsCache.size(); i++) {
            Fragment fragment = spy(mFragmentsCache.get(indexs[i]));
            doReturn(true).when(fragment).isAdded();
            mFragmentsCache.put(i, fragment);
            AlarmClock.sCurrentTab = indexs[i];
            //invoke switchModeToNormal()
            ReflectUtil.invoke(AlarmClock.class, "switchModeToNormal", null, alarmClock);
            //assert
            if (fragment instanceof AlarmClockFragment) {
                ((AlarmClockFragment) fragment).setModeToNormal();
            } else if (fragment instanceof WorldClockViewFragment) {
                ((WorldClockViewFragment) fragment).setModeToNormal();
            } else {
                ((OplusTimerFragment) fragment).setModeToNormal();
            }
        }
    }

    @Ignore
    @Test
    public void should_no_exception_when_call_all_nonPrivate_method_with_illegal_args() throws IllegalAccessException {
        boolean isClassObject = false;
        ReflectUtil.NameFilter filter = new ReflectUtil.NameFilter();
        filter.addMethodName("switchActivity");
        filter.addMethodName("onUIConfigChanged");
        filter.addMethodName("onNavigationItemSelected");
        filter.addMethodName("openAppByWorldWidget");
        filter.addMethodName("statementDismiss");
        boolean invokeSuperMethod = false;
        AlarmClock alarmClock = Robolectric.buildActivity(AlarmClock.class).create().get();
        Map<Method, Throwable> map = ReflectUtil.invokeMethodsWithIllegalArgs(alarmClock, isClassObject,
                null, filter, null, null, invokeSuperMethod);
        ShadowLooper shadowLooper = Shadows.shadowOf(alarmClock.getMainLooper());
        shadowLooper.idleFor(1000, TimeUnit.MILLISECONDS);
        assertEquals(0, map.size());
    }


    @Implements(Utils.class)
    public static class ShadowUtils {
        public static boolean isColoros70SpeechAssist(Context context) {
            return true;
        }
    }


    @Implements(COUIDarkModeUtil.class)
    public static class ShadowColorDarkModeUtil {
        @Implementation
        public static void setForceDarkAllow(View view, boolean allow) {
        }
    }

    @Implements(DeviceUtils.class)
    public static class ShadowDeviceUtils {
        public static boolean isExpVersion(Context context) {
            return false;
        }
    }

    @Implements(PrivacyPolicyAlert.class)
    public static class ShadowPrivacyPolicyAlert {
        public boolean checkPermitPrivacyPolicy() {
            return false;
        }

        public static boolean isFirstEntry(Context c) {
            return false;
        }
    }
}
