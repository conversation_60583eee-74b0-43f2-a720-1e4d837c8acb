/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockProviderTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2020/1/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/1/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.provider;

import android.content.ContentValues;
import android.content.UriMatcher;
import android.net.Uri;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.os.WaveformEffect;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;

import java.util.HashMap;

import static org.junit.Assert.*;

public class ClockProviderTest extends TestParent {

    private static final String PARAM_NOTIFY = "PARAM_NOTIFY";
    private static final String UNNOTIFY = "UNNOTIFY";

    private static final int ALARMS = 1;
    private static final int ALARMS_ID = 2;
    private static final int SCHEDULES = 3;
    private static final int SCHEDULES_ID = 4;
    private static final int CITIES = 5;
    private static final int CITIES_ID = 6;
    private static final int DEFAULT_VOLUME = 5;
    private static final int NEW_CITIES = 7;
    private static final int TIMERS = 8;
    private static final int ALARM_REPEAT = 9;
    private static final int ALARM_REPEAT_ID = 10;
    private static final int ALARM_HOLIDAY = 11;
    private static final int ALARM_HOLIDAY_ID = 12;

    private static final UriMatcher sURLMatcher = new UriMatcher(UriMatcher.NO_MATCH);

    static {
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm", ALARMS);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm/#", ALARMS_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "schedules", SCHEDULES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "schedules/#", SCHEDULES_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "cities", CITIES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "cities/*", CITIES_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "new_cities", NEW_CITIES);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "timers", TIMERS);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarms_repeat", ALARM_REPEAT);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarms_repeat/#", ALARM_REPEAT_ID);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm_holiday", ALARM_HOLIDAY);
        sURLMatcher.addURI(ClockContract.AUTHORITY, "alarm_holiday/#", ALARM_HOLIDAY_ID);
    }

    private ClockProvider mClockProvider;

    @Override
    public void setUp() throws Exception {
        mClockProvider = new ClockProvider();
    }

    @Test
    public void should_resultValues_contain_HOUR_of_zero_when_fixAlarmInsertValues_with_initialValues_not_contain_HOUR() throws NoSuchMethodException, IllegalAccessException {

        ContentValues initialValues = new ContentValues();
        ContentValues resultValues = (ContentValues) ReflectUtil.invoke(ClockProvider.class, "fixAlarmInsertValues", new Object[] {initialValues}, mClockProvider, ContentValues.class);
        assertEquals(0, resultValues.get(ClockContract.Alarm.HOUR));
    }

    @Test
    public void should_resultValues_contain_ALERTTYPE_of_2_when_fixAlarmInsertValues_with_initialValues_not_contain_ALERTTYPE() throws NoSuchMethodException, IllegalAccessException {

        ContentValues initialValues = new ContentValues();
        ContentValues resultValues = (ContentValues) ReflectUtil.invoke(ClockProvider.class, "fixAlarmInsertValues", new Object[] {initialValues}, mClockProvider, ContentValues.class);
        assertEquals(2, resultValues.get(ClockContract.Alarm.ALERTTYPE));
    }

    @Test
    public void should_resultValues_contain_ALERTTYPE_of_0_when_fixAlarmInsertValues_with_initialValues_not_contain_ALERTTYPE_and_VIBRATE_is_EFFECT_RINGTONE_NOVIBRATE() throws NoSuchMethodException, IllegalAccessException {

        ContentValues initialValues = new ContentValues();
        initialValues.put(ClockContract.Alarm.VIBRATE, WaveformEffect.EFFECT_RINGTONE_NOVIBRATE);
        ContentValues resultValues = (ContentValues) ReflectUtil.invoke(ClockProvider.class, "fixAlarmInsertValues", new Object[] {initialValues}, mClockProvider, ContentValues.class);
        assertEquals(0, resultValues.get(ClockContract.Alarm.ALERTTYPE));

    }

    @Test
    public void should_resultUri_has_PARAM_NOTIFY_of_UNNOTIFY_when_buildUnNotifyUri_with_baseUri() {
        Uri baseUri = Uri.parse("content://com.coloros.alarmclock.alarmclock/new_cities");

        Uri uri = ClockProvider.buildUnNotifyUri(baseUri);

        String para = uri.getQueryParameter(PARAM_NOTIFY);
        assertEquals(UNNOTIFY, para);
    }

    @Test
    public void should_true_when_shouldDoNotify_with_baseUri_not_has_para_of_PARAM_NOTIFY() throws NoSuchMethodException, IllegalAccessException {
        Uri baseUri = Uri.parse("content://com.coloros.alarmclock.alarmclock/new_cities");
        boolean result = (boolean) ReflectUtil.invoke(ClockProvider.class, "shouldDoNotify", new Object[]{baseUri}, mClockProvider, Uri.class);
        assertTrue(result);
    }


    @Test
    public void should_false_when_shouldDoNotify_with_baseUri_has_para_PARAM_NOTIFY_of_UNNOTIFY() throws NoSuchMethodException, IllegalAccessException {
        Uri baseUri = Uri.parse("content://com.coloros.alarmclock.alarmclock/new_cities");
        baseUri = baseUri.buildUpon().appendQueryParameter(PARAM_NOTIFY, UNNOTIFY).build();
        boolean result = (boolean) ReflectUtil.invoke(ClockProvider.class, "shouldDoNotify", new Object[]{baseUri}, mClockProvider, Uri.class);
        assertFalse(result);
    }

    @Test
    public void should_return_excepted_strResult_when_getType_with_uri() {

        HashMap<Uri, String> uriStringMap = new HashMap<>();
        uriStringMap.put(Uri.parse("content://com.coloros.alarmclock.alarmclock/alarm"), "vnd.android.cursor.dir/alarms");
        uriStringMap.put(Uri.parse("content://com.coloros.alarmclock.alarmclock/schedules"), "vnd.android.cursor.dir/schedules");
        uriStringMap.put(Uri.parse("content://com.coloros.alarmclock.alarmclock/new_cities"), "vnd.android.cursor.dir/new_cities");

        for (Uri uri : uriStringMap.keySet()) {
            String strResult = mClockProvider.getType(uri);
            assertEquals(uriStringMap.get(uri), strResult);
        }
    }

}