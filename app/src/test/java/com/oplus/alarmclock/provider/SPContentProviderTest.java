/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ClockProviderTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2020/1/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/1/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.provider;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;


import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;


import org.junit.BeforeClass;
import org.junit.Test;
import org.robolectric.Robolectric;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


public class SPContentProviderTest extends TestParent {
    private static final String AUTHORITY = "com.oplus.alarmclock.sphelper";
    private static final String COMMA_REPLACEMENT = "__COMMA__";
    private static final String CONTENT = "content://";
    private static final String SEPARATOR = "/";
    private static final String CONTENT_URI = CONTENT + AUTHORITY;
    private static final String TYPE_STRING_SET = "string_set";
    private static final String TYPE_STRING = "string";
    private static final String TYPE_INT = "int";
    private static final String TYPE_LONG = "long";
    private static final String TYPE_FLOAT = "float";
    private static final String TYPE_BOOLEAN = "boolean";
    private static final String VALUE = "value";
    private static final String KEY_SP_STRING_SET = "key_sp_string_set";
    private static final String KEY_SP_STRING = "key_sp_string";
    private static final String KEY_SP_INT = "key_sp_int";
    private static final String KEY_SP_LONG = "key_sp_long";
    private static final String KEY_SP_FLOAT = "key_sp_float";
    private static final String KEY_SP_BOOLEAN = "key_sp_boolean";
    private static final Set<String> VALUE_SP_STRING_SET = Collections.singleton("1,2,3");
    private static final String VALUE_SP_STRING = "spString";
    private static final String TYPE_GET_ALL = "get_all";
    private static final String CURSOR_COLUMN_NAME = "cursor_name";
    private static final String CURSOR_COLUMN_TYPE = "cursor_type";
    private static final String CURSOR_COLUMN_VALUE = "cursor_value";
    private static final Integer VALUE_SP_INT = 1;
    private static final Long VALUE_SP_LONG = 1L;
    private static final Float VALUE_SP_FLOAT = 1f;
    private static final Boolean VALUE_SP_BOOLEAN = true;

    private SPContentProvider mContentProvider;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        mContentProvider = Robolectric.buildContentProvider(SPContentProvider.class).create().get();
    }

    @Test
    public void should_resultValues_put_TYPE_STRING_SET_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING_SET + SEPARATOR + KEY_SP_STRING_SET);
        Set<String> convert = new HashSet<>();
        for (String string : VALUE_SP_STRING_SET) {
            convert.add(string.replace(",", COMMA_REPLACEMENT));
        }
        ContentValues cv = new ContentValues();
        cv.put(VALUE, convert.toString());
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Set<String> spSetString = SPContentHelper.getStringSet(KEY_SP_STRING_SET, new HashSet<>());
        assertNotNull(spSetString);
    }

    @Test
    public void should_resultValues_put_TYPE_STRING_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_STRING + SEPARATOR + KEY_SP_STRING);
        ContentValues cv = new ContentValues();
        cv.put(VALUE, VALUE_SP_STRING);
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        String spString = SPContentHelper.getString(KEY_SP_STRING, "");
        assertEquals(VALUE_SP_STRING, spString);
    }

    @Test
    public void should_resultValues_put_TYPE_INT_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_INT + SEPARATOR + KEY_SP_INT);
        ContentValues cv = new ContentValues();
        cv.put(VALUE, VALUE_SP_INT);
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Integer spInt = SPContentHelper.getInt(KEY_SP_INT, 0);
        assertEquals(VALUE_SP_INT, spInt);
    }

    @Test
    public void should_resultValues_put_TYPE_LONG_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_LONG + SEPARATOR + KEY_SP_LONG);
        ContentValues cv = new ContentValues();
        cv.put(VALUE, VALUE_SP_LONG);
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Long spLong = SPContentHelper.getLong(KEY_SP_LONG, 0);
        assertEquals(VALUE_SP_LONG, spLong);
    }

    @Test
    public void should_resultValues_put_TYPE_FLOAT_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_FLOAT + SEPARATOR + KEY_SP_FLOAT);
        ContentValues cv = new ContentValues();
        cv.put(VALUE, VALUE_SP_FLOAT);
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Float spFloat = SPContentHelper.getFloat(KEY_SP_FLOAT, 0);
        assertEquals(VALUE_SP_FLOAT, spFloat);
    }

    @Test
    public void should_resultValues_put_TYPE_BOOLEAN_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_BOOLEAN + SEPARATOR + KEY_SP_BOOLEAN);
        ContentValues cv = new ContentValues();
        cv.put(VALUE, VALUE_SP_BOOLEAN);
        mContentProvider.update(uri, cv, null, null);
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Boolean spBoolean = SPContentHelper.getBoolean(KEY_SP_BOOLEAN, false);
        assertEquals(VALUE_SP_BOOLEAN, spBoolean);
    }

    @Test
    public void should_resultValues_put_TYPE_GET_ALL_when_update_with_uri() {
        Uri uri = Uri.parse(CONTENT_URI + SEPARATOR + TYPE_GET_ALL);
        Cursor cursor = mContentProvider.query(uri, null, null, null);
        Map<String, Object> resultMap = new HashMap<>();
        if (cursor != null) {
            if (cursor.moveToFirst()) {
                int nameIndex = cursor.getColumnIndex(CURSOR_COLUMN_NAME);
                int typeIndex = cursor.getColumnIndex(CURSOR_COLUMN_TYPE);
                int valueIndex = cursor.getColumnIndex(CURSOR_COLUMN_VALUE);
                do {
                    String key = cursor.getString(nameIndex);
                    String type = cursor.getString(typeIndex);
                    Object value = null;
                    if (type.equalsIgnoreCase(TYPE_STRING)) {
                        value = cursor.getString(valueIndex);
                        if (((String) value).contains(COMMA_REPLACEMENT)) {
                            String str = (String) value;
                            if (str.matches("\\[.*\\]")) {
                                String sub = str.substring(1, str.length() - 1);
                                String[] spl = sub.split(", ");
                                Set<String> returns = new HashSet<>();
                                for (String t : spl) {
                                    returns.add(t.replace(COMMA_REPLACEMENT, ", "));
                                }
                                value = returns;
                            }
                        }
                    } else if (type.equalsIgnoreCase(TYPE_BOOLEAN)) {
                        value = cursor.getString(valueIndex);
                    } else if (type.equalsIgnoreCase(TYPE_INT)) {
                        value = cursor.getInt(valueIndex);
                    } else if (type.equalsIgnoreCase(TYPE_LONG)) {
                        value = cursor.getLong(valueIndex);
                    } else if (type.equalsIgnoreCase(TYPE_FLOAT)) {
                        value = cursor.getFloat(valueIndex);
                    } else if (type.equalsIgnoreCase(TYPE_STRING_SET)) {
                        value = cursor.getString(valueIndex);
                    }
                    resultMap.put(key, value);
                }
                while (cursor.moveToNext());
            }
            cursor.close();
        }
        SPContentHelper.init(AlarmClockApplication.getInstance());
        Map<String, ?> stringMap = SPContentHelper.getAll();
        assertEquals(resultMap, stringMap);
    }
}