/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-9, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.provider;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteAbortException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;

import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implements;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.ArgumentMatchers.startsWith;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.internal.verification.VerificationModeFactory.times;

public class ClockDatabaseHelperTest extends TestParent {
    ClockDatabaseHelper mSpyHelper;
    SQLiteDatabase mMockDb;
    public static final String TIMER_TABLE_NAME = "timers";
    static final int VERSION_20 = 20;
    static final int VERSION_25 = 25;
    //Tables names
    static final String ALARMS_TABLE_NAME = "alarms";
    static final String PRIMARY_KEY = " INTEGER PRIMARY KEY AUTOINCREMENT, ";
    static final String SCHEDULES_TABLE_NAME = "alarm_schedule";
    //View name
    static final String SCHEDULES_VIEW_NAME = "view_schedules";

    //Temp tables names.
    private static final String TMP_ALARMS_TABLE_NAME = "tmp_alarms";
    private static final String TMP_SCHEDULES_TABLE_NAME = "tmp_alarm_schedule";

    //alarm repeat table name
    public static final String ALARM_REPEAT_TABLE_NAME = "alarm_repeat";

    private static final int ALARM_ID_INDEX = 8;

    private static final String SCHEME_ALARMS_TABLE = " ("
            + ClockContract.Alarm.ID + PRIMARY_KEY
            + ClockContract.Alarm.HOUR + " INTEGER NOT NULL, "
            + ClockContract.Alarm.MINUTES + " INTEGER NOT NULL, "
            + ClockContract.Alarm.DAYS_OF_WEEK + " INTEGER, "
            + ClockContract.Alarm.ALARM_TIME + " INTEGER NOT NULL, "
            + ClockContract.Alarm.ENABLED + " INTEGER NOT NULL, "
            + ClockContract.Alarm.ALERTTYPE + " TEXT, "
            + ClockContract.Alarm.MESSAGE + " TEXT NOT NULL, "
            + ClockContract.Alarm.SNOOZE + " INTEGER, "
            + ClockContract.Alarm.ALERT + " TEXT, "
            + ClockContract.Alarm.ALERT_RINGNAME + " TEXT, "
            + ClockContract.Alarm.VOLUME + " INTEGER, "
            + ClockContract.Alarm.VIBRATE + " INTEGER, "
            + ClockContract.Alarm.BACKGROUND + " TEXT, "
            + ClockContract.Alarm.DELETE_AFTER_USE + " INTEGER, "
            + ClockContract.Alarm.WORKDAY_SWITCH + " INTEGER,"
            + ClockContract.Alarm.HOLIDAY_SWITCH + " INTEGER,"
            + ClockContract.Alarm.OWNER_USER_ID + " INTEGER"
            + ");";

    private static final String SCHEME_SCHEDULE_TABLE = " ("
            + ClockContract.Schedule._ID + PRIMARY_KEY
            + ClockContract.Schedule.YEAR + " INTEGER NOT NULL, "
            + ClockContract.Schedule.MONTH + " INTEGER NOT NULL, "
            + ClockContract.Schedule.DAY + " INTEGER NOT NULL, "
            + ClockContract.Schedule.HOUR + " INTEGER NOT NULL, "
            + ClockContract.Schedule.MINUTES + " INTEGER NOT NULL, "
            + ClockContract.Schedule.ALARM_TIME + " INTEGER NOT NULL, "
            + ClockContract.Schedule.SNOOZETIME + " INTEGER, "
            + ClockContract.Schedule.ALARM_STATE + " INTEGER NOT NULL, "
            + ClockContract.Schedule.ALARM_ID + " INTEGER REFERENCES "
            + ALARMS_TABLE_NAME + "(" + ClockContract.Alarm.ID + ") "
            + "ON UPDATE CASCADE ON DELETE CASCADE" + ");";

    private static final String SQL_INSERT_SCHEDULE =
            "INSERT INTO " + SCHEDULES_TABLE_NAME + " ("
                    + ClockContract.Schedule.YEAR + ", "
                    + ClockContract.Schedule.MONTH + ", "
                    + ClockContract.Schedule.DAY + ", "
                    + ClockContract.Schedule.HOUR + ", "
                    + ClockContract.Schedule.MINUTES + ", "
                    + ClockContract.Schedule.ALARM_TIME + ", "
                    + ClockContract.Schedule.SNOOZETIME + ", "
                    + ClockContract.Schedule.ALARM_STATE + ", "
                    + ClockContract.Schedule.ALARM_ID + ") "
                    + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);";

    private static final String ALARMS_REPEAT_TABLE = " ("
            + ClockContract.AlarmsRepeat._ID + PRIMARY_KEY
            + ClockContract.AlarmsRepeat.ALARM_DURATION + " INTEGER NOT NULL, "
            + ClockContract.AlarmsRepeat.ALARM_INTERVAL + " INTEGER NOT NULL, "
            + ClockContract.AlarmsRepeat.ALARM_NUM + " INTEGER NOT NULL, "
            + ClockContract.AlarmsRepeat.ALARM_PROMPT + " INTEGER"
            + ");";

    private static final String SQL_CREATE_ALARM_REPEAT_TABLE =
            "CREATE TABLE IF NOT EXISTS " + ALARM_REPEAT_TABLE_NAME + ALARMS_REPEAT_TABLE;
    private static final String SQL_CREATE_TIMER_TABLE =
            "CREATE TABLE IF NOT EXISTS " + TIMER_TABLE_NAME + " ("
                    + ClockContract.TimerTableColumns._ID + PRIMARY_KEY
                    + ClockContract.TimerTableColumns.DURATION + " INTEGER, "
                    + ClockContract.TimerTableColumns.DESCRIPTION + " TEXT, "
                    + ClockContract.TimerTableColumns.FLAG + " INTEGER, "
                    + ClockContract.TimerTableColumns.SELECTED + " INTEGER,"
                    + ClockContract.TimerTableColumns.RING + " TEXT,"
                    + ClockContract.TimerTableColumns.RINGNAME + " TEXT);";

    @BeforeClass
    public static void classSetUp(){
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback,
                null,null,null);
    }
    @Override
    public void setUp()throws Exception{
        super.setUp();
        mSpyHelper = Mockito.spy(new ClockDatabaseHelper(mContext));
        mMockDb = mock(SQLiteDatabase.class);
    }

    @Test
    public void should_call_mMockDb_execSQL_with_specific_sql_when_onUpgrade_with_oldVersion_is_VERSION_20_and_currentVersion_is_VERSION_25_and_getColumnIndex_return_negativeNum(){
        int oldVersion = VERSION_20;
        int currentVersion = VERSION_25;
        Cursor cursor = mock(Cursor.class);
        int index = -1;
        when(cursor.getColumnIndex(anyString())).thenReturn(index);
        String sql = "SELECT * FROM " + ALARMS_TABLE_NAME + " LIMIT 0";
        when(mMockDb.rawQuery(sql, null)).thenReturn(cursor);
        SQLiteException sqLiteException = new SQLiteAbortException();
        doThrow(sqLiteException).when(mMockDb).execSQL(startsWith("alter table " + ALARMS_TABLE_NAME + " add column ownerUserId INTEGER default '"));
        //invoke onUpgrade()
        mSpyHelper.onUpgrade(mMockDb, oldVersion, currentVersion);
        //assert && verify
        verify(mMockDb,times(2)).execSQL(SQL_CREATE_TIMER_TABLE);
        verify(mMockDb,times(2)).execSQL("alter table " + ALARMS_TABLE_NAME + " add column workdaySwitch INTEGER");
        verify(mMockDb,times(2)).execSQL(SQL_CREATE_ALARM_REPEAT_TABLE);
        verify(mMockDb).execSQL(startsWith("alter table " + ALARMS_TABLE_NAME + " add column ownerUserId INTEGER default '"));
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void should_call_execSQL_with_SQL_INSERT_SCHEDULE_count_times_when_fixScheduleInfo_with_cursor_getCount_is_two() throws NoSuchMethodException, IllegalAccessException {
        final int count = 2;
        Answer cursorAnswer = new Answer() {
            int size = count;
            int pos = -1;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String method = invocation.getMethod().getName();
                switch(method){
                    case "getCount":
                        return size;
                    case "moveToNext":
                        return ++pos < size;
                }
                return null;
            }
        };
        Cursor cursor = mock(Cursor.class, cursorAnswer);
        when(mMockDb.query(eq(ALARMS_TABLE_NAME), eq(AlarmContract.INSTANCE.getQUERY_COLUMNS()), anyString(),
                (String[])isNull(), (String) isNull(), (String) isNull(), (String)isNull())).thenReturn(cursor);
        Alarm alarm = new Alarm();
        ShadowAlarmUtils.sAlarm = alarm;
        //invoke fixScheduleInfo()
        ReflectUtil.invoke(ClockDatabaseHelper.class, "fixScheduleInfo",
                new Object[]{mMockDb}, mSpyHelper, SQLiteDatabase.class);
        //assert
        verify(mMockDb, times(count)).execSQL(eq(SQL_INSERT_SCHEDULE), any(String[].class));

    }

    @Test
    public void should_alarmList_size_equalsTo_alarmCount_and_mScheduleRecords_size_equalsTo_one_when_loadAllAlarms_with_alarmRecord_id_equalsTo_scheduleRecor_alarmId()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        //init alarmList
        SQLiteDatabase db = mock(SQLiteDatabase.class);
        final int alarmCount = 3;
        Answer alarmAnswer = new Answer() {
            int count = alarmCount;
            int pos = -1;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String method = invocation.getMethod().getName();
                switch(method){
                    case "getLong":
                        int index = invocation.getArgument(0);
                        if(index == ClockContract.Alarm.ALARM_ID_INDEX){
                            return (long)pos;
                        }
                        break;
                    case "moveToNext":
                        if(++pos < count){
                            return true;
                        }else {
                            return false;
                        }
                }
                return null;
            }
        };
        Cursor alarmCursor = mock(Cursor.class, alarmAnswer);
        when(db.query(eq(ALARMS_TABLE_NAME), any(String[].class), (String) isNull(), (String[])isNull(),
                (String)isNull(), (String)isNull(),anyString())).thenReturn(alarmCursor);
        final int scheduleCount = alarmCount;
        Answer scheduleAnswer = new Answer() {
            int count = scheduleCount;
            int pos = -1;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String method = invocation.getMethod().getName();
                switch(method){
                    case "getLong":
                        int index = invocation.getArgument(0);
                        if(index == ALARM_ID_INDEX){
                            return (long)pos;
                        }
                        break;
                    case "moveToNext":
                        if(++pos < count){
                            return true;
                        }else {
                            return false;
                        }
                }
                return null;
            }
        };
        Cursor scheduleCursor = mock(Cursor.class, scheduleAnswer);
        when(db.query(eq(SCHEDULES_TABLE_NAME), any(String[].class), (String)isNull(), (String[])isNull(),
                (String)isNull(), (String)isNull(), anyString())).thenReturn(scheduleCursor);
        //inovke loadAllAlarms
        ArrayList alarmList = (ArrayList) ReflectUtil.invoke(ClockDatabaseHelper.class, "loadAllAlarms", new Object[]{db},
                null, SQLiteDatabase.class);
        //assert
        assertEquals(alarmCount, alarmList.size());
        for(int i=0; i<alarmCount; i++){
            Object alarmRecord = alarmList.get(i);
            List mScheduleRecords = (List)ReflectUtil.getFieldValue(alarmRecord.getClass(), "mScheduleRecords", alarmRecord);
            int expectScheduleRecordCount = 1;
            assertEquals(expectScheduleRecordCount, mScheduleRecords.size());
        }
    }

    @Test
    public void should_call_insert_with_TMP_SCHEDULES_TABLE_NAME_alarmCount_times_when_insertAlarmsToTmpTables_with_alarmRecords_in_list_have_one_ScheduleRecord()
            throws NoSuchMethodException, IllegalAccessException, ClassNotFoundException, InstantiationException, InvocationTargetException {
        final int alarmCount = 3;
        Answer alarmAnswer = new Answer() {
            int count = alarmCount;
            int pos = -1;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String method = invocation.getMethod().getName();
                switch(method){
                    case "getLong":
                        int index = invocation.getArgument(0);
                        if(index == ClockContract.Alarm.ALARM_ID_INDEX){
                            return (long)pos;
                        }
                        break;
                    case "moveToNext":
                        if(++pos < count){
                            return true;
                        }else {
                            return false;
                        }
                }
                return null;
            }
        };
        SQLiteDatabase db = mock(SQLiteDatabase.class);
        long id = 1L;
        when(db.insert(eq(TMP_ALARMS_TABLE_NAME), (String)isNull(), any(ContentValues.class))).thenReturn(id);
        Cursor alarmCursor = mock(Cursor.class, alarmAnswer);
        when(db.query(eq(ALARMS_TABLE_NAME), any(String[].class), (String) isNull(), (String[])isNull(),
                (String)isNull(), (String)isNull(),anyString())).thenReturn(alarmCursor);
        ArrayList list = (ArrayList) ReflectUtil.invoke(ClockDatabaseHelper.class, "getAllAlarms",
                new Object[]{db}, null, SQLiteDatabase.class);
        Class scheduleRecordClazz = Class.forName("com.oplus.alarmclock.provider.ClockDatabaseHelper$ScheduleRecord");
        Constructor constructor = scheduleRecordClazz.getDeclaredConstructor();
        constructor.setAccessible(true);
        Object scheduleRecord = constructor.newInstance();
        for(int i=0; i<alarmCount; i++){
            Object alarmRecord = list.get(i);
            ReflectUtil.invoke(alarmRecord.getClass(), "addSchedule", new Object[]{scheduleRecord}, alarmRecord, scheduleRecordClazz);
        }
        //invoke insertAlarmsToTmpTables
        ReflectUtil.invoke(ClockDatabaseHelper.class, "insertAlarmsToTmpTables",
                new Object[]{db, list}, null, SQLiteDatabase.class, ArrayList.class);
        //verify
        verify(db,times(alarmCount)).insert(eq(TMP_SCHEDULES_TABLE_NAME), (String)isNull(), any(ContentValues.class));
    }

    @Implements(AlarmUtils.class)
    public static class ShadowAlarmUtils{
        static Alarm sAlarm;
        public static Alarm createAlarmFromCur(Cursor c, Context context) {
            return sAlarm;
        }
    }


}
