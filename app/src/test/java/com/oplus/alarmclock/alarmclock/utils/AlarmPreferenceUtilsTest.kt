/************************************************************
 * Copyright 2010-2022 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
Description : AlarmPreferenceUtilsTest for test
 * History :( ID, Date, Author, Description)
 * v1.0, 20122-4-20, den<PERSON><PERSON><PERSON>, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock.utils

import android.content.SharedPreferences
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import org.junit.Assert
import org.junit.Test
import java.util.TimeZone

class AlarmPreferenceUtilsTest : TestParent() {

    @Test
    @Throws(InterruptedException::class)
    fun should_return_not_null_when_addAlarmInfo() {
        //Given
        val mAlarm = Alarm()
        mAlarm.id = 5000L
        //When
        AlarmPreferenceUtils.instance.addAlarmInfo(System.currentTimeMillis(), mAlarm)
        val alarmPreferences: SharedPreferences? = ReflectUtil.getFieldValue(
            AlarmPreferenceUtils::class.java, "alarmPreferences", AlarmPreferenceUtils.instance
        ) as SharedPreferences
        val result = alarmPreferences?.getString(mAlarm.id.toString(), null)
        //Then
        Assert.assertNotNull(result)
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_not_equals_when_editAlarmInfo() {
        //Given
        val mAlarm = Alarm()
        mAlarm.id = 5000L
        mAlarm.hour = 10
        val mNewAlarm = Alarm()
        mNewAlarm.id = 5000L
        mNewAlarm.hour = 8
        //When
        AlarmPreferenceUtils.instance.addAlarmInfo(System.currentTimeMillis(), mAlarm)
        val alarmPreferences: SharedPreferences? = ReflectUtil.getFieldValue(
            AlarmPreferenceUtils::class.java, "alarmPreferences", AlarmPreferenceUtils.instance
        ) as SharedPreferences
        val beforeResultStr = alarmPreferences?.getString(mAlarm.id.toString(), null)
        AlarmPreferenceUtils.instance.editAlarmInfo(System.currentTimeMillis(), mAlarm, mNewAlarm)
        val resultStr = alarmPreferences?.getString(mAlarm.id.toString(), null)
        //Then
        Assert.assertNotEquals(beforeResultStr, resultStr)
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_not_equals_when_deleteAlarmInfo() {
        //Given
        val mAlarm = Alarm()
        mAlarm.id = 5000L
        mAlarm.hour = 10
        //When
        AlarmPreferenceUtils.instance.addAlarmInfo(System.currentTimeMillis(), mAlarm)
        val alarmPreferences: SharedPreferences? = ReflectUtil.getFieldValue(
            AlarmPreferenceUtils::class.java, "alarmPreferences", AlarmPreferenceUtils.instance
        ) as SharedPreferences
        val beforeResultStr = alarmPreferences?.getString(mAlarm.id.toString(), null)
        AlarmPreferenceUtils.instance.deleteAlarmInfo(System.currentTimeMillis() + 1000L, mAlarm)
        val resultStr = alarmPreferences?.getString(mAlarm.id.toString(), null)
        //Then
        Assert.assertNotEquals(beforeResultStr, resultStr)
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_different_RingInfo_before_when_getAlarmInfo() {
        //Given
        val mAlarm = Alarm()
        mAlarm.id = 5000L
        mAlarm.hour = 10
        //When
        AlarmPreferenceUtils.instance.addRingInfo(mAlarm)
        val curTimeZoneId = TimeZone.getDefault().id
        val mChangedAlarmInfoList = AlarmPreferenceUtils.instance.getRingInfo(curTimeZoneId)
        val calendar = AlarmNotRingCheckUtils.buildCalendar(curTimeZoneId)
        var mNewAlarm = mChangedAlarmInfoList[ChangedAlarmInfo.calculateAlarmTimeStamp(calendar, mAlarm!!)] as Alarm
        //Then
        val b = ReflectUtil.invoke(
            AlarmPreferenceUtils::class.java,
            "checkIsSameAlarm",
            arrayOf<Any>(mAlarm, mNewAlarm),
            AlarmPreferenceUtils.instance,
            Alarm::class.java,
            Alarm::class.java
        ) as Boolean
        Assert.assertTrue(b)
    }
}