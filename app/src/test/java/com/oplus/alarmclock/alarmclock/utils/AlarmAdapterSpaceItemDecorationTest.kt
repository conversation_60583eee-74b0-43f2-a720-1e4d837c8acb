/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmAdapterSpaceItemDecorationTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/23
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2024/04/23     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.alarmclock.utils

import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.R
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.alarmclock.mini.AlarmMiniAdapterSpaceItemDecoration
import com.oplus.alarmclock.utils.LoopAlarmUtils
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Test
import org.mockito.Mockito
import java.util.Calendar

class AlarmAdapterSpaceItemDecorationTest : TestParent() {


    private val m16Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_16)
    }
    private val m24Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_24)
    }
    private val m102Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_111)
    }
    private val m6Dp by lazy {
        AlarmClockApplication.getInstance().resources.getDimensionPixelSize(R.dimen.layout_dp_6)
    }

    @Test
    fun should_setPadLandItemMargin_whit_position_0() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setPadLandItemMargin(0, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingBottom } returns m102Dp
            every { paddingTop } returns m102Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }

    @Test
    fun should_setPadLandItemMargin_whit_position_1() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setPadLandItemMargin(1, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }


    @Test
    fun should_setPadLandItemMargin_whit_position_2() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setPadLandItemMargin(2, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        val view2 = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }


    @Test
    fun should_setAlarmItemMargin_whit_position_0() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setAlarmItemMargin(0, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingBottom } returns m102Dp
            every { paddingTop } returns m102Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }

    @Test
    fun should_setAlarmItemMargin_whit_position_0_setMarginHorizontal() {
        val mItemDecoration = AlarmMiniAdapterSpaceItemDecoration()
        val view = View(mContext).apply {
            layoutParams = ViewGroup.MarginLayoutParams(m16Dp, m16Dp)
        }
        mItemDecoration.setMarginHorizontal(view, m6Dp, m24Dp)
        Assert.assertEquals(m6Dp, view.marginStart)
    }

    @Test
    fun should_setAlarmItemMargin_whit_position_1() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setAlarmItemMargin(1, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }

    @Test
    fun should_setAlarmItemMargin_whit_position_2() {
        val rect = Rect()
        val mItemDecoration = AlarmAdapterSpaceItemDecoration()
        mItemDecoration.setAlarmItemMargin(2, rect)
        val view = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        val view2 = mockk<View>().apply {
            every { marginEnd } returns m6Dp
            every { marginStart } returns m6Dp
            every { paddingStart } returns m24Dp
            every { paddingEnd } returns m24Dp
            justRun { invalidate() }
        }
        Assert.assertNotNull(rect.left)
    }

    @Test
    fun should_computeLoopCycleDate_whit_false() {
        val loopCycle = 4
        val loopDay = 2
        val nowTime = Calendar.getInstance()
        val create = Calendar.getInstance()
        mockkStatic(LoopAlarmUtils::class)
        val reset = LoopAlarmUtils.computeLoopCycleDate(loopCycle, loopDay, nowTime, create)
        Assert.assertNotNull(reset)
    }

    @Test
    fun should_computeLoopAlarmDateText_whit_text_cycle_date() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        val today = mContext.getString(R.string.today)
        val reset = LoopAlarmUtils.computeLoopAlarmDateText(mContext, 1, alarm)
        Assert.assertEquals(reset, today)
    }
}