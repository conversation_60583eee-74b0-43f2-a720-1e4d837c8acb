/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-9, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.util.ArrayMap;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import java.util.Calendar;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;

public class WeekDayStartFromHelperTest extends TestParent {

    @Test
    public void testQueryLocationWeekStartDay() {
        //ensure Calendar.getInstance().getFirstDayOfWeek() is Calendar.SUNDAY
        Locale.setDefault(Locale.Category.FORMAT, Locale.US);
        ArrayMap<Locale, Integer> map = new ArrayMap<>(3);
        Locale bn = new Locale("bn", "BD");
        Locale pt = new Locale("pt", "PT");
        map.put(Locale.US, Calendar.SUNDAY - 1);
        map.put(bn, RepeatSet.FIRST_DAY_IS_FRIDAY);
        map.put(pt, RepeatSet.FIRST_DAY_IS_MONDAY);
        Set<Map.Entry<Locale, Integer>> set = map.entrySet();
        for (Map.Entry<Locale, Integer> entry : set) {
            Locale.setDefault(entry.getKey());
            //invoke queryLocationWeekStartDay()
            Integer startDay = WeekDayStartFromHelper.getLocationWeekStartDay();
            assertEquals(startDay, entry.getValue());
        }
    }


}