/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-30, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import static android.os.Looper.getMainLooper;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.robolectric.annotation.LooperMode.Mode.PAUSED;

import static java.util.Calendar.HOUR_OF_DAY;

import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.alarmclock.mini.AlarmMiniListAdapter;
import com.oplus.alarmclock.alarmclock.mini.OplusAlarmMiniActivity;
import com.oplus.alarmclock.alarmclock.mini.OplusAlarmMiniFragment;
import com.oplus.alarmclock.databinding.MiniAlarmListItemViewBinding;
import com.oplus.alarmclock.mvvm.base.rvAdapter.BaseViewHolder;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.view.DigitalClock;
import com.oplus.utils.StringUtils;

import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.LooperMode;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.concurrent.TimeUnit;


public class AlarmMiniListAdapterTest extends TestParent {

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    public void should_select_all_alarm_when_updateData(){
        Alarm alarm1 = new Alarm();
        alarm1.setId(1);
        Alarm alarm2 = new Alarm();
        Alarm alarm3 = new Alarm();
        ArrayList<Alarm> list = new ArrayList<>();
        list.add(alarm1);
        list.add(alarm2);
        list.add(alarm3);
        AlarmMiniListAdapter adapter = new AlarmMiniListAdapter();
        adapter.updateData(list);
        Alarm al = adapter.getAlarmById(1);
        assertNotNull(al);
    }

    @Test
    public void should_adapter_view_holder_call_bind_clock_time_when_time_is_now(){
        Alarm alarm1 = new Alarm();
        AlarmMiniListAdapter adapter = new AlarmMiniListAdapter();
        DigitalClock digitalClock = new DigitalClock(mContext);
        adapter.bindClockTime(digitalClock,alarm1);
        Calendar ca = Calendar.getInstance();
        assertEquals(ca.get(HOUR_OF_DAY),alarm1.getHour());
    }

    @Test
    public void should_alarm_schedule_alarm_is_now_call_setAlarmTips(){
        Alarm alarm1 = new Alarm();
        TextView alarmTips = new TextView(mContext);
        TextView loopAlarmTips = new TextView(mContext);
        AlarmSchedule alarmSchedule = AlarmSchedule.build(Calendar.getInstance());
        alarmSchedule.setAlarm(alarm1);
        AlarmMiniListAdapter adapter = new AlarmMiniListAdapter();
        adapter.setAlarmTips(alarmTips,loopAlarmTips,alarm1,alarmSchedule);
        assertEquals(alarm1,alarmSchedule.getAlarm());
    }

    @Test
    public void should_select_all_alarm_when_to_alarm_is_repeat_call_setAlarmTips() {
        Alarm alarm1 = new Alarm();
        alarm1.setWorkdaySwitch(1);
        alarm1.setmCloseOncePriTime(System.currentTimeMillis());
        TextView alarmTips = new TextView(mContext);
        TextView loopAlarmTips = new TextView(mContext);
        AlarmSchedule alarmSchedule = AlarmSchedule.build(Calendar.getInstance());
        alarmSchedule.setAlarm(alarm1);
        AlarmMiniListAdapter adapter = new AlarmMiniListAdapter();
        adapter.setAlarmTips(alarmTips,loopAlarmTips,alarm1,alarmSchedule);
        assertEquals(alarmTips.getVisibility(), View.VISIBLE);
    }



}

