/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-22, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.view.AlarmCloseModelPreferenceCategory;

import org.junit.BeforeClass;
import org.junit.Test;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

@Config(shadows = {ShadowColorDarkModeUtil.class, ShadowUtils.class})
public class AlertCloseModelFragmentTest extends TestParent {
    AlertCloseModelFragment mFragment;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    public void should_getValue_equalsTo_value_when_onPreferenceChange_with_specific_preference_in_prefs_and_value() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //init fragment
        mFragment = new AlertCloseModelFragment();
        FragmentActivity activity = Robolectric.buildActivity(AlertCloseModelActivity.class).create().resume().get();
        FragmentHostCallback host = new FragmentHostCallback(activity, new Handler(), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }

            @Override
            public void onStartActivityFromFragment(@NonNull Fragment fragment, Intent intent,
                                                    int requestCode, @Nullable Bundle options) {
                fragment.getActivity().startActivityFromFragment(fragment, intent, requestCode, options);
            }

            @Override
            public void onStartIntentSenderFromFragment(
                    @NonNull Fragment fragment, IntentSender intent, int requestCode,
                    @Nullable Intent fillInIntent, int flagsMask, int flagsValues,
                    int extraFlags, Bundle options) throws IntentSender.SendIntentException {
                fragment.getActivity().startIntentSenderFromFragment(fragment, intent, requestCode,
                        fillInIntent, flagsMask, flagsValues, extraFlags, options);
            }
        };
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        ReflectUtil.setFieldValue(Fragment.class, "mFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        ReflectUtil.invoke(Fragment.class, "performAttach", null, mFragment);
        ReflectUtil.invoke(Fragment.class, "performCreate", new Object[]{null},
                mFragment, Bundle.class);
        LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        ReflectUtil.invoke(Fragment.class, "performCreateView", new Object[]{inflater, null, null}, mFragment,
                LayoutInflater.class, ViewGroup.class, Bundle.class);

        AlarmCloseModelPreferenceCategory mListPrePosition = (AlarmCloseModelPreferenceCategory) ReflectUtil.getFieldValue(AlertCloseModelFragment.class, "mListPrePosition", mFragment);

        mListPrePosition.setPositionValue(AlarmCloseModelUtils.CLOSE_MODEL_BUTTON);
        assertEquals(AlarmCloseModelUtils.Companion.getSInstance().getMCloseModel(), AlarmCloseModelUtils.CLOSE_MODEL_BUTTON);
    }


    @Test
    public void should_can_clock_when_canClick_with_clicks_greater_than_1000_ms() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        //        mFragment = new AlertCloseModelFragment();
        DoubleClickHelper doubleClickHelper = new DoubleClickHelper();
        ReflectUtil.invoke(DoubleClickHelper.class, "canClick", null, doubleClickHelper);
        long elapsed = SystemClock.elapsedRealtime();
        long lastClickTime = elapsed - 10000;

        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", doubleClickHelper, lastClickTime);
//        ReflectUtil.setFieldValue(AlertCloseModelFragment.class,"mDoubleClickHelper", mFragment, doubleClickHelper);
        boolean b = (boolean) ReflectUtil.invoke(DoubleClickHelper.class, "canClick", null, doubleClickHelper);
        assertTrue(b);


        elapsed = SystemClock.elapsedRealtime();
        lastClickTime = elapsed - 50;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", doubleClickHelper, lastClickTime);
        boolean b1 = (boolean) ReflectUtil.invoke(DoubleClickHelper.class, "canClick", null, doubleClickHelper);
        assertFalse(b1);
    }
}
