/************************************************************
 * Copyright 2010-2022 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
Description : AlarmNotRingCheckUtils for test
 * History :( ID, Date, Author, Description)
 * v1.0, 20122-4-20, den<PERSON><PERSON><PERSON>, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock.utils

import android.content.Context
import android.net.Uri
import android.util.Pair
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.alarmclock.LegalHolidayUtil
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.mockk
import kotlinx.coroutines.Job
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import java.util.TimeZone
import java.util.Calendar

class AlarmNotRingCheckUtilsTest : TestParent() {
    @Test
    @Throws(InterruptedException::class)
    fun should_not_null_checkJob_when_checkAlarmNotRing() {
        //When
        ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "checkAlarmNotRing",
            arrayOf<Any>(),
            AlarmNotRingCheckUtils,
        )
        //Then
        val checkJob: Job = ReflectUtil.getFieldValue(
            AlarmNotRingCheckUtils::class.java, "checkJob", AlarmNotRingCheckUtils
        ) as Job

        Assert.assertNotNull(checkJob)
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_ture_when_isEffectiveAlarm_if_not_workDay() {
        //Given
        val curTimeZoneId = TimeZone.getDefault().id
        val mCalendar = Calendar.getInstance()
        val mAlarm = Alarm()
        val mRings = AlarmPreferenceUtils.instance.getRingInfo(curTimeZoneId)
        mAlarm.workdaySwitch = 1
        //非工作日设置为4月16日，星期六
        mockkStatic(LegalHolidayUtil::class)
        mCalendar.set(2022, 3, 16)
        val mAlarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        //When
        val b = ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "isEffectiveAlarm",
            arrayOf<Any>(mContext, mCalendar, mAlarmInfo, mRings),
            AlarmNotRingCheckUtils,
            Context::class.java,
            Calendar::class.java,
            ChangedAlarmInfo::class.java,
            Map::class.java
        ) as kotlin.Pair<Int, AlarmNotRingCheckUtils.StatisticData?>
        //Then
        Assert.assertTrue(AlarmNotRingCheckUtils.isEffectiveAlarmCode(b.first))
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_ture_when_isEffectiveAlarm_if_is_holiday() {
        //Given
        val curTimeZoneId = TimeZone.getDefault().id
        val mCalendar = Calendar.getInstance()
        val mAlarm = Alarm()
        val mRings = AlarmPreferenceUtils.instance.getRingInfo(curTimeZoneId)
        //不满足重复规则且为节假日
        mAlarm.setRepeat(2)
        mCalendar.set(2022, 4, 3)
        mAlarm.holidaySwitch = 1
        mockkStatic(LegalHolidayUtil::class)
        val holiday = LegalHolidayUtil.Holiday()
        holiday.colorType = LegalHolidayUtil.COLOR_TYPE_HOLIDAY
        holiday.year = mCalendar.get(Calendar.YEAR)
        holiday.yearOfDay = mCalendar.get(Calendar.DAY_OF_YEAR)
        val holidayList = mutableListOf<LegalHolidayUtil.Holiday>()
        holidayList.add(holiday)
        val mAlarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        every { LegalHolidayUtil.getHolidayFromCache(mCalendar) } returns holidayList
        //When
        val b = ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "isEffectiveAlarm",
            arrayOf<Any>(mContext, mCalendar, mAlarmInfo, mRings),
            AlarmNotRingCheckUtils,
            Context::class.java,
            Calendar::class.java,
            ChangedAlarmInfo::class.java,
            Map::class.java
        ) as kotlin.Pair<Int, AlarmNotRingCheckUtils.StatisticData?>
        //Then
        Assert.assertTrue(AlarmNotRingCheckUtils.isEffectiveAlarmCode(b.first))
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_ture_when_only_open_onece() {
        //Given
        val curTimeZoneId = TimeZone.getDefault().id
        val mCalendar = Calendar.getInstance()
        val mAlarm = mockk<Alarm>()
        val mRings = AlarmPreferenceUtils.instance.getRingInfo(curTimeZoneId)
        //不满足重复规则且为节假日
        every { mAlarm.repeatSet } returns 0
        every { mAlarm.workdaySwitch } returns 0
        every { mAlarm.getmCloseOnceTimeNext() } returns 7000L
        every { mAlarm.getmCloseOncePriTime() } returns 5000L
        every { mAlarm.id } returns 1
        every { mAlarm.isEnabled } returns true
        mCalendar.set(2022, 4, 3)
        mockkObject(ChangedAlarmInfo.Companion)
        val alarmTimeStampMock: Long
        if (mAlarm.getmCloseOncePriTime() + 1 + mAlarm.getmCloseOnceTimeNext() == 0L) {
            alarmTimeStampMock = 0L
        } else {
            alarmTimeStampMock = (mAlarm.getmCloseOncePriTime() + 1 + mAlarm.getmCloseOnceTimeNext()) / 2
        }
        every { ChangedAlarmInfo.calculateAlarmTimeStamp(mCalendar, mAlarm) } returns alarmTimeStampMock
        mockkStatic(LegalHolidayUtil::class)
        val holiday = LegalHolidayUtil.Holiday()
        holiday.colorType = LegalHolidayUtil.COLOR_TYPE_HOLIDAY
        holiday.year = mCalendar.get(Calendar.YEAR)
        holiday.yearOfDay = mCalendar.get(Calendar.DAY_OF_YEAR)
        val holidayList = mutableListOf<LegalHolidayUtil.Holiday>()
        holidayList.add(holiday)
        val mAlarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        every { LegalHolidayUtil.getHolidayFromCache(mCalendar) } returns holidayList
        //When
        val b = ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "isEffectiveAlarm",
            arrayOf<Any>(mContext, mCalendar, mAlarmInfo, mRings),
            AlarmNotRingCheckUtils,
            Context::class.java,
            Calendar::class.java,
            ChangedAlarmInfo::class.java,
            Map::class.java
        ) as kotlin.Pair<Int, AlarmNotRingCheckUtils.StatisticData?>
        //Then
        Assert.assertTrue(AlarmNotRingCheckUtils.isEffectiveAlarmCode(b.first))
    }
    @Test
    @Ignore
    @Throws(InterruptedException::class)
    fun should_return_false_when_not_ringing_normally() {
        //Given
        val curTimeZoneId = TimeZone.getDefault().id
        val mCalendar = Calendar.getInstance()
        val mAlarm = mockk<Alarm>()
        var mRings = AlarmPreferenceUtils.instance.getRingInfo(curTimeZoneId)
        mRings = spyk(mRings)
        every { mAlarm.repeatSet } returns 0
        every { mAlarm.workdaySwitch } returns 0
        every { mAlarm.getmCloseOnceTimeNext() } returns 7000L
        every { mAlarm.getmCloseOncePriTime() } returns 5000L
        every { mAlarm.id } returns 1
        every { mAlarm.isEnabled } returns true
        every { mAlarm.hour } returns 1
        every { mAlarm.minutes } returns 20
        every { mAlarm.ringName } returns "无"
        every { mAlarm.label } returns "Labal"
        every { mAlarm.holidaySwitch } returns 5
        every { mAlarm.vibrate } returns 1
        every { mRings.containsKey(ChangedAlarmInfo.calculateAlarmTimeStamp(mCalendar, mAlarm)) } returns false
        val mUri = Uri.parse("file://ss/")
        every { mAlarm.alert } returns mUri
        mCalendar.set(2022, 4, 3)
        mockkObject(ChangedAlarmInfo.Companion)
        every { ChangedAlarmInfo.calculateAlarmTimeStamp(mCalendar, mAlarm) } returns 1000L

        mockkStatic(LegalHolidayUtil::class)
        val holiday = LegalHolidayUtil.Holiday()
        holiday.colorType = LegalHolidayUtil.COLOR_TYPE_HOLIDAY
        holiday.year = mCalendar.get(Calendar.YEAR)
        holiday.yearOfDay = mCalendar.get(Calendar.DAY_OF_YEAR)
        val holidayList = mutableListOf<LegalHolidayUtil.Holiday>()
        holidayList.add(holiday)
        val mAlarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        every { LegalHolidayUtil.getHolidayFromCache(mCalendar) } returns holidayList
        //When
        val b = ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "isEffectiveAlarm",
            arrayOf<Any>(mContext, mCalendar, mAlarmInfo, mRings),
            AlarmNotRingCheckUtils,
            Context::class.java,
            Calendar::class.java,
            ChangedAlarmInfo::class.java,
            Map::class.java
        ) as kotlin.Pair<Int, AlarmNotRingCheckUtils.StatisticData?>
        //Then
        Assert.assertFalse(AlarmNotRingCheckUtils.isEffectiveAlarmCode(b.first))
    }

    @Test
    @Ignore
    @Throws(InterruptedException::class)
    fun should_return_false_when_rings_contains_Ring_Key() {
        //Given
        val curTimeZoneId = TimeZone.getDefault().id
        val mCalendar = mockk<Calendar>()
        val mAlarm = mockk<Alarm>()
        val mRings = mutableMapOf<Long, Alarm>()
        mRings[1000L] = mAlarm
        mRings[6000L] = mAlarm
        spyk(mRings)
        val instance = spyk(AlarmPreferenceUtils.instance)
        mockkStatic(AlarmRingStatisticUtils::class)
        every { mAlarm.repeatSet } returns 0
        every { mAlarm.workdaySwitch } returns 0
        every { mAlarm.getmCloseOnceTimeNext() } returns 7000L
        every { mAlarm.getmCloseOncePriTime() } returns 5000L
        every { mAlarm.id } returns 1
        every { mAlarm.isEnabled } returns true
        every { mAlarm.hour } returns 1
        every { mAlarm.minutes } returns 20
        every { mAlarm.ringName } returns "无"
        every { mAlarm.label } returns "Labal"
        every { mAlarm.holidaySwitch } returns 5
        every { mAlarm.vibrate } returns 1
        every { instance.getRingInfo(curTimeZoneId) } returns mRings
        every { mAlarm.alert } returns null
        every { mCalendar.timeInMillis } returns 6000L
        every { mCalendar.timeZone } returns mockk()
        mockkObject(ChangedAlarmInfo.Companion)
        every { ChangedAlarmInfo.calculateAlarmTimeStamp(mCalendar, mAlarm) } returns 1000L
        every { ChangedAlarmInfo.timeStampToTimeStr(any(), any()) } returns "2022:04:03"
        mockkStatic(LegalHolidayUtil::class)
        val mAlarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        //When
        val b = ReflectUtil.invoke(
            AlarmNotRingCheckUtils::class.java,
            "isEffectiveAlarm",
            arrayOf<Any>(mContext, mCalendar, mAlarmInfo, mRings),
            AlarmNotRingCheckUtils,
            Context::class.java,
            Calendar::class.java,
            ChangedAlarmInfo::class.java,
            Map::class.java
        ) as kotlin.Pair<Int, AlarmNotRingCheckUtils.StatisticData?>
        //Then
        Assert.assertFalse(AlarmNotRingCheckUtils.isEffectiveAlarmCode(b.first))
    }
}