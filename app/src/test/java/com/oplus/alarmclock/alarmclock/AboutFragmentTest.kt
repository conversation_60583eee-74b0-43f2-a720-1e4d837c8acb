/************************************************************
 * Copyright 2010-2022 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
Description : The Main activity  fragment for the about clock
 * History :( ID, Date, Author, Description)
 * v1.0, 2022-5-25, den<PERSON><PERSON><PERSON>, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock

import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.IntentSender.SendIntentException
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentHostCallback
import com.oplus.alarmclock.AlarmClock
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil
import com.oplus.alarmclock.shadows.ShadowUtils
import org.junit.Assert
import org.junit.Test
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@Config(shadows = [ShadowColorDarkModeUtil::class, ShadowUtils::class])
class AboutFragmentTest : TestParent() {
    var mFragment: AboutFragment? = null

    @Test
    @Throws(
            NoSuchFieldException::class,
            IllegalAccessException::class,
            NoSuchMethodException::class,
            ClassNotFoundException::class
    )
    fun should_getValue_equalsTo_value_when_onPreferenceChange_with_specific_preference_in_prefs_and_value() {
        //init fragment
        mFragment = AboutFragment()
        val activity: FragmentActivity = Robolectric.buildActivity(
                AlarmClock::class.java
        ).create().resume().get()
        val host: FragmentHostCallback<*> =
                object : FragmentHostCallback<Any?>(activity, Handler(), 0) {
                    override fun onGetHost(): Any? {
                        return null
                    }

                    override fun onStartActivityFromFragment(
                        fragment: Fragment,
                        intent: Intent,
                        requestCode: Int,
                        options: Bundle?
                    ) {
                        fragment.activity!!
                                .startActivityFromFragment(fragment, intent, requestCode, options)
                    }

                    @Throws(SendIntentException::class)
                    override fun onStartIntentSenderFromFragment(
                        fragment: Fragment,
                        intent: IntentSender,
                        requestCode: Int,
                        fillInIntent: Intent?,
                        flagsMask: Int,
                        flagsValues: Int,
                        extraFlags: Int,
                        options: Bundle?
                    ) {
                        fragment.activity!!.startIntentSenderFromFragment(
                                fragment, intent, requestCode,
                                fillInIntent, flagsMask, flagsValues, extraFlags, options
                        )
                    }
                }
        val fragmentManager = activity.supportFragmentManager
        ReflectUtil.setFieldValue(
                Fragment::class.java, "mFragmentManager",
                mFragment, fragmentManager
        )
        ReflectUtil.setFieldValue(Fragment::class.java, "mHost", mFragment, host)
        ReflectUtil.invoke(Fragment::class.java, "performAttach", null, mFragment)
        ReflectUtil.invoke(
                Fragment::class.java, "performCreate", arrayOf(null),
                mFragment, Bundle::class.java
        )
        val inflater = mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        ReflectUtil.invoke(
                Fragment::class.java,
                "performCreateView",
                arrayOf<Any?>(inflater, null, null),
                mFragment,
                LayoutInflater::class.java,
                ViewGroup::class.java,
                Bundle::class.java
        )
        ReflectUtil.invoke(
                Fragment::class.java,
                "onCreateView",
                arrayOf<Any?>(inflater, null, null),
                mFragment,
                LayoutInflater::class.java,
                ViewGroup::class.java,
                Bundle::class.java
        )
        ReflectUtil.invoke(
                Fragment::class.java,
                "onCreateView",
                arrayOf<Any?>(inflater, null, null),
                mFragment,
                LayoutInflater::class.java,
                ViewGroup::class.java,
                Bundle::class.java
        )
        ReflectUtil.invoke(
                Fragment::class.java,
                "onStart",
                arrayOf<Any?>(),
                mFragment,
        )
        ReflectUtil.invoke(
                Fragment::class.java,
                "onResume",
                arrayOf<Any?>(),
                mFragment,
        )

        val mDoubleClickHelper =
                ReflectUtil.getFieldValue(AboutFragment::class.java, "mDoubleClickHelper", mFragment)
        Assert.assertNotNull(mDoubleClickHelper)
    }
}