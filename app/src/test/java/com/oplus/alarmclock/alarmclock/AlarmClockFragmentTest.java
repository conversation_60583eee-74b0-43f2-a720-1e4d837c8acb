/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-13, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.res.ColorStateList;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.progressbar.COUILoadingView;
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton;
import com.coui.appcompat.bottomnavigation.COUINavigationView;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.imageview.ShapeableImageView;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockTest;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.RuntimePermissionAlert;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.adapter.AlarmListAdapter;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.oplus.alarmclock.utils.FloatingButtonTool;
import com.oplus.anim.EffectiveAnimationView;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowToast;
import org.xmlpull.v1.XmlPullParserException;

import java.io.FileNotFoundException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.COUIRecyclerView;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Ignore
@Config(shadows = {AlarmClockFragmentTest.ShadowColorDarkModeUtil.class, AlarmClockTest.ShadowPrivacyPolicyAlert.class,
        ShadowGetInitialDisplayDensityUtil.class})
public class AlarmClockFragmentTest extends TestParent {
    AlarmClockFragment mFragment;
    AlarmClock mAlarmClock;
    final long MENU_CLICK_DURATION = DoubleClickHelper.MENU_CLICK_DURATION;
    COUIRecyclerView mAlarmsList;
    COUILoadingView mLoadingView;
    TextView mEmptyTextView;
    View mDividerLine;
    private DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mFragment = new AlarmClockFragment();

        mAlarmClock = Robolectric.buildActivity(AlarmClock.class).create().resume().get();
//        mAlarmClock.initBottomMainTool();
        FragmentHostCallback host = new FragmentHostCallback(mAlarmClock, new Handler(), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }

            @Override
            public void onStartActivityFromFragment(@NonNull Fragment fragment, Intent intent,
                                                    int requestCode, @Nullable Bundle options) {
                fragment.getActivity().startActivityFromFragment(fragment, intent, requestCode, options);
            }

            @Override
            public void onStartIntentSenderFromFragment(
                    @NonNull Fragment fragment, IntentSender intent, int requestCode,
                    @Nullable Intent fillInIntent, int flagsMask, int flagsValues,
                    int extraFlags, Bundle options) throws IntentSender.SendIntentException {
                fragment.getActivity().startIntentSenderFromFragment(fragment, intent, requestCode,
                        fillInIntent, flagsMask, flagsValues, extraFlags, options);
            }
        };
        FragmentManager fragmentManager = mAlarmClock.getSupportFragmentManager();
        ReflectUtil.setFieldValue(Fragment.class, "mFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mChildFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        ReflectUtil.invoke(Fragment.class, "performCreate", new Object[]{null},
                mFragment, Bundle.class);
        ReflectUtil.invoke(Fragment.class, "performResume", null, mFragment);
        //init view
        View rootView = mock(View.class);
        mAlarmsList = mock(COUIRecyclerView.class);
        mLoadingView = mock(COUILoadingView.class);
        mEmptyTextView = mock(TextView.class);
        mDividerLine = mock(View.class);
        when(mDividerLine.getLayoutParams()).thenReturn(mock(ViewGroup.LayoutParams.class));
        when(rootView.findViewById(android.R.id.list)).thenReturn(mAlarmsList);
        when(rootView.findViewById(R.id.loadingView)).thenReturn(mLoadingView);
        when(rootView.findViewById(android.R.id.empty)).thenReturn(mEmptyTextView);
        when(rootView.findViewById(R.id.divider_line)).thenReturn(mDividerLine);
//        mFragment.initView(rootView);

        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        COUIRecyclerView mAlarmsList = mock(COUIRecyclerView.class);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mAlarmsList", mFragment, mAlarmsList);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mLoadingView", mFragment, mLoadingView);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mDoubleClickHelper", mFragment, mDoubleClickHelper);
    }

    @Test
    public void should_call_notifyItemChanged_when_onItemClick_with_alarmList_size_greater_than_positon_and_mMode_is_MODEL_EDIT() throws NoSuchMethodException, IllegalAccessException, ClassNotFoundException, FileNotFoundException, XmlPullParserException, NoSuchFieldException {
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        mListAdapter = spy(mListAdapter);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        int listSize = 5;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);

        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mMode", mFragment, AlarmClockFragment.MODEL_EDIT);

        int position = 0;
        //invoke onItemClick()
        mFragment.alarmListItemClick(position);
        //verify
        verify(mListAdapter).notifyItemChanged(position);
    }

    @Test
    @Ignore
    public void should_call_resetCheckingPermissions_when_onItemClick_with_mMode_is_MODEL_NORMAL_and_mActionForItemClick_is_true() throws NoSuchMethodException, IllegalAccessException, ClassNotFoundException, FileNotFoundException, XmlPullParserException, NoSuchFieldException {
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        mListAdapter = spy(mListAdapter);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        int listSize = 1;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        long id = 10L;
        alarm.setId(id);
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);

        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mMode", mFragment, AlarmClockFragment.MODEL_NORMAL);
        boolean actionForItemClick = true;
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mActionForItemClick", mFragment, actionForItemClick);
        int position = 0;

        RuntimePermissionAlert runtimePermissionAlert = mock(RuntimePermissionAlert.class);
        ReflectUtil.setFieldValue(AlarmClock.class, "mRuntimePermissionAlert", mAlarmClock, runtimePermissionAlert);

        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));

        //invoke onItemClick()
        mFragment.onItemClick(position);
        //assert
        verify(runtimePermissionAlert).resetCheckingPermissions();
    }


    @Test
    @Ignore
    public void should_call_changeMode_with_MODEL_EDIT_when_onItemLongClick_with_mMode_is_MODEL_NORMAL_and_listSize_lessThan_position() throws NoSuchMethodException, IllegalAccessException, ClassNotFoundException, FileNotFoundException, XmlPullParserException, NoSuchFieldException {

        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        mListAdapter = spy(mListAdapter);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        int listSize = 1;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        long id = 10L;
        alarm.setId(id);
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mMode", mFragment, AlarmClockFragment.MODEL_NORMAL);
        boolean actionForItemClick = true;
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mActionForItemClick", mFragment, actionForItemClick);
        int position = 0;
        mFragment = Mockito.spy(mFragment);
        //invoke onItemClick()
        mFragment.onItemLongClick(position);
        //verify
        verify(mListAdapter).changeMode(AlarmClockFragment.MODEL_EDIT);
    }


    @Test
    @Ignore
    public void should_call_setIntent_with_listSize_when_openModelView_with_R_id_add_and_listSize_lessThan_MAX_ALARM_COUNT() throws IllegalAccessException, NoSuchFieldException {

        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        //ensure canClick() return true
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));

        int listSize = 1;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        long id = 10L;
        alarm.setId(id);
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);

        //invoke openModelView()
        mFragment.openModelView(false);
        //verify
        Intent intent = mAlarmClock.getIntent();
        assertEquals(-1L, intent.getLongExtra(ClockConstant.ALARM_ID, 0L));
        assertEquals(listSize, intent.getIntExtra(ClockConstant.ALARM_COUNT, -1));
    }

    @Test
    @Ignore
    public void should_show_Toast_with_String_add_alarm_limit_when_openModelView_with_R_id_add_and_listSize_equalsTo_MAX_ALARM_COUNT()
            throws IllegalAccessException, NoSuchFieldException {
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        //ensure canClick() return true
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));
        //ensure mListAdapter.getItemCount() < MAX_ALARM_COUNT not satisfied
        int listSize = AlarmClockFragment.MAX_ALARM_COUNT;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);

        //invoke openModelView()
        mFragment.openModelView(false);
        //verify
        assertEquals(mContext.getString(R.string.add_alarm_limit), ShadowToast.getTextOfLatestToast());
    }


    @Test
    @Ignore
    public void should_call_mListAdapter_changeMode_with_MODEL_EDIT_when_changeMode_with_mMode_is_MODEL_EDIT_and_getActivity_return_instanceof_AlarmClock()
            throws IllegalAccessException, NoSuchFieldException, NoSuchMethodException {
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        mListAdapter = spy(mListAdapter);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        //ensure canClick() return true
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));

        int listSize = 1;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        long alarmId = 10L;
        alarm.setId(alarmId);
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);
        //invoke changeMode()
        ReflectUtil.invoke(AlarmClockFragment.class, "changeMode",
                new Object[]{AlarmClockFragment.MODEL_EDIT, alarmId}, mFragment, int.class, long.class);
        //verify
        verify(mListAdapter).changeMode(AlarmClockFragment.MODEL_EDIT);
        verify(mListAdapter).clearAlarmSelect(list, alarmId);
    }

    @Test
    @Ignore
    public void should_call_setIcon_with_color_menu_ic_add_disabled_and_call_setEnabled_with_true_when_changeMode_with_listSize_equalsTo_MAX_ALARM_COUNT_and_isAdded_return_true()
            throws IllegalAccessException, NoSuchFieldException, NoSuchMethodException {
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        //ensure canClick() return true
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));

        int listSize = AlarmClockFragment.MAX_ALARM_COUNT;
        List<Alarm> list = new ArrayList(listSize);
        Alarm alarm = new Alarm();
        long alarmId = 10L;
        alarm.setId(alarmId);
        for (int i = 0; i < listSize; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);

        COUIFloatingButton couiFloatingButton = mock(COUIFloatingButton.class);
        when(couiFloatingButton.getMainFloatingButton()).thenReturn(mock(ShapeableImageView.class));
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mCouiFloatingButton", mFragment, couiFloatingButton);

        FloatingButtonTool floatingButtonTool = new FloatingButtonTool();
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mFloatingButtonTool", mFragment, floatingButtonTool);

        ReflectUtil.setFieldValue(BaseFragment.class, "mContext", mFragment, mContext);

        MenuItem mEditItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(Fragment.class, "mAdded", mFragment, true);
        //invoke changeMode()
        ReflectUtil.invoke(AlarmClockFragment.class, "changeMode", new Object[] {AlarmClockFragment.MODEL_NORMAL, (long)AlarmClockFragment.NO_ALARM_ID}, mFragment, int.class, long.class);
        //verify
        verify(couiFloatingButton).setMainFloatingButtonBackgroundColor(ColorStateList.valueOf(mContext.getColor(R.color.main_floating_button_bg_color)));
    }

    @Test
    @Ignore
    public void should_call_setSelected_with_true_which_id_lessThan_midIndex_when_onEditAlarmsLoadComplete_with_mSelectedIds_contains_id_lessThan_midIndex()
            throws IllegalAccessException, NoSuchFieldException {
        when(mLoadingView.getVisibility()).thenReturn(View.VISIBLE);
        AlarmListAdapter mListAdapter = (AlarmListAdapter) ReflectUtil.getFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment);
        //ensure canClick() return true
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -(MENU_CLICK_DURATION * 2));

        int listSize = 10;
        int midIndex = listSize / 2;
        ArrayList<Alarm> list = new ArrayList(listSize);
        Set<Long> mSelectedIds = new HashSet();
        for (int i = 0; i < listSize; i++) {
            Alarm alarm = new Alarm();
            alarm.setId(i);
            list.add(alarm);
            if (i < midIndex) {
                mSelectedIds.add((long) i);
            }
        }
        ReflectUtil.setFieldValue(AlarmListAdapter.class, "mSelectedIds", mListAdapter, mSelectedIds);
        mListAdapter.updateData(list);
        MenuItem item = mock(MenuItem.class);
        when(item.getItemId()).thenReturn(R.id.add);
        //invoke onEditAlarmsLoadComplete()
        mFragment.onEditAlarmsLoadComplete(mContext, list, AlarmClockFragment.NO_ALARM_ID);
        //verify && assert
        for (int i = 0; i < listSize; i++) {
            if (i < midIndex) {
                assertTrue(list.get(i).isSelected());
            } else {
                assertFalse(list.get(i).isSelected());
            }
        }
        verify(mLoadingView).setVisibility(View.GONE);
    }

    @Test
    @Ignore
    public void should_no_exception_when_call_all_nonPrivate_method_with_illegal_args() throws IllegalAccessException {
        boolean isClassObject = false;
        ReflectUtil.NameFilter filter = new ReflectUtil.NameFilter();
        filter.addMethodName("onItemLongClick");
        filter.addMethodName("onCheckedChanged");
        filter.addMethodName("getTitle");
        filter.addMethodName("onEditAlarmsLoadComplete");
        filter.addMethodName("initializeFloatingButton");
        Map<Method, Throwable> map = (Map<Method, Throwable>) ReflectUtil.invokeMethodsWithIllegalArgs(
                mFragment, isClassObject, null, filter);
//        assertTrue(map.size() != 0);
    }

    @Implements(COUIDarkModeUtil.class)
    public static class ShadowColorDarkModeUtil {
        @Implementation
        public static void setForceDarkAllow(View view, boolean allow) {
        }

        public static boolean isNightMode(Context context) {
            return false;
        }
    }

    @Test
    @Ignore
    public void should_MenuItem_enabled_when_updateEditMenuAndTitle_adapter_mSelectedIds_isNot_NULL() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mMode", mFragment, AlarmClockFragment.MODEL_EDIT);
        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);

        List<Alarm> alarmList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Alarm alarm = Alarm.build(false, 0, 0, 1, 0, "",
                    null, "", 0, 0, 0, 0);
            alarm.setId(i);
            alarmList.add(alarm);
        }

        mListAdapter.updateData(alarmList);
        mListAdapter.setAlarmSelected(0, true);
        mListAdapter.setAlarmSelected(1, true);

        MenuItem deleteMenuItem = mock(MenuItem.class);
        COUINavigationView navigationView = mock(COUINavigationView.class);
        Menu menu = mock(Menu.class);
        when(navigationView.getMenu()).thenReturn(menu);
        when(menu.findItem(R.id.navigation_delete)).thenReturn(deleteMenuItem);
        ReflectUtil.setFieldValue(AlarmClock.class, "mNavigationTool", mAlarmClock, navigationView);

        ReflectUtil.setFieldValue(Fragment.class, "mAdded", mFragment, true);

        //invoke
        ReflectUtil.invoke(AlarmClockFragment.class, "updateEditMenuAndTitle", null, mFragment);

        //verify
        verify(deleteMenuItem).setEnabled(true);
    }

    @Test
    @Ignore
    public void should_MenuItem_enabled_when_updateEditMenuAndTitle_adapter_mSelectedIds_is_NULL() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mMode", mFragment, AlarmClockFragment.MODEL_EDIT);
        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);

        List<Alarm> alarmList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Alarm alarm = Alarm.build(false, 0, 0, 1, 0, "",
                    null, "", 0, 0, 0, 0);
            alarm.setId(i);
            alarmList.add(alarm);
        }

        mListAdapter.updateData(alarmList);

        MenuItem deleteMenuItem = mock(MenuItem.class);
        COUINavigationView navigationView = mock(COUINavigationView.class);
        Menu menu = mock(Menu.class);
        when(navigationView.getMenu()).thenReturn(menu);
        when(menu.findItem(R.id.navigation_delete)).thenReturn(deleteMenuItem);
        ReflectUtil.setFieldValue(AlarmClock.class, "mNavigationTool", mAlarmClock, navigationView);

        ReflectUtil.setFieldValue(Fragment.class, "mAdded", mFragment, true);

        //invoke
        ReflectUtil.invoke(AlarmClockFragment.class, "updateEditMenuAndTitle", null, mFragment);

        //verify
        verify(deleteMenuItem).setEnabled(false);
    }

    @Test
    @Ignore
    public void should_change_order_when_updateList_with_updatePosition() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        int oldPosition = 4;
        int updatePosition = 5;
        List<Alarm> alarmList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Alarm alarm = Alarm.build(false, 0, 0, 1, 0, "",
                    null, "", 0, 0, 0, 0);
            alarm.setId(i);
            alarm.setLabel("Clock" + i);
            alarmList.add(alarm);
        }
        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        mListAdapter.updateData(alarmList);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);

        String clockLabel = "Clock";
        Alarm currentAlarm = Alarm.build(false, 0, 0, 1, 0, clockLabel,
                null, "", 0, 0, 0, 0);
        currentAlarm.setId(oldPosition);
        alarmList.set(oldPosition, currentAlarm);
        Collections.swap(alarmList, oldPosition, updatePosition);

        mListAdapter.updateData(alarmList);

        List<Alarm> alarmList2 = mListAdapter.getList();
        Alarm alarm = alarmList2.get(updatePosition);

        assertEquals(alarm.getLabel(), clockLabel);

    }

    @Test
    @Ignore
    public void should_show_Toast_with_String_add_alarm_limit_when_openModelView_with_listSize_not_less_than_MAX_ALARM_COUNT2()
            throws IllegalAccessException, NoSuchFieldException {
        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);
        COUIRecyclerView mAlarmsList = mock(COUIRecyclerView.class);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mAlarmsList", mFragment, mAlarmsList);

        List<Alarm> list = new ArrayList<>();
        Alarm alarm = new Alarm();
        for (int i = 0; i < AlarmClockFragment.MAX_ALARM_COUNT; i++) {
            list.add(alarm);
        }
        mListAdapter.updateData(list);
        mFragment.openModelView(false);

        //verify
        assertEquals(mContext.getString(R.string.add_alarm_limit), ShadowToast.getTextOfLatestToast());
    }

    @Test
    @Ignore
    public void should_EmptyTextView_VISIBLE_when_onAlarmsLoadComplete_with_alarm_list_isEmpty() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        ArrayList<Alarm> list = new ArrayList<>();
        Context context = spy(mContext);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mEmptyTextView", mFragment, mEmptyTextView);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mAlarmsList", mFragment, mAlarmsList);
        AlarmListAdapter mListAdapter = new AlarmListAdapter(mAlarmClock);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mListAdapter", mFragment, mListAdapter);

        EffectiveAnimationView mViewEmpty = mock(EffectiveAnimationView.class);
        ReflectUtil.setFieldValue(AlarmClockFragment.class, "mViewEmpty", mFragment, mViewEmpty);

        ReflectUtil.invoke(AlarmClockFragment.class, "onAlarmsLoadComplete", new Object[]{context, list, AlarmClockFragment.NO_ALARM_ID},
                mFragment, Context.class, ArrayList.class, int.class);

        Shadows.shadowOf(Looper.getMainLooper()).idle();

        verify(mEmptyTextView).setVisibility(View.VISIBLE);
    }

}
