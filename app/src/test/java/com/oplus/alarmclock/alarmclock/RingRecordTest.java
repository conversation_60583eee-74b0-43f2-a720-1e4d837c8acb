/************************************************************
 * Copyright 2010-2022 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description :
 * History :( ID, Date, Author, Description)
 * v1.0, 2022-02-09, niexiaokang, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.mvvm.ringrecord.RingRecordUtils;
import com.oplus.alarmclock.mvvm.ringrecord.RingRecordViewModel;
import com.oplus.alarmclock.provider.alarmring.AlarmRing;
import com.oplus.alarmclock.utils.ReflectUtilsKt;
import com.oplus.alarmclock.utils.Utils;
import com.coloros.widget.smallweather.TimeInfoBuilder;

import org.junit.Test;

import java.util.Calendar;

import androidx.test.platform.app.InstrumentationRegistry;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

public class RingRecordTest extends TestParent {
    @Test
    public void should_return_true_when_time_is_this_year_with_year() {
        String date1 = "2020";
        boolean isCurrentYear1 = RingRecordUtils.isCurrentYear(date1);
        assertFalse(isCurrentYear1);
    }

    @Test
    public void should_return_true_when_time_is_today_with_stepTime() {
        AlarmRing alarmRing1 = new AlarmRing();
        AlarmRing alarmRing2 = new AlarmRing();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        alarmRing1.setStepTime(calendar.getTimeInMillis());
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - 1);
        alarmRing2.setStepTime(calendar.getTimeInMillis());
        boolean isToday1 = RingRecordUtils.isToday(alarmRing1.getStepTime());
        boolean isToday2 = RingRecordUtils.isToday(alarmRing2.getStepTime());
        assertTrue(isToday1);
        assertFalse(isToday2);
    }

    @Test
    public void should_return_today_when_time_is_today_with_stepTime() {
        AlarmRing alarmRing = new AlarmRing();
        alarmRing.setStepTime(System.currentTimeMillis());
        RingRecordViewModel model = new RingRecordViewModel();
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        TimeInfoBuilder builder = new TimeInfoBuilder(context);
        Class<?>[] clsArr = {AlarmRing.class, TimeInfoBuilder.class};
        String str = (String) ReflectUtilsKt.invoke(RingRecordViewModel.class, "getTitleText", clsArr, model, alarmRing, builder);
        assertNotNull(str);
        String[] strings = context.getResources().getStringArray(R.array.global_timezone_day_offset);
        String today = strings[1];
        boolean contain = str.contains(today);
        assertTrue(contain);
    }

    @Test
    public void should_return_hashcode_when_app_is_release() {
        String versionName = Utils.getVersionName();
        String[] splitArr = versionName.split("_");
        assertNotNull(splitArr);
//        boolean size = splitArr.length >= 2;
//        assertTrue(size);
    }
}