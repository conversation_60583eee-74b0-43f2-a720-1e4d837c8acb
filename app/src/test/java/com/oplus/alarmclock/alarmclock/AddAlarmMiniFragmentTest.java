/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-30, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import static android.os.Looper.getMainLooper;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.robolectric.annotation.LooperMode.Mode.PAUSED;

import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;

import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.mini.AddAlarmMiniActivity;
import com.oplus.alarmclock.alarmclock.mini.AddAlarmMiniFragment;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.utils.StringUtils;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.LooperMode;

import java.util.concurrent.TimeUnit;


@Config(shadows = {ShadowColorDarkModeUtil.class, ShadowUtils.class})
@LooperMode(PAUSED)
public class AddAlarmMiniFragmentTest extends TestParent {
    AddAlarmMiniFragment mFragment;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        Intent intent = new Intent();
        long alarmId = -1;
        intent.putExtra(ClockConstant.ALARM_ID, alarmId);
        FragmentActivity activity = Robolectric.buildActivity(AddAlarmMiniActivity.class, intent).create().resume().get();
        FragmentHostCallback host = new FragmentHostCallback(activity, new Handler(), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }

            @Override
            public void onStartActivityFromFragment(@NonNull Fragment fragment, Intent intent,
                                                    int requestCode, @Nullable Bundle options) {
                fragment.getActivity().startActivityFromFragment(fragment, intent, requestCode, options);
            }

            @Override
            public void onStartIntentSenderFromFragment(
                    @NonNull Fragment fragment, IntentSender intent, int requestCode,
                    @Nullable Intent fillInIntent, int flagsMask, int flagsValues,
                    int extraFlags, Bundle options) throws IntentSender.SendIntentException {
                fragment.getActivity().startIntentSenderFromFragment(fragment, intent, requestCode,
                        fillInIntent, flagsMask, flagsValues, extraFlags, options);
            }
        };
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        mFragment = new AddAlarmMiniFragment();
        ReflectUtil.setFieldValue(Fragment.class, "mFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        ReflectUtil.invoke(Fragment.class, "performAttach", null, mFragment);
        ReflectUtil.invoke(Fragment.class, "performCreate", new Object[]{null},
                mFragment, Bundle.class);
        LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        ReflectUtil.invoke(Fragment.class, "performCreateView", new Object[]{inflater, null, null}, mFragment,
                LayoutInflater.class, ViewGroup.class, Bundle.class);
        ReflectUtil.invoke(Fragment.class, "performResume", null, mFragment);

        AlarmClockApplication sInstance = spy(AlarmClockApplication.getInstance());
        Resources resources = spy(mContext.getResources());
        String[] strArr = new String[]{"one", "two", "three"};
        doReturn(strArr).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_OLD(), "array", "oplus"));
        doReturn(strArr).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_NEW(), "array", "oplus"));
        when(sInstance.getResources()).thenReturn(resources);
        ReflectUtil.setFieldValue(AlarmClockApplication.class, "sInstance", null, sInstance);
        Shadows.shadowOf(getMainLooper()).idleFor(1000, TimeUnit.MILLISECONDS);
    }

    @Config(shadows = {AddAlarmFragmentTest.ShadowDeviceUtils.class})
    @Test
    public void should_call_confirm_save_alarm()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmFragmentTest.ShadowDeviceUtils.sIsExpVersion = false;
        Alarm alarmTemp = new Alarm();
        alarmTemp.setSnoonzeItem(ClockConstant.SNOOZE_SWITCH_OFF);
        ReflectUtil.setFieldValue(AddAlarmMiniFragment.class, "mAlarm", mFragment, alarmTemp);
        ReflectUtil.setFieldValue(AddAlarmMiniFragment.class, "mIsNewAlarm", mFragment, true);
        View view = mFragment.getView().getRootView().findViewById(R.id.alarm_mini_save_alarm);
        mFragment.onClick(view);
        //assert
        assertEquals(alarmTemp.getDeleteAfterUse(), 0);
    }

    @Config(shadows = {AddAlarmFragmentTest.ShadowDeviceUtils.class})
    @Test
    public void should_call_init_alarm_when_alarm_id_is_zero()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmFragmentTest.ShadowDeviceUtils.sIsExpVersion = false;
        Alarm alarmTemp = new Alarm();
        alarmTemp.setSnoonzeItem(ClockConstant.SNOOZE_SWITCH_OFF);
        ReflectUtil.setFieldValue(AddAlarmMiniFragment.class, "mIsNewAlarm", mFragment, true);
        mFragment.initData();
        assertEquals(mFragment.getAlarm().getWorkdaySwitch(), 0);
    }

    @Config(shadows = {AddAlarmFragmentTest.ShadowDeviceUtils.class})
    @Test
    public void should_call_openMoreSetting_when_alarm_is_close()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmFragmentTest.ShadowDeviceUtils.sIsExpVersion = false;
        Alarm alarmTemp = new Alarm();
        alarmTemp.setSnoonzeItem(ClockConstant.SNOOZE_SWITCH_OFF);
        ReflectUtil.setFieldValue(AddAlarmMiniFragment.class, "mAlarm", mFragment, alarmTemp);
        View view = mFragment.getView().getRootView().findViewById(R.id.more_setting_layout);
        mFragment.onClick(view);
        assertEquals(mFragment.getAlarm().getSnoonzeItem(), 0);
    }

}
