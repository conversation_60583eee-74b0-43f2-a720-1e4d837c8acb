/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-29, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.os.Parcel;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
public class AlarmRepeatTest extends TestParent {

    final int[] data = new int[]{10,1,2,3,4};
    @Override
    public void setUp()throws Exception{
        super.setUp();
    }

    @Test
    public void testAlarmRepeat(){
        Parcel parcel = Parcel.obtain();
        parcel.writeLong(data[0]);
        parcel.writeInt(data[1]);
        parcel.writeInt(data[2]);
        parcel.writeInt(data[3]);
        parcel.writeInt(data[4]);
        parcel.setDataPosition(0);
        AlarmRepeat repeat = new AlarmRepeat(parcel){
        };
        assertEquals(data[0],repeat.getmId());
        assertEquals(data[1],repeat.getmAlarmDuration());
        assertEquals(data[2],repeat.getmAlarmInterval());
        assertEquals(data[3],repeat.getmAlarmNum());
        assertEquals(data[4],repeat.getmAlarmPrompt());
    }

    @Test
    public void testCreateFromParcel(){
        Parcel parcel = Parcel.obtain();
        parcel.writeLong(data[0]);
        parcel.writeInt(data[1]);
        parcel.writeInt(data[2]);
        parcel.writeInt(data[3]);
        parcel.writeInt(data[4]);
        parcel.setDataPosition(0);
        assertNotNull(AlarmRepeat.CREATOR.createFromParcel(parcel));
    }

    @Test
    public void testWriteToParcel(){
        Parcel parcel = Parcel.obtain();
        AlarmRepeat repeat = AlarmRepeat.build(data[0],data[1],data[2],data[3],data[4]);
        repeat.writeToParcel(parcel, 0);
        assertEquals(data[0],repeat.getmId());
        assertEquals(data[1],repeat.getmAlarmDuration());
        assertEquals(data[2],repeat.getmAlarmInterval());
        assertEquals(data[3],repeat.getmAlarmNum());
        assertEquals(data[4],repeat.getmAlarmPrompt());
    }

}
