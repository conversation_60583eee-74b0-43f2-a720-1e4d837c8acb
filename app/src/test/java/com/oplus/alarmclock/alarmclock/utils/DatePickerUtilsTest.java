package com.oplus.alarmclock.alarmclock.utils;

import static org.junit.Assert.assertEquals;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.Calendar;


public class DatePickerUtilsTest extends TestParent {

    public void setUp() throws Exception {
        super.setUp();
    }

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    public  void should_time_now_getTimeForAfter1970_with_return_time_can(){
        Calendar can = DatePickerUtils.getTimeForAfter1970(19103);
        long days = DatePickerUtils.todayAfter1970days(can);
        assertEquals(days,19103);
    }



}