/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-30, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.PowerManager;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;
import org.robolectric.Shadows;
import org.robolectric.shadows.ShadowLooper;

import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AlarmAlertWakeLockTest extends TestParent {

    static final int ACQUIRE_WAKELOCK_DIM = 3;
    @Override
    public void setUp()throws Exception{
        super.setUp();
    }

    @Test
    public void should_acquice_wakeLock_and_release_wakeLock_after_timeout_when_handleMessage_with_what_is_ACQUIRE_WAKELOCK_DIM_and_sCpuWakeLock_isNull_and_alarmLength_is_one() throws NoSuchFieldException, IllegalAccessException {
        mContext = spy(mContext);
        Handler sHandler = (Handler) ReflectUtil.getFieldValue(AlarmAlertWakeLock.class, "sHandler", null);
        int alarmLength = 1;
        Message msg = Message.obtain();
        msg.what = ACQUIRE_WAKELOCK_DIM;
        msg.obj = mContext;
        msg.arg1 = alarmLength;
        ReflectUtil.setFieldValue(AlarmAlertWakeLock.class, "sCpuWakeLock", null, null);
        //invoke handleMessage()
        sHandler.handleMessage(msg);
        PowerManager.WakeLock wakeLock = (PowerManager.WakeLock) ReflectUtil.getFieldValue(AlarmAlertWakeLock.class, "sCpuWakeLock", null);
        //assert
        assertTrue(wakeLock.isHeld());
        int timeout = 70000;
        ShadowLooper shadowLooper = Shadows.shadowOf(Looper.getMainLooper());
        shadowLooper.idleFor(timeout, TimeUnit.MILLISECONDS);
        //assert
        assertFalse(wakeLock.isHeld());
    }

}
