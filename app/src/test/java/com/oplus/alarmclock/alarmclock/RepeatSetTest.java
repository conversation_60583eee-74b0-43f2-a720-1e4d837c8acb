/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description :
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-12-13, yuxiaolong, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.content.Context;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.utils.DeviceUtils;

import org.junit.Assert;
import org.junit.Test;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implements;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.spy;

import java.util.Calendar;

@Config(shadows = RepeatSetTest.ShadowDeviceUtils.class)
public class RepeatSetTest extends TestParent {
    private static long ONE_DAY = 1000 * 60 * 60 * 24;

    @Test
    public void should_turn_alarm_day_right_when_getDescription_with_repeatSet_workdaySwitch_holidaySwitch() {

        //init
        final Context spyContext = spy(mContext);

        int repeatSet = RepeatSet.REPEAT_DAILY;
        int workdaySwitch = Math.random() > 0.5 ? 1 : 0;
        int holidaySwitch = 1;
        boolean showNever = false;
        Alarm alarm = new Alarm();
        //invoke
        String result = RepeatSet.getDescription(spyContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm,true);

        //assert
        if (workdaySwitch == 1) {
            assertEquals(result, spyContext.getString(R.string.oplus_workday_switch));
        } else {
            String[] day = spyContext.getResources().getStringArray(R.array.alarm_repeat_strings_2);
            String space = spyContext.getText(R.string.day_concat).toString();
            String resultEquals = day[1] + space + spyContext.getString(R.string.exclude_holiday);
            assertEquals(result, resultEquals);
        }
    }

    @Test
    public void should_return_string_when_getDescription_with_context_is_null() {
        int repeatSet = 0; //ring once
        int workdaySwitch = 0;
        int holidaySwitch = 1;
        boolean showNever = false;
        boolean showAll = true;
        Alarm alarm = new Alarm();

        String result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(mContext.getString(R.string.ring_once), result);

        result = RepeatSet.getDescription(null, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(mContext.getString(R.string.ring_once), result);
    }

    @Test
    public void should_return_workdayTypeString_when_getDescription_with_workdaySwitch_is_1() {
        int repeatSet = 0; //ring once
        int workdaySwitch = 1;
        int holidaySwitch = 1;
        boolean showNever = false;
        boolean showAll = true;
        Alarm alarm = new Alarm();

        String exceptedResult = WorkDayTypeUtils.getWorkdayTypeLabelString(WorkDayTypeUtils.WORKDAY_TYPE_WORKDAY);

        String result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);

        Assert.assertEquals(exceptedResult, result);
    }

    @Test
    public void should_turn_alarm_day_right_when_getDescription_with_different_repeatSet() {
        int repeatSet = RepeatSet.REPEAT_NONE;
        int workdaySwitch = 0;
        int holidaySwitch = 0;
        boolean showNever = false;
        boolean showAll = true;
        Alarm alarm = new Alarm();

        String exceptedResult = mContext.getString(R.string.ring_once);
        String result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        repeatSet = RepeatSet.REPEAT_DAILY;
        exceptedResult = mContext.getResources().getStringArray(R.array.alarm_repeat_strings_2)[1];
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        repeatSet = RepeatSet.MONDAY_TO_FRIDAY;
        exceptedResult = mContext.getResources().getString(R.string.monday_to_friday);
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        repeatSet = RepeatSet.MONDAY_TO_SATURDAY;
        exceptedResult = mContext.getResources().getString(R.string.monday_to_saturday);
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        repeatSet = RepeatSet.MONDAY;
        exceptedResult = mContext.getResources().getStringArray(R.array.days_of_week_short)[0];
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);
    }

    @Test
    public void should_turn_alarm_day_right_when_getDescription_with_alarm_is_special_days() {
        int repeatSet = RepeatSet.REPEAT_NONE;
        int workdaySwitch = 0;
        int holidaySwitch = 0;
        boolean showNever = false;
        boolean showAll = true;
        Alarm alarm = new Alarm();
        alarm.setmSpecialAlarmDays("#19314#19313#");

        String exceptedResult = AlarmUtils.getCustomAlarmDateDescription(alarm, mContext, false, AlarmUtils.ALARM_DESCRIPTION_MAX, Calendar.getInstance());
        String result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        alarm.setmSpecialAlarmDays("#19314#19313#19312#19311#19310#");
        exceptedResult = AlarmUtils.getCustomAlarmDateDescription(alarm, mContext, false, AlarmUtils.ALARM_DESCRIPTION_MAX, Calendar.getInstance())
                + mContext.getString(R.string.alarm_list_more);
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);

        long closeNextTime = System.currentTimeMillis();
        long closePriTime = System.currentTimeMillis() - ONE_DAY;
        alarm.setmCloseOnceTimeNext(closeNextTime);
        alarm.setmCloseOncePriTime(closePriTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(closeNextTime);
        calendar.set(Calendar.SECOND, 1);
        alarm.setmSpecialAlarmDays("#19314#19313#");
        exceptedResult = AlarmUtils.getCustomAlarmDateDescription(alarm, mContext, false, AlarmUtils.ALARM_DESCRIPTION_MAX, calendar);
        result = RepeatSet.getDescription(mContext, repeatSet, workdaySwitch, holidaySwitch, showNever, alarm, showAll);
        Assert.assertEquals(exceptedResult, result);
    }


    @Test
    public void should_return_ture_when_isRepeat_with_REPEAT_DAILY() {
        boolean isRepeat = RepeatSet.isRepeat(RepeatSet.REPEAT_DAILY);
        assertTrue(isRepeat);
    }

    @Test
    public void should_return_7_when_getNextAlarm_with_NOW() {
        int addDays = RepeatSet.getNextAlarm(Calendar.getInstance(),0);
        assertEquals(addDays,7);
    }


    @Test
    public void should_return_7_when_getLastAlarm_with_NOW() {
        int addDays = RepeatSet.getLastAlarm(Calendar.getInstance(),0);
        assertEquals(addDays,8);
    }

    @Implements(DeviceUtils.class)
    public static class ShadowDeviceUtils {

        public static boolean isExpVersion(Context context) {
            return false;
        }
    }
}