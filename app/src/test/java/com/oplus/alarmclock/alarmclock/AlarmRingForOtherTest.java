/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-10-21, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.checkbox.COUICheckBox;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.view.DigitalClock;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.view.DigitalClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.view.DigitalClock;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.shadows.ShadowGetInitialDisplayDensityUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;

import static com.oplus.alarmclock.alarmclock.AlarmRingForOther.RING_URI;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {AlarmRingForOtherTest.ShadowColorDarkModeUtil.class, ShadowGetInitialDisplayDensityUtil.class})
public class AlarmRingForOtherTest extends TestParent {
    AlarmRingForOther mActivity;
    private static final int MAX_ALARM_NAME_SHOW_LENGTH = 10;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        Intent intent = new Intent();
        intent.putExtra(AlarmRingForOther.RING_URI, "uriString");
        mActivity = Robolectric.buildActivity(AlarmRingForOther.class, intent).create().get();
    }

    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void shouldSetEnabledWithTrueWhenOnCreateOptionsMenuWithDeleteAdapterGetItemCountGreaterThanZeroAndSelectedCountGreaterThanZero()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        Activity spyActivity = Mockito.spy(mActivity);
        int count = 2;
        ArrayList<Alarm> list = new ArrayList(count);
        for (int i = 0; i < count; i++) {
            list.add(new Alarm());
        }
        //ensure DeleteAdapter#getItemCoun() > 0
        ShadowAlarmUtils.sAlarmList = list;
        //init deleteAdapter
        ReflectUtil.invoke(AlarmRingForOther.class, "updateListLayout", new Object[]{false}, spyActivity, boolean.class);
        int mSelectedCount = 1;
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mSelectedCount", spyActivity, mSelectedCount);
        MenuItem mDoneItem = mock(MenuItem.class);
        MenuItem cancelItem = mock(MenuItem.class);
        Menu menu = mock(Menu.class);
        when(menu.findItem(R.id.done)).thenReturn(mDoneItem);
        when(menu.findItem(R.id.cancel)).thenReturn(cancelItem);
        MenuInflater menuInflater = mock(MenuInflater.class);
        doReturn(menuInflater).when(spyActivity).getMenuInflater();
        //invoke onCreateOptionsMenu()
        spyActivity.onCreateOptionsMenu(menu);
        //assert
        verify(mDoneItem).setEnabled(true);
    }


    @Config(shadows = {ShadowAlarmUtils.class})
    @Test
    public void shouldCallSetEnabledWithFalseAndDeleteAdapterNotNullWhenUpdateListLayoutWithAlarmCacheNotNullAndCheckSetIsNullAndDeleteAdapterIsNull()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        int count = 2;
        ArrayList<Alarm> list = new ArrayList(count);
        for (int i = 0; i < count; i++) {
            list.add(new Alarm());
        }
        //ensure DeleteAdapter#getItemCoun() > 0
        ShadowAlarmUtils.sAlarmList = list;
        boolean other = false;
        MenuItem mDoneItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mDoneItem", mActivity, mDoneItem);
        //invoke updateListLayout()
        ReflectUtil.invoke(AlarmRingForOther.class, "updateListLayout",
                new Object[]{other}, mActivity, boolean.class);
        COUIRecyclerView.Adapter<RecyclerView.ViewHolder> deleteAdapter = (COUIRecyclerView.Adapter) ReflectUtil.getFieldValue(
                AlarmRingForOther.class, "mDeleteAdapter", mActivity);

        //assert
        verify(mDoneItem).setEnabled(false);
        assertNotNull(deleteAdapter);
    }


    @Config(shadows = {ShadowAlarmRingUtils.class, ShadowContextNative.class})
    @Test
    public void shouldGetRingNameEqualsToRingTitleWhenSaveRingSetResultsWithCheckSetElementsAreTrueAndIsMediaURIValidReturnFalse()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        mActivity = Mockito.spy(mActivity);
        ContentResolver contentResolver = mock(ContentResolver.class);
        doReturn(contentResolver).when(mActivity).getContentResolver();
        int size = 3;
        boolean[] mCheckSet = new boolean[size];
        ArrayList<Alarm> mAlarmCache = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            mCheckSet[i] = true;
            mAlarmCache.add(new Alarm());
        }
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mCheckSet", mActivity, mCheckSet);
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mAlarmCache", mActivity, mAlarmCache);
        ShadowAlarmRingUtils.sIsMediaUriValid = true;
        //invoke saveRingSetResults()
        ReflectUtil.invoke(AlarmRingForOther.class, "saveRingSetResults",
                null, mActivity);
        //assert
        String ringTitle = mContext.getResources().getString(R.string.default_alarm_summary);
        for (int i = 0; i < size; i++) {
            assertEquals(ringTitle, mAlarmCache.get(i).getRingName());
        }
    }

    @Test
    public void shouldTitleEqualsToAlarmRingNameWhenGetAudioTitleWithUriStartWithStartAndCursorGetCountIsOne() {
        AlarmRingForOther spyActivity = Mockito.spy(mActivity);
        ContentResolver contentResolver = mock(ContentResolver.class);
        Cursor cursor = mock(Cursor.class);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        int index = 0;
        String alarmRingName = "ringName";
        when(cursor.getString(index)).thenReturn(alarmRingName);
        when(contentResolver.query(any(Uri.class), any(String[].class), (String) isNull(),
                (String[]) isNull(), (String) isNull())).thenReturn(cursor);
        doReturn(contentResolver).when(spyActivity).getContentResolver();
        String start = "content://media/external/";
        Uri uri = Uri.parse(start + "ring");
        //invoke getAudioTitle()
        String title = spyActivity.getAudioTitle(uri);
        //assert
        assertEquals(alarmRingName, title);
    }

    @Test
    public void shouldSizeEqualsToSelectedCountAndCallSetEnabledWithTrueWhenUpdateActionBarMenuWithCheckSetLengthIsSizeAndElementsAreTrue() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        int size = 3;
        boolean[] mCheckSet = new boolean[size];
        for (int i = 0; i < size; i++) {
            mCheckSet[i] = true;
        }
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mCheckSet", mActivity, mCheckSet);
        MenuItem mDoneItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mDoneItem", mActivity, mDoneItem);
        //invoke updateActionBarMenu()
        ReflectUtil.invoke(AlarmRingForOther.class, "updateActionBarMenu", null, mActivity);
        int mSelectedCount = (int) ReflectUtil.getFieldValue(AlarmRingForOther.class, "mSelectedCount", mActivity);
        //assert
        assertEquals(size, mSelectedCount);
        verify(mDoneItem).setEnabled(true);
    }


    @Config(shadows = {ShadowRepeatSet.class})
    @Test
    @Ignore
    public void shouldCallSetTextWithTextAndCallSetWidthWhenOnBindViewHolderWithDaysOfWeekStrNotEmptyAndLabelLengthGreaterThanMaxAlarmNameShowLength()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException, ClassNotFoundException, InvocationTargetException, InstantiationException {
        boolean other = false;
        //init deleteAdapter
        ReflectUtil.invoke(AlarmRingForOther.class, "updateListLayout",
                new Object[]{other}, mActivity, boolean.class);
        COUIRecyclerView.Adapter<RecyclerView.ViewHolder> deleteAdapter = (COUIRecyclerView.Adapter) ReflectUtil.getFieldValue(
                AlarmRingForOther.class, "mDeleteAdapter", mActivity);
        assertNotNull(deleteAdapter);
        //init MViewHolder
        View itemView = mock(View.class);
        COUICheckBox mOnButton = mock(COUICheckBox.class);
        DigitalClock mDigitalClock = mock(DigitalClock.class);
        TextView mLabelView = mock(TextView.class);
        when(itemView.findViewById(R.id.oplus_listview_scrollchoice_checkbox)).thenReturn(mOnButton);
        when(itemView.findViewById(R.id.alarm_delete_digitalClock)).thenReturn(mDigitalClock);
        when(itemView.findViewById(R.id.label)).thenReturn(mLabelView);
        Class mViewHolder = Class.forName("com.oplus.alarmclock.alarmclock.AlarmRingForOther$MViewHolder");
        Constructor constructor = mViewHolder.getDeclaredConstructor(View.class);
        constructor.setAccessible(true);
        int position = 1;
        COUIRecyclerView.ViewHolder viewHolder = (COUIRecyclerView.ViewHolder) constructor.newInstance(itemView);
        COUIRecyclerView.ViewHolder spyViewHolder = spy(viewHolder);
        doReturn(position).when(spyViewHolder).getAdapterPosition();

        ArrayList<Alarm> mAlarmCache = new ArrayList(1);
        Alarm alarm = new Alarm();
        char c = 'a';
        StringBuilder sb = new StringBuilder(MAX_ALARM_NAME_SHOW_LENGTH + 1);
        //ensure label.length() > MAX_ALARM_NAME_SHOW_LENGTH
        for (int i = 0; i <= MAX_ALARM_NAME_SHOW_LENGTH; i++) {
            sb.append(c);
        }
        alarm.setLabel(sb.toString());
        mAlarmCache.add(alarm);
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mAlarmCache", mActivity, mAlarmCache);

        String daysOfWeekStr = "daysOfWeekStr";
        ShadowRepeatSet.sDescription = daysOfWeekStr;

        boolean[] mCheckSet = new boolean[]{true};
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mCheckSet", mActivity, mCheckSet);
        boolean mIsSelect = true;
        ReflectUtil.setFieldValue(AlarmRingForOther.class, "mIsSelect", mActivity, mIsSelect);

        //invoke onBindViewHolder()
        deleteAdapter.onBindViewHolder(spyViewHolder, position);
        //assert
        verify(mOnButton).setChecked(true);
        verify(mLabelView).setText(anyString());
    }


    @Implements(RepeatSet.class)
    public static class ShadowRepeatSet {
        static String sDescription;

        public static String getDescription(Context context, int repeatSet, int workdaySwitch, int holidaySwitch, boolean showNever) {
            return sDescription;
        }
    }

    @Implements(AlarmRingUtils.class)
    public static class ShadowAlarmRingUtils {
        static boolean sIsMediaUriValid;

        public static boolean isMediaUriValid(Context context, Uri uri) {
            return sIsMediaUriValid;
        }
    }

    @Implements(AlarmUtils.class)
    public static class ShadowAlarmUtils {
        static ArrayList<Alarm> sAlarmList;

        public static ArrayList<Alarm> getAllAlarms(Context context) {
            return sAlarmList;
        }
    }

    @Implements(COUIDarkModeUtil.class)
    public static class ShadowColorDarkModeUtil {
        @Implementation
        public static void setForceDarkAllow(View view, boolean allow) {
        }
    }
}
