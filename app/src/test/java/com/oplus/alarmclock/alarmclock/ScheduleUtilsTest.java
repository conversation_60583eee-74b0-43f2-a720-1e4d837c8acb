package com.oplus.alarmclock.alarmclock;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.Calendar;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class ScheduleUtilsTest extends TestParent {

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }


    @Test
    public void should_return_ContentValues_not_null_when_createContentValues_with_alarmId_and_AlarmSchedule() throws NoSuchMethodException, IllegalAccessException {
        long alarmId = 10L;
        AlarmSchedule alarmSchedule = new AlarmSchedule();
        alarmSchedule.setAlarmId(alarmId);
        alarmSchedule.setYear(2020);
        alarmSchedule.setMonth(3);
        alarmSchedule.setDay(20);
        alarmSchedule.setHour(17);
        alarmSchedule.setMinute(20);
        long time = System.currentTimeMillis();
        alarmSchedule.setTime(time);
        alarmSchedule.setAlarmState(2);
        alarmSchedule.setSnoonzeTime(2000);

        ContentValues contentValues = (ContentValues) ReflectUtil.invoke(ScheduleUtils.class,
                "createContentValues", new Object[]{alarmId, alarmSchedule}, null, long.class, AlarmSchedule.class);

        assertNotNull(contentValues);
        long alarmIdFromSchedule = contentValues.getAsLong(ClockContract.Schedule.ALARM_ID);
        assertEquals(alarmId, alarmIdFromSchedule);
    }

    @Test
    public void should_return_alarm_time_when_getAlarmTimeInMills_with_AlarmSchedule() throws NoSuchMethodException, IllegalAccessException {
        AlarmSchedule alarmSchedule = new AlarmSchedule();

        int timeYear = 2020;
        int timeMonth = 3;
        int timeDay = 20;
        int timeHour = 17;
        int timeMinute = 20;

        alarmSchedule.setYear(timeYear);
        alarmSchedule.setMonth(timeMonth);
        alarmSchedule.setDay(timeDay);
        alarmSchedule.setHour(timeHour);
        alarmSchedule.setMinute(timeMinute);

        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, timeYear);
        calendar.set(Calendar.MONTH, timeMonth);
        calendar.set(Calendar.DAY_OF_MONTH, timeDay);
        calendar.set(Calendar.HOUR_OF_DAY, timeHour);
        calendar.set(Calendar.MINUTE, timeMinute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long time = calendar.getTimeInMillis();

        long timeInMillis = (long) ReflectUtil.invoke(ScheduleUtils.class,
                "getAlarmTimeInMills", new Object[]{alarmSchedule}, null, AlarmSchedule.class);
        assertEquals(time, timeInMillis);
    }

    @Test
    public void should_return_time_big_14_minute_when_getMissedTimeToLiveInMills_with_alarmTime() throws NoSuchMethodException, IllegalAccessException {
        final Calendar calendar = Calendar.getInstance();
        long alarmTime = calendar.getTimeInMillis();

        int MISSED_TIME_TO_LIVE_MINUTE_OFFSET = 14;
        int DEFAULT_TIME_ONE_SECOND = 1000;
        int DEFAULT_TIME_ONE_MINUTE = 60;

        long timeInMillis = (long) ReflectUtil.invoke(ScheduleUtils.class,
                "getMissedTimeToLiveInMills", new Object[]{alarmTime}, null, long.class);

        alarmTime = alarmTime + MISSED_TIME_TO_LIVE_MINUTE_OFFSET * DEFAULT_TIME_ONE_MINUTE * DEFAULT_TIME_ONE_SECOND;

        assertEquals(timeInMillis, alarmTime);

    }

    @Test
    public void should_return_time_big_alarmDuration_minute_when_getTimeoutInMills_with_alarmTime() throws NoSuchMethodException, IllegalAccessException {
        final Calendar calendar = Calendar.getInstance();
        long alarmTime = calendar.getTimeInMillis();

        int mAlarmDuration = 5;
        int DEFAULT_TIME_ONE_SECOND = 1000;
        int DEFAULT_TIME_ONE_MINUTE = 60;

        long timeInMillis = (long) ReflectUtil.invoke(ScheduleUtils.class,
                "getTimeoutInMills", new Object[]{alarmTime, mAlarmDuration}, null, long.class, long.class);

        alarmTime = alarmTime + mAlarmDuration * DEFAULT_TIME_ONE_MINUTE * DEFAULT_TIME_ONE_SECOND;

        assertEquals(timeInMillis, alarmTime);

    }

    @Test
    public void should_return_false_when_isCanGetNextSchedule_with_get_schedule_not_null() throws NoSuchMethodException, IllegalAccessException {
        mContext = spy(mContext);
        ContentResolver contentResolver = mock(ContentResolver.class);
        doReturn(contentResolver).when(mContext).getContentResolver();
        Cursor cursor = mock(Cursor.class);
        int cursorCount = 5;
        when(cursor.getCount()).thenReturn(cursorCount);
        when(contentResolver.query(eq(ClockContract.Schedule.ALARM_SCHEDULE_CONTENT_URI), any(String[].class), (String) isNull(),
                (String[]) isNull(), (String) isNull())).thenReturn(cursor);

        boolean result = (boolean) ReflectUtil.invoke(ScheduleUtils.class,
                "isCanGetNextSchedule", new Object[]{mContext, null, null, null}, null, Context.class, String.class, String[].class, String.class);

        assertFalse(result);

    }



}