/************************************************************
 * Copyright 2010-2022 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
Description : ChangedAlarmInfoTest for test
 * History :( ID, Date, Author, Description)
 * v1.0, 20122-4-20, den<PERSON><PERSON><PERSON>, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock.utils

import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import io.mockk.every
import io.mockk.mockkObject
import org.junit.Assert
import org.junit.Test
import java.util.TimeZone
import java.util.Calendar

class ChangedAlarmInfoTest : TestParent() {

    @Test
    @Throws(InterruptedException::class)
    fun should_return_true_when_isExpired_with_time_Expired() {
        //Given
        val mAlarm = Alarm()
        val curTimeZoneId = TimeZone.getDefault().id
        val calendar = AlarmNotRingCheckUtils.buildCalendar(curTimeZoneId)
        mAlarm.hour = calendar.get(Calendar.HOUR)
        mAlarm.minutes = calendar.get(Calendar.MINUTE)
        val alarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        val curCheckTime = System.currentTimeMillis() - 1000L
        val preCheckTime = curCheckTime - 36000L
        mockkObject(ChangedAlarmInfo)
        every {
            ChangedAlarmInfo.calculateAlarmTimeStamp(
                any(),
                any()
            )
        } returns curCheckTime + 3600000L
        //When
        val b = alarmInfo.isExpired(curTimeZoneId, preCheckTime, curCheckTime)
        //Then
        Assert.assertTrue(b)
    }

    @Test
    @Throws(InterruptedException::class)
    fun should_return_false_when_isExpired_without_time_Expired() {
        //Given
        val mAlarm = Alarm()
        val curTimeZoneId = TimeZone.getDefault().id
        val calendar = AlarmNotRingCheckUtils.buildCalendar(curTimeZoneId)
        mAlarm.hour = calendar.get(Calendar.HOUR)
        mAlarm.minutes = calendar.get(Calendar.MINUTE)
        val alarmInfo = ChangedAlarmInfo(alarm = mAlarm)
        val curCheckTime = System.currentTimeMillis() - 1000L
        val preCheckTime = curCheckTime - 36000L
        mockkObject(ChangedAlarmInfo)
        every {
            ChangedAlarmInfo.calculateAlarmTimeStamp(
                any(),
                any()
            )
        } returns (preCheckTime + curCheckTime) / 2
        //When
        val b = alarmInfo.isExpired(curTimeZoneId, preCheckTime, curCheckTime)
        //Then
        Assert.assertFalse(b)
    }
}