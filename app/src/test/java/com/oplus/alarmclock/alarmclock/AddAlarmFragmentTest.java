/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-30, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.alarmclock;

import android.app.Activity;
import android.content.AsyncQueryHandler;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.res.Resources;
import android.database.Cursor;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;
import androidx.fragment.app.FragmentManager;

import com.coui.appcompat.preference.COUIJumpPreference;
import com.coui.appcompat.preference.COUISwitchPreference;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.utils.AlarmSetStaticHandler;
import com.oplus.alarmclock.alarmclock.utils.SetAlarmAsyncQueryHandler;
import com.oplus.alarmclock.alarmclock.utils.SetAlarmHandler;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.DeviceUtils;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.alarmclock.utils.AlarmRingUtils;
import com.oplus.alarmclock.utils.ClockConstant;
import com.oplus.alarmclock.utils.StaticHandler;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowColorDarkModeUtil;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.ShadowUtils;
import com.oplus.utils.StringUtils;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implements;
import org.robolectric.annotation.LooperMode;
import org.robolectric.shadows.ShadowActivity;
import org.robolectric.shadows.ShadowLooper;
import org.robolectric.shadows.ShadowToast;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static android.os.Looper.getMainLooper;
import static android.provider.AlarmClock.EXTRA_DAYS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.robolectric.Shadows.shadowOf;
import static org.robolectric.annotation.LooperMode.Mode.PAUSED;


@Config(shadows = {ShadowColorDarkModeUtil.class, ShadowUtils.class})
@LooperMode(PAUSED)
@Ignore
public class AddAlarmFragmentTest extends TestParent {
    AddAlarmFragment mFragment;
    final int SET_ALERT_TYPE_DIALOG = 6;
    final int RING_REQUEST_CODE = 2001;
    final int ALERT_CYCLE_DETAILS_REQUEST_CODE = 2003;
    final int SET_RING_NAME_PRE = 7;
    final long BACK_CLICK_DURATION = 1000L;
    final String RING_TITLE = MediaStore.MediaColumns.TITLE;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        Intent intent = new Intent();
        long alarmId = -1;
        intent.putExtra(ClockConstant.ALARM_ID, alarmId);
        FragmentActivity activity = Robolectric.buildActivity(AlarmClock.class, intent).create().resume().get();
        FragmentHostCallback host = new FragmentHostCallback(activity, new Handler(), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }

            @Override
            public void onStartActivityFromFragment(@NonNull Fragment fragment, Intent intent,
                                                    int requestCode, @Nullable Bundle options) {
                fragment.getActivity().startActivityFromFragment(fragment, intent, requestCode, options);
            }

            @Override
            public void onStartIntentSenderFromFragment(
                    @NonNull Fragment fragment, IntentSender intent, int requestCode,
                    @Nullable Intent fillInIntent, int flagsMask, int flagsValues,
                    int extraFlags, Bundle options) throws IntentSender.SendIntentException {
                fragment.getActivity().startIntentSenderFromFragment(fragment, intent, requestCode,
                        fillInIntent, flagsMask, flagsValues, extraFlags, options);
            }
        };
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        mFragment = new AddAlarmFragment();
        ReflectUtil.setFieldValue(Fragment.class, "mFragmentManager",
                mFragment, fragmentManager);
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        ReflectUtil.invoke(Fragment.class, "performAttach", null, mFragment);
        ReflectUtil.invoke(Fragment.class, "performCreate", new Object[]{null},
                mFragment, Bundle.class);
        LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        ReflectUtil.invoke(Fragment.class, "performCreateView", new Object[]{inflater, null, null}, mFragment,
                LayoutInflater.class, ViewGroup.class, Bundle.class);
        ReflectUtil.invoke(Fragment.class, "performResume", null, mFragment);

        AlarmClockApplication sInstance = spy(AlarmClockApplication.getInstance());
        Resources resources = spy(mContext.getResources());
        String[] strArr = new String[]{"one", "two", "three"};
        doReturn(strArr).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_OLD(), "array", "oplus"));
        doReturn(strArr).when(resources).getStringArray(mContext.getResources().getIdentifier(
                StringUtils.INSTANCE.getSELECT_RINGTONE_NEW(), "array", "oplus"));
        when(sInstance.getResources()).thenReturn(resources);
        ReflectUtil.setFieldValue(AlarmClockApplication.class, "sInstance", null, sInstance);


        Shadows.shadowOf(getMainLooper()).idleFor(1000, TimeUnit.MILLISECONDS);

    }

    @Ignore
    @Test
    public void should_call_closeAlarmSetSetPage_when_onOptionsItemSelected_with_itemId_is_cancel_and_mCanClickSaveOrCancel_is_true()
            throws NoSuchFieldException, IllegalAccessException {
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mCanClickSaveOrCancel", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "sCanClickDelay", mFragment, true);
        MenuItem item = mock(MenuItem.class);
        when(item.getItemId()).thenReturn(R.id.cancel);
        mFragment = Mockito.spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(item);
        //assert
        verify(mFragment).closeAlarmSetSetPage();
    }

    @Ignore
    @Test
    public void should_showToast_with_text_expectedString_when_onOptionsItemSelected_with_itemId_is_done_and_mIsNewAlarm_is_true_and_mCanClickSaveOrCancel_true()
            throws NoSuchFieldException, IllegalAccessException {
        int hour = 12;
        int minutes = 30;
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, new Alarm());
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "sCanClickDelay", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mCanClickSaveOrCancel", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarmCount", mFragment, 20);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mIsNewAlarm", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mTimePickerHour", mFragment, hour);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mTimePickerMinute", mFragment, minutes);
        MenuItem item = mock(MenuItem.class);
        when(item.getItemId()).thenReturn(R.id.save);
        mFragment.onOptionsItemSelected(item);
        Alarm alarm = (Alarm) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mAlarm", mFragment);
        //assert
        assertEquals(hour, alarm.getHour());
        assertEquals(minutes, alarm.getMinutes());
    }


    @Ignore
    @Test
    public void should_showToast_with_text_expectedString_when_onOptionsItemSelected_with_itemId_is_done_and_mIsNewAlarm_is_true_and_mAlarmCount_greaterThan_MAX_ALARM_COUNT()
            throws NoSuchFieldException, IllegalAccessException {
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "sCanClickDelay", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mCanClickSaveOrCancel", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarmCount", mFragment, AlarmClockFragment.MAX_ALARM_COUNT);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mIsNewAlarm", mFragment, true);
        MenuItem item = mock(MenuItem.class);
        when(item.getItemId()).thenReturn(R.id.save);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(item);
        String toastString = ShadowToast.getTextOfLatestToast();
        String expectedString = mContext.getString(R.string.shortcut_alarm_num_reach_max);
        //assert
        assertEquals(expectedString, toastString);
    }

    @Ignore
    @Test
    public void should_startActivity_with_extra_NAVIGATE_UP_TITLE_TEXT_when_onPreferenceTreeClick_with_preference_is_mAlarmCycleDetailsPref_and_mIsNewAlarm_is_true()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mIsNewAlarm", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, new Alarm());
        //invoke onPreferenceTreeClick()
        mFragment.onClick(mViewHolder.mRingLayout);
        ShadowActivity shadowActivity = shadowOf(mFragment.getActivity());
        Intent intent = shadowActivity.getNextStartedActivity();
        String str = intent.getStringExtra(Utils.NAVIGATE_UP_TITLE_TEXT);
        //assert
        assertEquals(mContext.getString(R.string.new_alarm), str);
    }


    @Ignore
    @Test
    public void should_startActivity_with_extra_NAVIGATE_UP_TITLE_ID_when_onPreferenceTreeClick_with_preference_is_mRingPref_and_mIsNewAlarm_is_true()
            throws NoSuchFieldException, IllegalAccessException {
        View view = mock(View.class);
        view.setId(R.id.ll_ring);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mIsNewAlarm", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, new Alarm());
        //invoke onPreferenceTreeClick()
        mFragment.onClick(view);
        ShadowActivity shadowActivity = shadowOf(mFragment.getActivity());
        Intent intent = shadowActivity.getNextStartedActivity();
        int strId = intent.getIntExtra(Utils.NAVIGATE_UP_TITLE_ID, -1);
        //assert
        assertEquals(R.string.new_alarm, strId);
    }

    @Ignore
    @Test
    public void should_showDialog_with_id_SET_ALERT_TYPE_DIALOG_when_onPreferenceTreeClick_with_preference_is_mAlertTypePref()
            throws NoSuchFieldException, IllegalAccessException {
        COUIJumpPreference alertTypePref = (COUIJumpPreference) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mAlertTypePref", mFragment);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, new Alarm());
        //invoke onPreferenceTreeClick()
//        mFragment.onPreferenceTreeClick(alertTypePref);
        ShadowActivity shadowActivity = shadowOf(mFragment.getActivity());
        int lastShownDialog = shadowActivity.getLastShownDialogId();
        //assert
        assertEquals(SET_ALERT_TYPE_DIALOG, lastShownDialog);
    }


    @Test
    public void should_call_setSilent_with_true_when_onActivityResult_with_requestCode_is_RING_REQUEST_CODE_and_resultCode_is_RESULT_OK_and_getAlert_is_null() throws NoSuchFieldException, IllegalAccessException {
        Alarm alarm = new Alarm();
        mFragment.mViewHolder.mAlarm = alarm;
        Intent data = new Intent();
        int vibrateType = 5;
        data.putExtra(ClockConstant.EXTRA_VIBRATE_TYPE, vibrateType);
        //invoke
        mFragment.onActivityResult(RING_REQUEST_CODE, Activity.RESULT_OK, data);
        //assert
        assertTrue(alarm.isSilent());
        assertEquals(vibrateType, alarm.getVibrate());
    }

    @Test
    public void should_call_setSummary_with_ringName_when_onActivityResult_with_requestCode_is_RING_REQUEST_CODE_and_getAlert_is_NotNull_and_hasExtra_EXTRA_RINGTONE_TITLE() throws NoSuchFieldException, IllegalAccessException {
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        Intent data = new Intent();
        data.putExtra(RingtoneManager.EXTRA_RINGTONE_PICKED_URI, Uri.parse("uri"));
        String ringName = "ringToneTitle";
        data.putExtra(RingtoneManager.EXTRA_RINGTONE_TITLE, "ringToneTitle");
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);
        mFragment.onActivityResult(RING_REQUEST_CODE, Activity.RESULT_OK, data);
        //assert
        assertFalse(alarm.isSilent());
        assertEquals(ringName, mViewHolder.mRingSummary.getText().toString());
    }


    //enter dead loop mFragment.onActivityResult()->updateLeftTimeInfo()
    // ->AlarmUtils.getElapsedTimeUntilAlarmDescription()->getAlarmTimeMills()->getAlarmTime()
    @Test
    @Ignore
    public void should_call_setSummary_with_ringName_when_onActivityResult_with_requestCode_is_ALERT_CYCLE_DETAILS_REQUEST_CODE_and_hasExtra_ALERT_CYCLE_PREFERENCE_STRING() throws NoSuchFieldException, IllegalAccessException {
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        Intent data = new Intent();
        int holidaySwitch = 3;
        data.putExtra("holiday_switch_preference", holidaySwitch);
        String summyTip = "summyTip";
        data.putExtra("day_ring_frequence_string_id", summyTip);
        int days = 5;
        data.putExtra(ClockConstant.ALERT_CYCLE_PREFERENCE, days);
        mFragment.onActivityResult(ALERT_CYCLE_DETAILS_REQUEST_CODE, Activity.RESULT_OK, data);
        //assert
        assertEquals(holidaySwitch, alarm.getHolidaySwitch());
        assertEquals(days, alarm.getRepeatSet());
    }

    @Test
    public void should_return_repeatSet_equalsTo_sum_when_getDaysFromIntent_with_intent_with_extra_daysArray() throws NoSuchMethodException, IllegalAccessException {
        Random rand = new Random();
        int repeatSetCount = 4;
        int repeat = 5;
        for (int j = 0; j < repeat; j++) {
            ArrayList<Integer> daysArray = new ArrayList(repeatSetCount);
            int sum = 0;
            for (int i = 0; i < repeatSetCount; i++) {
                //[1-7]
                int dayOfWeek = rand.nextInt(7) + 1;
                if (!daysArray.contains(dayOfWeek)) {
                    daysArray.add(dayOfWeek);
                    if (dayOfWeek == 1) {
                        dayOfWeek += 5;
                    } else {
                        dayOfWeek -= 2;
                    }
                    sum = sum | 1 << dayOfWeek;
                }
            }
            Intent intent = new Intent();
            intent.putExtra(EXTRA_DAYS, daysArray);
            //invoke getDaysFromIntent()
            int repeatSet = (int) ReflectUtil.invoke(AddCustomAlarmManager.class, "getDaysFromIntent",
                    new Object[]{intent}, mFragment.mAddAlarmManager.getCustomAlarmManager(), Intent.class);
            //assert
            assertEquals(sum, repeatSet);
        }
    }

    @Ignore
    @Test
    public void should_call_setSnoonzeItem_with_SNOOZE_SWITCH_ON_5_MIN_when_onPreferenceChange_with_preference_is_mRingSnoozePref_and_arg1_is_true()
            throws NoSuchFieldException, IllegalAccessException {
        COUISwitchPreference mRingSnoozePref = (COUISwitchPreference) ReflectUtil.getFieldValue(
                AddAlarmFragment.class, "mRingSnoozePref", mFragment);
        boolean arg1 = true;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, alarm);
        //invoke onPreferenceChange
//        mFragment.onPreferenceChange(mRingSnoozePref, arg1);
        //assert
        assertEquals(ClockConstant.SNOOZE_SWITCH_ON_5_MIN, alarm.getSnoonzeItem());
    }


    @Test
    public void should_call_setSummary_with_ringName_when_handleMessage_with_SET_RING_NAME_PRE_and_ringName() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        AlarmSetStaticHandler handler = (AlarmSetStaticHandler) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mHandler", mFragment.mViewHolder);
        Message message = Message.obtain();
        message.what = SET_RING_NAME_PRE;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        String ringName = "ringName";
        alarm.setRingName(ringName);
        AddAlarmViewHolder ringPref = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);
        //invoke handleMessage()
        ReflectUtil.invoke(AlarmSetStaticHandler.class, "handleMessage", new Object[]{message, mFragment},
                handler, Message.class, Object.class);
        //assert
        assertEquals(ringName, ringPref.mRingSummary.getText().toString());
    }

    @Config(shadows = {ShadowAlarmRingUtils.class})
    @Test
    public void should_call_setRingName_with_dynamic_weather_alert_when_handleMessage_with_isUriValid_is_false_and_getAlert_equalsTo_ALARM_ALERT_DYNAMIC_WEATHER()
            throws NoSuchFieldException, IllegalAccessException {
        Message msg = Message.obtain();
        msg.what = AddAlarmViewHolder.SET_ALARL_GET_TITLE;
        ShadowAlarmRingUtils.sIsValid = false;
        Alarm alarm = new Alarm();
        alarm.setAlert(Uri.parse(ClockConstant.ALARM_ALERT_DYNAMIC_WEATHER));
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        SetAlarmHandler mSetAlarmHandler = (SetAlarmHandler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mSetAlarmHandler", mFragment.mAddAlarmManager.getAlarmRingManager());
        //invoke handleMessage()
        mSetAlarmHandler.handleMessage(msg);
        //assert
        assertEquals(mContext.getString(R.string.string_weather_alert), alarm.getRingName());
    }

    @Config(shadows = {ShadowRingtoneManager.class})
    @Test
    public void should_getRingName_notEqualsTo_title_when_handleMessage_with_isUriValid_is_true_and_getAlert_notStartWith_ringtone_and_notification_and_alarm()
            throws NoSuchFieldException, IllegalAccessException {
        Message msg = Message.obtain();
        msg.what = AddAlarmViewHolder.SET_ALARL_GET_TITLE;
        ShadowAlarmRingUtils.sIsValid = true;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        SetAlarmHandler mSetAlarmHandler = (SetAlarmHandler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mSetAlarmHandler", mFragment.mAddAlarmManager.getAlarmRingManager());
        ShadowRingtoneManager.sRingtone = mock(Ringtone.class);
        String title = "xxxxxxx";
        when(ShadowRingtoneManager.sRingtone.getTitle(any(Context.class))).thenReturn(title);
        //invoke handleMessage()
        mSetAlarmHandler.handleMessage(msg);
        //assert
        assertNotEquals(title, alarm.getRingName());
    }

    @Test
    public void should_call_setRingName_with_mAlertNone_when_handleMessage_with_what_is_SET_ALARL_SET_RING_AND_URI_and_isSilent_is_true()
            throws NoSuchFieldException, IllegalAccessException {
        Message msg = Message.obtain();
        msg.what = AddAlarmViewHolder.SET_ALARL_SET_RING_AND_URI;
        Alarm alarm = new Alarm();
        alarm.setSilent(true);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        Handler mSetAlarmHandler = (Handler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mSetAlarmHandler", mFragment.mAddAlarmManager.getAlarmRingManager());
        String ringName = "ringName";
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlertNone", mFragment.mViewHolder, ringName);
        //invoke handleMessage()
        mSetAlarmHandler.handleMessage(msg);
        //assert
        assertEquals(ringName, alarm.getRingName());
    }

    @Test
    public void should_call_setRingName_with_title_when_onQueryComplete_with_token_is_SET_ALARL_START_QUERY_AUTO_and_getCount_greaterThan_zero() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException, InvocationTargetException, InstantiationException {
        //init AddAlarmFragment#mHandlerQuery
        ReflectUtil.invoke(AddAlarmRingManager.class, "startQuery", new Object[]{100, Uri.parse("uri"), Uri.parse("uri"), null, null, null, null},
                mFragment.mAddAlarmManager.getAlarmRingManager(), int.class, Object.class, Uri.class, String[].class, String.class, String[].class, String.class);
        SetAlarmAsyncQueryHandler mHandlerQuery = (SetAlarmAsyncQueryHandler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mHandlerQuery", mFragment.mAddAlarmManager.getAlarmRingManager());
        Cursor cursor = mock(Cursor.class);
        String title = "title";
        when(cursor.getString(0)).thenReturn(title);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        int token = AddAlarmViewHolder.SET_ALARL_START_QUERY_AUTO;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        //invoke onQueryComplete()
        ReflectUtil.invoke(SetAlarmAsyncQueryHandler.class, "onQueryComplete", new Object[]{token, Uri.parse("uriString"), cursor}, mHandlerQuery, int.class, Object.class, Cursor.class);
        //assert
        assertEquals(title, alarm.getRingName());
    }


    @Test
    public void should_call_getString_with_nameColumn_when_onQueryComplete_with_token_is_SET_ALARL_START_QUERY_DEFAULT_and_hasColumn_RING_TITLE()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //init AddAlarmFragment#mHandlerQuery
        ReflectUtil.invoke(AddAlarmRingManager.class, "startQuery", new Object[]{100, Uri.parse("uri"), Uri.parse("uri"), null, null, null, null},
                mFragment.mAddAlarmManager.getAlarmRingManager(), int.class, Object.class, Uri.class, String[].class, String.class, String[].class, String.class);
        SetAlarmAsyncQueryHandler mHandlerQuery = (SetAlarmAsyncQueryHandler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mHandlerQuery", mFragment.mAddAlarmManager.getAlarmRingManager());
        Cursor cursor = mock(Cursor.class);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        int nameColumn = 0;
        when(cursor.getColumnIndex(RING_TITLE)).thenReturn(nameColumn);
        String title = "title";
        when(cursor.getString(nameColumn)).thenReturn(title);
        int token = AddAlarmViewHolder.SET_ALARL_START_QUERY_DEFAULT;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        //invoke onQueryComplete()
        ReflectUtil.invoke(SetAlarmAsyncQueryHandler.class, "onQueryComplete", new Object[]{token, Uri.parse("uriString"), cursor}, mHandlerQuery, int.class, Object.class, Cursor.class);
        //verify
        verify(cursor).getString(nameColumn);
    }


    @Test
    public void should_call_setRingName_with_mDefaultSummary_when_onQueryComplete_with_token_is_SET_ALARL_START_QUERY_DEFAULT_and_hasNotColumn_RING_TITLE()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //init AddAlarmFragment#mHandlerQuery
        ReflectUtil.invoke(AddAlarmRingManager.class, "startQuery", new Object[]{100, Uri.parse("uri"), Uri.parse("uri"), null, null, null, null},
                mFragment.mAddAlarmManager.getAlarmRingManager(), int.class, Object.class, Uri.class, String[].class, String.class, String[].class, String.class);
        SetAlarmAsyncQueryHandler mHandlerQuery = (SetAlarmAsyncQueryHandler) ReflectUtil.getFieldValue(AddAlarmRingManager.class, "mHandlerQuery", mFragment.mAddAlarmManager.getAlarmRingManager());
        Cursor cursor = mock(Cursor.class);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        int nameColumn = -1;
        when(cursor.getColumnIndex(RING_TITLE)).thenReturn(nameColumn);
        int token = AddAlarmViewHolder.SET_ALARL_START_QUERY_DEFAULT;
        Alarm alarm = new Alarm();
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        //invoke onQueryComplete()
        ReflectUtil.invoke(SetAlarmAsyncQueryHandler.class, "onQueryComplete", new Object[]{token, Uri.parse("uriString"), cursor}, mHandlerQuery, int.class, Object.class, Cursor.class);
        //assert
        assertEquals(mContext.getString(R.string.default_alarm_summary), alarm.getRingName());
    }

    @Test
    public void should_Preference_Summary_Equals_oplus_AlertType_when_updateTime_with_AlertTypeItem() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        //init
        int mAlertTypeItem = 0;
        Alarm alarm = Alarm.build(false, 0, 0, 0, mAlertTypeItem, "",
                null, "", 0, 0, 0, 0);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);

        //invoke
        ReflectUtil.invoke(AddAlarmManager.class, "updateTime", null, mFragment.mAddAlarmManager);
        AddAlarmViewHolder alertTypePref = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);

        String[] mAlertArray = mContext.getResources().getStringArray(R.array.oplus_AlertType);

        //assert
        assertEquals(mAlertArray[mAlertTypeItem], alertTypePref.mRingTypeSummary.getText().toString());

    }

    @Ignore
    @Test
    public void should_workdaySwitchPreference_checked_when_updateWorkdaySwitch_with_workdaySwitch_is_1() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        //init
        int workdaySwitch = 1;
        Alarm alarm = Alarm.build(false, 0, 0, 0, 0, "",
                null, "", 0, 0, workdaySwitch, 0);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);
        //invoke
        ReflectUtil.invoke(AddAlarmViewHolder.class, "updateWorkdaySwitch", null, mFragment.mViewHolder);
        COUISwitchPreference workdaySwitchPreference = (COUISwitchPreference) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mWorkdaySwitch", mFragment);
        //assert
        assertTrue(workdaySwitchPreference.isChecked());

        workdaySwitch = 0;
        alarm.setWorkdaySwitch(workdaySwitch);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, alarm);
        //invoke
        ReflectUtil.invoke(AddAlarmFragment.class, "updateWorkdaySwitch", null, mFragment);

        workdaySwitchPreference = (COUISwitchPreference) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mWorkdaySwitch", mFragment);
        //assert
        assertFalse(workdaySwitchPreference.isChecked());

    }

    @Ignore
    @Test
    public void should_alarmCycleDetailsPref_Enabled_when_updateAlarmCycle_with_workdaySwitch_is_0() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        //init
        int workdaySwitch = 1;
        Alarm alarm = Alarm.build(false, 0, 0, 0, 0, "",
                null, "", 0, 0, workdaySwitch, 0);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, alarm);
        //invoke
        ReflectUtil.invoke(AddAlarmFragment.class, "updateAlarmCycle", null, mFragment);
        COUIJumpPreference alarmCycleDetailsPref = (COUIJumpPreference) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mAlarmCycleDetailsPref", mFragment);
        //assert
        assertFalse(alarmCycleDetailsPref.isEnabled());

        workdaySwitch = 0;
        alarm.setWorkdaySwitch(workdaySwitch);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "mAlarm", mFragment, alarm);
        //invoke
        ReflectUtil.invoke(AddAlarmFragment.class, "updateAlarmCycle", null, mFragment);
        alarmCycleDetailsPref = (COUIJumpPreference) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mAlarmCycleDetailsPref", mFragment);
        //assert
        assertTrue(alarmCycleDetailsPref.isEnabled());

    }

    @Ignore
    @Test
    public void should_show_alarm_label_when_setPreferenceValueDelay_with_alarm_label_is_not_null() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {

        //init
        String label = "XXXXXX";
        Alarm alarm = Alarm.build(false, 0, 0, 0, 0, label,
                null, "", 0, 0, 0, 0);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCanClickSaveOrCancel", mFragment.mViewHolder, false);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);

        ReflectUtil.invoke(AddAlarmViewHolder.class, "setPreferenceValueDelay", null, mFragment.mViewHolder);
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);
        assertEquals(label, mViewHolder.mAlarmLabel.getText().toString());


        label = "Alarm";
        mContext = spy(mContext);
        alarm = Alarm.build(false, 0, 0, 0, 0, label,
                null, "", 0, 0, 0, 0);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCanClickSaveOrCancel", mFragment.mViewHolder, false);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarm);

        ReflectUtil.invoke(AddAlarmViewHolder.class, "setPreferenceValueDelay", null, mFragment.mViewHolder);
        mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class,
                "mViewHolder", mFragment);
        assertEquals(mContext.getString(R.string.default_label), mViewHolder.mAlarmLabel.getText().toString());
    }


    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onTabChanged_when_onTabChanged_with_toType_is_TAB_TYPE_INDEX_1_()
            throws NoSuchFieldException, IllegalAccessException {
        ShadowDeviceUtils.sIsExpVersion = false;
        int toType = 1;

        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());

        mFragment = spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.onTabChanged(toType, 0);
        Alarm alarm = (Alarm) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder);
        //assert
        assertEquals(alarm.getWorkdaySwitch(), 1);
    }

    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onTabChanged_when_onTabChanged_with_toType_is_TAB_TYPE_INDEX_2_()
            throws NoSuchFieldException, IllegalAccessException {
        ShadowDeviceUtils.sIsExpVersion = true;
        int toType = 2;

        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());

        mFragment = spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.onTabChanged(toType, 0);
        Alarm alarm = (Alarm) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder);
        //assert
        assertEquals(alarm.getWorkdaySwitch(), 0);
    }

    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onClick_when_onClick_with_id_is_date_picker_header_month_layout_()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class, "mViewHolder", mFragment);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCurrentView", mFragment.mViewHolder, 0);
        //invoke onOptionsItemSelected()
        mFragment.onClick(mFragment.mViewHolder.mExpandLayout);
        //assert
        assertEquals(mFragment.mViewHolder.mCurrentView, 1);
    }


    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onClick_when_onClick_with_id_is_add_alarm_repeat_date_pick_()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class, "mViewHolder", mFragment);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCurrentView", mFragment.mViewHolder, 0);
        //invoke onOptionsItemSelected()
        mFragment.onClick(mFragment.mViewHolder.mAlarmRepeatMore);
        //assert
        assertTrue(mFragment.mViewHolder.mShowDatePicker);
    }

    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onClick_when_onClick_with_id_is_add_alarm_repeat_date_pick_show_true_()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class, "mViewHolder", mFragment);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCurrentView", mFragment.mViewHolder, 0);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mShowDatePicker", mFragment.mViewHolder, true);
        //invoke onOptionsItemSelected()
        mFragment.onClick(mFragment.mViewHolder.mAlarmRepeatMore);
        //assert
        assertFalse(mFragment.mViewHolder.mShowDatePicker);
    }


    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onClick_when_onClick_with_id_is_add_alarm_work_day_layout_()
            throws NoSuchFieldException, IllegalAccessException {
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class, "mViewHolder", mFragment);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCurrentView", mFragment.mViewHolder, 0);
        ReflectUtil.setFieldValue(AddWorkAlarmManager.class, "mWorkdayTypePosition", mFragment.mAddAlarmManager.getWorkdayManage(), 1);
        //invoke onOptionsItemSelected()
        mFragment.onClick(mFragment.mViewHolder.mWorkDayLayout);
        Shadows.shadowOf(getMainLooper()).idleFor(1000, TimeUnit.MILLISECONDS);
        //assert
        assertEquals(mFragment.mViewHolder.mAlarm.getmWorkDayType(), 0);
    }


    @Test
    public void should_call_updateHolidaySwitch_when_onWeekPickClick_with_day_is_1_and_isCheck_true()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());
        mFragment.mAddAlarmManager.getCustomAlarmManager().onWeekPickClick(2, true);
        AddAlarmViewHolder mViewHolder = (AddAlarmViewHolder) ReflectUtil.getFieldValue(AddAlarmFragment.class, "mViewHolder", mFragment);
        //assert
        assertEquals(mViewHolder.mHolidaySwitch.isEnabled(), true);
    }


    @Implements(RingtoneManager.class)
    public static class ShadowRingtoneManager {
        static Ringtone sRingtone;

        public static Ringtone getRingtone(Context context, Uri ringtoneUri) {
            return sRingtone;
        }

    }

    @Implements(AlarmRingUtils.class)
    public static class ShadowAlarmRingUtils {
        static boolean sIsValid;

        public static boolean isMediaUriValid(Context context, Uri uri) {
            return sIsValid;
        }
    }

    @Implements(DeviceUtils.class)
    public static class ShadowDeviceUtils {
        static boolean sIsExpVersion;

        public static boolean isExpVersion(Context context) {
            return sIsExpVersion;
        }

    }

    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_onTabChanged_when_onTabChanged_with_toType_is_TAB_TYPE_INDEX_0_()
            throws NoSuchFieldException, IllegalAccessException {
        ShadowDeviceUtils.sIsExpVersion = false;
        int toType = 0;

        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());

        mFragment = spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.mAddAlarmManager.updateMargin();
        mFragment.onTabChanged(toType, 0);
        Alarm alarm = (Alarm) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder);
        //assert
        assertEquals(alarm.getWorkdaySwitch(), 0);
    }

    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_selectHoliday_when_check_with_to_true_()
            throws NoSuchFieldException, IllegalAccessException {
        ShadowDeviceUtils.sIsExpVersion = false;
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, new Alarm());

        mFragment = spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.mAddAlarmManager.getCustomAlarmManager().selectHoliday(true);
        Alarm alarm = (Alarm) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder);
        //assert
        assertEquals(alarm.getHolidaySwitch(), 1);
    }


    @Config(shadows = {ShadowDeviceUtils.class})
    @Test
    public void should_call_confirm_save_alarm()
            throws NoSuchFieldException, IllegalAccessException {
        ShadowDeviceUtils.sIsExpVersion = false;
        Alarm alarmTemp = new Alarm();
        alarmTemp.setSnoonzeItem(ClockConstant.SNOOZE_SWITCH_OFF);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mAlarm", mFragment.mViewHolder, alarmTemp);
        ReflectUtil.setFieldValue(AddAlarmFragment.class, "sCanClickDelay", mFragment, true);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mCanClickSaveOrCancel", mFragment.mViewHolder, true);
        ReflectUtil.setFieldValue(AddAlarmViewHolder.class, "mIsNewAlarm", mFragment.mViewHolder, true);
        mFragment = spy(mFragment);
        //invoke onOptionsItemSelected()
        mFragment.mAddAlarmManager.confirm();
        boolean alarm = (Boolean) ReflectUtil.getFieldValue(AddAlarmViewHolder.class, "mCanClickSaveOrCancel", mFragment.mViewHolder);
        //assert
        assertFalse(alarm);
    }
}
