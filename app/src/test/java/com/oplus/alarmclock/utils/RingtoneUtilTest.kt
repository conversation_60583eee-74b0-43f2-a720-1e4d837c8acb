/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - UtilsTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/11/22     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.content.Context
import android.database.Cursor
import android.net.Uri
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.TestParent
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class RingtoneUtilTest : TestParent() {

    override fun setUp() {
        super.setUp()
        mockkStatic(RingtoneUtil::class)
    }

    override fun tearDown() {
        super.tearDown()
        unmockkStatic(RingtoneUtil::class)
    }

    @Test
    fun should_getInternalRingtoneTitle_with_internal_file_uri() {
        val context = mockk<Context>()
        val cursor = mockk<Cursor>().apply {
            every { moveToFirst() } returns true
            every { getString(0) } returns "title"
            every { getString(1) } returns "path"
        }
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } returns cursor
        justRun { cursor.close() }
        val result = RingtoneUtil.getInternalRingtoneTitle(context,
            Uri.parse("content://media/internal/ringtones?title=test"))
        Assert.assertEquals(result, "title")
    }

    @Test
    fun should_getInternalRingtoneTitle_with_not_internal_file_uri() {
        val context = mockk<Context>()
        val cursor = mockk<Cursor>().apply {
            every { moveToFirst() } returns true
            every { getString(0) } returns "test_display_name"
            every { getString(1) } returns "path"
        }
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } returns cursor
        justRun { cursor.close() }
        val result = RingtoneUtil.getInternalRingtoneTitle(context,
            Uri.parse("content://media/external/ringtones?title=test_display_name"))
        Assert.assertEquals(result, "test_display_name")
    }

    @Test
    fun should_getInternalRingtoneTitle_with_no_data() {
        val context = mockk<Context>()
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } returns null
        val result = RingtoneUtil.getInternalRingtoneTitle(context,
            Uri.parse("content://media/external/ringtones?title=title"))
        Assert.assertEquals(result, "")
    }

    @Test
    fun should_getInternalRingtoneTitle_throw_exception() {
        val context = mockk<Context>()
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } throws Exception()
        val result = RingtoneUtil.getInternalRingtoneTitle(context,
            Uri.parse("content://media/external/ringtones?title=title"))
        Assert.assertEquals(result, "")
    }

    @Test
    fun should_getInternalRingtoneUri_with_title_test() {
        val context = mockk<Context>()
        val cursor = mockk<Cursor>().apply {
            every { moveToFirst() } returns true
            every { getLong(0) } returns 1
            every { getString(1) } returns "test"
            every { getString(2) } returns "path"
        }
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } returns cursor
        justRun { cursor.close() }
        val result = RingtoneUtil.getInternalRingtoneUri(context, "test")
        val actual = "content://media/internal/audio/media/1?title=test&canonical=1"
        Assert.assertEquals(result.toString(), actual)
    }

    @Test
    fun should_getInternalRingtoneUri_with_empty_title() {
        val context = mockk<Context>()
        val result = RingtoneUtil.getInternalRingtoneUri(context, "")
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_getInternalRingtoneUri_with_title_no_data() {
        val context = mockk<Context>()
        val cursor = mockk<Cursor>().apply {
            every { moveToFirst() } returns false
            every { getLong(0) } returns 1
            every { getString(1) } returns "test"
            every { getString(2) } returns "path"
        }
        every { context.contentResolver.query(any(), any(), any(), any(), any()) } returns cursor
        justRun { cursor.close() }
        val result = RingtoneUtil.getInternalRingtoneUri(context, "test")
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_getOldTitleFromUri_with_uri_null() {
        val result = RingtoneUtil.getOldTitleFromUri(null)
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_getOldTitleFromUri_with_uri_test() {
        mockkStatic(Uri::class)
        val oldUri = Uri.parse("content://media/external/ringtones?title=test")
        val uri = mockk<Uri>()
        every { Uri.parse(oldUri.toString()) } returns oldUri
        every { uri.getQueryParameter("title") } returns "test"
        val result = RingtoneUtil.getOldTitleFromUri(oldUri)
        Assert.assertEquals(result, "test")
        unmockkStatic(Uri::class)
    }

    @Test
    fun should_getOldTitleFromUri_throw_exception() {
        mockkStatic(Uri::class)
        val oldUri = Uri.parse("content://media/external/ringtones?title=test")
        val uri = mockk<Uri>()
        every { Uri.parse(oldUri.toString()) } throws Exception()
        every { uri.getQueryParameter("title") } throws Exception()
        val result = RingtoneUtil.getOldTitleFromUri(oldUri)
        Assert.assertEquals(result, null)
        unmockkStatic(Uri::class)
    }

    @Test
    fun should_getNewTitleFromOldTitle_with_title_test() {
        val context = spyk(AlarmClockApplication.getInstance())
        every { context.resources.getString(any()) } returns "T"
        every { context.resources.getStringArray(any()) } returns arrayOf("title/test")
        val result = RingtoneUtil.getNewTitleFromOldTitle("title")
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_getNewTitleFromOldTitle_with_title_empty() {
        val result = RingtoneUtil.getNewTitleFromOldTitle("")
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_isInternalFileUri_with_internal_uri() {
        val result = RingtoneUtil.isInternalFileUri("content://media/internal/title")
        Assert.assertTrue(result)
    }

    @Test
    fun should_isInternalFileUri_with_external_uri() {
        val result = RingtoneUtil.isInternalFileUri("content://media/external/title")
        Assert.assertFalse(result)
    }

    @Test
    fun should_removeExtension_with_null() {
        val result = RingtoneUtil.removeExtension(null)
        Assert.assertEquals(result, null)
    }

    @Test
    fun should_removeExtension_without_dot() {
        val result = RingtoneUtil.removeExtension("test")
        Assert.assertEquals(result, "test")
    }

    @Test
    fun should_removeExtension_with_contain_dot() {
        val result = RingtoneUtil.removeExtension("test.ogg")
        Assert.assertEquals(result, "test")
    }
}