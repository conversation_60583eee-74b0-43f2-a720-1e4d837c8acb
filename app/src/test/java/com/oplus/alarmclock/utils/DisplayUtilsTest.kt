/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - UtilsTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/22
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/11/22     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.content.Context
import android.util.Size
import android.view.Display
import android.view.WindowManager
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@Config(sdk = [28])
@RunWith(RobolectricTestRunner::class)
class DisplayUtilsTest {

    @Before
    fun setUp() {
        mockkStatic(DisplayUtils::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(DisplayUtils::class)
    }

    @Test
    fun should_getWindowHeight_when_width_more_than_height() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns  800
            every { height } returns 400
        }
        every { DisplayUtils.getWindowSize(context, false) } returns size

        mockkStatic(FoldScreenUtils::class)
        every { FoldScreenUtils.isRealOslo() } returns false
        unmockkStatic(FoldScreenUtils::class)

        val result = DisplayUtils.getWindowHeight(context)
        Assert.assertEquals(result, size.width)
    }

    @Test
    fun should_getWindowHeight_when_width_less_than_height() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 400
            every { height } returns 800
        }
        every { DisplayUtils.getWindowSize(context, false) } returns size

        mockkStatic(FoldScreenUtils::class)
        every { FoldScreenUtils.isRealOslo() } returns false
        unmockkStatic(FoldScreenUtils::class)

        val result = DisplayUtils.getWindowHeight(context)
        Assert.assertEquals(result, size.height)
    }

    @Test
    fun should_getWindowHeight_when_oslo() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 400
            every { height } returns 800
        }
        every { DisplayUtils.getWindowSize(context, false) } returns size

        mockkStatic(FoldScreenUtils::class)
        every { FoldScreenUtils.isRealOslo() } returns true
        unmockkStatic(FoldScreenUtils::class)

        val result = DisplayUtils.getWindowHeight(context)
        Assert.assertEquals(result, size.height)
    }

    @Test
    fun should_getRealWindowSize() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.getWindowSize(context, true) } returns size

        val result = DisplayUtils.getRealWindowSize(context)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getWindowSize() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.getWindowSize(context, false) } returns size

        val result = DisplayUtils.getWindowSize(context)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getMaxWindowSize_with_above_r() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.isAboveR() } returns true
        every { DisplayUtils.getMaxWindowSizeAboveR(context) } returns size

        val result = DisplayUtils.getMaxWindowSize(context)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getMaxWindowSize_with_below_r() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.isAboveR() } returns false
        every { DisplayUtils.getWindowSizeBelowQ(context, true) } returns size

        val result = DisplayUtils.getMaxWindowSize(context)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getWindowSize_with_above_r_real_true() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.isAboveR() } returns true
        every { DisplayUtils.getWindowSizeAboveR(context, any()) } returns size

        val result = DisplayUtils.getWindowSize(context, true)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getWindowSize_with_below_r_real_false() {
        val context = mockk<Context>()
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }
        every { DisplayUtils.isAboveR() } returns false
        every { DisplayUtils.getWindowSizeBelowQ(context, any()) } returns size

        val result = DisplayUtils.getWindowSize(context, false)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getWindowSizeBelowQ_with_real_true() {
        val context = mockk<Context>()
        val display = mockk<Display>().apply {
            justRun { getRealMetrics(any()) }
        }
        val manager = mockk<WindowManager>().apply {
            every { defaultDisplay } returns display
        }
        every { context.getSystemService(Context.WINDOW_SERVICE) } returns manager
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }

        val result = DisplayUtils.getWindowSizeBelowQ(context, true)
        Assert.assertEquals(result, size)
    }

    @Test
    fun should_getWindowSizeBelowQ_with_real_false() {
        val context = mockk<Context>()
        val display = mockk<Display>().apply {
            justRun { getMetrics(any()) }
        }
        val manager = mockk<WindowManager>().apply {
            every { defaultDisplay } returns display
        }
        every { context.getSystemService(Context.WINDOW_SERVICE) } returns manager
        val size = mockk<Size>().apply {
            every { width } returns 800
            every { height } returns 400
        }

        val result = DisplayUtils.getWindowSizeBelowQ(context, false)
        Assert.assertEquals(result, size)
    }
}