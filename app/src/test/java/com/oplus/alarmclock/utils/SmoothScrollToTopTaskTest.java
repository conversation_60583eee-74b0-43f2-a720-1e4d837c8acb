/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-26, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.utils;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import org.mockito.ArgumentMatchers;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SmoothScrollToTopTaskTest extends TestParent {
    @Test
    public void should_return_distance_to_top_when_calculateDistanceToTop_with_listView_height_big_enough_and_mScrollStartPos_is_zero() throws NoSuchMethodException, IllegalAccessException {
        int itemViewHeight = 20;
        int visibleItemNum = 10;
        int listViewPaddingTop = 10;
        int firstVisiblePosition = 5;
        int dividerHeight = 1;
        ListView mockListView = getListView(itemViewHeight, visibleItemNum, listViewPaddingTop,
        firstVisiblePosition, dividerHeight);

        //calculate expected distanceToTop
        int expectedDistanceToTop = firstVisiblePosition * (itemViewHeight+dividerHeight) + listViewPaddingTop;
        SmoothScrollToTopTask task = new SmoothScrollToTopTask(mockListView);
        //invoke start()
        int distanceToTop = (int) ReflectUtil.invoke(SmoothScrollToTopTask.class, "calculateDistanceToTop", null, task);
        //assert
        assertEquals(expectedDistanceToTop, distanceToTop);

    }


    @Test
    public void should_return_a_value_smaller_than_distance_to_top_when_calculateDistanceToTop_with_distance_to_top_bigger_than_listView_heigh_and_mScrollStartPos_is_zero() throws NoSuchMethodException, IllegalAccessException {
        int itemViewHeight = 20;
        int visibleItemNum = 3;
        int listViewPaddingTop = 10;
        int firstVisiblePosition = 5;
        int dividerHeight = 1;
        ListView mockListView = getListView(itemViewHeight, visibleItemNum, listViewPaddingTop,
                firstVisiblePosition, dividerHeight);

        //calculate expected value
        int expectedValue = visibleItemNum * (itemViewHeight+dividerHeight) + listViewPaddingTop;

        SmoothScrollToTopTask task = new SmoothScrollToTopTask(mockListView);
        //invoke start()
        int distanceToTop = (int) ReflectUtil.invoke(SmoothScrollToTopTask.class, "calculateDistanceToTop", null, task);
        //assert
        assertEquals(expectedValue, distanceToTop);
    }


    @Test
    public void should_return_value_relative_to_mScrollStartPos_when_calculateDistanceToTop_with_mScrollStartPos_is_not_zero() throws NoSuchMethodException, IllegalAccessException {
        int itemViewHeight = 20;
        int visibleItemNum = 3;
        int listViewPaddingTop = 10;
        int firstVisiblePosition = 5;
        int dividerHeight = 1;
        ListView mockListView = getListView(itemViewHeight, visibleItemNum, listViewPaddingTop,
                firstVisiblePosition, dividerHeight);
        //init scrollStartPosition to not zero
        int scrollStartPosition = 2;
        int duration = 0;
        SmoothScrollToTopTask task = new SmoothScrollToTopTask(mockListView, scrollStartPosition, duration);
        //calculate expected distanceToTop
        int expectedValue = scrollStartPosition * (itemViewHeight + dividerHeight) + listViewPaddingTop;

        //invoke start()
        int distanceToTop = (int) ReflectUtil.invoke(SmoothScrollToTopTask.class, "calculateDistanceToTop", null, task);
        //assert
        assertEquals(expectedValue, distanceToTop);
    }


    @Test
    public void should_return_value_relative_to_firstVisiblePosition_when_calculateDistanceToTop_with_mScrollStartPos_is_not_zero_and_mScrollStartPos_bigger_than_firstVisiblePosition() throws NoSuchMethodException, IllegalAccessException {
        int itemViewHeight = 20;
        int visibleItemNum = 3;
        int listViewPaddingTop = 10;
        int firstVisiblePosition = 5;
        int dividerHeight = 1;
        ListView mockListView = getListView(itemViewHeight, visibleItemNum, listViewPaddingTop, firstVisiblePosition, dividerHeight);
        //init scrollStartPosition to not zero
        int scrollStartPosition = 10;
        int duration = 0;
        SmoothScrollToTopTask task = new SmoothScrollToTopTask(mockListView, scrollStartPosition, duration);
        //calculate expected distanceToTop
        int expectedValue = firstVisiblePosition * (itemViewHeight + dividerHeight) + listViewPaddingTop;

        //invoke start()
        int distanceToTop = (int) ReflectUtil.invoke(SmoothScrollToTopTask.class, "calculateDistanceToTop", null, task);
        //assert
        assertEquals(expectedValue, distanceToTop);
    }

    public ListView getListView(int itemViewHeight, int visibleItemNum, int listViewPaddingTop, int firstVisiblePosition , int dividerHeight){
        //init SmoothScrollToTopTask#mListView
        View itemView = mock(View.class);
        //make SmoothScrollToTopTask#getItemHeight always return itemViewHeight
        when(itemView.getMeasuredHeight()).thenReturn(itemViewHeight);
        ListAdapter mockAdapter = mock(ListAdapter.class);
        when(mockAdapter.getView(anyInt(), ArgumentMatchers.isNull(View.class), any(ViewGroup.class))).thenReturn(itemView);

        //init Adapter
        ListView mockListView = mock(ListView.class);
        when(mockListView.getAdapter()).thenReturn(mockAdapter);

        //set mockListView's paddingTop
        when(mockListView.getPaddingTop()).thenReturn(listViewPaddingTop);

        //set mockListView's dividerHeight
        when(mockListView.getDividerHeight()).thenReturn(dividerHeight);

        //init mockListView's firstVisibleView and set firstVisibleView#mTop is 0
        View mockView = mock(View.class);
        int top = 0;
        when(mockView.getTop()).thenReturn(top);
        when(mockListView.getChildAt(0)).thenReturn(mockView);

        //set mockListView's firstVisiblePosition
        when(mockListView.getFirstVisiblePosition()).thenReturn(firstVisiblePosition);

        //set mockListView#height,ensure that listViewHeight bigger than distance to top
        // or smaller  than distance to top
        int listViewHeight = visibleItemNum * (itemViewHeight+dividerHeight);
        when(mockListView.getHeight()).thenReturn(listViewHeight);
        return mockListView;
    }

}
