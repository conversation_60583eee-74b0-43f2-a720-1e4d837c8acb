/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-17, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.utils;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowRingtoneManager;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.robolectric.annotation.Config;

import javax.annotation.meta.When;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockingDetails;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@Config(shadows = {ShadowRingtoneManager.class})
public class AlarmRingUtilsTest extends TestParent {
    @Test
    public void should__when_getMusicPathFromUriString_with() {
        mContext = spy(mContext);
        String uriStr = "uriStr";
        Uri uri = Uri.parse(uriStr);
        Cursor cursor = mock(Cursor.class);
        ContentResolver contentResolver = mock(ContentResolver.class);
        when(contentResolver.query(eq(uri), any(String[].class), (String) isNull(), (String[]) isNull(),
                (String) isNull())).thenReturn(cursor);
        when(mContext.getContentResolver()).thenReturn(contentResolver);
        String filePath = "filePath";
        when(cursor.getString(0)).thenReturn(filePath);
        when(cursor.moveToFirst()).thenReturn(true);
        //invoke getMusicPathFromUriString()
        String path = AlarmRingUtils.getMusicPathFromUriString(mContext, uriStr);
        //assert
        assertEquals(filePath, path);
    }

    @Test
    public void should_resultUri_equalsTo_uri_when_queryUriforAudio_with_path_notNull_and_cursor_notNull() throws NoSuchMethodException, IllegalAccessException {
        mContext = spy(mContext);
        Cursor cursor = mock(Cursor.class);
        long id = 1L;
        when(cursor.getLong(0)).thenReturn(id);
        when(cursor.moveToFirst()).thenReturn(true);
        ContentResolver contentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(contentResolver);
        Uri uri = Uri.parse("uriString");
        String path = "path";
        when(contentResolver.query(eq(uri), any(String[].class), anyString(), any(String[].class),
                (String) isNull())).thenReturn(cursor);
        //invoke queryUriforAudio()
        Uri resultUri = (Uri) ReflectUtil.invoke(AlarmRingUtils.class, "queryUriforAudio",
                new Object[]{mContext, uri, path}, null, Context.class, Uri.class, String.class);
        //assert
        uri = ContentUris.withAppendedId(uri, id);
        assertEquals(uri, resultUri);
    }

    @Test
    public void should_result_equalsTo_title_when_getRingtoneNameByUri_with_cursor_size_is_one_and_ringtoneUri_startWith_startStr() {
        mContext = spy(mContext);
        Cursor cursor = mock(Cursor.class);
        String startStr = "content://media/external/";
        Uri ringtoneUri = Uri.parse(startStr);
        String title = "title";
        String path = startStr + title;
        when(cursor.getString(0)).thenReturn(null);
        when(cursor.getString(1)).thenReturn(path);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        ContentResolver contentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(contentResolver);
        when(contentResolver.query(eq(ringtoneUri), any(String[].class), (String) isNull(),
                (String[]) isNull(), (String) isNull())).thenReturn(cursor);
        //invoke getRingtoneNameByUri()
        String result = AlarmRingUtils.getRingtoneNameByUri(mContext, ringtoneUri);
        //assert
        assertEquals(title, result);
    }

    @Test
    public void should_return_true_when_isMediaUriValid_with_Uri_isValid() {
        Context context = spy(mContext);
        String startStr = "content://media/external/";
        Uri ringtoneUri = Uri.parse(startStr);
        Cursor cursor = mock(Cursor.class);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        ContentResolver contentResolver = mock(ContentResolver.class);
        when(context.getContentResolver()).thenReturn(contentResolver);
        when(contentResolver.query(eq(ringtoneUri), any(String[].class), (String) isNull(),
                (String[]) isNull(), (String) isNull())).thenReturn(cursor);

        boolean isUriValid = AlarmRingUtils.isMediaUriValid(context, ringtoneUri);
        assertTrue(isUriValid);
    }

    @Test
    public void should_return_ActualDefaultRingtoneUri_Uri_when_buildRingtonePickIntent_with_defaultUri() {
        Context context = spy(mContext);
        Uri defaultUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM);
        int vibrate = 64;
        boolean showSilent = true;

        Intent intent = AlarmRingUtils.buildDefaultRingtonePickIntent(context, defaultUri, vibrate, showSilent, 1);

        Uri uri = intent.getParcelableExtra(RingtoneManager.EXTRA_RINGTONE_EXISTING_URI);
        //
        assertEquals(defaultUri, uri);

    }

    @Test
    public void should_right_results_when_isInternalRingtone_with_uri_different() {

        Uri uri = null;
        boolean isInternal = AlarmRingUtils.isInternalRingtone(uri);
        assertFalse(isInternal);

        Uri uri1 = MediaStore.Audio.Media.INTERNAL_CONTENT_URI;
        boolean isInternal1 = AlarmRingUtils.isInternalRingtone(uri1);
        assertTrue(isInternal1);

        String startStr2 = "xxxxx";
        uri = Uri.parse(startStr2);
        boolean isInternal2 = AlarmRingUtils.isInternalRingtone(uri);
        assertFalse(isInternal2);

    }

    @Test
    public void should__when_getRingNameByUri_with() {

        Context context = spy(mContext);
        String startStr2 = "xxxxx";
        Uri uri = Uri.parse(startStr2);
        Ringtone ringtone = RingtoneManager.getRingtone(context, uri);

        String title = null;
        if ((ringtone != null) && !TextUtils.isEmpty(ringtone.getTitle(context))) {
            title = ringtone.getTitle(context);
        }

        String titleReturn = AlarmRingUtils.getRingNameByUri(context, uri);

        assertNull(title, titleReturn);

    }

    @Test
    @Ignore
    public void should_return_the_incoming_Uri_when_getRealRingUri_with_UriIsNull_or_UriHaveNoPath_or_UriPathNotStartWithSystemUri() {
        Context context = spy(mContext);
        Uri uri = null;
        Uri uriReturn = AlarmRingUtils.getRealRingUri(context, uri);
        assertNull(uriReturn);

        uri = spy(Uri.class);
        Uri uriReturn2 = AlarmRingUtils.getRealRingUri(context, uri);
        assertNull(uriReturn2.getPath());

        String startStr = "xxxxx";
        uri = Uri.parse(startStr);
        Uri uriReturn3 = AlarmRingUtils.getRealRingUri(context, uri);
        assertEquals(startStr, uriReturn3.getPath());
    }

}
