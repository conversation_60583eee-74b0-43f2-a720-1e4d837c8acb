package com.oplus.alarmclock.utils;

import android.graphics.Color;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.BeforeClass;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ColorLinearGradientUtilTest extends TestParent {


    private int mRedStart;
    private int mBlueStart;
    private int mGreenStart;
    private int mRedEnd;
    private int mBlueEnd;
    private int mGreenEnd;

    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mRedStart = 0;
        mBlueStart = 0;
        mGreenStart = 0;
        mRedEnd = 0;
        mBlueEnd = 0;
        mGreenEnd = 0;
    }

    @Test
    public void should_when_with() throws NoSuchFieldException, IllegalAccessException {
        int radio = 90;
        int startColor = 255;
        int endColor = 255;

        ColorLinearGradientUtil mColorLinearGradientUtil = new ColorLinearGradientUtil(startColor, endColor);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mRedStart", mColorLinearGradientUtil, mRedStart);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mBlueStart", mColorLinearGradientUtil, mBlueStart);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mGreenStart", mColorLinearGradientUtil, mGreenStart);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mRedEnd", mColorLinearGradientUtil, mRedEnd);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mBlueEnd", mColorLinearGradientUtil, mBlueEnd);
        ReflectUtil.setFieldValue(ColorLinearGradientUtil.class, "mGreenEnd", mColorLinearGradientUtil, mGreenEnd);

        mRedStart = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mRedStart", mColorLinearGradientUtil);
        mBlueStart = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mBlueStart", mColorLinearGradientUtil);
        mGreenStart = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mGreenStart", mColorLinearGradientUtil);
        mRedEnd = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mRedEnd", mColorLinearGradientUtil);
        mBlueEnd = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mBlueEnd", mColorLinearGradientUtil);
        mGreenEnd = (int) ReflectUtil.getFieldValue(ColorLinearGradientUtil.class, "mGreenEnd", mColorLinearGradientUtil);

        int red = (int) (mRedStart + ((mRedEnd - mRedStart) * radio + 0.5));
        int green = (int) (mGreenStart + ((mGreenEnd - mGreenStart) * radio + 0.5));
        int blue = (int) (mBlueStart + ((mBlueEnd - mBlueStart) * radio + 0.5));
        int colorLocal = Color.argb(255, red, green, blue);
        int color = mColorLinearGradientUtil.getColor(radio);

        assertEquals(colorLocal, color);

    }
}