/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-17, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.utils;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import java.util.concurrent.TimeUnit;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class ClickUtilsTest extends TestParent {
    @Test
    public void testClickableWithLong() throws InterruptedException {
        boolean b = ClickUtils.clickable(-1);
        assertTrue(b);

        TimeUnit.MILLISECONDS.sleep(50);
        int Threshold1 = 40;
        boolean b1 = ClickUtils.clickable(Threshold1);
        assertTrue(b1);

        int Threshold2 = 200;
        TimeUnit.MILLISECONDS.sleep(100);
        boolean b2 = ClickUtils.clickable(Threshold2);
        assertFalse(b2);
    }

}
