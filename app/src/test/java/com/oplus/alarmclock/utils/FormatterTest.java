/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-23, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.utils;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmUtils;

import org.junit.Assert;
import org.junit.Test;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Random;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNull;

import android.util.Pair;

public class FormatterTest extends TestParent {
    private final int HOUR_IN_MIN = 60;
    private final int MIN_IN_SEC = 60;
    private final int HOURS_OF_WHOLE_DAY = 24;
    private final int SEC_IN_MILLISEC = 1000;
    private final long HOUR_IN_SEC = HOUR_IN_MIN * MIN_IN_SEC;
    private final long MIN_IN_MILLISEC = MIN_IN_SEC * SEC_IN_MILLISEC;
    private final long HOUR_IN_MILLISEC = HOUR_IN_MIN * MIN_IN_MILLISEC;

    @Test
    public void should_return_string_equals_to_expectedString_when_getCountDownTime_with_specific_time(){
        Random rand = new Random();
        DecimalFormat decimalFormat = new DecimalFormat();
        decimalFormat.setRoundingMode(RoundingMode.FLOOR);
        int repeat = 50;
        for(int i=0; i<repeat; i++){
            //calculate elapseTime
            //ensure that the values are bigger than 0
            int hour = rand.nextInt(HOURS_OF_WHOLE_DAY-1) + 1;
            int min = rand.nextInt(HOUR_IN_MIN-1) + 1;
            int sec = rand.nextInt(MIN_IN_SEC -1) + 1;
            long time = hour* HOUR_IN_SEC + min* MIN_IN_SEC + sec;
            //invoke getCountDownTime()
            String actualStr = Formatter.getCountDownTime(mContext,time);
            //build expected string
            String hourStr = AlarmUtils.getNumberFormattedQuantityString(mContext, R.plurals.hours_short, hour);
            String minStr = AlarmUtils.getNumberFormattedQuantityString(mContext, R.plurals.minutes_plurals, min);
            String secStr = AlarmUtils.getNumberFormattedQuantityString(mContext, R.plurals.timer_sec_plurals, sec);
            StringBuilder strBuilder = new StringBuilder(32);
            strBuilder.append(hourStr).append(minStr).append(secStr);
            String expectedStr = strBuilder.toString();
            //assert equals
            Assert.assertEquals(expectedStr,actualStr);
        }

    }
    @Test
    public void should_equals_to_expectedStr_when_getTimerStr_with_specific_duration(){
        Random rand = new Random();
        DecimalFormat decimalFormat = new DecimalFormat();
        int minIntegerDigits = 2;
        decimalFormat.setMinimumIntegerDigits(minIntegerDigits);
        int repeat = 50;
        for(int i=0; i<repeat; i++){
            int hour = rand.nextInt(HOURS_OF_WHOLE_DAY);
            int min = rand.nextInt(HOUR_IN_MIN);
            long sec = rand.nextInt(MIN_IN_SEC);
            System.out.println("hour: " + hour + " min: " + min + " sec: " + sec);
            long duration = hour*HOUR_IN_MILLISEC + min*MIN_IN_MILLISEC + sec*SEC_IN_MILLISEC;
            //invoke getTimerStr()
            String actualTimeStr = Formatter.getTimerStr(duration, mContext);
            //construct expectedTimeStr
            String timeSeparator = mContext.getResources().getString(R.string.time_separator);
            String hourStr = decimalFormat.format(hour);
            String minStr = decimalFormat.format(min);
            String secStr = decimalFormat.format(sec);
            StringBuilder sb = new StringBuilder(16);
            String expectedTimeStr = sb.append(hourStr).append(timeSeparator).append(minStr).
                    append(timeSeparator).append(secStr).toString();
            //assert
            Assert.assertEquals(expectedTimeStr, actualTimeStr);
        }

    }


    @Test
    public void should_return_format_string_have_amPm_and_seconds_When_get12ModeFormat_with_parameter(){
        float[] amPmRatio = new float[3];
        amPmRatio[0] = -1f;
        amPmRatio[1] = 0f;
        amPmRatio[2] = 1f;
        boolean[] includeSeconds = new boolean[2];
        includeSeconds[0] = true;
        includeSeconds[1] = false;
        String[] modeFormat = new String[4];
        modeFormat[0] = "hms";
        modeFormat[1] = "hm";
        modeFormat[2] = "hmsa";
        modeFormat[3] = "hma";

        for (float ap : amPmRatio) {
            for (boolean seconds : includeSeconds) {
                String result = Formatter.get12ModeFormat(ap, seconds).toString();
                if(seconds){
                    if (ap < 0){
                        assertEquals(modeFormat[0],result);
                    } else if(ap == 0){
                        assertEquals(modeFormat[0],result);
                    } else {
                        assertEquals(modeFormat[2],result);
                    }
                } else {
                    if (ap < 0){
                        assertEquals(modeFormat[1],result);
                    } else if(ap == 0){
                        assertEquals(modeFormat[1],result);
                    } else {
                        assertEquals(modeFormat[3],result);
                    }
                }
            }
        }
    }

    @Test
    public void should_return_pair_When_getTimerUnit_with_parameter() {
        Pair<String, String> result1 = Formatter.getTimerUnit(3600000L, mContext);
        assertNotEquals(result1, new Pair<>(1, "hour"));
        Pair<String, String> result2 = Formatter.getTimerUnit(60000L, mContext);
        assertNotEquals(result2, new Pair<>(1, "minute"));
        Pair<String, String> result3 = Formatter.getTimerUnit(6000L, mContext);
        assertNull(result3);
    }

    @Test
    public void should_getTimerDuration() {
        assertEquals("00:15:00", Formatter.getTimerDuration(900));
    }
}
