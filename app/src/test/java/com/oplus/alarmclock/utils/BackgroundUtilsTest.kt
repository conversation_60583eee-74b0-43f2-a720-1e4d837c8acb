/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - EditMenuClickUtilsTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/3/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/3/7     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.view.View
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.alarmclock.TestParent
import com.oplus.anim.EffectiveAnimationView
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import org.junit.Assert.*
import org.junit.Test

class BackgroundUtilsTest : TestParent() {
    override fun setUp() {
        super.setUp()
        mockkStatic(BackgroundUtils::class)
    }

    override fun tearDown() {
        super.tearDown()
        unmockkStatic(BackgroundUtils::class)
    }


    @Test
    fun should_setEmptyBackground_with_is_not_animating() {
        val view = EffectiveAnimationView(mContext)
        val spyView = spyk(view).apply {
            every { isAnimating } returns false
        }
        val isDark = COUIDarkModeUtil.isNightMode(mContext)
        BackgroundUtils.setEmptyBackground(isDark, view)
        assertEquals(view.visibility, View.VISIBLE)

        BackgroundUtils.setEmptyBackground(isDark, view, 0)
        assertEquals(view.visibility, View.VISIBLE)

        BackgroundUtils.setEmptyBackground(isDark, view, 4)
        assertEquals(view.visibility, View.VISIBLE)
    }

    @Test
    fun should_setEmptyBackground_with_is_light_os() {
        val view = EffectiveAnimationView(mContext)
        mockkStatic(ChannelManager::class)
        mockkStatic(LightOSUtils::class)
        mockkStatic(AppFeatureUtils::class)
        every { ChannelManager.getLightOSUtils().isLightOS() } returns true
        val isDark = COUIDarkModeUtil.isNightMode(mContext)
        BackgroundUtils.setEmptyBackground(isDark, view)
        assertEquals(view.visibility, View.VISIBLE)

        BackgroundUtils.setEmptyBackground(isDark, view, 0)
        assertEquals(view.visibility, View.VISIBLE)

        BackgroundUtils.setEmptyBackground(isDark, view, 4)
        assertEquals(view.visibility, View.VISIBLE)
        unmockkStatic(AppFeatureUtils::class)
        unmockkStatic(LightOSUtils::class)
        unmockkStatic(ChannelManager::class)
    }
}