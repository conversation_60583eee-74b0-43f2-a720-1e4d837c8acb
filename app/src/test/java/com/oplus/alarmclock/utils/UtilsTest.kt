/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - UtilsTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/11/22     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils

import android.app.Activity
import android.app.ActivityManager
import android.app.ActivityManager.RunningAppProcessInfo
import android.app.KeyguardManager
import android.content.ContentProviderOperation
import android.content.ContentProviderResult
import android.content.ContentResolver
import android.content.ContentUris
import android.content.Context
import android.content.OperationApplicationException
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.res.TypedArray
import android.os.RemoteException
import android.os.UserManager
import android.provider.Settings
import android.util.AttributeSet
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.heytap.addon.confinemode.OplusConfineModeManager
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.alarmclock.AlarmSchedule
import com.oplus.alarmclock.alarmclock.ScheduleUtils
import com.oplus.alarmclock.alert.CurrentAlarmScheduleHolder
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.clock.common.osdk.SystemPropNativeUtils
import com.oplus.clock.common.osdk.SystemPropNativeUtils.getInt
import com.oplus.clock.common.osdk.UserNativeUtils
import com.oplus.compat.view.WindowManagerNative
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import java.time.LocalDate
import java.time.Month

@Suppress("LargeClass")
class UtilsTest : TestParent() {
    override fun setUp() {
        super.setUp()
        mockkStatic(Utils::class)
    }

    override fun tearDown() {
        super.tearDown()
        unmockkStatic(Utils::class)
    }

    @Test
    fun should_isMOrLater_within_m() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", 0) } returns 23
        val result = Utils.isMOrLater()
        Assert.assertTrue(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isNOrLater_within_n() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 24
        val result = Utils.isNOrLater()
        Assert.assertTrue(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isAboveO_within_o() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 26
        val result = Utils.isAboveO()
        Assert.assertTrue(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isAboveQ_without_q() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 28
        val result = Utils.isAboveQ()
        Assert.assertFalse(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isAboveR_without_r() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 28
        val result = Utils.isAboveR()
        Assert.assertFalse(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isAboveS_without_s() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 28
        val result = Utils.isAboveS()
        Assert.assertFalse(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isAboveT_without_t() {
        mockkStatic(SystemPropNativeUtils::class)
        every { getInt("ro.build.version.sdk", any()) } returns 28
        val result = Utils.isAboveT()
        Assert.assertFalse(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_setHomeKeyLocked_with_lock_and_enable_status_bar() {
        val activity = mockk<Activity>()
        val window = mockk<Window>()
        val layoutParams = mockk<WindowManager.LayoutParams>()
        every { activity.window } returns window
        every { window.attributes } returns layoutParams
        Utils.setHomeKeyLocked(
            activity, Utils.KEY_LOCK_MODE_HOME_MENU, Utils.ENABLE_STATUS_BAR)
        val result = WindowManagerNative.LayoutParamsNative
            .getHomeAndMenuKeyState(activity.window.attributes)
        Assert.assertEquals(result, 0)
    }

    @Test
    fun should_setHomeKeyLocked_with_unlock_and_disable_status_bar() {
        val activity = mockk<Activity>()
        val window = mockk<Window>()
        val layoutParams = mockk<WindowManager.LayoutParams>()
        every { activity.window } returns window
        every { window.attributes } returns layoutParams
        Utils.setHomeKeyLocked(
            activity, Utils.KEY_LOCK_MODE_NORMAL, Utils.DISABLE_STATUS_BAR)
        val result = WindowManagerNative.LayoutParamsNative
            .getHomeAndMenuKeyState(activity.window.attributes)
        Assert.assertEquals(result, 0)
    }

    @Test
    fun should_setHomeKeyLocked_with_unlock_and_default_status_bar() {
        val activity = mockk<Activity>()
        val window = mockk<Window>()
        val layoutParams = mockk<WindowManager.LayoutParams>()
        every { activity.window } returns window
        every { window.attributes } returns layoutParams
        Utils.setHomeKeyLocked(
            activity, Utils.KEY_LOCK_MODE_NORMAL, Utils.DEFAULT_STATUS_BAR)
        val result = WindowManagerNative.LayoutParamsNative
            .getHomeAndMenuKeyState(activity.window.attributes)
        Assert.assertEquals(result, 0)
    }

    @Test
    fun should_isKeyguardLock_return_true() {
        val km = mockk<KeyguardManager>()
        val context = mockk<Context>()
        every { context.getSystemService(Context.KEYGUARD_SERVICE) } returns km
        every { km.inKeyguardRestrictedInputMode() } returns true
        val result = Utils.isKeyguardLock(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isKeyguardLock_return_false() {
        val km = mockk<KeyguardManager>()
        val context = mockk<Context>()
        every { context.getSystemService(Context.KEYGUARD_SERVICE) } returns km
        every { km.inKeyguardRestrictedInputMode() } returns false
        val result = Utils.isKeyguardLock(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isSpeechAiAvailable_with_custom_safe() {
        val context = mockk<Context>()
        mockkStatic(DeviceUtils::class)
        every { DeviceUtils.isCustomSafeVersion(context) } returns true
        val result = Utils.isSpeechAiAvailable(context)
        Assert.assertFalse(result)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_isSpeechAiAvailable_with_work_mode() {
        val context = mockk<Context>()
        mockkStatic(DeviceUtils::class)
        mockkStatic(UserNativeUtils::class)
        every { DeviceUtils.isCustomSafeVersion(context) } returns false
        every { UserNativeUtils.USER_CURRENT } returns ClockConstant.WORKMODE_USER_ID
        val result = Utils.isSpeechAiAvailable(context)
        Assert.assertFalse(result)
        unmockkStatic(DeviceUtils::class)
        unmockkStatic(UserNativeUtils::class)
    }

    @Test
    fun should_isSpeechAiAvailable_with_oplus_assistant_installed() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_OPLUS
        }
        mockkStatic(DeviceUtils::class)
        mockkStatic(UserNativeUtils::class)
        every { DeviceUtils.isCustomSafeVersion(context) } returns false
        every { UserNativeUtils.USER_CURRENT } returns ClockConstant.OWNER_USER_ID
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns packageInfo
        val result = Utils.isSpeechAiAvailable(context)
        Assert.assertTrue(result)
        unmockkStatic(DeviceUtils::class)
        unmockkStatic(UserNativeUtils::class)
    }

    @Test
    fun should_isSpeechAiAvailable_with_heytap_assistant_installed() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_NEW
        }
        mockkStatic(DeviceUtils::class)
        mockkStatic(UserNativeUtils::class)
        every { DeviceUtils.isCustomSafeVersion(context) } returns false
        every { UserNativeUtils.USER_CURRENT } returns ClockConstant.OWNER_USER_ID
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns null
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_NEW,
            PackageManager.GET_ACTIVITIES) } returns packageInfo
        val result = Utils.isSpeechAiAvailable(context)
        Assert.assertTrue(result)
        unmockkStatic(DeviceUtils::class)
        unmockkStatic(UserNativeUtils::class)
    }

    @Test
    fun should_isSpeechAiAvailable_with_notfound_assist() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_NEW
        }
        mockkStatic(DeviceUtils::class)
        mockkStatic(UserNativeUtils::class)
        every { DeviceUtils.isCustomSafeVersion(context) } returns false
        every { UserNativeUtils.USER_CURRENT } returns ClockConstant.OWNER_USER_ID
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(any() as String,
            PackageManager.GET_ACTIVITIES) } throws PackageManager.NameNotFoundException()
        val result = Utils.isSpeechAiAvailable(context)
        Assert.assertFalse(result)
        unmockkStatic(DeviceUtils::class)
        unmockkStatic(UserNativeUtils::class)
    }

    @Test
    fun should_setSuitableFontSize_with_view_is_null() {
        val result = Utils.setSuitableFontSize(null, 1.0f, 0)
        Assert.assertEquals(result, 0.0f)
    }

    @Test
    fun should_setSuitableFontSize_with_g1() {
        val view = mockk<TextView>()
        every { view.textSize } returns 30.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G1)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.0f, COUIChangeTextUtil.G1)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_setSuitableFontSize_with_fontScale14() {
        val view = mockk<TextView>()
        every { view.textSize } returns 30.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G1)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.4f, COUIChangeTextUtil.G1)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_setSuitableFontSize_with_g2() {
        val view = mockk<TextView>()
        every { view.textSize } returns 38.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G2)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.0f, COUIChangeTextUtil.G2)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_setSuitableFontSize_with_g3() {
        val view = mockk<TextView>()
        every { view.textSize } returns 20.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G3)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.0f, COUIChangeTextUtil.G3)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_setSuitableFontSize_with_g4() {
        val view = mockk<TextView>()
        every { view.textSize } returns 10.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G4)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.0f, COUIChangeTextUtil.G4)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_setSuitableFontSize_with_g5() {
        val view = mockk<TextView>()
        every { view.textSize } returns 60.0f
        every { view.setTextSize(any(), any()) } returns Unit
        val result = Utils.setSuitableFontSize(view, 1.0f, COUIChangeTextUtil.G5)
        val compileSize = COUIChangeTextUtil.getSuitableFontSize(
            view.textSize, 1.0f, COUIChangeTextUtil.G5)
        Assert.assertEquals(result, compileSize)
    }

    @Test
    fun should_startAiClock_with_oplus_assist_installed() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_OPLUS
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns packageInfo
        Utils.startAiClock(context)
    }

    @Test
    fun should_startAiClock_with_coloros60_speech_assist() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns null
        every { Utils.isColoros60SpeechAssist(context) } returns true
        Utils.startAiClock(context)
    }

    @Test
    fun should_startAiClock_with_coloros70_speech_assist() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns null
        every { Utils.isColoros60SpeechAssist(context) } returns false
        every { Utils.isColoros70SpeechAssist(context) } returns true
        Utils.startAiClock(context)
    }

    @Test
    fun should_startAiClock_with_action_voice_command() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_OPLUS,
            PackageManager.GET_ACTIVITIES) } returns null
        every { Utils.isColoros60SpeechAssist(context) } returns false
        every { Utils.isColoros70SpeechAssist(context) } returns false
        Utils.startAiClock(context)
    }

    @Test
    fun should_applyDbOperations_with_operations_is_null() {
        val context = mockk<Context>()
        val result = Utils.applyDbOperations(context, null)
        Assert.assertTrue(result)
    }

    @Test
    fun should_applyDbOperations_with_operations_is_empty() {
        val context = mockk<Context>()
        val operations = mockk<ArrayList<ContentProviderOperation>>()
        every { operations.isEmpty() } returns true
        val result = Utils.applyDbOperations(context, operations)
        Assert.assertTrue(result)
    }

    @Test
    fun should_applyDbOperations_with_operations_not_empty() {
        val context = mockk<Context>()
        val providerResult = mockk<ContentProviderResult>()
        val operations = ArrayList<ContentProviderOperation>().apply {
            val uri = ContentUris.withAppendedId(ClockContract.ALARM_CONTENT_URI, 0)
            this.add(ContentProviderOperation.newDelete(uri).build())
        }
        every { context.contentResolver.applyBatch(any() as String, operations) } returns arrayOf(
            providerResult
        )
        val result = Utils.applyDbOperations(context, operations)
        Assert.assertTrue(result)
    }

    @Test
    fun should_applyDbOperations_with_throw_remote_exception() {
        val context = mockk<Context>()
        val operations = mockk<ArrayList<ContentProviderOperation>>()
        every { operations.isEmpty() } returns false
        every { context.contentResolver.applyBatch(any() as String, operations) } throws RemoteException()
        val result = Utils.applyDbOperations(context, operations)
        Assert.assertFalse(result)
    }

    @Test
    fun should_applyDbOperations_with_throw_operation_application_exception() {
        val context = mockk<Context>()
        val operations = mockk<ArrayList<ContentProviderOperation>>()
        every { operations.isEmpty() } returns false
        every { context.contentResolver.applyBatch(
            any() as String, operations) } throws OperationApplicationException()
        val result = Utils.applyDbOperations(context, operations)
        Assert.assertFalse(result)
    }

    @Test
    fun should_setWindowStyle_with_activity_is_null() {
        Utils.setWindowStyle(null)
    }

    @Test
    fun should_setWindowStyle_with_window_is_null() {
        val activity = mockk<Activity>()
        every { activity.window } returns null
        Utils.setWindowStyle(activity)
    }

    @Test
    fun should_setWindowStyle_with_theme_dark() {
        val activity = mockk<Activity>()
        val window = mockk<Window>()
        val decorView = spyk<View>().apply {
            systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
        }
        every { activity.window } returns window
        every { window.decorView } returns decorView
        every { activity.packageManager.hasSystemFeature("oplus.amoled.theme") } returns true
        every { activity.packageManager.hasSystemFeature("oppo.amoled.theme") } returns true
        justRun { window.addFlags(any()) }
        every { decorView.systemUiVisibility } returns
                (View.SYSTEM_UI_FLAG_FULLSCREEN and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())

        Utils.setWindowStyle(activity)
        val result = decorView.systemUiVisibility
        println(result)
        Assert.assertEquals(result, 4)
    }

    @Test
    fun should_setWindowStyle_with_theme_day() {
        val activity = mockk<Activity>()
        val window = mockk<Window>()
        val decorView = spyk<View>().apply {
            systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
        }
        every { activity.window } returns window
        every { window.decorView } returns decorView
        every { activity.packageManager.hasSystemFeature("oplus.amoled.theme") } returns false
        every { activity.packageManager.hasSystemFeature("oppo.amoled.theme") } returns false
        justRun { window.addFlags(any()) }
        every { decorView.systemUiVisibility } returns
                (View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
        Utils.setWindowStyle(activity)
        val result = decorView.systemUiVisibility
        Assert.assertEquals(result, 8196)
    }

    @Test
    fun should_isGestureNavMode_with_context_null() {
        val result = Utils.isGestureNavMode(null)
        Assert.assertFalse(result)
    }

    @Test
    @Ignore
    fun should_isGestureNavMode_with_no_fully_gestural() {
        val context = mockk<Context>()
        val resolver = mockk<ContentResolver>()
        every { context.contentResolver } returns resolver
        every { Settings.Secure.getInt(resolver, "navigation_mode", 0) } returns 0
        val result = Utils.isGestureNavMode(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isColoros60SpeechAssist_with_package_version_500() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = "com.coloros.speechassist"
            versionCode = 500
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo("com.coloros.speechassist", 0) } returns packageInfo

        val result = Utils.isColoros60SpeechAssist(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isColoros60SpeechAssist_with_package_version_499() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = "com.coloros.speechassist"
            versionCode = 499
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo("com.coloros.speechassist", 0) } returns packageInfo

        val result = Utils.isColoros60SpeechAssist(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isColoros60SpeechAssist_with_package_version_1000() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = "com.coloros.speechassist"
            versionCode = 1000
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo("com.coloros.speechassist", 0) } returns packageInfo

        val result = Utils.isColoros60SpeechAssist(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isColoros60SpeechAssist_throw_exception() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.getPackageInfo("com.coloros.speechassist", 0) } throws Exception()

        val result = Utils.isColoros60SpeechAssist(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isColoros70SpeechAssist_with_package_version_500() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_NEW
            versionCode = 500
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_NEW, 0) } returns packageInfo

        val result = Utils.isColoros70SpeechAssist(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isColoros70SpeechAssist_with_package_version_499() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_NEW
            versionCode = 499
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_NEW, 0) } returns packageInfo

        val result = Utils.isColoros70SpeechAssist(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isColoros70SpeechAssist_with_package_version_1000() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            packageName = Utils.SPEECH_ASSIST_PACKAGE_NEW
            versionCode = 1000
        }
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_NEW, 0) } returns packageInfo

        val result = Utils.isColoros70SpeechAssist(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isColoros70SpeechAssist_throw_exception() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.getPackageInfo(Utils.SPEECH_ASSIST_PACKAGE_NEW, 0) } throws Exception()

        val result = Utils.isColoros70SpeechAssist(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isHardwareLinermotorSupport_with_support() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.hasSystemFeature("oplus.hardware.linermotor.support") } returns true

        val result = Utils.isHardwareLinermotorSupport(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isHardwareLinermotorSupport_with_not_support() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.hasSystemFeature("oplus.hardware.linermotor.support") } returns false
        every { pm.hasSystemFeature("oppo.hardware.linermotor.support") } returns false
        val result = Utils.isHardwareLinermotorSupport(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isHardwareLinermotorSupport_with_oppo_support() {
        val context = mockk<Context>()
        val pm = mockk<PackageManager>()
        every { context.packageManager } returns pm
        every { pm.hasSystemFeature("oplus.hardware.linermotor.support") } returns false
        every { pm.hasSystemFeature("oppo.hardware.linermotor.support") } returns true
        val result = Utils.isHardwareLinermotorSupport(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_getDateSettingsIntent() {
        val result = Utils.getDateSettingsIntent()
        Assert.assertEquals(result.action, "android.settings.DATE_SETTINGS")
    }

    @Test
    fun should_screenChangedByPowerButton_with_power_button() {
        mockkStatic(SystemPropNativeUtils::class)
        every {
            getInt(
                ClockConstant.SCREEN_OFF_REASON,
                ClockConstant.GO_TO_SLEEP_REASON_POWER_BUTTON
            )
        } returns ClockConstant.GO_TO_SLEEP_REASON_POWER_BUTTON
        val result = Utils.screenChangedByPowerButton()
        Assert.assertTrue(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_screenChangedByPowerButton_without_power_button() {
        mockkStatic(SystemPropNativeUtils::class)
        every {
            getInt(
                ClockConstant.SCREEN_OFF_REASON,
                ClockConstant.GO_TO_SLEEP_REASON_POWER_BUTTON
            )
        } returns 0
        val result = Utils.screenChangedByPowerButton()
        Assert.assertFalse(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isUserKeyUnlocked_without_unlocked() {
        val context = mockk<Context>()
        val userManger = mockk<UserManager>()
        every { context.getSystemService(Context.USER_SERVICE) } returns userManger
        every { userManger.isUserUnlocked } returns false

        val result = Utils.isUserKeyUnlocked(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isUserKeyUnlocked_with_unlocked() {
        val context = mockk<Context>()
        val userManger = mockk<UserManager>()
        every { context.getSystemService(Context.USER_SERVICE) } returns userManger
        every { userManger.isUserUnlocked } returns true

        val result = Utils.isUserKeyUnlocked(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isUserKeyUnlocked_with_user_manager_null() {
        val context = mockk<Context>()
        every { context.getSystemService(Context.USER_SERVICE) } returns null

        val result = Utils.isUserKeyUnlocked(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_screenChangedByApplication_without_power_button() {
        mockkStatic(SystemPropNativeUtils::class)
        every {
            getInt(
                ClockConstant.SCREEN_OFF_REASON,
                ClockConstant.GO_TO_SLEEP_REASON_POWER_BUTTON
            )
        } returns 0
        val result = Utils.screenChangedByApplication()
        Assert.assertTrue(result)
        unmockkStatic(SystemPropNativeUtils::class)
    }

    @Test
    fun should_isDay_with_hour() {
        var hour = 6
        val result = Utils.isDay(hour)
        Assert.assertTrue(result)

        hour = 18
        val result1 = Utils.isDay(hour)
        Assert.assertFalse(result1)
    }

    @Test
    fun should_isSystemUser_without_systemuser() {
        val context = mockk<Context>()
        val userManger = mockk<UserManager>()
        every { context.applicationContext } returns context
        every { context.getSystemService(Context.USER_SERVICE) } returns userManger
        every { userManger.isSystemUser } returns false

        val result = Utils.isSystemUser(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isSystemUser_with_systemuser() {
        val context = mockk<Context>()
        val userManger = mockk<UserManager>()
        every { context.applicationContext } returns context
        every { context.getSystemService(Context.USER_SERVICE) } returns userManger
        every { userManger.isSystemUser } returns true

        val result = Utils.isSystemUser(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isSystemUser_with_user_manager_null() {
        val context = mockk<Context>()
        every { context.applicationContext } returns context
        every { context.getSystemService(Context.USER_SERVICE) } returns null

        val result = Utils.isSystemUser(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_supportConfineMode_with_less_r() {
        every { Utils.isAboveR() } returns false

        val result = Utils.supportConfineMode()
        Assert.assertFalse(result)
    }

    @Test
    fun should_supportConfineMode_with_not_support() {
        val manager = mockk<OplusConfineModeManager>()
        every { Utils.isAboveR() } returns true
        every { manager.confineMode } returns 0
        val result = Utils.supportConfineMode()
        Assert.assertFalse(result)
    }

    @Test
    fun should_getCloseOnceToastString_with_open_after_today() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-21"

        val time = 1700524800000L //2023-11-21 8:0:0
        val result = Utils.getCloseOnceToastString(time)
        val compiler = "Turned off. Alarm will ring again after Nov 21, 2023 (today)."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getCloseOnceToastString_with_tomorrow() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1700611200000L //2023-11-22 8:0:0
        val result = Utils.getCloseOnceToastString(time)
        val compiler = "Turned off. Alarm will ring again after Nov 22, 2023 (tomorrow)."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getCloseOnceToastString_with_after_time_day() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1701302400000L //2023-11-30 8:0:0
        val result = Utils.getCloseOnceToastString(time)
        val compiler = "Turned off. Alarm will ring again after Nov 30, 2023."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTime_with_alarm_null() {
        val result = Utils.getNextAlarmTime(mContext, null)
        Assert.assertEquals(result, 0L)
    }

    @Test
    fun should_getNextAlarmTime_with_alarm_state_over_silent_state() {
        mockkStatic(ScheduleUtils::class)
        mockkStatic(CurrentAlarmScheduleHolder::class)
        val alarm = mockk<Alarm>().apply {
            every { id } returns 0
            every { hour } returns 8
            every { minutes } returns 0
            every { time } returns 1700524800000L
            every { repeatSet } returns 1
            every { workdaySwitch } returns 0
            every { isEnabled } returns true
        }
        val schedule1 = mockk<AlarmSchedule>().apply {
            every { alarmId } returns 0
            every { getAlarm() } returns alarm
            every { alarmState } returns 1
            every { time } returns 1700524800000L
        }

        val schedule2 = mockk<AlarmSchedule>().apply {
            every { alarmId } returns 0
            every { getAlarm() } returns alarm
            every { alarmState } returns 1
            every { time } returns 1700528400000L
        }

        every { alarm.isEnabled } returns true
        every { ScheduleUtils.getSchedulesOfAlarm(
            AlarmClockApplication.getInstance(), alarm) } returns arrayListOf(schedule1)
        every { CurrentAlarmScheduleHolder.getAlarmSchedule() } returns schedule1
        every { ScheduleUtils.getNextSchedules(mContext, any()) } returns arrayListOf(schedule2)

        val result = Utils.getNextAlarmTime(mContext, alarm)
        Assert.assertEquals(result, 1700528400000L)
        unmockkStatic(ScheduleUtils::class)
        unmockkStatic(CurrentAlarmScheduleHolder::class)
    }

    @Test
    fun should_getNextAlarmTime_with_alarm_silent_state() {
        mockkStatic(ScheduleUtils::class)
        mockkStatic(CurrentAlarmScheduleHolder::class)
        val alarm = mockk<Alarm>().apply {
            every { id } returns 0
            every { hour } returns 8
            every { minutes } returns 0
            every { time } returns 1700524800000L
            every { repeatSet } returns 1
            every { workdaySwitch } returns 0
            every { isEnabled } returns true
        }
        val schedule1 = mockk<AlarmSchedule>().apply {
            every { alarmId } returns 0
            every { getAlarm() } returns alarm
            every { alarmState } returns 0
            every { time } returns 1700524800000L
        }

        val schedule2 = mockk<AlarmSchedule>().apply {
            every { alarmId } returns 1
            every { getAlarm() } returns alarm
            every { alarmState } returns 1
            every { time } returns 1700528400000L
        }

        every { alarm.isEnabled } returns true
        every { ScheduleUtils.getSchedulesOfAlarm(
            AlarmClockApplication.getInstance(), alarm) } returns arrayListOf(schedule1)
        every { CurrentAlarmScheduleHolder.getAlarmSchedule() } returns schedule2
        every { ScheduleUtils.getNextSchedules(mContext, any()) } returns arrayListOf(schedule2)

        val result = Utils.getNextAlarmTime(mContext, alarm)
        Assert.assertEquals(result, 1700524800000L)
        unmockkStatic(ScheduleUtils::class)
        unmockkStatic(CurrentAlarmScheduleHolder::class)
    }

    @Test
    fun should_getNextAlarmTime_with_alarm_shedule_null() {
        mockkStatic(ScheduleUtils::class)
        val alarm = mockk<Alarm>().apply {
            every { id } returns 0
            every { hour } returns 9
            every { minutes } returns 0
            every { isEnabled } returns true
        }
        every { ScheduleUtils.getSchedulesOfAlarm(AlarmClockApplication.getInstance(), alarm) } returns null

        val result = Utils.getNextAlarmTime(mContext, null)
        Assert.assertEquals(result, 0L)
        unmockkStatic(ScheduleUtils::class)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_today_open_today() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-21"

        val closeTime = 1700524800000L //2023-11-21 8:0:0
        val nextTime = 1700528400000L //2023-11-21 9:0:0
        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        println(result)
        val compiler = "Turned off for today. Turns back on: Today."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_today_open_tomorrow() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val closeTime = 1700524800000L //2023-11-21 8:0:0
        val nextTime = 1700611200000L //2023-11-22 8:0:0
        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        val compiler = "Turned off for today. Turns back on: Tomorrow."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_today_open_after_time() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-24"

        val closeTime = 1700524800000L //2023-11-21 8:0:0
        val nextTime = 1700784000000L //2023-11-24 8:0:0
        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        val compiler = "Turned off for today. Turns back on: Nov 24, 2023."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_tomorrow_open_tomorrow() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val closeTime = 1700611200000L //2023-11-22 8:0:0
        val nextTime = 1700614800000L //2023-11-22 9:0:0
        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        val compiler = "Turned off for tomorrow. Turns back on: Tomorrow."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_tomorrow_open_after_time() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-24"

        val closeTime = 1700611200000L //2023-11-22 8:0:0
        val nextTime = 1700784000000L //2023-11-24 8:0:0
        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        val compiler = "Turned off for tomorrow. Turns back on: Nov 24, 2023."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getNextAlarmTipsString_with_close_after_time() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325

        val closeTime = 1700784000000L //2023-11-24 8:0:0
        val nextTime = 1701133200000L //2023-11-28 8:0:0

        every { Utils.getLocaleDateMDFormat(context, closeTime) } returns "2023-11-24"
        every { Utils.getLocaleDateMDFormat(context, nextTime) } returns "2023-11-28"

        val result = Utils.getNextAlarmTipsString(closeTime, nextTime)
        val compiler = "Turned off on Nov 24, 2023. Turns back on: Nov 28, 2023."
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getDialogDayDiffString_with_open_after_today() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-21"

        val time = 1700524800000L //2023-11-21 8:0:0
        val result = Utils.getDialogDayDiffString(time)
        val compiler = "Turn off for today"
        Assert.assertEquals(result, compiler)
    }

    @Test
    @Ignore
    fun should_getDialogDayDiffString_with_tomorrow() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1700611200000L //2023-11-22 8:0:0
        val result = Utils.getDialogDayDiffString(time)
        val compiler = "Skip tomorrow"
        Assert.assertEquals(result, compiler)
    }

    @Test
    @Ignore
    fun should_getDialogDayDiffString_with_after_time_day() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1701302400000L //2023-11-30 8:0:0
        val result = Utils.getDialogDayDiffString(time)
        val compiler = "Skip Nov 30, 2023"
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getAlarmNextTimeString_with_open_after_today() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-21"

        val time = 1700524800000L //2023-11-21 8:0:0
        val result = Utils.getAlarmNextTimeString(time)
        val compiler = "Today"
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getAlarmNextTimeString_with_tomorrow() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1700611200000L //2023-11-22 8:0:0
        val result = Utils.getAlarmNextTimeString(time)
        val compiler = "Tomorrow"
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getAlarmNextTimeString_with_after_time_day() {
        val context = mockk<Context>()
        val localeDate = mockk<LocalDate>()
        mockkStatic(LocalDate::class)
        every { LocalDate.now() } returns localeDate
        every { localeDate.year } returns 2023
        every { localeDate.month } returns Month.NOVEMBER
        every { localeDate.dayOfYear } returns 325
        every { Utils.getLocaleDateMDFormat(context, any()) } returns "2023-11-22"

        val time = 1701302400000L //2023-11-30 8:0:0
        val result = Utils.getAlarmNextTimeString(time)
        val compiler = "Nov 30, 2023"
        Assert.assertEquals(result, compiler)
    }

    @Test
    fun should_getDialVersion_with_context_is_null() {
        val context = mockk<Context>()
        every { Utils.getContextFromPkg(
            AlarmClockApplication.getInstance().applicationContext,
            "com.oplus.smartengine")
        } returns null

        val result = Utils.getDialVersion()
        Assert.assertEquals(result, Utils.VERSION_12)
    }

    @Test
    fun should_getDialVersion_with_version_empty() {
        val context = mockk<Context>().apply {
            every { packageName } returns "com.oplus.smartengine"
        }
        val packageManager = mockk<PackageManager>()
        val packageInfo = mockk<PackageInfo>().apply {
            versionName = ""
        }
        every { Utils.getContextFromPkg(
            AlarmClockApplication.getInstance().applicationContext,
            "com.oplus.smartengine")
        } returns context
        every { context.packageManager } returns packageManager
        every { context.packageManager.getPackageInfo(context.packageName,
            PackageManager.GET_META_DATA) } returns packageInfo

        val result = Utils.getDialVersion()
        Assert.assertEquals(result, Utils.VERSION_12)
    }

    @Test
    fun should_isMainProcessRunning_with_context_null() {
        val result = Utils.isMainProcessRunning(null)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isMainProcessRunning_with_no_packageName() {
        val context = mockk<Context>().apply {
            every { packageName } returns null
        }
        val result = Utils.isMainProcessRunning(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isMainProcessRunning_with_process_visible() {
        val context = mockk<Context>().apply {
            every { packageName } returns "com.coloros.alarmclock"
        }
        val processInfo1 = mockk<RunningAppProcessInfo>().apply {
            processName = "com.coloros.alarmclock"
            importance = RunningAppProcessInfo.IMPORTANCE_VISIBLE
        }
        val manager = mockk<ActivityManager>()
        every { context.getSystemService(Context.ACTIVITY_SERVICE) } returns manager
        every { manager.runningAppProcesses } returns arrayListOf(processInfo1)

        val result = Utils.isMainProcessRunning(context)
        Assert.assertTrue(result)
    }

    @Test
    fun should_isMainProcessRunning_with_process_no_visible() {
        val context = mockk<Context>().apply {
            every { packageName } returns "com.coloros.alarmclock"
        }
        val processInfo1 = mockk<RunningAppProcessInfo>().apply {
            processName = "com.coloros.alarmclock"
            importance = RunningAppProcessInfo.IMPORTANCE_SERVICE
        }
        val manager = mockk<ActivityManager>()
        every { context.getSystemService(Context.ACTIVITY_SERVICE) } returns manager
        every { manager.runningAppProcesses } returns arrayListOf(processInfo1)

        val result = Utils.isMainProcessRunning(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isMainProcessRunning_with_process_empty() {
        val context = mockk<Context>().apply {
            every { packageName } returns "com.coloros.alarmclock"
        }
        val manager = mockk<ActivityManager>()
        every { context.getSystemService(Context.ACTIVITY_SERVICE) } returns manager
        every { manager.runningAppProcesses } returns arrayListOf()

        val result = Utils.isMainProcessRunning(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_isMainProcessRunning_throw_exception() {
        val context = mockk<Context>().apply {
            every { packageName } returns "com.coloros.alarmclock"
        }
        val manager = mockk<ActivityManager>()
        every { context.getSystemService(Context.ACTIVITY_SERVICE) } returns manager
        every { manager.runningAppProcesses } throws Exception()

        val result = Utils.isMainProcessRunning(context)
        Assert.assertFalse(result)
    }

    @Test
    fun should_getAttrTextFontWeight_with_300() {
        val context = mockk<Context>()
        val attrs = mockk<AttributeSet>()
        val typeArray = mockk<TypedArray>()

        every { context.resources.obtainAttributes(attrs, any()) } returns typeArray
        every { typeArray.getInteger(any(), any()) } returns 300
        justRun { typeArray.recycle() }

        val fontWeight = 300
        val result = Utils.getAttrTextFontWeight(context, attrs, 0, fontWeight)
        Assert.assertEquals(result, fontWeight)
    }
}