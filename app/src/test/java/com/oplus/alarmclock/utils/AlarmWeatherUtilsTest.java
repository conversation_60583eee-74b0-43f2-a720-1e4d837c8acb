/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmWeatherUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2020/3/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2020/3/7     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.provider.Settings;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.compat.provider.SettingsNative;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.HashMap;

import static org.junit.Assert.*;

public class AlarmWeatherUtilsTest extends TestParent {

    private AlarmWeatherUtils mAlarmWeatherUtils;
    private AlarmWeatherUtils.LoadWeatherTypeListener mListener;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mListener = Mockito.mock(AlarmWeatherUtils.LoadWeatherTypeListener.class);
        mAlarmWeatherUtils = new AlarmWeatherUtils(mListener);
    }

    @Test
    public void should_return_excepted_resId_when_getWeatherAlertResIdByWeatherType_with_newDataSource() throws NoSuchMethodException, IllegalAccessException {

        int exceptedResId;
        boolean isNewDateSource = true;
        HashMap<Integer, Integer> weatherTypeAndResIdMap = new HashMap<>();
        weatherTypeAndResIdMap.put(-1, R.raw.ringtone_weather_default);
        weatherTypeAndResIdMap.put(80, R.raw.ringtone_weather_default);

        for (Integer weatherType : weatherTypeAndResIdMap.keySet()) {
            exceptedResId = weatherTypeAndResIdMap.get(weatherType);
            int resId = ChannelManager.INSTANCE.getLightOSUtils().getWeatherAlertResIdByWeatherType(weatherType, isNewDateSource);
            if (ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
                assertEquals(exceptedResId, resId);
            } else {
                assertEquals(-1, resId);
            }
        }
    }

    @Test
    public void should_return_excepted_resId_when_getWeatherAlertResIdByWeatherType_with_oldDataSource() throws NoSuchMethodException, IllegalAccessException {
        int exceptedResId;
        boolean isNewDateSource = false;
        HashMap<Integer, Integer> weatherTypeAndResIdMap = new HashMap<>();
        weatherTypeAndResIdMap.put(-1, R.raw.ringtone_weather_default);
        weatherTypeAndResIdMap.put(50, R.raw.ringtone_weather_default);

        for (Integer weatherType : weatherTypeAndResIdMap.keySet()) {
            exceptedResId = weatherTypeAndResIdMap.get(weatherType);
            int resId = ChannelManager.INSTANCE.getLightOSUtils().getWeatherAlertResIdByWeatherType(weatherType, isNewDateSource);
            if (ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
                assertEquals(exceptedResId, resId);
            } else {
                assertEquals(-1, resId);
            }
        }
    }

    @Test
    public void should_call_mListener_when_getLocalWeatherInfoOld_with_invalid_weather_info() throws NoSuchMethodException, IllegalAccessException {

        String weatherInfo = "invalid info";
        Settings.Secure.putString(mContext.getContentResolver(),"oplus_weather_info", weatherInfo);
        ReflectUtil.invoke(AlarmWeatherUtils.class, "getLocalWeatherInfoOld",
                new Object[]{mContext}, mAlarmWeatherUtils, Context.class);

        Mockito.verify(mListener).onLoadComplete(Mockito.eq(false), Mockito.anyInt());
    }

    @Test
    public void should_return_excepted_alertName_when_getWeatherAlertStringName_with_weatherType() throws NoSuchMethodException, IllegalAccessException {

        HashMap<Integer, String> exceptedAlertName = new HashMap<>();
        exceptedAlertName.put(0, "weather_alarm_default.ogg");
        exceptedAlertName.put(5, "weather_alarm_rain.ogg");
        exceptedAlertName.put(9, "weather_alarm_thunderstorm2.ogg");
        exceptedAlertName.put(32, "weather_alarm_snow.ogg");
        exceptedAlertName.put(50, "weather_alarm_haze.ogg");
        exceptedAlertName.put(62, "weather_alarm_wind.ogg");

        for (Integer weatherType : exceptedAlertName.keySet()) {
            String alertName = (String) ReflectUtil.invoke(AlarmWeatherUtils.class, "getWeatherAlertStringName",
                    new Object[]{weatherType}, mAlarmWeatherUtils, int.class);
            assertEquals(exceptedAlertName.get(weatherType), alertName);
        }
    }

    @Test
    public void should_return_excepted_resId_when_getWeatherAlertResIdByWeatherType() throws NoSuchMethodException, IllegalAccessException {

        int exceptedResId;
        boolean isNewDateSource = true;
        HashMap<Integer, Integer> weatherTypeAndResIdMap = new HashMap<>();
        weatherTypeAndResIdMap.put(-1, R.raw.ringtone_weather_default);
        weatherTypeAndResIdMap.put(10, R.raw.ringtone_weather_default);
        weatherTypeAndResIdMap.put(80, R.raw.ringtone_weather_default);

        for (Integer weatherType : weatherTypeAndResIdMap.keySet()) {
            exceptedResId = weatherTypeAndResIdMap.get(weatherType);
            int resId = ChannelManager.INSTANCE.getLightOSUtils().getWeatherAlertResIdByWeatherType(weatherType, isNewDateSource);
            if (ChannelManager.INSTANCE.getLightOSUtils().supportWeatherAlert()) {
                assertEquals(exceptedResId, resId);
            } else {
                assertEquals(-1, resId);
            }
        }
    }

    @Implements(Utils.class)
    public static class ShadowUtils {

        public static boolean isAboveR() {
            return false;
        }
    }
}