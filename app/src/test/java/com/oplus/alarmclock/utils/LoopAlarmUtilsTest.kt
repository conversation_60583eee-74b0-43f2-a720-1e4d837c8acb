/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LoopAlarmUtilsTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/21
 ** Author      : helin
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  helin         2023/12/21      1.0        create
 ***********************************************************************/
package com.oplus.alarmclock.utils

import android.net.Uri
import com.oplus.alarmclock.R
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.alarmclock.AlarmUtils
import com.oplus.alarmclock.alarmclock.LegalHolidayUtil
import com.oplus.alarmclock.provider.ClockContract.ALARM_CONTENT_URI
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.mockito.Mockito
import java.util.Calendar

class LoopAlarmUtilsTest : TestParent() {

    @Test
    fun should_computeLoopCycleDate_whit_false() {
        val loopCycle = 4
        val loopDay = 2
        val nowTime = Calendar.getInstance()
        val create = Calendar.getInstance()
        mockkStatic(LoopAlarmUtils::class)
        val reset = LoopAlarmUtils.computeLoopCycleDate(loopCycle, loopDay, nowTime, create)
        Assert.assertNotNull(reset)
    }


    @Test
    fun should_computeLoopAlarmDateText_whit_text_cycle_date() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        val today = mContext.getString(R.string.today)
        val reset = LoopAlarmUtils.computeLoopAlarmDateText(mContext, 1, alarm)
        Assert.assertEquals(reset, today)
    }


    @Test
    fun should_computeLoopDays_whit_cycle_day_4() {
        val loopAlarmList = mutableListOf<Alarm>()
        for (index in 1..5) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                if (index % 2 == 0) {
                    isEnabled = true
                }
            }
            loopAlarmList.add(alarm)
        }
        mockkStatic(LoopAlarmUtils::class)
        val reset = LoopAlarmUtils.computeLoopDays(loopAlarmList)
        Assert.assertEquals(reset.first, 2)
    }

    @Test
    fun should_checkAlarmList_whit_work_day_list_add() {
        val loopAlarmList = mutableListOf<Alarm>()
        for (index in 1..5) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
            }
            loopAlarmList.add(alarm)
        }
        LoopAlarmUtils.checkAlarmList(loopAlarmList, 7)
        Assert.assertEquals(loopAlarmList.size, 7)
    }

    @Test
    @Ignore
    fun should_checkAlarmList_whit_work_day_list_remove() {
        val loopAlarmList = mutableListOf<Alarm>()
        for (index in 1..5) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
            }
            loopAlarmList.add(alarm)
        }
        LoopAlarmUtils.checkAlarmList(loopAlarmList, 3)
        Assert.assertEquals(loopAlarmList.size, 3)
    }


    @Test
    fun should_getPreviousAlarmTime_whit_time_now_ring_time() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        val proTime = LoopAlarmUtils.getPreviousAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(proTime)
    }

    @Test
    fun should_getPreviousAlarmTime_whit_time_now_ring_time_holiday_switch_on() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        mockkStatic(LegalHolidayUtil::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }

        val proTime = LoopAlarmUtils.getPreviousAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(proTime)
    }

    @Test
    fun should_getPreviousAlarmTime_whit_time_now_ring_time_and_holiday_list_is_null() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        mockkStatic(LegalHolidayUtil::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        every { LegalHolidayUtil.getHolidayFromCache(any()) } returns null
        val proTime = LoopAlarmUtils.getPreviousAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(proTime)
    }


    @Test
    fun should_getPreviousAlarmTime_whit_time_now_ring_time_and_holiday_switch_on_reset_not_null() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LoopAlarmUtils::class)
        mockkStatic(LegalHolidayUtil::class)
        val alarm = Alarm().apply {
            setmLoopCycleDays(4)
            setmLoopDay(2)
            setmLoopWorkDays(3)
            setmLoopRestDays("2#3#")
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        every { LegalHolidayUtil.getHolidayFromCache(any()) } returns null
        val proTime = LoopAlarmUtils.getPreviousAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(proTime)
    }

    @Test
    fun should_getCloseOnceNextAlarmTime_with_time_now_cycle_4_loopDay_1_next_time() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LegalHolidayUtil::class)
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        val loopAlarmList = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            loopAlarmList.add(alarm)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        every { spk.getLoopAlarms(mContext, 1) } returns loopAlarmList
        val nextTime = spk.getNextAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(nextTime)
    }

    @Test
    fun should_getCloseOnceNextAlarmTime_with_time_now_cycle_4_loopDay_1_next_time_loop_list_not_null() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LegalHolidayUtil::class)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            loopAlarmList = list
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        val nextTime = spk.getNextAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(nextTime)
    }

    @Test
    fun should_getCloseOnceNextAlarmTime_with_time_now_cycle_4_loopDay_1_next_time_loop_list_not_null_not_equt() {
        mContext = Mockito.spy(mContext)
        mockkStatic(LegalHolidayUtil::class)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            loopAlarmList = list
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        val nextTime = spk.getNextAlarmTime(alarm, Calendar.getInstance())
        Assert.assertNotNull(nextTime)
    }


    @Test
    fun should_addLoopAlarm_with_list_not_null() {
        mContext = Mockito.spy(mContext)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(AlarmUtils::class)
        val uri = Uri.parse("$ALARM_CONTENT_URI/2")
        every { AlarmUtils.alarmResolverInsert(mContext.contentResolver, ALARM_CONTENT_URI, any()) } returns uri
        val reset = LoopAlarmUtils.addLoopAlarm(mContext, alarm, true, list, true)
        Assert.assertTrue(reset)
    }

    @Test
    fun should_deleteLoopAlarm_with_list_not_null() {
        mContext = Mockito.spy(mContext)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        every { spk.getLoopAlarms(mContext, 1) } returns list
        LoopAlarmUtils.deleteLoopAlarm(mContext, alarm)
        Assert.assertNotNull(alarm)
    }

    @Test
    fun should_loopAlarmTypeString_with_alarm_not_null_and_next_time_not_null() {
        mContext = Mockito.spy(mContext)
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(LoopAlarmUtils::class)
        val time = 1701302400000L
        val result = LoopAlarmUtils.loopAlarmTypeString(mContext, alarm, time)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_deleteLoopAlarm_with_list_not_null_then_alarm_is_not_null() {
        mContext = Mockito.spy(mContext)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        every { spk.getLoopAlarms(mContext, 1) } returns list
        LoopAlarmUtils.deleteLoopAlarm(mContext, alarm)
        Assert.assertNotNull(alarm)
    }

    @Test
    fun should_contrastAlarmList_with_alarm_not_null_and_next_time_not_null() {
        mContext = Mockito.spy(mContext)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }

        val list2 = ArrayList<Alarm>()
        for (index in 1..6) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list2.add(alarm)
        }
        mockkStatic(LoopAlarmUtils::class)
        val result = LoopAlarmUtils.contrastAlarmList(list2, list)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_buildLoopAlarmBackupStr_with_alarm_not_null_and_next_time_not_null() {
        mContext = Mockito.spy(mContext)
        val reset = "2#3"
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }
        mockkStatic(LoopAlarmUtils::class)
        val spk = spyk<LoopAlarmUtils>()
        every { spk.getLoopAlarms(mContext, 1) } returns list
        val result = LoopAlarmUtils.buildLoopAlarmBackupStr(1, reset, mContext)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_parserLoopAlarmBackupStr_with_alarm_not_null_and_next_str_not_null() {
        mContext = Mockito.spy(mContext)
        val backStr = "8,30,1,1@8,30,1,2@8,30,1,3&1#"
        val alarm = Alarm().apply {
            id = 1
            setmLoopCycleDays(4)
            setmLoopDay(1)
            setmLoopWorkDays(3)
            holidaySwitch = 1
            setmWorkdayUpdateTime(Calendar.getInstance().timeInMillis)
        }
        val result = LoopAlarmUtils.parserLoopAlarmBackupStr(backStr, 2, alarm)
        Assert.assertTrue(result.isNotEmpty())
    }

    @Test
    fun should_checkLoopAlarmResetDays_check_alarm_list_is_not_one_close() {
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index == 1
            }
            list.add(alarm)
        }
        val result = LoopAlarmUtils.checkLoopAlarmResetDays(list, 1)
        Assert.assertTrue(result)
    }

    @Test
    fun should_contrastAlarmList_with_alarm_not_null_and_next_time_not_null_call_data_6() {
        mContext = Mockito.spy(mContext)
        val list = ArrayList<Alarm>()
        for (index in 1..4) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list.add(alarm)
        }

        val list2 = ArrayList<Alarm>()
        for (index in 1..6) {
            val alarm = Alarm().apply {
                setmLoopAlarmNumber(index)
                hour = 8
                minutes = 30
                isEnabled = index != 1
            }
            list2.add(alarm)
        }
        mockkStatic(LoopAlarmUtils::class)
        val result = LoopAlarmUtils.contrastAlarmList(list2, list)
        Assert.assertNotNull(result)
    }
}