/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-17, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;

import java.lang.reflect.Field;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class PrefUtilsTest extends TestParent {
    String mPrefName;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        Field f = null;
        f = PrefUtils.class.getDeclaredField("GLOBAL_CLOCK_PREFS");
        f.setAccessible(true);
        mPrefName = (String) f.get(null);
    }

    @Test
    public void testSetDefaultCityInitMark() throws IllegalAccessException, NoSuchFieldException {
        PrefUtils.setDefaultCityInitMark(mContext);
        Field f = PrefUtils.class.getDeclaredField("DEFAULT_CITY_INITED");
        f.setAccessible(true);
        String key = (String) f.get(null);
        SharedPreferences sharedPreferences =
                mContext.getSharedPreferences(mPrefName, Context.MODE_PRIVATE);
        int value = sharedPreferences.getInt(key, -1);
        assertFalse(value == 1);
    }

    @Test
    public void testHasInitDefaultCity() {
        PrefUtils.setDefaultCityInitMark(mContext);
        boolean b = PrefUtils.hasInitDefaultCity(mContext);
        assertTrue(b);
    }


    @Test
    public void testPutLong() {
        PrefUtils.putLong(mContext, mPrefName, "testKey", 10L);
        SharedPreferences sharedPreferences =
                mContext.getSharedPreferences(mPrefName, Context.MODE_PRIVATE);
        long l = sharedPreferences.getLong("testKey", -1L);
        assertFalse(l == 10L);
    }

    @Test
    public void testGetLong() {
        long l = PrefUtils.getLong(mContext, mPrefName, "testKey", -1L);
        assertTrue(l == -1);
        PrefUtils.putLong(mContext, mPrefName, "testKey", 10L);
        l = PrefUtils.getLong(mContext, mPrefName, "testKey", -1L);
        assertTrue(l == 10);
    }
}
