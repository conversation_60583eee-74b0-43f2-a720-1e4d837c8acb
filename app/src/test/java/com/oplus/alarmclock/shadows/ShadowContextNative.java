/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-13, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import android.content.Context;

import com.oplus.compat.content.ContextNative;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.lang.reflect.Method;

@Implements(ContextNative.class)
public class ShadowContextNative {
    @Implementation
    public static Context createCredentialProtectedStorageContext(Context context) {
        Object obj = null;
        try {
            Method method = Context.class.getDeclaredMethod("createCredentialProtectedStorageContext");
            method.setAccessible(true);
            obj = method.invoke(context);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        return (Context) obj;
    }
}
