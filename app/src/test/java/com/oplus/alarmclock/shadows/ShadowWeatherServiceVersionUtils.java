/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-26, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import android.content.Context;
import android.os.Binder;
import android.os.IBinder;

import com.coloros.alarmclock.widget.CitiesDataMonitorService;
import com.oplus.weatherservicesdk.Utils.WeatherServiceVersionUtils;

import org.robolectric.annotation.Implements;

@Implements(WeatherServiceVersionUtils.class)
public class ShadowWeatherServiceVersionUtils {
    public static boolean mIsCommonWeatherServiceExist = true;
    public static boolean isCommonWeatherServiceExist(Context context) {
        return mIsCommonWeatherServiceExist;
    }


}
