/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2021-9-7, hewei, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import com.oplus.compat.view.WindowManagerNative;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(WindowManagerNative.class)
public class ShadowGetInitialDisplayDensityUtil {
    @Implementation
    public static int getInitialDisplayDensity(int displayId) {
        return displayId;
    }
}
