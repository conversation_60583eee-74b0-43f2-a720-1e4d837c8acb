/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-14, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import android.os.UserHandle;

import com.oplus.alarmclock.shadows.annotation.Mapping;
import com.oplus.alarmclock.shadows.annotation.MappingConstructor;
import com.oplus.compat.os.UserHandleNative;

import java.lang.reflect.Method;

@Mapping(UserHandleNative.class)
public class ShadowUserHandleNative {
    @MappingConstructor
    public ShadowUserHandleNative(ClassInitializer ci) {
        try {
            Method method = UserHandle.class.getDeclaredMethod("myUserId");
            method.setAccessible(true);
            int userId = (int) method.invoke(null);
            Method method1 = UserHandle.class.getDeclaredMethod("of", int.class);
            method1.setAccessible(true);
            UserHandleNative.OWNER = (UserHandle) method1.invoke(null, userId);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

        UserHandleNative.USER_CURRENT = -2;
        UserHandleNative.USER_ALL = -1;
        UserHandleNative.CURRENT = UserHandleNative.OWNER;
        UserHandleNative.USER_SYSTEM = 0;
    }

}
