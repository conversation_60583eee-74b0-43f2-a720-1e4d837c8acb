/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-23, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import com.oplus.alarmclock.shadows.annotation.Mapping;
import com.oplus.alarmclock.shadows.annotation.MappingConstructor;
import com.oplus.alarmclock.shadows.annotation.MappingMethod;
import com.oplus.compat.app.ActivityManagerNative;


@Mapping(ActivityManagerNative.class)
public class ShadowActivityManagerNative {

    @MappingMethod(body = "return 0;")
    public static int getCurrentUser() {
        return 0;
    }

    @MappingConstructor
    public ShadowActivityManagerNative(ClassInitializer ci) {

    }
}
