/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-14, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.shadows;

import android.content.Context;

import com.oplus.alarmclock.utils.ProcessGuard;
import com.oplus.alarmclock.utils.ProcessGuard;
import com.oplus.alarmclock.utils.ProcessGuard;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(ProcessGuard.class)
public class ShadowProcessGuard {

    @Implementation
    public static void startProtect(Context context, String tag, long mills) {

    }
}
