/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-17, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock;

import android.content.Context;

import androidx.annotation.CallSuper;
import androidx.test.core.app.ApplicationProvider;

import com.oplus.alarmclock.shadows.ShadowProcessGuard;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

@Config(sdk = 28,shadows={ShadowProcessGuard.class})
@RunWith(RobolectricTestRunner.class)
public abstract class TestParent {
    protected  Context mContext;
    @Before
    public void setUp() throws Exception{
        MockitoAnnotations.initMocks(this);
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    @CallSuper
    public void tearDown() {
        mContext = null;
    }
}
