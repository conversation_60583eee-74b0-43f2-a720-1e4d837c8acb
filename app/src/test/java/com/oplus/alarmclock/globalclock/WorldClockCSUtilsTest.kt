/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorldClockCSUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/6
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaokang  2023/5/6    1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import androidx.fragment.app.FragmentActivity
import com.oplus.alarmclock.TestParent
import org.junit.Test
import org.robolectric.Robolectric

class WorldClockCSUtilsTest : TestParent() {
    @Test
    fun should_no_exception_when_use_buried_point_time_zone_change() {
        val activity = Robolectric.buildActivity(FragmentActivity::class.java).get()
        WorldClockCSUtils.buriedPointTimeZoneChange(activity)
    }
}