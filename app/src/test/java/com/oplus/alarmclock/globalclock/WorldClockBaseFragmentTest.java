/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main mFragmentActivity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-16, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.globalclock;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentHostCallback;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.AlarmSettingActivity;
import com.oplus.alarmclock.databinding.WorldClockViewLayoutBinding;
import com.oplus.alarmclock.utils.PrefUtils;
import com.coloros.alarmclock.widget.CitiesDataMonitorService;
import com.oplus.alarmclock.utils.ClockOplusCSUtils;
import com.oplus.alarmclock.utils.DoubleClickHelper;
import com.coloros.alarmclock.widget.WidgetUtils;
import com.oplus.statistics.OplusTrack;
import com.oplus.alarmclock.ReflectUtil;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import org.robolectric.shadows.ShadowActivity;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Ignore
public class WorldClockBaseFragmentTest extends TestParent {
    WorldClockBaseFragment mFragment;
    final String ACTION_UPDATE_CITIES_LIST = "com.oplus.alarmclock.widget.UPDATE_CITIES_LIST";
    final long MENU_CLICK_DURATION = DoubleClickHelper.MENU_CLICK_DURATION;
    int mCurrentCitiesCount;
    DoubleClickHelper mDoubleClickHelper = new DoubleClickHelper();

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mFragment = spy(new WorldClockBaseFragment<WorldClockViewLayoutBinding>() {
            @Override
            protected int layoutId() {
                return R.layout.world_clock_view_layout;
            }

            @Override
            public ViewGroup getBlurView() {
                return null;
            }

            @Override
            public void onFocused(boolean focused) {
            }

            @Override
            public void onPreChangeTab() {

            }

            @Override
            public int getCurrentCitiesCount() {
                return 0;
            }
        });
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mDoubleClickHelper", mFragment, mDoubleClickHelper);
    }

    @Config(shadows = {ShadowPrefUtils.class, ShadowWidgetUtils.class})
    @Test
    public void should_start_CitiesDataMonitorService_when_addDefaultCityIfNeeded_with_hasInitDefaultCity_is_false_and_hasAddWorldClockWidget_is_true() throws NoSuchMethodException, IllegalAccessException {
        FragmentActivity activity = Robolectric.buildActivity(FragmentActivity.class).get();
        doReturn(activity).when(mFragment).getActivity();
        //invoke addDefaultCityIfNeeded()
        ReflectUtil.invoke(WorldClockBaseFragment.class, "addDefaultCityIfNeeded", null, mFragment);
        ShadowActivity shadowActivity = Shadows.shadowOf(activity);
        Intent intent = shadowActivity.getNextStartedService();
        //assert
        assertEquals(CitiesDataMonitorService.class.getName(), intent.getComponent().getClassName());
        assertEquals(ACTION_UPDATE_CITIES_LIST, intent.getAction());
    }

    @Config(shadows = {ShadowOPlusStatistics.class})
    @Test
    @Ignore
    public void should_call_onEvent_with_STR_PRESS_GLOBAL_DELETE_MENU_when_onOptionsItemSelected_with_R_id_edit_and_canClick() throws NoSuchFieldException, IllegalAccessException {
        //ensure elapsed - MENU_CLICK_DURATION > mLastClickTime, so that canClick return true;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -MENU_CLICK_DURATION - 1);
        MenuItem menuItem = Mockito.mock(MenuItem.class);
        when(menuItem.getItemId()).thenReturn(R.id.edit);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(menuItem);
        //assert
        Assert.assertEquals(ClockOplusCSUtils.STR_PRESS_GLOBAL_DELETE_MENU, ShadowOPlusStatistics.sEventId);
        //release
        ShadowOPlusStatistics.sEventId = null;
    }

    @Config(shadows = {ShadowOPlusStatistics.class})
    @Test
    public void should_call_onEvent_with_STR_PRESS_GLOBAL_ADD_MENU_when_onOptionsItemSelected_with_R_id_add_and_count_smaller_than_MAX_CITY_NUM() throws NoSuchFieldException, IllegalAccessException {
        //ensure count < MAX_CITY_NUM
        mCurrentCitiesCount = WorldClockBaseFragment.MAX_CITY_NUM - 1;
        doReturn(mCurrentCitiesCount).when(mFragment).getCurrentCitiesCount();
        //ensure elapsed - MENU_CLICK_DURATION > mLastClickTime, so that canClick return true;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -MENU_CLICK_DURATION - 1);
        MenuItem menuItem = Mockito.mock(MenuItem.class);
        when(menuItem.getItemId()).thenReturn(R.id.add);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(menuItem);
        //assert
        assertEquals(ClockOplusCSUtils.STR_PRESS_GLOBAL_ADD_MENU, ShadowOPlusStatistics.sEventId);
        //release
        ShadowOPlusStatistics.sEventId = null;
    }

    @Config(shadows = {MyShadowToast.class})
    @Test
    public void should_call_onEvent_with_STR_PRESS_GLOBAL_ADD_MENU_when_onOptionsItemSelected_with_R_id_add_and_count_bigger_than_MAX_CITY_NUM()
            throws NoSuchFieldException, IllegalAccessException {
        //ensure count > MAX_CITY_NUM
        mCurrentCitiesCount = WorldClockBaseFragment.MAX_CITY_NUM + 1;
        doReturn(mCurrentCitiesCount).when(mFragment).getCurrentCitiesCount();
        doReturn(mContext.getString(R.string.add_world_clock_limit)).when(mFragment).getString(R.string.add_world_clock_limit);
        //ensure elapsed - MENU_CLICK_DURATION > mLastClickTime, so that canClick return true;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -MENU_CLICK_DURATION - 1);
        MenuItem menuItem = Mockito.mock(MenuItem.class);
        when(menuItem.getItemId()).thenReturn(R.id.add);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(menuItem);
        //assert
        verify(MyShadowToast.sToast).show();
        //release
        MyShadowToast.sToast = null;
    }

    @Test
    public void should_set_mCurrentLayoutType_to_LAYOUT_GRID_TYPE_with_STR_PRESS_GLOBAL_ADD_MENU_when_onOptionsItemSelected_with_R_id_layout_type_and_mCurrentLayoutType_is_LAYOUT_LIST_TYPE() throws NoSuchFieldException, IllegalAccessException {
        doReturn(Robolectric.buildActivity(FragmentActivity.class).get()).when(mFragment).getActivity();
        //ensure elapsed - MENU_CLICK_DURATION > mLastClickTime, so that canClick return true;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -MENU_CLICK_DURATION - 1);
        //set mCurrentLayoutType to LAYOUT_GRID_TYPE
        MenuItem menuItem = Mockito.mock(MenuItem.class);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(menuItem);
        //assert
    }

    @Config(shadows = {ShadowOPlusStatistics.class})
    @Test
    public void should_call_onEvent_with_SETTING_FROM_GLOBAL_CLOCK_when_onOptionsItemSelected_with_R_id_settings_and_canClick() throws NoSuchFieldException, IllegalAccessException {
        //ensure elapsed - MENU_CLICK_DURATION > mLastClickTime, so that canClick return true;
        ReflectUtil.setFieldValue(DoubleClickHelper.class, "mLastClickTime", mDoubleClickHelper, -MENU_CLICK_DURATION - 1);
        MenuItem menuItem = Mockito.mock(MenuItem.class);
        when(menuItem.getItemId()).thenReturn(R.id.settings);
        FragmentActivity activity = Robolectric.buildActivity(FragmentActivity.class).get();
        FragmentHostCallback host = new FragmentHostCallback(activity, mock(Handler.class), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }
        };
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        //invoke onOptionsItemSelected()
        mFragment.onOptionsItemSelected(menuItem);
        //assert
        assertEquals(ClockOplusCSUtils.SETTING_FROM_GLOBAL_CLOCK, ShadowOPlusStatistics.sEventId);
        ShadowActivity shadowActivity = Shadows.shadowOf(activity);
        Intent intent = shadowActivity.getNextStartedActivity();
        String expectedStartedActivityName = intent.getComponent().getClassName();
        assertEquals(expectedStartedActivityName, AlarmSettingActivity.class.getName());
        //release
        ShadowOPlusStatistics.sEventId = null;
    }

    @Ignore
    @Config(shadows = {ShadowPrefUtils.class})
    @Test
    public void should_call_mAddItem_setIcon_with_ic_add_and_call_mLayoutItem_setEnabled_with_true_when_updateMenu_with_count_smaller_than_MAX_CITY_NUM_and_not_equalsTo_zero()
            throws NoSuchFieldException, IllegalAccessException {
        //ensure count smaller than MAX_CITY_NUM and not equalsTo zero
        mCurrentCitiesCount = WorldClockBaseFragment.MAX_CITY_NUM - 1;
        doReturn(mCurrentCitiesCount).when(mFragment).getCurrentCitiesCount();
        FragmentActivity activity = Robolectric.buildActivity(FragmentActivity.class).get();
        FragmentHostCallback host = new FragmentHostCallback(activity, mock(Handler.class), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }
        };
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        //init Menu
        MenuItem addItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mAddItem", mFragment, addItem);
        MenuItem layoutItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mLayoutItem", mFragment, layoutItem);
        MenuItem editItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mEditItem", mFragment, editItem);
        //invoke updateMenu()
        mFragment.updateMenu();
        //verify
        verify(addItem).setIcon(R.drawable.oplus_color_menu_ic_add);
        verify(layoutItem).setVisible(true);
    }


    @Ignore
    @Config(shadows = {ShadowPrefUtils.class})
    @Test
    public void should_call_mAddItem_setIcon_with_ic_add_disabled_and_call_mLayoutItem_setEnabled_with_true_when_updateMenu_with_count_bigger_than_MAX_CITY_NUM_and_mCurrentLayoutType_equalsTo_LAYOUT_GRID_TYPE()
            throws NoSuchFieldException, IllegalAccessException {
        //ensure count smaller than MAX_CITY_NUM and not equalsTo zero
        mCurrentCitiesCount = WorldClockBaseFragment.MAX_CITY_NUM + 1;
        doReturn(mCurrentCitiesCount).when(mFragment).getCurrentCitiesCount();
        FragmentActivity activity = Robolectric.buildActivity(FragmentActivity.class).get();
        FragmentHostCallback host = new FragmentHostCallback(activity, mock(Handler.class), 0) {
            @Nullable
            @Override
            public Object onGetHost() {
                return null;
            }
        };
        ReflectUtil.setFieldValue(Fragment.class, "mHost", mFragment, host);
        //init Menu
        MenuItem addItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mAddItem", mFragment, addItem);
        MenuItem layoutItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mLayoutItem", mFragment, layoutItem);
        MenuItem editItem = mock(MenuItem.class);
        ReflectUtil.setFieldValue(WorldClockBaseFragment.class, "mEditItem", mFragment, editItem);
        //invoke updateMenu()
        mFragment.updateMenu();
        //verify
        verify(addItem).setIcon(R.drawable.color_menu_ic_add_disabled);
    }

    @Implements(PrefUtils.class)
    public static class ShadowPrefUtils {
        static int sCityLayoutType;

        @Implementation
        public static boolean hasInitDefaultCity(Context context) {
            return false;
        }

        public static int getCityLayoutType(Context context) {
            return sCityLayoutType;
        }
    }

    @Implements(WidgetUtils.class)
    public static class ShadowWidgetUtils {
        @Implementation
        public static boolean hasAlreadyAddedWorldClockWidget(Context context) {
            return true;
        }
    }

    @Implements(OplusTrack.class)
    public static class ShadowOPlusStatistics {
        static String sEventId;

        public static void onEvent(final Context context, final String eventID) {
            sEventId = eventID;
        }
    }

    @Implements(Toast.class)
    public static class MyShadowToast {
        static Toast sToast = Mockito.mock(Toast.class);

        @Implementation
        public static Toast makeText(Context context, CharSequence text, int duration) {
            return sToast;
        }
    }


}
