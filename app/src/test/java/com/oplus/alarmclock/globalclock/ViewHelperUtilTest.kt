/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ViewHelperUtilTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/6
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaokang  2023/5/6    1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.view.View
import androidx.fragment.app.FragmentActivity
import com.oplus.alarmclock.TestParent
import org.junit.Assert.assertEquals
import org.junit.Test
import org.robolectric.Robolectric

class ViewHelperUtilTest : TestParent() {

    @Test
    fun should_no_exception_when_use_clear() {
        val activity = Robolectric.buildActivity(FragmentActivity::class.java).get()
        val view = View(activity)
        view.run {
            ViewHelperUtil.clear(this)
            assertEquals(alpha, 1F)
            assertEquals(scaleX, 1F)
            assertEquals(scaleY, 1F)
            assertEquals(translationX, 0F)
            assertEquals(translationY, 0F)
            assertEquals(rotation, 0F)
            assertEquals(rotation, 0F)
            assertEquals(rotationX, 0F)
            assertEquals(rotationY, 0F)
            assertEquals(pivotX, 0F)
            assertEquals(pivotY, 0F)
            val animator = animate()
            assert(animator.interpolator == null)
            assertEquals(animator.startDelay, 0)
        }
    }
}