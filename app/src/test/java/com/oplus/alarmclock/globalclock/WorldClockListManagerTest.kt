/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorldClockListManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.view.View
import android.widget.RelativeLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.view.dial.AlarmDialClockMsgTextView
import com.oplus.alarmclock.view.dial.AlarmDialClockTextView
import com.oplus.alarmclock.view.dial.WorldClockAnimationManager
import org.junit.Assert
import org.junit.Test

class WorldClockListManagerTest : TestParent() {
    private lateinit var mManager: WorldClockListManager

    override fun setUp() {
        super.setUp()
        mManager = WorldClockListManager(mContext)
    }

    @Test
    fun should_return_true_when_is_init() {
        val recyclerView = init()
        ReflectUtil.invoke(WorldClockListManager::class.java, "onListScroll", null, mManager)
        ReflectUtil.invoke(WorldClockListManager::class.java, "getMsgDistance", null, mManager)
        ReflectUtil.invoke(
            WorldClockListManager::class.java,
            "getRatio",
            arrayOf<Any>(0),
            mManager,
            Int::class.java
        )
        ReflectUtil.invoke(
            WorldClockListManager::class.java,
            "updateView",
            arrayOf<Any>(0F, 0),
            mManager,
            Float::class.java, Int::class.java
        )
        mManager.mOnScrollListener.onScrollStateChanged(
            recyclerView,
            RecyclerView.SCROLL_STATE_IDLE
        )
        val mViewTextSize =
            ReflectUtil.getFieldValue(
                WorldClockListManager::class.java,
                "mViewTextSize",
                mManager
            ) as Float
        val isInit = mViewTextSize >= 0
        Assert.assertTrue(isInit)
    }

    private fun init(): RecyclerView {
        val manager = WorldClockAnimationManager()
        val recyclerView = RecyclerView(mContext)
        val divider = View(mContext)
        val dialClockRl = RelativeLayout(mContext)
        val dialMsgTv = AlarmDialClockMsgTextView(mContext)
        val dialWordTimeTv = AlarmDialClockTextView(mContext).apply {
            layoutParams = ConstraintLayout.LayoutParams(0, 0)
        }
        val dialWordMsgTv = AlarmDialClockMsgTextView(mContext)
        val clickView = View(mContext)
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mAlarmDialClockManager",
            mManager,
            manager
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mRecyclerView",
            mManager,
            recyclerView
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mDialClock",
            mManager,
            dialClockRl
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mDialClockMsgTv",
            mManager,
            dialMsgTv
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mDialClockTv",
            mManager,
            dialWordTimeTv
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mDialClockTvMsgTv",
            mManager,
            dialWordMsgTv
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mClickView",
            mManager,
            clickView
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mDivider",
            mManager,
            divider
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mAlphaBottom",
            mManager,
            1F
        )
        ReflectUtil.setFieldValue(
            WorldClockListManager::class.java,
            "mAlphaTop",
            mManager,
            1F
        )
        return recyclerView
    }
}