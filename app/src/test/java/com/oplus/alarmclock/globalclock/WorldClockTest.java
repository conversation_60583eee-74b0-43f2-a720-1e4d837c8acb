package com.oplus.alarmclock.globalclock;

import android.content.Context;

import com.coloros.refusedesktop.Constants;
import com.coloros.refusedesktop.model.DialClockModel;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.mvvm.worldclock.WorldClockViewModel;
import com.oplus.alarmclock.view.dial.AlarmDialClockTextView;
import com.coloros.refusedesktop.viewmodel.DialClockViewModel;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;

import static org.junit.Assert.assertTrue;

/**
 * Created by W9013986 on 2022/2/23
 */
public class WorldClockTest extends TestParent {

    @Test
    public void should_return_true_when_zone_is_china_with_zone() throws NoSuchMethodException, IllegalAccessException {
        TimeZone timeZone = TimeZone.getTimeZone("Asia/Shanghai");
        Calendar calendar = Calendar.getInstance(timeZone);
        Object[] args = {calendar};
        WorldClockViewModel model = new WorldClockViewModel();
        String zoneName = (String) ReflectUtil.invoke(WorldClockViewModel.class, "getZoneName", args, model, Calendar.class);
        String matchStr = "China";
        boolean contains = zoneName.contains(matchStr);
        assertTrue(contains);
    }

    @Test
    public void should_return_true_when_text_with_text() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        AlarmDialClockTextView tv = new AlarmDialClockTextView(mContext);
        Object[] args = {mContext};
        ReflectUtil.invoke(AlarmDialClockTextView.class, "initTextLength", args, tv, Context.class);
        float mTextLength = (float) ReflectUtil.getFieldValue(AlarmDialClockTextView.class, "mTextLength", tv);
        float mTextSize = (float) ReflectUtil.getFieldValue(AlarmDialClockTextView.class, "mTextSize", tv);
        boolean haveTextLength = mTextLength > 0;
        boolean haveTextSize = mTextSize > 0;
        assertTrue(haveTextLength);
        assertTrue(haveTextSize);
    }

    @Test
    @Ignore
    public void should_return_true_when_time_less_than_with_time() throws NoSuchMethodException, IllegalAccessException {
        DialClockViewModel mViewModel = DialClockViewModel.INSTANCE;
        long correctingTime = (long) ReflectUtil.invoke(DialClockViewModel.class, "correctingTime", new Object[]{true}, mViewModel);
        boolean time = correctingTime < 1000;
        assertTrue(time);
    }

    @Test
    public void should_split_right_when_splitClockKey_with_different_key() {
        String key = Constants.EXTRA_DIAL_CLOCK_CITY_ID;
        String wholeKey = "dial_clock_city_id_69_1_0";
        String widgetCode = DialClockViewModel.INSTANCE.splitClockKey(key, wholeKey);
        Assert.assertEquals("69&1&0", widgetCode);
    }

    @Config(shadows = ShadowCityUtils.class)
    @Test
    public void should_save_cityId_to_sp_when_saveDataFromBackUpRestore_with_cityId_and_widgetCode() {
        int cityId = 1;
        String widgetCode = "69&1&0";
        City city = new City();
        city.setCityId(cityId);
        city.setTimezone("GMT+8");
        city.setName("beijing");
        ShadowCityUtils.sCity = city;
        DialClockViewModel.INSTANCE.saveDataFromBackUpRestore(mContext, String.valueOf(cityId), widgetCode);
        DialClockModel model = DialClockViewModel.INSTANCE.getDialClockData(mContext, 69, 1, 0);
        Assert.assertNotNull(model);
        Assert.assertEquals(cityId, Integer.parseInt(model.getMCityId()));
    }

    @Implements(CityUtils.class)
    private static class ShadowCityUtils{

        static City sCity;

        @Implementation
        public static City getCityByCityId(Context context, String cityId, Locale locale) {
            return sCity;
        }
    }
}
