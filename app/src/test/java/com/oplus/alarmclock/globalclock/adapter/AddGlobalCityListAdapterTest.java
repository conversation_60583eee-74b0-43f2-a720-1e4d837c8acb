/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AddGlobalCityListAdapterTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2019/11/13     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock.adapter;

import android.database.Cursor;
import android.text.BidiFormatter;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.COUIRecyclerView;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.TimeZone;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

public class AddGlobalCityListAdapterTest extends TestParent {

    private SimpleDateFormat mGmtFormatter;
    private BidiFormatter mBidiFormatter;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        mGmtFormatter = new SimpleDateFormat("ZZZZ", Locale.getDefault());
        mBidiFormatter = BidiFormatter.getInstance();
    }

    @Test
    public void should_return_expectedGmsString_when_getTimeZoneText_with_string_in_timeZoneString() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {

        String[] timeZoneStr = new String[] {"GMT-6","GMT-5","GMT-4","GMT-3","GMT-2",
                "GMT-1", "GMT-0", "GMT+1", "GMT+2", "GMT+3", "GMT+4", "GMT+5", "GMT+6"};
        String[] expectedResult = new String[] {"-0600", "-0500", "-0400", "-0300", "-0200", "-0100",
                "+0000", "+0100", "+0200", "+0300", "+0400", "+0500", "+0600"};

        AddGlobalCityListAdapter addGlobalCityListAdapter = mock(AddGlobalCityListAdapter.class);
        ReflectUtil.setFieldValue(AddGlobalCityListAdapter.class, "mGmtFormatter",
                addGlobalCityListAdapter, mGmtFormatter);
        ReflectUtil.setFieldValue(AddGlobalCityListAdapter.class, "mBidiFormatter",
                addGlobalCityListAdapter, mBidiFormatter);

        for (int i = 0; i < timeZoneStr.length; i++) {
            TimeZone timeZone = TimeZone.getTimeZone(timeZoneStr[i]);
            String gmsString = (String) ReflectUtil.invoke(AddGlobalCityListAdapter.class,
                    "getTimeZoneText", new Object[]{timeZone}, addGlobalCityListAdapter, TimeZone.class);
            assertEquals(expectedResult[i], gmsString);
        }
    }

    @Ignore
    @Test
    public void should_call_setText_with_text_call_setOnClickListener_with_text_when_onBindViewHolder_with_position_bigger_than_zero_and_cursor_isNot_null() throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException, NoSuchFieldException {

        Cursor cursor = mock(Cursor.class);
        AddGlobalCityListAdapter adapter = new AddGlobalCityListAdapter(null, cursor);
        final int cityNameIndex = 2;
        final int timezoneIDIndex = 5;
        final int countryIndex = 7;
        final int position = 1;
        final String timezoneID = "GMT+8";
        final String exceptGmtString = "+0800";

        // 3 types, input value and except value pair
        HashMap<String, String> cityNamePair = new HashMap<>();
        cityNamePair.put("成都", "成都(中国)");

        HashMap<String, String> countryNamePair = new HashMap<>();
        countryNamePair.put("成都", "中国");


        for (String key: cityNamePair.keySet()) {

            when(cursor.moveToPosition(position - 1)).thenReturn(true);
            when(cursor.getColumnIndex(ClockContract.City.CITY_NAME)).thenReturn(cityNameIndex);
            when(cursor.getColumnIndex(ClockContract.City.TIMEZONE_ID)).thenReturn(timezoneIDIndex);
            when(cursor.getColumnIndex(ClockContract.City.CITY_COUNTRY)).thenReturn(countryIndex);

            when(cursor.getString(cityNameIndex)).thenReturn(key);
            when(cursor.getString(timezoneIDIndex)).thenReturn(timezoneID);
            when(cursor.getString(countryIndex)).thenReturn(countryNamePair.get(key));

            TextView gmt = mock(TextView.class);
            TextView cityName = mock(TextView.class);

            View itemView = mock(View.class);
            when(itemView.findViewById(R.id.text_city_name)).thenReturn(cityName);
            when(itemView.findViewById(R.id.gmt)).thenReturn(gmt);

            Class ItemViewHolderClazz = Class.forName("com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter$ItemViewHolder");
            Constructor constructor = ItemViewHolderClazz.getDeclaredConstructor(AddGlobalCityListAdapter.class, View.class);
            constructor.setAccessible(true);
            COUIRecyclerView.ViewHolder viewHolder = (COUIRecyclerView.ViewHolder) constructor.newInstance(adapter, itemView);
            COUIRecyclerView.ViewHolder spyViewHolder = spy(viewHolder);

            ReflectUtil.setFieldValue(AddGlobalCityListAdapter.class, "mCursor", adapter, cursor);
            //invoke onBindViewHolder()
            adapter.onBindViewHolder(spyViewHolder, position);
            //assert
            verify(gmt).setText(exceptGmtString);
            verify(cityName).setText(cityNamePair.get(key));
            verify(itemView).setOnClickListener(any(View.OnClickListener.class));
        }
    }

}