/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AddCityManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/5
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2024/3/5     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.globalclock

import android.app.Activity
import android.content.Intent
import android.content.Loader
import android.database.Cursor
import android.os.Bundle
import android.view.Menu
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.ActionBar
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.searchview.COUISearchView
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.appcompat.touchsearchview.COUITouchSearchView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.behavior.HeadScaleWithSearchBhv
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter
import com.oplus.alarmclock.globalclock.view.TopMarginView
import com.oplus.alarmclock.provider.ClockContract
import com.oplus.alarmclock.utils.ClockOplusCSUtils
import com.oplus.alarmclock.utils.DoubleClickHelper
import com.oplus.alarmclock.utils.ToastManager
import com.oplus.anim.EffectiveAnimationView
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

class AddCityManagerTest : TestParent() {

    override fun setUp() {
        super.setUp()
    }

    override fun tearDown() {
        super.tearDown()
    }

    @Test
    @Ignore
    fun should_initData_with_show_panel() {
        val viewHolder = mockk<AddCityViewHolder>().apply {
            justRun { mStatusBarHeight = any() }
            justRun { initViewBg(any()) }
            justRun { initAppBarLayout(any()) }
            justRun { mToolBar = any() }
            every { mToolBar } returns null
            justRun { initSearchView(any(), any()) }
            justRun { initListView(any()) }
            justRun { initSearchBar() }
        }
        val mockkIntent = mockk<Intent>().apply {
            every { getIntExtra("current_count", 0) } returns 0
            every { getBooleanExtra("isFromSetting", false) } returns true
            every { action } returns "com.oplus.alarmclock.ADD_WORLD_CLOCK"
            every { getBooleanExtra("start_activity_from_screen", false) } returns true
            every { getBooleanExtra("isFromDialClock", false) } returns false
            every { getBooleanExtra("is_show_panel", false) } returns true
        }
        val activity = mockk<Activity>().apply {
            every { intent } returns mockkIntent
            every { isFinishing } returns false
        }
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            justRun { handleQuery(any(), any()) }
        }
        val addCityManager = AddCityManager(activity, dialog, viewHolder)
        addCityManager.initData()

        Assert.assertTrue(addCityManager.mIsShowPanel)
    }

    @Test
    @Ignore
    fun should_initData_with_not_show_panel() {
        val viewHolder = mockk<AddCityViewHolder>().apply {
            justRun { mStatusBarHeight = any() }
            justRun { initViewBg(any()) }
            justRun { initAppBarLayout(any()) }
            justRun { mToolBar = any() }
            every { mToolBar } returns null
            justRun { initSearchView(any(), any()) }
            justRun { initListView(any()) }
            justRun { initSearchBar() }
        }
        val mockkIntent = mockk<Intent>().apply {
            every { getIntExtra("current_count", 0) } returns 0
            every { getBooleanExtra("isFromSetting", false) } returns false
            every { action } returns "com.oplus.alarmclock.ADD_WORLD_CLOCK"
            every { getBooleanExtra("start_activity_from_screen", false) } returns false
            every { getBooleanExtra("isFromDialClock", false) } returns false
            every { getBooleanExtra("is_show_panel", false) } returns false
        }
        val activity = mockk<Activity>().apply {
            every { intent } returns mockkIntent
            every { isFinishing } returns false
        }
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            justRun { handleQuery(any(), any()) }
        }
        val addCityManager = AddCityManager(activity, dialog, viewHolder)
        addCityManager.initData()

        Assert.assertFalse(addCityManager.mIsShowPanel)
    }

    @Test
    fun should_initToolBar_with_is_from_setting() {
        val mockkToolbar = mockk<COUIToolbar>().apply {
            justRun { setTitle(any() as Int) }
        }
        val actionBar = mockk<ActionBar>().apply {
            justRun { setDisplayHomeAsUpEnabled(true) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mToolBar } returns mockkToolbar
        }
        val activity = mockk<AppCompatActivity>().apply {
            justRun { setSupportActionBar(mockkToolbar) }
            every { supportActionBar } returns actionBar
        }
        val fragment = mockk<AddCityFragment>().apply {
            justRun { setHasOptionsMenu(true) }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mActivity } returns activity
            every { mFragment } returns fragment
            every { mViewHolder } returns viewHolder
            every { mIsFromSetting } returns true
            every { initToolBar() } answers { callOriginal() }
        }
        addCityManager.initToolBar()
    }

    @Test
    @Ignore
    fun should_initToolBar_with_is_not_from_setting() {
        val mockkMenu = mockk<Menu>().apply {
            every { findItem(any()) } returns null
        }
        val mockkToolbar = mockk<COUIToolbar>().apply {
            justRun { setTitle(any() as Int) }
            justRun { inflateMenu(any()) }
            justRun { isTitleCenterStyle = true }
            justRun { setTitleTextSize(any()) }
            every { menu } returns mockkMenu
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mToolBar } returns mockkToolbar
        }
        val mockkIntent = mockk<Intent>().apply {
            every { getBooleanExtra("is_from_word_clock_or_widget", false) } returns true
        }
        val activity = mockk<AppCompatActivity>().apply {
            every { intent } returns mockkIntent
        }
        val fragment = mockk<AddCityFragment>()
        val addCityManager = mockk<AddCityManager>().apply {
            every { mActivity } returns activity
            every { mFragment } returns fragment
            every { mViewHolder } returns viewHolder
            every { mIsFromSetting } returns false
            every { initToolBar() } answers { callOriginal() }
        }
        addCityManager.initToolBar()
    }

    @Test
    fun should_doQuery_with_empty_text() {
        val searchBar = mockk<EditText>().apply {
            justRun { setText(any<String>(), any<TextView.BufferType>()) }
        }
        val emptyView = mockk<EffectiveAnimationView>()
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mSearchBar } returns searchBar
            every { mViewEmpty } returns emptyView
        }
        val fragment = mockk<AddCityFragment>().apply {
            justRun { resetEmptyAnimToBegin(any()) }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mFragment } returns fragment
            every { mViewHolder } returns viewHolder
            justRun { handleQuery() }
            every { mQueryString } answers { callOriginal() }
            every { mQueryString = any() } answers { callOriginal() }
            every { doQuery(any()) } answers { callOriginal() }
        }
        addCityManager.doQuery("")
        Assert.assertEquals(addCityManager.mQueryString, "")
    }

    @Test
    fun should_doQuery_with_text_length_over_100() {
        val searchBar = mockk<EditText>().apply {
            justRun { setText(any<String>(), any<TextView.BufferType>()) }
        }
        val emptyView = mockk<EffectiveAnimationView>()
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mSearchBar } returns searchBar
            every { mViewEmpty } returns emptyView
        }
        val fragment = mockk<AddCityFragment>().apply {
            justRun { resetEmptyAnimToBegin(any()) }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mFragment } returns fragment
            every { mViewHolder } returns viewHolder
            justRun { handleQuery() }
            justRun { showToast(any(), any()) }
            every { mQueryString } answers { callOriginal() }
            every { mQueryString = any() } answers { callOriginal() }
            every { doQuery(any()) } answers { callOriginal() }
        }
        val testText = "01234567890123456789012345678901234567890123456789" +
                "01234567890123456789012345678901234567890123456789test"
        addCityManager.doQuery(testText)
        Assert.assertEquals(addCityManager.mQueryString,
            "0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789")
    }

    @Test
    fun should_doQuery_with_text_length_less_100() {
        val searchBar = mockk<EditText>().apply {
            justRun { setText(any<String>(), any<TextView.BufferType>()) }
        }
        val emptyView = mockk<EffectiveAnimationView>()
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mSearchBar } returns searchBar
            every { mViewEmpty } returns emptyView
        }
        val fragment = mockk<AddCityFragment>().apply {
            justRun { resetEmptyAnimToBegin(any()) }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mFragment } returns fragment
            every { mViewHolder } returns viewHolder
            justRun { handleQuery() }
            every { mQueryString } answers { callOriginal() }
            every { mQueryString = any() } answers { callOriginal() }
            every { doQuery(any()) } answers { callOriginal() }
        }
        val testText = "01test"
        addCityManager.doQuery(testText)
        Assert.assertEquals(addCityManager.mQueryString,
            "01test")
    }

    @Test
    fun should_selectCity_with_position_0() {
        mockkStatic(CityUtils::class)
        val cursor = mockk<Cursor>().apply {
            every { moveToPosition(0) } returns true
            every { getLong(any()) } returns 1
            every { getColumnIndex(ClockContract.City.CITY_ID) } returns 0
        }
        justRun { CityUtils.asyncSetCityUserAdd(any(), any()) }
        val addCityManager = mockk<AddCityManager>().apply {
            every { selectCity(0, cursor) } answers { callOriginal() }
        }
        addCityManager.selectCity(0, cursor)
        unmockkStatic(CityUtils::class)
    }

    @Test
    fun should_onStateChange_with_state_edit_and_anim_inited() {
        val searchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
            justRun { visibility = View.GONE }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns searchView
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder).apply {
            mIsInitAnimate = true
        }
        addCityManager.onStateChange(0, 1)
        verify {
            searchView.visibility = View.GONE
        }
    }

    @Test
    fun should_onStateChange_with_state_normal_and_anim_inited() {
        val searchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
            justRun { visibility = View.VISIBLE }
        }
        val behavior = mockk<HeadScaleWithSearchBhv>().apply {
            justRun { setScaleEnable(false) }
        }
        val mockkLayoutManager = mockk<FastScrollLinearLayoutManager>().apply {
            every { itemCount } returns 1
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            every { layoutManager } returns mockkLayoutManager
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns searchView
            every { mBehavior } returns behavior
            every { mCityList } returns cityListView
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder).apply {
            mIsInitAnimate = true
        }
        addCityManager.onStateChange(0, 0)
        verify {
            searchView.visibility = View.VISIBLE
            behavior.setScaleEnable(false)
        }
    }

    @Test
    fun should_onStateChange_with_state_edit_and_anim_not_init() {
        mockkStatic(TopMarginView::class)
        val searchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
            justRun { visibility = View.GONE }
        }
        val topMarginView = mockk<TopMarginView>()
        val globalSearchView = mockk<COUISearchBar>().apply {
            every { height } returns 1200
        }
        val headerView = mockk<View>().apply {
            every { height } returns 40
        }
        val dividerLine = mockk<View>()
        every { TopMarginView.getViewTopMargin(globalSearchView) } returns 12

        val toolbar = mockk<COUIToolbar>().apply {
            every { height } returns 800
        }

        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns searchView
            every { mTopMarginView } returns null
            justRun { mTopMarginView = any() }
            every { mGlobalSearchView } returns globalSearchView
            every { mToolBar } returns toolbar

            every { mSearchHeightView } returns null
            justRun { mSearchHeightView = any() }

            every { mHeaderHeightView } returns null
            justRun { mHeaderHeightView = any() }

            every { mHeaderView } returns headerView
            every { mStatusBarHeight } returns 0
            every { mDividerLine } returns dividerLine
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder).apply {
            mIsInitAnimate = false
        }
        addCityManager.onStateChange(0, 1)
        verify {
            searchView.visibility = View.GONE
        }
        Assert.assertTrue(addCityManager.mIsInitAnimate)
        unmockkStatic(TopMarginView::class)
    }

    @Suppress("LongMethod")
    @Test
    fun should_onStateChange_with_normal_and_anim_uninit() {
        mockkStatic(TopMarginView::class)
        val searchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
            justRun { visibility = View.VISIBLE }
        }
        val appBarLayout = mockk<AppBarLayout>()
        val topMarginView = mockk<TopMarginView>()
        val globalSearchView = mockk<COUISearchBar>().apply {
            every { height } returns 1200
        }
        val headerView = mockk<View>().apply {
            every { height } returns 40
        }
        val dividerLine = mockk<View>().apply {
            justRun { visibility = any() }
        }
        every { TopMarginView.getViewTopMargin(globalSearchView) } returns 12

        val toolbar = mockk<COUIToolbar>().apply {
            every { height } returns 800
        }
        val behavior = mockk<HeadScaleWithSearchBhv>().apply {
            justRun { setScaleEnable(false) }
            every { isFirstIn } returns true
        }
        val childView = mockk<View>()
        val mockkLayoutManager = mockk<FastScrollLinearLayoutManager>().apply {
            every { itemCount } returns 2
            every { getChildAt(1) } returns childView
            justRun { scrollToPositionWithOffset(any(), any()) }
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            every { layoutManager } returns mockkLayoutManager
            every { startNestedScroll(ViewCompat.SCROLL_AXIS_VERTICAL) } returns true
        }

        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns searchView
            every { mTopMarginView } returns null
            justRun { mTopMarginView = any() }
            every { mGlobalSearchView } returns globalSearchView
            every { mToolBar } returns toolbar

            every { mSearchHeightView } returns null
            justRun { mSearchHeightView = any() }

            every { mHeaderHeightView } returns null
            justRun { mHeaderHeightView = any() }

            every { mHeaderView } returns headerView
            every { mStatusBarHeight } returns 0
            every { mDividerLine } returns dividerLine

            every { mBehavior } returns behavior
            every { mCityList } returns cityListView
            every { mColorAppBarLayout } returns appBarLayout
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder).apply {
            mIsInitAnimate = false
            mIntent = Intent()
        }
        addCityManager.onStateChange(0, 0)
        verify {
            searchView.visibility = View.VISIBLE
        }
        Assert.assertTrue(addCityManager.mIsInitAnimate)
        unmockkStatic(TopMarginView::class)
    }

    @Test
    fun should_onItemClock_with_can_not_click() {
        val doubleClickHelper = mockk<DoubleClickHelper>().apply {
            every { canClick() } returns false
        }
        val cursor = mockk<Cursor>()
        val addCityManager = mockk<AddCityManager>().apply {
            every { mDoubleClickHelper } returns doubleClickHelper
            every { onItemClock(any(), any()) } answers { callOriginal() }
        }
        addCityManager.onItemClock(0, cursor)
    }

    @Test
    @Ignore
    fun should_onItemClock_with_not_from_setting_and_dial_clock() {
        val doubleClickHelper = mockk<DoubleClickHelper>().apply {
            every { canClick() } returns true
        }
        val cursor = mockk<Cursor>()
        val activity = mockk<FragmentActivity>()
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            justRun { dismiss() }
        }
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
            justRun { finish() }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mDoubleClickHelper } returns doubleClickHelper
            every { mIsFromSetting } returns false
            every { mIsFromDialClock } returns false
            every { mFragment } returns fragment
            every { mDialog } returns dialog
            every { cityIsSelected(any(), any()) } returns true
            justRun { showToast(any(), any()) }
            every { onItemClock(any(), any()) } answers { callOriginal() }
        }

        addCityManager.onItemClock(0, cursor)
        verify {
            addCityManager.showToast(any(), any())
            dialog.dismiss()
        }
    }

    @Test
    @Ignore
    fun should_onItemClock_with_unselected_and_not_from_setting_and_dial_clock() {
        val doubleClickHelper = mockk<DoubleClickHelper>().apply {
            every { canClick() } returns true
        }
        val cursor = mockk<Cursor>()
        val activity = mockk<FragmentActivity>()
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            justRun { dismiss() }
        }
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
            justRun { finish() }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mDoubleClickHelper } returns doubleClickHelper
            every { mIsFromSetting } returns false
            every { mIsFromDialClock } returns false
            every { mFragment } returns fragment
            every { mDialog } returns dialog
            every { cityIsSelected(any(), any()) } returns false
            justRun { selectCity(any(), any()) }
            every { onItemClock(any(), any()) } answers { callOriginal() }
        }

        addCityManager.onItemClock(0, cursor)
        verify {
            addCityManager.selectCity(any(), any())
            dialog.dismiss()
        }
    }

    @Test
    @Ignore
    fun should_onItemClock_with_from_setting_and_dial_clock() {
        val doubleClickHelper = mockk<DoubleClickHelper>().apply {
            every { canClick() } returns true
        }
        val cursor = mockk<Cursor>()
        val activity = mockk<FragmentActivity>()
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            justRun { dismiss() }
        }
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
            justRun { finish() }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mDoubleClickHelper } returns doubleClickHelper
            every { mIsFromSetting } returns true
            every { mIsFromDialClock } returns true
            every { mFragment } returns fragment
            every { mDialog } returns dialog
            justRun { saveResultForSetting(any(), any()) }
            every { onItemClock(any(), any()) } answers { callOriginal() }
        }

        addCityManager.onItemClock(0, cursor)
        verify {
            addCityManager.saveResultForSetting(any(), any())
            dialog.dismiss()
        }
    }

    @Test
    fun should_onCreateLoader_with_bundle_null() {
        val addCityManager = mockk<AddCityManager>().apply {
            every { onCreateLoader(0, null) } answers { callOriginal() }
        }
        val result = addCityManager.onCreateLoader(0, null)
        Assert.assertNull(result)
    }

    @Test
    fun should_onCreateLoader_with_bundle_not_null() {
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { clearFlags() }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder)
        val bundle = Bundle().apply {
            putString("QUERY_TEXT", "test")
        }
        val result = addCityManager.onCreateLoader(0, bundle)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_onCreateLoader_with_bundle_from_setting() {
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { clearFlags() }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder).apply {
            mIsFromSetting = true
        }
        val bundle = Bundle().apply {
            putString("QUERY_TEXT", "test")
        }
        val result = addCityManager.onCreateLoader(0, bundle)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_onLoadFinished_with_query_string_empty() {
        mockkStatic(CityUtils::class)
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { clearFlags() }
            justRun { swapCursor(any()) }
        }
        val missMatch = mockk<LinearLayout>().apply {
            justRun { visibility = View.GONE }
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            justRun { visibility = View.VISIBLE }
            justRun { scrollToPosition(0) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
            every { mMissMatch } returns missMatch
            every { mCityList } returns cityListView
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        every { CityUtils.isJapanese() } returns false

        val addCityManager = mockk<AddCityManager>().apply {
            justRun { addStatistics(any()) }
            every { mViewHolder } returns viewHolder
            every { mQueryString } returns null
            every { onLoadFinished(any(), any()) } answers { callOriginal() }
        }
        val cursor = mockk<Cursor>()
        val loader = mockk<Loader<Cursor?>>()
        addCityManager.onLoadFinished(loader, cursor)
        unmockkStatic(CityUtils::class)

        verify {
            cityListView.visibility = View.VISIBLE
            cityListView.scrollToPosition(0)
        }
    }

    @Test
    fun should_onLoadFinished_with_query_string_test_and_cursor_count_not_0() {
        mockkStatic(CityUtils::class)
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { clearFlags() }
            justRun { swapCursor(any()) }
        }
        val missMatch = mockk<LinearLayout>().apply {
            justRun { visibility = View.GONE }
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            justRun { visibility = View.VISIBLE }
            justRun { scrollToPosition(0) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
            every { mMissMatch } returns missMatch
            every { mCityList } returns cityListView
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        every { CityUtils.isJapanese() } returns false

        val addCityManager = mockk<AddCityManager>().apply {
            justRun { addStatistics(any()) }
            every { mViewHolder } returns viewHolder
            every { mQueryString } returns "test"
            every { onLoadFinished(any(), any()) } answers { callOriginal() }
        }
        val cursor = mockk<Cursor>().apply {
            every { count } returns 1
        }
        val loader = mockk<Loader<Cursor?>>()
        addCityManager.onLoadFinished(loader, cursor)
        unmockkStatic(CityUtils::class)

        verify {
            cityListView.visibility = View.VISIBLE
            cityListView.scrollToPosition(0)
        }
    }

    @Test
    @Ignore
    fun should_onLoadFinished_with_query_string_test_else() {
        mockkStatic(CityUtils::class)
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { clearFlags() }
            justRun { swapCursor(any()) }
        }
        val missMatch = mockk<LinearLayout>().apply {
            justRun { visibility = View.GONE }
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            justRun { visibility = View.VISIBLE }
            justRun { scrollToPosition(0) }
        }
        val emptyView = mockk<EffectiveAnimationView>()
        val tvEmptyView = mockk<TextView>()

        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
            every { mMissMatch } returns missMatch
            every { mCityList } returns cityListView
            every { mViewEmpty } returns emptyView
            every { mTvEmpty } returns tvEmptyView
        }
        val dialog = mockk<AddCityBottomSheetDialog>().apply {
            every { playEmptyAnimOrShowEmptyIcon(any(), any(), any()) }
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        every { CityUtils.isJapanese() } returns false

        val addCityManager = mockk<AddCityManager>().apply {
            justRun { addStatistics(any()) }
            every { mViewHolder } returns viewHolder
            every { mQueryString } returns "test"
            every { mFragment } returns fragment
            every { mDialog } returns dialog
            every { onLoadFinished(any(), any()) } answers { callOriginal() }
        }
        val cursor = mockk<Cursor>().apply {
            every { count } returns 1
        }
        val loader = mockk<Loader<Cursor?>>()
        addCityManager.onLoadFinished(loader, cursor)
        unmockkStatic(CityUtils::class)

        verify {
            cityListView.visibility = View.VISIBLE
            cityListView.scrollToPosition(0)
        }
    }

    @Test
    fun should_onLoaderReset_run() {
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            justRun { swapCursor(null) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mViewHolder } returns viewHolder
            every { onLoaderReset(any()) } answers { callOriginal() }
        }

        val loader = mockk<Loader<Cursor?>>()
        addCityManager.onLoaderReset(loader)

        verify {
            listAdapter.swapCursor(null)
        }
    }

    @Test
    fun should_onKey_with_char_else() {
        mockkStatic(CityUtils::class)
        val mockkCursor = mockk<Cursor>().apply {
            every { isClosed } returns false
            every { moveToFirst() } returns true
            every { getColumnIndex(any()) } returns 0
            every { getString(any()) } returns "test"
            every { position } returns 0
            every { moveToNext() } returns false
        }
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            every { cursor } returns mockkCursor
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            every { layoutManager } returns null
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mListAdapter } returns listAdapter
            every { mCityList } returns cityListView
            every { mSearchBar } returns null
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        every { CityUtils.isJapanese() } returns false

        val addCityManager = AddCityManager(fragment, viewHolder)

        addCityManager.onKey("t")
        unmockkStatic(CityUtils::class)

        verify {
            mockkCursor.moveToFirst()
            mockkCursor.position
        }
    }

    @Test
    fun should_onKey_with_char_mi() {
        val searchBar = mockk<EditText>().apply {
            every { windowToken } returns null
        }
        val cityListView = mockk<COUIRecyclerView>().apply {
            justRun { scrollToPosition(0) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mCityList } returns cityListView
            every { mSearchBar } returns searchBar
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder)

        addCityManager.onKey("*")
        verify {
            cityListView.scrollToPosition(0)
        }
    }

    @Test
    fun should_onNameClick_with_char() {
        val touchSearchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns touchSearchView
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mViewHolder } returns viewHolder
            every { onNameClick(any()) } answers { callOriginal() }
        }

        addCityManager.onNameClick("*")
        verify {
            touchSearchView.closing()
        }
    }

    @Test
    fun should_registerConfigChangeReceiver_with_run() {
        val mockkIntent = mockk<Intent>()
        val activity = mockk<FragmentActivity>().apply {
            every { registerReceiver(any(), any(), any()) } returns mockkIntent
        }

        val addCityManager = mockk<AddCityManager>().apply {
            every { mActivity } returns activity
            every { registerConfigChangeReceiver() } answers { callOriginal() }
        }
        addCityManager.registerConfigChangeReceiver()
        verify {
            activity.registerReceiver(any(), any(), any())
        }
    }

    @Test
    fun should_unregisterConfigChangeReceiver_with_run() {
        val activity = mockk<FragmentActivity>().apply {
            justRun { unregisterReceiver(any()) }
        }

        val addCityManager = mockk<AddCityManager>().apply {
            every { mActivity } returns activity
            every { unregisterConfigChangeReceiver() } answers { callOriginal() }
        }
        addCityManager.unregisterConfigChangeReceiver()
        verify {
            activity.unregisterReceiver(any())
        }
    }

    @Test
    fun should_showToast_with_short() {
        mockkStatic(ToastManager::class)
        justRun { ToastManager.showToast(any() as Int, Toast.LENGTH_SHORT) }
        val addCityManager = mockk<AddCityManager>().apply {
            every { showToast(any(), any()) } answers { callOriginal() }
        }
        addCityManager.showToast(121213, true)
        verify {
            ToastManager.showToast(any() as Int, Toast.LENGTH_SHORT)
        }
        unmockkStatic(ToastManager::class)
    }

    @Test
    fun should_showToast_with_long() {
        mockkStatic(ToastManager::class)
        justRun { ToastManager.showToast(any() as Int, Toast.LENGTH_LONG) }
        val addCityManager = mockk<AddCityManager>().apply {
            every { showToast(any(), any()) } answers { callOriginal() }
        }
        addCityManager.showToast(121213, false)
        verify {
            ToastManager.showToast(any() as Int, Toast.LENGTH_LONG)
        }
        unmockkStatic(ToastManager::class)
    }

    @Test
    fun should_saveResultForSetting_with_cursor() {
        val activity = mockk<FragmentActivity>().apply {
            justRun { setResult(any(), any()) }
        }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mActivity } returns activity
            every { saveResultForSetting(any(), any()) } answers { callOriginal() }
        }
        val cursor = mockk<Cursor>().apply {
            every { isClosed } returns false
            every { moveToPosition(0) } returns true
            every { getColumnIndex(ClockContract.City.CITY_ID) } returns 0
            every { getInt(0) } returns 1
            every { getColumnIndex(ClockContract.City.CITY_NAME) } returns 1
            every { getString(1) } returns "Shanghai"
            every { getColumnIndex(ClockContract.City.CITY_COUNTRY) } returns 2
            every { getString(2) } returns "China"
            every { getColumnIndex(ClockContract.City.TIMEZONE_ID) } returns 2
            every { getString(3) } returns "China/Shanghai"
            justRun { close() }
        }
        addCityManager.saveResultForSetting(0, cursor)
        verify {
            cursor.close()
            activity.setResult(any(), any())
       }
    }

    @Test
    fun should_addStatistics_with_cursor_null() {
        mockkStatic(ClockOplusCSUtils::class)
        justRun { ClockOplusCSUtils.onCommon(any(), any(), any()) }
        val addCityManager = mockk<AddCityManager>().apply {
            every { mQueryString } returns "test"
            every { addStatistics(any()) } answers { callOriginal() }
        }
        addCityManager.addStatistics(null)
        verify {
            ClockOplusCSUtils.onCommon(any(), any(), any())
        }
        unmockkStatic(ClockOplusCSUtils::class)
    }

    @Test
    fun should_onDestroy_just_run() {
        val touchSearchView = mockk<COUITouchSearchView>().apply {
            justRun { closing() }
            every { removeCallbacks(null) } returns true
        }
        val listAdapter = mockk<AddGlobalCityListAdapter>().apply {
            every { cursor } returns null
            justRun { swapCursor(null) }
            justRun { setOnItemClickListener(null) }
        }
        val viewHolder = mockk<AddCityViewHolder>().apply {
            every { mTouchSearchView } returns touchSearchView
            every { mListAdapter } returns listAdapter
        }
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<AddCityFragment>().apply {
            every { requireActivity() } returns activity
        }
        val addCityManager = AddCityManager(fragment, viewHolder)
        justRun { addCityManager.unregisterConfigChangeReceiver() }

        addCityManager.onDestroy()
        verify {
            touchSearchView.removeCallbacks(null)
            listAdapter.setOnItemClickListener(null)
            addCityManager.unregisterConfigChangeReceiver()
        }
    }
}