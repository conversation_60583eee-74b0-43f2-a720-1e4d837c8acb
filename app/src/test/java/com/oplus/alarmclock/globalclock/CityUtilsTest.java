/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-10, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.globalclock;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.ArrayMap;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class CityUtilsTest extends TestParent {
    private static final String TIMEZONE_ID_BEIJING = "Asia/Shanghai";
    private static final String TIMEZONE_ID_URUMQI = "Asia/Urumqi";
    private static final String TIMEZONE_ID_KASHGAR = "Asia/Kashgar";
    private static final String TIMEZONE_ID_PYONGYANG = "Asia/Pyongyang";
    private static final String TIMEZONE_ID_SEOUL = "Asia/Seoul";
    static final String TIMEZONE_ID_SDM = "Africa/Sao_Tome";
    static final String TIMEZONE_ID_STANDARD = "GMT-0";
    private static final int CITY_NAME_INDEX = 2;
    private static final int ID_CITY_INDEX = 4;
    @Test
    public void should_return_list_with_size_is_10_when_getCities_with_cursor_count_is_10()
            throws NoSuchMethodException, IllegalAccessException {
        //cursor count
        final int count = 10;
        //init cityNames return by cursor
        final String[] cityNames = new String[count];
        for(int i=0; i<count; i++){
            cityNames[i] = String.valueOf('a'+i);
        }
        //init timezoneIds return by cursor
        final String[] timezoneIds = new String[]{TIMEZONE_ID_URUMQI,TIMEZONE_ID_BEIJING,TIMEZONE_ID_KASHGAR,TIMEZONE_ID_PYONGYANG,TIMEZONE_ID_SEOUL,TIMEZONE_ID_SDM,TIMEZONE_ID_STANDARD,TIMEZONE_ID_STANDARD,TIMEZONE_ID_STANDARD,TIMEZONE_ID_STANDARD};
        //init timezoneIds replacement relation
        final ArrayMap<String, String> timezoneIdMap = new ArrayMap(8);
        timezoneIdMap.put(TIMEZONE_ID_URUMQI, TIMEZONE_ID_BEIJING);
        timezoneIdMap.put(TIMEZONE_ID_KASHGAR, TIMEZONE_ID_BEIJING);
        timezoneIdMap.put(TIMEZONE_ID_PYONGYANG, TIMEZONE_ID_SEOUL);
        timezoneIdMap.put(TIMEZONE_ID_SDM, TIMEZONE_ID_STANDARD);
        //init cursor's answer
        Answer answer = new Answer() {
            int mCount = count;
            int mPosition = -1;
            String[] mTimeZoneIds = timezoneIds;
            String[] mCityNames = cityNames;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String methodName = invocation.getMethod().getName();
                switch (methodName){
                    case "moveToNext":
                        if(mPosition >= mCount-1){
                            return false;
                        }
                        mPosition++;
                        return true;
                    case "getString":
                        int columnIndex = invocation.getArgument(0);
                        if(columnIndex == ID_CITY_INDEX){
                            return mTimeZoneIds[mPosition];
                        }else if(columnIndex == CITY_NAME_INDEX){
                            return mCityNames[mPosition];
                        }
                        break;
                }
                return null;
            }
        };
        Cursor cursor = Mockito.mock(Cursor.class);
        when(cursor.moveToNext()).thenAnswer(answer);
        when(cursor.getString(anyInt())).thenAnswer(answer);
        //init spyContext
        Context spyContext = spy(mContext);
        ContentResolver contentResolver = Mockito.mock(ContentResolver.class);
        when(contentResolver.query(any(Uri.class),any(String[].class), any(String.class),
                any(String[].class), any(String.class))).thenReturn(cursor);
        doReturn(contentResolver).when(spyContext).getContentResolver();
        //invoke getCities(Context context, String selection, String[] args)
        ArrayList<City> list = (ArrayList) ReflectUtil.invoke(CityUtils.class, "getCities", new Object[]{spyContext,
                "selection", new String[]{"args"}, "sortOrder"}, null, Context.class, String.class, String[].class, String.class);
        //assert
        assertEquals(count, list.size());
        for(int i=0; i<count; i++){
            City city = list.get(i);
            //assert City's name equals to name return by cursor
            assertEquals(cityNames[i], city.getName());
            //assert City's timezone equals to timezone return by cursor
            // or replace according to relations descripted by timezoneIdMap
            String expectedTimeZoneId = timezoneIdMap.get(timezoneIds[i]);
            if(expectedTimeZoneId ==  null){
                expectedTimeZoneId = timezoneIds[i];
            }
            assertEquals(expectedTimeZoneId, city.getTimezone());
        }
    }


    @Test
    public void testA(){
        //取城市名称
//       String name = "台北，台湾，中国";
       String name = "Taipei, Taiwan, China";


        name = name.split(",")[0];

        int index = name.indexOf("\uff0c");
        if (index == -1) {
            index = name.length();
        }
        int af = index;
    }
    @Test
    public void should_return_expectedClause_when_buildLocaleSelectionClause_with_specific_locale() throws NoSuchMethodException, IllegalAccessException {
        final Locale[] locales = new Locale[]{Locale.TAIWAN, Locale.CHINA, Locale.CANADA, Locale.US,
                Locale.UK, Locale.FRANCE, Locale.KOREA};
        ArrayMap<Locale,String> localeMap = new ArrayMap(8);
        localeMap.put(Locale.TAIWAN, ClockContract.City.TW_NAME);
        localeMap.put(Locale.CHINA, ClockContract.City.ZH_NAME);
        localeMap.put(Locale.CANADA, ClockContract.City.EN_NAME);
        localeMap.put(Locale.US, ClockContract.City.EN_NAME);
        localeMap.put(Locale.UK, ClockContract.City.EN_NAME);
        for(Locale locale : locales){
            //construct expectedClause
            String localeName = localeMap.get(locale);
            if(localeName == null){
                localeName = locale.toString();
            }
            String expectedClause = ClockContract.City.LOCALE_LAN + "='" + localeName + "'";
            //invoke buildLocaleSelectionClause()
            String clause = (String) ReflectUtil.invoke(CityUtils.class, "buildLocaleSelectionClause",
                    new Object[]{locale}, null, Locale.class);
            //assert
            assertEquals(expectedClause, clause);
        }
    }

    @Test
    public void should_return_string_in_cities_when_getDisplayCityName_with_string_link_cities_and_country(){
        String[] cities = new String[]{"bj","sh","gz","sz","hz","cq","wh"};
        City city = new City();
        for(int i=0; i<cities.length; i++){
            city.setName(cities[i]);
            //invoke getDisplayCityName()
            String cityName = city.getName();
            //assert
            assertEquals(cities[i], cityName);
        }
    }

    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_bigger_than_defaultZoneId_and_offsetMinute_is_zero() {
        Locale.setDefault(new Locale("en"));
        Locale.setDefault(Locale.CHINA);
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT+";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix+defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        Calendar defaultCalendar = Calendar.getInstance(defaultTimeZone);
        for(int i=defaultZoneId+1; i<=12; i++){
            builder.delete(0,builder.length());
            Calendar calendar = Calendar.getInstance(defaultTimeZone);
            int houroffset = defaultZoneId-i;
            calendar.add(Calendar.HOUR_OF_DAY,-houroffset);
            if(calendar.get(Calendar.DAY_OF_MONTH) != defaultCalendar.get(Calendar.DAY_OF_MONTH)){
                //tomorrow
                builder.append(dayOffsets[0]);
            }else {
                //today
                builder.append(dayOffsets[1]);
            }
            //plurality
            if (houroffset < -1) {
                builder.append(seperator).append(String.format(hourOffsets[1], Math.abs(houroffset)));
            } else {
                builder.append(seperator).append(String.format(hourOffsets[3], Math.abs(houroffset)));
            }
            String hourStr = mContext.getResources().getQuantityString(R.plurals.global_hour,houroffset);
            builder.append(hourStr);
            String expectedTimeZoneOffsetStr = builder.toString();
            System.out.println(expectedTimeZoneOffsetStr);
            //construct timezone id
            String gmtString = gmtPrefix + i;
            //invoke getTimeZoneOffset
            String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
            assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);

        }
    }


    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_smaller_than_defaultZoneId_and_offsetMinute_is_zero() {
        //ensure locale is not Russia
        Locale.setDefault(new Locale("en"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix + "+" + defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        Calendar defaultCalendar = Calendar.getInstance(defaultTimeZone);
        for(int i=defaultZoneId-1; i>=-12; i--){
            builder.delete(0,builder.length());
            Calendar calendar = Calendar.getInstance(defaultTimeZone);
            int houroffset = defaultZoneId-i;
            calendar.add(Calendar.HOUR_OF_DAY,-houroffset);
            if(calendar.get(Calendar.DAY_OF_MONTH) != defaultCalendar.get(Calendar.DAY_OF_MONTH)){
                //yesterday
                builder.append(dayOffsets[2]);
            }else {
                //today
                builder.append(dayOffsets[1]);
            }
            //plurality
            if (houroffset > 1) {
                builder.append(seperator).append(String.format(hourOffsets[0], Math.abs(houroffset)));
            } else {
                builder.append(seperator).append(String.format(hourOffsets[2], Math.abs(houroffset)));
            }
            String hourStr = mContext.getResources().getQuantityString(R.plurals.global_hour,houroffset);
            builder.append(hourStr);
            String expectedTimeZoneOffsetStr = builder.toString();
            System.out.println(expectedTimeZoneOffsetStr);
            //construct timezone id
            String sign = "+";
            if(i<0){
                sign = "-";
            }
            String gmtString = gmtPrefix + sign + Math.abs(i);
            //invoke getTimeZoneOffset
            String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
            System.out.println(timeZoneOffsetStr);
            assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
        }
    }


    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_equal_to_defaultZoneId_and_offsetMinute_is_zero() {
        //ensure locale is not Russia
        Locale.setDefault(new Locale("en"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String gmtPrefix = "GMT";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix + "+" + defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        //today
        builder.append(dayOffsets[1]);
        String expectedTimeZoneOffsetStr = builder.toString();
        System.out.println(expectedTimeZoneOffsetStr);
        //construct timezone id
        String gmtString = gmtPrefix + "+" + defaultZoneId;
        //invoke getTimeZoneOffset
        String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
        System.out.println(timeZoneOffsetStr);
        assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
    }


    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_bigger_than_defaultZoneId_and_offsetMinute_is_not_zero() {
        //ensure locale is not Russia
        Locale.setDefault(new Locale("en"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT+";
        String gmtSuffix = ":30";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix+defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        Calendar defaultCalendar = Calendar.getInstance(defaultTimeZone);
        for(int i=defaultZoneId+1; i<=12; i++){
            builder.delete(0,builder.length());
            Calendar calendar = Calendar.getInstance(defaultTimeZone);
            int houroffset = defaultZoneId-i;
            int minuteoffset = -30;
            calendar.add(Calendar.HOUR_OF_DAY,-houroffset);
            calendar.add(Calendar.MINUTE, -minuteoffset);
            if(calendar.get(Calendar.DAY_OF_MONTH) != defaultCalendar.get(Calendar.DAY_OF_MONTH)){
                //tomorrow
                builder.append(dayOffsets[0]);
            }else {
                //today
                builder.append(dayOffsets[1]);
            }
            int decimalPartInhourOffset = 5;
            //plurality
            builder.append(seperator).append(String.format(hourOffsets[5],
                    Math.abs(houroffset), decimalPartInhourOffset));
            String hourStr = mContext.getResources().getQuantityString(R.plurals.global_hour,houroffset);
            builder.append(hourStr);
            String expectedTimeZoneOffsetStr = builder.toString();
            System.out.println(expectedTimeZoneOffsetStr);
            //construct timezone id
            String gmtString = gmtPrefix + i + gmtSuffix;
            //invoke getTimeZoneOffset
            String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
            assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
        }
    }

    @Ignore
    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_smaller_than_defaultZoneId_and_offsetMinute_is_not_zero() {
        //ensure locale is not Russia
        Locale.setDefault(new Locale("en"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT+";
        String gmtSuffix = ":30";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix+defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        Calendar defaultCalendar = Calendar.getInstance(defaultTimeZone);
        for(int i=defaultZoneId-2; i>=-12; i--){
            builder.delete(0,builder.length());
            Calendar calendar = Calendar.getInstance(defaultTimeZone);
            //minuteoffset in reverse with houroffset.example:houroffset=2,minuteoffset=-0.5;
            // in the end houroffset=1.5
            int houroffset;
            int minuteoffset;
            if(i>=0){
                houroffset = defaultZoneId-i-1;
                minuteoffset = -30;

            }else {
                houroffset = defaultZoneId-i;
                minuteoffset = 30;
            }
            calendar.add(Calendar.HOUR_OF_DAY,-houroffset);
            calendar.add(Calendar.MINUTE, -minuteoffset);
            if(calendar.get(Calendar.DAY_OF_MONTH) != defaultCalendar.get(Calendar.DAY_OF_MONTH)){
                //tomorrow
                builder.append(dayOffsets[2]);
            }else {
                //today
                builder.append(dayOffsets[1]);
            }
            int decimalPartInhourOffset = 5;
            //plurality
            builder.append(seperator).append(String.format(hourOffsets[4], houroffset, decimalPartInhourOffset));
            String hourStr = mContext.getResources().getQuantityString(R.plurals.global_hour,houroffset);
            builder.append(hourStr);
            String expectedTimeZoneOffsetStr = builder.toString();
            System.out.println(expectedTimeZoneOffsetStr);
            //construct timezone id
            String gmtString = gmtPrefix + i + gmtSuffix;
            if(i<0){
                gmtString = gmtString.replace("+", "");
            }
            System.out.println(gmtString);
            //invoke getTimeZoneOffset
            String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, seperator);
            assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
        }
    }

    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_offsetHour_is_zero_and_offsetMinute_is_not_zero() {
        //ensure locale is not Russia
        Locale.setDefault(new Locale("en"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT";
        String gmtSuffix = ":15";
        int defaultZoneId = 8;
        //init defaultTimeZone GMT+8 Asia/shanghai
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix  + "+" + defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        //today
        builder.append(dayOffsets[1]);
        int hourOffset = 0;
        int decimalPartInHour = 25;
        builder.append(seperator).append(String.format(hourOffsets[5],
                hourOffset, Math.abs(decimalPartInHour)));
        String hourStr = mContext.getResources().getQuantityString(R.plurals.global_hour,hourOffset);
        builder.append(hourStr);
        String expectedTimeZoneOffsetStr = builder.toString();
        System.out.println(expectedTimeZoneOffsetStr);
        //construct timezone id
        String gmtString = gmtPrefix + "+" + defaultZoneId + gmtSuffix;
        //invoke getTimeZoneOffset
        String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
        System.out.println(timeZoneOffsetStr);
        assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
    }

    @Config(qualifiers = "ru")
    @Test
    public void should_return_expectedTimeZoneOffsetStr_when_getTimeZoneOffset_with_zoneId_bigger_than_defaultZoneId_and_minuteoffset_is_zero_and_languge_is_russian() {
        //ensure locale  isnot Russia
        Locale.setDefault(new Locale("ru"));
        StringBuilder builder = new StringBuilder();
        String[] dayOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_day_offset);
        String[] hourOffsets = mContext.getResources()
                .getStringArray(R.array.global_timezone_hours_format);
        String seperator = ", ";
        String gmtPrefix = "GMT+";
        int defaultZoneId = 0;
        TimeZone defaultTimeZone = TimeZone.getTimeZone(gmtPrefix+defaultZoneId);
        TimeZone.setDefault(defaultTimeZone);
        Calendar defaultCalendar = Calendar.getInstance(defaultTimeZone);
        for(int i=defaultZoneId+1; i<=12; i++){
            builder.delete(0,builder.length());
            Calendar calendar = Calendar.getInstance(defaultTimeZone);
            int houroffset = defaultZoneId-i;
            calendar.add(Calendar.HOUR_OF_DAY,-houroffset);
            if(calendar.get(Calendar.DAY_OF_MONTH) != defaultCalendar.get(Calendar.DAY_OF_MONTH)){
                //tomorrow
                builder.append(dayOffsets[0]);
            }else {
                //today
                builder.append(dayOffsets[1]);
            }
            //plurality
            if (houroffset < -1) {
                builder.append(seperator).append(String.format(hourOffsets[1], Math.abs(houroffset)));
            } else {
                builder.append(seperator).append(String.format(hourOffsets[3], Math.abs(houroffset)));
            }
            String hourStr;
            if (Math.abs(houroffset) <= 1) {
                hourStr = mContext.getString(R.string.hour1);
            } else if (Math.abs(houroffset) <= 4) {
                hourStr = mContext.getString(R.string.hour);
            } else {
                hourStr = mContext.getString(R.string.hour5_);
            }
            builder.append(hourStr);
            String expectedTimeZoneOffsetStr = builder.toString();
            System.out.println(expectedTimeZoneOffsetStr);
            //construct timezone id
            String gmtString = gmtPrefix + i;
            //invoke getTimeZoneOffset
            String timeZoneOffsetStr = CityUtils.getTimeZoneOffset(gmtString, ", ");
            assertEquals(expectedTimeZoneOffsetStr, timeZoneOffsetStr);
        }
    }




}
