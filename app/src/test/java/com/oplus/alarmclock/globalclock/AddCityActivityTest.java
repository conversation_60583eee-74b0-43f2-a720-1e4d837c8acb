/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-5, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.globalclock;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Loader;
import android.database.Cursor;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewPropertyAnimator;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.testing.FragmentScenario;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.coui.appcompat.searchview.COUISearchView;
import com.coui.appcompat.searchview.COUISearchBar;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.behavior.HeadScaleWithSearchBhv;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.behavior.HeadScaleWithSearchBhv;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.Utils;
import com.coui.appcompat.touchsearchview.COUITouchSearchView;

import org.junit.After;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Locale;

import com.google.android.material.appbar.AppBarLayout;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.behavior.HeadScaleWithSearchBhv;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;
import com.oplus.alarmclock.provider.ClockContract;
import com.oplus.alarmclock.utils.PrefUtils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.adapter.AddGlobalCityListAdapter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyFloat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {AddCityActivityTest.ShadowUtils.class})
@Ignore
public class AddCityActivityTest extends TestParent {
    FragmentScenario<AddCityFragment> spyFragmentScenario;
    AddCityFragment spyActivity;
    ViewGroup spyViewGroup;
    private AppBarLayout mColorAppBarLayout;
    private COUISearchBar mGlobalSearchView;
    private COUIToolbar mToolBar;
    private View mForeground;
    private CoordinatorLayout.LayoutParams mLayoutParams;
    private HeadScaleWithSearchBhv mBehavior;
    private RelativeLayout mRlSearch;
    private COUITouchSearchView mTouchSearchView;
    private COUIRecyclerView mCityList;
    private EditText mSearchBar;
    private View mDivider;
    private ViewStub mSearchTipViewStub;
    private TextView mMissMatch;
    private final int MAX_INPUT_LENGTH = 100;
    private final int MINUTE_IN_MILLIS = 60 * 1000;
    private final int HOUR_IN_MILLIS = MINUTE_IN_MILLIS * 60;

    @After
    public void tearDown() {
        spyActivity = null;
        mGlobalSearchView = null;
        mColorAppBarLayout = null;
        mDivider = null;
        mForeground = null;
        mSearchBar = null;
        mSearchTipViewStub = null;
        mToolBar = null;
        mLayoutParams = null;
        mBehavior = null;
        mCityList = null;
        mRlSearch = null;
        mTouchSearchView = null;
    }

    public void initActivity() throws NoSuchFieldException, IllegalAccessException {
        mGlobalSearchView = mock(COUISearchBar.class);
        mColorAppBarLayout = mock(AppBarLayout.class);
        mDivider = mock(View.class);
        mForeground = mock(LinearLayout.class);
        mSearchBar = mock(EditText.class);
        mSearchTipViewStub = mock(ViewStub.class);
        mToolBar = mock(COUIToolbar.class);
        mLayoutParams = mock(CoordinatorLayout.LayoutParams.class);
        mBehavior = mock(HeadScaleWithSearchBhv.class);
        mCityList = mock(COUIRecyclerView.class);
        mRlSearch = mock(RelativeLayout.class);
        mTouchSearchView = mock(COUITouchSearchView.class);
        mMissMatch = mock(TextView.class);

        when(mColorAppBarLayout.getLayoutParams()).thenReturn(mLayoutParams);
        when(mLayoutParams.getBehavior()).thenReturn(mBehavior);
        when(mGlobalSearchView.getSearchEditText()).thenReturn(mSearchBar);
        when(mColorAppBarLayout.getViewTreeObserver()).thenReturn(mock(ViewTreeObserver.class));
        when(mRlSearch.getLayoutParams()).thenReturn(mock(CoordinatorLayout.LayoutParams.class));
        spyFragmentScenario = FragmentScenario.launch(AddCityFragment.class, null, R.style.AppNoTitleThemeTranslucent, null);
        spyFragmentScenario.onFragment(fragment -> spyActivity = fragment);
        spyViewGroup = (ViewGroup) ReflectUtil.getFieldValue(AddCityFragment.class, "mRoot", spyActivity);
        doReturn(mColorAppBarLayout).when(spyViewGroup).findViewById(R.id.abl_toolbar_options);
        doReturn(mToolBar).when(spyViewGroup).findViewById(R.id.toolbar_options);
        doReturn(mDivider).when(spyViewGroup).findViewById(R.id.divider);
//        doReturn(mSearchTipViewStub).when(spyActivity).findViewById(R.id.stub_tip_layout);
//        doReturn(mForeground).when(spyActivity).findViewById(R.id.foreground);
        doReturn(mGlobalSearchView).when(spyViewGroup).findViewById(R.id.globalSearchView);
        doReturn(mTouchSearchView).when(spyViewGroup).findViewById(R.id.touch_search_bar);
        doReturn(mMissMatch).when(spyViewGroup).findViewById(R.id.miss_match);
        doReturn(mCityList).when(spyViewGroup).findViewById(R.id.allCities);
//        doReturn(mRlSearch).when(spyViewGroup).findViewById(R.id.rl_search);

        spyActivity.onCreateView(Mockito.mock(LayoutInflater.class), Mockito.mock(ViewGroup.class), Mockito.mock(Bundle.class));
    }

    @Override
    public void setUp() throws Exception {
        super.setUp();
        initActivity();
    }

    @Config(shadows = {ShadowPrefUtils.class})
    @Test
    public void should_call_animToSearch_and_showSearchTipIfNeeded_when_onStateChange_with_STATE_EDIT_and_shouldShowCitySearchTip_return_true() {
        ShadowPrefUtils.sShouldShowCitySearchTip = true;
        ViewPropertyAnimator mockAnimator1 = mock(ViewPropertyAnimator.class);
        when(mToolBar.animate()).thenReturn(mockAnimator1);
        when(mockAnimator1.alpha(anyFloat())).thenReturn(mockAnimator1);
        when(mockAnimator1.setDuration(anyLong())).thenReturn(mockAnimator1);
        when(mockAnimator1.setListener(any(AnimatorListenerAdapter.class))).thenReturn(mockAnimator1);

        ViewPropertyAnimator mockAnimator2 = mock(ViewPropertyAnimator.class);
        when(mockAnimator2.alpha(anyFloat())).thenReturn(mockAnimator2);
        when(mockAnimator2.setDuration(anyLong())).thenReturn(mockAnimator2);
        when(mockAnimator2.setListener((ArgumentMatchers.isNull(Animator.AnimatorListener.class)))).thenReturn(mockAnimator2);

        View searchTipView = mock(View.class);
//        when(searchTipView.findViewById(R.id.tip_textview)).thenReturn(mock(TextView.class));
//        when(searchTipView.findViewById(R.id.cancel_tip)).thenReturn(mock(View.class));
        when(mSearchTipViewStub.inflate()).thenReturn(searchTipView);
        //invoke onStateChange
        spyActivity.onStateChange(0, COUISearchBar.STATE_EDIT);

        //verify
        verify(mSearchTipViewStub).inflate();
//        verify(mBackgroundMask).animate();
        verify(mockAnimator2).alpha(1);
        verify(mTouchSearchView, times(2)).closing();
    }


    @Test
    public void should_set_setSelection_0_and_call_animBack_when_onStateChange_with_STATE_NORMAL() throws NoSuchFieldException, IllegalAccessException {
        ShadowPrefUtils.sShouldShowCitySearchTip = true;
        ViewPropertyAnimator mockAnimator1 = mock(ViewPropertyAnimator.class);
        when(mToolBar.animate()).thenReturn(mockAnimator1);
        when(mockAnimator1.alpha(anyFloat())).thenReturn(mockAnimator1);
        when(mockAnimator1.setDuration(anyLong())).thenReturn(mockAnimator1);
        when(mockAnimator1.setListener(any(AnimatorListenerAdapter.class))).thenReturn(mockAnimator1);

        ViewPropertyAnimator mockAnimator2 = mock(ViewPropertyAnimator.class);
        when(mockAnimator2.alpha(anyFloat())).thenReturn(mockAnimator2);
        when(mockAnimator2.setDuration(anyLong())).thenReturn(mockAnimator2);
        when(mockAnimator2.setListener((any(Animator.AnimatorListener.class)))).thenReturn(mockAnimator2);

        View searchTipView = mock(View.class);
//        when(searchTipView.findViewById(R.id.tip_textview)).thenReturn(mock(TextView.class));
//        when(searchTipView.findViewById(R.id.cancel_tip)).thenReturn(mock(View.class));
        when(mSearchTipViewStub.inflate()).thenReturn(searchTipView);
        ObjectAnimator mockAnimator = mock(ObjectAnimator.class);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mSearchAnimator", spyActivity,
                mockAnimator);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mPaddingTopAnimator", spyActivity,
                mockAnimator);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mHeaderHeightAnimator", spyActivity,
                mockAnimator);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mSearchViewHeightAnimator", spyActivity,
                mockAnimator);
        //invoke onStateChange
        spyActivity.onStateChange(0, COUISearchBar.STATE_NORMAL);
        //verify
        verify(mBehavior).setScaleEnable(false);
        verify(mockAnimator2).alpha(0);
    }



    @Ignore
    @Test
    public void should_call_hideSearchTipView_when_onTouch_with_ACTION_DOWN_and_mSearchAnimator_is_not_running()
            throws NoSuchFieldException, IllegalAccessException {
        ObjectAnimator searchAnimator = mock(ObjectAnimator.class);
        boolean isRunning = false;
        when(searchAnimator.isRunning()).thenReturn(isRunning);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mSearchAnimator", spyActivity,
                searchAnimator);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mSearchTipViewStub", spyActivity,
                mSearchTipViewStub);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mForeground", spyActivity,
                mForeground);

        MotionEvent event = mock(MotionEvent.class);
        when(event.getAction()).thenReturn(MotionEvent.ACTION_DOWN);
        //invoke onTouch()
//        spyActivity.onTouch(null, event);
        //verify
        verify(mSearchTipViewStub).setVisibility(View.GONE);
    }


    @Config(shadows = {ShadowCityUtils.class})
    @Test
    public void should_return_selected_when_cityIsSelected_with_cityId_in_cursor_equals_to_selectedCity_cityId()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        //init mListAdapter
        ReflectUtil.invoke(AddCityActivity.class, "initListView", null,
                spyActivity);
        Cursor cursor = mock(Cursor.class);
        boolean isSuccess = true;
        when(cursor.moveToPosition(anyInt())).thenReturn(isSuccess);
        long cityId = 10;
        when(cursor.getLong(anyInt())).thenReturn(cityId);
        int listSize = 8;
        ShadowCityUtils.sCities = new ArrayList(listSize);
        for(int i=0; i<listSize; i++){
            City city = new City();
            //ensure City descripted by cityId in sCities
            city.setCityId((int)cityId+i);
            ShadowCityUtils.sCities.add(city);
        }
        //invoke cityIsSelected()
        int position = 1;
        boolean selected = (boolean) ReflectUtil.invoke(AddCityActivity.class, "cityIsSelected", new Object[]{position, cursor},
                spyActivity, int.class, Cursor.class);
        assertTrue(selected);
        //release
        ShadowCityUtils.sCities = null;
    }

    @Test
    public void should_return_unselected_when_cityIsSelected_with_cityId_in_cursor_not_in_selectedCityIds() throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        //init mListAdapter
        ReflectUtil.invoke(AddCityActivity.class, "initListView", null,
                spyActivity);
        Cursor cursor = mock(Cursor.class);
        boolean isSuccess = true;
        when(cursor.moveToPosition(anyInt())).thenReturn(isSuccess);
        long cityId = 10;
        when(cursor.getLong(anyInt())).thenReturn(cityId);
        int listSize = 8;
        ShadowCityUtils.sCities = new ArrayList(listSize);
        for(int i=0; i<listSize; i++){
            City city = new City();
            //ensure City descripted by cityId not in sCities
            city.setCityId((int)cityId+i+1);
            ShadowCityUtils.sCities.add(city);
        }
        int position = 1;
        //invoke cityIsSelected()
        boolean selected = (boolean) ReflectUtil.invoke(AddCityActivity.class, "cityIsSelected", new Object[]{position, cursor},
                spyActivity, int.class, Cursor.class);
        assertFalse(selected);
        //release
        ShadowCityUtils.sCities = null;
    }

    @Ignore
    @Test
    public void should_never_call_setQuery_and_call_mBackgroundMask_setVisibility_GONE_when_doQuery_with_newText_not_empty_and_length_smaller_than_MAX_INPUT_LENGTH() throws NoSuchMethodException, IllegalAccessException {
        char[] chars = new char[MAX_INPUT_LENGTH/5];
        Arrays.fill(chars, 'a');
        String newText = String.valueOf(chars);
        ReflectUtil.invoke(AddCityActivity.class, "doQuery", new Object[]{newText}, spyActivity, String.class);
        //verify
        verify(mSearchBar,never()).setText(newText, TextView.BufferType.EDITABLE);
//        verify(mBackgroundMask).setVisibility(View.GONE);
    }

    @Ignore
    @Test
    public void should_cut_out_newText_and_call_setQuery_when_doQuery_with_newText_length_bigger_than_MAX_INPUT_LENGTH()
            throws NoSuchMethodException, IllegalAccessException {
        int extraLength = 10;
        char[] extraLongChars = new char[MAX_INPUT_LENGTH+extraLength];
        Arrays.fill(extraLongChars, 'a');
        char[] maxLongChars = new char[MAX_INPUT_LENGTH];
        Arrays.fill(maxLongChars, 'a');
        String newText = String.valueOf(extraLongChars);
        String standardText = String.valueOf(maxLongChars);
        ReflectUtil.invoke(AddCityActivity.class, "doQuery", new Object[]{newText}, spyActivity, String.class);
        //verify
        verify(mSearchBar).setText(standardText, TextView.BufferType.EDITABLE);
//        verify(mBackgroundMask).setVisibility(View.GONE);
    }


    @Config(shadows = {ShadowCityUtils.class})
    @Test
    public void should_call_mCityList_setVisibility_with_VISIBLE_and_swapCursor_with_cursor_when_onLoadFinished_with_queryString_notEmpty_and_cursor_getCount_is_1()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        AddGlobalCityListAdapter mListAdapter = mock(AddGlobalCityListAdapter.class);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mListAdapter", spyActivity, mListAdapter);
        String queryString = "queryString";
        ReflectUtil.setFieldValue(AddCityActivity.class, "mQueryString", spyActivity, queryString);
        Cursor cursor = mock(Cursor.class);
        int count = 1;
        when(cursor.getCount()).thenReturn(count);
        Loader<Cursor> loader = mock(Loader.class);
        ShadowCityUtils.sIsJapanese = false;
        //invoke onLoadFinished()
        ReflectUtil.invoke(AddCityActivity.class, "onLoadFinished", new Object[]{loader, cursor}, spyActivity, Loader.class,Cursor.class);
        //verify
        verify(mCityList).setVisibility(View.VISIBLE);
        verify(mListAdapter).swapCursor(cursor);
    }

    @Config(shadows = {ShadowCityUtils.class})
    @Test
    public void should_call_mCityList_setVisibility_with_GONE_and_never_call_swapCursor_with_cursor_when_onLoadFinished_with_queryString_notEmpty_and_cursor_getCount_is_0_and_local_is_japanese()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        AddGlobalCityListAdapter mListAdapter = mock(AddGlobalCityListAdapter.class);
        ReflectUtil.setFieldValue(AddCityActivity.class, "mListAdapter", spyActivity, mListAdapter);
        String queryString = "queryString";
        ReflectUtil.setFieldValue(AddCityActivity.class, "mQueryString", spyActivity, queryString);
        Cursor cursor = mock(Cursor.class);
        int count = 0;
        when(cursor.getCount()).thenReturn(count);
        Loader<Cursor> loader = mock(Loader.class);
        ShadowCityUtils.sIsJapanese = true;
        //invoke onLoadFinished()
        ReflectUtil.invoke(AddCityActivity.class, "onLoadFinished", new Object[]{loader, cursor}, spyActivity, Loader.class,Cursor.class);
        //verify
        verify(mCityList).setVisibility(View.GONE);
        verify(mListAdapter,never()).swapCursor(cursor);
    }

    @Test
    public void should_return_expectedWhereClause_when_buildWhereClause_with_locale_is_china_and_queryText_is_ASIIC() throws NoSuchMethodException, IllegalAccessException {
        String queryText = "''test\\test__test%%test";
        String queryTextInSql = "''''test\\\\test\\_\\_test\\%\\%test";
        String localeSelectionClause = "locale='zh_CN'";
        String expectedWhereClause = "(" + ClockContract.City.FIRST_SPELL + " LIKE '" + queryTextInSql + "%' OR "
                + ClockContract.City.FULL_SPELL + " LIKE '" + queryTextInSql + "%')"
                + " AND " + localeSelectionClause;
        Locale.setDefault(Locale.CHINA);
        //invoke buildWhereClause()
        String whereClause = (String) ReflectUtil.invoke(AddCityActivity.class, "buildWhereClause",
                new Object[]{queryText}, spyActivity, String.class);
        //assert
        assertEquals(expectedWhereClause, whereClause);
    }

    @Test
    public void should_return_expectedWhereClause_when_buildWhereClause_with_locale_is_taiwan_and_queryText_is_not_ASIIC() throws NoSuchMethodException, IllegalAccessException {
        String queryText = "深圳''深圳\\深圳__深圳%%深圳";
        String queryTextInSql = "深圳''''深圳\\\\深圳\\_\\_深圳\\%\\%深圳";
        String localeSelectionClause = "locale='zh_TW'";
        String expectedWhereClause = "(" + ClockContract.City.CITY_NAME + " LIKE '%" + queryTextInSql + "%'"
                + " OR " + ClockContract.City.REGION + "='" + queryTextInSql + "')"
                + " AND " + localeSelectionClause;
        Locale.setDefault(Locale.TAIWAN);
        //invoke buildWhereClause()
        String whereClause = (String) ReflectUtil.invoke(AddCityActivity.class, "buildWhereClause",
                new Object[]{queryText}, spyActivity, String.class);
        //assert
        assertEquals(expectedWhereClause, whereClause);
    }

    @Test
    public void should_return_expectedWhereClause_when_buildWhereClause_with_locale_not_in_TRANSLATIONS() throws NoSuchMethodException, IllegalAccessException {
        String queryText = "test''test\\test__test%%test";
        String queryTextInSql = "test''''test\\\\test\\_\\_test\\%\\%test";
        String localeSelectionClause = "locale='en_US'";
        String expectedWhereClause = "(" + ClockContract.City.CITY_NAME + " LIKE '%" + queryTextInSql + "%'"
                + " OR " + ClockContract.City.REGION + "='" + queryTextInSql + "'"
                + " OR (" + ClockContract.City.REGION + "='" + queryTextInSql + "' COLLATE NOCASE)"
                + " OR " + "REPLACE(" + ClockContract.City.REGION + ",' ','')='" + queryTextInSql + "'"
                + " OR " + "(REPLACE(" + ClockContract.City.REGION + ",' ','')='" + queryTextInSql
                + "' COLLATE NOCASE)" + ")"
                + " AND " + localeSelectionClause;
        Locale.setDefault(Locale.US);
        //invoke buildWhereClause()
        String whereClause = (String) ReflectUtil.invoke(AddCityActivity.class, "buildWhereClause",
                new Object[]{queryText}, spyActivity, String.class);
        //assert
        assertEquals(expectedWhereClause, whereClause);
    }

    @Ignore
    @Test
    public void should_call_setSelection_with_keyPosInfirstLetters_when_doFastScroll_with_key()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        Locale.setDefault(Locale.US);
        AddGlobalCityListAdapter cursorAdapter = (AddGlobalCityListAdapter) ReflectUtil.getFieldValue(AddCityActivity.class,
                "mListAdapter", spyActivity);
        Cursor cursor = mock(Cursor.class);
        cursorAdapter.swapCursor(cursor);
        Answer cursorAnswer = new Answer<Object>() {
            String[] firstLetters = new String[]{"a","b","c","c","e","d"};
            int position = 0;
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                String methodName = invocation.getMethod().getName();
                if(methodName.equals("getString")){
                    return firstLetters[position];
                }else if(methodName.equals("getPosition")){
                    return position;
                }else if(methodName.equals("moveToNext")){
                    if(position <firstLetters.length-1){
                        position++;
                        return true;
                    }else {
                        return false;
                    }
                }
                return null;
            }
        };
        when(cursor.moveToFirst()).thenReturn(true);
        int columeIndex = 0;
        when(cursor.getColumnIndex(ClockContract.City.FIRST_LETTER)).thenReturn(columeIndex);
        when(cursor.getString(columeIndex)).thenAnswer(cursorAnswer);
        when(cursor.getPosition()).thenAnswer(cursorAnswer);
        when(cursor.moveToNext()).thenAnswer(cursorAnswer);
        String key = "D";
        LinearLayoutManager mLayoutManager = mock(LinearLayoutManager.class);
        when(mCityList.getLayoutManager()).thenReturn(mLayoutManager);
        when(mBehavior.getIsFirstIn()).thenReturn(true);
        when(mGlobalSearchView.getSearchState()).thenReturn(COUISearchBar.STATE_NORMAL);
        //invoke doFastScroll()
        ReflectUtil.invoke(AddCityActivity.class, "doFastScroll", new Object[]{key}, spyActivity, CharSequence.class);
        //verify
        int keyPosInfirstLetters = 5;
        verify(mLayoutManager).scrollToPositionWithOffset(keyPosInfirstLetters-1, 0);
    }

    @Implements(CityUtils.class)
    public static class ShadowCityUtils{
        static ArrayList<City> sCities;
        static boolean sIsJapanese;
        @Implementation
        public static ArrayList<City> getAllCities(Context context) {
            return sCities;
        }
        @Implementation
        public static boolean isJapanese() {
            return sIsJapanese;
        }

    }

    @Implements(PrefUtils.class)
    public static class ShadowPrefUtils{
        static boolean sShouldShowCitySearchTip;
        @Implementation
        public static boolean shouldShowCitySearchTip(Context context) {
            return sShouldShowCitySearchTip;
        }
    }

    @Implements(Utils.class)
    public static class ShadowUtils{
        public static float setSuitableFontSize(TextView view, float dfltSize, float fontScale, int sizeType) {
            return 0.0f;
        }
    }


}
