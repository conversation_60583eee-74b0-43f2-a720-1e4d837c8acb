/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-16, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.globalclock;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.ItemTouchHelper;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.adapter.CityListAdapter;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SimpleItemTouchHelperCallBackTest extends TestParent {
    SimpleItemTouchHelperCallBack mCallback;
    @Override
    public void setUp() throws Exception {
        super.setUp();
        mCallback = new SimpleItemTouchHelperCallBack(mContext, mock(CityListAdapter.class),mock(IAfterMove.class));
    }
    @Test
    public void should_call_onItemSelected_when_onSelectedChanged_with_actinoState_not_equalTo_ACTION_STATE_IDLE(){
        CityListAdapter.ItemTouchViewHolder holder = mock(CityListAdapter.ItemTouchViewHolder.class);
        //ensure actinoState not equals to ACTION_STATE_IDLE
        int actinoState = ItemTouchHelper.ACTION_STATE_IDLE+1;
        //invoke onSelectedChanged()
        mCallback.onSelectedChanged(holder, actinoState);
        //verify
        verify(holder).onItemSelected();
    }

    @Test
    public void should_call_onItemDrop_when_onSelectedChanged_with_ACTION_STATE_IDLE() throws NoSuchFieldException, IllegalAccessException {
        CityListAdapter.ItemTouchViewHolder holder = mock(CityListAdapter.ItemTouchViewHolder.class);
        ReflectUtil.setFieldValue(SimpleItemTouchHelperCallBack.class, "mSelectedViewHolder",
                mCallback, holder);
        //ensure actinoState not equals to ACTION_STATE_IDLE
        //invoke onSelectedChanged()
        mCallback.onSelectedChanged(null, ItemTouchHelper.ACTION_STATE_IDLE);
        //verify
        verify(holder).onItemDrop();
    }

    @Test
    public void should_return_viewholder_above_selectedView_and_nearest_to_selectedView_when_chooseDropTarget_with_curY_in_view_above_selectedView(){
        int heigh = 10;
        int width = 20;
        int size = 10;
        int left = 0;
        List<COUIRecyclerView.ViewHolder> dropTargets = new ArrayList(size);
        for(int i=0; i<size; i++){
            int top = i*heigh;
            int bottom = top + heigh;
            int right = left + width;
            View itemView = mock(View.class);
            when(itemView.getHeight()).thenReturn(heigh);
            when(itemView.getWidth()).thenReturn(width);
            when(itemView.getTop()).thenReturn(top);
            when(itemView.getLeft()).thenReturn(left);
            when(itemView.getBottom()).thenReturn(bottom);
            when(itemView.getRight()).thenReturn(right);
            COUIRecyclerView.ViewHolder holder = new TestViewHolder(itemView);
            dropTargets.add(holder);
        }
        int selectedIndex = size-1;
        COUIRecyclerView.ViewHolder selected = dropTargets.get(selectedIndex);
        int repeat = 10;
        Random random = new Random();
        for(int i=0; i<repeat; i++){
            //set indexOfTouchedItem in (0,selectedIndex-1)
            int indexOfTouchedItem = random.nextInt(selectedIndex);
            //obtain ViewHolder above selected item
            COUIRecyclerView.ViewHolder expectedWinner = dropTargets.get(selectedIndex - 1);
            int curX = left;
            //ensure curY smaller than targetItem's midline in vertical
            int curY = indexOfTouchedItem * heigh;
            //invoke chooseDropTarget()
            COUIRecyclerView.ViewHolder winner = mCallback.chooseDropTarget(selected, dropTargets, curX, curY);
            System.out.println(indexOfTouchedItem + " : " + expectedWinner.itemView.getTop() + " : " +winner.itemView.getTop());
            //assert
            assertEquals(expectedWinner, winner);
        }
    }


    @Test
    public void should_return_viewholder_below_selectedView_and_nearest_to_selectedView_when_chooseDropTarget_with_curY_in_view_below_selectedView(){
        int heigh = 10;
        int width = 20;
        int size = 10;
        int left = 0;
        List<COUIRecyclerView.ViewHolder> dropTargets = new ArrayList(size);
        for(int i=0; i<size; i++){
            int top = i*heigh;
            int bottom = top + heigh;
            int right = left + width;
            View itemView = mock(View.class);
            when(itemView.getHeight()).thenReturn(heigh);
            when(itemView.getWidth()).thenReturn(width);
            when(itemView.getTop()).thenReturn(top);
            when(itemView.getLeft()).thenReturn(left);
            when(itemView.getBottom()).thenReturn(bottom);
            when(itemView.getRight()).thenReturn(right);
            COUIRecyclerView.ViewHolder holder = new TestViewHolder(itemView);
            dropTargets.add(holder);
        }
        int selectedIndex = 0;
        COUIRecyclerView.ViewHolder selected = dropTargets.get(selectedIndex);
        int repeat = 10;
        Random random = new Random();
        for(int i=0; i<repeat; i++){
            //set indexOfTouchedItem in (1,size-1)
            int indexOfTouchedItem = random.nextInt(size-2) + 1;
            //obtain ViewHolder below selected item
            COUIRecyclerView.ViewHolder expectedWinner = dropTargets.get(selectedIndex + 1);
            int curX = left;
            //ensure curY bigger than targetItem's midline in vertical
            int curY = (indexOfTouchedItem+1) * heigh;
            //invoke chooseDropTarget()
            COUIRecyclerView.ViewHolder winner = mCallback.chooseDropTarget(selected, dropTargets, curX, curY);
            System.out.println(indexOfTouchedItem + " : " + expectedWinner.itemView.getTop() + " : " +winner.itemView.getTop());
            //assert
            assertEquals(expectedWinner, winner);
        }
    }

    @Test
    public void should_return_viewholder_rightOf_selectedView_and_nearest_to_selectedView_when_chooseDropTarget_with_curX_in_view_rightOf_selectedView(){
        int heigh = 10;
        int width = 20;
        int size = 10;
        int top = 0;
        List<COUIRecyclerView.ViewHolder> dropTargets = new ArrayList(size);
        for(int i=0; i<size; i++){
            int left = i*width;
            int bottom = top + heigh;
            int right = left + width;
            View itemView = mock(View.class);
            when(itemView.getHeight()).thenReturn(heigh);
            when(itemView.getWidth()).thenReturn(width);
            when(itemView.getTop()).thenReturn(top);
            when(itemView.getLeft()).thenReturn(left);
            when(itemView.getBottom()).thenReturn(bottom);
            when(itemView.getRight()).thenReturn(right);
            COUIRecyclerView.ViewHolder holder = new TestViewHolder(itemView);
            dropTargets.add(holder);
        }
        int selectedIndex = 0;
        COUIRecyclerView.ViewHolder selected = dropTargets.get(selectedIndex);
        int repeat = 10;
        Random random = new Random();
        for(int i=0; i<repeat; i++){
            //set indexOfTouchedItem in (1,size-1)
            int indexOfTouchedItem = random.nextInt(size-2) + 1;
            //obtain ViewHolder below selected item
            COUIRecyclerView.ViewHolder expectedWinner = dropTargets.get(selectedIndex + 1);
            //ensure curX bigger than targetItem's Left
            int curX = (indexOfTouchedItem+1)*width;
            //set curY to view's midline
            int curY = heigh/2;
            //invoke chooseDropTarget()
            COUIRecyclerView.ViewHolder winner = mCallback.chooseDropTarget(selected, dropTargets, curX, curY);
            System.out.println(indexOfTouchedItem + " : " + expectedWinner.itemView.getTop() + " : " +winner.itemView.getTop());
            //assert
            assertEquals(expectedWinner, winner);
        }
    }


    @Test
    public void should_return_viewholder_leftOf_selectedView_and_nearest_to_selectedView_when_chooseDropTarget_with_curX_in_view_leftOf_selectedView(){
        int heigh = 10;
        int width = 20;
        int size = 10;
        int top = 0;
        List<COUIRecyclerView.ViewHolder> dropTargets = new ArrayList(size);
        for(int i=0; i<size; i++){
            int left = i*width;
            int bottom = top + heigh;
            int right = left + width;
            View itemView = mock(View.class);
            when(itemView.getHeight()).thenReturn(heigh);
            when(itemView.getWidth()).thenReturn(width);
            when(itemView.getTop()).thenReturn(top);
            when(itemView.getLeft()).thenReturn(left);
            when(itemView.getBottom()).thenReturn(bottom);
            when(itemView.getRight()).thenReturn(right);
            COUIRecyclerView.ViewHolder holder = new TestViewHolder(itemView);
            dropTargets.add(holder);
        }
        int selectedIndex = size - 1;
        COUIRecyclerView.ViewHolder selected = dropTargets.get(selectedIndex);
        int repeat = 10;
        Random random = new Random();
        for(int i=0; i<repeat; i++){
            //set indexOfTouchedItem in (0,size-2)
            int indexOfTouchedItem = random.nextInt(size-1);
            //obtain ViewHolder below selected item
            COUIRecyclerView.ViewHolder expectedWinner = dropTargets.get(selectedIndex - 1);
            //ensure curX smaller than targetItem's left
            int curX = (indexOfTouchedItem)*width-1;
            //set curY to itemView's midline in vertical
            int curY = heigh/2;
            //invoke chooseDropTarget()
            COUIRecyclerView.ViewHolder winner = mCallback.chooseDropTarget(selected, dropTargets, curX, curY);
            System.out.println(indexOfTouchedItem + " : " + expectedWinner.itemView.getTop() + " : " +winner.itemView.getTop());
            //assert
            assertEquals(expectedWinner, winner);
        }
    }


    class TestViewHolder extends COUIRecyclerView.ViewHolder{
        public TestViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }
}
