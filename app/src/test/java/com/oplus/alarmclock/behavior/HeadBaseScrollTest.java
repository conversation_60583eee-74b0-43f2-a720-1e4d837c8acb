/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-26, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.behavior;

import android.view.View;
import android.view.ViewGroup;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import com.google.android.material.appbar.AppBarLayout;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HeadBaseScrollTest extends TestParent {
    protected int mDividerWidthChangeInitY;
    private int mTitleAlphaChangeOffset;
    public int mListFirstChildEndY = 0;
    HeadBaseScroll mHeadBaseScroll;
    int mChildY;
    @Override
    public void setUp() throws Exception {
        super.setUp();
        mHeadBaseScroll = new HeadBaseScroll(mContext,null);
        ViewGroup scrollView = mock(ViewGroup.class);
        int childCount = 5;
        View child = mock(View.class);
        when(child.getVisibility()).thenReturn(View.INVISIBLE,View.VISIBLE,View.VISIBLE,View.INVISIBLE,View.VISIBLE);
        when(scrollView.getChildCount()).thenReturn(childCount);
        when(scrollView.getChildAt(anyInt())).thenReturn(child);
        Answer answer = new Answer(){
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = mChildY;
                return null;
            }
        };
        doAnswer(answer).when(child).getLocationOnScreen(any(int[].class));
//        mHeadBaseScroll.mScrollView = scrollView;
//        mHeadBaseScroll.mTextView = mock(View.class);
//        mHeadBaseScroll.mDividerParams = mock(ViewGroup.LayoutParams.class);
//        mHeadBaseScroll.mDividerLine = mock(View.class);
//        mHeadBaseScroll.mLargeTitleParams = mock(AppBarLayout.LayoutParams.class);
//        mHeadBaseScroll.mToolbar = mock(COUIToolbar.class);
    }

    @Test
    public void should_mCurrentOffset_equalsTo_offset_and_call_setAlpha_with_zero_when_onListScroll_with_mChildY_less_than_mListFirstChildEndY(){
//        mHeadBaseScroll.mListFirstChildInitY = 100;
//        mHeadBaseScroll.mToolbarChangeInitY = 90;
//        mHeadBaseScroll.mTitleMarginChangEndY = 70;
//        mHeadBaseScroll.mListFirstChildEndY = 40;
//
//        mChildY = mHeadBaseScroll.mListFirstChildEndY/2;
//        int offset = mDividerWidthChangeInitY - mListFirstChildEndY;
//        //invoke onListScroll()
//        mHeadBaseScroll.onListScroll();
//        //assert
//        assertEquals(offset, mHeadBaseScroll.mCurrentOffset);
    }

    @Test
    public void should__when_onListScroll_with_mChildY_less_than_mListFirstChildInitY_minus_mTitleAlphaChangeOffset(){
//        mHeadBaseScroll.mListFirstChildInitY = 100;
//        mHeadBaseScroll.mToolbarChangeInitY = 90;
//        mHeadBaseScroll.mTitleMarginChangEndY = 70;
//        mHeadBaseScroll.mListFirstChildEndY = 40;
//        mTitleAlphaChangeOffset = mContext.getResources().getDimensionPixelOffset(R.dimen.title_alpha_rang_min_count_height);
//        mChildY = mHeadBaseScroll.mListFirstChildInitY - mTitleAlphaChangeOffset;
//        int offset = mTitleAlphaChangeOffset;
//        //invoke onListScroll()
//        mHeadBaseScroll.onListScroll();
//        //assert && verify
//        assertEquals(offset, mHeadBaseScroll.mCurrentOffset);
//        verify(mHeadBaseScroll.mTextView).setAlpha(0);
    }
}
