/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-27, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.behavior;


import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;

import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Ignore;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import com.google.android.material.appbar.AppBarLayout;
import com.coui.appcompat.tablayout.COUITabLayout;
import com.oplus.alarmclock.AlarmClock;
import com.oplus.alarmclock.BaseFragment;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SmallTabBehaviorTest extends TestParent {
    @Test
    @Ignore
    public void should_mTabLayout_equalTo_colorTabLayout_and_mHandleScroll_is_true_when_onStartNestedScroll_with_mCurrentOffset_isnot_zero_and_mTabLayout_is_null() throws NoSuchFieldException, IllegalAccessException {
        SmallTabBehavior behavior = new SmallTabBehavior();
        int axes = ViewCompat.SCROLL_AXIS_VERTICAL;
        AppBarLayout colorAppBarLayout = mock(AppBarLayout.class);
        int childCount = 1;
        when(colorAppBarLayout.getChildCount()).thenReturn(childCount);
        FrameLayout frameLayout = mock(FrameLayout.class);
        when(colorAppBarLayout.getChildAt(anyInt())).thenReturn(frameLayout);
        when(frameLayout.getChildCount()).thenReturn(childCount);
        COUITabLayout colorTabLayout = mock(COUITabLayout.class);
        when(frameLayout.getChildAt(anyInt())).thenReturn(colorTabLayout);
        //ensure !mTabLayout.isEnabled() unsatified
        boolean enable = true;
        when(colorTabLayout.isEnabled()).thenReturn(enable);
        CoordinatorLayout parent = mock(CoordinatorLayout.class);
        ViewPager2 mViewPager = mock(ViewPager2.class);
        AlarmClock.LocalFragmentPagerAdapter adapter = mock(AlarmClock.LocalFragmentPagerAdapter.class);
        when(adapter.getCurrentFragment()).thenReturn(mock(BaseFragment.class));
        when(mViewPager.getAdapter()).thenReturn(adapter);
        when(parent.findViewById(R.id.view_pager)).thenReturn(mViewPager);
        View directTargetChild = mock(View.class);
        View target = mock(View.class);
        int type = 1;
        //ensure mCurrentOffset != 0
        int currentOffset = 1;
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mCurrentOffset", behavior, currentOffset);
        //invoke onStartNestedScroll()
        behavior.onStartNestedScroll(parent, colorAppBarLayout, directTargetChild, target, axes, type);
        //assert
        Object obj = ReflectUtil.getFieldValue(SmallTabBehavior.class, "mTabLayout", behavior);
        assertEquals(colorTabLayout, obj);
        assertTrue((boolean) ReflectUtil.getFieldValue(SmallTabBehavior.class, "mHandleScroll", behavior));
    }

    @Test
    public void should_mDividerWidthRange_is_one_when_onListScroll_with_y_lessThan_mDividerWidthChangeInitY_and_mDividerWidthChangeEndY() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        SmallTabBehavior behavior = new SmallTabBehavior();
        ViewGroup scrollView = mock(ViewGroup.class);
        int childCount = 3;
        when(scrollView.getChildCount()).thenReturn(childCount);
        View view = mock(View.class);
        when(scrollView.getChildAt(anyInt())).thenReturn(view);
        when(view.getVisibility()).thenReturn(View.VISIBLE, View.VISIBLE, View.INVISIBLE);
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mScrollView", behavior, scrollView);
        int mDividerWidthChangeEndY = 100;
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mDividerWidthChangeEndY", behavior, mDividerWidthChangeEndY);
        //ensure y < mDividerWidthChangeEndY
        final int y = mDividerWidthChangeEndY - 1;
        Answer answer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(view).getLocationOnScreen(any(int[].class));
        //ensure mDividerWidthChangeInitY less than y
        int mDividerWidthChangeInitY = y + 1;
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mDividerWidthChangeInitY", behavior, mDividerWidthChangeInitY);
        CoordinatorLayout.LayoutParams mDividerParams = mock(CoordinatorLayout.LayoutParams.class);
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mDividerParams", behavior, mDividerParams);
        View mDividerLine = mock(View.class);
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mDividerLine", behavior, mDividerLine);
        int mDividerWidthChangeOffset = 1;
        ReflectUtil.setFieldValue(SmallTabBehavior.class, "mDividerWidthChangeOffset", behavior, mDividerWidthChangeOffset);
        //invoke onListScroll()
        ReflectUtil.invoke(SmallTabBehavior.class, "onListScroll", null, behavior);
        //assert
        float mDividerWidthRange = (float) ReflectUtil.getFieldValue(SmallTabBehavior.class, "mDividerWidthRange", behavior);
        System.out.println(mDividerWidthRange);
        assertEquals(1f, mDividerWidthRange, 0);
    }

    @Test
    public void should_returnValue_equalTo_y_when_getViewPositionY_with_visibleChild_yPosition_is_y() throws NoSuchMethodException, IllegalAccessException {
        SmallTabBehavior behavior = new SmallTabBehavior();
        ViewGroup viewGroup = mock(ViewGroup.class);
        int childCount = 3;
        when(viewGroup.getChildCount()).thenReturn(childCount);
        final View[] children = new View[childCount];
        int visibleViewIndex = childCount - 1;
        for(int i=0; i<childCount; i++){
            children[i] = mock(View.class);
            if(i == visibleViewIndex){
                when(children[i].getVisibility()).thenReturn(View.VISIBLE);
            }else {
                when(children[i].getVisibility()).thenReturn(View.INVISIBLE);
            }
        }
        final int y = 100;
        Answer answer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(children[visibleViewIndex]).getLocationOnScreen(any(int[].class));
        Answer<View> answer1 = new Answer() {
            @Override
            public View answer(InvocationOnMock invocation) throws Throwable {
                int index = invocation.getArgument(0);
                return children[index];
            }
        };
        when(viewGroup.getChildAt(anyInt())).thenAnswer(answer1);
        //invoke
        int positionY = (int) ReflectUtil.invoke(SmallTabBehavior.class, "getViewPositionY",
                new Object[]{viewGroup}, behavior, View.class);
        //assert
        assertEquals(y, positionY);
    }


    @Test
    public void should_returnValue_equalTo_y_when_getViewPositionY_with_no_visibleChild_in_viewGroup() throws NoSuchMethodException, IllegalAccessException {
        SmallTabBehavior behavior = new SmallTabBehavior();
        ViewGroup viewGroup = mock(ViewGroup.class);
        int childCount = 3;
        when(viewGroup.getChildCount()).thenReturn(childCount);
        final View[] children = new View[childCount];
        for(int i=0; i<childCount; i++){
            children[i] = mock(View.class);
            when(children[i].getVisibility()).thenReturn(View.INVISIBLE);
        }
        final int y = 100;
        Answer answer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(viewGroup).getLocationOnScreen(any(int[].class));
        Answer<View> answer1 = new Answer() {
            @Override
            public View answer(InvocationOnMock invocation) throws Throwable {
                int index = invocation.getArgument(0);
                return children[index];
            }
        };
        when(viewGroup.getChildAt(anyInt())).thenAnswer(answer1);
        //invoke
        int positionY = (int) ReflectUtil.invoke(SmallTabBehavior.class, "getViewPositionY",
                new Object[]{viewGroup}, behavior, View.class);
        //assert
        assertEquals(y, positionY);
    }
}
