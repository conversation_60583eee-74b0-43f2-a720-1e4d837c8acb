/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-25, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.behavior;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.searchview.COUISearchBar;
import com.coui.appcompat.searchview.COUISearchView;
import com.coui.appcompat.searchview.COUISearchViewAnimate;
import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.facebook.rebound.Spring;

import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import com.google.android.material.appbar.AppBarLayout;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HeadScaleWithSearchBhvTest extends TestParent {
    RecyclerView.OnScrollListener mOnScrollListener;
    @Test
    public void should_call_setEndValue_with_endValue_when_onScrollStateChanged_with_newState_is_SCROLL_STATE_IDLE_and_y_lessThan_mListFirstChildInitY_and_mFlagListScroll_is_true()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        //obtain onScrollStateChanged
        HeadScaleWithSearchBhv bhv = new HeadScaleWithSearchBhv();
        ReflectUtil.invoke(HeadScaleWithSearchBhv.class, "init",new Object[]{mContext}, bhv, Context.class);
        //ensure started is true
        CoordinatorLayout parent = mock(CoordinatorLayout.class);
        //mAppBarLayout = child
        AppBarLayout child = mock(AppBarLayout.class);
        when(child.getHeight()).thenReturn(Integer.MAX_VALUE);
        int nestedScrollAxes = ViewCompat.SCROLL_AXIS_VERTICAL;
        View directTargetChild = mock(View.class);
        //mScrollView = target
        COUIRecyclerView target = mock(COUIRecyclerView.class);
        View view = mock(View.class);
        when(target.getChildAt(anyInt())).thenReturn(view);
        int type = 1;
        COUISearchBar mGlobalSearchView =  mock(COUISearchBar.class);
        EditText mSearchViewText= mock(EditText.class);
        when(mGlobalSearchView.getSearchEditText()).thenReturn(mSearchViewText);
        when(mGlobalSearchView.getPaddingStart()).thenReturn(1);
        when(child.findViewById(R.id.globalSearchView)).thenReturn(mGlobalSearchView);
        Answer mScrollViewAnswer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                mOnScrollListener = invocation.getArgument(0);
                return null;
            }
        };
        doAnswer(mScrollViewAnswer).when(target).addOnScrollListener(any(RecyclerView.OnScrollListener.class));
        //invoke onStartNestedScroll()
        bhv.onStartNestedScroll(parent, child, directTargetChild, target, nestedScrollAxes, type);
        int newState = COUIRecyclerView.SCROLL_STATE_IDLE;
        boolean mFlagListScroll = true;
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mFlagListScroll", bhv, mFlagListScroll);
        int mListFirstChildInitY = 100;
        //ensure mLocation[1] < mListFirstChildInitY
        final int y = mListFirstChildInitY-1;
        //ensure mLocation[1] > (mListFirstChildInitY - mStandardScroll / 2)
        int mStandardScroll = (mListFirstChildInitY - y) * 2 + 10;
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mListFirstChildInitY", bhv, mListFirstChildInitY);
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mStandardScroll", bhv, mStandardScroll);
        Answer answer = new Answer(){
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(view).getLocationOnScreen(any(int[].class));
        Spring spring = mock(Spring.class);
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mSpring", bhv, spring);
        RecyclerView recyclerView = mock(RecyclerView.class);
        //invoke onScrollStateChanged()
        mOnScrollListener.onScrollStateChanged(recyclerView, newState);
        //assert
        int endValue = y - mListFirstChildInitY - mContext.getResources().getDimensionPixelOffset(R.dimen.list_to_ex_top_padding);
        verify(spring).setEndValue(endValue);

    }


    @Test
    public void should_call_setEndValue_with_endValue_when_onScrollStateChanged_with_newState_is_SCROLL_STATE_IDLE_and_y_lessThan_mListFirstChildInitY_and_mTitleMarginChangeEndY_lessThan_y()
            throws NoSuchMethodException, IllegalAccessException, NoSuchFieldException {
        //obtain onScrollStateChanged
        HeadScaleWithSearchBhv bhv = new HeadScaleWithSearchBhv();
        ReflectUtil.invoke(HeadScaleWithSearchBhv.class, "init",new Object[]{mContext}, bhv, Context.class);
        //ensure started is true
        CoordinatorLayout parent = mock(CoordinatorLayout.class);
        //mAppBarLayout = child
        AppBarLayout child = mock(AppBarLayout.class);
        when(child.getHeight()).thenReturn(Integer.MAX_VALUE);
        int nestedScrollAxes = ViewCompat.SCROLL_AXIS_VERTICAL;
        View directTargetChild = mock(View.class);
        //mScrollView = target
        COUIRecyclerView target = mock(COUIRecyclerView.class);
        View view = mock(View.class);
        when(target.getChildAt(anyInt())).thenReturn(view);
        int type = 1;
        COUISearchBar mGlobalSearchView =  mock(COUISearchBar.class);
        EditText mSearchViewText = mock(EditText.class);
        when(mGlobalSearchView.getSearchEditText()).thenReturn(mSearchViewText);
        when(mGlobalSearchView.getPaddingStart()).thenReturn(1);
        when(child.findViewById(R.id.globalSearchView)).thenReturn(mGlobalSearchView);
        Answer mScrollViewAnswer = new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                mOnScrollListener = invocation.getArgument(0);
                return null;
            }
        };
        doAnswer(mScrollViewAnswer).when(target).addOnScrollListener(any(RecyclerView.OnScrollListener.class));
        //invoke onStartNestedScroll()
        bhv.onStartNestedScroll(parent, child, directTargetChild, target, nestedScrollAxes, type);
        int newState = COUIRecyclerView.SCROLL_STATE_IDLE;
        boolean mFlagListScroll = true;
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mFlagListScroll", bhv, mFlagListScroll);
        int mListFirstChildInitY = 100;
        //ensure mLocation[1] < mListFirstChildInitY
        final int y = mListFirstChildInitY-1;
        //ensure mLocation[1] < (mListFirstChildInitY - mStandardScroll / 2)
        int mStandardScroll = (mListFirstChildInitY - y) * 2 - 10;
        //ensure mLocation[1] > mTitleMarginChangeEndY
        int mTitleMarginChangeEndY = y - 1;
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mTitleMarginChangeEndY", bhv, mTitleMarginChangeEndY);
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mListFirstChildInitY", bhv, mListFirstChildInitY);
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mStandardScroll", bhv, mStandardScroll);
        Answer answer = new Answer(){
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(view).getLocationOnScreen(any(int[].class));
        Spring spring = mock(Spring.class);
        ReflectUtil.setFieldValue(HeadScaleWithSearchBhv.class, "mSpring", bhv, spring);
        RecyclerView recyclerView = mock(RecyclerView.class);
        //invoke onScrollStateChanged()
        mOnScrollListener.onScrollStateChanged(recyclerView, newState);
        //assert
        int endValue = y - mTitleMarginChangeEndY - mContext.getResources().getDimensionPixelOffset(R.dimen.list_to_ex_top_padding);
        verify(spring).setEndValue(endValue);
    }

    @Test
    public void should_mAppBarLayout_equalTo_child_when_onStartNestedScroll_with_nestedScrollAxes_is_SCROLL_AXIS_VERTICAL_and_hasScaleableChildren_return_true()
            throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        HeadScaleWithSearchBhv bhv = new HeadScaleWithSearchBhv();
        ReflectUtil.invoke(HeadScaleWithSearchBhv.class, "init",new Object[]{mContext}, bhv, Context.class);
        //ensure started is true
        CoordinatorLayout parent = mock(CoordinatorLayout.class);
        //mAppBarLayout = child
        AppBarLayout child = mock(AppBarLayout.class);
        when(child.getHeight()).thenReturn(Integer.MAX_VALUE);
        int nestedScrollAxes = ViewCompat.SCROLL_AXIS_VERTICAL;
        View directTargetChild = mock(View.class);
        //mScrollView = target
        COUIRecyclerView target = mock(COUIRecyclerView.class);
        int type = 1;

        COUISearchBar mGlobalSearchView =  mock(COUISearchBar.class);
        EditText mSearchViewText= mock(EditText.class);
        when(mGlobalSearchView.getSearchEditText()).thenReturn(mSearchViewText);
        when(child.findViewById(R.id.globalSearchView)).thenReturn(mGlobalSearchView);
        //ensure getChildCount > 1
        int childCount = 2;
        when(target.getChildCount()).thenReturn(childCount);
        View view = mock(View.class);
        final int y = 100;
        Answer answer = new Answer(){
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                int[] location = invocation.getArgument(0);
                location[1] = y;
                return null;
            }
        };
        doAnswer(answer).when(view).getLocationOnScreen(any(int[].class));
        when(target.getChildAt(1)).thenReturn(view);
        //invoke onStartNestedScroll()
        bhv.onStartNestedScroll(parent, child, directTargetChild, target, nestedScrollAxes, type);
        //assert
        assertEquals(child, ReflectUtil.getFieldValue(HeadScaleWithSearchBhv.class, "mAppBarLayout", bhv));
    }




}
