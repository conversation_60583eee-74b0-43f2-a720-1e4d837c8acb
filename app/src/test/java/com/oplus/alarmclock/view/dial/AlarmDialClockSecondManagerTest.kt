/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - AlarmDialClockManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view.dial

import android.graphics.Canvas
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import org.junit.Test

class AlarmDialClockSecondManagerTest : TestParent() {

    @Test
    fun test_alarm_dial_clock_manager() {
        val dialGlow = AlarmDialClockGlowTable(mContext)
        runClock(AlarmDialClockGlowTable::class.java, dialGlow, true)
        val dial = AlarmDialClockSecond(mContext)
        runClock(AlarmDialClockSecond::class.java, dial, true)
        val dialHour = AlarmDialClockHour(mContext)
        runClock(AlarmDialClockHour::class.java, dialHour, false)
        val dialMinute = AlarmDialClockMinute(mContext)
        runClock(AlarmDialClockMinute::class.java, dialMinute, false)
        val text = AlarmDialClockTextView(mContext)
        val mManager = AlarmDialClockManager()
        mManager.init(dialGlow, dial, dialHour, dialMinute, text)
        mManager.start()
    }

    private fun <T> runClock(cls: Class<T>, t: T, haveOnMeasure: Boolean) {
        if (haveOnMeasure) {
            ReflectUtil.invoke(cls,
                "onMeasure",
                arrayOf(0, 0),
                t,
                Int::class.java,
                Int::class.java)
        }
        ReflectUtil.invoke(cls,
            "onSizeChanged",
            arrayOf(0, 0, 0, 0),
            t,
            Int::class.java,
            Int::class.java,
            Int::class.java,
            Int::class.java)
        ReflectUtil.invoke(cls,
            "onDraw",
            arrayOf(Canvas()),
            t,
            Canvas::class.java
        )
    }
}