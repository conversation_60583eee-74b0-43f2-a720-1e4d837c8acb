/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WorldClockAnimationManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view.dial

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.globalclock.view.ScrollLinearLayoutManager
import com.oplus.alarmclock.utils.PrefUtils
import org.junit.Assert
import org.junit.Test
import org.mockito.Mockito.mock

class WorldClockAnimationManagerTest : TestParent() {
    companion object {
        private const val SP_NAME = "alarm_dial_clock"
        private const val IS_TEXT_MODEL = "is_text_model"
    }

    private lateinit var mManager: WorldClockAnimationManager

    override fun setUp() {
        super.setUp()
        mManager = WorldClockAnimationManager()
    }

    @Test
    fun should_return_visible_when_is_text_is_ture_with_is_text() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, true)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, false)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        val isVisibility = dialClockRl.visibility == View.VISIBLE
        Assert.assertTrue(isVisibility)
    }

    @Test
    fun should_return_true_when_animation_finish_with_is_text() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = RecyclerView(mContext)
        val manager = ScrollLinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        rv.layoutManager = manager
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, false)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        mManager.startDialChangeAnimation(0)
        Assert.assertTrue(mManager.mIsText)
    }

    @Test
    fun should_return_true_when_animation_finish() {
        val dial = mock(View::class.java)
        val rv = mock(View::class.java)
        mManager.startDialMarginAnimation(dial, rv, false)
        mManager.startDialMarginAnimation(dial, rv, true)
        val view = View(mContext).apply {
            layoutParams = ViewGroup.MarginLayoutParams(0, 0)
        }
        mManager.setDialLayout(view, true)
        mManager.setDialLayout(view, false)
        Assert.assertTrue(mManager.mCurrentIsCenter)
    }

    @Test
    fun should_return_visible_changeTextModele_with_is_text() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, false)
        mManager.changeTextModel()
        mManager.setIsHover(true)
        Assert.assertTrue(mManager.misHover)
    }

    @Test
    fun should_return_visible_change_Normal_with_is_text_flase() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, true)
        mManager.changeToNormal()
        mManager.setIsHover(true)
        Assert.assertTrue(mManager.misHover)
    }

    @Test
    fun should_return_visible_change_Normal_with_is_text_true() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, false)
        mManager.changeToNormal()
        val isVisibility = dialMsgTv.visibility == View.INVISIBLE
        Assert.assertTrue(!isVisibility)
    }

    @Test
    fun should_return_visible_change_setIsToCenter_true() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, true)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        mManager.setIsToCenter(true)
        Assert.assertTrue(mManager.mCurrentIsCenter)
    }

    @Test
    @Suppress("LongMethod", "ArrayPrimitive")
    fun test_start_dial_animation() {
        val dialClockRl = mock(View::class.java)
        val dialMsgTv = mock(View::class.java)
        val dialWordTimeTv = mock(View::class.java)
        val dialWordMsgTv = mock(View::class.java)
        val rv = mock(RecyclerView::class.java)
        PrefUtils.putBoolean(mContext, SP_NAME, IS_TEXT_MODEL, true)
        mManager.initDialClock(dialClockRl, dialMsgTv, dialWordTimeTv, dialWordMsgTv, rv)
        mManager.setIsToCenter(true)
        mManager.startDialMarginAnimation(dialClockRl, rv, false)
        Assert.assertTrue(!mManager.mCurrentIsCenter)
    }
}