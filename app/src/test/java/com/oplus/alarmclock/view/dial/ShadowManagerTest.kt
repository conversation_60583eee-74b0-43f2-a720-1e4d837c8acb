/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ShadowManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/2/17     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.view.dial

import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.view.water.WaterClockView
import org.junit.Test

class ShadowManagerTest : TestParent() {

    @Test
    fun test_shadow_manager() {
        val dial = AlarmDialClockTable(mContext)
        val dialGlow = AlarmDialClockGlowTable(mContext)
        dialGlow.setData(0)
        val dialSecond = AlarmDialClockSecond(mContext)
        dialSecond.setData(0, 0)
        val dialHour = AlarmDialClockHour(mContext)
        dialHour.setData(0, 0)
        val dialMinute = AlarmDialClockMinute(mContext)
        dialMinute.setData(0, 0)
        val shadow = WaterClockView(mContext)
        val clockManager = WorldClockAnimationManager()
        val mManager = ShadowManager()
        mManager.init(dial, dialGlow, dialHour, dialMinute, dialSecond, shadow, clockManager)
        clockManager.mIsText = true
        val manager = AlarmDialClockManager()
        mManager.startAnimation(manager)
    }
}