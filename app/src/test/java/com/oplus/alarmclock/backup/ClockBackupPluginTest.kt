/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ClockBackupPluginTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
@file:Suppress("VarCouldBeVal", "NoUnusedImports", "MaximumLineLength", "TooGenericExceptionThrown")
package com.oplus.alarmclock.backup

import android.content.Context
import android.database.Cursor
import com.oplus.alarmclock.TestParent
import android.os.Bundle
import android.provider.Settings
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.alarmclock.backup.ClockBackupPlugin
import org.mockito.Mockito
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.alarmclock.backup.ClockBackupPluginTest
import org.mockito.invocation.InvocationOnMock
import org.mockito.ArgumentMatchers
import com.oplus.alarmclock.utils.AlarmRingUtils
import com.oplus.alarmclock.shadows.utils.ShadowUtil
import com.oplus.alarmclock.shadows.ShadowUserHandleNative
import org.junit.Assert
import org.junit.BeforeClass
import org.junit.Test
import org.mockito.stubbing.Answer
import org.robolectric.annotation.Config
import org.robolectric.annotation.Implementation
import org.robolectric.annotation.Implements
import java.io.File
import java.io.IOException
import java.lang.Exception
import java.lang.RuntimeException
import java.util.*

class ClockBackupPluginTest : TestParent() {
    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_create_CLOCK_XML_in_rootPath_when_onStart_with_rootPath_and_file_not_exists() {
        val bundle = Bundle()
        val rootPath = "."
        val keyRootPath = "backup_root_path"
        //ensure config.getBackupRootPath() return rootPath
        bundle.putString(keyRootPath, rootPath)
        bundle.putBundle("config", bundle)
        val brEngineConfig = BREngineConfig(bundle)
        val plugin = ClockBackupPlugin()
        plugin.onCreate(mContext, Mockito.mock(BRPluginHandler::class.java), brEngineConfig)
        val targetPath = rootPath + File.separator + "Clock"
        val file = File(targetPath)
        //ensure file not exists
        if (file.exists()) {
            if (file.isFile) {
                file.delete()
            } else {
                val files = file.listFiles()
                for (f in files) {
                    f.delete()
                }
                file.delete()
            }
        }
        val file2 = File(file.absolutePath + File.separator + ClockBackupPlugin.CLOCK_XML)
        if (file2.exists()) {
            file2.delete()
        }
        //invoke onStart()
        ReflectUtil.invoke(ClockBackupPlugin::class.java, "onStart", arrayOf(), plugin)
        //assert
        Assert.assertTrue(file.exists())
        Assert.assertTrue(file2.exists())
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_mClockList_size_equalTo_alarmCount_when_onBackup_with_mCursorArray_ALARM_CLOCK_count_and_mMaxCount_are_alarmCount() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set alarmCursor count and mMaxCount
        val alarmCount = 2
        val cursorArray = arrayOfNulls<Cursor>(4)
        val alarmCursor = Mockito.mock(Cursor::class.java)
        cursorArray[ALARM_CLOCK] = alarmCursor
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, alarmCount)
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        val alarmAnswer: Answer<*> = object : Answer<Any?> {
            var count = alarmCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "isNull" -> return false
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(alarmCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.moveToNext()).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.isAfterLast).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.isNull(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)
        //invoke onBackup()
        plugin.onBackup(null)
        //assert
        val mClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(alarmCount.toLong(), mClockList.size.toLong())
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_mWorldClockList_size_equalTo_cityCount_when_onBackup_with_mCursorArray_WORLD_CLOCK_count_and_mMaxCount_are_cityCount() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set cityCursor count and mMaxCount
        val cityCount = 2
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, cityCount)
        val cursorArray = arrayOfNulls<Cursor>(4)
        //init cityCursor
        val cityCursor = Mockito.mock(Cursor::class.java)
        cursorArray[WORLD_CLOCK] = cityCursor
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        val cityAnswer: Answer<*> = object : Answer<Any?> {
            var count = cityCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(cityCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.moveToNext()).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.isAfterLast).thenAnswer(cityAnswer)
        //invoke onBackup()
        plugin.onBackup(null)
        //assert
        val mWorldClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mWorldClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(cityCount.toLong(), mWorldClockList.size.toLong())
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_mTimerList_size_equalTo_timerCount_when_onBackup_with_mCursorArray_TIMER_count_and_mMaxCount_are_timerCount() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set timerCursor count and mMaxCount
        val timerCount = 2
        val cursorArray = arrayOfNulls<Cursor>(4)
        val timerCursor = Mockito.mock(Cursor::class.java)
        //init cursorArray[TIMER]
        cursorArray[TIMER] = timerCursor
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, timerCount)
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        val timerAnswer: Answer<*> = object : Answer<Any?> {
            var count = timerCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "getLong" -> return 1L
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(timerCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getLong(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.moveToNext()).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.isAfterLast).thenAnswer(timerAnswer)
        plugin.onBackup(null)
        //assert
        val mTimerList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mTimerList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(timerCount.toLong(), mTimerList.size.toLong())
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_mAlarmRepeatList_size_equalTo_repeatCount_when_onBackup_with_mCursorArray_REPEAT_INFO_count_and_mMaxCount_are_repeatCount() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set repeatCursor count and mMaxCount
        val repeatCount = 2
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, repeatCount)
        //init mCursorArray[REPEAT_INFO]
        val cursorArray = arrayOfNulls<Cursor>(4)
        val repeatCursor = Mockito.mock(Cursor::class.java)
        cursorArray[REPEAT_INFO] = repeatCursor
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        val repeatAnswer: Answer<*> = object : Answer<Any?> {
            var count = repeatCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getInt" -> return 1
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(repeatCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(repeatAnswer)
        Mockito.`when`(repeatCursor.moveToNext()).thenAnswer(repeatAnswer)
        Mockito.`when`(repeatCursor.isAfterLast).thenAnswer(repeatAnswer)
        //invoke onBackup()
        plugin.onBackup(null)
        //assert
        val mAlarmRepeatList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mAlarmRepeatList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(repeatCount.toLong(), mAlarmRepeatList.size.toLong())
    }

    @Test
    @Suppress("LongMethod")
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_list_count_equalTo_cursor_count_when_onBackup_with_cursor_are_not_null_and_mMaxCount_equalTo_sum_of_all_cursors_count() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set cursor count and mMaxCount
        val rand = Random()
        val alarmCount = rand.nextInt(10) + 1
        val cityCount = rand.nextInt(10) + 1
        val timerCount = rand.nextInt(10) + 1
        val repeatCount = rand.nextInt(10) + 1
        val cursorArray = arrayOfNulls<Cursor>(4)
        val alarmCursor = Mockito.mock(Cursor::class.java)
        val cityCursor = Mockito.mock(Cursor::class.java)
        val timerCursor = Mockito.mock(Cursor::class.java)
        val repeatCursor = Mockito.mock(Cursor::class.java)
        cursorArray[ALARM_CLOCK] = alarmCursor
        cursorArray[WORLD_CLOCK] = cityCursor
        cursorArray[TIMER] = timerCursor
        cursorArray[REPEAT_INFO] = repeatCursor
        val maxCount = alarmCount + cityCount + timerCount + repeatCount
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, maxCount)
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        //init alarmCursor
        val alarmAnswer: Answer<*> = object : Answer<Any?> {
            var count = alarmCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "isNull" -> return false
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(alarmCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.moveToNext()).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.isAfterLast).thenAnswer(alarmAnswer)
        Mockito.`when`(alarmCursor.isNull(ArgumentMatchers.anyInt())).thenAnswer(alarmAnswer)

        //init cityCursor
        val cityAnswer: Answer<*> = object : Answer<Any?> {
            var count = cityCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(cityCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.moveToNext()).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.isAfterLast).thenAnswer(cityAnswer)

        //init timerCursor
        val timerAnswer: Answer<*> = object : Answer<Any?> {
            var count = timerCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "getLong" -> return 1L
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(timerCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getLong(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.moveToNext()).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.isAfterLast).thenAnswer(timerAnswer)

        //init repeatCursor
        val repeatAnswer: Answer<*> = object : Answer<Any?> {
            var count = repeatCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getInt" -> return 1
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(repeatCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(repeatAnswer)
        Mockito.`when`(repeatCursor.moveToNext()).thenAnswer(repeatAnswer)
        Mockito.`when`(repeatCursor.isAfterLast).thenAnswer(repeatAnswer)

        //invoke onBackup()
        plugin.onBackup(null)
        //assert
        val mClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(alarmCount.toLong(), mClockList.size.toLong())
        val mWorldClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mWorldClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(cityCount.toLong(), mWorldClockList.size.toLong())
        val mTimerList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mTimerList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(timerCount.toLong(), mTimerList.size.toLong())
        val mAlarmRepeatList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mAlarmRepeatList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(repeatCount.toLong(), mAlarmRepeatList.size.toLong())
    }

    @Test
    @Suppress("LongMethod")
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_list_count_equalTo_cursor_count_when_onBackup_with_some_cursor_are_null_and_mMaxCount_equalTo_sum_of_all_cursors_count() {
        val plugin = ClockBackupPlugin()
        //init ClockBackupPlugin
        plugin.onCreate(
            Mockito.mock(Context::class.java), Mockito.mock(
                BRPluginHandler::class.java
            ), Mockito.mock(BREngineConfig::class.java)
        )
        //set cursor count and mMaxCount
        val rand = Random()
        val alarmCount = 0
        val cityCount = rand.nextInt(10) + 1
        val timerCount = rand.nextInt(10) + 1
        val repeatCount = 0
        val cursorArray = arrayOfNulls<Cursor>(4)
        val alarmCursor: Cursor? = null
        val cityCursor = Mockito.mock(Cursor::class.java)
        val timerCursor = Mockito.mock(Cursor::class.java)
        val repeatCursor: Cursor? = null
        cursorArray[ALARM_CLOCK] = alarmCursor
        cursorArray[WORLD_CLOCK] = cityCursor
        cursorArray[TIMER] = timerCursor
        cursorArray[REPEAT_INFO] = repeatCursor
        val maxCount = alarmCount + cityCount + timerCount + repeatCount
        ReflectUtil.setFieldValue(ClockBackupPlugin::class.java, "mMaxCount", plugin, maxCount)
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )

        //init cityCursor
        val cityAnswer: Answer<*> = object : Answer<Any?> {
            var count = cityCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(cityCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.moveToNext()).thenAnswer(cityAnswer)
        Mockito.`when`(cityCursor.isAfterLast).thenAnswer(cityAnswer)

        //init timerCursor
        val timerAnswer: Answer<*> = object : Answer<Any?> {
            var count = timerCount
            var pos = 0
            @Throws(Throwable::class)
            override fun answer(invocation: InvocationOnMock): Any? {
                val method = invocation.method.name
                when (method) {
                    "getString" -> return "string"
                    "getInt" -> return 1
                    "getLong" -> return 1L
                    "moveToNext" -> pos++
                    "isAfterLast" -> return pos >= count
                }
                return null
            }
        }
        Mockito.`when`(timerCursor.getString(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.getLong(ArgumentMatchers.anyInt())).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.moveToNext()).thenAnswer(timerAnswer)
        Mockito.`when`(timerCursor.isAfterLast).thenAnswer(timerAnswer)

        //invoke onBackup()
        plugin.onBackup(null)
        //assert
        val mClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(alarmCount.toLong(), mClockList.size.toLong())
        val mWorldClockList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mWorldClockList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(cityCount.toLong(), mWorldClockList.size.toLong())
        val mTimerList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mTimerList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(timerCount.toLong(), mTimerList.size.toLong())
        val mAlarmRepeatList = ReflectUtil.getFieldValue(
            ClockBackupPlugin::class.java,
            "mAlarmRepeatList", plugin
        ) as ArrayList<Alarm>
        Assert.assertEquals(repeatCount.toLong(), mAlarmRepeatList.size.toLong())
    }

    @Test
    @Suppress("LongMethod")
    fun should_list_count_equalTo_cursor_count_and_finish_in_four_sec_when_onBackup_with_cursor_are_not_null_and_mMaxCount_bigger_sum_of_all_cursors_count() {
        val currentThread = Thread.currentThread()
        val runnable = Runnable {
            val plugin = ClockBackupPlugin()
            //init ClockBackupPlugin
            plugin.onCreate(
                Mockito.mock(Context::class.java), Mockito.mock(
                    BRPluginHandler::class.java
                ), Mockito.mock(BREngineConfig::class.java)
            )
            //set cursor count and mMaxCount
            val rand = Random()
            val alarmCount = rand.nextInt(10) + 1
            val cityCount = rand.nextInt(10) + 1
            val timerCount = rand.nextInt(10) + 1
            val repeatCount = rand.nextInt(10) + 1
            val cursorArray = arrayOfNulls<Cursor>(4)
            val alarmCursor = Mockito.mock(
                Cursor::class.java
            )
            val cityCursor = Mockito.mock(
                Cursor::class.java
            )
            val timerCursor = Mockito.mock(
                Cursor::class.java
            )
            val repeatCursor = Mockito.mock(
                Cursor::class.java
            )
            cursorArray[ALARM_CLOCK] = alarmCursor
            cursorArray[WORLD_CLOCK] = cityCursor
            cursorArray[TIMER] = timerCursor
            cursorArray[REPEAT_INFO] = repeatCursor
            try {
                //ensure maxCount bigger than the sum of all cursors' count
                val maxCount = alarmCount + cityCount + timerCount + repeatCount + 1
                ReflectUtil.setFieldValue(
                    ClockBackupPlugin::class.java,
                    "mMaxCount",
                    plugin,
                    maxCount
                )
                ReflectUtil.setFieldValue(
                    ClockBackupPlugin::class.java,
                    "mCursorArray",
                    plugin,
                    cursorArray
                )
                //init alarmCursor
                val alarmAnswer: Answer<*> = object : Answer<Any?> {
                    var count = alarmCount
                    var pos = 0
                    @Throws(Throwable::class)
                    override fun answer(invocation: InvocationOnMock): Any? {
                        val method = invocation.method.name
                        when (method) {
                            "getString" -> return "string"
                            "getInt" -> return 1
                            "isNull" -> return false
                            "moveToNext" -> pos++
                            "isAfterLast" -> return pos >= count
                        }
                        return null
                    }
                }
                Mockito.`when`(alarmCursor.getString(ArgumentMatchers.anyInt()))
                    .thenAnswer(alarmAnswer)
                Mockito.`when`(alarmCursor.getInt(ArgumentMatchers.anyInt()))
                    .thenAnswer(alarmAnswer)
                Mockito.`when`(alarmCursor.moveToNext()).thenAnswer(alarmAnswer)
                Mockito.`when`(alarmCursor.isAfterLast).thenAnswer(alarmAnswer)
                Mockito.`when`(alarmCursor.isNull(ArgumentMatchers.anyInt()))
                    .thenAnswer(alarmAnswer)

                //init cityCursor
                val cityAnswer: Answer<*> = object : Answer<Any?> {
                    var count = cityCount
                    var pos = 0
                    @Throws(Throwable::class)
                    override fun answer(invocation: InvocationOnMock): Any? {
                        val method = invocation.method.name
                        when (method) {
                            "getString" -> return "string"
                            "getInt" -> return 1
                            "moveToNext" -> pos++
                            "isAfterLast" -> return pos >= count
                        }
                        return null
                    }
                }
                Mockito.`when`(cityCursor.getString(ArgumentMatchers.anyInt()))
                    .thenAnswer(cityAnswer)
                Mockito.`when`(cityCursor.getInt(ArgumentMatchers.anyInt())).thenAnswer(cityAnswer)
                Mockito.`when`(cityCursor.moveToNext()).thenAnswer(cityAnswer)
                Mockito.`when`(cityCursor.isAfterLast).thenAnswer(cityAnswer)

                //init timerCursor
                val timerAnswer: Answer<*> = object : Answer<Any?> {
                    var count = timerCount
                    var pos = 0
                    @Throws(Throwable::class)
                    override fun answer(invocation: InvocationOnMock): Any? {
                        val method = invocation.method.name
                        when (method) {
                            "getString" -> return "string"
                            "getInt" -> return 1
                            "getLong" -> return 1L
                            "moveToNext" -> pos++
                            "isAfterLast" -> return pos >= count
                        }
                        return null
                    }
                }
                Mockito.`when`(timerCursor.getString(ArgumentMatchers.anyInt()))
                    .thenAnswer(timerAnswer)
                Mockito.`when`(timerCursor.getInt(ArgumentMatchers.anyInt()))
                    .thenAnswer(timerAnswer)
                Mockito.`when`(timerCursor.getLong(ArgumentMatchers.anyInt()))
                    .thenAnswer(timerAnswer)
                Mockito.`when`(timerCursor.moveToNext()).thenAnswer(timerAnswer)
                Mockito.`when`(timerCursor.isAfterLast).thenAnswer(timerAnswer)

                //init repeatCursor
                val repeatAnswer: Answer<*> = object : Answer<Any?> {
                    var count = repeatCount
                    var pos = 0
                    @Throws(Throwable::class)
                    override fun answer(invocation: InvocationOnMock): Any? {
                        val method = invocation.method.name
                        when (method) {
                            "getInt" -> return 1
                            "moveToNext" -> pos++
                            "isAfterLast" -> return pos >= count
                        }
                        return null
                    }
                }
                Mockito.`when`(repeatCursor.getInt(ArgumentMatchers.anyInt()))
                    .thenAnswer(repeatAnswer)
                Mockito.`when`(repeatCursor.moveToNext()).thenAnswer(repeatAnswer)
                Mockito.`when`(repeatCursor.isAfterLast).thenAnswer(repeatAnswer)

                //invoke onBackup()
                plugin.onBackup(null)
                //assert
                val mClockList = ReflectUtil.getFieldValue(
                    ClockBackupPlugin::class.java,
                    "mClockList", plugin
                ) as ArrayList<Alarm>
                Assert.assertEquals(alarmCount.toLong(), mClockList.size.toLong())
                val mWorldClockList = ReflectUtil.getFieldValue(
                    ClockBackupPlugin::class.java,
                    "mWorldClockList", plugin
                ) as ArrayList<Alarm>
                Assert.assertEquals(cityCount.toLong(), mWorldClockList.size.toLong())
                val mTimerList = ReflectUtil.getFieldValue(
                    ClockBackupPlugin::class.java,
                    "mTimerList", plugin
                ) as ArrayList<Alarm>
                Assert.assertEquals(timerCount.toLong(), mTimerList.size.toLong())
                val mAlarmRepeatList = ReflectUtil.getFieldValue(
                    ClockBackupPlugin::class.java,
                    "mAlarmRepeatList", plugin
                ) as ArrayList<Alarm>
                Assert.assertEquals(repeatCount.toLong(), mAlarmRepeatList.size.toLong())
            } catch (e: Exception) {
                throw RuntimeException(e.message)
            }
            currentThread.interrupt()
        }
        val thread = Thread(runnable)
        thread.start()
        var interrupted = false
        try {
            Thread.sleep(4000)
        } catch (e: InterruptedException) {
            interrupted = true
        }
        Assert.assertTrue(interrupted)
    }

    @Test
    @Throws(
        NoSuchFieldException::class,
        IllegalAccessException::class,
        NoSuchMethodException::class
    )
    fun should_mMaxCount_equalTo_sum_of_counts_when_getMaxCount_with_cursors_in_mCursorArray_getCount_return_counts() {
        val counts = intArrayOf(2, 3, 4, 5)
        var totalCount = 0
        val cursorArray = arrayOfNulls<Cursor>(counts.size)
        for (i in counts.indices) {
            val cursor = Mockito.mock(Cursor::class.java)
            Mockito.`when`(cursor.count).thenReturn(counts[i])
            Mockito.`when`(cursor.isClosed).thenReturn(false)
            cursorArray[i] = cursor
            totalCount += counts[i]
        }
        val plugin = ClockBackupPlugin()
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mCursorArray",
            plugin,
            cursorArray
        )
        //invoke getMaxCount()
        ReflectUtil.invoke(ClockBackupPlugin::class.java, "getMaxCount", null, plugin)
        //assert
        Assert.assertEquals(
            totalCount, ReflectUtil.getFieldValue(
                ClockBackupPlugin::class.java, "mMaxCount", plugin
            )
        )
    }

    @Suppress("FuncSingleCommentRule")
    @Config(shadows = [ShadowAlarmRingUtils::class])
    @Test
    @Throws(
        NoSuchMethodException::class,
        IllegalAccessException::class,
        NoSuchFieldException::class,
        IOException::class
    )
    fun should_return_mDefaultAlarmMediaUriStr_when_getRightAlertUri_with_DEFAULT_ALARM_SETTING_URI_and_ringFile_exists() {
        val DEFAULT_ALARM_SETTING_URI = Settings.System.DEFAULT_ALARM_ALERT_URI
            .toString()
        val plugin = ClockBackupPlugin()
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mDefaultAlarmMediaUriStr",
            plugin,
            ShadowAlarmRingUtils.sDefaultAlarmMediaUriStr
        )
        val defaultRingFile = File(ShadowAlarmRingUtils.sDefaultRingPath)
        if (!defaultRingFile.exists()) {
            defaultRingFile.createNewFile()
        }
//        //inovke getRightAlertUri()
//        val ringPath = ReflectUtil.invoke(
//            ClockBackupPlugin::class.java,
//            "getRightAlertUri",
//            arrayOf<Any>(DEFAULT_ALARM_SETTING_URI),
//            plugin,
//            String::class.java
//        ) as? String
//        //assert
//        Assert.assertEquals(ShadowAlarmRingUtils.sDefaultRingPath, ringPath)
        //release
        if (defaultRingFile.exists()) {
            defaultRingFile.delete()
        }
    }

    @Test
    @Throws(
        NoSuchMethodException::class,
        IllegalAccessException::class,
        NoSuchFieldException::class,
        IOException::class
    )
    fun should_return_null_when_getRightAlertUri_with_DEFAULT_ALARM_SETTING_URI_and_ringFile_not_exists() {
        val DEFAULT_ALARM_SETTING_URI = Settings.System.DEFAULT_ALARM_ALERT_URI
            .toString()
        val plugin = ClockBackupPlugin()
        ReflectUtil.setFieldValue(
            ClockBackupPlugin::class.java,
            "mDefaultAlarmMediaUriStr",
            plugin,
            ShadowAlarmRingUtils.sDefaultAlarmMediaUriStr
        )
        val defaultRingFile = File(ShadowAlarmRingUtils.sDefaultRingPath)
        if (defaultRingFile.exists()) {
            defaultRingFile.delete()
        }
        //inovke getRightAlertUri()
        val ringPath = ReflectUtil.invoke(
            ClockBackupPlugin::class.java,
            "getRightAlertUri",
            arrayOf<Any>(DEFAULT_ALARM_SETTING_URI),
            plugin,
            String::class.java
        ) as? String
        //assert
        Assert.assertNull(ringPath)
        //release
        if (defaultRingFile.exists()) {
            defaultRingFile.delete()
        }
    }

    @Implements(AlarmRingUtils::class)
    object ShadowAlarmRingUtils {
        var sDefaultAlarmMediaUriStr = "DefaultAlarmMediaUriStr"
        var sDefaultRingPath = "./default.mp3"

        @JvmStatic
        @Implementation
        fun getMusicPathFromUriString(context: Context?, uriStr: String): String? {
            return if (uriStr == sDefaultAlarmMediaUriStr) {
                sDefaultRingPath
            } else null
        }
    }

    companion object {
        private const val ALARM_CLOCK = 0
        private const val WORLD_CLOCK = 1
        private const val TIMER = 2
        private const val REPEAT_INFO = 3

        @JvmStatic
        @BeforeClass
        fun classSetUp() {
            ShadowUtil.init(
                ShadowUserHandleNative::class.java,
                ShadowUtil.sClassInitializerCallback,
                null,
                null,
                null
            )
        }
    }
}