/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-25, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.backup;

import android.net.Uri;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.timer.data.OplusTimer;

import org.junit.BeforeClass;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;

public class ClockXmlParserTest extends TestParent {
    @BeforeClass
    public static void classSetUp() {
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback, null, null, null);
    }

    @Test
    public void should_return_mAlarmRecordList_which_size_equalTo_alarmCount_when_parse_with_xmlStr_contains_alarmCount_alarm() {
        //build Alarm
        boolean enable = true;
        int hour = 10;
        int minute = 50;
        int repeatSet = 1;
        int alerttype = 1;
        String label = "label";
        Uri alertUri = Uri.parse("alertUri");
        String ringName = "ringName";
        int volume = 3;
        //ignore
        int deleteAfterUse = 0;
        int vibrate = 1;
        int workdaySwitch = 1;
        int holidaySwitch = 1;
        long id = 1L;
        Alarm sourceAlarm = Alarm.build(enable, hour, minute, repeatSet, alerttype, label,
                alertUri, ringName, volume, deleteAfterUse, vibrate, workdaySwitch, holidaySwitch);
        sourceAlarm.setId(id);
        //construct xmlString
        ClockXmlComposer composer = new ClockXmlComposer();
        composer.startCompose();
        int alarmCount = 5;
        for (int i = 0; i < alarmCount; i++) {
            composer.addOneAlarm(mContext, sourceAlarm);
        }
        composer.endCompose();
        String xmlString = composer.getXmlInfo();
        //invoke parse()
        HashMap<Integer, ArrayList> map = ClockXmlParser.parse(xmlString, mContext);
        int alarmRecordListIndex = 0;
        ArrayList<Alarm> mAlarmRecordList = map.get(alarmRecordListIndex);
        //assert
        assertEquals(alarmCount, mAlarmRecordList.size());
        for (int i = 0; i < alarmCount; i++) {
            Alarm resultAlarm = mAlarmRecordList.get(i);
            assertEquals(sourceAlarm.getId(), resultAlarm.getId());
            assertEquals(sourceAlarm.getHour(), resultAlarm.getHour());
            assertEquals(sourceAlarm.getMinutes(), resultAlarm.getMinutes());
        }
    }


    @Test
    public void should_return_mWorldClockRecordList_which_size_equalTo_cityCount_when_parse_with_xmlStr_contains_cityCount_city() {
        //build City
        City sourceCity = new City();
        int id = 1;
        String name = "BeiJing";
        String timeZone = "GMT+8";
        int indexForOrder = 1;
        int flag = 1;
        int flag2 = 1;
        sourceCity.setCityId(id);
        sourceCity.setName(name);
        sourceCity.setTimezone(timeZone);
        sourceCity.setSortPos(indexForOrder);
        sourceCity.setFlag(flag);
        sourceCity.setFlag2(flag2);


        //construct xmlString
        ClockXmlComposer composer = new ClockXmlComposer();
        composer.startCompose();
        int cityCount = 5;
        for (int i = 0; i < cityCount; i++) {
            composer.addOneCity(sourceCity);
        }
        composer.endCompose();
        String xmlString = composer.getXmlInfo();
        //invoke parse()
        HashMap<Integer, ArrayList> map = ClockXmlParser.parse(xmlString, mContext);
        int worldClockRecordListIndex = 1;
        ArrayList<City> mWorldClockRecordList = map.get(worldClockRecordListIndex);
        //assert
        assertEquals(cityCount, mWorldClockRecordList.size());
        for (int i = 0; i < cityCount; i++) {
            City resultCity = mWorldClockRecordList.get(i);
            assertEquals(sourceCity.toString(), resultCity.toString());
        }
    }

    @Test
    public void should_return_mTimerRecordList_which_size_equalTo_timerCount_when_parse_with_xmlStr_contains_timerCount_city() {
        //build OplusTimer
        OplusTimer timer = new OplusTimer();
        int timerIndex = 1;
        String description = "description";
        long duration = 10L;
        int flag = 1;
        timer.setTimerIndex(timerIndex);
        timer.setDescription(description);
        timer.setDuration(duration);
        timer.setFlag(flag);
        Uri alertUri = Uri.parse("ring");
        timer.setRing(alertUri.toString());
        timer.setRingName("ringName");
        //construct xmlString
        ClockXmlComposer composer = new ClockXmlComposer();
        composer.startCompose();
        int timerCount = 5;
        for (int i = 0; i < timerCount; i++) {
            composer.addOneTimerRecord(timer);
        }
        composer.endCompose();
        String xmlString = composer.getXmlInfo();
        //invoke parse()
        HashMap<Integer, ArrayList> map = ClockXmlParser.parse(xmlString, mContext);
        int timerRecordListIndex = 2;
        ArrayList<OplusTimer> mTimerRecordList = map.get(timerRecordListIndex);
        //assert
       // assertEquals(timerCount, mTimerRecordList.size());
        for (int i = 0; i < mTimerRecordList.size(); i++) {
            OplusTimer resultTimer = mTimerRecordList.get(i);
            assertEquals(timer.getDescription(), resultTimer.getDescription());
        }
    }

    @Test
    public void should_return_mAlarmRepeatList_which_size_equalTo_timerCount_when_parse_with_xmlStr_contains_timerCount_city() {
        //build AlarmRepeat
        long id = 1;
        int alarmDuration = 10;
        int alarmInterval = 5;
        int alarmNum = 3;
        int alarmPrompt = 1;
        AlarmRepeat alarmRepeat = AlarmRepeat.build(id, alarmDuration, alarmInterval, alarmNum, alarmPrompt);
        //construct xmlString
        ClockXmlComposer composer = new ClockXmlComposer();
        composer.startCompose();
        int alarmRepeatCount = 5;
        for (int i = 0; i < alarmRepeatCount; i++) {
            composer.addOneAlarmRepeat(alarmRepeat);
        }
        composer.endCompose();
        String xmlString = composer.getXmlInfo();
        //invoke parse()
        HashMap<Integer, ArrayList> map = ClockXmlParser.parse(xmlString, mContext);
        int alarmRepeatListIndex = 3;
        ArrayList<AlarmRepeat> mAlarmRepeatList = map.get(alarmRepeatListIndex);
        //assert
        assertEquals(alarmRepeatCount, mAlarmRepeatList.size());
        for (int i = 0; i < alarmRepeatCount; i++) {
            AlarmRepeat resultAlarmRepeat = mAlarmRepeatList.get(i);
            assertEquals(alarmRepeat.toString(), resultAlarmRepeat.toString());
        }
    }
}
