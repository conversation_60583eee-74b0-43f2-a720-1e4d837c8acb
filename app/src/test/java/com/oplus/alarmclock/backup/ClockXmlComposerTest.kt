/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ClockXmlComposerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
@file:Suppress("VarCouldBeVal", "NoUnusedImports", "MaximumLineLength")
package com.oplus.alarmclock.backup

import android.content.Context
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.backup.ClockXmlComposer
import android.util.Xml
import org.mockito.Mockito
import com.oplus.alarmclock.timer.data.OplusTimer
import com.oplus.alarmclock.alarmclock.AlarmRepeat
import android.content.SharedPreferences
import android.net.Uri
import com.oplus.alarmclock.utils.TimerConstant
import com.oplus.alarmclock.backup.BackUpConstant
import com.oplus.alarmclock.AlarmClockApplication
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.alarmclock.Alarm
import com.oplus.alarmclock.globalclock.City
import org.powermock.reflect.Whitebox
import com.oplus.alarmclock.utils.PrefUtils
import com.oplus.alarmclock.shadows.utils.ShadowUtil
import com.oplus.alarmclock.shadows.ShadowUserHandleNative
import org.junit.Assert
import org.junit.BeforeClass
import org.junit.Test
import org.robolectric.annotation.Config
import org.robolectric.annotation.Implementation
import org.robolectric.annotation.Implements
import org.xmlpull.v1.XmlSerializer
import java.io.StringWriter
import java.lang.Exception
import java.util.HashMap

class ClockXmlComposerTest : TestParent() {
    private var mSerializer: XmlSerializer? = null
    private var mTempSerializer: XmlSerializer? = null
    private var mStringWriter: StringWriter? = null
    private var clockXmlComposer: ClockXmlComposer? = null
    @Throws(Exception::class)
    override fun setUp() {
        super.setUp()
        mSerializer = Xml.newSerializer()
        mTempSerializer = Xml.newSerializer()
        mStringWriter = StringWriter()
        clockXmlComposer = ClockXmlComposer()
    }

    @Test
    @Suppress("LongMethod")
    @Throws(
        NoSuchMethodException::class,
        IllegalAccessException::class,
        NoSuchFieldException::class
    )
    fun should_return_success_when_addOneAlarm_with_alarm_isNot_Null() {
        var enable = true
        var hour = 10
        var minute = 50
        var repeatSet = 1
        val repeatList = intArrayOf(1, 2, 4, 8, 16, 32, 64)
        val alerttype = 1
        var label = "label"
        val alertUri = Uri.parse("alertUri")
        val ringName = "ringName"
        val volume = 3
        //ignore
        val deleteAfterUse = 0
        val vibrate = 64
        var workdaySwitch = 1
        var holidaySwitch = 1
        var id = 1L
        for (i in 0..9) {
            enable = i % 2 == 0
            hour = i
            minute = i
            repeatSet = repeatList[i % 7]
            label = "$label [ $i ]"
            workdaySwitch = i % 2
            holidaySwitch = i % 2
            id = i.toLong()
            val sourceAlarm = Alarm.build(
                enable, hour, minute, repeatSet, alerttype, label,
                alertUri, ringName, volume, deleteAfterUse, vibrate, workdaySwitch, holidaySwitch
            )
            sourceAlarm.id = id
            val spyContext = Mockito.spy(mContext)

            //invoke
            ReflectUtil.setFieldValue(
                ClockXmlComposer::class.java,
                "mSerializer",
                clockXmlComposer,
                mSerializer
            )
            ReflectUtil.setFieldValue(
                ClockXmlComposer::class.java,
                "mStringWriter",
                clockXmlComposer,
                mStringWriter
            )
            ReflectUtil.setFieldValue(
                ClockXmlComposer::class.java,
                "mTempSerializer",
                clockXmlComposer,
                mTempSerializer
            )

            //verify
            val isAddSuccess = ReflectUtil.invoke(
                ClockXmlComposer::class.java,
                "addOneAlarm",
                arrayOf(spyContext, sourceAlarm),
                clockXmlComposer,
                Context::class.java,
                Alarm::class.java
            ) as Boolean
            Assert.assertTrue(isAddSuccess)
        }
    }

    @Test
    @Throws(
        NoSuchFieldException::class,
        IllegalAccessException::class,
        NoSuchMethodException::class
    )
    fun should_return_success_when_addOneCity_with_city_isNot_Null() {
        for (i in 0..9) {
            val city = City()
            city.cityId = 1
            city.displayPosition = 1
            city.flag = 1
            city.flag2 = 1
            city.name = "成都$i"
            city.sortPos = 1
            city.timezone = "GMT-0"

            //invoke
            ReflectUtil.setFieldValue(
                ClockXmlComposer::class.java,
                "mSerializer",
                clockXmlComposer,
                mSerializer
            )
            //verify
            val isAddSuccess = ReflectUtil.invoke(
                ClockXmlComposer::class.java,
                "addOneCity",
                arrayOf<Any>(city),
                clockXmlComposer,
                City::class.java
            ) as Boolean
            Assert.assertTrue(isAddSuccess)
        }
    }

    @Test
    @Throws(
        NoSuchFieldException::class,
        IllegalAccessException::class,
        NoSuchMethodException::class
    )
    fun should_return_success_when_addOneTimerRecord_with_timer_isNot_Null() {
        //init
        val timer = OplusTimer()
        timer.flag = 0
        val description = "123"
        timer.description = description
        timer.selected = 0
        val duration = (60 * 1000).toLong()
        timer.duration = duration
        timer.position = 0
        val alertUri = Uri.parse("ring")
        timer.ring = alertUri.toString()
        timer.ringName = "ringName"
        ReflectUtil.setFieldValue(
            ClockXmlComposer::class.java,
            "mSerializer",
            clockXmlComposer,
            mSerializer
        )
        //invoke
        val isAddSuccess = ReflectUtil.invoke(
            ClockXmlComposer::class.java,
            "addOneTimerRecord",
            arrayOf<Any>(timer),
            clockXmlComposer,
            OplusTimer::class.java
        ) as Boolean
        //verify
        Assert.assertTrue(isAddSuccess)
    }

    @Test
    @Throws(
        NoSuchFieldException::class,
        IllegalAccessException::class,
        NoSuchMethodException::class
    )
    fun should_return_success_when_addOneAlarmRepeat_with_AlarmRepeat_isNot_Null() {
        //init
        val alarmRepeat = AlarmRepeat()
        alarmRepeat.setmAlarmPrompt(0)
        val repeatAlertLength = intArrayOf(1, 5, 10, 15, 20, 30)
        alarmRepeat.setmAlarmDuration(repeatAlertLength[2])
        val repeatAlertNum = intArrayOf(1, 2, 3, 5, 10)
        alarmRepeat.setmAlarmNum(repeatAlertNum[2])
        val repeatAlertInterval = intArrayOf(5, 10, 15, 20, 25, 30)
        alarmRepeat.setmAlarmInterval(repeatAlertInterval[2])
        ReflectUtil.setFieldValue(
            ClockXmlComposer::class.java,
            "mSerializer",
            clockXmlComposer,
            mSerializer
        )
        //invoke
        val isAddSuccess = ReflectUtil.invoke(
            ClockXmlComposer::class.java,
            "addOneAlarmRepeat",
            arrayOf<Any>(alarmRepeat),
            clockXmlComposer,
            AlarmRepeat::class.java
        ) as Boolean
        //verify
        Assert.assertTrue(isAddSuccess)
    }

    @Test
    @Throws(
        NoSuchFieldException::class,
        IllegalAccessException::class,
        NoSuchMethodException::class
    )
    fun should_return_success_when_addTimerRecord_with_context() {
        val mockPrefs = Mockito.mock(SharedPreferences::class.java)
        val context = Mockito.spy(mContext)
        Mockito.doReturn(true).`when`(mockPrefs)
            .getBoolean(TimerConstant.TIMER_STATUS_START_PREFERENCE, false)
        Mockito.doReturn(false).`when`(mockPrefs)
            .getBoolean(TimerConstant.TIMER_STATUS_PAUSE_PREFERENCE, false)
        Mockito.doReturn(false).`when`(mockPrefs)
            .getBoolean(TimerConstant.TIMER_NEED_TO_ALARM_PREFERENCE, false)
        val timerSetTime = 1000L
        Mockito.doReturn(timerSetTime).`when`(mockPrefs)
            .getLong(BackUpConstant.TIMER_SET_TIME_PREFERENCE, 0)
        val timerData = 1000L
        Mockito.doReturn(timerData).`when`(mockPrefs)
            .getLong(BackUpConstant.TIMER_SET_TIME_PREFERENCE, 0)
        val timerStart = 1000L
        Mockito.doReturn(timerStart).`when`(mockPrefs)
            .getLong(BackUpConstant.TIMER_DATA_START_PREFERENCE, 0)
        val timerTotalTime = 1000L
        Mockito.doReturn(timerTotalTime).`when`(mockPrefs)
            .getLong(BackUpConstant.TIMER_DATA_TOTAL_TIME_PREFERENCE, 0)
        val timerStatus = 1
        Mockito.doReturn(timerStatus).`when`(mockPrefs)
            .getInt(BackUpConstant.TIMER_STATUS_PREFERENCE, 0)
        Mockito.doReturn(mockPrefs).`when`(context).getSharedPreferences(
            AlarmClockApplication.SHARED_PREFS_ALARM_CLOCK_APP,
            Context.MODE_PRIVATE
        )
        ReflectUtil.setFieldValue(
            ClockXmlComposer::class.java,
            "mSerializer",
            clockXmlComposer,
            mSerializer
        )

        //invoke
        val isAddSuccess = ReflectUtil.invoke(
            ClockXmlComposer::class.java,
            "addTimerRecord",
            arrayOf<Any>(context),
            clockXmlComposer,
            Context::class.java
        ) as Boolean

        //verify
        Assert.assertTrue(isAddSuccess)
    }

    @Test
    fun should_add_xml_tag_failed_when_addDialClockCityData_with_context_is_null() {
        val success = clockXmlComposer!!.addDialClockCityData(null)
        Assert.assertFalse(success)
    }

    @Suppress("FuncSingleCommentRule")
    @Config(shadows = [ShadowPrefUtils::class])
    @Test
    fun should_add_xml_tag_when_addDialClockCityData_with_context_not_null() {
//        var preMap: MutableMap<String, String>? = null
//        ShadowPrefUtils.sMap = preMap
//        var success = clockXmlComposer?.addDialClockCityData(mContext)
//        Assert.assertFalse(success == true)
//        preMap = HashMap()
//        ShadowPrefUtils.sMap = preMap
//        success = clockXmlComposer?.addDialClockCityData(mContext)
//        Assert.assertFalse(success == true)
//        preMap["dial_clock_city_id_69_1_0"] = "5"
//        ShadowPrefUtils.sMap = preMap
//        Whitebox.setInternalState(clockXmlComposer, "mSerializer", mSerializer)
//        success = clockXmlComposer?.addDialClockCityData(mContext)
//        Assert.assertTrue(success == true)
    }

    @Implements(PrefUtils::class)
    private object ShadowPrefUtils {
        var sMap: Map<String, String>? = null
        @JvmStatic
        @Implementation
        fun getAll(context: Context?, prefName: String?): Map<String, *>? {
            return sMap
        }
    }

    companion object {
        @JvmStatic
        @BeforeClass
        fun classSetUp() {
            ShadowUtil.init(
                ShadowUserHandleNative::class.java,
                ShadowUtil.sClassInitializerCallback,
                null,
                null,
                null
            )
        }
    }
}