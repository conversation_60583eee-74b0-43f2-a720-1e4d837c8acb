/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-9-24, zhangjinbiao, create
 ***********************************************************/
package com.oplus.alarmclock.backup;

import android.content.ContentProviderOperation;
import android.content.Context;

import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.alarmclock.Alarm;
import com.oplus.alarmclock.alarmclock.AlarmRepeat;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.shadows.ShadowContextNative;
import com.oplus.alarmclock.shadows.ShadowUserHandleNative;
import com.oplus.alarmclock.shadows.utils.ShadowUtil;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;
import com.oplus.backup.sdk.common.host.BREngineConfig;
import com.oplus.backup.sdk.component.BRPluginHandler;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.globalclock.City;
import com.oplus.alarmclock.timer.data.OplusTimer;
import com.oplus.alarmclock.timer.data.TimerDataHelper;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.robolectric.annotation.Config;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import java.util.ArrayList;
import java.util.Random;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Config(shadows = {ShadowContextNative.class})
public class ClockRestorePluginTest extends TestParent {
    @BeforeClass
    public static void classSetUp(){
        ShadowUtil.init(ShadowUserHandleNative.class, ShadowUtil.sClassInitializerCallback,null,null,null);
    }

    @Test
    @Ignore
    public void should_call_mWorldClockRecordList_get_eight_times_when_onRestore_with_mWorldClockRecordList_size_is_eight() throws NoSuchFieldException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        plugin.onCreate(mContext, mock(BRPluginHandler.class), mock(BREngineConfig.class));
        ArrayList<ContentProviderOperation> mDbOps = new ArrayList();
        int size = 8;
        City city = new City();
        ArrayList<City> mWorldClockRecordList = spy(new ArrayList(8));
        for(int i=0; i<size; i++){
            mWorldClockRecordList.add(city);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mWorldClockRecordList", plugin, mWorldClockRecordList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mDbOps", plugin, mDbOps);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mMaxCount", plugin, size);
        //invoke onRestore(Bundle arg0)
        plugin.onRestore(null);
        //assert
        verify(mWorldClockRecordList, times(size)).get(anyInt());
    }

    @Test
    @Ignore
    public void should_mDbOps_size_equalTo_mTimerRecordList_size_when_onRestore_with_mTimerRecordList() throws NoSuchFieldException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        plugin.onCreate(mContext, mock(BRPluginHandler.class), mock(BREngineConfig.class));
        ArrayList<ContentProviderOperation> mDbOps = new ArrayList();
        int size = 8;
        OplusTimer timer = new OplusTimer();
        ArrayList<OplusTimer> mTimerRecordList = spy(new ArrayList(8));
        for(int i=0; i<size; i++){
            mTimerRecordList.add(timer);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mTimerRecordList", plugin, mTimerRecordList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mDbOps", plugin, mDbOps);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mMaxCount", plugin, size);
        //invoke onRestore(Bundle arg0)
        plugin.onRestore(null);
        //assert
        assertEquals(size, mDbOps.size());
    }

    @Test
    @Ignore
    public void should_mDbOps_size_equalTo_mAlarmRepeatList_size_when_onRestore_with_mAlarmRepeatList() throws NoSuchFieldException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        plugin.onCreate(mContext, mock(BRPluginHandler.class), mock(BREngineConfig.class));
        ArrayList<ContentProviderOperation> mDbOps = new ArrayList();
        int size = 8;
        AlarmRepeat repeat = new AlarmRepeat();
        ArrayList<AlarmRepeat> mAlarmRepeatList = spy(new ArrayList(8));
        for(int i=0; i<size; i++){
            mAlarmRepeatList.add(repeat);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mAlarmRepeatList", plugin, mAlarmRepeatList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mDbOps", plugin, mDbOps);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mMaxCount", plugin, size);
        //invoke onRestore(Bundle arg0)
        plugin.onRestore(null);
        //assert
        assertEquals(size, mDbOps.size());
    }

    @Test
    @Ignore
    public void should_mCompletedCount_equalTo_mMaxCount_when_onRestore_with_mMaxCount_equalTo_sumOf_counts() throws NoSuchFieldException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        plugin.onCreate(mContext, mock(BRPluginHandler.class), mock(BREngineConfig.class));
        int[] counts = new int[]{2,3,4};
        int maxCount = 0;
        for(int i=0; i<counts.length; i++){
            maxCount += counts[i];
        }
        ArrayList<ContentProviderOperation> mDbOps = new ArrayList();

        //mWorldClockRecordList
        City city = new City();
        ArrayList<City> mWorldClockRecordList = spy(new ArrayList(8));
        for(int i=0; i<counts[0]; i++){
            mWorldClockRecordList.add(city);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mWorldClockRecordList", plugin, mWorldClockRecordList);

        //mTimerRecordList
        OplusTimer timer = new OplusTimer();
        ArrayList<OplusTimer> mTimerRecordList = spy(new ArrayList(8));
        for(int i=0; i<counts[1]; i++){
            mTimerRecordList.add(timer);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mTimerRecordList", plugin, mTimerRecordList);

        //mAlarmRepeatList
        AlarmRepeat repeat = new AlarmRepeat();
        ArrayList<AlarmRepeat> mAlarmRepeatList = spy(new ArrayList(8));
        for(int i=0; i<counts[2]; i++){
            mAlarmRepeatList.add(repeat);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mAlarmRepeatList", plugin, mAlarmRepeatList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mDbOps", plugin, mDbOps);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mMaxCount", plugin, maxCount);
        //invoke onRestore(Bundle arg0)
        plugin.onRestore(null);
        //assert
        int completeCount = (int) ReflectUtil.getFieldValue(ClockRestorePlugin.class, "mCompletedCount", plugin);
        assertEquals(mTimerRecordList.size()+mAlarmRepeatList.size(), mDbOps.size());
        assertEquals(maxCount, completeCount);
    }

    @Test
    @Ignore
    public void should_mCompletedCount_equalTo_mMaxCount_when_onRestore_with_mMaxCount_greater_than_sumOf_counts() throws NoSuchFieldException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        plugin.onCreate(mContext, mock(BRPluginHandler.class), mock(BREngineConfig.class));
        int[] counts = new int[]{2,3,4};
        int maxCount = 0;
        for(int i=0; i<counts.length; i++){
            maxCount += counts[i];
        }
        //ensure mMaxCount greater than sum of counts
        maxCount++;
        ArrayList<ContentProviderOperation> mDbOps = new ArrayList();

        //mWorldClockRecordList
        City city = new City();
        ArrayList<City> mWorldClockRecordList = spy(new ArrayList(8));
        for(int i=0; i<counts[0]; i++){
            mWorldClockRecordList.add(city);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mWorldClockRecordList", plugin, mWorldClockRecordList);

        //mTimerRecordList
        OplusTimer timer = new OplusTimer();
        ArrayList<OplusTimer> mTimerRecordList = spy(new ArrayList(8));
        for(int i=0; i<counts[1]; i++){
            mTimerRecordList.add(timer);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mTimerRecordList", plugin, mTimerRecordList);

        //mAlarmRepeatList
        AlarmRepeat repeat = new AlarmRepeat();
        ArrayList<AlarmRepeat> mAlarmRepeatList = spy(new ArrayList(8));
        for(int i=0; i<counts[2]; i++){
            mAlarmRepeatList.add(repeat);
        }
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mAlarmRepeatList", plugin, mAlarmRepeatList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mDbOps", plugin, mDbOps);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mMaxCount", plugin, maxCount);
        //invoke onRestore(Bundle arg0)
        plugin.onRestore(null);
        //assert
        int completeCount = (int) ReflectUtil.getFieldValue(ClockRestorePlugin.class, "mCompletedCount", plugin);
        assertEquals(maxCount, completeCount);
    }

    @Config(shadows = {ShadowTimerDataHelper.class})
    @Test
    public void should_list_size_always_lessThan_maxListSize_when_getTimersInDB_with_list_size_is_random_num_and_timerSizeInDB_lessThan_TIMER_PAGE_ITEM_MAX_NUM() throws NoSuchMethodException, IllegalAccessException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        int timerSizeInDB = 8;
        ShadowTimerDataHelper.sTimers = mock(ArrayList.class);
        when(ShadowTimerDataHelper.sTimers.size()).thenReturn(timerSizeInDB);
        Random rand = new Random();
        ArrayList<OplusTimer> list = new ArrayList<>(8);
        OplusTimer timer = mock(OplusTimer.class);
        int repeat = 5;
        for(int i=0; i<repeat; i++){
            final int listSize = rand.nextInt(10);
            list.clear();
            for(int j=0; j<listSize; j++){
                list.add(timer);
            }
            ReflectUtil.invoke(ClockRestorePlugin.class, "modifyTimerList",
                    new Object[]{list}, plugin, ArrayList.class);
        }
    }



    @Test
    public void should_return_expectedMaxCount_when_getMaxCount_with_arrayLists_size_in_counts() throws NoSuchFieldException, IllegalAccessException, NoSuchMethodException {
        ClockRestorePlugin plugin = new ClockRestorePlugin();
        int[] counts = new int[]{2,3,4,5};
        int expectedMaxCount = 0;
        for(int i=0; i<counts.length; i++){
            expectedMaxCount += counts[i];
        }
        ArrayList<Alarm> mAlarmRecordList = mock(ArrayList.class);
        ArrayList<City> mWorldClockRecordList = mock(ArrayList.class);
        ArrayList<OplusTimer> mTimerRecordList = mock(ArrayList.class);
        ArrayList<AlarmRepeat> mAlarmRepeatList = mock(ArrayList.class);
        when(mAlarmRecordList.size()).thenReturn(counts[0]);
        when(mWorldClockRecordList.size()).thenReturn(counts[1]);
        when(mTimerRecordList.size()).thenReturn(counts[2]);
        when(mAlarmRepeatList.size()).thenReturn(counts[3]);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mAlarmRecordList",
                plugin, mAlarmRecordList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mWorldClockRecordList",
                plugin, mWorldClockRecordList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mTimerRecordList",
                plugin, mTimerRecordList);
        ReflectUtil.setFieldValue(ClockRestorePlugin.class, "mAlarmRepeatList",
                plugin, mAlarmRepeatList);
        //invoke
        int maxCount = (int) ReflectUtil.invoke(ClockRestorePlugin.class,
                "getMaxCount", null, plugin);
        assertEquals(expectedMaxCount, maxCount);
    }

    @Implements(TimerDataHelper.class)
    public static class ShadowTimerDataHelper{
        static ArrayList<OplusTimer>  sTimers;
        @Implementation
        public static ArrayList<OplusTimer> getTimersInDB(Context context) {
            return sTimers;
        }
    }
}
