/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-8-28, zhangjinbiao, create
 ***********************************************************/
package com.coloros.widget;

import android.content.Context;
import android.util.AttributeSet;

import com.coloros.alarmclock.widget.OplusTimePickerCustomClock;
import com.coui.appcompat.picker.COUINumberPicker;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.shadows.annotation.Mapping;
import com.oplus.alarmclock.shadows.annotation.MappingConstructor;

import org.junit.Ignore;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
@Ignore
public class OplusTimePickerCustomClockTest extends TestParent {
    final int HOURS_IN_HALF_DAY = 12;
    @Test
    public void should_return_time_when_getCurrentHour_with_is24HourView_is_true() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        boolean is24HourView = true;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        int time = 23;
        when(mockOplusHourSpinner.getValue()).thenReturn(time);
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner", oplusTimePickerCustomClock, mockOplusHourSpinner);
        //invoke getCurrentHour()
        int actualTime = oplusTimePickerCustomClock.getCurrentHour();
        //assert
        assertEquals(time, actualTime);
    }

    @Test
    public void should_return_time_when_getCurrentHour_with_is24HourView_is_false_and_mIsAm_is_true() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        //set mIs24HourView to false;
        boolean is24HourView = false;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        //set mIsAm to true
        boolean isAm = true;
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mIsAm", oplusTimePickerCustomClock, isAm);
        int time = 10;
        when(mockOplusHourSpinner.getValue()).thenReturn(time);
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner", oplusTimePickerCustomClock, mockOplusHourSpinner);
        //invoke getCurrentHour()
        int actualTime = oplusTimePickerCustomClock.getCurrentHour();
        //assert
        assertEquals(time, actualTime);
    }


    @Test
    public void should_return_time_in_24Hour_mode_when_getCurrentHour_with_is24HourView_is_false_and_mIsAm_is_false() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        //set mIs24HourView to false;
        boolean is24HourView = false;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        //set mIsAm to false
        boolean isAm = false;
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mIsAm", oplusTimePickerCustomClock, isAm);
        int time = 10;
        when(mockOplusHourSpinner.getValue()).thenReturn(time);
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner", oplusTimePickerCustomClock, mockOplusHourSpinner);
        //invoke getCurrentHour()
        int actualTime = oplusTimePickerCustomClock.getCurrentHour();
        //assert
        int expectedTime = time + HOURS_IN_HALF_DAY;
        assertEquals(expectedTime, actualTime);
    }


    @Test
    public void should_call_setValue_with_time_and_set_mIsAm_true_when_setCurrentHour_with_mIs24HourView_is_false_and_time_smaller_than_HOURS_IN_HALF_DAY() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        //set mIs24HourView to false;
        boolean is24HourView = false;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        //set mIsAm to false
        boolean isAm = false;
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mIsAm", oplusTimePickerCustomClock, isAm);

        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner",
                oplusTimePickerCustomClock, mockOplusHourSpinner);
        int time = 10;
        //invoke setCurrentHour()
        oplusTimePickerCustomClock.setCurrentHour(time);
        //verify&assert
        verify(mockOplusHourSpinner).setValue(time);
        boolean actualIsAm = (boolean) ReflectUtil.getFieldValue(OplusTimePickerCustomClock.class,
                "mIsAm", oplusTimePickerCustomClock);
        assertTrue(actualIsAm);
    }

    @Test
    public void should_call_setValue_with_value_smaller_than_HOURS_IN_HALF_DAY_and_set_mIsAm_false_when_setCurrentHour_with_mIs24HourView_is_false_and_time_bigger_than_HOURS_IN_HALF_DAY() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        //set mIs24HourView to false;
        boolean is24HourView = false;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        //set mIsAm to false
        boolean isAm = true;
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mIsAm", oplusTimePickerCustomClock, isAm);
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner",
                oplusTimePickerCustomClock, mockOplusHourSpinner);
        int time = 23;
        //invoke setCurrentHour()
        oplusTimePickerCustomClock.setCurrentHour(time);
        //verify&assert
        verify(mockOplusHourSpinner).setValue(time%HOURS_IN_HALF_DAY);
        boolean actualIsAm = (boolean) ReflectUtil.getFieldValue(OplusTimePickerCustomClock.class,
                "mIsAm", oplusTimePickerCustomClock);
        assertFalse(actualIsAm);
    }


    @Test
    public void should_call_setValue_with_time_when_setCurrentHour_with_mIs24HourView_is_true_and_time_bigger_than_HOURS_IN_HALF_DAY() throws NoSuchFieldException, IllegalAccessException {
        OplusTimePickerCustomClock oplusTimePickerCustomClock = new OplusTimePickerCustomClock(mContext);
        //set mIs24HourView to true;
        boolean is24HourView = true;
        oplusTimePickerCustomClock.setIs24HourView(is24HourView);
        COUINumberPicker mockOplusHourSpinner = mock(COUINumberPicker.class);
        ReflectUtil.setFieldValue(OplusTimePickerCustomClock.class, "mOplusHourSpinner",
                oplusTimePickerCustomClock, mockOplusHourSpinner);
        int time = 23;
        //invoke setCurrentHour()
        oplusTimePickerCustomClock.setCurrentHour(time);
        //verify
        verify(mockOplusHourSpinner).setValue(time);
    }

}
