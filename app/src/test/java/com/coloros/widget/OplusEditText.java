/************************************************************
 * Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 **
 Description : The Main activity for the Camera application
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-7-23, zhangjinbiao, create
 ***********************************************************/
package com.coloros.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.widget.EditText;
import com.oplus.alarmclock.R;

public class OplusEditText extends EditText {
    public static final int MODE_BACKGROUND_NONE = 0;
    public static final int MODE_BACKGROUND_LINE = 1;
    public static final int MODE_BACKGROUND_RECT = 2;


    public OplusEditText(Context var1) {
        this(var1, null);
    }

    public OplusEditText(Context var1, AttributeSet var2) {
        this(var1,var2, R.attr.background);
    }

    public OplusEditText(Context var1, AttributeSet var2, int var3) {
        this(var1,var2, var3,0);
    }

    public OplusEditText(Context var1, AttributeSet var2, int var3, int var4) {
        super(var1,var2,var3,var4);
    }

    public void setFastDeletable(boolean var1) {
        throw new RuntimeException("stub");
    }

    public boolean isFastDeletable() {
        throw new RuntimeException("stub");
    }

    protected void onFocusChanged(boolean var1, int var2, Rect var3) {
    }

    public void setOnTextDeletedListener(OnTextDeletedListener var1) {
    }

    public void setTextDeletedListener(OnPasswordDeletedListener var1) {
    }

    public boolean onTouchEvent(MotionEvent var1) {
        throw new RuntimeException("stub");
    }

    public void setCompoundDrawables(Drawable var1, Drawable var2, Drawable var3, Drawable var4) {
    }

    public boolean onKeyDown(int var1, KeyEvent var2) {
        return false;
    }

    public void setText(CharSequence var1, BufferType var2) {
    }

    public void forceFinishDetach() {
    }

    public void dispatchStartTemporaryDetach() {
    }

    public boolean isDeleteButtonExist() {
        return false;
    }

    public boolean dispatchHoverEvent(MotionEvent var1) {
        return false;
    }

    public int getDeleteButtonLeft() {
        return -1;
    }

    public void setBoxBackgroundMode(int var1) {
    }

    public void setBoxStrokeColor(int var1) {
    }

    public int getBoxStrokeColor() {
        return 0;
    }

    public void updateLabelState(boolean var1) {
    }

    public void setTopHint(CharSequence var1) {
    }

    public CharSequence getHint() {
        return null;
    }

    public void setHintEnabled(boolean var1) {
    }

    public boolean isHintEnabled() {
        return false;
    }

    public boolean isProvidingHint() {
        return false;
    }

    public void setCollapsedTextAppearance(int var1, ColorStateList var2) {
    }

    public boolean ismHintAnimationEnabled() {
        return false;
    }

    public void setmHintAnimationEnabled(boolean var1) {
    }

    public void draw(Canvas var1) {
    }

    protected void onMeasure(int var1, int var2) {
    }

    protected void onLayout(boolean var1, int var2, int var3, int var4, int var5) {
    }

    public boolean cutoutIsOpen() {
        return false;
    }

    protected void drawableStateChanged() {
    }

    public boolean performClick() {
        return false;
    }


    public interface OnPasswordDeletedListener {
        boolean onPasswordDeleted();
    }

    public interface OnTextDeletedListener {
        boolean onTextDeleted();
    }
}

