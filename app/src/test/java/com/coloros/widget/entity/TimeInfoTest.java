/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimeInfoTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/19     1.0            add file
 ****************************************************************/
package com.coloros.widget.entity;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Test;

public class TimeInfoTest extends TestParent {

    @Test
    public void should_equal_when_equals_with_same_data() {
        TimeInfo timeInfo = new TimeInfo();
        boolean equals = timeInfo.equals(null);
        Assert.assertFalse(equals);

        equals = timeInfo.equals(new String());
        Assert.assertFalse(equals);

        TimeInfo timeInfo1 = new TimeInfo();
        equals = timeInfo.equals(timeInfo1);
        Assert.assertTrue(equals);

        Assert.assertEquals(timeInfo.toString(), timeInfo1.toString());
        Assert.assertEquals(timeInfo.hashCode(), timeInfo1.hashCode());
    }
}