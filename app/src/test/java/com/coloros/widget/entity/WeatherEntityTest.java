package com.coloros.widget.entity;

import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;
import com.oplus.alarmclock.TestParent;

import junit.framework.TestCase;

import org.junit.Assert;
import org.junit.Test;

public class WeatherEntityTest extends TestParent {

    @Test
    public void should_equal_when_equals_with_same_data() {
        WeatherEntity weatherEntity = new WeatherEntity();
        boolean equals = weatherEntity.equals(null);
        Assert.assertFalse(equals);

        equals = weatherEntity.equals(new String());
        Assert.assertFalse(equals);

        WeatherEntity weatherEntity1 = weatherEntity.clone();
        equals = weatherEntity.equals(weatherEntity1);
        Assert.assertTrue(equals);

        Assert.assertEquals(weatherEntity.hashCode(), weatherEntity1.hashCode());

    }
}