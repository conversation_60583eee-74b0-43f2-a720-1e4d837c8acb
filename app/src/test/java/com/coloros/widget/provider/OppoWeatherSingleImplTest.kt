/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OppoWeatherSingleImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.content.Context
import android.widget.RemoteViews
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.commondata.ClockTypeSetting
import com.coloros.widget.smallweather.ClockWidgetManager
import com.oplus.alarmclock.R
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.utils.DeviceUtils
import com.oplus.alarmclock.utils.FoldScreenUtils
import com.oplus.alarmclock.utils.Utils
import com.oplus.font.OplusFontManager
import com.oplus.utils.CommonUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class OppoWeatherSingleImplTest : TestParent() {

    @Test
    fun should_smallestWidgetHeight_with_invalid() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { smallestWidgetHeight() } coAnswers { callOriginal() }
        }
        val result = impl.smallestWidgetHeight()
        verify {
            impl.smallestWidgetHeight()
        }
        Assert.assertEquals(result, 72)
    }

    @Test
    fun should_just_run_widgetType() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { widgetType() } coAnswers { callOriginal() }
        }
        val result = impl.widgetType()
        verify {
            impl.widgetType()
        }
        Assert.assertEquals(result, ClockType.WIDGET_TYPE_HOR)
    }

    @Test
    fun should_layoutDualClockPortrait_with_redone() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutDualClockPortrait() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns true

        val result = impl.layoutDualClockPortrait()
        Assert.assertEquals(result, R.layout.one_line_double_clock_red_widget_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutDualClockPortrait_with_normal() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutDualClockPortrait() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns false
        unmockkStatic(DeviceUtils::class)
        val result = impl.layoutDualClockPortrait()
        Assert.assertEquals(result, R.layout.one_line_double_clock_widget_view_t)
    }

    @Test
    fun should_layoutDualClockLand_with_redone() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutDualClockLand() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns true
        val result = impl.layoutDualClockLand()
        Assert.assertEquals(result, R.layout.one_line_double_clock_red_widget_land_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutDualClockLand_with_normal() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutDualClockLand() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns false
        val result = impl.layoutDualClockLand()
        Assert.assertEquals(result, R.layout.one_line_double_clock_widget_land_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutSingleClockPortrait_with_redone() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutSingleClockPortrait() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns true
        val result = impl.layoutSingleClockPortrait()
        Assert.assertEquals(result, R.layout.one_line_hor_single_clock_red_widget_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutSingleClockPortrait_with_normal() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutSingleClockPortrait() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns false
        val result = impl.layoutSingleClockPortrait()
        Assert.assertEquals(result, R.layout.one_line_hor_single_clock_widget_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutSingleClockLand_with_redone() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutSingleClockLand() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns true
        val result = impl.layoutSingleClockLand()
        Assert.assertEquals(result, R.layout.one_line_hor_single_clock_red_widget_land_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutSingleClockLand_with_normal() {
        mockkStatic(DeviceUtils::class)
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutSingleClockLand() } answers  { callOriginal() }
        }
        every { DeviceUtils.isSupportRedOne() } returns false
        val result = impl.layoutSingleClockLand()
        Assert.assertEquals(result, R.layout.one_line_hor_single_clock_widget_land_view_t)
        unmockkStatic(DeviceUtils::class)
    }

    @Test
    fun should_layoutThemeDualClockPortrait_with_normal() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutThemeDualClockPortrait() } answers  { callOriginal() }
        }
        val result = impl.layoutThemeDualClockPortrait()
        verify {
            impl.layoutThemeDualClockPortrait()
        }
        Assert.assertEquals(result, R.layout.one_line_double_clock_overall_theme_widget_view)
    }

    @Test
    fun should_layoutThemeDualClockLand_with_normal() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutThemeDualClockLand() } answers  { callOriginal() }
        }
        val result = impl.layoutThemeDualClockLand()
        verify {
            impl.layoutThemeDualClockLand()
        }
        Assert.assertEquals(result, R.layout.one_line_double_clock_overall_theme_widget_land_view)
    }

    @Test
    fun should_layoutThemeSingleClockPortrait_with_normal() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutThemeSingleClockPortrait() } answers  { callOriginal() }
        }
        val result = impl.layoutThemeSingleClockPortrait()
        verify {
            impl.layoutThemeSingleClockPortrait()
        }
        Assert.assertEquals(result, R.layout.one_line_single_clock_overall_theme_widget_view)
    }

    @Test
    fun should_layoutThemeSingleClockLand_with_normal() {
        val impl = mockk<OppoWeatherSingleImpl>().apply {
            every { layoutThemeSingleClockLand() } answers  { callOriginal() }
        }
        val result = impl.layoutThemeSingleClockLand()
        verify {
            impl.layoutThemeSingleClockLand()
        }
        Assert.assertEquals(result, R.layout.one_line_single_clock_overall_theme_widget_land_view)
    }

    @Test
    fun should_updateTimeColonMargin_with_os12_dualclock() {
        mockkStatic(Utils::class)
        every { Utils.isAboveOS13() } returns false
        val remoteViews = mockk<RemoteViews>()
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { updateTimeColonMargin(any(), any()) } answers { callOriginal() }
        }
        val result = impl.updateTimeColonMargin(remoteViews, true)
        Assert.assertTrue(result)
        unmockkStatic(Utils::class)
    }

    @Test
    fun should_updateTimeColonMargin_with_os13_dualclock() {
        mockkStatic(Utils::class)
        mockkStatic(OplusFontManager::class)

        val manager = mockk<OplusFontManager>().apply {
            every { isFlipFontUsed } returns true
        }
        every { Utils.isAboveOS13() } returns true
        every { OplusFontManager.getInstance() } returns manager

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { updateTimeColonMargin(any(), any()) } answers { callOriginal() }
        }
        val result = impl.updateTimeColonMargin(remoteViews, true)
        Assert.assertFalse(result)

        unmockkStatic(Utils::class)
        unmockkStatic(OplusFontManager::class)
    }

    @Test
    fun should_updateTimeColonMargin_with_os13_dualclock_no_flipfont() {
        mockkStatic(Utils::class)
        mockkStatic(OplusFontManager::class)

        val manager = mockk<OplusFontManager>().apply {
            every { isFlipFontUsed } returns false
        }
        every { Utils.isAboveOS13() } returns true
        every { OplusFontManager.getInstance() } returns manager

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { isPortrait } returns true
            every { updateTimeColonMargin(any(), any()) } answers { callOriginal() }
        }
        val result = impl.updateTimeColonMargin(remoteViews, true)
        Assert.assertFalse(result)

        every { impl.isPortrait } returns false
        val result2 = impl.updateTimeColonMargin(remoteViews, true)
        Assert.assertFalse(result2)

        unmockkStatic(Utils::class)
        unmockkStatic(OplusFontManager::class)
    }

    @Test
    fun should_updateTimeColonMargin_with_os13_singalclock_no_flipfont() {
        mockkStatic(Utils::class)
        mockkStatic(OplusFontManager::class)

        val manager = mockk<OplusFontManager>().apply {
            every { isFlipFontUsed } returns false
        }
        every { Utils.isAboveOS13() } returns true
        every { OplusFontManager.getInstance() } returns manager

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { isPortrait } returns true
            every { updateTimeColonMargin(any(), any()) } answers { callOriginal() }
        }
        val result = impl.updateTimeColonMargin(remoteViews, false)
        Assert.assertFalse(result)

        every { impl.isPortrait } returns false
        val result2 = impl.updateTimeColonMargin(remoteViews, false)
        Assert.assertFalse(result2)

        unmockkStatic(Utils::class)
        unmockkStatic(OplusFontManager::class)
    }

    @Test
    fun should_widgetElementTextSize_with_dualclock_port() {
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { widgetElementTextSize(any(), any()) } answers { callOriginal() }
        }
        val result = impl.widgetElementTextSize(isDualClock = true, isPortrait = true)
        val actual = mapOf(
            BaseWidgetImpl.WIDGET_ELEMENT_TIME to R.dimen.one_line_double_time_size,
            BaseWidgetImpl.WIDGET_ELEMENT_COLON to R.dimen.one_line_double_time_size,
            BaseWidgetImpl.WIDGET_ELEMENT_DATE to R.dimen.one_line_double_date_weather_size,
            BaseWidgetImpl.WIDGET_ELEMENT_WEATHER to R.dimen.one_line_double_date_weather_size,
            BaseWidgetImpl.WIDGET_ELEMENT_CITY to R.dimen.one_line_double_date_weather_size,
        )
        Assert.assertEquals(result, actual)
    }

    @Test
    fun should_widgetElementTextSize_with_dualclock_land() {
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { widgetElementTextSize(any(), any()) } answers { callOriginal() }
        }
        val result = impl.widgetElementTextSize(isDualClock = true, isPortrait = false)
        val actual = mapOf(
            BaseWidgetImpl.WIDGET_ELEMENT_TIME to R.dimen.one_line_time_hour_txt_sz_land_t,
            BaseWidgetImpl.WIDGET_ELEMENT_COLON to R.dimen.one_line_time_hour_txt_sz_land_t,
            BaseWidgetImpl.WIDGET_ELEMENT_DATE to R.dimen.one_line_date_info_txt_sz,
            BaseWidgetImpl.WIDGET_ELEMENT_WEATHER to R.dimen.one_line_weather_info_txt_sz,
            BaseWidgetImpl.WIDGET_ELEMENT_CITY to R.dimen.one_line_local_city_txt_sz
        )
        Assert.assertEquals(result, actual)
    }

    @Test
    fun should_widgetElementTextSize_with_singalclock_port() {
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { widgetElementTextSize(any(), any()) } answers { callOriginal() }
        }
        val result = impl.widgetElementTextSize(isDualClock = false, isPortrait = true)
        val actual = mapOf(
            BaseWidgetImpl.WIDGET_ELEMENT_TIME to R.dimen.one_line_hor_single_clock_time_font_size_t,
            BaseWidgetImpl.WIDGET_ELEMENT_COLON to R.dimen.one_line_hor_single_clock_colon_font_size_t,
            BaseWidgetImpl.WIDGET_ELEMENT_DATE to R.dimen.one_line_hor_single_clock_weather_font_size2,
            BaseWidgetImpl.WIDGET_ELEMENT_WEATHER to R.dimen.one_line_hor_single_clock_weather_font_size2
        )
        Assert.assertEquals(result, actual)
    }

    @Test
    fun should_widgetElementTextSize_with_singalclock_land() {
        val impl = mockk<OppoWeatherSingleImpl>(relaxUnitFun = true).apply {
            every { widgetElementTextSize(any(), any()) } answers { callOriginal() }
        }
        val result = impl.widgetElementTextSize(isDualClock = false, isPortrait = false)
        val actual = mapOf(
            BaseWidgetImpl.WIDGET_ELEMENT_TIME to R.dimen.one_line_hor_single_clock_time_land_font_size_t,
            BaseWidgetImpl.WIDGET_ELEMENT_COLON to R.dimen.one_line_hor_single_clock_time_land_font_size_t,
            BaseWidgetImpl.WIDGET_ELEMENT_DATE to R.dimen.one_line_hor_single_clock_date_font_size,
            BaseWidgetImpl.WIDGET_ELEMENT_WEATHER to R.dimen.one_line_hor_single_clock_date_font_size
        )
        Assert.assertEquals(result, actual)
    }

    @Test
    fun should_updateRemoteViews_with_dual_clock_normal_size() {
        mockkObjects()
        every { ClockTypeSetting.isDoubleClockType() } returns false
        every { CommonUtil.isPortraitLayoutDirection() } returns true
        every { CommonUtil.isZh(any()) } returns true
        val manager = mockk<ClockWidgetManager>(relaxed = true).apply {
            every { isClockAllOverTheme } returns false
        }
        every { ClockWidgetManager.getInstance() } returns manager

        val mockkContext = mockk<Context>(relaxed = true).apply {
            every { packageName } returns "com.coloros.alarmclock"
        }
        val result = OppoWeatherImpl(mockkContext).updateRemoteViews(true)
        Assert.assertNotNull(result)
        unmockkObjects()
    }

    private fun mockkObjects() {
        mockkStatic(ClockTypeSetting::class)
        mockkStatic(OppoWeatherImpl::class)
        mockkStatic(CommonUtil::class)
        mockkStatic(FoldScreenUtils::class)
        mockkStatic(ClockWidgetManager::class)
        mockkObject(FoldScreenUtils)
        mockkObject(CommonUtil)
    }

    private fun unmockkObjects() {
        unmockkObject(CommonUtil)
        unmockkObject(FoldScreenUtils)
        unmockkStatic(ClockWidgetManager::class)
        unmockkStatic(OppoWeatherImpl::class)
        unmockkStatic(CommonUtil::class)
        unmockkStatic(FoldScreenUtils::class)
        unmockkStatic(ClockTypeSetting::class)
    }
}