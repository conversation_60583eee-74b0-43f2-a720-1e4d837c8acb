/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - OppoWeatherImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/8
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/12/8     1.0            build this module
 ****************************************************************/
package com.coloros.widget.provider

import android.content.Context
import android.graphics.Bitmap
import android.widget.RemoteViews
import com.coloros.widget.smallweather.ClockWidgetManager
import com.oplus.alarmclock.TestParent
import com.oplus.utils.CommonUtil
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class RealmeWeatherImplTest : TestParent() {

    @Test
    fun should_smallestWidgetHeight_with_invalid() {
        val impl = mockk<RealmeWeatherImpl>().apply {
            every { smallestWidgetHeight() } coAnswers { callOriginal() }
        }
        val result = impl.smallestWidgetHeight()
        verify {
            impl.smallestWidgetHeight()
        }
        Assert.assertEquals(result, 0)
    }

    @Test
    fun should_createRemoteViews_witch_normal() {
        val context = mockk<Context>().apply {
            every { packageName } returns  "com.coloros.alarmclock"
        }
        val result = RealmeWeatherImpl(context).createRemoteViews()
        Assert.assertNotNull(result)
    }

    @Test
    fun should_updateRemoteViews_witch_normal() {
        mockkStatic(ClockWidgetManager::class)

        val manager = mockk<ClockWidgetManager>().apply {
            every { localNewWeatherInfo } returns "sun"
            every { curTextColor } returns 0
            justRun { updateTextColor(any()) }
        }
        every { ClockWidgetManager.getInstance() } returns manager

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val mockContext = mockk<Context>(relaxed = true).apply {
            every { packageName } returns  "com.coloros.alarmclock"
        }

        val impl = mockk<RealmeWeatherImpl>().apply {
            every { context } returns mockContext
            every { createRemoteViews() } returns remoteViews
            justRun { updateDataFormat(any(), any()) }
            justRun { setWidgetViewClickEvent(any()) }
            justRun { updateAllChildTextViewColor(any(), any()) }
            every { updateRemoteViews(any()) } answers { callOriginal() }
        }
        val result = impl.updateRemoteViews(false)
        Assert.assertEquals(result, remoteViews)

        unmockkStatic(ClockWidgetManager::class)
    }

    @Test
    fun should_setWidgetViewClickEvent_witch_view() {
        mockkStatic(ClockWidgetManager::class)
        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val mockContext = mockk<Context>(relaxed = true).apply {
            every { packageName } returns  "com.coloros.alarmclock"
        }

        val impl = mockk<RealmeWeatherImpl>().apply {
            every { context } returns mockContext
            every { setWidgetViewClickEvent(any()) } answers { callOriginal() }
        }

        impl.setWidgetViewClickEvent(remoteViews)
        verify {
            impl.setWidgetViewClickEvent(remoteViews)
        }
        unmockkStatic(ClockWidgetManager::class)
    }

    @Test
    fun should_updateAllChildTextViewColor_witch_normal() {
        mockkStatic(ClockWidgetManager::class)

        val bitmap = mockk<Bitmap>()
        val manager = mockk<ClockWidgetManager>().apply {
            every { weatherIconResId } returns 1
            every { haveDateAndWeatherWidget() } returns true
            every { updateDividerLineBitmap() } returns bitmap
        }
        every { ClockWidgetManager.getInstance() } returns manager

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val impl = mockk<RealmeWeatherImpl>().apply {
            every { updateAllChildTextViewColor(any(), any()) } answers { callOriginal() }
        }

        impl.updateAllChildTextViewColor(remoteViews, 0)
        verify {
            impl.updateAllChildTextViewColor(remoteViews, 0)
        }
        unmockkStatic(ClockWidgetManager::class)
    }

    @Test
    fun should_updateDataFormat_witch_normal() {
        mockkStatic(CommonUtil::class)
        mockkObject(CommonUtil)

        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val mockContext = mockk<Context>(relaxed = true).apply {
            every { packageName } returns  "com.coloros.alarmclock"
        }

        every { CommonUtil.isZh(any()) } returns true

        val impl = mockk<RealmeWeatherImpl>().apply {
            every { context } returns mockContext
            justRun { updateDataFormat(any(), any(), any()) }
            every { updateDataFormat(any(), any()) } answers { callOriginal() }
        }
        impl.updateDataFormat(remoteViews, 1)

        unmockkObject(CommonUtil)
        unmockkStatic(CommonUtil::class)
    }

    @Test
    fun should_updateDataFormat_witch_three_param() {
        val remoteViews = mockk<RemoteViews>(relaxUnitFun = true)
        val mockContext = mockk<Context>(relaxed = true).apply {
            every { packageName } returns  "com.coloros.alarmclock"
        }
        val impl = mockk<RealmeWeatherImpl>().apply {
            every { context } returns mockContext
            every { updateDataFormat(any(), any(), any()) } answers { callOriginal() }
        }
        impl.updateDataFormat(remoteViews, 1, "EEE")
    }
}