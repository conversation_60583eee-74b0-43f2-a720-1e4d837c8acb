/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - UtilsShadow.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/2
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/2     1.0            add file
 ****************************************************************/
package com.coloros.widget.smallweather;

import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.utils.Utils;
import com.oplus.alarmclock.utils.Utils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(Utils.class)
public class ShadowUtils {

    public static boolean sAboveOS12 = true;
    @Implementation
    public static boolean isAboveOS12() {
        return sAboveOS12;
    }
}
