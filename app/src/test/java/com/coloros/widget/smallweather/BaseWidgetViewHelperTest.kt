/****************************************************************
 ** Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BaseWidgetViewHelperTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/23
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2024/04/23     1.0            build this module
 ****************************************************************/

package com.coloros.widget.smallweather

import android.content.Context
import android.provider.Settings
import com.coloros.widget.commondata.ClockType
import com.coloros.widget.entity.WeatherEntity
import com.oplus.alarmclock.ReflectUtil
import com.oplus.alarmclock.TestParent
import com.oplus.alarmclock.shadows.ShadowWeatherServiceVersionUtils
import com.oplus.weatherservicesdk.model.SecureSettingsData
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.robolectric.annotation.Config

class BaseWidgetViewHelperTest : TestParent() {

    private val mWidgetViewHelper = BaseWidgetViewHelper.getInstance()

    @Test
    @Ignore
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_null_when_parserWeatherInfo_with_weatherInfo_is_empty() {
        val weatherEntity = ReflectUtil.invoke(BaseWidgetViewHelper::class.java,
                "parserWeatherInfo", arrayOf<Any>(""), mWidgetViewHelper, String::class.java) as WeatherEntity
        Assert.assertNull(weatherEntity)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_weatherEntity_when_parserWeatherInfo_with_weatherInfo_not_empty() {
        val weatherInfo = "26::4::Showers"
        val weatherEntity = ReflectUtil.invoke(BaseWidgetViewHelper::class.java,
                "parserWeatherInfo", arrayOf<Any>(weatherInfo), mWidgetViewHelper, String::class.java) as WeatherEntity
        Assert.assertEquals("26", weatherEntity.degree)
        Assert.assertEquals("4", weatherEntity.type)
        Assert.assertEquals("Showers", weatherEntity.description)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_set_weatherEntry_null_when_setEmptyString_with_WeatherEntity() {
        val weatherEntity = WeatherEntity()
        weatherEntity.degree = "10"
        weatherEntity.type = "3"
        weatherEntity.cityCode = "028"
        ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "setEmptyString",
                arrayOf<Any>(weatherEntity), mWidgetViewHelper, WeatherEntity::class.java)
        Assert.assertEquals("", weatherEntity.description)
        Assert.assertEquals("", weatherEntity.type)
        Assert.assertEquals("", weatherEntity.cityCode)
    }

    @Test
    @Ignore
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_null_when_getLocalWeatherInfo_with_context_is_null() {
        val weatherEntity = ReflectUtil.invoke(BaseWidgetViewHelper::class.java,
                "getLocalWeatherInfo", arrayOf(null), mWidgetViewHelper, Context::class.java) as WeatherEntity
        Assert.assertNull(weatherEntity)
    }

    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_null_when_getLocalWeatherInfo_with_weather_service_not_exist_and_timeZone_is_null() {
        val weatherInfo = "26::4::Showers"
        Settings.Secure.putString(mContext.contentResolver, BaseWidgetViewHelper.OPLUS_RESIDENT_WEATHER_INFO_SETTING, weatherInfo)
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = false
        val weatherEntity = mWidgetViewHelper.getLocalWeatherInfo(mContext)
        Assert.assertNull(weatherEntity)
    }


    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_weatherEntity_with_timezone_correct_when_getResidentWeatherInfo_with_weather_service_not_exist_and_timeZone_not_null() {
        val weatherInfo = "26::4::Showers::108.1::30.99::028::028::8.0"
        Settings.Secure.putString(mContext.contentResolver, BaseWidgetViewHelper.OPLUS_RESIDENT_WEATHER_INFO_SETTING, weatherInfo)
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = false
        val weatherEntity = mWidgetViewHelper.getResidentWeatherInfo(mContext)
        Assert.assertEquals("8.0", weatherEntity.timeZone)
    }

    @Test
    fun should_return_SINGLE_CLOCK_when_getCurClockType_with_isDoubleClockOpened_if_false() {
        val clockType = mWidgetViewHelper.getCurClockType(mContext, false)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
    }

    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_SINGLE_CLOCK_when_getCurClockType_with_local_and_resident_has_relationship() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        val local = WeatherEntity()
        local.cityCode = "100"
        local.parentCityCode = "200"
        val resident = WeatherEntity()
        resident.cityCode = "200"
        resident.parentCityCode = "300"
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        val clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
    }

    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_SINGLE_CLOCK_when_getCurClockType_with_local_or_resident_is_null() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        val local = WeatherEntity()
        val resident = WeatherEntity()
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, null)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        var clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, null)
        clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
    }

    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_SINGLE_CLOCK_when_getCurClockType_with_local_or_resident_is_null_no() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        val local = WeatherEntity()
        val resident = WeatherEntity()
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, null)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        var clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, null)
        clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
    }

    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Ignore
    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_DOUBLE_CLOCK_when_getCurClockType_with_local_and_resident_not_have_relationship() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        val local = WeatherEntity()
        local.cityCode = "100"
        local.parentCityCode = "200"
        val resident = WeatherEntity()
        resident.cityCode = "300"
        resident.parentCityCode = "400"
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        val clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.DOUBLE_CLOCK.toLong(), clockType.toLong())
    }
    @Config(shadows = [ShadowWeatherServiceVersionUtils::class])
    @Ignore
    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_DOUBLE_CLOCK_when_getCurClockType_with_local_and_resident_not_have_relationship_isno() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        val local = WeatherEntity()
        local.cityCode = "100"
        local.parentCityCode = "200"
        val resident = WeatherEntity()
        resident.cityCode = "300"
        resident.parentCityCode = "400"
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        val clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.DOUBLE_CLOCK.toLong(), clockType.toLong())
    }

    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_SINGLE_CLOCK_when_getCurClockType_when_timezone_is_same_below_OS12() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        ShadowUtils.sAboveOS12 = false
        val local = WeatherEntity()
        local.timeZone = "8"
        val resident = WeatherEntity()
        resident.cityCode = "8"
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        val clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
        ShadowUtils.sAboveOS12 = true
    }


    @Test
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun should_return_SINGLE_CLOCK_when_getCurClockType_when_timezone_is_same_below_OS13() {
        ShadowWeatherServiceVersionUtils.mIsCommonWeatherServiceExist = true
        ShadowUtils.sAboveOS12 = false
        val local = WeatherEntity()
        local.timeZone = "8"
        val resident = WeatherEntity()
        resident.cityCode = "8"
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewLocationWeatherEntity", mWidgetViewHelper, local)
        ReflectUtil.setFieldValue(BaseWidgetViewHelper::class.java, "mNewResidentWeatherEntity", mWidgetViewHelper, resident)
        val clockType = mWidgetViewHelper.getCurClockType(mContext, true)
        Assert.assertEquals(ClockType.SINGLE_CLOCK.toLong(), clockType.toLong())
        ShadowUtils.sAboveOS12 = true
    }


    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_weatherEntity_timezone_is_null_when_fillNewWeatherEntityData_with_secureSettingsData_timezone_is_null() {
        val weatherDesc = "snow"
        val secureSettingsData = SecureSettingsData()
        secureSettingsData.timeZone = null
        secureSettingsData.weatherDesc = weatherDesc
        val weatherEntity = WeatherEntity()
        ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "fillNewWeatherEntityData", arrayOf(secureSettingsData, weatherEntity),
                mWidgetViewHelper, SecureSettingsData::class.java, WeatherEntity::class.java)
        Assert.assertEquals("", weatherEntity.timeZone)
        Assert.assertEquals(weatherDesc, weatherEntity.description)
    }


    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isCityChanged_with_newWeatherEntity_and_oldWeatherEntity() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew: WeatherEntity? = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "100"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "200"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew = null
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
    }
    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isCityChanged_with_newWeatherEntity_and_oldWeatherEntity_is_not_null() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew: WeatherEntity? = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "100"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "200"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew = null
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isCityChanged_with_newWeatherEntity_and_oldWeatherEntity2() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew: WeatherEntity? = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "100"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "200"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew = null
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isCityChanged_with_newWeatherEntity_and_oldWeatherEntity2_is_null() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew: WeatherEntity? = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "100"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityOld.cityCode = "100"
        weatherEntityNew.cityCode = "200"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew = null
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isCityChanged", arrayOf<Any?>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isWeatherInfoChanged_with_newWeatherEntity_and_oldWeatherEntity() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.centigrade = "100"
        weatherEntityOld.degree = "20"
        weatherEntityOld.type = "4"
        weatherEntityOld.description = "rain"
        weatherEntityNew.centigrade = "100"
        weatherEntityNew.degree = "20"
        weatherEntityNew.type = "5"
        weatherEntityNew.description = "rain"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew.type = "4"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isWeatherInfoChanged_with_newWeatherEntity_and_oldWeatherEntity_not() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.centigrade = "100"
        weatherEntityOld.degree = "20"
        weatherEntityOld.type = "4"
        weatherEntityOld.description = "rain"
        weatherEntityNew.centigrade = "100"
        weatherEntityNew.degree = "20"
        weatherEntityNew.type = "5"
        weatherEntityNew.description = "rain"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew.type = "4"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
    }

    @Test
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun should_return_correct_changed_when_isWeatherInfoChanged_with_newWeatherEntity_and_oldWeatherEntity_not_null() {
        val weatherEntityOld = WeatherEntity()
        var weatherEntityNew = weatherEntityOld
        var isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
        weatherEntityNew = WeatherEntity()
        weatherEntityOld.centigrade = "100"
        weatherEntityOld.degree = "20"
        weatherEntityOld.type = "4"
        weatherEntityOld.description = "rain"
        weatherEntityNew.centigrade = "100"
        weatherEntityNew.degree = "20"
        weatherEntityNew.type = "5"
        weatherEntityNew.description = "rain"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertTrue(isChange)
        weatherEntityNew.type = "4"
        isChange = ReflectUtil.invoke(BaseWidgetViewHelper::class.java, "isWeatherInfoChanged", arrayOf<Any>(weatherEntityOld, weatherEntityNew),
                mWidgetViewHelper, WeatherEntity::class.java, WeatherEntity::class.java) as Boolean
        Assert.assertFalse(isChange)
    }
}