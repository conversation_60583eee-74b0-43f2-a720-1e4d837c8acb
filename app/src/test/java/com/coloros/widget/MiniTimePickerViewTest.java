/****************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MiniTimePickerViewTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/12/19     1.0            add file
 ****************************************************************/
package com.coloros.widget;

import com.oplus.alarmclock.R;
import com.oplus.alarmclock.ReflectUtil;
import com.oplus.alarmclock.TestParent;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

public class MiniTimePickerViewTest extends TestParent {

    private MiniTimePickerView miniTimePickerView;

    @Override
    public void setUp() throws Exception {
        super.setUp();
        miniTimePickerView = new MiniTimePickerView(mContext);
    }

    @Test
    @Ignore
    public void should_layoutId_correct_when_getLayoutResId() throws NoSuchMethodException, IllegalAccessException {
        int layoutId = (int) ReflectUtil.invoke(MiniTimePickerView.class, "getLayoutResId", new Object[]{}, miniTimePickerView);
        Assert.assertEquals(R.layout.mini_time_picker_custom, layoutId);
    }
}