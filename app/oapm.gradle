apply plugin: 'oapm-perf'
OApmConfig {
    defaultConfig {
        /*autoDependencies {
            //oapm的渠道打开性能检测组件，开发，提测请使用oapm渠道的apk。oapm渠道也可以添加一些测试路径
            variantFilters  'oapmReleaseImplementation', 'oapmDebugImplementation', 'OPPODebugImplementation'
        }*/
        iconEnabled false
        logLevel 5
        soFilters 'arm64-v8a'
        autoDependencies {
            variantFilters "oapmImplementation"
        }
    }
    // 内存监控配置
    memory {
        enabled true
        // 内存泄露监控配置
        leak {
            // 内存泄露监控开关，默认开启
            enabled true
            // 配置发生泄露后是否进行导航栏提示，默认开启提示，接入方希望不进行提示时
            // showTipsOnNav true
            // 内存泄露是否在服务端分析，默认 fasle，若希望服务端分析并下载hprof文件、设为 true 即可
            analysisOnServer true
        }
    }
    startupSpeed {
        //至少需要配置启动页面，否则release会取不到启动堆栈
        launchActivity 'com.oplus.alarmclock.AlarmClock'
    }
}