package com.oplus.alarmclock.utils;

import android.app.Activity;
import android.content.pm.ActivityInfo;

import com.customer.feedback.sdk.FeedbackHelper;
import com.oplus.alarmclock.AlarmClockApplication;
import com.oplus.alarmclock.BaseActivity;
import com.oplus.alarmclock.alarmclock.statement.ClockStatementBottomSheetDialog;
import com.oplus.clock.common.osdk.ContextNativeUtils;
import com.oplus.clock.common.utils.Log;

import org.jetbrains.annotations.NotNull;


public class FeedbackMultiChannelUtil {
    private static final String TAG = "FeedbackMultiChannelUtil";

    public void initFeedbackSDK() {
        Log.d(TAG, "init feedback.");
        FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.CN);
        FeedbackHelper.getInstance(AlarmClockApplication.getInstance()).setNetworkStatusListener(b -> PrefUtils.putBoolean(AlarmClockApplication.getInstance(), ClockStatementBottomSheetDialog.PERMISSION_SP_NAME, ClockStatementBottomSheetDialog.IS_AGREE_PERMISSION, b));
        OpenHelper.INSTANCE.getIdAtFixedRate(new OpenHelper.OnGetIdListener() {
            @Override
            public void onComplete(@NotNull String serviceInfo) {
                Log.d(TAG, "onComplete： " + serviceInfo);
                FeedbackHelper.setId(serviceInfo);
            }

            @Override
            public void onFailed(@NotNull String reason) {
                Log.d(TAG, "onFailed： " + reason);
            }
        });
    }

    public void openFeedbackPage(Activity openActivity) {
        Log.d(TAG, "clicked help and feedback setting");
        if (openActivity == null) {
            Log.e(TAG, "openActivity is null, can't open feedback page");
            return;
        }
        try {
            FeedbackHelper fbHelper = FeedbackHelper.getInstance(openActivity.getApplicationContext());
            if (fbHelper != null) {
                FeedbackHelper.setCredentialProtectedStorageContext(ContextNativeUtils.createCredentialProtectedStorageContext(openActivity));
                FeedbackHelper.setNetworkUserAgree(true);
                if ((openActivity instanceof BaseActivity) && ((BaseActivity) openActivity).isTabletMode()) {
                    fbHelper.setCommonOrientationType(ActivityInfo.SCREEN_ORIENTATION_USER);
                }
                fbHelper.openFeedback(openActivity);
            }
            ClockOplusCSUtils.onCommon(AlarmClockApplication.getInstance(), ClockOplusCSUtils.SETTING_TO_FEEDBACK_CLICK);
        } catch (Exception e) {
            Log.e(TAG, "can't open feedback page,error:" + e);
        }
    }
}
