/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - AlarmSpotifyUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
@file:Suppress("EmptyFunctionBlock", "UnusedPrivateMember", "FunctionOnlyReturningConstant")
package com.oplus.alarmclock.utils

import android.content.Context
import android.graphics.Bitmap
import android.widget.ImageView
import com.oplus.alarmclock.BuildConfig
import com.oplus.alarmclock.alarmclock.AlarmSchedule

object AlarmSpotifyUtils {

    @JvmStatic
    fun isSupport(): Boolean {
        return BuildConfig.IS_SUPPORT_SPOTIFY
    }

    @JvmStatic
    fun isSpotifyRing(ringUri: String?): Boolean {
        return false
    }

    @JvmStatic
    fun isSpotifyRing(ringUri: String?, checkSupport: Boolean?): Boolean {
        return false
    }

    @JvmStatic
    fun play(
        context: Context?,
        uri: String?,
        result: (success: Boolean) -> Unit = {}
    ) {}

    @JvmStatic
    fun setVolume(volume: Float?) {}

    @JvmStatic
    fun stop(context: Context?) {}

    @JvmStatic
    fun loadSpotifyImage(imageUrl: String?, view: ImageView?) {}

    @JvmStatic
    fun getSpotifyImage(imageUrl: String?, callback: (image: Bitmap?) -> Unit) {}

    @JvmStatic
    fun getRingtoneDefaultTips(context: Context?): String? {
        return ""
    }

    @JvmStatic
    fun getRingtoneInfo(): String? {
        return null
    }

    @JvmStatic
    fun getAlertName(context: Context?): String? {
        return null
    }

    @JvmStatic
    fun launchSpotifyService(alarmSchedule: AlarmSchedule?) {}
}