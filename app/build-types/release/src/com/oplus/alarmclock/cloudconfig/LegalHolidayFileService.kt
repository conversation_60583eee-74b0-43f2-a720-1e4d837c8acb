/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - LegalHolidayFileService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/10/10
 ** Author: ZhaoX<PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/10/10     1.0            build this module
 ****************************************************************/
package com.oplus.alarmclock.cloudconfig

import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.stat.Const
import java.io.File

@Config("calendar_legal_holiday", Const.CONFIG_TYPE_FILE)
interface LegalHolidayFileService {
    fun getFile(): Observable<File>
}