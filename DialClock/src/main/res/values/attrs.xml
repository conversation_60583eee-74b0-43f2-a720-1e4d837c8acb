<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="LocalNumberPicker">
        <!--  The divider for making the selection area. -->
        <attr name="selectionDivider" format="reference" />
        <!--  The height of the selection divider. -->
        <attr name="selectionDividerHeight" format="dimension" />
        <!-- The min height of the OplusNumberPicker. -->
        <attr name="internalMinHeight" format="dimension" />
        <!-- The max height of the OplusNumberPicker. -->
        <attr name="internalMaxHeight" format="dimension" />
        <!--  The min width of the OplusNumberPicker. -->
        <attr name="internalMinWidth" format="dimension" />
        <!-- The max width of the OplusNumberPicker. -->
        <attr name="internalMaxWidth" format="dimension" />
        <attr name="numberTextSize" format="dimension" />
        <attr name="offsetHeight" format="dimension" />
        <attr name="clipLength" format="dimension" />
        <attr name="endTextSize" format="dimension" />
        <attr name="startTextSize" format="dimension" />
        <attr name="focusRowHeightDifference" format="dimension" />
        <attr name="isBold" format="boolean" />
        <attr name="focusTextSize" format="dimension" />
        <attr name="couiNormalTextColor" format="color|reference" />
        <attr name="colorFocusTextColor1" format="color" />
        <attr name="couiPickerRowNumber" format="integer" />
        <attr name="couiIsDrawBackground" format="boolean" />
        <attr name="couiPickerVisualWidth" format="dimension" />
        <attr name="couiNOPickerPaddingLeft" format="dimension" />
        <attr name="couiNOPickerPaddingRight" format="dimension" />
        <attr name="solidColor" format="color|reference" />
        <attr name="colorPickerAlignPosition1">
            <enum name="middle" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
        </attr>
    </declare-styleable>
    <declare-styleable name="BoldTextView">
        <attr name="text_weight" format="dimension" />
    </declare-styleable>
    <declare-styleable name="TimerTextView">
        <attr name="timer_multiple" format="float" />
    </declare-styleable>
    <declare-styleable name="DialClockBaseTable">
        <attr name="is_pure_dial" format="boolean" />
    </declare-styleable>
</resources>