<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="OplusNumberPickerDflt">
        <item name="android:solidColor">@android:color/transparent</item>
        <item name="android:background">@null</item>
        <item name="selectionDividerHeight">2dip</item>
        <item name="internalMinHeight">120dip</item>
        <item name="internalMaxHeight">207dp</item>
        <item name="internalMinWidth">80dip</item>
        <item name="internalMaxWidth">120dip</item>
        <item name="numberTextSize">@dimen/timer_picker_text_size</item>
        <item name="startTextSize">@dimen/timer_picker_text_size</item>
        <item name="endTextSize">@dimen/timer_picker_text_size</item>
        <item name="focusTextSize">@dimen/timer_picker_text_size</item>
        <item name="couiNormalTextColor">@color/oplus_number_normal_text_color</item>
        <item name="colorFocusTextColor1">@color/oplus_number_focuse_text_color</item>
        <item name="couiPickerRowNumber">3</item>
        <item name="couiIsDrawBackground">false</item>
        <item name="focusRowHeightDifference">2dip</item>
        <item name="couiPickerVisualWidth">@dimen/timer_picker_width</item>
    </style>

    <style name="MiniNumberPickerDflt" parent="OplusNumberPickerDflt">
        <item name="internalMinHeight">120dip</item>
        <item name="internalMaxHeight">207dp</item>
        <item name="internalMinWidth">60dip</item>
        <item name="internalMaxWidth">60dip</item>
        <item name="numberTextSize">@dimen/text_size_dp_20</item>
        <item name="startTextSize">@dimen/text_size_dp_16</item>
        <item name="endTextSize">@dimen/text_size_dp_16</item>
        <item name="focusTextSize">@dimen/text_size_dp_20</item>
        <item name="couiNormalTextColor">@color/oplus_number_normal_text_color</item>
        <item name="colorFocusTextColor1">@color/clock_theme_color</item>
        <item name="couiPickerVisualWidth">@dimen/timer_picker_width</item>
    </style>

    <style name="timer_dflt_value">
        <item name="timeBackground">@color/background_color</item>
    </style>
</resources>