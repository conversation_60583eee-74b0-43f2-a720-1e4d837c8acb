<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:paddingBottom="24dp"
    tools:ignore="HardcodedText,SpUsage">

    <TextView
        android:id="@+id/timer_custom_time_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="12dp"
        android:text="自定义计时器"
        android:textColor="@color/white"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>