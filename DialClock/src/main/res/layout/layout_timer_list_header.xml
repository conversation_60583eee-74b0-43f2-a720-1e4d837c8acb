<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="HardcodedText,SpUsage">

    <TextView
        android:id="@+id/timer_custom_time_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="16dp"
        android:paddingBottom="16dp"
        android:text="计时器"
        android:textColor="@color/black_transparent_85"
        android:textSize="20dp"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="ss01"
        android:fontVariationSettings="'wght' 1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UnusedAttribute" />
</androidx.constraintlayout.widget.ConstraintLayout>