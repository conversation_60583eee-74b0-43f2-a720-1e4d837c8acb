<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="12dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="12dp"
    android:layout_marginBottom="5dp"
    android:background="@drawable/btn_bg_white_16"
    android:elevation="10dp"
    android:outlineSpotShadowColor="@color/black_transparent_6"
    android:padding="12dp"
    tools:ignore="HardcodedText,SpUsage,UnusedAttribute">

    <TextView
        android:id="@+id/timer_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="刷牙"
        android:textDirection="locale"
        android:textColor="@color/text_black_alpha_60"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/timer_time_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/timer_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="ss01"
        android:text="00:02:00"
        android:textColor="@color/black_transparent_85"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/timer_name_tv"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>