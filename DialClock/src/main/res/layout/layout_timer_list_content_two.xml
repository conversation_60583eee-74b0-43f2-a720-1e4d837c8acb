<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="UnusedAttribute,ContentDescription,HardcodedText,SpUsage" >

    <View
        android:id="@+id/top_view"
        android:layout_width="match_parent"
        android:layout_height="12dp"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/bg_iv"
        android:layout_width="86dp"
        android:layout_height="86dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/bg_timer_list"
        android:elevation="@dimen/layout_dp_10"
        android:outlineSpotShadowColor="@color/black_transparent_6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/top_view" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/timer_num_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="23dp"
        android:elevation="@dimen/layout_dp_10"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="ss01"
        android:text="1"
        android:textColor="@color/black_transparent_85"
        android:textSize="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_iv"
        app:text_weight="@dimen/layout_dp_1" />

    <TextView
        android:id="@+id/timer_unit_tv"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:elevation="@dimen/layout_dp_10"
        android:gravity="center"
        android:maxLines="2"
        android:text="分钟"
        android:textColor="@color/black_transparent_85"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timer_num_tv" />
</androidx.constraintlayout.widget.ConstraintLayout>