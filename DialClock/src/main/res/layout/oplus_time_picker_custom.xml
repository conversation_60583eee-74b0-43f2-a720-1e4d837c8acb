<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/time_pickers"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/layout_dp_16"
    android:layout_marginEnd="@dimen/layout_dp_16"
    android:gravity="center_horizontal"
    android:layoutDirection="ltr"
    android:orientation="horizontal"
    tools:ignore="ResAuto,HardcodedText,SpUsage">

    <com.coui.appcompat.picker.COUINumberPicker
        android:id="@+id/hour"
        android:layout_width="0dp"
        android:layout_height="@dimen/timer_picker_height"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:couiPickerRowNumber="3"
        app:couiPickerVerticalFading="true"
        android:layout_gravity="center_horizontal"
        app:couiPickerDiffusion="4dp"
        app:focusTextSize="@dimen/timer_number_picker_text_size"
        app:startTextSize="@dimen/timer_number_picker_text_size" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/oplus_timepicker_hour_text"
        android:layout_width="@dimen/layout_dp_25"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="ss01"
        android:gravity="center"
        android:text=":"
        android:textColor="@color/colon_color"
        android:textSize="@dimen/timer_number_picker_text_size"
        android:textStyle="bold"
        app:text_weight="@dimen/layout_dp_1_5" />

    <com.coui.appcompat.picker.COUINumberPicker
        android:id="@+id/minute"
        android:layout_width="0dp"
        android:layout_height="@dimen/timer_picker_height"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:couiPickerRowNumber="3"
        app:couiPickerVerticalFading="true"
        android:layout_gravity="center_horizontal"
        app:couiPickerDiffusion="4dp"
        app:focusTextSize="@dimen/timer_number_picker_text_size"
        app:startTextSize="@dimen/timer_number_picker_text_size" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/oplus_timepicker_minute_text"
        android:layout_width="@dimen/layout_dp_25"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="ss01"
        android:gravity="center"
        android:text=":"
        android:textColor="@color/colon_color"
        android:textSize="@dimen/timer_number_picker_text_size"
        android:textStyle="bold"
        app:text_weight="@dimen/layout_dp_1_5" />

    <com.coui.appcompat.picker.COUINumberPicker
        android:id="@+id/second"
        android:layout_width="0dp"
        android:layout_height="@dimen/timer_picker_height"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:couiPickerRowNumber="3"
        app:couiPickerVerticalFading="true"
        android:layout_gravity="center_horizontal"
        app:couiPickerDiffusion="4dp"
        app:focusTextSize="@dimen/timer_number_picker_text_size"
        app:startTextSize="@dimen/timer_number_picker_text_size" />

</LinearLayout>
