<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.oplus.dialclock.view.DialItemView16
        android:id="@+id/dial_world_clock_dv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:forceDarkAllowed="false"
        tools:ignore="UnusedAttribute" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/dial_world_clock_city"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dial_world_city_margin_top_15"
            android:textColor="@color/dial_world_city_text_color_16"
            android:textSize="@dimen/dial_world_clock_city_text_size"
            android:textFontWeight="500"
            android:minHeight="@dimen/dial_world_clock_city_height_15"
            android:gravity="center"
            android:ellipsize="end"
            android:lines="1"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/dial_world_clock_offset_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dial_world_offset_margin_top"
            android:textColor="@color/dial_world_offset_time_text_color_16"
            android:textSize="@dimen/dial_world_clock_offset_time_text_size_15"
            android:minHeight="@dimen/dial_world_clock_offset_height_15"
            android:gravity="center"
            android:lines="1"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/dial_world_clock_offset_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dial_world_offset_margin_top_15"
            android:textColor="@color/dial_world_offset_time_text_color_16"
            android:textSize="@dimen/dial_world_clock_offset_time_text_size_15"
            android:minHeight="@dimen/dial_world_clock_offset_height_15"
            android:gravity="center"
            android:ellipsize="end"
            android:lines="1"
            tools:ignore="SpUsage" />
    </LinearLayout>
</LinearLayout>