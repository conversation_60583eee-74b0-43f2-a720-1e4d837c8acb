<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/dial_world_clock_empty"
    android:gravity="center"
    android:orientation="vertical">

    <com.oplus.dialclock.view.DialAddView
        android:layout_width="@dimen/dial_world_height_15"
        android:layout_height="@dimen/dial_world_height_15" />

    <TextView
        android:id="@+id/dial_world_clock_empty_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:lines="1"
        android:ellipsize="end"
        android:textSize="@dimen/dial_world_empty_text_size"
        android:textStyle="bold"
        android:gravity="center"
        android:minHeight="@dimen/dial_world_clock_empty_height"
        android:textColor="@color/dial_world_city_text_color_16"
        tools:ignore="SpUsage" />
</LinearLayout>
