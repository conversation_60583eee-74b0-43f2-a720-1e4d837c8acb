<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.oplus.dialclock.view.DialAddView
        android:id="@+id/dial_world_clock_region_add"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/dial_world_clock_place_city"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dial_world_city_margin_top"
        android:textSize="@dimen/dial_world_normal_text_size"
        tools:ignore="SpUsage" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/dial_world_clock_place_day_offset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dial_world_offset_margin_top"
            android:textSize="@dimen/dial_world_normal_text_size"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/dial_world_clock_place_time_offset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dial_world_offset_margin_top"
            android:textSize="@dimen/dial_world_normal_text_size"
            tools:ignore="SpUsage" />
    </LinearLayout>

</LinearLayout>
