<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="android.widget.LinearLayout"
    tools:ignore="HardcodedText" >

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/hour_start_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="0" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/hour_end_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="8" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/colon_middle_tv"
        style="@style/stop_watch_text"
        android:text=":"
        app:text_weight="@dimen/layout_dp_1_5" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/minute_start_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="1" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/minute_end_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="5" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/colon_end_tv"
        style="@style/stop_watch_text"
        android:text=":"
        app:text_weight="@dimen/layout_dp_1_5" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/second_start_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="1" />

    <com.oplus.dragonfly.timer.view.BoldTextView
        android:id="@+id/second_end_tv"
        style="@style/stop_watch_text"
        app:text_weight="@dimen/layout_dp_1_5"
        tools:text="1" />
</merge>