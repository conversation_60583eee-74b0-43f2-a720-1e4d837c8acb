<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="6dp"
    android:layout_marginEnd="6dp"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.oplus.dialclock.view.DialAddView
        android:id="@+id/dial_world_clock_region_add"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/dial_world_clock_place_city"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/oos_dial_world_city_margin_top_15"
        android:textSize="@dimen/layout_dp_10"
        android:textFontWeight="600"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/dial_world_clock_place_day_offset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/layout_dp_4"
        android:textFontWeight="500"
        android:textSize="@dimen/layout_dp_10"
        tools:ignore="SpUsage" />

    <TextView
        android:id="@+id/dial_world_clock_place_time_offset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/layout_dp_2"
        android:textSize="@dimen/layout_dp_10"
        android:textFontWeight="500"
        tools:ignore="SpUsage" />
</LinearLayout>
