@file:Suppress("MagicNumber")
package com.oplus.dialclock.util

import android.annotation.SuppressLint
import android.content.ContentProviderClient
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import com.heytap.addon.content.OplusFeatureConfigManager
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils.isFeatureSupport
import com.oplus.dialclock.util.IntentUtils.PKG_OPLUS_CLOCK
import com.oplus.dialclock.util.IntentUtils.PKG_WPLUS_CLOCK
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone
import kotlin.math.min

object ClockUtil {
    const val VERSION_12 = 12
    const val VERSION_13 = 13
    const val VERSION_14 = 14
    const val VERSION_15 = 15
    const val VERSION_16 = 16
    /** OS14上最多显示三个表盘  */
    const val DIAL_CLOCK_MAX_NUM_3 = 3

    /** OS13上最多显示四个表盘  */
    const val DIAL_CLOCK_MAX_NUM_4 = 4
    private const val TAG = "ClockUtil"
    private const val ACTION_PART_TAB_INDEX = "clock_tab_index"
    private const val ACTION_COM_OPLUS_ALARMCLOCK_ALARMCLOCK = "com.oplus.alarmclock.AlarmClock"
    private const val AUTHORITY = "com.oplus.alarmclock.provider.communicate"
    /** 时钟自定义轻量OS feature  */
    private const val FEATURE_LIGHT_OS = "com.oplus.alarmclock.light_weight_os"
    /** 非一加外销轻量OS系统，老版本兼容 */
    private const val FEATURE_LIGHT_OS_OPLUS = "oppo.sys.light.func"
    /**  一加外销轻量OS  */
    private const val FEATURE_LIGHT_OS_OP = "oplus.software.product.oh_light"
    private const val JUMP_TO_ADDCITY = "jump_to_addcity"
    private const val CITY_MAX_SHOW_NUM = 8 //最多显示8个，超过8个则以...显示
    private const val CITY_MAX_SHOW_NUM_FOUR = 4 //最多显示4个，超过4个则以...显示
    private const val STANDARD_VERSION_13 = "13.0.4" //13.0需求基准版本
    private const val STANDARD_VERSION_14 = "14.5.0" //14.0需求基准版本
    private const val STANDARD_VERSION_15 = "15.5.2" //15.0需求基准版本
    private const val STANDARD_VERSION_16 = "16.2.0" //16.0需求基准版本
    private const val CHANGE_CLICK_EFFECT_VERSION = "15.5.18" //世界时钟点击效果切换对应时钟版本
    private const val PERFORMANCE_OPTIMIZATION_VERSION = "15.6.6" //卡顿优化对应时钟版本
    private const val STANDARD_VERSION_LENTH = 3 //基准版本长度
    private const val ELLIPSIS = "..."//省略号
    private val PROCESS_COMMUNICATE_URI = Uri.parse("content://$AUTHORITY")
    private val LOCALE_WHITE_LIST = arrayOf("zh", "ja", "ko") //中文，日文，韩文显示4个
    private var isLightOS: Boolean? = null


    @SuppressLint("WrongConstant")
    fun getContextFromPkg(context: Context, packageName: String): Context? {
        var keyguardContext: Context? = null
        try {
            keyguardContext = context.createPackageContext(
                packageName,
                Context.CONTEXT_INCLUDE_CODE or Context.CONTEXT_IGNORE_SECURITY
                        or Context.MODE_MULTI_PROCESS
            )
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "get $packageName KeyguardContext fail")
        }
        return keyguardContext
    }

    @JvmStatic
    fun getContextFromClock(context: Context): Context? {
        return getContextFromPkg(context, PKG_OPLUS_CLOCK) ?: getContextFromPkg(context, PKG_WPLUS_CLOCK)
    }

    fun getClockResourceId(context: Context, name: String, defType: String): Int {
        return context.resources?.getIdentifier(name, defType, context.packageName) ?: -1
    }

    fun getClockString(context: Context, id: Int, vararg formatArgs: Any): String? {
       return if (id > -1) {
           getContextFromPkg(context, context.packageName)?.resources?.getString(id, formatArgs)
        } else null
    }

    @JvmStatic
    private fun getClockVersion(context: Context): String? {
        val pm: PackageManager = context.packageManager
        return try {
            pm.getPackageInfo(context.packageName, PackageManager.GET_META_DATA).versionName
        } catch (e: Exception) {
            Log.e(TAG, "getClockVersion Exception", e)
            null
        }
    }

    /**
     * 是否是切换点击效果的时钟版本
     */
    @JvmStatic
    fun isChangeClickEffectClockVersion(context: Context): Boolean {
        val clockContext: Context? = getContextFromClock(context)
        val clockVersion: String? = clockContext?.let { getClockVersion(it) }
        return clockVersion?.let { isLargeVersion(it, CHANGE_CLICK_EFFECT_VERSION) } ?: false
    }

    /**
     * 是否是卡顿优化的时钟版本
     */
    @JvmStatic
    fun isPerformanceOptimizationClockVersion(context: Context): Boolean {
        val clockContext: Context? = getContextFromClock(context)
        val clockVersion: String? = clockContext?.let { getClockVersion(it) }
        return clockVersion?.let { isLargeVersion(it, PERFORMANCE_OPTIMIZATION_VERSION) } ?: false
    }

    @JvmStatic
    @Suppress("NestedBlockDepth", "LoopWithTooManyJumpStatements")
    fun getDialVersion(context: Context): Int {
        var dialVersion: Int = VERSION_12
        val clockContext: Context? = getContextFromClock(context)
        val clockVersion: String? = clockContext?.let { getClockVersion(it) }
        try {
            clockVersion?.let {
                if (isLargeVersion(it, STANDARD_VERSION_16)) {
                    dialVersion = VERSION_16
                } else if (isLargeVersion(it, STANDARD_VERSION_15)) {
                    dialVersion = VERSION_15
                } else if (isLargeVersion(it, STANDARD_VERSION_14)) {
                    dialVersion = VERSION_14
                } else if (isLargeVersion(it, STANDARD_VERSION_13)) {
                    dialVersion = VERSION_13
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getDialVersion Exception", e)
        }
        Log.d(TAG, "getDialVersion:$dialVersion")
        return dialVersion
    }

    @JvmStatic
    @Suppress("NestedBlockDepth", "LoopWithTooManyJumpStatements")
    private fun isLargeVersion(targetVersion: String, standardVersion: String): Boolean {
        var isLargeVersion = false
        val targetArr = targetVersion.split(".", "_", ",")
        val standardArr = standardVersion.split(".")
        for (i in 0 until min(targetArr.size, standardArr.size)) {
            val targetNumber = targetArr[i].toInt()
            val standardNumber = standardArr[i].toInt()
            if (targetNumber == standardNumber) {
                isLargeVersion = if (targetArr.size < standardArr.size) {
                    if (i == targetArr.size - 1) {
                        false
                    } else {
                        continue
                    }
                } else {
                    if (i == standardArr.size - 1) {
                        true
                    } else {
                        continue
                    }
                }
            } else if (targetNumber < standardNumber) {
                isLargeVersion = false
                break
            } else if (targetNumber > standardNumber) {
                isLargeVersion = true
                break
            }
        }
        return isLargeVersion
    }

    /**
     * 根据显示的大小获取合适的文字显示
     *
     * @param content  字符串内容
     * @param ellipsis 超出最大宽度时尾部显示的符号 "xxx..."
     * @return 适配后的字符串内容
     */
    @JvmStatic
    fun getAdapterSizeText(content: String, ellipsis: String = ELLIPSIS): String {
        val maxNum = if (LOCALE_WHITE_LIST.contains(Locale.getDefault().language)) {
            CITY_MAX_SHOW_NUM_FOUR
        } else CITY_MAX_SHOW_NUM
        if (content.length > maxNum) {
            return content.substring(0, maxNum) + ellipsis
        }
        return content
    }

    fun getTimeZoneOffset(context: Context, targetTimezone: String): String {
        val clockContext = getContextFromClock(context) ?: return ""
        val stringArray = clockContext.resources.getIdentifier("global_timezone_day_offset", "array", clockContext.packageName)
        val dayOffsets = clockContext.resources.getStringArray(stringArray)
        val time = Calendar.getInstance(TimeZone.getTimeZone(targetTimezone))
        val time2 = Calendar.getInstance(TimeZone.getDefault())
        var dayoffset = time2[Calendar.DAY_OF_WEEK] - time[Calendar.DAY_OF_WEEK]
        if (dayoffset == Calendar.SUNDAY - Calendar.SATURDAY) {
            dayoffset = 1
        }
        if (dayoffset == Calendar.SATURDAY - Calendar.SUNDAY) {
            dayoffset = -1
        }
        return dayOffsets[dayoffset + 1]
    }

    fun startAlarmByAction(context: Context, index: Int) {
        val enterApp = Intent(ACTION_COM_OPLUS_ALARMCLOCK_ALARMCLOCK)
        enterApp.addFlags(
            Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    or Intent.FLAG_ACTIVITY_SINGLE_TOP
        )
        enterApp.putExtra(ACTION_PART_TAB_INDEX, index)
        enterApp.setPackage(getContextFromClock(context)?.packageName)
        try {
            context.startActivity(enterApp)
        } catch (e: Exception) {
            Log.e(TAG, "startAlarmByAction error:" + e.message)
        }
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun jumpToAddCityActivity(context: Context) {
        var providerClient: ContentProviderClient? = null
        try {
            providerClient = context.contentResolver.acquireContentProviderClient(PROCESS_COMMUNICATE_URI)
            providerClient?.run {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    call(AUTHORITY, JUMP_TO_ADDCITY, null, Bundle())
                } else {
                    call(JUMP_TO_ADDCITY, null, Bundle())
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "jumpToAddCityActivity error:" + e.message)
        } finally {
            if (providerClient != null) {
                providerClient.release()
                providerClient.close()
            }
        }
    }

    /**
     * 当前点击位置是否在view内，都使用绝对位置,分屏下这个方法好使
     *
     * @param event
     * @param view
     * @return
     */
    @JvmStatic
    fun isViewContains(event: MotionEvent?, view: View?): Boolean {
        return if (event == null || view == null) {
            false
        } else {
            val location = IntArray(2)
            view.getLocationOnScreen(location)
            val startX = location[0]
            val endX = startX + view.width
            val startY = location[1]
            val endY = startY + view.height
            val eventX = event.rawX.toInt()
            val eventY = event.rawY.toInt()
            !(eventX < startX || eventX > endX || eventY < startY || eventY > endY)
        }
    }

    /**
     * 判断是否一加外销
     */
    @JvmStatic
    fun isWplus(context: Context?): Boolean {
        runCatching {
            context?.let {
                return getContextFromClock(it)?.packageName == PKG_WPLUS_CLOCK
            }
        }

        return false
    }

    /**
     * 获取轻量OS
     */
    @JvmStatic
    fun asyncSupportLightOS(context: Context?) {
        if (context == null) {
            Log.e(TAG, "async support light os context null!")
            return
        }
        kotlin.runCatching {
            if (isFeatureSupport(context.contentResolver, FEATURE_LIGHT_OS)) {
                isLightOS = true
            } else if (context.packageManager.hasSystemFeature(FEATURE_LIGHT_OS_OPLUS)) {
                isLightOS = true
            } else if (OplusFeatureConfigManager.getInstance(context).hasFeature(FEATURE_LIGHT_OS_OP)) {
                isLightOS = true
            }
        }
    }

    /**
     * 是否是轻量OS
     */
    @JvmStatic
    fun isLightOS(): Boolean {
        Log.e(TAG, "isLightOS:$isLightOS")
        return isLightOS ?: false
    }
}