/*********************************************************************************
 ** Copyright (C) 2021 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - CouiSupportUtil.kt.java
 ** Description:暗色模式和全局主题色适配
 **
 ** Version: 1.0
 ** Date: 2021-06-18
 ** Author: qdq
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>

 ** ------------------------------------------------------------------------------
 ** qdq                         2021-06-18          1.0     Create this module
 ********************************************************************************/
package com.oplus.dialclock.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.content.res.TypedArray
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.Typeface.Builder
import android.provider.Settings
import android.util.Log
import java.util.Locale

object CouiSupportUtil {

    private const val FORTY_EIGHT = 48
    private const val THIRTY_TWO = 32
    private const val TWELVE = 12
    private const val TAG_ATTR = "attr"
    private const val TAG = "CouiSupportUtil"
    private const val FONT_TYPE = "sys-sans-en"
    private const val FONT_VARIATION_SETTINGS = "font_variation_settings"
    private const val DEFAULT_FONT_PATH = "/system/fonts/SysFont-Regular.ttf"
    private const val ROBOTO_FONT_KEY = "Roboto:"
    private const val ROBOTO_FONT_PATH = "/system/fonts/Roboto-Regular.ttf"
    private const val SYS_SANS_FONT_KEY = "SysSans:"
    private const val SYS_SANS_FONT_PATH = "/system/fonts/SysSans-En-Regular.ttf"
    private const val OP_SANS_FONT_KEY = "OPSans:"
    private const val OP_SANS_FONT_PATH = "/system/fonts/OPSans-En-Regular.ttf"
    private const val TEXT_WEIGHT = "'wght' %d"
    private const val DEFAULT_FONT_VARIATION_SETTINGS = 550
    private const val FONT_VARIATION_STATUS = 0x00000f000
    /** roboto字体类型 0
     * oppo sans字体类型 1,2
     * one sans字体类型 3
     */
    private const val ROBOTO_FONT_TYPE = 0
    private const val OP_SANS_FONT_TYPE = 3
    private var sSansENTypefaceRegular: Typeface? = null
    private var sSansENTypefaceBold: Typeface? = null
    private val sSansEnTypefaceWeight: HashMap<String, Typeface> = HashMap()
    private val SUPPORT_FONT_VARIATION_LIST: List<String> = arrayListOf(
        "en",  //英语
        "zh" //中文
    )
    fun getAttibuteColor(context: Context?, resName: String): ColorStateList {
        val id = getResourceId(
            context,
            resName,
            TAG_ATTR
        )
        if (id == Resources.ID_NULL) {
            Log.d(TAG, "getAttibuteColor id null")
            return ColorStateList.valueOf(Color.GREEN)
        }
        var typedArray: TypedArray? = null
        try {
            typedArray = context?.obtainStyledAttributes(intArrayOf(id))
            if (typedArray != null) {
                val csl = typedArray.getColorStateList(0)
                if (csl != null) {
                    return csl
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getAttibuteColor e", e)
        } finally {
            typedArray?.recycle()
        }
        return ColorStateList.valueOf(Color.GREEN)
    }

    private fun getResourceId(context: Context?, resName: String, type: String): Int {
        if ((context == null) || (resName.isEmpty())) {
            Log.d(TAG, "getResourceId 0 context=$context resName=$resName")
            return 0
        }
        Log.d(TAG, "getResourceId packageName=${context.packageName} type=$type resName=$resName")
        return context.resources.getIdentifier(resName, type, context.packageName)
    }

    fun isNightMode(context: Context): Boolean {
        val configuration = context.resources.configuration
        val currentNightMode = configuration.uiMode and FORTY_EIGHT
        return THIRTY_TWO == currentNightMode
    }

    @JvmStatic
    fun getSansEnTypeface(isBold: Boolean): Typeface? {
        return if (isBold) {
            if (isUseSansEN(sSansENTypefaceBold)) {
                try {
                    sSansENTypefaceBold = Typeface.create(
                        FONT_TYPE, Typeface.BOLD)
                    Log.d(TAG, "SysSans-En-Medium")
                } catch (e: RuntimeException) {
                    Log.w(TAG, "Create Typeface from /system/fonts/SysSans-En-Medium.otf failed!")
                    sSansENTypefaceBold = Typeface.DEFAULT_BOLD
                }
            }
            Log.d(TAG, "get bold typeface sSansENTypefaceBold:$sSansENTypefaceBold")
            sSansENTypefaceBold
        } else {
            if (isUseSansEN(sSansENTypefaceRegular)) {
                try {
                    sSansENTypefaceRegular = Typeface.create(
                        FONT_TYPE, Typeface.NORMAL)
                    Log.d(TAG, "SysSans-En-Regular")
                } catch (e: RuntimeException) {
                    Log.w(TAG, "Create Typeface from /system/fonts/SysSans-En-Regular.otf failed!")
                    sSansENTypefaceRegular = Typeface.DEFAULT
                }
            }
            Log.d(TAG, "get normal typeface sSansENTypefaceRegular:$sSansENTypefaceRegular")
            sSansENTypefaceRegular
        }
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun getNumberTypeface(context: Context, isBold: Boolean, checkWght: Int): Typeface? {
        val fontVariationSettings: Int = Settings.System.getInt(context.contentResolver, FONT_VARIATION_SETTINGS, DEFAULT_FONT_VARIATION_SETTINGS)
        //int类型，第4位代表状态
        val fontVariationStatus = ((fontVariationSettings) and FONT_VARIATION_STATUS) shr TWELVE
        /*      int类型，后三位代表粗细度 wght
                val fontVariationValues = ((fontVariationSettings) and 0x000000fff)*/
        if (fontVariationStatus > OP_SANS_FONT_TYPE) {
            //三方字体
            return getSansEnTypeface(isBold)
        } else {
            val key = SYS_SANS_FONT_KEY.plus(checkWght)
            if (sSansEnTypefaceWeight[key] == null) {
                val wght = TEXT_WEIGHT.format(Locale.US, checkWght)
                kotlin.runCatching {
                    val weightTypeface = Builder(SYS_SANS_FONT_PATH).setFontVariationSettings(wght).build()
                    Log.d(TAG, "Create Typeface from /system/fonts/SysSans-En-Regular.ttf wght:$checkWght success!")
                    sSansEnTypefaceWeight[key] = weightTypeface
                    return weightTypeface
                }.getOrElse {
                    Log.e(TAG, "Create Typeface wght:$checkWght failed!")
                    return Builder(DEFAULT_FONT_PATH).setFontVariationSettings(wght).build()
                }
            } else {
                Log.d(TAG, "get typeface from cache key:$key")
                return sSansEnTypefaceWeight[key]
            }
        }
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun getSansEnTypeface(context: Context, isBold: Boolean, checkWght: Int): Typeface? {
        val fontVariationSettings: Int = Settings.System.getInt(context.contentResolver, FONT_VARIATION_SETTINGS, DEFAULT_FONT_VARIATION_SETTINGS)
        //int类型，第4位代表状态
        val fontVariationStatus = ((fontVariationSettings) and FONT_VARIATION_STATUS) shr TWELVE
/*      int类型，后三位代表粗细度 wght
        val fontVariationValues = ((fontVariationSettings) and 0x000000fff)*/
        if (fontVariationStatus > OP_SANS_FONT_TYPE) {
            //三方字体
            return getSansEnTypeface(isBold)
        } else {
            val key = when (fontVariationStatus) {
                ROBOTO_FONT_TYPE -> ROBOTO_FONT_KEY.plus(checkWght)
                OP_SANS_FONT_TYPE -> OP_SANS_FONT_KEY.plus(checkWght)
                else -> SYS_SANS_FONT_KEY.plus(checkWght)
            }
            if (sSansEnTypefaceWeight[key] == null) {
                val wght = TEXT_WEIGHT.format(Locale.US, checkWght)
                kotlin.runCatching {
                    val weightTypeface = when (fontVariationStatus) {
                        ROBOTO_FONT_TYPE -> {
                            Log.d(TAG, "Create Typeface from /system/fonts/Roboto-Regular.ttf wght:$checkWght success!")
                            Builder(ROBOTO_FONT_PATH).setFontVariationSettings(wght).build()
                        }
                        OP_SANS_FONT_TYPE -> {
                            Log.d(TAG, "Create Typeface from /system/fonts/OPSans-En-Regular.ttf wght:$checkWght success!")
                            Builder(OP_SANS_FONT_PATH).setFontVariationSettings(wght).build()
                        }
                        else -> {
                            Log.d(TAG, "Create Typeface from /system/fonts/SysSans-En-Regular.ttf wght:$checkWght success!")
                            Builder(SYS_SANS_FONT_PATH).setFontVariationSettings(wght).build()
                        }
                    }
                    Log.d(TAG, "Create Typeface success!")
                    sSansEnTypefaceWeight[key] = weightTypeface
                    return weightTypeface
                }.getOrElse {
                    Log.e(TAG, "Create Typeface wght:$checkWght failed!")
                    return Builder(DEFAULT_FONT_PATH).setFontVariationSettings(wght).build()
                }
            } else {
                Log.d(TAG, "get typeface from cache key:$key")
                return sSansEnTypefaceWeight[key]
            }
        }
    }

    private fun isUseSansEN(type: Typeface?): Boolean {
        return (type == null) && isCurrentLanguageSupportVariationFont()
    }

    private fun isCurrentLanguageSupportVariationFont(): Boolean {
        val currentLocale = Locale.getDefault()
        return SUPPORT_FONT_VARIATION_LIST.contains(currentLocale.language)
    }
}