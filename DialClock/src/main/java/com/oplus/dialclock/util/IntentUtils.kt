/************************************************************
 * Copyright 2000-2016 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 *
 * FileName       : IntentUtils.java
 * Version Number : 1.0
 * Description    : Intent util
 * Author         : an.wang
 * Date           : 2020-03-10
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2020-03-10, WangAn, create
 */
package com.oplus.dialclock.util

object IntentUtils {
    const val INDEX_2 = 2
    const val INDEX_4 = 4
    const val INDEX_8 = 8
    const val INDEX_11 = 11
    const val INDEX_12 = 12
    const val INDEX_13 = 13
    const val INDEX_14 = 14
    const val INDEX_15 = 15
    const val INDEX_17 = 17
    const val INDEX_18 = 18
    const val INDEX_20 = 20
    const val INDEX_40 = 40
    const val INDEX_45 = 45

    val CHARS = charArrayOf(
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',  // 0-9
        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',  // 10-19
        'u', 'v', 'w', 'x', 'y', 'z',  // 20-25
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',  // 26-35
        'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',  // 36-45
        'U', 'V', 'W', 'X', 'Y', 'Z'
    ) // 46-51
    private val O = String(
        charArrayOf(
            CHARS[INDEX_2],
            CHARS[INDEX_14],
            CHARS[INDEX_11],
            CHARS[INDEX_14],
            CHARS[INDEX_17],
            CHARS[INDEX_14],
            CHARS[INDEX_18]
        )
    )
    private val W = String(
        charArrayOf(
            CHARS[INDEX_14],
            CHARS[INDEX_13],
            CHARS[INDEX_4],
            CHARS[INDEX_15],
            CHARS[INDEX_11],
            CHARS[INDEX_20],
            CHARS[INDEX_18]
        )
    )
    private val P = String(
         charArrayOf(
             CHARS[INDEX_40],
             CHARS[INDEX_15],
             CHARS[INDEX_15],
             CHARS[INDEX_14],
             CHARS[INDEX_45],
             CHARS[INDEX_8],
             CHARS[INDEX_12],
             CHARS[INDEX_4],
             CHARS[INDEX_17]
         )
    )
    val LOW_TIMER_ROOT = P
    val PKG_OPLUS_CLOCK = "com." + O + ".alarmclock"
    val PKG_WPLUS_CLOCK = "com." + W + ".deskclock"
}