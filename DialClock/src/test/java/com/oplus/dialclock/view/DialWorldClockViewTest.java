package com.oplus.dialclock.view;

import android.annotation.SuppressLint;
import android.app.Application;

import com.oplus.dialclock.model.DialWorldClockModel;
import com.oplus.dialclock.view.DialWorldClockView;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import java.lang.reflect.Field;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28, manifest=Config.NONE)
public class DialWorldClockViewTest {
    private DialWorldClockView mDialWorldClockView;
    private Application mApplication;

    @Before
    public void setUp() {
        mApplication = RuntimeEnvironment.application;
        mDialWorldClockView = new DialWorldClockView(mApplication);
    }

    @SuppressLint("IgnoreWithoutReason")
    @Test
    @Ignore
    public void should_verify_when_setData_with_out() {
        DialWorldClockView dialWorldClockView = Mockito.mock(DialWorldClockView.class);
        DialWorldClockModel dialWorldClockModel = Mockito.mock(DialWorldClockModel.class);
        dialWorldClockView.setData(dialWorldClockModel);
        Mockito.verify(dialWorldClockView).setData(Mockito.any());
    }

    @Test
    public void should_equal_when_update_with_out() throws Exception {
        setNightModel(true);
        mDialWorldClockView.update(null, null);
        Assert.assertTrue(isNightModel());

        setNightModel(false);
        mDialWorldClockView.update(null, null);
        Assert.assertTrue(!isNightModel());
    }

    private boolean isNightModel() throws Exception {
        Field field = mDialWorldClockView.getClass().getDeclaredField("mIsNightMode");
        field.setAccessible(true);
        return (boolean) field.get(mDialWorldClockView);
    }

    private void setNightModel(boolean isNight) throws Exception {
        Field field = mDialWorldClockView.getClass().getDeclaredField("mIsNightMode");
        field.setAccessible(true);
        field.set(mDialWorldClockView, isNight);
    }
}
