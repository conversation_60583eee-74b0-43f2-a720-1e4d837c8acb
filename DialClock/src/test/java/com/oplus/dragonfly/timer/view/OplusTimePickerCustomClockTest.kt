/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - OplusTimePickerCustomClockTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.dragonfly.timer.view

import com.oplus.BaseTest
import io.mockk.every
import io.mockk.mockk
import org.junit.Test

class OplusTimePickerCustomClockTest : BaseTest() {

    @Test
    fun test_oplus_time_picker_custom_clock() {
        val timePicker = mockk<OplusTimePickerCustomClock>()
        every { timePicker.time = any() } returns Unit
        every { timePicker.setIsCountDown(any()) } returns Unit
        every { timePicker.setIs24HourView(any()) } returns Unit
        every { timePicker.isEnabled = any() } returns Unit
        timePicker.run {
            time = 100
            setIsCountDown(false)
            setIs24HourView(false)
            time = 222
            isEnabled = true
            isEnabled = false
        }
    }
}