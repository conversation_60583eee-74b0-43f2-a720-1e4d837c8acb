/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - TimerTextViewTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.dragonfly.timer.view

import com.oplus.BaseTest
import org.junit.Test

class TimerTextViewTest : BaseTest() {

    @Test
    fun test_timer_text_view() {
        val timerTextView = TimerTextView(mContext)
        timerTextView.setThemeColor(0)
        timerTextView.update(0, 0)
    }
}