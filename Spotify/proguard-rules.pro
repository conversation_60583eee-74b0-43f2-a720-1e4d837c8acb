# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
-ignorewarnings

# The remainder of this file is identical to the non-optimized version
# of the Proguard configuration file (except that the other file has
# flags to turn off optimization).

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

-keepattributes *Annotation*
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.app.Fragment
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class com.oplus.statistics.** { *; }
-keep public class env.** { *; }
-keep public class com.oplus.romupdate.** { *; }
-keep public class android.support.v4.** { *; }
-keep public class com.oplus.common.** { *; }
-keep public class com.oplus.backup.** { *; }
-keep public class com.oplus.backup.sdk.** { *; }
-keep public class com.oplus.callandmessage.** { *; }
-keep public class com.oplus.calendarfeast.** { *; }
-keep public class com.oplus.alarmclock.backup.** { *; }
-keep public class com.coloros.refusedesktop.view.** { *; }
-keep public class com.coloros.refusedesktop.model.** { *; }

-ignorewarnings
-keep class com.oplus.smartsdk.**{ *; }
-keep class com.oplus.smartengine.SmartViewImpl{ *; }
-keep class com.oplus.annotation.**{ *; }
-keep class com.oplus.annotationcompiler.**{ *; }
-keep class androidx.** { *; }
-keep class kotlinx.** { *; }
-keep class kotlin.** { *; }

-keep class com.oplus.cardwidget.proto.** {*;}

-keep class com.google.protobuf.** {*;}

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.

# For weather service sdk
-keep class com.oplus.servicesdk.** { *; }

# For behavior
-keep public class com.oplus.alarmclock.behavior.** {*;}

#os7.0 supportgit lo
-keep class com.oplus.compat.**{*;}
# globalclock add city list anim
-keep class com.oplus.alarmclock.globalclock.view.**{*;}


# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { *; }

# morning.model
-keep class com.oplus.alarmclock.Morning.model.**{*;}


# network.model
-keep class com.oplus.alarmclock.network.Model.**{*;}

-keep class com.coui.appcompat.widget.COUISearchView** {*;}

 -useuniqueclassmembernames

 #### 2020 - 05 - 06
 -keep class android.os.OplusKeyEventManager { *; }
 -keep interface android.os.OplusKeyEventManager$OnKeyEventObserver { *; }

 #OplusSmallWeatherWidget start 2020.6.9

 #---------------------------------Third Party jar Area-----------------------------

 # com.oplus.statistics.jar
 -keep class com.oplus.statistics.** { *; }

 # bitmapinfoutils-1.0.0.jar
 -keep class com.oplus.launcher.bitmapinfoutils.** { *; }

 # OplusUIEngine-1.0.0.jar
 -keep class com.oplus.uiengine.** { *; }

#---------------------------------Customization Area-------------------------------

-keep class com.oplus.widget.smallweather.SmallWeatherWidgetFrameHost {
    public <init>(***);
    public java.lang.Object onCommand(int, int[], java.lang.Object);
}
-keep class com.oplus.widget.smallweather.NormalWidgetUtil {
    public java.lang.Object onCommand(int, int[], java.lang.Object);
}
-keep class com.oplus.realweather.AutoLocationUtils {
    public java.lang.Object onCommand(int, int[], java.lang.Object);
}
-keep class com.coloros.widget.engine.IEngineWidgetCtrl {
  *;
}
-keep class com.oplus.servicesdk.** { *; }
-keep class com.oplus.weatherservicesdk.** { *; }
-keep class com.oplus.compat.**{*;}
-useuniqueclassmembernames

 #OplusSmallWeatherWidget end 2020.6.9

# appPlatform

-dontwarn com.android.mkstubs.**
-dontwarn com.oplus.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**

-keep class mirror.android.** {*;}


-keep class com.oplus.reflect.** {*;}
-keep class com.oplus.utils.reflect.** {*;}
#-keep class com.oplus.compat.** {*;}
-keep class com.heytap.reflect.** {*;}
-keepclassmembers class com.heytap.compat.**.*Native {
    static com.heytap.reflect.Ref* *;
}
-keepclassmembers class com.heytap.compat.**.*Native$* {
    static com.heytap.reflect.Ref* *;
}

-keep class com.heytap.addon.os.**{*;}
-keep class android.os.IOplusExInputCallBack {*; }
-keep class android.os.IOplusExService {*; }
-keep class com.heytap.addon.confinemode.**{*;}

 -keep class android.os.ColorKeyEventManager { *; }
 -keep interface android.os.ColorKeyEventManager$OnKeyEventObserver { *; }
-keep class androidx.appcompat.widget.**{*;}

 # feedback sdk
 -keep public class com.customer.feedback.sdk.**{*;}
 -keep @androidx.anntotation.Keep class **
-keep @com.oplus.baselib.database.annotation.DbEntity class * {*;}

-keepclassmembers class * {
   @com.oplus.nearx.cloudconfig.anotation.FieldIndex *;
 }

-keep @androidx.anntotation.Keep class **

# api-adapter
-dontwarn com.oplus.inner.**
-dontwarn com.oplus.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**
-dontwarn com.oplus.compat.**
-dontwarn com.oplus.epona.internal.LoggerSnapshot
-dontwarn com.oplus.epona.internal.LoggerSnapShotOplusCompat
-dontwarn android.telephony.**

-keep class mirror.android.** {*;}
-keep class com.oplus.utils.reflect.** {*;}
-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}
-keep class com.oplus.os.OplusBuild {*;}
-keep class com.oplus.os.OplusBuild.** {*;}

-keep class com.heytap.epona.ExceptionInfo {*;}

-keepclassmembers class com.oplus.compat.**.*Native {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Native$* {
 static com.oplus.utils.reflect.Ref* *;
}

-keepclassmembers class com.oplus.compat.**.*Compat {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat$* {
 static com.oplus.utils.reflect.Ref* *;
}

-keep class com.oplus.compat.app.ActivityManagerNative$ProcessObserver { *;}
-keep class com.oplus.compat.app.ActivityManagerNative$PackageDataObserver{ *;}
-keep public class com.oplus.compat.fingerprint.FingerprintNative { *; }
-keep public class com.oplus.compat.app.StatusBarManagerNative { *; }

#epona
-keepattributes *Annotation*
-keep @com.oplus.epona.annotation.Provider class * {*;}
-keep @interface com.oplus.epona.annotation.Provider
-keepclassmembers class *{
    @com.oplus.epona.annotation.Provider *;
}
-keep @com.oplus.epona.annotation.Action class * {*;}
-keep @interface com.oplus.epona.annotation.Action
-keepclassmembers class * {
    @com.oplus.epona.annotation.Action *;
}

-dontwarn com.squareup.javapoet.**
-dontwarn com.google.common.reflect.**
-dontwarn com.google.auto.service.processor.**
-dontwarn com.google.auto.common.**
-dontwarn com.oplus.epona.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.common.util.concurrent.**
-dontwarn com.squareup.javapoet.**

-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}
-keep class com.oplus.os.OplusBuild {*;}
-keep class com.oplus.os.OplusBuild.** {*;}
-keep class com.oplus.flashbacksdk.** {*;}

-keep class com.heytap.epona.ExceptionInfo {*;}

-keep class * implements androidx.viewbinding.ViewBinding {*;}

#Questionnaire begin
#-keep class com.oplus.questionnaire.**{ *; }
-keep class com.oplus.questionnaire.network.InputServiceInfoParams{ *; }
-keep class com.oplus.questionnaire.network.model.QuestionnaireDto{ *; }
-keep class com.oplus.questionnaire.domain.**{ *; }
-keep class com.oplus.questionnaire.presentation.SubmitResult{ *; }
-keep class com.oplus.questionnaireui.data.**{ *; }
#Questionnaire end

-dontwarn com.google.**
-keep class com.google.** {
    *;
}

-keep class com.oplus.questionnaire.data.** {
    *;
}

# Retrofit2
-dontwarn okio.**
-dontwarn retrofit2.Platform$Java8