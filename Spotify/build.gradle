plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'obuildplugin'
}
apply from: '../coverageTest.gradle'
apply from: '../androidCompile.gradle'
android {
    namespace = "com.oplus.spotify"

    defaultConfig {
        minSdk prop_minSdkVersion as Integer
        targetSdkVersion prop_targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    dataBinding {
        enabled = true
    }
    lintOptions {
        disable "GradleDependency", "GradleDynamicVersion", "UnusedResources", "TypographyEllipsis"
    }
}

dependencies {
    implementation project(':Common')
    implementation 'androidx.annotation:annotation:1.4.0'
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:${prop_lifecycleVersion}"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:${prop_lifecycleVersion}"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:${prop_lifecycleVersion}"
    implementation "androidx.customview:customview:1.1.0"
    implementation "androidx.core:core-ktx:1.13.1"

    compileOnly 'com.oplus.statistics:track:3.0.13'
    implementation "com.oplus.appcompat:core:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"
    implementation "com.oplus.appcompat:toolbar:${prop_versionName}"
    implementation "com.oplus.appcompat:chip:${prop_versionName}"
    implementation "com.oplus.appcompat:preference:${prop_versionName}"
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:tablayout:${prop_versionName}"
    implementation "com.oplus.appcompat:viewpager:${prop_versionName}"
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
    implementation "androidx.media:media:1.6.0"
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    testImplementation 'junit:junit:4.+'
    testImplementation "io.mockk:mockk:1.12.1"
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.robolectric:robolectric:4.12.2'
    testImplementation 'androidx.test:runner:1.4.0'
    testImplementation 'androidx.test:rules:1.4.0'
    testImplementation 'androidx.test.ext:junit:1.1.3'
    testImplementation 'androidx.test.ext:truth:1.4.0'
    testImplementation 'org.mockito:mockito-android:3.11.2'
    testImplementation 'org.mockito:mockito-core:3.11.2'
    androidTestImplementation "androidx.room:room-testing:2.4.0"
    androidTestImplementation "io.mockk:mockk-android:1.12.1"
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

}