/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - NestedScrollableHost.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/11/10
 ** Author: helin
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin  2022/11/10     1.0            build this module
 ****************************************************************/

package com.oplus.spotify.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.widget.FrameLayout
import com.coui.appcompat.viewpager.COUIViewPager2.ORIENTATION_HORIZONTAL
import com.coui.appcompat.viewpager.COUIViewPager2
import kotlin.math.absoluteValue

class NestedScrollableHost : FrameLayout {
    companion object {
        const val SENSITIVE = .5f
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    private var touchSlop = 0
    private var initialX = 0f
    private var initialY = 0f
    private val parentViewPager: COUIViewPager2?
        get() {
            var v: View? = parent as? View
            while (v != null && v !is COUIViewPager2) {
                v = v.parent as? View
            }
            return v as? COUIViewPager2
        }

    init {
        touchSlop = ViewConfiguration.get(context).scaledTouchSlop
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        handleInterceptTouchEvent(e)
        return super.onInterceptTouchEvent(e)
    }

    /**
     * 内部拦截分发事件
     */
    private fun handleInterceptTouchEvent(e: MotionEvent) {
        val orientation = parentViewPager?.orientation ?: return
        if (e.action == MotionEvent.ACTION_DOWN) {
            initialX = e.x
            initialY = e.y
        } else if (e.action == MotionEvent.ACTION_MOVE) {
            val dx = e.x - initialX
            val dy = e.y - initialY
            val isVpHorizontal = orientation == ORIENTATION_HORIZONTAL
            //滑动灵敏度 * 2
            val scaledDx = dx.absoluteValue * if (isVpHorizontal) SENSITIVE else 1f
            val scaledDy = dy.absoluteValue * if (isVpHorizontal) 1f else SENSITIVE
            if (scaledDx > touchSlop || scaledDy > touchSlop) {
                if (isVpHorizontal == (scaledDy > scaledDx)) {
                    //纵向滑动
                    parent.requestDisallowInterceptTouchEvent(true)
                } else {
                    //横向滑动
                    parent.requestDisallowInterceptTouchEvent(false)
                }
            }
        }
    }
}