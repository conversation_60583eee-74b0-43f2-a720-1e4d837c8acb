/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - SpotifyActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.spotify

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.media.RingtoneManager
import android.net.Uri
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.core.content.ContextCompat
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.mvvm.base.BaseAVMActivity
import com.oplus.clock.common.utils.DisplayUtils
import com.oplus.clock.common.utils.Log
import com.oplus.spotify.constant.SpotifyEvent
import com.oplus.spotify.databinding.ActivitySpotifyBinding
import com.oplus.spotify.fragment.SpotifyRecommendFragment
import com.oplus.spotify.model.SpotifyMediaItem
import com.oplus.spotify.utils.SpotifySpUtil
import com.oplus.spotify.utils.SpotifyStatisticUtils
import com.oplus.spotify.utils.SpotifyUtils
import com.oplus.spotify.utils.StatusBarUtil
import com.oplus.spotify.viewmodel.SpotifyMainVM
import com.oplus.spotify.viewmodel.SpotifyMainVM.ConnectState

class SpotifyActivity : BaseAVMActivity<ActivitySpotifyBinding, SpotifyMainVM>() {

    companion object {
        private const val TAG = "SpotifyActivity"
        private const val RECOMMEND_FRAGMENT = "recommend_fragment"
    }

    private var defaultRes: Resources? = null
    private var mSearchItem: MenuItem? = null
    private var originConfiguration: Configuration? = null

    override fun layoutId(): Int {
        return R.layout.activity_spotify
    }

    override fun viewModelClass(): Class<SpotifyMainVM> {
        return SpotifyMainVM::class.java
    }

    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(newBase)
        originConfiguration = Configuration(this.resources.configuration)
        val newContext = DisplayUtils.setDefaultDisplay(this)
        defaultRes = newContext?.resources
    }

    override fun getResources(): Resources {
        return defaultRes ?: super.getResources()
    }

    override fun initView() {
        super.initView()
        initToolbar()
        COUITextViewCompatUtil.setPressRippleDrawable(mViewBinding.layoutLogin.btnAuth)
        COUITextViewCompatUtil.setPressRippleDrawable(mViewBinding.loadFailed.textDsc)
    }

    override fun hideNavigation(): Boolean {
        return false
    }

    override fun bindViewModel() {
        mViewBinding.viewModel = mViewModel
        mViewModel.connectState.observe(this) {
            Log.d(TAG, "connect state changed:$it")
            when (it) {
                ConnectState.UNINSTALLED,
                ConnectState.NONETWORK,
                ConnectState.OFFLINE,
                ConnectState.UNLOGIN -> {
                    mSearchItem?.isVisible = false
                    stopLoadingAnim()
                }

                ConnectState.CONNECTING -> playLoadingAnim()
                ConnectState.FAILURE -> {
                    mSearchItem?.isVisible = false
                    stopLoadingAnim()
                    playLoadFailAnim()
                }

                ConnectState.CONNECTED -> {
                    mSearchItem?.isVisible = true
                    addFragment()
                }

                else -> {}
            }
        }
        this.lifecycle.addObserver(mViewModel)
    }

    override fun initData() {
        if (intent.hasExtra(RingtoneManager.EXTRA_RINGTONE_EXISTING_URI)) {
            if (mViewModel.selectedMediaId == null) {
                SpotifyStatisticUtils.onCommon(
                        this.applicationContext,
                        SpotifyStatisticUtils.EVENT_ENTER_SPOTIFY_RINGTONE
                )
            }

            try {
                val uri =
                        intent.getParcelableExtra<Uri>(RingtoneManager.EXTRA_RINGTONE_EXISTING_URI)
                Log.d(TAG, "has EXTRA_RINGTONE_EXISTING_URI $uri")
                mViewModel.selectedMediaTitle = SpotifySpUtil.getRingName(this)
                mViewModel.selectedMediaId = uri?.toString()
            } catch (e: Exception) {
                Log.e(TAG, "init data error extra")
            }
        }

        LiteEventBus.instance.with(SpotifyEvent.EVENT_SET_RINGTONE, hashCode().toString())
            .observe(this) {
                if (it is SpotifyMediaItem) {
                    mViewModel.selectedMediaTitle = it.title
                    mViewModel.selectedMediaId = it.mediaId
                    setRingResult(it)
                }
            }
        LiteEventBus.instance.with(SpotifyEvent.EVENT_RECOMMEND_COMPLETE, hashCode().toString())
            .observe(this) {
                stopLoadingAnim()
                if (it == true) {
                    mViewModel.connectState.postValue(ConnectState.FAILURE)
                }
            }
        LiteEventBus.instance.with(SpotifyEvent.EVENT_ITEM_PLAYING, hashCode().toString())
            .observe(this) {
                if (it is SpotifyMediaItem) {
                    it.mediaId?.let { mediaId -> mViewModel.playMedia(mediaId) }
                }
            }
        LiteEventBus.instance.with(SpotifyEvent.EVENT_ITEM_STOP, hashCode().toString())
            .observe(this) {
                mViewModel.pauseMedia()
            }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_spotify_main, menu)
        mSearchItem = menu?.findItem(R.id.searchIcon)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.searchIcon -> SpotifyUtils.jumpToSpotifySearch(this)
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onStatusBarClicked() {
        super.onStatusBarClicked()
        LiteEventBus.instance.send(SpotifyEvent.EVENT_SCROLL_TO_TOP)
    }

    private fun initToolbar() {
        mViewBinding.appbar.setPadding(
            0,
            StatusBarUtil.getStatusBarHeight(this),
            0,
            0
        )
        setSupportActionBar(mViewBinding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        mViewBinding.toolbar.setNavigationOnClickListener { finish() }
        window.navigationBarColor = ContextCompat.getColor(this, R.color.spotify_main_bg_color)
    }

    private fun addFragment() {
        Log.d(TAG, "addFragment:${mViewModel.selectedMediaId}")
        val fragment = SpotifyRecommendFragment.newInstance(mViewModel.selectedMediaId)
        supportFragmentManager
            .beginTransaction()
            .replace(R.id.container, fragment, RECOMMEND_FRAGMENT)
            .commit()
    }

    private fun playLoadingAnim() {
        mViewBinding.loading.loadingView.apply {
            clearAnimation()
            if (COUIDarkModeUtil.isNightMode(context)) {
                post {
                    setAnimation("loading_night.json")
                    playAnimation()
                }
            } else {
                post {
                    setAnimation("loading.json")
                    playAnimation()
                }
            }
        }
        mViewBinding.loading.loadingLayout.visibility = View.VISIBLE
    }

    private fun stopLoadingAnim() {
        mViewBinding.loading.loadingView.apply {
            cancelAnimation()
            clearAnimation()
        }
        mViewBinding.loading.loadingLayout.visibility = View.GONE
    }

    private fun playLoadFailAnim() {
        mViewBinding.loadFailed.loadFailedView.apply {
            clearAnimation()
            if (COUIDarkModeUtil.isNightMode(context)) {
                post {
                    setAnimation("load_failed_night.json")
                    playAnimation()
                }
            } else {
                post {
                    setAnimation("load_failed.json")
                    playAnimation()
                }
            }
        }
    }

    private fun stopLoadFailAnim() {
        mViewBinding.loadFailed.loadFailedView.apply {
            cancelAnimation()
            clearAnimation()
        }
    }

    private fun setRingResult(mediaItem: SpotifyMediaItem) {
        Log.d(TAG, "setRingResult:${mediaItem.title}, ${mediaItem.mediaId}")
        intent.putExtra(SpotifyEvent.EXTRA_RINGTONE_NAME, mediaItem.title)
        intent.putExtra(RingtoneManager.EXTRA_RINGTONE_PICKED_URI, Uri.parse(mediaItem.mediaId))
        intent.putExtra(SpotifyEvent.EXTRA_RINGTONE_TYPE, SpotifyEvent.TYPE_SPOTIFY)
        setResult(RESULT_OK, intent)
    }

    override fun onStop() {
        super.onStop()
        stopLoadingAnim()
        stopLoadFailAnim()
    }

    override fun onDestroy() {
        super.onDestroy()
        defaultRes = null
        this.resources.updateConfiguration(originConfiguration, resources.displayMetrics)
        LiteEventBus.instance.releaseObserver(hashCode().toString())
    }

    override fun isBottomMargin(): Boolean {
        return true
    }
}