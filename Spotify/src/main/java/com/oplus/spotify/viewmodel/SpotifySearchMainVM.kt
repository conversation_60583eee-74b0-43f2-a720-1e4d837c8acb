/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - SpotifySearchMainVM.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.spotify.viewmodel

import android.app.Application
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.mvvm.vm.BaseAVM
import com.oplus.clock.common.utils.Log
import com.oplus.spotify.constant.SpotifyEvent
import com.oplus.spotify.utils.SpotifySpUtil

class SpotifySearchMainVM(application: Application) : BaseAVM(application) {

    companion object {
        private const val TAG = "SpotifySearchMainVM"
    }

    var currentSearchWord: String? = null
    var showHistory: MutableLiveData<Boolean> = MutableLiveData(false)

    val searchHistory: MutableLiveData<MutableList<String>> by lazy {
        MutableLiveData(SpotifySpUtil.getHistoryList(getApplication()))
    }

    fun saveSearchHistory(word: String) {
        Log.d(TAG, "saveSearchHistory:$word")
        searchHistory.value?.apply {
            remove(word)
            add(word)
        }?.also {
            searchHistory.postValue(it)
            SpotifySpUtil.setHistoryLists(getApplication(), it)
        }
    }

    fun clearHistory() {
        showHistory.value = false
        searchHistory.value?.clear()
        SpotifySpUtil.clearHistoryList(getApplication())
    }

    /**退出页面，暂停播放*/
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        LiteEventBus.instance.send(SpotifyEvent.EVENT_ITEM_PLAYING)
    }

    override fun onCleared() {
        super.onCleared()
        Log.e(TAG, "onCleared")
        showHistory.value = false
        currentSearchWord = null
        searchHistory.value?.clear()
    }
}