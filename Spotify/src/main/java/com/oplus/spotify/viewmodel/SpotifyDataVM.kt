/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - SpotifyDataVM.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.spotify.viewmodel

import android.content.Context
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.mvvm.vm.BaseVM
import com.oplus.clock.common.utils.Log
import com.oplus.spotify.constant.SpotifyConstant
import com.oplus.spotify.constant.SpotifyEvent
import com.oplus.spotify.model.SpotifyMediaItem
import com.oplus.spotify.model.recommend.RecommendModel
import com.oplus.spotify.model.search.SearchModel
import com.oplus.spotify.utils.SpotifyConnector

class SpotifyDataVM : BaseVM() {

    companion object {
        private const val TAG = "SpotifyDataVM"
    }
    var isLoading: Boolean = false

    private val mRecommendItems: ArrayList<SpotifyItemVM> by lazy {
        ArrayList()
    }

    private val mRecommendModel by lazy {
        RecommendModel()
    }

    fun loadData(
        context: Context,
        category: Int? = null,
        recommend: Boolean? = true,
        callBack: (data: ArrayList<SpotifyItemVM>?) -> Unit? = {}
    ) {
        Log.d(TAG, "load data category:$category, $recommend")
        recommend?.let {
            if (it) {
                loadRecommendData(context, callBack)
            } else {
                loadSearchData(category, callBack)
            }
        }
    }

    private fun loadRecommendData(
        context: Context,
        callBack: (data: ArrayList<SpotifyItemVM>?) -> Unit? = {}
    ) {
        Log.d(TAG, "load recommend data:$context")
        mRecommendItems.clear()
        mRecommendModel.loadData(context, isLoadCached = false,
            onSuccess = {
                Log.d(TAG, "load data success!")
                it?.let { items ->
                    Log.d(TAG, "loadRecommendData:${items.first().item?.mediaId} ${items.first().item?.title}")
                    if (items.first().item?.mediaId?.contains(SpotifyConstant.RECENTLY_PLAYED) == true) {
                        Log.d(TAG, "loadData is RECENTLY_PLAYED")
                        mRecommendItems.addAll(0, items)
                    } else {
                        mRecommendItems.addAll(items)
                    }
                }
            }, onFailed = {
                Log.d(TAG, "load data failed!")
            }, onCompleted = {
                Log.d(TAG, "load data completed!")
                callBack(mRecommendItems)
                val empty = mRecommendItems.isEmpty()
                LiteEventBus.instance.send(SpotifyEvent.EVENT_RECOMMEND_COMPLETE, empty)
            })
    }

    private fun loadSearchData(
        category: Int?,
        callBack: (data: ArrayList<SpotifyItemVM>?) -> Unit? = {}
    ) {
        if (showVipTips(category)) {
            Log.d(TAG, "loadSearchData show vip item!")
            callBack(null)
            return
        }
        category?.let {
            SearchModel.getInstance().getSearchData(it)?.let { data ->
                callBack(data)
            }
        }
    }

    fun showVipTips(category: Int?): Boolean {
        return (category == SpotifyMediaItem.ItemType.TYPE_TRACK.ordinal)
                && (true != SpotifyConnector.getInstance().canPlayOnDemand)
    }
}