/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - SpotifyConnector.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.spotify.utils

import android.content.Context
import android.support.v4.media.MediaBrowserCompat
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaControllerCompat
import android.support.v4.media.session.PlaybackStateCompat
import com.oplus.clock.common.event.LiteEventBus
import com.oplus.clock.common.utils.Log
import com.oplus.spotify.constant.SpotifyConstant
import com.oplus.spotify.constant.SpotifyEvent
import com.oplus.spotify.model.mbs.ConnectionCallback
import com.oplus.spotify.model.mbs.MediaBrowserHelper
import com.oplus.spotify.model.mbs.PlayStateCallback
import java.lang.ref.Reference
import java.lang.ref.ReferenceQueue
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentLinkedDeque

class SpotifyConnector private constructor() {

    companion object {
        private const val TAG = "SpotifyConnector"

        private val INSTANCE: SpotifyConnector by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            SpotifyConnector()
        }

        @JvmStatic
        fun getInstance(): SpotifyConnector {
            return INSTANCE
        }
    }

    var canPlayOnDemand: Boolean? = null

    private val mReferenceQueue: ReferenceQueue<PlayStateCallback> by lazy {
        ReferenceQueue()
    }
    private val mPlayCallbackList: ConcurrentLinkedDeque<WeakReference<PlayStateCallback>> by lazy {
        ConcurrentLinkedDeque()
    }

    private val mMediaControllerCallback = object : MediaControllerCompat.Callback() {
        override fun onMetadataChanged(metadata: MediaMetadataCompat?) {
            super.onMetadataChanged(metadata)
            Log.d(TAG, "media controller metadata changed!")
            notifyMetadataChanged(metadata)
        }

        override fun onPlaybackStateChanged(stateCompat: PlaybackStateCompat?) {
            super.onPlaybackStateChanged(stateCompat)
            Log.d(TAG, "media controller playback state changed: ${stateCompat?.state}")
            stateCompat?.apply {
                if (PlaybackStateCompat.STATE_ERROR == state) {
                    disconnect()
                } else if (extras?.getBoolean(SpotifyConstant.OFFLINE_MODE) == true) {
                    disconnect()
                } else {
                    notifyPlayStateChanged(stateCompat)
                    notifyCanPlayOnDemandChanged(stateCompat)
                }
            }
        }

        override fun onSessionDestroyed() {
            super.onSessionDestroyed()
            Log.d(TAG, "media controller session destroyed!")
            disconnect()
        }

        override fun binderDied() {
            super.binderDied()
            Log.d(TAG, "media controller binder died!")
            disconnect()
        }

        override fun onAudioInfoChanged(info: MediaControllerCompat.PlaybackInfo?) {
            super.onAudioInfoChanged(info)
            Log.d(TAG, "onAudioInfoChanged $info")
        }
    }

    fun registerPlayStateCallback(callback: PlayStateCallback) {
        synchronized(this) {
            var releaseCallback: Reference<out PlayStateCallback>? = null
            while (mReferenceQueue.poll().also { releaseCallback = it } != null) {
                mPlayCallbackList.remove(releaseCallback)
            }

            for (callbackImpl in mPlayCallbackList) {
                if (callbackImpl.get() === callback) {
                    Log.e(TAG,  "the listener is registed!")
                    return
                }
            }

            val weakReference = WeakReference<PlayStateCallback>(callback, mReferenceQueue)
            mPlayCallbackList.add(weakReference)
            Log.d(TAG, "registerPlayCallback:${mPlayCallbackList.size}")
        }
    }

    fun unregisterPlayStateCallback(callback: PlayStateCallback) {
        synchronized(this) {
            Log.d(TAG, "unregisterPlayCallback:${mPlayCallbackList.size}")
            for (callbackImpl in mPlayCallbackList) {
                if (callbackImpl.get() === callback) {
                    mPlayCallbackList.remove(callbackImpl)
                    Log.d(TAG, "unregisterPlayCallback remove end:${mPlayCallbackList.size}")
                    return
                }
            }
        }
    }

    fun requestMediaController(
        context: Context,
        onBrowserCallBack: (mediaBrowser: MediaBrowserCompat?) -> Unit? = {},
        onControllerCallBack: (mediaController: MediaControllerCompat?) -> Unit? = {},
        onFailure: (throwable: Throwable) -> Unit? = {}
    ) {
        if (MediaBrowserHelper.getInstance().isMbsConnected()) {
            Log.d(TAG, "requestMediaController is connected!")
            onBrowserCallBack(MediaBrowserHelper.getInstance().getMediaBrowser())
            onControllerCallBack(MediaBrowserHelper.getInstance().getMediaController())
        } else {
            Log.d(TAG, "requestMediaController connect")
            connect(
                context,
                onBrowserCallBack,
                onControllerCallBack,
                onFailure = {
                    onBrowserCallBack(null)
                    onControllerCallBack(null)
                    onFailure(it)
            })
        }
    }

    fun getMediaBrowser(): MediaBrowserCompat? {
        return MediaBrowserHelper.getInstance().getMediaBrowser()
    }

    fun getMediaController(): MediaControllerCompat? {
        return MediaBrowserHelper.getInstance().getMediaController()
    }

    fun connect(
        context: Context,
        onBrowserCallBack: (mediaBrowser: MediaBrowserCompat?) -> Unit? = {},
        onControllerCallBack: (mediaController: MediaControllerCompat?) -> Unit? = {},
        onFailure: (throwable: Throwable) -> Unit? = {}
    ) {
        connectToMbs(context, onBrowserCallBack, onControllerCallBack, onFailure)
    }

    /**
     * 连接MediaBrowserService，用于获取spotify recommend数据
     * 1. remote连接成功时，连接
     * 2. remote连接失败但非用户“未登录/授权”，连接
     */
    private fun connectToMbs(
        context: Context,
        onBrowserCallBack: (mediaBrowser: MediaBrowserCompat?) -> Unit? = {},
        onControllerCallBack: (mediaController: MediaControllerCompat?) -> Unit? = {},
        onFailure: (throwable: Throwable) -> Unit? = {}
    ) {
        MediaBrowserHelper.getInstance().getMediaClient(
            context,
            callback = object : ConnectionCallback {
                override fun onSuccess(
                    mediaBrowserCompat: MediaBrowserCompat?,
                    mediaControllerCompat: MediaControllerCompat?
                ) {
                    Log.d(TAG, "mbs connect success")
                    initDemandEnabled(mediaControllerCompat?.playbackState)
                    mediaControllerCompat?.registerCallback(mMediaControllerCallback)
                    onBrowserCallBack(mediaBrowserCompat)
                    onControllerCallBack(mediaControllerCompat)
                }

                override fun onFailure(throwable: Throwable?) {
                    Log.e(TAG, "mbs connect fail:${throwable?.printStackTrace()}")
                    if (throwable != null) {
                        onFailure.invoke(throwable)
                    }
                }
            }, true)
    }

    private fun initDemandEnabled(stateCompat: PlaybackStateCompat?) {
        canPlayOnDemand = stateCompat?.extras?.getBoolean(SpotifyConstant.ON_DEMAND_ENABLED)
        Log.d(TAG, "initDemandEnabled: $canPlayOnDemand")
    }

    private fun notifyPlayStateChanged(stateCompat: PlaybackStateCompat?) {
        for (callbackImpl in mPlayCallbackList) {
            callbackImpl.get()?.onPlayStateChanged(stateCompat)
        }
    }

    private fun notifyMetadataChanged(metadata: MediaMetadataCompat?) {
        for (callbackImpl in mPlayCallbackList) {
            callbackImpl.get()?.onMetadataChanged(metadata)
        }
    }

    private fun notifyCanPlayOnDemandChanged(stateCompat: PlaybackStateCompat?) {
        val demandEnabled = stateCompat?.extras?.getBoolean(SpotifyConstant.ON_DEMAND_ENABLED)
        Log.d(TAG, "notifyCanPlayOnDemandChanged: $demandEnabled")
        if (demandEnabled != null && canPlayOnDemand != demandEnabled) {
            canPlayOnDemand = demandEnabled
            LiteEventBus.instance.send(SpotifyEvent.EVENT_USER_CAPABILITIES_CHANGED, canPlayOnDemand)
        }
    }

    fun disconnect() {
        Log.d(TAG, "disconnect spotify")
        mPlayCallbackList.clear()
        canPlayOnDemand = null
        getMediaController()?.unregisterCallback(mMediaControllerCallback)
        MediaBrowserHelper.getInstance().release()
    }
}