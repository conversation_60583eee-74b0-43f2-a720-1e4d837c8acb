/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - SpotifyConstant.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.spotify.constant

object SpotifyConstant {

    const val PACKAGE_NAME = "com.spotify.music"

    //download
    const val INSTALL_ID_KEY = "id"
    const val INSTALL_REFERRER_KEY = "referrer"
    const val URI_MARKET_1 = "market://details"
    const val URI_MARKET_2 = "https://play.google.com/store/apps/details"

    //login
    const val SPOTIFY_DEFAULT_URI = "spotify:upsell:premium_in_app_destination"

    const val ACTION_EXTERNAL_LOGIN = "com.spotify.music.ACTION_EXTERNAL_LOGIN"
    const val SPOTIFY_SETTING_PAGE = "spotify:config"

    //prepare
    const val ACTION_PREPARE = "com.spotify.music.ACTION_PREPARE"
    const val PACKAGE_SERVICE = "com.spotify.music.alarmlauncher.SpotifyAlarmLauncherService"
    const val EXTRA_CONTEXT_URI = "com.spotify.music.extra.CONTEXT_URI"
    const val EXTRA_START_TIME = "com.spotify.music.EXTRA_START_TIME"

    //mbs
    const val ACTION_MEDIA_BROWSER_SERVICE = "android.media.browse.MediaBrowserService"
    const val EXTRA_RESET = "Reset"
    const val EXTRA_SUGGESTED_TYPE = "com.spotify.music.extra.SUGGESTED_TYPE"
    const val SUGGESTED_TYPE_WAKE = "wake"

    const val ON_DEMAND_ENABLED = "com.spotify.music.extra.ON_DEMAND_ENABLED"
    const val OFFLINE_MODE = "com.spotify.music.extra.OFFLINE_MODE"

    const val PLAY_TRACK_INDEX = "com.spotify.music.extra.TRACK_INDEX"
    const val PLAYBACK_TYPE = "com.spotify.music.extra.PLAYBACK_TYPE"
    const val META_ART_URI = "com.spotify.music.extra.ART_HTTPS_URI"

    const val RECENTLY_PLAYED = "com.spotify.recently-played"

    //自定义error
    const val NO_ERROR = -1
    const val ERROR_UNKNOWN = 0
    const val ERROR_OFFLINE_MODE = 1000
    const val ERROR_APP_ABNORMAL = 1001
    const val ERROR_TIMEOUT = 1002
}