/************************************************************
 * Copyright 2016 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :PlatformClockListener
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.coloros.platformalarmclock;

public interface PlatformClockListener {
    void isBindAlarmClock(boolean bindSuccess);

    void dismissClock(long clockId);

    void snoozeClock(long clockId);

    void alarmClockRing(PlatformClockInfo platformClockInfo);

    void onDataChanged(int action, int enableAssociate, long alarmId);
}
