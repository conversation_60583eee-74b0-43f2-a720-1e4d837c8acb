/************************************************************
 * Copyright 2016 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :PlatformClockBroadcastReceiver
 * clock ask heath app to bind clock app
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.coloros.platformalarmclock;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

public class PlatformClockBroadcastReceiver extends BroadcastReceiver {


    private final static String TAG = "PlatformClockBroadcastReceiver";
    private final static String PLATFORM_CLOCK_BROADCAST_AWAKEN_CLOCK_ACTION = "com.coloros.platformalarmclock.platform.awaken_clock_action";

    @SuppressLint("LongLogTag")
    @Override
    public void onReceive(Context context, Intent intent) {

        Log.i(TAG, "onReceive");
        if (intent != null) {
            final String action = intent.getAction();
            Log.i(TAG, "onReceive action = " + action);
            if (!TextUtils.isEmpty(action)) {
                if (action.equals(PLATFORM_CLOCK_BROADCAST_AWAKEN_CLOCK_ACTION) && (context != null)) {
                    try {
                        //在外部应用中静态注册的广播，以通知外部应用来重新绑定服务
                        PlatformClockManager.getManager().setContext(context.getApplicationContext());
                        //由于之前的版本健康app将此视为绑定的广播，所以在此重新绑定一次
                        PlatformClockManager.getManager().rebindAlarmClock(context.getApplicationContext());
                    } catch (Exception e) {
                        Log.e(TAG, "action : " + e.getMessage());
                    }
                }
            } else {
                Log.e(TAG, "onReceive action is null ");
            }
        } else {
            Log.e(TAG, "onReceive intent is null ");
        }
    }
}
