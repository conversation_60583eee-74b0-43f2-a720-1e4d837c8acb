/************************************************************
 * Copyright 2016 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :PlatformClockInfo
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.coloros.platformalarmclock;

import android.os.Parcel;
import android.os.Parcelable;

public class PlatformClockInfo implements Parcelable {
    private long mScheduleId;
    private long mAlarmTime;
    private boolean mDelayReminder;
    private String mTagName;
    private String mLanguage;

    private boolean mIsGarbAlarm;

    private int mRingNum;
    private int mSnoozeTime;
    public int getmSnoozeTime() {
        return mSnoozeTime;
    }

    public void setmSnoozeTime(int mSnoozeTime) {
        this.mSnoozeTime = mSnoozeTime;
    }

    public int getmRingNum() {
        return mRingNum;
    }

    public void setmRingNum(int mRingNum) {
        this.mRingNum = mRingNum;
    }

    public PlatformClockInfo() {
    }

    public PlatformClockInfo(long mScheduleId, long mAlarmTime, boolean mDelayReminder, String mTagName, String mLanguage) {
        this.mScheduleId = mScheduleId;
        this.mAlarmTime = mAlarmTime;
        this.mDelayReminder = mDelayReminder;
        this.mTagName = mTagName;
        this.mLanguage = mLanguage;
    }

    protected PlatformClockInfo(Parcel in) {
        mScheduleId = in.readLong();
        mAlarmTime = in.readLong();
        mDelayReminder = in.readByte() != 0;
        mTagName = in.readString();
        mLanguage = in.readString();
        mIsGarbAlarm = in.readByte() != 0;
        mRingNum = in.readInt();
        mSnoozeTime = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(mScheduleId);
        dest.writeLong(mAlarmTime);
        dest.writeByte((byte) (mDelayReminder ? 1 : 0));
        dest.writeString(mTagName);
        dest.writeString(mLanguage);
        dest.writeByte((byte) (mIsGarbAlarm ? 1 : 0));
        dest.writeInt(mRingNum);
        dest.writeInt(mSnoozeTime);
    }


    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<PlatformClockInfo> CREATOR = new Creator<PlatformClockInfo>() {
        @Override
        public PlatformClockInfo createFromParcel(Parcel in) {
            return new PlatformClockInfo(in);
        }

        @Override
        public PlatformClockInfo[] newArray(int size) {
            return new PlatformClockInfo[size];
        }
    };

    public long getScheduleId() {
        return mScheduleId;
    }

    public void setScheduleId(long mScheduleId) {
        this.mScheduleId = mScheduleId;
    }

    public long getAlarmTime() {
        return mAlarmTime;
    }

    public void setAlarmTime(long mAlarmTime) {
        this.mAlarmTime = mAlarmTime;
    }

    public boolean isDelayReminder() {
        return mDelayReminder;
    }

    public void setDelayReminder(boolean mDelayReminder) {
        this.mDelayReminder = mDelayReminder;
    }

    public String getTagName() {
        return mTagName;
    }

    public void setTagName(String mTagName) {
        this.mTagName = mTagName;
    }

    public String getLanguage() {
        return mLanguage;
    }

    public void setLanguage(String mLanguage) {
        this.mLanguage = mLanguage;
    }

    public boolean ismIsGarbAlarm() {
        return mIsGarbAlarm;
    }

    public void setmIsGarbAlarm(boolean mIsGarbAlarm) {
        this.mIsGarbAlarm = mIsGarbAlarm;
    }
    @Override
    public String toString() {
        return "PlatformClockInfo{"
                + "mScheduleId=" + mScheduleId
                + ", mAlarmTime=" + mAlarmTime
                + ", mDelayReminder=" + mDelayReminder
                + ", mTagName='" + mTagName
                + ", mLanguage='" + mLanguage
                + ", mIsGarbAlarm='" + mIsGarbAlarm
                + ", mRingNum='" + mRingNum
                + ", mSnoozeTime='" + mSnoozeTime
                + '}';
    }
}
