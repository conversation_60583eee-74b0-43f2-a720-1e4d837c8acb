/************************************************************
 * Copyright 2016 OPPO Mobile Comm Corp., Ltd. All rights reserved.
 * <p>
 * Description :IClockAidlInterface.aidl
 * <p>
 * History :( ID, Date, Author, Description)
 * v1.0, 2020-03-05, Yuxiaolong, create
 ************************************************************/
package com.coloros.alarmclock;

// Declare any non-default types here with import statements
import com.coloros.alarmclock.IClockUpdateAidlInterface;
import com.coloros.platformalarmclock.PlatformClockInfo;
interface IClockAidlInterface {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
    void bindAlarmClock();

    boolean dismissClock(long scheduleId);

    boolean snoozeClock(long scheduleId);

    boolean registerListener(IClockUpdateAidlInterface listener);

    void unbindAlarmClock();

    void reWakeUpCurrentAlarmRing();

    PlatformClockInfo getCurrentAlarm();

    void notifyDataChange(int enable,long alarmId);
}
