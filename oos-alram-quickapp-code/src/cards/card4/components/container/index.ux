<template>
  <div
    class="widgetui-container"
    style="{{ containerStyle }}"
    forcedark="false"
    desc-restyling="true"
    onresize="resizeChange">
    <slot></slot>
  </div>
</template>

<script>
  /**
   * @file 卡片容器组件
   */
  export default {
    props: {
      isDark: {
        type: Boolean,
        default: false
      },
      backgroundColor: {
        type: String,
        default: '#fff'
      }
    },

    computed: {
      containerStyle() {
        return {
          backgroundColor: this.isDark ? '#383838' : this.backgroundColor
        }
      }
    },

    resizeChange(event) {
      this.$emit('emitResize', {
        width: event.offsetWidth,
        height: event.offsetHeight
      })
    }
  }
</script>

<style lang="less">
  .widgetui-container {
    padding: 16px;
    background-size: contain;
    width: 100%;
    height: 100%;
  }
</style>
