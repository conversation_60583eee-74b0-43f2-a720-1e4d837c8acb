/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseCompatActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.base

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.clock.common.utils.ColorStatusBarResponseUtil
import com.oplus.clock.common.utils.Log
import com.oplus.clock.common.utils.StyleUtil

abstract class BaseCompatActivity : AppCompatActivity(), ColorStatusBarResponseUtil.StatusBarClickListener {

    companion object {
        private const val TAG = "BaseActivity"
    }
    protected var mColorStatusBarResponseUtil: ColorStatusBarResponseUtil? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StyleUtil.setWindowStyle(this)
        StyleUtil.setAnmiToolbarActivityWindowStyle(this)
        if (hideNavigation()) {
            StyleUtil.gestureNavTransparent(this)
        } else {
            StyleUtil.gestureNavTransparent(this, false)
        }

        if (shouldApplyThemeOverlays()) {
            COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        }

        if (isRegisterStatusBar() && mColorStatusBarResponseUtil == null) {
            mColorStatusBarResponseUtil = ColorStatusBarResponseUtil(this)
            mColorStatusBarResponseUtil?.setStatusBarClickListener(this)
            mColorStatusBarResponseUtil?.register()
        }
    }

    protected open fun hideNavigation(): Boolean {
        return true
    }

    protected open fun shouldApplyThemeOverlays(): Boolean {
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        mColorStatusBarResponseUtil?.unRegister()
    }

    protected open fun isRegisterStatusBar(): Boolean {
        return false
    }

    override fun onStatusBarClicked() {
        Log.d(TAG, "status bar clicked")
    }
}