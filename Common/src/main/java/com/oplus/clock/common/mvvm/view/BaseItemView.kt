/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseItemView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.view

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.oplus.clock.common.mvvm.vm.BaseVM

abstract class BaseItemView<VB : ViewDataBinding, VM : BaseVM>(
    context: Context,
    viewGroup: ViewGroup
) {
    private val layoutId by lazy {
        layoutId()
    }

    lateinit var viewModel: VM
    var binding: VB = DataBindingUtil.inflate<VB>(
        LayoutInflater.from(context), layoutId, viewGroup, false)

    fun setData(viewModel: VM) {
        this.viewModel = viewModel
        bindViewModel(viewModel)
        binding.executePendingBindings()
    }

    /**
     * layout id
     * @return layout
     */
    protected abstract fun layoutId(): Int

    /**
     * bind view model
     */
    protected abstract fun bindViewModel(data: VM)
}