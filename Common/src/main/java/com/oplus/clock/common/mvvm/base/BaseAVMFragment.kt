/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseVMFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider
import com.oplus.clock.common.mvvm.vm.BaseAVM
import com.oplus.clock.common.utils.Log

abstract class BaseAVMFragment<VB : ViewDataBinding, VM : BaseAVM> : BaseVBFragment<VB>() {

    companion object {
        private const val TAG = "BaseVMFragment"
    }

    protected var mViewModel: VM? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        initVm()
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    /**
     * The viewmodel class of binding
     * @return class type
     */
    protected abstract fun viewModelClass(): Class<VM>

    protected open fun bindViewModel() {}

    override fun initView() {
        bindViewModel()
        this.mViewBinding.lifecycleOwner = this
    }

    @Suppress("TooGenericExceptionCaught")
    private fun initVm() {
        try {
            mViewModel = ViewModelProvider(this)[viewModelClass()]
            mViewModel?.mErrLiveData?.observe(viewLifecycleOwner) { onVmError(it) }
        } catch (e: Exception) {
            Log.d(TAG, "${this::class.java.simpleName} initVm e:$e")
        }
    }

    open fun onVmError(e: Exception) {
        Log.d(TAG, "${this::class.java.simpleName} onVmError e:$e")
    }
}