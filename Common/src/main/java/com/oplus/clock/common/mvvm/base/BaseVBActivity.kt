/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseVBActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.base

import android.os.Bundle
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding

abstract class BaseVBActivity<T : ViewDataBinding> : BaseScreenActivity() {

    protected lateinit var mViewBinding: T

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initVb()
        initView()
        initData()
    }

    /**
     * layout id
     * @return layout
     */
    @LayoutRes
    abstract fun layoutId(): Int

    /**
     * init view
     */
    abstract fun initView()

    /**
     * init data
     */
    abstract fun initData()

    private fun initVb() {
        mViewBinding = DataBindingUtil.setContentView<T>(this, layoutId())
    }
}