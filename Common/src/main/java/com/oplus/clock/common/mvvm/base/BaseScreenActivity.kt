/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseScreenActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.base

import android.content.res.Configuration
import android.os.Bundle
import com.oplus.clock.common.uiconfig.FoldScreenConfig
import com.oplus.clock.common.uiconfig.IUIConfig
import com.oplus.clock.common.uiconfig.UIConfigMonitor
import com.oplus.clock.common.utils.FoldScreenUtils
import java.util.function.Consumer

abstract class BaseScreenActivity : BaseCompatActivity(), UIConfigMonitor.OnUIConfigChangeListener {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerUIConfigMonitor()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIConfigMonitor.instance.onActivityConfigChanged(newConfig)
        dealOsloOrientation()
    }

    /**
     * 处理平板横竖屏边距padding
     * isRealOslo 获取的是物理平板，包括分屏模式，所以需要处理平板横竖屏，和其他情况（其他情况padding设置为默认）
     */
    private fun dealOsloOrientation() {
        if (!FoldScreenUtils.isRealOslo(this)) {
            return
        }
        when {
            FoldScreenUtils.isOsloLandscape(this) -> onOsloLandOrientation()
            FoldScreenUtils.isOsloPortrait(this) -> onOsloPortOrientation()
            else -> onOsloOtherOrientation()
        }
    }

    @Suppress("EmptyFunctionBlock")
    protected fun onOsloLandOrientation() {}

    @Suppress("EmptyFunctionBlock")
    protected fun onOsloPortOrientation() {}

    @Suppress("EmptyFunctionBlock")
    protected fun onOsloOtherOrientation() {}

    protected fun registerUIConfigMonitor() {
        UIConfigMonitor.instance.attachActivity(this)
        FoldScreenUtils.updateUIOrientation(this)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        configList.forEach(Consumer {
            if (it is FoldScreenConfig) {
                FoldScreenUtils.updateUIOrientation(this)
            }
        })
    }
}