/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - IntentNativeUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.osdk

import android.content.Context
import android.view.Window
import androidx.annotation.RequiresPermission
import com.oplus.compat.view.WindowManagerNative
import com.oplus.view.OplusWindowAttributesManager
import com.oplus.wrapper.os.ServiceManager
import com.oplus.wrapper.view.IWindowManager

object WindowNativeUtils {

    const val DEFAULT_DISPLAY_DENSITY_DPI = 480

    @JvmStatic
    fun getInitialDisplayDensity(displayId: Int, def: Int = DEFAULT_DISPLAY_DENSITY_DPI): Int {
        return kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                val service = ServiceManager.getService(Context.WINDOW_SERVICE)
                IWindowManager.Stub.asInterface(service).getInitialDisplayDensity(displayId)
            } else {
                WindowManagerNative.getInitialDisplayDensity(displayId)
            }
        }.getOrDefault(def)
    }

    @JvmStatic
    @RequiresPermission("com.oplus.permission.safe.WINDOW")
    fun setIgnoreHomeMenuKey(window: Window) {
        val param = kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                OplusWindowAttributesManager.IGNORE_HOME_MENU_KEY
            } else {
                WindowManagerNative.LayoutParamsNative.IGNORE_HOME_MENU_KEY
            }
        }.getOrDefault(1)
        setIgnoreHomeMenuKeyState(window, param)
    }

    @JvmStatic
    @RequiresPermission("com.oplus.permission.safe.WINDOW")
    fun setDisableStatusBar(window: Window) {
        val param = kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                OplusWindowAttributesManager.DISABLE_STATUS_BAR
            } else {
                WindowManagerNative.LayoutParamsNative.DISABLE_STATUS_BAR
            }
        }.getOrDefault(1)
        setIgnoreHomeMenuKeyState(window, param)
    }

    @JvmStatic
    @RequiresPermission("com.oplus.permission.safe.WINDOW")
    fun setDefaultStatusBar(window: Window) {
        val param = kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                OplusWindowAttributesManager.DEFAULT_STATUS_BAR
            } else {
                WindowManagerNative.LayoutParamsNative.DEFAULT_STATUS_BAR
            }
        }.getOrDefault(0)
        setIgnoreHomeMenuKeyState(window, param)
    }

    @JvmStatic
    @RequiresPermission("com.oplus.permission.safe.WINDOW")
    private fun setIgnoreHomeMenuKeyState(window: Window, param: Int) {
        kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                OplusWindowAttributesManager.setIgnoreHomeMenuKeyState(
                    window,
                    param
                )
            } else {
                WindowManagerNative.LayoutParamsNative.setHomeAndMenuKeyState(
                    window.attributes,
                    param
                )
            }
        }
    }
}