/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseVMActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.base

import android.os.Bundle
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider
import com.oplus.clock.common.mvvm.vm.BaseVM
import com.oplus.clock.common.utils.Log

abstract class BaseVMActivity<VB : ViewDataBinding, VM : BaseVM> : BaseVBActivity<VB>() {

    companion object {
        private const val TAG = "BaseVMActivity"
    }

    protected lateinit var mViewModel: VM

    override fun onCreate(savedInstanceState: Bundle?) {
        initVm()
        super.onCreate(savedInstanceState)
    }

    /**
     * The viewmodel class of binding
     * @return class type
     */
    protected abstract fun viewModelClass(): Class<VM>

    protected open fun bindViewModel() {}

    override fun initView() {
        bindViewModel()
        this.mViewBinding.lifecycleOwner = this
    }

    private fun initVm() {
        mViewModel = ViewModelProvider(this)[viewModelClass()]
        mViewModel.mErrLiveData.observe(this) { onVmError(it) }
    }

    open fun onVmError(e: Exception) {
        Log.d(TAG, "${this::class.java.simpleName} onVmError e:$e")
    }
}