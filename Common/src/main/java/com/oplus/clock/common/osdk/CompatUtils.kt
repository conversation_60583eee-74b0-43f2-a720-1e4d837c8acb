/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - CompatUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.osdk

import android.os.Build
import androidx.annotation.ChecksSdkIntAtLeast
import com.oplus.clock.common.utils.Log
import com.oplus.os.OplusBuild

object CompatUtils {

    private const val TAG = "CompatUtils"
    private const val OSDK_SUB_VERSION = 6

    @JvmStatic
    fun supportOsdk(): Boolean {
        kotlin.runCatching {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                Log.d(TAG, "support osdk false > sdk < 33")
                return false
            }
            if (OplusBuild.VERSION.SDK_VERSION > OplusBuild.OsdkVersionCodes.OS_14_0) {
                Log.d(TAG, "support osdk true > oplus version > 14")
                return true
            }
            return if (OplusBuild.VERSION.SDK_VERSION == OplusBuild.OsdkVersionCodes.OS_14_0) {
                Log.d(TAG, "support osdk true > sdk = 14, sub sdk = 6")
                OplusBuild.VERSION.SDK_SUB_VERSION >= OSDK_SUB_VERSION
            } else {
                false
            }
        }.onFailure {
            Log.d(TAG, "support osdk false exception")
        }
        Log.d(TAG, "support osdk false")
        return false
    }

    @JvmStatic
    @ChecksSdkIntAtLeast(api = 34)
    fun supportSysApi(): Boolean {
        kotlin.runCatching {
            return Build.VERSION.SDK_INT > Build.VERSION_CODES.TIRAMISU
        }
        return false
    }

    /**
     * @return true 使用下沉接口 ,false 使用历史接口
     */
    @JvmStatic
    fun isOs15(): Boolean {
        kotlin.runCatching {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                return OplusBuild.VERSION.SDK_VERSION >= OplusBuild.OsdkVersionCodes.OS_15_0_0
            }
        }
        return false
    }
}