/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ViewClick.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: Zhao<PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.anotation

@kotlin.annotation.Target(
    AnnotationTarget.FUNCTION,
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.PROPERTY_SETTER
)
@kotlin.annotation.Retention(AnnotationRetention.BINARY)
annotation class ViewClick(vararg val value: Int = [-1])