/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - LiteEventBus.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2022/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.event

import android.text.TextUtils
import android.util.Log
import androidx.lifecycle.ExternalLiveData

class LiteEventBus private constructor() {

    companion object {
        private const val TAG = "LiteEventBus"

        val instance: LiteEventBus by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            LiteEventBus()
        }
    }

    private var eventObserversMap: HashMap<EventObserver, ExternalLiveData<Any>> = HashMap()
    private val lock = Any()

    /**
     * 关联（关联后，发送的事件才能收到）
     *
     * @param eventId 事件标识
     * @param observerId 注册标识
     * @return
     */
    fun with(eventId: String, observerId: String): ExternalLiveData<Any> {
        return with(EventObserver(observerId, eventId))
    }

    /**
     * 关联（关联后，发送的事件才能收到）
     * 同一个事件监听只允许注册一次
     * @param registerId 事件标识
     * @return
     */
    fun with(eventObserver: EventObserver): ExternalLiveData<Any> {
        synchronized(lock) {
            return if (eventObserversMap.containsKey(eventObserver)) {
                eventObserversMap[eventObserver]!!
            } else {
                val data = ExternalLiveData<Any>()
                eventObserversMap[eventObserver] = data
                data
            }
        }
    }

    /**
     * 发送事件,广播
     *
     * @param eventId    事件标识
     * @param evcent 附带内容
     */
    fun send(eventId: String, evcent: Any? = "") {
        Log.d(TAG, "send event = $eventId")
        synchronized(lock) {
            eventObserversMap.forEach { (event, livedata) ->
                if (TextUtils.equals(event.eventId, eventId)) {
                    livedata.postValue(evcent)
                }
            }
        }
    }

    /**
     * release memory
     *
     * @param observerId
     */
    fun releaseObserver(observerId: String) {
        synchronized(lock) {
            val filter = eventObserversMap.filter { (event) ->
                TextUtils.equals(event.observerId, observerId)
            }
            if (filter.isNotEmpty()) {
                filter.forEach { (t) -> eventObserversMap.remove(t) }
            }
        }
    }

    /**
     * release event and memory
     *
     * @param eventId
     */
    fun releaseEvent(eventId: String) {
        val filter = eventObserversMap.filter { (event) ->
            TextUtils.equals(event.eventId, eventId)
        }
        if (filter.isNotEmpty()) {
            filter.forEach { (t) -> eventObserversMap.remove(t) }
        }
    }

    fun releaseAllObservers() {
        synchronized(lock) {
            eventObserversMap.clear()
        }
    }
}

data class EventObserver constructor(var observerId: String, var eventId: String) {
    override fun hashCode(): Int {
        return observerId.hashCode() + 31 * eventId.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) {
            return true
        }
        if (other is EventObserver && TextUtils.equals(other.eventId, eventId)
                && TextUtils.equals(other.observerId, observerId)) {
            return true
        }
        return false
    }
}