/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - UserNativeUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.osdk

import android.app.OplusActivityManager
import android.os.Process
import com.oplus.clock.common.utils.Log
import com.oplus.compat.app.ActivityManagerNative
import com.oplus.compat.os.UserHandleNative
import com.oplus.wrapper.os.UserHandle

object UserNativeUtils {
    private const val TAG = "ActivityNativeUtils"
    val USER_CURRENT: Int
        @JvmStatic
        get() {
            if (CompatUtils.supportSysApi()) {
                kotlin.runCatching {
                    return UserHandle.USER_CURRENT
                }
            }
            return UserHandleNative.USER_CURRENT
        }

    @JvmStatic
    fun getCurrentUser(): Int {
        var currentUserId = 0
        kotlin.runCatching {
            currentUserId = if (CompatUtils.supportSysApi()) {
                if (CompatUtils.isOs15()) {
                    val oplusActivityManager = OplusActivityManager()
                    oplusActivityManager.currentUser
                } else {
                    com.oplusx.sysapi.app.ActivityManagerNative.getCurrentUser()
                }
            } else {
                ActivityManagerNative.getCurrentUser()
            }
            Log.d(TAG, "getCurrentUser currentUserID: $currentUserId")
        }.onFailure {
            Log.e(TAG, "getCurrentUser error : " + it.message)
        }
        return currentUserId
    }


    @JvmStatic
    fun myUserId(): Int {
        kotlin.runCatching {
            return if (CompatUtils.supportOsdk()) {
                UserHandle.myUserId()
            } else {
                UserHandleNative.myUserId()
            }
        }.onFailure {
            Log.e(TAG, "addViewForWindowManager userID error:${it.message}")
        }
        return 0
    }

    @JvmStatic
    fun getIdentifier(): Int {
        return kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                UserHandle(Process.myUserHandle()).identifier
            } else {
                UserHandleNative.getIdentifier(Process.myUserHandle())
            }
        }.getOrDefault(0)
    }
}