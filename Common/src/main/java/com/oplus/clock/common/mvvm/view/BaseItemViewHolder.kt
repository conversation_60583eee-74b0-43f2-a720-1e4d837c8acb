/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseItemViewHolder.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.view

import androidx.recyclerview.widget.RecyclerView
import com.oplus.clock.common.mvvm.vm.BaseVM

open class BaseItemViewHolder<VM : BaseVM> (
    private val itemViewProxy: BaseItemView<*, VM>
) : RecyclerView.ViewHolder(itemViewProxy.binding.root) {

    fun bindViewModel(viewModel: VM) {
        itemViewProxy.setData(viewModel)
    }
}