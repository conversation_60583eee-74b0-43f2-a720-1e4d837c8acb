/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - StatusBarNativeUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.osdk

import android.app.OplusStatusBarManager
import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import com.oplusx.sysapi.app.StatusBarManagerNative

object StatusBarNativeUtils {

    private val mOplusStatusBarManagerResult = lazy {
        kotlin.runCatching {
            OplusStatusBarManager()
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    @JvmStatic
    fun disable(statusType: Int, context: Context) {
        kotlin.runCatching {

            if (CompatUtils.supportSysApi()) {
                if (CompatUtils.isOs15()) {
                    val oplusStatusBarManager = mOplusStatusBarManagerResult.value.getOrNull()
                    if (oplusStatusBarManager != null) {
                        return oplusStatusBarManager.disable(context, statusType)
                    }
                }
                return StatusBarManagerNative.disable(statusType)
            } else {
                return com.oplus.compat.app.StatusBarManagerNative.disable(statusType)
            }
        }
    }
}
