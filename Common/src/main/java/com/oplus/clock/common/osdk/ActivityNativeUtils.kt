/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ActivityNativeUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  ZhaoXing    2023/8/3     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.osdk

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import androidx.annotation.RequiresApi
import com.oplus.compat.app.ActivityManagerNative
import com.oplus.wrapper.app.IActivityManager
import com.oplus.wrapper.os.ServiceManager

object ActivityNativeUtils {

    @JvmStatic
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getConfiguration(): Configuration? {
        return kotlin.runCatching {
            if (CompatUtils.supportOsdk()) {
                val service = ServiceManager.getService(Context.ACTIVITY_SERVICE)
                IActivityManager.Stub.asInterface(service).configuration
            } else {
                ActivityManagerNative.getConfiguration()
            }
        }.getOrNull()
    }
}