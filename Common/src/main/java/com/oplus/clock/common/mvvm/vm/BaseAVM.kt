/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - BaseViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/10
 ** Author: Ni<PERSON><EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  NieXiaoKang  2022/1/10     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.mvvm.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.cancel

open class BaseAVM(application: Application) : AndroidViewModel(application),
    DefaultLifecycleObserver {

    val mErrLiveData by lazy { MutableLiveData<Exception>() }

    @Suppress("TooGenericExceptionCaught")
    fun <T> launchUI(mLiveData: MutableLiveData<T>?, block: suspend CoroutineScope.() -> T) {
        viewModelScope.launch {
            try {
                val value = withContext(Dispatchers.IO) { block() }
                mLiveData?.value = value
            } catch (e: Exception) {
                mErrLiveData.value = e
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.cancel()
    }
}