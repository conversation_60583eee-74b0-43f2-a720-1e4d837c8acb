/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - ColorStatusBarResponseUtil.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/2/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong  2021/2/19     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.os.Build

class ColorStatusBarResponseUtil(val mActivity: Activity) {

    interface StatusBarClickListener {
        fun onStatusBarClicked()
    }

    companion object {
        const val TAG = "ColorStatusBarResponseUtil"
    }

    private var mReceiver: BroadcastReceiver? = null
    private var mIsRegistered = false
    private var mStatusBarClickListener: StatusBarClickListener? = null


    fun register() {
        initReceiver()
    }

    @SuppressLint("RegisterReceiverDetector", "UnspecifiedRegisterReceiverFlag")
    private fun initReceiver() {
        if (!mIsRegistered) {
            mReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    Log.i(TAG, "The broadcast receiever was registered successfully and receives the broadcast")
                    mStatusBarClickListener?.onStatusBarClicked()
                }
            }
            val intentFilter = IntentFilter()
            if (VersionUtils.isOsVersion11_3()) {
                intentFilter.addAction("com.oplus.clicktop")
            } else {
                intentFilter.addAction("com.color.clicktop")
            }
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    mActivity.registerReceiver(this.mReceiver, intentFilter, RECEIVER_EXPORTED)
                } else {
                    mActivity.registerReceiver(this.mReceiver, intentFilter)
                }
                mIsRegistered = true
            } catch (e: IllegalStateException) {
                Log.d(TAG, "register e:$e")
            }
        }
    }


    fun unRegister() {
        if (mIsRegistered) {
            mIsRegistered = false
            if (mReceiver != null) {
                mActivity.unregisterReceiver(mReceiver)
            }
        }
    }

    fun setStatusBarClickListener(listener: StatusBarClickListener?) {
        mStatusBarClickListener = listener
    }
}