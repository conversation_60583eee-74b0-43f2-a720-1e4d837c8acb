/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - VersionUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  TaoLin   2023/3/13     1.0            add file
 ****************************************************************/
package com.oplus.clock.common.utils

import android.os.Build
import com.oplus.clock.common.osdk.SystemPropNativeUtils
import com.oplus.os.OplusBuild

@Suppress("TooGenericExceptionCaught")
object VersionUtils {
    private const val TAG = "VersionUtils"
    private const val CODE_OS_VERSION_13_2 = 28
    private const val PROPERTY_TRIAL_ENABLE = "persist.sys.alwayson.enable"
    private const val PROPERTY_QCOM_QE_OFF = "persist.sys.assert.panic"
    private const val PROPERTY_MTK_QE_OFF = "persist.sys.assert.enable"
    private const val PROPERTY_TRUE = "true"

    /**
     * 是不是OS13.2及以后的版本
     */
    @JvmStatic
    fun isAboveOS132(): Boolean {
        kotlin.runCatching {
            return OplusBuild.getOplusOSVERSION() >= CODE_OS_VERSION_13_2
        }.onFailure {
            Log.w(TAG, "isAboveOS132 : ${it.message}")
        }
        return false
    }

    @JvmStatic
    @Suppress("FunctionNaming")
    fun isOsVersion11_3(): Boolean {
        kotlin.runCatching {
            return OplusBuild.getOplusOSVERSION() >= OplusBuild.OplusOS_11_3
        }.onFailure {
            Log.w(TAG, "isOsVersion11_3Exception : ${it.message}")
        }
        return false
    }

    @JvmStatic
    fun isOsVersion15(): Boolean {
        kotlin.runCatching {
            return OplusBuild.VERSION.SDK_VERSION >= OplusBuild.OsdkVersionCodes.OS_15_0_0
        }
        return false
    }

    /**
     * 当前版本是否大于15.0.1
     */
    @JvmStatic
    fun isOSVersion1501(): Boolean {
        kotlin.runCatching {
            return OplusBuild.VERSION.SDK_VERSION >= OplusBuild.OsdkVersionCodes.OS_15_0_1
        }
        return false
    }

    /**
     * 当前版本是否大于15.0.2
     */
    @JvmStatic
    fun isOSVersion1502(): Boolean {
        kotlin.runCatching {
            return OplusBuild.VERSION.SDK_VERSION >= OplusBuild.OsdkVersionCodes.OS_15_0_2
        }
        return false
    }

    /**
     * 是不是关闭了Log开关
     */
    @JvmStatic
    fun isQeOff(): Boolean {
        kotlin.runCatching {
            val (qeOff, qeOffMtk) = Pair<String, String>(
                    SystemPropNativeUtils.get(PROPERTY_QCOM_QE_OFF),
                    SystemPropNativeUtils.get(PROPERTY_MTK_QE_OFF)
            )
            return !PROPERTY_TRUE.equals(qeOff, ignoreCase = true)
                    && !PROPERTY_TRUE.equals(qeOffMtk, ignoreCase = true)
        }
        return false
    }

    @JvmStatic
    fun isQ(): Boolean {
        kotlin.runCatching {
            return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
        }
        return false
    }

    @JvmStatic
    fun isR(): Boolean {
        kotlin.runCatching {
            return Build.VERSION.SDK_INT >= Build.VERSION_CODES.R
        }
        return false
    }

    @JvmStatic
    fun isS(): Boolean {
        kotlin.runCatching {
            return Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
        }
        return false
    }
}