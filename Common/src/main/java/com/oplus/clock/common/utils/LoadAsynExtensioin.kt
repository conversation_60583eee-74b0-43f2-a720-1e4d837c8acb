/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - LoadAsynExtention.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/09
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  helin    2023/5/09     1.0            build this module
 ****************************************************************/
package com.oplus.clock.common.utils

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.Deferred
import kotlin.coroutines.CoroutineContext

/** region 生命周期扩展 */
fun <T> LifecycleOwner.loadSuspendAsync(context: CoroutineContext = Dispatchers.IO, loader: suspend () -> T): DeferredWrapper<T> {
    val deferred = lifecycleScope.async(context, start = CoroutineStart.DEFAULT) {
        loader()
    }
    return DeferredWrapper(lifecycle, deferred)
}

fun <T> LifecycleOwner.loadAsync(context: CoroutineContext = Dispatchers.IO, loader: () -> T): DeferredWrapper<T> {
    val deferred = lifecycleScope.async(context, start = CoroutineStart.DEFAULT) {
        loader()
    }
    return DeferredWrapper(lifecycle, deferred)
}

/** region 任务扩展 */
@Suppress("TooGenericExceptionCaught")
infix fun <T> DeferredWrapper<T>.then(block: (T) -> Unit): Job {
    return lifecycle.coroutineScope.launch(Dispatchers.Main) {
        try {
            block(deferred.await())
        } catch (e: Exception) {
            Log.e("Exception in then()!")
            throw e
        }
    }
}

data class DeferredWrapper<T>(val lifecycle: Lifecycle, val deferred: Deferred<T>)