/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - Log.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/7/13
 ** Author: Yang<PERSON><PERSON><PERSON>@Apps.Clock
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/7/13     1.0            add file
 ****************************************************************/
package com.oplus.clock.common.utils

object Log {
    private const val APP_TAG = "ClockTag_"
    private const val TAG = APP_TAG + "OplusClock"
    private var sLevel = android.util.Log.VERBOSE

    init {
        initLogSwitch()
    }

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun initLogSwitch() {
        sLevel = if (!VersionUtils.isAboveOS132() && VersionUtils.isQeOff()) {
            android.util.Log.WARN
        } else {
            android.util.Log.VERBOSE
        }
    }

    @JvmStatic
    fun v(tag: String, msg: String) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(APP_TAG + tag, msg)
        }
    }

    @JvmStatic
    fun v(msg: String) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(TAG, msg)
        }
    }

    @JvmStatic
    fun d(tag: String, msg: String) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(APP_TAG + tag, msg)
        }
    }

    @JvmStatic
    fun d(tag: String, msg: String, ex: Throwable) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(APP_TAG + tag, msg, ex)
        }
    }

    @JvmStatic
    fun d(msg: String) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(TAG, msg)
        }
    }

    @JvmStatic
    fun i(tag: String, msg: String) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(APP_TAG + tag, msg)
        }
    }

    @JvmStatic
    fun i(msg: String) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(TAG, msg)
        }
    }

    @JvmStatic
    fun w(tag: String, msg: String) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(APP_TAG + tag, msg)
        }
    }

    @JvmStatic
    fun w(msg: String) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(TAG, msg)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(APP_TAG + tag, msg)
        }
    }

    @JvmStatic
    fun e(msg: String) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, msg)
        }
    }

    @JvmStatic
    fun e(msg: String, ex: Throwable) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, msg, ex)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String, ex: Throwable) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(APP_TAG + tag, msg, ex)
        }
    }
}