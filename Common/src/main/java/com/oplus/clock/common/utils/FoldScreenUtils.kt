/****************************************************************
 ** Copyright (C), 2010-2022, OPLUS Mobile Comm Corp., Ltd.
 ** File:  - FoldScreenUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/7/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2021/7/13     1.0            add file
 ****************************************************************/
package com.oplus.clock.common.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.os.Build
import android.provider.Settings
import android.view.Surface
import android.view.WindowManager
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.oplus.clock.common.R
import com.oplus.content.OplusFeatureConfigManager
import java.lang.ref.WeakReference

object FoldScreenUtils {

    private const val TAG = "FoldScreenUtils"
    private const val SYSTEM_FOLDING_MODE_KEYS = "oplus_system_folding_mode"
    private const val SYSTEM_FOLDING_MODE_OPEN = 1
    private const val SYSTEM_FOLDING_MODE_CLOSE = 0

    /**
     * 蜻蜓
     */
    private const val FEATURE_FOLD = "oplus.hardware.type.fold"
    private const val DRAGONFLY = "oplus.software.fold_remap_display_disabled"

    /**
     * 在configChange时需要切换所有activity方向时才使用 UIConfigMonitor里保存的ResponsiveUIConfig.uiStatus
     * 的值，否则使用activity重新获取一遍uiStatus，避免获取到的值不准确
     *
     * @param activity
     */
    @JvmStatic
    fun updateUIOrientation(activity: Activity) {
        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get()
        if (activityReference == null) {
            Log.w(TAG, "activity memory has been recycled and cannot be updateUIOrientation")
            return
        }

        val orientation =
            if (!isDragonfly() && (isScreenRealUnfold(activity) || isRealOslo(activity))) {
                ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
            } else {
                ActivityInfo.SCREEN_ORIENTATION_NOSENSOR
            }
        if (activityReference.requestedOrientation != orientation) {
            activityReference.requestedOrientation = orientation
            Log.d(TAG, "activity $activityReference updateUIOrientation： $orientation")
        }
    }

    /**
     * 折叠屏，屏幕是否展开
     *
     * @param activity
     */
    @JvmStatic
    fun isScreenUnfold(activity: Context): Boolean {
        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get() ?: return false
        return activityReference.resources.getBoolean(R.bool.is_unfold)
    }

    /**注意 此方法在大屏 分屏情况下，获取的值是 FOLD，也就是小屏*/
    @JvmStatic
    fun isScreenFold(activity: Context): Boolean {
        val value = ResponsiveUIConfig.getDefault(activity).uiStatus.value
        return (value == null) || (value == UIConfig.Status.FOLD)
    }

    /**
     * 获取屏幕真实的折叠和展开状态，true 则表示是大屏
     * 即使在大屏分屏的情况下，也能获取到是大屏状态
     * */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun isScreenRealUnfold(context: Context): Boolean {
        var realFoldState = SYSTEM_FOLDING_MODE_CLOSE
        try {
            realFoldState = Settings.Global.getInt(
                context.contentResolver, SYSTEM_FOLDING_MODE_KEYS, SYSTEM_FOLDING_MODE_CLOSE
            )
        } catch (e: Exception) {
            Log.e(TAG, "read real screen status error ${e.message} ")
        }
        Log.d(TAG, "realFoldState $realFoldState")
        return realFoldState == SYSTEM_FOLDING_MODE_OPEN
    }

    /**
     * 是否是平板
     * 动态获取方式，会随着分屏/显示大小等变化
     * */
    @JvmStatic
    fun isOslo(activity: Context?): Boolean {
        val weakReference = WeakReference(activity)
        val activityReference = weakReference.get() ?: return false
        return activityReference.resources.getBoolean(R.bool.is_oslo)
    }

    /**
     * 是否是平板
     * 物理获取方式，即使分屏情况下，获取到的也是true
     * */
    @JvmStatic
    fun isRealOslo(context: Context?): Boolean {
        return isTableScreen(context)
    }

    /**
     * 是否是平板竖屏
     */
    @JvmStatic
    fun isOsloPortrait(activity: Activity?): Boolean {
        return isOslo(activity) && !isLandscapeScreen(activity)
    }

    /**
     * 是否是平板横屏
     * 会根据分屏/显示大小等变化
     */
    @JvmStatic
    fun isOsloLandscape(activity: Context?): Boolean {
        return isOslo(activity) && isLandscapeScreen(activity)
    }

    /**
     * 是否是平板横屏
     * 根据屏幕尺寸判断，返回的是物理的横竖屏
     */
    @JvmStatic
    fun isRealOsloLandscape(context: Context?): Boolean {
        return isRealOslo(context) && isLandscapeScreen(context)
    }

    /**
     * 是否是平板竖屏
     * 根据屏幕尺寸判断，返回的是物理的横竖屏
     */
    @JvmStatic
    fun isRealOsloPortrait(context: Context?): Boolean {
        return isRealOslo(context) && !isLandscapeScreen(context)
    }

    /**
     * 是否是分屏模式（孔雀和平板横屏没有分屏模式）
     * 孔雀分屏后是常规屏
     * 平板横屏分屏后：1/3是常规屏；1/2是孔雀屏；2/3是平板
     * @return
     */
    @JvmStatic
    fun isInDealMultiWindowMode(context: Context?, isInMultiWindowMode: Boolean): Boolean {
        if (context == null) {
            Log.e(TAG, "isInDealMultiWindowMode context null")
            return isInMultiWindowMode
        }
        return if (isScreenRealUnfold(context) || isRealOsloLandscape(context)) {
            false
        } else isInMultiWindowMode
    }

    @JvmStatic
    fun isTableScreen(context: Context?): Boolean {
        return if (context == null) {
            Log.e(TAG, "isTableScreen context null")
            false
        } else {
            val isTableScreen = OplusFeatureConfigManager.getInstance().hasFeature("oplus.hardware.type.tablet")
            Log.d(TAG, "isTableScreen $isTableScreen")
            isTableScreen
        }
    }

    /**
     * 判断当前屏幕是否横屏，不区分多窗口模式
     *
     * @return
     */
    @JvmStatic
    fun isLandscapeScreen(context: Context?): Boolean {
        if (context == null) {
            Log.e(TAG, "isLandscapeScreen context null")
            return false
        }
        val angle = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            context.applicationContext?.display?.rotation
        } else {
            (context.applicationContext?.getSystemService(Context.WINDOW_SERVICE) as? WindowManager)?.defaultDisplay?.rotation
        }
        return (angle == Surface.ROTATION_90 || angle == Surface.ROTATION_270)
    }

    /**
     * 是否有折叠能力
     */
    @JvmStatic
    fun isFold(): Boolean {
        val haveFold =
            com.oplus.content.OplusFeatureConfigManager.getInstance().hasFeature(FEATURE_FOLD)
        Log.d(TAG, "haveFold:$haveFold")
        return haveFold
    }

    @JvmStatic
    fun isFoldRemapDisplayDisabled(): Boolean {
        return com.oplus.content.OplusFeatureConfigManager.getInstance().hasFeature(DRAGONFLY)
    }

    /**
     * 判断设备是否是蜻蜓
     * FEATURE_FOLD：是否有折叠能力
     * DRAGONFLY：禁用大小屏切换的能力
     * @return
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun isDragonfly(): Boolean {
        return try {
            isFold() && isFoldRemapDisplayDisabled()
        } catch (e: Exception) {
            Log.d(TAG, "isDragonfly e :$e")
            false
        }
    }

    @JvmStatic
    fun isLargeFoldDevice(): Boolean {
        return kotlin.runCatching {
            return isFold() && !isFoldRemapDisplayDisabled()
        }.getOrDefault(false)
    }
}