plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'obuildplugin'
}
apply from: '../coverageTest.gradle'
apply from: '../androidCompile.gradle'
android {
    namespace = "com.oplus.clock.common"
    defaultConfig {
        minSdk prop_minSdkVersion as Integer
        targetSdkVersion prop_targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    dataBinding {
        enabled = true
    }
    lintOptions {
        disable "GradleDependency"
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.4.0'

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$prop_kotlinVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$prop_kotlinVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$prop_lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$prop_lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$prop_lifecycleVersion"
    compileOnly "com.oplus.support:api-adapter-compat:$prop_apiAdapterCompatVersion"
    compileOnly "com.oplus.appcompat:core:${prop_versionName}"
    compileOnly "com.oplus.appcompat:recyclerview:${prop_versionName}"
    compileOnly "com.oplus.appcompat:responsiveui:${prop_versionName}"
    compileOnly "com.oplus.appplatform:sysapi:${prop_sysApiSdkVersion}"
    compileOnly "com.oplus.sdk:addon:${prop_addonSdkVersion}"

    testImplementation "io.mockk:mockk:1.12.1"
    testImplementation 'junit:junit:4.13.2'
    testImplementation "com.oplus.appcompat:core:${prop_versionName}"
    testImplementation "com.oplus.appcompat:responsiveui:${prop_versionName}"
    androidTestImplementation "androidx.room:room-testing:2.4.0"
    androidTestImplementation "io.mockk:mockk-android:1.12.1"
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

}