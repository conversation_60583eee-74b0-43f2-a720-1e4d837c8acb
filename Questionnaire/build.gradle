plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'obuildplugin'
}
apply from: '../androidCompile.gradle'
android {
    namespace = "com.oplus.questionnaire"
    defaultConfig {
        minSdk prop_minSdkVersion as Integer
        targetSdkVersion prop_targetSdkVersion
        versionCode 1
        versionName "1.0"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
//            buildConfigField "String", "HOST_URL", "\"https://traffic-operation-cn.allawntech.com/\""
            buildConfigField "String", "HOST_URL", "\"http://traffic-operation-test.wanyol.com/\""
            buildConfigField "String", "SIGN_KEY", "\"3b2b3b8285953631cb6740e2f7b23bfd\""
            buildConfigField "String", "ENV", "\"prod\""
            buildConfigField "long", "MIN_MANUAL_SYNC_TIME_INTERVAL", "1000L * 60"
            buildConfigField "long", "AUTO_SYNC_TIME_INTERVAL", "15L"
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "HOST_URL", "\"https://traffic-operation-cn.allawntech.com/\""
            buildConfigField "String", "SIGN_KEY", "\"3b2b3b8285953631cb6740e2f7b23bfd\""
            buildConfigField "String", "ENV", "\"prod\""
            buildConfigField "long", "MIN_MANUAL_SYNC_TIME_INTERVAL", "1000L * 60 * 30"
            buildConfigField "long", "AUTO_SYNC_TIME_INTERVAL", "24 * 60L"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    testOptions {
        unitTests.returnDefaultValues = true
    }
    lintOptions {
        disable "GradleDependency"
    }
}
//由于buildConfigField原因，只能在这里from coverageTest.gradle, 否则报错！
apply from: '../coverageTest.gradle'
dependencies {
    implementation project(':Common')

    //for coroutines
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.2'
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2'
    //for lifecycle
    api "androidx.lifecycle:lifecycle-viewmodel-savedstate:2.4.0"
    api "androidx.lifecycle:lifecycle-runtime-ktx:2.4.0"
    api "androidx.lifecycle:lifecycle-livedata-ktx:2.4.0"
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0"
    implementation "androidx.customview:customview:1.1.0"
    implementation "androidx.core:core-ktx:1.13.1"
    //stdid
    api 'com.oplus.stdid.sdk:sdk:1.0.4'
    api 'com.oplus.support:api-adapter-compat:11.16.0'
    compileOnly "com.oplus.appplatform:sysapi:${prop_sysApiSdkVersion}"
    compileOnly "com.oplus.sdk:addon:${prop_addonSdkVersion}"

    implementation "com.oplus.appcompat:core:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"
    implementation "com.oplus.appcompat:progressbar:${prop_versionName}"
    implementation "com.oplus.appcompat:toolbar:${prop_versionName}"
    // Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'

    // WebView
    implementation 'androidx.webkit:webkit:1.4.0'

    //retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation('com.squareup.retrofit2:converter-gson:2.9.0') {
        exclude group: 'com.google.code.gson', module: 'gson'
    }
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation "com.squareup.okhttp3:logging-interceptor:4.12.0"

    //room
    kapt 'androidx.room:room-compiler:2.5.2'
    implementation 'androidx.room:room-runtime:2.5.2'
    implementation 'androidx.room:room-ktx:2.5.2'
    testImplementation "io.mockk:mockk:1.12.1"
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation "androidx.room:room-testing:2.4.0"
    androidTestImplementation "io.mockk:mockk-android:1.12.1"
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}