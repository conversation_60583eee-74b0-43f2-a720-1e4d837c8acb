/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : QuestionnaireContentDataTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/30       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.data.entity

import org.junit.Assert
import org.junit.Test

class QuestionnaireContentDataTest {

    @Test
    fun testMapperToUiData() {
        val questionnaireUiData = QuestionnaireUiData(
            attributes = QuestionnaireAttributesData(
                closeBtnText = mapOf("zh-cn" to "ASDAS"),
                jumpText = mapOf("zh-cn" to "saDS"),
                linkType = 1,
                linkUrl = "https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html"
            ),
            desc = mapOf("zh-cn" to "测试"),
            operatePositions = listOf(OperatePosition(appId = "1", num = "1", sort = 1, strategyNum = 1)),
            picUrl = "https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178"
        )
        val questionnaireContentData = QuestionnaireContentData(
            attributes = "{\"closeBtnText\":{\"zh-cn\":\"ASDAS\"}," +
                        "\"jumpText\":{\"zh-cn\":\"saDS\"}," +
                        "\"linkType\":1," +
                        "\"linkUrl\":\"https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html\"}",
            desc = mapOf("zh-cn" to "测试"),
            operatePositions = listOf(OperatePosition(appId = "1", num = "1", sort = 1, strategyNum = 1)),
            picUrl = "https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178"
        )
        Assert.assertEquals(questionnaireUiData, questionnaireContentData.mapperToUiData())
    }
}