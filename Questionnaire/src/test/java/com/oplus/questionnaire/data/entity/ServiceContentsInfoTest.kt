/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ServiceContentsInfoTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/30       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.data.entity

import com.oplus.questionnaire.data.entity.ServiceContentsInfo.ServiceInfoParams
import com.oplus.questionnaire.data.entity.ServiceContentsInfo.Content
import org.junit.Assert

import org.junit.Test

class ServiceContentsInfoTest {

    @Test
    fun testMapperServiceContentsInfoToServiceInfoParams() {
        val serviceContentsInfo = ServiceContentsInfo(
            content = "",
            serviceId = 1,
            version = 1
        )
        val result = ServiceInfoParams("1", 1)
        Assert.assertEquals(result, serviceContentsInfo.mapperToServiceInfoParams())
    }

    @Test
    fun testMapperContent() {
        val result = Content(
            attributes = "{" +
                    "\"closeBtnText\":{\"zh-cn\":\"ASDAS\"}," +
                    "\"jumpText\":{\"zh-cn\":\"saDS\"}," +
                    "\"linkType\":1," +
                    "\"linkUrl\":\"https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html\"}",
            desc = mapOf("zh-cn" to "测试"),
            operatePositions = listOf(OperatePosition(appId = "1", num = "1", sort = 1, strategyNum = 1)),
            picUrl = "https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178"
        )
        val serviceContentsInfo = ServiceContentsInfo(
            content = CONTENT,
            serviceId = 1,
            version = 1
        )
        Assert.assertEquals(result, serviceContentsInfo.mapperContent())
    }

    companion object {
        private const val CONTENT = "{\"attributes\":\"{" +
                "\\\"closeBtnText\\\":{\\\"zh-cn\\\":\\\"ASDAS\\\"}," +
                "\\\"jumpText\\\":{\\\"zh-cn\\\":\\\"saDS\\\"}," +
                "\\\"linkType\\\":1," +
                "\\\"linkUrl\\\":\\\"https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html\\\"}\"," +
                "\"desc\":{\"zh-cn\":\"测试\"}," +
                "\"operatePositions\":[{" +
                "\"appId\":\"1\"," +
                "\"num\":\"1\"," +
                "\"sort\":1," +
                "\"strategyNum\":1}]," +
                "\"picUrl\":\"https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178\"}"
    }
}