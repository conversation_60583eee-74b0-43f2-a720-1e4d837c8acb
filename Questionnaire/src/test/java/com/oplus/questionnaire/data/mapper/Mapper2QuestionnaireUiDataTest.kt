/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Mapper2QuestionnaireUiDataTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/30       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.data.mapper

import com.oplus.questionnaire.data.entity.QuestionnaireUiData
import com.oplus.questionnaire.data.entity.QuestionnaireAttributesData
import com.oplus.questionnaire.data.entity.Questionnaire
import com.oplus.questionnaire.data.entity.OperatePosition
import com.oplus.questionnaire.data.entity.QuestionnaireUiDataWithServiceId
import org.junit.Assert
import org.junit.Test

class Mapper2QuestionnaireUiDataTest {

    @Test
    fun testMapperQuestionnaireToServiceId() {
        val input = listOf(Questionnaire(1, "1", "1", 1, 1, CONTENT))
        val questionnaireUiData = QuestionnaireUiData(
            attributes = QuestionnaireAttributesData(
                closeBtnText = mapOf("zh-cn" to "ASDAS"),
                jumpText = mapOf("zh-cn" to "saDS"),
                linkType = 1,
                linkUrl = "https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html"
            ),
            desc = mapOf("zh-cn" to "测试"),
            operatePositions = listOf(OperatePosition(appId = "1", num = "1", sort = 1, strategyNum = 1)),
            "https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178"
        )
        val result = arrayListOf(QuestionnaireUiDataWithServiceId(1, questionnaireUiData))
        val mapper2QuestionnaireUiData = Mapper2QuestionnaireUiData()
        Assert.assertEquals(result, mapper2QuestionnaireUiData.map(input))
    }

    companion object {
        private const val CONTENT = "{\"attributes\":\"{" +
                "\\\"closeBtnText\\\":{\\\"zh-cn\\\":\\\"ASDAS\\\"}," +
                "\\\"jumpText\\\":{\\\"zh-cn\\\":\\\"saDS\\\"}," +
                "\\\"linkType\\\":1," +
                "\\\"linkUrl\\\":\\\"https://ocs-cn-south1.heytapcs.com/wj-prod/jsapi/index.html\\\"}\"," +
                "\"desc\":{\"zh-cn\":\"测试\"}," +
                "\"operatePositions\":[{" +
                "\"appId\":\"1\"," +
                "\"num\":\"1\"," +
                "\"sort\":1," +
                "\"strategyNum\":1}]," +
                "\"picUrl\":\"https://s3v2-qos.storage.wanyol.com/mobile-wisdom-touch-wanyol-com/20211221/2021122114214383178\"}"
    }
}