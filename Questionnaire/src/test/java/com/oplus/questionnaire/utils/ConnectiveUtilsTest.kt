/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConnectiveUtilsTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.questionnaire.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore

@SuppressLint("IgnoreWithoutReason")
@Ignore
class ConnectiveUtilsTest {

    @MockK
    private lateinit var context: Context

    @MockK
    private lateinit var connectivityManager: ConnectivityManager

    @Before
    fun setUp() {
        context = mockk(relaxed = true)
        connectivityManager = mockk(relaxed = true)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testNetworkAvailable() {
        every { connectivityManager.activeNetworkInfo } returns null
        every { context.getSystemService(Context.CONNECTIVITY_SERVICE) } returns connectivityManager
        Assert.assertFalse(isNetworkAvailable(context))
        val networkInfo = mockk<NetworkInfo>()
        every { connectivityManager.activeNetworkInfo } returns networkInfo
        every { networkInfo.isConnected } returns false
        Assert.assertFalse(isNetworkAvailable(context))
        every { networkInfo.isConnected } returns true
        Assert.assertTrue(isNetworkAvailable(context))
    }

    @Test
    fun testInternetAvailable() {
        // activeNetwork is null
        every { context.getSystemService(Context.CONNECTIVITY_SERVICE) } returns connectivityManager
        every { connectivityManager.activeNetwork } returns null
        Assert.assertFalse(isInternetAvailable(context))

        // networkCapabilities is null
        val activeNetwork = mockk<Network>()
        every { connectivityManager.activeNetwork } returns activeNetwork
        Assert.assertFalse(isInternetAvailable(context))

        // networkCapabilities has not transport wifi
        val networkCapabilities = mockk<NetworkCapabilities>()
        every { connectivityManager.getNetworkCapabilities(any()) } returns networkCapabilities
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        Assert.assertFalse(isNetworkAvailable(context))

        // networkCapabilities has transport wifi
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns true
        Assert.assertTrue(isInternetAvailable(context))

        // networkCapabilities doesn't has transport wifi capability, but transport WIFI
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) } returns true
        Assert.assertTrue(isInternetAvailable(context))

        // networkCapabilities doesn't has transport wifi capability and not transport WIFI, but transport cellular
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) } returns true
        Assert.assertTrue(isInternetAvailable(context))

        // networkCapabilities doesn't has transport wifi capability and not transport WIFI,
        // and transport cellular, but transport ethernet
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) } returns true
        Assert.assertTrue(isInternetAvailable(context))

        // networkCapabilities doesn't has transport wifi capability and not transport WIFI,
        // and not transport cellular, and not transport ethernet
        every { networkCapabilities.hasCapability(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) } returns false
        every { networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) } returns false
        Assert.assertFalse(isInternetAvailable(context))
    }
}