/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AppUtilsTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.questionnaire.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore

@SuppressLint("IgnoreWithoutReason")
@Ignore
class AppUtilsTest {

    @MockK
    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun testGetVersionCode() {
        // getVersionCode error
        Assert.assertEquals(0, AppUtils.getVersionCode(context))

        // get the specified VersionCode
        val packageManager = mockk<PackageManager>(relaxed = true)
        every { context.packageManager } returns packageManager
        every { context.packageName } returns PACKAGE_NAME
        val packageInfo = mockk<PackageInfo>(relaxed = true)
        every { packageInfo.longVersionCode } returns LONG_VERSION_CODE
        every { packageManager.getPackageInfo(any() as String,
            any() as PackageManager.PackageInfoFlags) } returns packageInfo
        Assert.assertEquals(20211221, AppUtils.getVersionCode(context))
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val PACKAGE_NAME = "com.oplus.questionnaire"
        private const val LONG_VERSION_CODE = 20211221L
    }
}