/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LocalExtTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/30       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.utils

import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.util.Locale

class LocaleExtTest {

    private lateinit var locale: Locale

    @Before
    fun setUp() {
        locale = mockk(relaxed = true)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testLocaleParams() {
        // locale is null
        Assert.assertEquals("", getLocaleParams(null))

        // locale is CN
        every { locale.language } returns "zh"
        every { locale.country } returns "CN"
        Assert.assertEquals("zh-CN", getLocaleParams(locale))
    }
}