/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IgnoreCheckerTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/30       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.utils

import android.annotation.SuppressLint
import android.content.Context
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore

@SuppressLint("IgnoreWithoutReason")
@Ignore
class IgnoreCheckerTest {

    @MockK
    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk(relaxed = true)
        mockkStatic(PreferencesUtils::class)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun isInIgnorePeriod() {
        // less than a week
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() - LESS_THAN_WEEK
        Assert.assertTrue(IgnoreChecker.isInIgnorePeriod(context))

        // future timestamp
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() + LESS_THAN_WEEK
        Assert.assertTrue(IgnoreChecker.isInIgnorePeriod(context))

        // more than a week
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() - MORE_THAN_WEEK
        Assert.assertFalse(IgnoreChecker.isInIgnorePeriod(context))

        // future timestamp more than a week
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() + MORE_THAN_WEEK
        Assert.assertFalse(IgnoreChecker.isInIgnorePeriod(context))

        // a week
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() - WEEK
        Assert.assertFalse(IgnoreChecker.isInIgnorePeriod(context))

        // future a week
        every {
            PreferencesUtils.getLong(context = context, key = any())
        } returns System.currentTimeMillis() + WEEK
        Assert.assertFalse(IgnoreChecker.isInIgnorePeriod(context))
    }

    companion object {
        private const val LESS_THAN_WEEK = 204800000
        private const val MORE_THAN_WEEK = 604800001
        private const val WEEK = 604800001
    }
}