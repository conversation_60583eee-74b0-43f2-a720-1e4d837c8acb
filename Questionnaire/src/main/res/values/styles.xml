<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="CommonAppBarStyle" parent="android:Widget">
        <item name="android:background">?attr/couiColorBackground</item>
    </style>

    <style name="AppNoTitleTheme.ActionMode" parent="AppNoTitleTheme">
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowActionModeOverlay">true</item>
        <item name="android:actionModeStyle">@style/actionModeStyle</item>
    </style>

    <style name="actionModeStyle">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:height">@dimen/action_mode_style_height</item>
    </style>


    <style name="AppNoTitleTheme" parent="AppTheme">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
    </style>

    <style name="AppTheme" parent="AppBaseTheme"></style>

    <style name="BaseTheme" parent="@style/Theme.COUI.Main">
        <item name="android:textAlignment">gravity</item>
        <item name="android:textDirection">locale</item>
        <item name="android:forceDarkAllowed" tools:ignore="NewApi">true</item>
        <item name="android:isLightTheme" tools:ignore="NewApi">true</item>
        <item name="enableFollowSystemForceDarkRank">true</item>
        <item name="android:fastScrollTrackDrawable">@null</item>
    </style>

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-v[XX]/styles.xml on newer devices.
    -->
    <style name="AppBaseTheme" parent="BaseTheme">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
    </style>
</resources>