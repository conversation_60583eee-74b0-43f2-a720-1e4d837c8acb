<?xml version="1.0" encoding="utf-8"?>
<!--suppress AndroidDomInspection -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        style="@style/CommonAppBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:orientation="vertical"
        app:elevation="@dimen/toolbar_elevation"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/toolbar_height" />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/common_margin"
            android:layout_marginRight="@dimen/common_margin"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false"
            tools:ignore="PrivateResource,UnusedAttribute" />
    </com.google.android.material.appbar.AppBarLayout>

    <com.coui.appcompat.progressbar.COUILoadingView
        android:id="@+id/loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:text="@null"
        app:couiLoadingViewColor="?attr/couiColorPrimary"
        app:couiLoadingViewType="large"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="PrivateResource" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar"
        android:orientation="vertical" />

</androidx.constraintlayout.widget.ConstraintLayout>