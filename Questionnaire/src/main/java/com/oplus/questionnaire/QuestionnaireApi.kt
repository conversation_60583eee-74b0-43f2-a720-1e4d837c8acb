/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : QuestionnaireApi.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.questionnaire

import android.view.ViewStub
import androidx.activity.ComponentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.oplus.questionnaire.data.QuestionnaireFactory
import com.oplus.questionnaire.data.local.AppDatabase
import com.oplus.questionnaire.data.remote.QuestionnaireService
import com.oplus.questionnaire.data.remote.doFailure
import com.oplus.questionnaire.data.remote.doSuccess
import com.oplus.questionnaire.ui.QuestionnaireCardHelper
import com.oplus.questionnaire.utils.IgnoreChecker
import com.oplus.questionnaire.utils.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

object QuestionnaireApi {

    private const val TAG = "QuestionnaireApi"

    @JvmStatic
    fun injectQuestionnaire(
        passedInActivity: WeakReference<ComponentActivity>,
        targetView: ViewStub,
    ) {
        Log.i(TAG, "injectQuestionnaire")
        val activity = passedInActivity.get() ?: return
        activity.lifecycleScope.launch {
            Log.i(TAG, "injectQuestionnaire -> launch")
            val questionnaireCardHelper = QuestionnaireCardHelper()
            val questionnaireRepository = QuestionnaireFactory.makeQuestionnaireRepository(
                activity,
                QuestionnaireService.create(),
                AppDatabase.getInstance(activity)
            )
            activity.repeatOnLifecycle(Lifecycle.State.STARTED) {
                if (!IgnoreChecker.isInIgnorePeriod(activity)) {
                    withContext(Dispatchers.IO) {
                        questionnaireRepository.getQuestionnaires().collect { result ->
                            result.doSuccess { value ->
                                Log.i(
                                    TAG,
                                    "injectQuestionnaire -> getQuestionnaire doSuccess ${value.size}"
                                )
                                withContext(Dispatchers.Main) {
                                    if (value.isNotEmpty()) {
                                        Log.i(
                                            TAG,
                                            "injectQuestionnaire -> getQuestionnaire ${value[0]}"
                                        )
                                        val questionnaireUiData = value[0]
                                        if (IgnoreChecker.isInIgnorePeriod(activity) && questionnaireCardHelper.lazyLoader != null) {
                                            questionnaireCardHelper.lazyLoader?.hideCardView()
                                        } else {
                                            if (questionnaireCardHelper.lazyLoader?.isInitialized() == true) {
                                                questionnaireCardHelper.lazyLoader?.updateCardLayoutByUiData(
                                                    activity,
                                                    questionnaireUiData
                                                )
                                            } else {
                                                questionnaireCardHelper.serUpQuestionnaireCard(
                                                    activity,
                                                    targetView,
                                                    questionnaireUiData
                                                )
                                            }
                                        }
                                    } else if (questionnaireCardHelper.lazyLoader != null) {
                                        questionnaireCardHelper.lazyLoader?.hideCardView()
                                    }
                                }
                            }
                            result.doFailure {
                                Log.i(TAG, "injectQuestionnaire -> getQuestionnaire doFailure")
                            }
                        }
                    }
                } else {
                    if (questionnaireCardHelper.lazyLoader != null) {
                        questionnaireCardHelper.lazyLoader?.hideCardView()
                    }
                }
            }
        }
    }
}