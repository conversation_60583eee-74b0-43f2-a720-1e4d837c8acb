/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : QuestionnaireActivity.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  dustin.shu      2022/1/5      1.0        create
 ***********************************************************************/
package com.oplus.questionnaire

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.Configuration
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.animation.LinearInterpolator
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebSettingsCompat.DARK_STRATEGY_PREFER_WEB_THEME_OVER_USER_AGENT_DARKENING
import androidx.webkit.WebSettingsCompat.DARK_STRATEGY_WEB_THEME_DARKENING_ONLY
import androidx.webkit.WebViewFeature
import com.coui.appcompat.theme.COUIThemeOverlay
import com.coui.appcompat.progressbar.COUILoadingView
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.google.gson.Gson
import com.oplus.clock.common.osdk.ContextNativeUtils
import com.oplus.questionnaire.data.entity.IgnoredRecord
import com.oplus.questionnaire.data.entity.SubmitResult
import com.oplus.questionnaire.data.local.AppDatabase
import com.oplus.questionnaire.utils.H5_SERVICE_ID
import com.oplus.questionnaire.utils.H5_URL_INTENT_KEY
import com.oplus.questionnaire.utils.StatusBarUtil
import com.oplus.questionnaire.utils.Log
import com.oplus.questionnaire.utils.PreferencesUtils
import com.oplus.questionnaire.utils.PREF_KEY_IGNORE_TIMESTAMP

class QuestionnaireActivity : AppCompatActivity() {

    companion object {
        const val TAG = "QuestionnaireH5Activity"
        const val JS_INTERFACE_NAME = "nativeApiBridge"
        const val ANIM_DURATION = 100L
        const val PROGRESS_DONE = 100
    }

    private var mLinearLayout: LinearLayout? = null
    private var webView: WebView? = null
    private var loadingView: COUILoadingView? = null
    private var mUrl: String? = null
    private var mServiceId: Int? = null
    private var isLoadingDone: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.questionnaire_activity)

        initToolbar()
        setUpWebView()
        setUpNightMode(true)
        loadDataWithTheme(mUrl)

        //should be called before setContentView
        StatusBarUtil.setStatusBarTransparentAndBlackFont(this)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
    }

    private fun initToolbar() {
        val appBarLayout = findViewById<AppBarLayout>(R.id.appbar)
        appBarLayout?.setPadding(
            0,
            StatusBarUtil.getStatusBarHeight(this) + resources.getDimensionPixelOffset(R.dimen.tab_searchview_margin_top),
            0,
            0
        )
        val toolbar = findViewById<COUIToolbar>(R.id.toolbar)
        toolbar?.titleMarginTop
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    @SuppressLint("SetJavaScriptEnabled", "SetJavaScriptEnabledFixDetector", "WebViewDomStorage")
    private fun setUpWebView() {
        val context = applicationContext
        val cpContext = ContextNativeUtils.createCredentialProtectedStorageContext(applicationContext)
        webView = if (cpContext != null) {
            WebView(cpContext)
        } else {
            WebView(context)
        }
        mLinearLayout = findViewById(R.id.linearLayout)
        mLinearLayout?.addView(webView,
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.MATCH_PARENT)
        mUrl = intent.extras?.getString(H5_URL_INTENT_KEY)
        mServiceId = intent.extras?.getInt(H5_SERVICE_ID)
        val webSettings = webView?.settings
        webSettings?.apply {
            javaScriptEnabled = true
            builtInZoomControls = true
            displayZoomControls = true
            domStorageEnabled = true
        }
        webView?.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                Log.d(TAG, "onProgressChanged $newProgress")
                if (newProgress == PROGRESS_DONE && !isLoadingDone) {
                    isLoadingDone = true
                    hideLoadingUi()
                }
            }
        }
        webView?.addJavascriptInterface(
            GetSubmitResultJsInterface(this, mServiceId),
            JS_INTERFACE_NAME
        )
    }

    private fun hideLoadingUi() {
        loadingView = findViewById(R.id.loading_view)
        loadingView?.let {
            alphaAnimHide(it)
        }
        webView?.let {
            alphaAnimShow(it)
        }
    }


    private fun alphaAnimHide(view: View) {
        val alphaAnimation = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        alphaAnimation.duration = ANIM_DURATION
        alphaAnimation.interpolator = LinearInterpolator()
        alphaAnimation.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                //do nothing
            }

            override fun onAnimationEnd(animation: Animator) {
                view.visibility = View.GONE
            }

            override fun onAnimationCancel(animation: Animator) {
                //do nothing
            }

            override fun onAnimationRepeat(animation: Animator) {
                //do nothing
            }
        })
        alphaAnimation.start()
    }

    private fun alphaAnimShow(view: View) {
        val alphaAnimation = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        alphaAnimation.duration = ANIM_DURATION
        alphaAnimation.interpolator = LinearInterpolator()
        alphaAnimation.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                view.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animator) {
                //do nothing
            }

            override fun onAnimationCancel(animation: Animator) {
                //do nothing
            }

            override fun onAnimationRepeat(animation: Animator) {
                //do nothing
            }
        })
        alphaAnimation.start()
    }

    private fun setUpNightMode(forceDark: Boolean = false) {
        webView?.apply {
            val nightMode = resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
            if (nightMode == Configuration.UI_MODE_NIGHT_YES) {
                if (WebViewFeature.isFeatureSupported(WebViewFeature.FORCE_DARK)) {
                    WebSettingsCompat.setForceDark(
                        settings,
                        WebSettingsCompat.FORCE_DARK_ON
                    )
                }
                if (WebViewFeature.isFeatureSupported(WebViewFeature.FORCE_DARK_STRATEGY)) {
                    WebSettingsCompat.setForceDarkStrategy(
                        settings,
                        if (forceDark) {
                            DARK_STRATEGY_PREFER_WEB_THEME_OVER_USER_AGENT_DARKENING
                        } else {
                            DARK_STRATEGY_WEB_THEME_DARKENING_ONLY
                        }
                    )
                }
            }
        }
    }

    private fun loadDataWithTheme(url: String?) {
        url?.let {
            webView?.loadUrl(url)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        webView?.destroy()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}

class GetSubmitResultJsInterface(var activity: Activity, var serviceId: Int?) {

    @SuppressLint("JavascriptInterface")
    @JavascriptInterface
    fun submitHeytapWenjuan(result: String) {

        // Native 逻辑
        Log.d("submitHeytapWenjuan result is =$result")
        val submitResult = Gson().fromJson(result, SubmitResult::class.java)
        Log.d("submitHeytapWenjuan submitResult is =$submitResult ; serviceId = $serviceId")
        if (activity.isDestroyed || activity.isFinishing) {
            //Log.d(TAG, "activity isDestroyed or isFinishing")
        } else {
            //Log.d(TAG, "getResult invoke $result")
            //if success, delete record
            if (submitResult.success) {
                serviceId?.let {
                    AppDatabase.getInstance(activity).questionnaireDao()
                        .deleteQuestionnaireByServiceId(it)
                    AppDatabase.getInstance(activity).ignoredServiceDao()
                        .insertIgnoredService(IgnoredRecord(it, System.currentTimeMillis()))
                    PreferencesUtils.put(
                        context = activity,
                        key = PREF_KEY_IGNORE_TIMESTAMP,
                        value = System.currentTimeMillis()
                    )
                }
            }
        }
    }
}