package com.oplus.questionnaire.workers

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.oplus.questionnaire.utils.IgnoreChecker
import com.oplus.questionnaire.utils.isNetworkAvailable

class GetQuestionnaireFromServerWorker(
    context: Context,
    params: WorkerParameters
) : Worker(context, params) {

    override fun doWork(): Result {
        return if (IgnoreChecker.isInIgnorePeriod(applicationContext)) {
            Result.failure()
        } else if (!isNetworkAvailable(applicationContext)) {
            Result.failure()
        } else {
            // Logic to get questionnaire from server
            Result.success()
        }
    }
}