/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IgnoredServiceDaoTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/31       1.0      create
 ***********************************************************************/
package com.oplus.questionnaire.data.local

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.questionnaire.data.entity.IgnoredRecord
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers.equalTo
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.IOException

@RunWith(AndroidJUnit4::class)
class IgnoredServiceDaoTest {

    private lateinit var db: AppDatabase
    private lateinit var ignoredServiceDao: IgnoredServiceDao

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        db = Room.inMemoryDatabaseBuilder(context, AppDatabase::class.java).build()
        ignoredServiceDao = db.ignoredServiceDao()
    }

    @After
    @Throws(IOException::class)
    fun closeDb() {
        db.close()
    }

    @Test
    @Throws(Exception::class)
    fun testInsertIgnoredServiceAndSearchByServiceId() {
        val ignoredRecord = IgnoredRecord(
            serviceId = 1, timestamp = 12345L
        )
        ignoredServiceDao.insertIgnoredService(ignoredRecord)

        val insertResult = ignoredServiceDao.insertIgnoredService(ignoredRecord)
        MatcherAssert.assertThat(insertResult, equalTo(1L))

        val serviceId1Record = IgnoredRecord(
            serviceId = 1, timestamp = 123123123L
        )
        val insertReplaceResult = ignoredServiceDao.insertIgnoredService(serviceId1Record)
        MatcherAssert.assertThat(insertReplaceResult, equalTo(1L))

        val searchById1 = ignoredServiceDao.getIgnoredServiceByServiceId(1)
        MatcherAssert.assertThat(searchById1, equalTo(listOf(serviceId1Record)))
    }
}